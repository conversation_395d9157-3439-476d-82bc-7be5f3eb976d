// import IframeCont from "@/components/ui/iframeCont.vue";
import Layout from "@/components/ui/layout.vue";

import { OPERATION } from "@/config/custom/";
import customMenuStructure from "@/custom/config/navi/menuStructure.js";
import Logger from "yn-p1/libs/modules/log/logger";
import { getMergedMenuStructure } from "@/utils/common.js";
import journal from "./journal";
import process from "./process";
import ruleConfiguration from "./ruleConfiguration";
// import flowManage from "./flowManage";
import ruleSetMaintenance, { ruleSetRouterMap } from "./ruleSetMaintenance";
import { JOURNAL_LIST_PATH } from "../constant/common";
let menuStructure = [
  {
    path: "/",
    name: "main",
    components: {
      // 新增合并自身模块，需要在这里面进行集中设置
      organization: () => import("@/views/ownershipManagement/organization"),
      equity: () => import("@/views/ownershipManagement/equity"),
      reconciliationStatement: () =>
        import("@/views/reconciliation/reconciliationStatement"),
      reconciliationReport: () =>
        import(
          "@/views/reconciliation/reconciliationStatement/reconciliationReport/index.vue"
        ),
      voucherReconciliationReport: () =>
        import(
          "@/views/voucherReconciliation/reconciliationStatement/report/index.vue"
        ),
      reconciliationSettings: () =>
        import("@/views/reconciliation/reconciliationSetting"),
      voucherReconciliationStatement: () =>
        import("@/views/voucherReconciliation/reconciliationStatement"),
      voucherReconciliationSettings: () =>
        import("@/views/voucherReconciliation/reconciliationSetting"),
      todo: () => import("@/views/todo"),
      ...journal,
      ...process,
      ...ruleConfiguration,
      // ...flowManage,
      exchangerateSet: () => import("@/views/exchangeRate"),
      ...ruleSetMaintenance,
      // 表单外挂的方式。 走自己内部的路由便于传 pov 。配置菜单的时候 mr 改成 consolidation
      openForm: () => import("@/views/openForm"),
      // 菜单上配置了报告的情况。跟表单一样走内部路由 传 pov。
      openReport: () => import("@/views/openForm/report.vue"),
      mergeTaskSearch: () => import("@/views/manage/search/index.vue"),
      systemTab: () => import("@/components/ui/iframeCont.vue"),
      dimensionManage: () => import("@/views/dimensionManage"),
      emailReminderRule: () => import("@/views/emailReminderRule")
    },
    meta: { id: 51 }
  },
  {
    zh_CN: "所有权管理",
    en: "Ownership management",
    path: "/ownership",
    component: Layout,
    children: [
      {
        zh_CN: "组织架构",
        en: "organizational structure",
        path: "/ownership/organization",
        name: "organization",
        meta: {
          id: 121,
          type: "consolidation_module"
        },
        component: () => import("@/views/ownershipManagement/organization")
      },
      {
        zh_CN: "股权管理",
        en: "Equity management",
        path: "equity",
        name: "equity",
        meta: { id: 122, type: "consolidation_module" },
        component: () => import("@/views/ownershipManagement/equity")
      }
    ],
    meta: { id: 12, type: "consolidation" }
  },
  {
    zh_CN: "对账平台",
    en: "Reconciliation platform",
    path: "/reconciliation",
    component: Layout,
    children: [
      {
        zh_CN: "余额对账报表",
        en: "Reconciliation statement",
        path: "/reconciliation/reportList",
        meta: { id: 131, type: "consolidation_module" },
        name: "reconciliationStatement",
        component: () =>
          import("@/views/reconciliation/reconciliationStatement")
      },
      {
        zh_CN: "余额对账设置",
        en: "Reconciliation settings",
        path: "/reconciliation/settings",
        name: "reconciliationSettings",
        meta: { id: 132, type: "consolidation_module" },
        component: () => import("@/views/reconciliation/reconciliationSetting")
      },
      {
        zh_CN: "凭证对账报告",
        en: "VoucherReconciliation report",
        path: "/voucherReconciliation/reportList",
        name: "voucherReconciliationStatement",
        meta: { id: 132, type: "consolidation_module" },
        component: () =>
          import("@/views/voucherReconciliation/reconciliationStatement")
        // @/views/voucherReconciliation/reconciliationSetting
      },
      {
        zh_CN: "凭证对账设置",
        en: "VoucherReconciliation settings",
        path: "/voucherReconciliation/settings",
        name: "voucherReconciliationSettings",
        meta: { id: 132, type: "consolidation_module" },
        component: () =>
          import("@/views/voucherReconciliation/reconciliationSetting")
      },
      {
        zh_CN: "余额对账报表",
        en: "reconciliation report",
        path: "/reconciliation/report",
        name: "reconciliationReport",
        meta: { id: 133, type: "consolidation_module" },
        component: () =>
          import(
            "@/views/reconciliation/reconciliationStatement/reconciliationReport"
          )
      },
      {
        zh_CN: "凭证对账报表",
        en: "VoucherReconciliation report",
        path: "/voucherReconciliation/report",
        name: "voucherReconciliationReport",
        meta: { id: 134, type: "consolidation_module" },
        component: () =>
          import("@/views/voucherReconciliation/reconciliationStatement/report")
      }
    ],
    meta: { id: 13, type: "consolidation" }
  },
  {
    zh_CN: "日记账",
    en: "Journal",
    path: "/journal",
    component: Layout,
    meta: { id: 13, type: "consolidation" },
    children: [
      {
        zh_CN: "日记账模板",
        en: "Journal Template",
        path: "/journal/template",
        name: "journalTemplate",
        meta: { id: 131, type: "consolidation_module" },
        component: () => import("@/views/journal/journalTemplate")
      },
      {
        zh_CN: "日记账查询",
        en: "Journal Search",
        name: "journalSearch",
        path: "/journal/search",
        meta: { id: 132, type: "consolidation_module" },
        component: () => import("@/views/journal/journalSearch")
      },
      {
        zh_CN: "日记账列表",
        en: "Journal List",
        name: "journalList",
        path: JOURNAL_LIST_PATH,
        meta: { id: 133, type: "consolidation_module" },
        component: () => import("@/views/journal/journalList/list")
      },
      {
        zh_CN: "日记账详情",
        en: "Journal Detail",
        name: "journalDetail",
        path: "/journal/detail",
        meta: { id: 134, type: "consolidation_module", keepAlive: true },
        component: () => import("@/views/journal/journalList/detail")
      }
    ]
  },
  {
    zh_CN: "规则配置",
    en: "Rule configuration",
    path: "/ruleConfig",
    component: Layout,
    meta: { id: 14, type: "consolidation" },
    children: [
      {
        name: "rearrange",
        path: "/ruleConfig/rearrange",
        meta: { id: 141, type: "consolidation_module" },
        component: () => import("@/views/ruleConfiguration/rearrange")
      },
      {
        name: "rearrangeRule",
        path: "/ruleConfig/rearrangeRule",
        meta: { id: 142, type: "consolidation_module" },
        component: () => import("@/views/ruleConfiguration/rearrange/rule.vue")
      },
      ...ruleSetRouterMap,
      {
        name: "currentoffset",
        path: "/ruleConfig/currentoffset",
        meta: { id: 143, type: "consolidation_module" },
        component: () => import("@/views/ruleConfiguration/currentoffset")
      },
      {
        name: "currentoffsetRule",
        path: "/ruleConfig/currentoffsetRule",
        meta: { id: 144, type: "consolidation_module" },
        component: () =>
          import("@/views/ruleConfiguration/currentoffset/rule.vue")
      },
      {
        zh_CN: "汇率管理",
        en: "Exchangerate Set",
        name: "exchangerateSet",
        path: "/exchangerateSet",
        meta: { id: 139, type: "consolidation_module" },
        component: () => import("@/views/exchangeRate")
      }
    ]
  },
  {
    zh_CN: "流程",
    en: "Process",
    path: "/process",
    component: Layout,
    meta: { id: 14, type: "consolidation" },
    children: [
      {
        zh_CN: "流程模板",
        en: "Process Template",
        path: "/process/template",
        name: "processTemplate",
        meta: { id: 141, type: "consolidation_module" },
        component: () => import("@/views/process/template")
      },
      {
        zh_CN: "流程节点",
        en: "Process Nodes",
        name: "processNodes",
        path: "/process/nodes",
        meta: { id: 132, type: "consolidation_module" },
        component: () => import("@/views/process/nodes")
      },
      {
        zh_CN: "流程控制",
        en: "Process Control",
        name: "processControl",
        path: "/process/control",
        meta: { id: 133, type: "consolidation_module" },
        component: () => import("@/views/process/control")
      },
      {
        zh_CN: "节点编辑",
        en: "Process NodesEdit",
        name: "processNodesEdit",
        path: "/process/editnodes",
        meta: { id: 134, type: "consolidation_module" },
        component: () => import("@/views/process/nodes/editnodes")
      },
      {
        zh_CN: "填报",
        en: "Process Report",
        name: "processReport",
        path: "/process/report",
        meta: { id: 135, type: "consolidation_module" },
        component: () => import("@/views/process/control/report")
      },
      {
        zh_CN: "表单",
        en: "Process Form",
        name: "processForm",
        path: "/process/form",
        meta: { id: 136, type: "consolidation_module" },
        component: () => import("@/views/process/form")
      },
      {
        zh_CN: "流程监控",
        en: "Process Watch",
        name: "processWatch",
        path: "/process/watch",
        meta: { id: 137, type: "consolidation_module" },
        component: () => import("@/views/process/watch")
      },
      {
        zh_CN: "校验跳转配置",
        en: "Process Rule",
        name: "processRule",
        path: "/process/rule",
        meta: { id: 138, type: "consolidation_module" },
        component: () => import("@/views/process/rule")
      }
    ]
  },
  {
    zh_CN: "表单",
    en: "Open Form",
    name: "openForm",
    path: "/openForm",
    component: () => import("@/views/openForm"),
    meta: { id: 16, type: "consolidation_module" }
  },
  {
    zh_CN: "待办",
    en: "To Do",
    name: "todo",
    path: "/todo",
    component: () => import("@/views/todo"),
    meta: { id: 16, type: "consolidation_module" }
  },
  {
    zh_CN: "报告",
    en: "Open Report",
    name: "openReport",
    path: "/openReport",
    component: () => import("@/views/openForm/report.vue"),
    meta: { id: 17, type: "consolidation_module" }
  },
  {
    zh_CN: "系统管理",
    en: "SystemManage",
    path: "/SystemManage",
    component: Layout,
    meta: { id: 15, type: "consolidation" },
    children: [
      {
        zh_CN: "合并任务查询",
        en: "Merge Task Search",
        name: "mergeTaskSearch",
        path: "/SystemManage/search",
        meta: { id: 151, type: "consolidation_module" },
        component: () => import("@/views/manage/search")
      }
    ]
  },
  {
    zh_CN: "维度管理",
    en: "Dimension Manage",
    name: "dimensionManage",
    path: "/dimensionManage",
    component: () => import("@/views/dimensionManage/index.vue"),
    meta: { id: 17, type: "consolidation_module" }
  },
  {
    zh_CN: "邮件提醒规则",
    en: "Email reminder rule",
    name: "emailReminderRule",
    path: "/emailReminderRule",
    component: () => import("@/views/emailReminderRule/index.vue"),
    meta: { id: 18, type: "consolidation_module" }
  }
];
if (Array.isArray(customMenuStructure) && customMenuStructure.length > 0) {
  menuStructure = getMergedMenuStructure(
    customMenuStructure,
    menuStructure,
    OPERATION,
    Logger
  );
}
export const routerMap = menuStructure;
