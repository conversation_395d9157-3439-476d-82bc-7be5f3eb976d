import Vue from "vue";
import VueRouter from "vue-router";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { BACKEND } from "../config/SETUP";
// const baseUrl = BACKEND.BASE_URL;
const consoleBaseUrl = BACKEND.ECS_CONSOLE_BASE_URL;
import { routerMap } from "./routerMap";
import { getServiceUrl } from "@/utils/common";
Vue.use(VueRouter);
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};
const createRouter = () =>
  new VueRouter({
    routes: routerMap
  });

const router = createRouter();

router.beforeEach((to, from, next) => {
  const params = {
    to,
    headers: {
      // Sbox: "default"
    },
    // 若lang不传或为空，方法内部会优先从to.query中取lang、其次sessionStorege、localStorege、若都不存在则默认"zh_CN"
    lang: "", // en_US
    // 获取多语言分类下词条，可同时获取多个分类
    modules: [
      "common",
      "c1.mdd.dimension",
      "c1.mdd.check_rules",
      "c1.consolidation.common",
      "c1.consolidation.process",
      "c1.consolidation.process.message_template",
      "c1.consolidation.verify_jump_configuration",
      "c1.consolidation.consolidation_structures",
      "c1.consolidation.equity_manager",
      "c1.common"
    ],
    // 通过key获取单独词条，可同时获取多个，用法与modules一致
    keys: [],
    baseUrl: getServiceUrl(consoleBaseUrl) // http://*************:22138 格式 ip+port
  };
  DsUtils.getTranslationData(params).then(() => next());
});
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher;
}
export default router;
