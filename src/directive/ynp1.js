// -------- this is a ynp1 component hack js-----------
import { smoothscroll } from "@/utils/dom";
// ---------- 滚动条自动滚动到底部-------------
const SELECTOR = ".ant-table-body";
// 自动滚动到可滚动dom底部， 默认 hack antd table, binding.value = base type
export const scrollBottom = {
  inserted(el, binding) {
    const params = binding.value;
    el._cache || (el._cache = {});
    el._cache.domWrapper = el.querySelector(params[0] || SELECTOR); // 出现滚动条的dom
  },
  update(el, binding) {
    const params = binding.value;
    const oldparams = binding.oldValue;
    if (params[1] !== oldparams[1]) {
      setTimeout(() => {
        smoothscroll(el._cache.domWrapper);
      }, 0);
    }
  }
};

// ---------- 可视区表格内滚动-------------
/**
 * 表格内滚动 v-scrollInner = params
 * @params = [
 *   this,
 *   '.离table最近的一层可视区dom类名',
 *   'yn-table scroll 配置属性',
 *   '除表格外，被其他dom额外被占用的高度'
 * ]
 */

export const scrollInner = {
  inserted(el, binding) {
    const params = binding.value;
    const vm = params[0];
    const viewAreaClass = params[1];
    el._cache || (el._cache = {});
    el._cache.parentsNode = vm.$el.querySelector(viewAreaClass);
  },
  componentUpdated(el, binding) {
    const params = binding.value;
    const offsetHeight = el._cache.parentsNode.offsetHeight;
    const style = getComputedStyle(el._cache.parentsNode);
    const header = el.querySelector(".ant-table-thead");
    const footer = el.querySelector(".ant-table-wrapper").parentNode
      .nextSibling;
    let footerHeight = 0;
    const otherdomHeightSum = params[3] || 0;
    if (footer) {
      const fstyle = getComputedStyle(footer);
      footerHeight =
        footer.offsetHeight +
        parseFloat(fstyle.marginTop) +
        parseFloat(fstyle.marginBottom);
    }
    const preReduce =
      parseFloat(style.paddingTop) +
      parseFloat(style.paddingBottom) +
      otherdomHeightSum;

    if (offsetHeight > 0 && el.offsetHeight >= offsetHeight - preReduce) {
      params[2].y =
        offsetHeight - header.offsetHeight - preReduce - footerHeight;
    } else {
      params[2].y = false;
    }
  }
};

// ------------------- 修复平台分页删除尾页所有数据展示无数据问题 ----------
export const fixLastpage = {
  update(el, binding, vnode) {
    const keys = Object.keys(binding.modifiers)[0];
    const params = binding.value || [];
    const page = vnode.componentOptions.propsData.current;
    const change = vnode.componentOptions.listeners.change;
    if (params.length === 0 && page !== 1) {
      vnode.context[keys] = page - 1;
      change();
    }
  }
};

export default {
  scrollBottom,
  scrollInner,
  fixLastpage
};
