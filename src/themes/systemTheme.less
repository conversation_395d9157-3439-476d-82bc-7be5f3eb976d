
/**
 * 主题色
 * theme1 秋日晴空蓝
 * theme2 天蓝
 * theme3 海昌蓝
 * theme4 孔雀绿
 * theme5 锦红
 */

#yn-primary-color() {
  theme1: #1890ff;
  theme2: #1377EB;
  theme3: #3E5D96;
  theme4: #019977;
  theme5: #E00033;
}
#yn-layout-header-background() {
  theme1: #1890ff;
  theme2: #1377EB;
  theme3: #292E3F;
  theme4: #019977;
  theme5: #E00033;
}
#yn-body-background() {
  theme1: #ffffff;
  theme2: #ffffff;
  theme3: #ffffff;
  theme4: #ffffff;
  theme5: #ffffff;
}
#yn-component-background() {
  theme1: #ffffff;
  theme2: #ffffff;
  theme3: #ffffff;
  theme4: #ffffff;
  theme5: #ffffff;
}

/****** 功能色 ******/
#yn-success-color() {
  theme1: #40BD48;
  theme2: #40BD48;
  theme3: #40BD48;
  theme4: #40BD48;
  theme5: #40BD48;
}
#yn-warning-color() {
  theme1: #FFB829;
  theme2: #FFB829;
  theme3: #FFB829;
  theme4: #FFB829;
  theme5: #FFB829;
}
#yn-error-color() {
  theme1: #F54645;
  theme2: #F54645;
  theme3: #F54645;
  theme4: #F54645;
  theme5: #F54645;
}
#yn-link-color() {
  theme1: #1E88E5;
  theme2: #1377EB;
  theme3: #3E5D96;
  theme4: #2B6FD4;
  theme5: #498EFF;
}
#yn-money-color() {
  theme1: #F56A00;
  theme2: #F56A00;
  theme3: #F56A00;
  theme4: #F56A00;
  theme5: #F56A00;
}
#yn-link-bg-color() {
  theme1: #e7eff7;
  theme2: #E7F1FD;
  theme3: #E7EFF7;
  theme4: #E9F0FA;
  theme5: #ECF3FF;
}
#yn-success-bg-color() {
  theme1: #EBF8EC;
  theme2: #EBF8EC;
  theme3: #EBF8EC;
  theme4: #EBF8EC;
  theme5: #EBF8EC;
}
#yn-warning-bg-color() {
  theme1: #FFF7E9;
  theme2: #FFF7E9;
  theme3: #FFF7E9;
  theme4: #FFF7E9;
  theme5: #FFF7E9;
}
#yn-error-bg-color() {
  theme1: #FEECEC;
  theme2: #FEECEC;
  theme3: #FEECEC;
  theme4: #FEECEC;
  theme5: #FEECEC;
}

/****** 中性色 ******/
#yn-text-color() {
  theme1: #161823;
  theme2: #1A253B;
  theme3: #1A253B;
  theme4: #1A253B;
  theme5: #1A253B;
}
#yn-disabled-color() {
  theme1: #999999;
  theme2: #BCC1CC;
  theme3: #BCC1CC;
  theme4: #BCC1CC;
  theme5: #BCC1CC;
}
#yn-disabled-bg-color() {
  theme1: #f5f5f7;
  theme2: #F5F5F7;
  theme3: #F5F5F7;
  theme4: #F5F5F7;
  theme5: #F5F5F7;
}
#yn-text-color-secondary() {
  theme1: #444444;
  theme2: #4E5D78;
  theme3: #4E5D78;
  theme4: #4E5D78;
  theme5: #4E5D78;
}
#yn-border-color-base() {
  theme1: #dadce0;
  theme2: #E1E5EB;
  theme3: #E1E5EB;
  theme4: #E1E5EB;
  theme5: #E1E5EB;
}
#yn-background-color-light() {
  theme1: #ebeef4;
  theme2: #E7F1FD;
  theme3: #E7F1FD;
  theme4: #E5F4F1;
  theme5: #FBE5EA;
}
#yn-table-header-bg() {
  theme1: #EAECF1;
  theme2: #EAECF1;
  theme3: #EAECF1;
  theme4: #EAECF1;
  theme5: #EAECF1;
}
#yn-label-color() {
  theme1: #666666;
  theme2: #8894A8;
  theme3: #8894A8;
  theme4: #8894A8;
  theme5: #8894A8;
}
#yn-auxiliary-color() {
  theme1: #c8c9cc;
  theme2: #D0D3DB;
  theme3: #D0D3DB;
  theme4: #D0D3DB;
  theme5: #D0D3DB;
}
#yn-background-color() {
  theme1: #f4f4f7;
  theme2: #F5F7FA;
  theme3: #F5F7FA;
  theme4: #F5F7FA;
  theme5: #F5F7FA;
}
#yn-hover-bg-color() {
  theme1: #f3f3f3;
  theme2: #EDF1F7;
  theme3: #F3F3F3;
  theme4: #F2F9F8;
  theme5: #FDF2F5;
}
#yn-icon-bg-color() {
  theme1: #E1EBF9;
  theme2: #E1EBF9;
  theme3: #E1EBF9;
  theme4: #E5F4F1;
  theme5: #FBE5EA;
}

/****** 主题辅助色 ******/
#yn-primary-1() {
  // 同上 #yn-hover-bg-color
  theme1: #f3f3f3;
  theme2: #EDF1F7;
  theme3: #F3F3F3;
  theme4: #F2F9F8;
  theme5: #FDF2F5;
}

/****** 图表色 ******/
#yn-chart-1() {
  theme1: #5F89CF;
  theme2: #1377EB;
  theme3: #3E5D96;
  theme4: #019977;
  theme5: #EF5E5E;
}
#yn-chart-2() {
  theme1: #3B93D7;
  theme2: #3BB875;
  theme3: #3CB7CC;
  theme4: #E5B733;
  theme5: #FFB253;
}
#yn-chart-3() {
  theme1: #25A4A3;
  theme2: #4CCDFE;
  theme3: #25A4A3;
  theme4: #05A2DA;
  theme5: #F48CA2;
}
#yn-chart-4() {
  theme1: #697E95;
  theme2: #747CFB;
  theme3: #DDAB36;
  theme4: #0052CC;
  theme5: #9BC3E1;
}
#yn-chart-5() {
  theme1: #D3B000;
  theme2: #FF99E6;
  theme3: #C45551;
  theme4: #40BEB3;
  theme5: #63B2EE;
}
#yn-chart-6() {
  theme1: #DB742C;
  theme2: #CDA5F3;
  theme3: #B072C4;
  theme4: #F2D17B;
  theme5: #8E72C0;
}
#yn-chart-7() {
  theme1: #C14645;
  theme2: #EFD311;
  theme3: #D67AA7;
  theme4: #F89689;
  theme5: #DAB78A;
}
#yn-chart-8() {
  theme1: #A07AB3;
  theme2: #81E9E6;
  theme3: #1F77B4;
  theme4: #9987CD;
  theme5: #F29D38;
}
#yn-chart-9() {
  theme1: #7D6AB2;
  theme2: #8894A8;
  theme3: #9193AB;
  theme4: #B3BAC5;
  theme5: #FFC7AA;
}
#yn-main-frame-header-background-image() {
  theme1: linear-gradient(
    90deg,
    #0089dc,
    #0158ba
  );
  theme2: linear-gradient(
    90deg,
    #006BE6,
    #0052CC
  );
  theme3: linear-gradient(
    90deg,
    #292E3F,
    #292E3F
  );
  theme4: linear-gradient(
    90deg,
    #019977,
    #019977
  );
  theme5: linear-gradient(
    90deg,
    #E00033,
    #E00033
  );
}
