// 多语言静态菜单
const multilingualMenu = {
  // 计算规则模块
  calcRule: {
    menus: [
      {
        zh: "[...]",
        en: "[...]",
        type: "dbSquare"
      },
      {
        zh: "DB(...)",
        en: "DB(...)",
        type: "db"
      },
      {
        zh: {
          CURRENT_USER_LOCK: "解锁",
          OTHER_USER_LOCK: "锁定",
          NO_USER_LOCK: "锁定"
        },
        en: {
          CURRENT_USER_LOCK: "unLock",
          OTHER_USER_LOCK: "lock",
          NO_USER_LOCK: "lock"
        },
        type: {
          CURRENT_USER_LOCK: "unLock",
          OTHER_USER_LOCK: "lock",
          NO_USER_LOCK: "lock",
          type: "lock"
        }
      },
      {
        zh: "历史",
        en: "history",
        type: "history"
      },
      {
        zh: "保存",
        en: "save",
        type: "save"
      },
      {
        zh: "提交",
        en: "submit",
        type: "submit"
      }
    ],
    unLockBtnList: [
      {
        zh: "取消",
        en: "cancel",
        type: "default",
        onHandle: "onCancelLockModal"
      },
      {
        zh: "直接解锁",
        en: "Direct unlock",
        type: "primary",
        onHandle: "onDirectUnlock"
      },
      {
        zh: "提交并解锁",
        en: "Submit and unlock",
        type: "primary",
        onHandle: "onSubmitAndUnLock"
      }
    ]
  },
  cube: {},
  dim: {}
};
export default multilingualMenu;
