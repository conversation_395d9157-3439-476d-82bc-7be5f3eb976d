// 命名：项目名+模块名+具体字段(驼峰) 例如mdd项目计算规则模块: mdd+ClcRule+Unlock => mddClcRuleUnlock
// 公共多语言：项目名+Common+具体字段 例如: mdd+Common+Search => mddCommonSearch
const mdd_zh_CN = {
  mddCommonSearch: "搜索",
  mddCommonDimension: "维度",
  mddCommonSaveSuccess: "保存成功",
  mddCommonSaveFail: "保存失败",
  mddCommonSubmitSuccess: "提交成功",
  mddCommonSubmitFail: "提交失败",
  mddCommonPlaceholder: "请输入搜索内容",
  mddCommonCreate: "新增",
  mddCommonAdd: "添加",
  mddCalcRule: "计算规则",
  mddCalcRuleUnLock: "解锁",
  mddCalcRuleUnLockTitle: "解锁提示",
  mddCalcRuleUnLockMeasage: "规则内容未提交，是否提交并解锁？",
  mddCalcRuleDbSelectCubes: "选择多维数据集",
  mddCalcRuleHistoryVersion: "历史版本",
  mddCalcRuleDB: "DB",
  mddCalcRuleUnLockSuccess: "解锁成功",
  mddCalcRuleUnLockFail: "解锁失败",
  mddCalcRuleLockSuccess: "锁定成功",
  mddCalcRuleLockFail: "锁定失败",

  // 校验规则-start
  mddCommonRulesName: "名称",
  mddCommonRulesCode: "编号",
  mddCommonRulesCreatTime: "创建时间",
  mddCommonOperation: "操作",

  mddRulesMainAddChecks: "添加校验",
  mddRulesMainAddChecksSubmit: "确定",
  mddRulesMainAddChecksCancel: "取消",
  mddRulesMainAddNewCheck: "新增校验",
  mddRulesMainEditCheck: "编辑校验",
  mddRulesMainViewCheck: "查看校验",
  mddRulesMainBasicInfoTitle: "基本信息",
  mddRulesMainBasicInfoAffectObject: "影响对象",
  mddRulesMainBasicInfoCreater: "创建人",
  mddRulesMainRuleExpressionTitle: "规则表达式",
  mddRulesMainExpression: "表达式",
  mddRulesMainBasicInfoNameRule: "名称不能为空！",
  mddRulesMainBasicInfoNamePlaceholder: "请输入名称",
  mddRulesMainBasicInfoCodePlaceholder: "请输入编码",
  mddRulesMainBasicInfoAffectPlaceholder: "新选择维度",
  mddRulesMainAddSuccess: "添加成功",
  mddRulesMainEditSuccess: "修改成功",
  mddRulesMainDeleteSuccess: "删除成功",
  mddRulesMainDeleteConfirmTitle: "删除规则",
  mddRulesMainDeleteConfirmContent: "确定要删除规则吗？",
  mddRulesMainDeleteConfirmOkText: "确定",
  mddRulesMainDeleteConfirmCancelText: "取消",
  mddRulesMainTableUpdateTime: "更新时间",

  mddRulesAppTableCheckRules: "校验规则",
  mddRulesAppTableAllotForm: "分配表单",
  mddRulesAppAllot: "分配",
  mddRulesAppHeaderTitle: "规则应用",
  mddRulesAppAllotForm: "选择分配表单",
  mddRulesAppApplicableProcess: "是否适用流程",
  mddRulesAppCheckType: "校验类型",
  mddRulesAppCheckResult: "校验结果",
  mddRulesAppNoPass: "不通过",
  mddRulesAppPass: "通过",
  mddRulesAppTipColor: "提示颜色",
  mddRulesAppTipText: "提示文本",
  mddRulesAppAllotFormPlaceholder: "请选择分配表单",
  mddRulesAppAllotFormRule: "分配表单不能为空！",
  mddRulesAppTipTextRule: "提示文本不能为空！",
  mddRulesAppCheckResultAlertRule: "至少要指定一种校验结果！",
  mddRulesAppTipTextAlertRule: "勾选后提示文本不能为空！",
  mddRulesAppAllotSuccess: "分配成功",
  // 校验规则-end
  mddDimensionName: "名称",
  mddDimensionInputName: "请输入名称",
  mddDimensionNameRule: "名称不能为空！",
  mddDimensionDesc: "说明",
  mddDimensionInputDesc: "请输入说明",
  mddDimensionShowName: "别名",
  mddDimensionInputShowName: "请输入别名",
  mddDimensionCode: "编码",
  mddDimensionInputCode: "请输入编码",
  mddDimensionMemberTypes: "数据类型",
  mddDimensionInputMemberTypes: "请选择数据类型",
  mddDimensionMemberTypesRule: "数据类型不能为空！",
  mddDimensionMemberSelect: "下拉框",
  mddDimensionMemberSelectType: "选择形式",
  mddDimensionMemberSelectTypeRule: "请选择形式！",
  mddDimensionMemberSelectTypeRuleInfo: "请选择形式",
  mddDimensionSelectDimension: "选择维度",
  mddDimensionSelectDimensionRule: "维度不能为空！",
  mddDimensionSelectDimensionInfo: "请选择维度",
  mddDimensionAddMember: "添加成员",
  mddDimensionWeight: "权重",
  mddDimensionWeightRule: "权重不能为空！",
  mddDimensionInputWeight: "请输入权重",
  mddDimensionStatus: "状态",
  mddDimensionStatusRule: "状态不能为空！",
  mddDimensionSelectStatus: "请选择状态",
  mddDimensionRelation: "关联",
  mddDimensionEnum: "枚举",
  mddDimensionText: "文本",
  mddDimensionNum: "数值",
  mddDimensionSelect: "请选择",
  mddDimensionInput: "请输入",
  mddDimensionEnable: "启用",
  mddDimensionDisable: "禁用",
  mddDimensionRelationError: "请先选择关联维度",
  mddDimensionLevel: "层级",
  mddDimensionAttr: "属性",
  mddDimensionAddSuccess: "添加成功",
  mddDimensionEditSuccess: "修改成功",
  mddDimensionType: "类型",
  mddDimensionTypeRule: "类型不能为空！",
  mddDimensionSelectType: "请选择维度类型",
  mddAddAttribute: "添加属性",
  mddEdit: "编辑",
  mddDelete: "删除",
  mddAttributeAddTitle: "自定义属性",
  mddDelAttribute: "删除属性?",
  mddDelAttributeSure: "确定要删除属性吗？",
  mddDelSuccess: "删除成功",
  mddAttributeTypeSelect: "请选择属性类型",
  mddSelectMember: "选成员",
  mddRelationMember: "请关联维度",
  mddDimensionTotal: "维度统计：共",
  mddDimensionTotalNum: "个维度",
  mddDimensionSearch: "请输入搜索内容",
  mddDimensionSearchInfo: "搜不到关键字内容",
  mddDimensionAdd: "新增维度",
  mddImport: "导入",
  mddExport: "导出",
  mddAddError: "添加失败",
  mddEditError: "修改失败",
  mddAddMember: "新增成员",
  mddAddSampleMember: "新增同级成员",
  mddAddChildMember: "新增子级成员",
  mddCopy: "复制",
  mddPaste: "粘贴",
  mddEditAttribute: "编辑属性",
  mddDelMember: "删除成员?",
  mddDelMemberSure: "确定要删除成员吗？",
  mddSave: "保存",
  mddNameRepeat: "名称不能重复!",
  mddSelectMembers: "选择成员",
  mddSourceTitle: "备选项",
  mddTargetTitle: "已选项",
  mddSubsetSearchError: "搜不到静态或者动态子集关键字内容!",
  mddSubsetTypeError: "子集类型不能为空！",
  mddSubsetType: "请选择子集",
  mddSubsetSet: "设置子集",
  mddSubsetDynamic: "动态子集",
  mddSubsetStatic: "静态子集",
  mddSubsetAddMember: "请添加成员！",
  mddSubsetTotal: "子集统计：共",
  mddSubsetTotalNum: "个子集",
  mddSubsetAdd: "新增子集",
  mddSubsetDel: "删除子集?",
  mddSubsetDelSure: "确定要删除子集吗？",
  mddSubsetEdit: "编辑子集",
  // cube
  mddCubes: "多维数据集",
  mddCubesDataSet: "数据集",
  mddCubesAlternative: "备选项",
  mddCubesSelected: "已选项",
  mddCubesMoveUp: "上移",
  mddCubesMoveDown: "下移",
  mddCubesMoveTop: "置顶",
  mddCubesMoveBottom: "置底"
};
export default mdd_zh_CN;
