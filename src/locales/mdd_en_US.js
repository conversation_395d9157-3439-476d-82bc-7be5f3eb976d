// 命名：项目名+模块名+具体字段(驼峰) 例如mdd项目计算规则模块: mdd+ClcRule+Unlock => mddClcRuleUnlock
// 公共多语言：项目名+Common+具体字段 例如: mdd+Common+Search => mddCommonSearch
const mdd_en_US = {
  mddCommonSearch: "search",
  mddCommonDimension: "dimension",
  mddCommonSaveSuccess: "Save successfully",
  mddCommonSaveFail: "Save failed",
  mddCommonSubmitSuccess: "Submit successfully",
  mddCommonSubmitFail: "Failure to submit",
  mddCommonPlaceholder: "Please enter the search content",
  mddCommonCreate: "",
  mddCommonAdd: "",
  mddCalcRule: "Calculation rule",
  mddCalcRuleUnLock: "unLock",
  mddCalcRuleUnLockTitle: "Unlock the prompt",
  mddCalcRuleUnLockMeasage:
    "Rule content not submitted, whether to submit and unlock？",
  mddCalcRuleDbSelectCubes: "Select the cube",
  mddCalcRuleHistoryVersion: "Historical version",
  mddCalcRuleDB: "DB",
  mddCalcRuleUnLockSuccess: "Unlock success",
  mddCalcRuleUnLockFail: "Unlock failure",
  mddCalcRuleLockSuccess: "Lock in success",
  mddCalcRuleLockFail: "lock failed",
  // 校验规则-start
  mddCommonRulesName: "",
  mddCommonRulesCode: "",
  mddCommonRulesCreatTime: "",
  mddCommonOperation: "",

  mddRulesMainAddChecks: "",
  mddRulesMainAddChecksSubmit: "",
  mddRulesMainAddChecksCancel: "",
  mddRulesMainAddNewCheck: "",
  mddRulesMainEditCheck: "",
  mddRulesMainViewCheck: "",
  mddRulesMainBasicInfoTitle: "",
  mddRulesMainBasicInfoAffectObject: "",
  mddRulesMainBasicInfoCreater: "",
  mddRulesMainRuleExpressionTitle: "",
  mddRulesMainExpression: "",
  mddRulesMainBasicInfoNameRule: "",
  mddRulesMainBasicInfoNamePlaceholder: "",
  mddRulesMainBasicInfoCodePlaceholder: "",
  mddRulesMainBasicInfoAffectPlaceholder: "",
  mddRulesMainAddSuccess: "",
  mddRulesMainEditSuccess: "",
  mddRulesMainDeleteSuccess: "",
  mddRulesMainDeleteConfirmTitle: "",
  mddRulesMainDeleteConfirmContent: "",
  mddRulesMainDeleteConfirmOkText: "",
  mddRulesMainDeleteConfirmCancelText: "",
  mddRulesMainTableUpdateTime: "",

  mddRulesAppTableCheckRules: "",
  mddRulesAppTableAllotForm: "",
  mddRulesAppAllot: "",
  mddRulesAppHeaderTitle: "",
  mddRulesAppAllotForm: "",
  mddRulesAppApplicableProcess: "",
  mddRulesAppCheckType: "",
  mddRulesAppCheckResult: "",
  mddRulesAppNoPass: "",
  mddRulesAppPass: "",
  mddRulesAppTipColor: "",
  mddRulesAppTipText: "",
  mddRulesAppAllotFormPlaceholder: "",
  mddRulesAppAllotFormRule: "",
  mddRulesAppTipTextRule: "",
  mddRulesAppCheckResultAlertRule: "",
  mddRulesAppTipTextAlertRule: "",
  mddRulesAppAllotSuccess: "",
  // 校验规则-end
  mddDimensionName: "",
  mddDimensionInputName: "",
  mddDimensionNameRule: "",
  mddDimensionDesc: "",
  mddDimensionInputDesc: "",
  mddDimensionShowName: "",
  mddDimensionInputShowName: "",
  mddDimensionCode: "",
  mddDimensionInputCode: "",
  mddDimensionMemberTypes: "",
  mddDimensionInputMemberTypes: "",
  mddDimensionMemberTypesRule: "",
  mddDimensionMemberSelect: "",
  mddDimensionMemberSelectType: "",
  mddDimensionMemberSelectTypeRule: "",
  mddDimensionMemberSelectTypeRuleInfo: "",
  mddDimensionSelectDimension: "",
  mddDimensionSelectDimensionRule: "",
  mddDimensionSelectDimensionInfo: "",
  mddDimensionAddMember: "",
  mddDimensionWeight: "",
  mddDimensionWeightRule: "",
  mddDimensionInputWeight: "",
  mddDimensionStatus: "",
  mddDimensionStatusRule: "",
  mddDimensionSelectStatus: "",
  mddDimensionRelation: "",
  mddDimensionEnum: "",
  mddDimensionText: "",
  mddDimensionNum: "",
  mddDimensionSelect: "",
  mddDimensionInput: "",
  mddDimensionEnable: "",
  mddDimensionDisable: "",
  mddDimensionRelationError: "",
  mddDimensionLevel: "",
  mddDimensionAttr: "",
  mddDimensionAddSuccess: "",
  mddDimensionEditSuccess: "",
  mddDimensionType: "",
  mddDimensionTypeRule: "",
  mddDimensionSelectType: "",
  mddAddAttribute: "",
  mddEdit: "",
  mddDelete: "",
  mddAttributeAddTitle: "",
  mddDelAttribute: "",
  mddDelAttributeSure: "",
  mddDelSuccess: "",
  mddAttributeTypeSelect: "请选择属性类型",
  mddSelectMember: "选成员",
  mddRelationMember: "请关联维度",
  mddDimensionTotal: "维度统计：共",
  mddDimensionTotalNum: "个维度",
  mddDimensionSearch: "",
  mddDimensionSearchInfo: "",
  mddDimensionAdd: "",
  mddImport: "",
  mddExport: "",
  mddAddError: "",
  mddEditError: "",
  mddAddMember: "",
  mddAddSampleMember: "",
  mddAddChildMember: "",
  mddCopy: "",
  mddPaste: "",
  mddEditAttribute: "",
  mddDelMember: "",
  mddDelMemberSure: "",
  mddSave: "",
  mddNameRepeat: "",
  mddSelectMembers: "",
  mddSourceTitle: "",
  mddTargetTitle: "",
  mddSubsetSearchError: "",
  mddSubsetTypeError: "",
  mddSubsetType: "",
  mddSubsetSet: "",
  mddSubsetDynamic: "",
  mddSubsetStatic: "",
  mddSubsetAddMember: "",
  mddSubsetTotal: "",
  mddSubsetTotalNum: "",
  mddSubsetAdd: "",
  mddSubsetDel: "",
  mddSubsetDelSure: "",
  mddSubsetEdit: "",
  // cube
  mddCubes: "",
  mddCubesDataSet: "",
  mddCubesAlternative: "",
  mddCubesSelected: "",
  mddCubesMoveUp: "",
  mddCubesMoveDown: "",
  mddCubesMoveTop: "",
  mddCubesMoveBottom: ""
};
export default mdd_en_US;
