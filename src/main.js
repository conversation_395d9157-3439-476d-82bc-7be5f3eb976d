import Vue from "vue";
import App from "./App.vue";
import PlatformCommunication from "@/utils/platformCommunication";
import "normalize.css/normalize.css";
import "./commonLess/layout.less";
import YnP1 from "yn-p1";
const { i18n } = YnP1.instances;
import { LOG, APPS, BACKEND } from "./config/SETUP";
import Logger from "yn-p1/libs/modules/log/logger";
import store from "./store/";
// import VueRouter from "vue-router";
import router from "@/router/index.js";
import "@/themes/theme.less";
import filters from "@/utils/filters.js";
import mixin from "@/mixin/index";
import "@/utils/ynp1.js";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
import SvgIcon from "@/components/ui/SvgIcon.vue";
import "@/directive";
import "../public/iconfont/iconfont.js";
import "../public/iconfont/iconfont.css";
import DsUtils from "yn-p1/libs/utils/DsUtils";
const urlLogLevel = UrlUtils.getQuery("logLevel");
// url上的自定参数的优先级最高
const logLevel = urlLogLevel ? Number(urlLogLevel) : LOG.LEVEL;
Logger.option("level", logLevel);
Logger.warn(
  `Platform Log Level: ${
    logLevel === 2
      ? "2 - Warn"
      : logLevel === 3
        ? "3 - Info"
        : logLevel === 4
          ? "4 - Log"
          : ""
  }`
);

Vue.component("svg-icon", SvgIcon);

Vue.config.productionTip = false;
const selfTab = UrlUtils.getQuery("selfTab"); // 对应 平台 应用类别单页面，及使用合并自己的页签
Vue.prototype.selfTab = !!selfTab;
PlatformCommunication.init(!!selfTab);
// Vue.use(VueRouter);
Vue.use(filters);
YnP1.setup(LOG, APPS, BACKEND);
YnP1.responsive.init();

window.$t = Vue.prototype.$t = function(key, defaultText, locale, values) {
  const queryLang = UrlUtils.getQuery("lang") || "";
  const lang =
    queryLang ||
    DsUtils.getSessionStorageItem("lang") ||
    DsUtils.getLocalStorageItem("lang") ||
    "zh_CN";
  const langData = i18n.getLocaleMessage(lang);
  if (langData.hasOwnProperty(key)) {
    return i18n.t(key, locale, values);
  } else {
    const msg = defaultText || key || "";
    return msg;
  }
};

const APPVER_WEB = {
  appName: "CONSOLIDATION",
  branchName:
    process.env.VUE_APP_BRANCH_NAME !== "undefined"
      ? process.env.VUE_APP_BRANCH_NAME
      : "未指定",
  version:
    process.env.VUE_APP_VERSION !== "undefined"
      ? process.env.VUE_APP_VERSION
      : "未输入",
  buildMode:
    process.env.VUE_APP_BUILDMODE !== "undefined"
      ? process.env.VUE_APP_BUILDMODE
      : "未输入",
  buildDate: process.env.VUE_APP_BUILDTIME
};

// 历史结构，about 页面使用的数据
const oldVersion = {
  branch:
    process.env.VUE_APP_BRANCH_NAME !== "undefined"
      ? process.env.VUE_APP_BRANCH_NAME
      : "未指定",
  ver:
    process.env.VUE_APP_VERSION !== "undefined"
      ? process.env.VUE_APP_VERSION
      : "未输入",
  buildMode:
    process.env.VUE_APP_BUILDMODE !== "undefined"
      ? process.env.VUE_APP_BUILDMODE
      : "未输入",
  date: process.env.VUE_APP_BUILDTIME
};

window.APPVER = {
  web: {
    consolidationWeb: { ...APPVER_WEB }
  },
  ...oldVersion
};

const APPVERLIST_WEB =
  DsUtils.getSessionStorageItem("APPVERLIST_WEB", {
    isJson: true
  }) || {};
APPVERLIST_WEB["CONSOLIDATION"] = APPVER_WEB;

DsUtils.setSessionStorageItem("APPVERLIST_WEB", APPVERLIST_WEB, {
  isJson: true
});

// console.log(router);
Vue.mixin(mixin);
new Vue({
  i18n,
  // router will be updated whild App is created
  router,
  store,
  render: h => h(App)
}).$mount("#app");
