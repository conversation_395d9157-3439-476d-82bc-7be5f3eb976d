import { Decimal } from "decimal.js";
import lang from "@/mixin/lang";
const { $t_common } = lang;
/*
 * 千分制金融数据处理
 * @params {num：原始数据， len: 保留精度，默认2，strict：默认false， 是否严格展示精度}
 * return String 千分制金融数据
 */
function toFinance(num, len = 2, strict = false) {
  if (isFinite(String(num)) && num !== "") {
    const number = new Decimal(num);

    const fixNum = number.toFixed(len); // 部分精度
    // 当严格模式下，全精度和部分精度等大，则取部分精度： 2.32000 == 2.32， len = 2
    const isSame = number.equals(new Decimal(fixNum));
    // 精度不足
    const floatNum = toFloat(number);
    const dotindex = floatNum.indexOf(".");
    const islessLen = dotindex < 0 || floatNum.length - dotindex < len + 1;
    if (!strict || isSame || islessLen) {
      if ((fixNum + "").indexOf(".") > -1) {
        return toFloat(fixNum).replace(/(\d)(?=(\d{3})+\.)/g, "$1,");
      } else {
        return toFloat(fixNum).replace(/(\d{1,3})(?=(?:\d{3})+$)/g, "$1,");
      }
    } else {
      return floatNum.replace(/(\d)(?=(\d{3})+\.)/g, "$1,");
    }
  } else {
    return num;
  }
}

/*
 * 大数 科学计数法转浮点数（不考虑非标科学技术数值： 23.3e+10 X  2.23e+11 √）
 * @params {num：原始数据， max: 大于该数则不转换科学计数}, 默认：10位，
 * @return String float number
 */
function toFloat(num, len = 2, max = 9999999999) {
  // 处理不需要转换的数字
  var str = "" + num;
  if (!/e/i.test(str)) {
    return str;
  }
  if (new Decimal(num).greaterThan(new Decimal(max))) {
    return str;
  }
  const allmatchs = str.match(/(\-{0,1})(\d(.\d+){0,1})e(\-|\+)(\d+)/);
  const sign = allmatchs[1]; // 正负号
  const direct = allmatchs[4]; // 指数符号
  const data = allmatchs[2]; // 数据部分
  const elen = parseInt(allmatchs[5]); // 指数，即 0 长度

  const othernumlen = data.slice(2).length;
  const number = data.slice(0, 1) + data.slice(2);

  if (direct === "-") {
    const Arr = new Array(elen).fill("0");
    Arr.splice(1, 0, ".");
    return sign + Arr.join("") + number;
  } else {
    if (elen - othernumlen > 0) {
      const Arr = new Array(elen - othernumlen).fill("0");
      return sign + number + Arr.join("");
    } else {
      return sign + number.slice(0, elen + 1) + "." + number.slice(elen);
    }
  }
}

/*
 * 千分制数值
 * @params {num number 原始数据}
 */
function toThousands(num) {
  return (num + "").replace(/(\d{1,3})(?=(?:\d{3})+$)/g, "$1,");
}

/*
 * 空文本占位文本
 * @params {text：String 原始数据， preText: String 占位文本}
 */
function preSetDesc(text, preText = $t_common("no_data")) {
  return text === "" || new RegExp(text).test(preText) ? preText : text;
}
const filters = {
  toFinance,
  toFloat,
  toThousands,
  preSetDesc
};
export default Vue => {
  // 导入的 filters 是一个对象，使用Object.keys()方法，得到一个由key组成的数组，遍历数据，让key作为全局过滤器的名字，后边的是key对应的处理函数，这样在任何一个组件中都可以使用全局过滤器了
  Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key]);
  });
};

export { toFinance };
