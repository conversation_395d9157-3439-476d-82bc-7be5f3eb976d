import cloneDeep from "lodash/cloneDeep";

/**
 * 将树形数据通过深度优先进行铺平
 * @param {Array} tree 树形数据
 * @returns {Array} res 铺平之后的数据
 */
function pavingTree(tree) {
  const copyTree = cloneDeep(tree);
  const res = [];
  while (copyTree.length) {
    const curItem = copyTree.shift();
    res.push(curItem);
    if (curItem.children && curItem.children.length) {
      copyTree.unshift(...curItem.children);
    }
  }
  return res;
}

/**
 * 对数组数据添加属性
 * @param {Array} data 数组数据
 */
function addKeyToData(data) {
  return data.map(item => {
    const { id, name } = item;
    return {
      ...item,
      key: `${id}-self`,
      dimMemberId: id,
      dimMemberName: name,
      label: name,
      memberType: 1,
      memberTypeValue: "self",
      objectId: `${id}-self`,
      title: `成员(${name})`,
      type: "member",
      value: id
    };
  });
}

/**
 * 给树形数据添加key，且如果 children 为空，则删除该属性，适用 yn-select-tree
 * @param {Array} tree 树形数据
 */
function addKeyToTree(tree) {
  const loop = arr => {
    arr.forEach(element => {
      element.key = element.menuId;
      element.label = element.menuName;
      if (element.children) {
        if (element.children.length) {
          loop(element.children);
        } else {
          delete element.children;
        }
      }
    });
  };
  loop(tree);
}

/**
 * 处理任务流模板左侧树
 * @param {Array} tree 树形数据
 */
function handleLefTree(tree) {
  const loop = data => {
    data.forEach(item => {
      item.key = item.id;
      item.title = item.name;
      item.isLeaf = !!item.parentId;
      item.scopedSlots.title = "custom";
      if (item.children && item.children.length) {
        loop(item.children);
      }
    });
  };
  loop(tree);
}

/**
 * 处理选择框数据
 * @param {Array} data 数组
 */
function handleDim(arr) {
  const loop = data => {
    data.forEach(item => {
      item.key = item.dimCode;
      item.label = item.dimName;
      item.value = item.dimCode;
      if (item.children && item.children.length) {
        loop(item.children);
      }
    });
  };
  loop(arr);
}

/**
 * 处理穿梭框返现数据（生效公司、生效期间）
 * @param {*} arr 数组
 */
function enchoTransferData(arr) {
  if (!arr || arr.length === 0) return [];
  return arr.map(item => ({
    dimMemberId: item.dimMemberId,
    dimMemberName: item.dimMemberName,
    dimCode: "",
    key: `${item.dimMemberId}-self`,
    label: item.dimMemberName,
    memberType: 1,
    memberTypeValue: "self",
    objectId: `${item.dimMemberId}-self`,
    shardim: "",
    title: `${item.dimMemberName}`,
    type: "member",
    value: item.dimMemberId
  }));
}

/**
 *  树形数组构造成树 item 需要 key、 parentKey
 * @param {树形数组} treeList
 * @returns 构造的树
 */
function buildTree(treeList) {
  const keyMapItem = {};
  treeList.forEach(item => {
    item.children = [];
    keyMapItem[item.key] = item;
  });
  treeList.forEach(item => {
    const parentItem = item.parentKey && keyMapItem[item.parentKey];
    if (parentItem) {
      parentItem.children.push(item);
    }
  });
  return Object.values(keyMapItem).filter(item => !item.parentKey);
}

/**
 * 处理表单列表数据
 * @param {Array} arr
 */
function handleFormData(arr) {
  const loop = data => {
    data.forEach(item => {
      item.key = item.data.elementId;
      item.title = item.data.elementName;
      item.parentKey = item.data.parentId;
      item.children = item.subNodes;
      item.scopedSlots = {
        title: "custom"
      };
      if (item.children && item.children.length) {
        loop(item.children);
      }
    });
  };
  loop(arr);
}

/**
 * 找到该节点的所有父节点key
 * @param {Array} arr
 * @param {String} key
 * @returns 返回 keys
 */
function findParentKeys(arr, key) {
  const targetKey = [...key];
  const keys = [];
  while (targetKey.length) {
    const k = targetKey.shift();
    const parentItem = arr.find(item => item.key === k);
    const parentKey = parentItem && parentItem.parentKey;
    parentKey && keys.push(parentKey);
    parentKey && targetKey.push(parentKey);
  }
  return keys;
}

/**
 * 组合函数
 * @param  {...any} fns 需要调用的函数
 * @returns 调用函数后最终的结果
 */
const composeFuns = (...fns) =>
  fns.reduce((fn1, fn2) => params => fn2(fn1(params)));

export {
  pavingTree,
  addKeyToData,
  addKeyToTree,
  composeFuns,
  handleLefTree,
  handleDim,
  buildTree,
  enchoTransferData,
  handleFormData,
  findParentKeys
};
