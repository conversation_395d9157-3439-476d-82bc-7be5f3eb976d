/**
 * @desc 日记账模板处理左侧树 数据
 * @data 接口返回的数据
 */

export function handleTempTree(data) {
  if (!Array.isArray(data)) return;
  let arr = [...data];
  while (arr.length) {
    const currentItem = arr.shift();
    currentItem.key = currentItem.objectId;
    currentItem.title = currentItem.name;
    currentItem.children = currentItem.subNodes || [];
    currentItem.scopedSlots = {
      title: "custom"
    };
    if (currentItem.subNodes && currentItem.subNodes.length > 0) {
      arr = arr.concat(currentItem.subNodes);
    }
  }
  return data;
}

/**
 *
 * @param {data} 树状数据
 * @returns 铺平之后的数组
 */
export function pavingTree(data) {
  const result = [];
  if (!Array.isArray(data)) return;
  const loop = (data, res) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const children = node.children || node.subNodes;
      res.push(node);
      if (children && children.length) {
        loop(children, res);
      }
    }
  };
  loop(data, result);
  return result;
}

/**
 *  日记账模板右侧 维度下拉列表数据处理
 * @param {data} 维度下拉列表接口返回数据
 * @returns 处理后的数据
 */
export function addParamsToData(data) {
  if (!data || !Array.isArray(data)) return [];
  let arr = [...data];
  while (arr.length) {
    const currentItem = arr.shift();
    const name = currentItem.name;
    const id = currentItem.id;
    currentItem.key = `${name}-${id}`;
    currentItem.objectId = currentItem.id;
    currentItem.label = currentItem.name;
    if (currentItem.children && currentItem.children.length > 0) {
      arr = arr.concat(currentItem.children);
    }
  }
  return data;
}

/**
 * 处理维度信息
 * (基础维 baseInfo、明细维 detailInfo、不展示维 notDisplayInfo)数据
 * @param {data} 维度信息数据
 * @returns 返回处理后的数据
 */
export function handleDimInfoData(data, type = "notDisplayInfo") {
  if (!data || !Array.isArray(data)) return [];
  let arr = [...data];
  while (arr.length) {
    const currentItem = arr.shift();
    if (!currentItem) return;
    currentItem.key = currentItem.dimId;
    currentItem.objectId = currentItem.dimId;
    currentItem.label = currentItem.dimMultiName;
    // 基础、明细维 是穿梭框需要 通过 domInfo 显示数据
    let members = currentItem.members;
    if (type !== "notDisplayInfo") {
      const memberExp = currentItem.members
        ? JSON.parse(currentItem.members)
        : "";
      members = formatRequestParams(memberExp, true);
    }
    currentItem.dimInfo = {
      dimCode: currentItem.dimCode,
      dimId: currentItem.dimId,
      dimName: currentItem.dimMultiName,
      dynamicOrStatic: "dynamic",
      members: type === "notDisplayInfo" ? "" : members,
      selectedItem: currentItem.members || []
    };

    if (currentItem.children && currentItem.children.length > 0) {
      arr = arr.concat(currentItem.children);
    }
  }
  return data;
}

/**
 *
 * @param {exprObj}  表达式
 * @returns string
 * 返回表达式字符串并且将 memberType 改成解析接口需要的 member字段
 */
export function formatRequestParams(exprObj = {}, flag = false) {
  const expr = JSON.parse(JSON.stringify(exprObj));
  const dimMemberExps = {};
  if (Object.keys(expr).length) {
    Object.keys(expr).map(item => {
      let propsName = item;
      // 入参表达式 成员 传 member
      if (item === "memberType") {
        propsName = "member";
      }
      // 返回参数 反显 需要 memberType
      if (item === "member") {
        propsName = "memberType";
      }
      dimMemberExps[propsName] = expr[item];
    });
  }
  if (flag) return dimMemberExps;
  return JSON.stringify(dimMemberExps);
}
/**
 * 获取对象数组的维度成员名称并进行拼接
 * 用于模板非编辑状态下的展示
 * @param {arr} 对象数组
 * @param {field} 需要获取的字段
 */
export function getNameFromArr(arr, type, dimId) {
  if (!arr) return;
  if (type === "notDisplayedInfo") {
    const list = Array.isArray(arr) ? arr : JSON.parse(arr);
    const nameList = list.map(item => item["dimMemberName"]);
    return nameList.join("、");
  }
}

/**
 * 包装 promise 防止 promise.all 其中一个接口出问题导致获取不到数据
 * @param {p} promise
 */
export function wrapPromise(p) {
  return new Promise(resolve => {
    p.then(res => {
      resolve({
        res,
        isOk: true
      });
    }).catch(err => {
      resolve({
        err,
        isOk: false
      });
    });
  });
}

/**
 * 默认选中数组列表的第一项（父项不可选中）
 * @param {Array} data
 * @returns {String} result
 */
export function defaultChooseFirst(data) {
  if (!Array.isArray(data)) return;
  const copyData = [...data];
  let result = null;
  while (copyData.length) {
    const currItem = copyData.shift();
    const { isLeaf, children } = currItem;
    if (isLeaf) {
      result = currItem;
      break;
    } else {
      if (children && children.length) {
        copyData.unshift(...children);
      }
    }
  }
  return result;
}

/**
 *  替换对象中空字段为'-'
 * @param {Object 对象} obj
 * @returns Object
 */
export function replaceEmptyFields(obj) {
  // 遍历对象的属性
  for (const prop in obj) {
    // 判断属性是否为空
    if (obj.hasOwnProperty(prop) && obj[prop] === "") {
      // 将空属性替换为短横线
      obj[prop] = "-";
    }
  }
  return obj;
}
