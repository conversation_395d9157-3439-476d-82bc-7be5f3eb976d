.resize-table-th .vdr {
  height: 100% !important;
  width: 100% !important;
  padding: 0.4375rem 1rem !important;
  border: none;
  transform: none !important;
  position: relative !important;
}

.resize-table-th {
  padding: 0 !important;
}

.resize-table-th:hover .handle-mr {
  background: content-box #ccc;
}

.resize-table-th .handle {
  cursor: col-resize;
  border: none;
  box-shadow: none !important;
}

.resize-table-th .handle-mr {
  width: 0.375rem !important;
  border-left: 2px solid transparent;
  border-right: 2px solid transparent;
  z-index: 2;
  cursor: col-resize;
  background: inherit;
  height: calc(100% - 0.75rem) !important;
  top: 0.375rem !important;
  right: 0 !important;
  display: block !important;
}
