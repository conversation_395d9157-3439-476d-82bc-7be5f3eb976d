/* eslint-disable no-proto */
import "vue-draggable-resizable-gorkys/dist/VueDraggableResizable.css";
import vdr from "vue-draggable-resizable-gorkys";
import "./index.less";
import _debounce from "lodash.debounce";
const OAM = ["push", "pop", "shift", "unshift", "short", "reverse", "splice"];

/**
 * 表格拖动工具
 * @param {*} init function: () => ({columns = [], left = 0, right = 0, table, callback})
 *  columns: 表格列集合
 *  left: 左侧固定列数
 *  right: 右侧固定列数
 *  table: table ref 引用
 *  callback: 列拖动后的回调
 * @returns 响应式表头
 */
export function getComponents(init) {
  return {
    header: {
      cell: (h, props, children = []) => {
        const { columns = [], left = 0, right = 0, table, callback } = init();
        const { key, ...restProps } = props;
        overrideArrayProto(columns, () => {
          fixWidth(columns, left, right, table, callback);
        });
        const col = columns.find(col => {
          const k = col.dataIndex || col.key;
          return k === key;
        });

        if (!col || !col.width) {
          return h(
            "th",
            {
              ...restProps
            },
            [...children]
          );
        }
        const { minWidth = 80, maxWidth } = col || {};
        const dragProps = {
          key: col.dataIndex || col.key,
          class: "",
          attrs: {
            w: col.width,
            h: "auto",
            axis: "x",
            minWidth,
            maxWidth,
            draggable: false,
            resizable: true,
            handles: ["mr"]
          },
          on: {
            resizing: (l, t, w) => {
              col.width = Math.max(w, 1);
              fixWidth(columns, left, right, table, callback);
            }
          }
        };
        const drag = h(
          vdr,
          {
            ...dragProps
          },
          [...children]
        );
        return h(
          "th",
          {
            ...restProps,

            class: "resize-table-th"
          },
          [drag]
        );
      }
    }
  };
}

// 通过dom精准获取宽度
const thClass = ".ant-table-scroll .ant-table-header .ant-table-thead th";

// 修复 yn-table 拖动列互相影响bug
const fixWidth = _debounce(function(columns, left, right, table, callback) {
  const doms = table.$el.querySelectorAll(thClass);
  const len = doms.length - 1;
  const keys = columns.map(item => item.key);
  Array.from(doms)
    .filter(dom => keys.includes(dom.getAttribute("key")))
    .forEach((dom, index) => {
      if (index < left || len - index < right) return;
      columns[index].width = dom.offsetWidth;
    });
  callback && callback(columns);
}, 300);

// 监控数组变动
function overrideArrayProto(array, callback) {
  // 保存原始 Vue Array 原型
  var originalProto = array.__proto__;
  // 通过 Object.create 方法创建一个对象，该对象的原型是Vue Array.prototype
  var overrideProto = Object.create(array.__proto__);
  var result;
  // 遍历要重写的数组方法
  OAM.forEach(method => {
    Object.defineProperty(overrideProto, method, {
      value: function() {
        // 调用原始原型上的方法
        result = originalProto[method].apply(this, arguments);
        callback();
        return result;
      }
    });
  });
  // 最后 让该数组实例的 __proto__ 属性指向 假的原型 overrideProto
  array.__proto__ = overrideProto;
}
