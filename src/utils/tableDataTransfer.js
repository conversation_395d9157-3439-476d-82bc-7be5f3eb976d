/**
 * @desc 获取表格数据
 * @params {Object} data
 * @params {Object} mappingObj 映射对象 {单元格字段:单元格类型值}
 *
*/
const DEFAULT_CELL_TYPE = "d0ff5b0d216b4c0d9a7b1e139ff7656a";// 默认单元格类型 字符串
class RowData {
  constructor(rowData, mappingObj, keyName) {
    const { key } = rowData;
    this.v = rowData[keyName];
    this.objectId = key;
    this.rowKey = key;
    this.readOnly = false;
    this.cellDataTypeId = mappingObj[keyName] || DEFAULT_CELL_TYPE;
  }
}
const getTableData = (data = {}, mappingObj) => {
  const { list = [], isLastPage, pageSize, total, startRow } = data;
  const items = list.map(item => {
    const talbeData = [];
    const res = {};
    Object.keys(item).forEach(keyName => {
      if (keyName !== "key") {
        res[keyName] = new RowData(item, mappingObj, keyName);
      }
    });
    res.objectId = item.key;
    talbeData.push(res);
  });
  return {
    count: total,
    totalCount: total,
    limit: pageSize,
    offset: startRow,
    hasMore: isLastPage,
    items
  };
};
export default getTableData;
