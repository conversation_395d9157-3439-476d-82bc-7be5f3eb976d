// -------- this is a ynp1 function hack js-----------
import UiUtils from "yn-p1/libs/utils/UiUtils";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import { isType } from "@/utils/common";

/**
 * 动态弹框用于tab切换，模态框动态显隐
 */
const utils = UiUtils;
const props = Object.getOwnPropertyNames(utils);
for (const key of props) {
  const _origin = utils[key];
  if (
    !isType(_origin, "Function") ||
    /^is/.test(key) ||
    /^_/.test(key) ||
    /^get/.test(key)
  ) { continue; }
  utils[key] = (...info) => {
    const Id = "ex-" + AppUtils.generateUniqueId();
    if (
      info &&
      isType(info[0], "Object") &&
      (info[0].hasOwnProperty("content") || info.hasOwnProperty("title"))
    ) {
      info[0].class = info[0].class ? `${info[0].class} ${Id}` : Id;
    }
    const modalId = _origin.apply(utils, info);
    if (isType(modalId, "Object")) {
      modalId.Id = Id;
      modalId.hide = () => {
        document.querySelector("." + Id).style.display = "none";
      };
      modalId.show = () => {
        document.querySelector("." + Id).style.display = "block";
      };
      modalId.exist = () => {
        return !!document.querySelector("." + Id);
      };
    }
    return modalId;
  };
}
export default utils;
