const SCROLLBARWIDTH = 10; // 滚动条宽度

// 二分法空闲流畅滚动动画
export function smoothscroll(dom) {
  setTimeout(() => {
    const currentScrollT = dom.scrollTop; // 已经被卷掉的高度
    const currentScrollL = dom.scrollLeft;
    const clientHeight = dom.offsetHeight; // 容器高度
    const scrollHeight = dom.scrollHeight; // 内容总高度
    if (scrollHeight === 0) return;
    if (scrollHeight > currentScrollT + clientHeight) {
      window.requestAnimationFrame(() => {
        smoothscroll(dom);
      });
      dom.scrollTo(
        currentScrollL,
        currentScrollT + (scrollHeight - currentScrollT - clientHeight + SCROLLBARWIDTH) / 2
      );
    } else {
      dom.scrollTo(
        currentScrollL,
        scrollHeight + SCROLLBARWIDTH
      );
    }
  }, 0);
}
