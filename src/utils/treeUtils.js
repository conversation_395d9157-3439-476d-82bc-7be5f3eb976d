// 修改树形结构的键值
export const changeTreeFieldName = (objAry, fieldName, newFieldName) => {
  if (objAry != null) {
    objAry.forEach(item => {
      Object.assign(item, {
        [newFieldName]: item[fieldName]
      });
      delete item[fieldName];
      changeTreeFieldName(item.children, fieldName, newFieldName);
    });
  }
};

// 修改树数据结构的属性对应值
export const changeTreeFieldValue = (objAry, fieldName, fieldNameValue) => {
  if (objAry != null) {
    objAry.forEach(item => {
      delete item[fieldName];
      Object.assign(item, {
        [fieldName]: fieldNameValue
      });
      changeTreeFieldValue(item.children, fieldName, fieldNameValue);
    });
  }
};
