import { hasValue } from "@/utils/common.js";
import _uniqueId from "lodash/uniqueId";

function setQueryId(task) {
  task._currentQueryId = _uniqueId("QueryId");
  return task._currentQueryId;
}
function getQueryId(task) {
  return task._currentQueryId;
}

export function query(asyncTask, callback) {
  return function(...args) {
    const id = setQueryId(asyncTask);
    return asyncTask.call(this, ...args).then(res => {
      if (getQueryId(asyncTask) === id) {
        return callback.call(this, res);
      }
    });
  };
}

export function getFilter(tree, type, obj) {
  for (const item of tree) {
    if (item.hasAuth) {
      const operator = (item.operatorList || [])
        .filter(op => op.operatorAuth)
        .map(op => op.operatorCode);
      for (const key in obj) {
        if (operator.includes(type) || !type) {
          obj[key].push(item[key]);
        }
      }
    }
    if (item.children) {
      getFilter(item.children, type, obj);
    }
  }
  return obj;
}

/**
 * 根据给定字段的数组值过滤树
 * @param {*} tree 树
 * @param {*} prop 属性
 * @param {*} arr 数组值
 * @param {*} newTree 返回值
 * @returns newTree
 */
export function filterTree(tree, prop, arr) {
  const newTree = [];
  for (const item of tree) {
    const data = {};
    for (const key in item) {
      data[key] = hasValue(item[key]) ? item[key].v || item[key] : "";
    }
    if (item.children && item.children.length > 0) {
      data.children = filterTree(item.children, prop, arr);
    }
    if (!arr || arr.length === 0 || arr.includes(data[prop])) {
      newTree.push(data);
    }
  }
  return newTree;
}

/**
 * 处理穿梭框返现数据（生效期间）
 * @param {*} arr 数组
 */
export function enchoTransferData(arr) {
  if (!arr || arr.length === 0) return [];
  return arr.map(item => ({
    dimMemberId: item.dimMemberName,
    dimMemberName: item.dimMemberRealName,
    dimCode: "",
    key: `${item.dimMemberName}-self`,
    label: item.dimMemberRealName,
    memberType: 1,
    memberTypeValue: "self",
    objectId: `${item.dimMemberName}-self`,
    shardim: "",
    title: `${item.dimMemberRealName}`,
    type: "member",
    value: item.dimMemberName
  }));
}

/**
 * 处理穿梭框返现数据（生效公司）
 * @param {*} arr 数组
 */
export function companyTransferData(arr) {
  if (!arr || arr.length === 0) return [];
  return arr.map(item => ({
    dimMemberId: item.objectId,
    dimMemberName: item.dimMemberName,
    dimCode: "",
    key: `${item.objectId}-self`,
    label: item.dimMemberName,
    memberType: 1,
    memberTypeValue: "self",
    objectId: `${item.objectId}-self`,
    shardim: "",
    title: `${item.dimMemberName}`,
    type: "member",
    value: item.objectId
  }));
}

// 格式化树 ：左树右表组件
export function formatterData(data = []) {
  return data.map(item => {
    if (item.children) {
      item.children.forEach(child => {
        child.title = child.templateName;
        child.key = child.objectId;
        child.isLeaf = true;
        child.scopedSlots = {
          title: "custom"
        };
      });
    } else {
      item.children = [];
    }
    return {
      key: item.objectId,
      isLeaf: false,
      title: item.templateName,
      scopedSlots: {
        title: "custom"
      },
      ...item
    };
  });
}
