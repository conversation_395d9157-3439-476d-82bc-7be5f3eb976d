import _cloneDeep from "lodash/cloneDeep";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import { POLLING_INTERVAL } from "@/constant/common.js";
import commonService from "@/services/common";
import store from "../store/common";
import lang from "@/mixin/lang";
const { $t_process } = lang;
/**
 * @desc 根据url string转换为对象
 * @param {string} str
 */
export const queryString = str => {
  let strings = decodeURIComponent(str);
  const obj = {};
  const start = strings.indexOf("?");
  if (start !== -1) {
    strings = strings.substr(start + 1);
  }
  const arr = strings.split("&");
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i].split("=");
    if (item[0]) {
      obj[item[0]] = item[1];
    }
  }

  return obj;
};

/**
 * @param {Array} tree
 * @returns 树结构拍平之后的对象：key 为 item 的key，value为item
 */
export const paveTree = tree => {
  const treeMap = {};
  const loop = data => {
    data.forEach(item => {
      treeMap[item.key] = item;
      if (item.children && item.children.length) {
        loop(item.children);
      }
    });
  };
  loop(tree);
  return treeMap;
};

/**
 * @desc 数据安全性 保证获取到正确类型的数据
 * @param {object} data
 * @param {string} path 取值路径
 * @param {any} defaultValue 取不到值时的默认值
 */
export const getSafetyData = (data, path, defaultValue) => {
  if (data) {
    return (
      (!Array.isArray(path)
        ? path
          .replace(/\[/g, ".")
          .replace(/\]/g, "")
          .split(".")
        : path
      ).reduce((o, k) => (o || {})[k], data) || defaultValue
    );
  } else {
    return defaultValue;
  }
};

export const isObject = obj => {
  return Object.prototype.toString.call(obj) === "[object Object]";
};

export const getId = (length, custom = "") => {
  return (
    Number(
      Math.random()
        .toString()
        .substr(3, length) + Date.now()
    ).toString(36) + custom
  );
};

/**
 * @param {array} list 列表数据
 * @param {string} parentIdField 数据中parentId的字段名
 * @param {string} idField 数据中id的字段名
 * @param {string} newParentIdField  要生成新的parentId字段名，不传不生成
 * @param {string} newIdField 要生成新的id字段名，不传不生成
 */
export const listToTree = (
  list = [],
  parentIdField,
  idField,
  newParentIdField,
  newIdField
) => {
  const rootMap = {};
  const dataMap = {};
  list.forEach(item => {
    newIdField && (item[newIdField] = getId(10));
    dataMap[item[idField]] = item;
    if (!item[parentIdField]) {
      rootMap[item[idField]] = item;
    }
  });
  // 断层节点提升为根节点
  list.forEach(item => {
    if (!dataMap[item[parentIdField]]) {
      rootMap[item[idField]] = item;
    }
  });
  // 拼接tree
  list.forEach(item => {
    const parentItem = dataMap[item[parentIdField]];
    if (parentItem) {
      if (!parentItem.children) parentItem.children = [];
      newParentIdField && (item[newParentIdField] = parentItem[newIdField]);
      parentItem.children.push(item);
    }
  });

  return Object.keys(rootMap).map(key => rootMap[key]);
};

/**
 * @desc  广度
 * @param {*} tree
 * @param {*} cb
 */
export function bfTree(tree = [], cb) {
  let queue = [];
  for (let i = 0; i < tree.length; i++) {
    queue.push(tree[i]);
  }
  while (queue.length !== 0) {
    const item = queue.shift();
    let breakSign = false;
    cb && (breakSign = cb(item));
    if (breakSign) {
      queue = [];
      break;
    }
    if (item.children) {
      for (let i = 0; i < item.children.length; i++) {
        queue.push(item.children[i]);
      }
    }
  }
}

/**
 * @desc 对比两个数组之间的变化 返回新增和删除项
 * @param {array} arr1 原来的数组
 * @param {array} arr2 新数组
 * @param {string} idField
 */
export function arrayDiff(arr1 = [], arr2 = [], idField = "objectId") {
  const arr1Map = {};
  const deleteMap = {};
  arr1.forEach(item => {
    arr1Map[item[idField]] = item;
    deleteMap[item[idField]] = true;
  });
  const createList = [];
  const deleteList = [];
  arr2.forEach(item => {
    // 新增
    if (!arr1Map[item[idField]]) {
      createList.push(item);
      // 移除掉未改变的, 剩下的就是删除项
    } else {
      delete deleteMap[item[idField]];
    }
  });
  // 删除项
  Object.keys(deleteMap).forEach(key => {
    deleteList.push(arr1Map[key]);
  });

  return {
    create: createList,
    delete: deleteList
  };
}

/**
 * @desc 向父窗口发送message
 * @param {string} message loginOut表示登录失效、noticeList：控制台公告列表页、todoList控制台待办列表、todoDetail代办详情需传参（data：类型object，该条信息所有数据）
 * @param {string} messageText 提示信息（例：全局提示消息）
 * @param {string} messageType  success, error, warning，目前固定值success， 后期根据实际情况选择支持
 * @param {object} data  额外返回数据，暂时无用，后期根据实际情况选择支持
 */
export function postMessage2ECS2(message, messageText, messageType, data) {
  if (!message) return;
  if (!messageType) messageType = "success";
  const messageData = {
    source: "",
    actionType: message, // 'loginOut', // loginOut失效跳转登陆页
    messageText: messageText || "",
    messageType: messageType, // success, error, warning 根据实际情况选择支持
    data: data || null // 额外返回数据，暂时无用，根据实际情况选择支持
  };
  window.parent.postMessage(JSON.stringify(messageData), "*");
}
/**
 * @desc 子集管理将树结构格式化成前端tree组件需要的格式
 */
export function formatTreeData(data, dimSubsetType, subsetId) {
  const treeList = [];
  const loop = (list, parentList, newparentId) => {
    list.forEach(vlist => {
      let parentLists = {};
      parentLists = {
        ...vlist.data,
        depth: vlist.depth,
        title: vlist.data.dimMemberName,
        key: subsetId
          ? `${subsetId}-${vlist.data.objectId}`
          : vlist.data.objectId,
        newId: getId(10),
        newparentId: newparentId,
        dimSubsetType: dimSubsetType,
        shardim: vlist.data.dimMemberShared === "true",
        memberLength: list.length, // 每层的成员个数
        isLeaf: !(Array.isArray(vlist.subNodes) && vlist.subNodes.length > 0)
      };
      if (Array.isArray(vlist.subNodes) && vlist.subNodes.length > 0) {
        parentLists.children = [];
        loop(vlist.subNodes, parentLists, parentLists.newId);
      }
      parentList.children.push(parentLists);
    });
  };
  if (Array.isArray(data.subNodes)) {
    data.subNodes.forEach(item => {
      let parentList = {};
      parentList = {
        ...item.data,
        depth: item.depth,
        title: item.data.dimMemberName,
        key: subsetId
          ? `${subsetId}-${item.data.objectId}`
          : item.data.objectId,
        newId: getId(10),
        newparentId: null,
        dimSubsetType: dimSubsetType,
        shardim: item.dimMemberShared === "true",
        memberLength: data.subNodes.length, // 每层的成员个数
        isLeaf: !(Array.isArray(item.subNodes) && item.subNodes.length > 0)
      };
      if (Array.isArray(item.subNodes) && item.subNodes.length > 0) {
        parentList.children = [];
        loop(item.subNodes, parentList, parentList.newId);
      }
      treeList.push(parentList);
    });
  }
  return treeList;
}

/**
 * 获取地址栏参数, 如果是中文，加上encodeURI转码，兼容ie
 * @param {str} variable 参数名
 */
export function getUrlEncode(url) {
  var urlreset = url.split("?")[1];
  if (!urlreset) return url;
  var vars = urlreset.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    const han = /^[\u4e00-\u9fa5]+$/;
    if (han.test(pair[1])) {
      pair[1] = encodeURI(pair[1]);
    } else if (pair[1].includes("like")) {
      const params = pair[1].split("*");
      const newParams = params.map(it => {
        if (han.test(it)) {
          it = encodeURI(it);
        }
        return it;
      });
      pair[1] = newParams.join("*");
    }
    vars[i] = pair.join("=");
  }
  return `${url.split("?")[0]}?${vars.join("&")}`;
}

export function stopPropagation(event) {
  event = window.event || event;
  if (event.stopPropagation) {
    // 针对 Mozilla 和 Opera
    event.stopPropagation();
  } else {
    // 针对 IE
    event.cancelBubble = true;
  }
}

/**
 * @desc 深拷贝对象数组或对象内某个字段到指定字段
 * @param { Object | Array } object 待处理数据
 * @param {Array} source字段  source[index] -> target[index]
 * @param {Array} target字段
 */
export function setDataKey2key(data, key, copyTo) {
  if (isType(data, "Object")) {
    const temp = { ...data };
    key.map((item, index) => {
      if (copyTo[index] === "key") {
        temp[copyTo[index]] = temp[item] || AppUtils.generateUniqueId();
      } else {
        temp[copyTo[index]] = temp[item];
      }
    });
    temp.children = setDataKey2key(temp.children, key, copyTo);
    return temp;
  }
  if (isType(data, "Array")) {
    return data.map(item => {
      return setDataKey2key(item, key, copyTo);
    });
  }
  return data;
}

/**
 * @desc 获取数据类型
 * @param { Object } object 待判断数据
 * @return {String} 类型(类名称)
 */
export function getType(object) {
  return Object.prototype.toString.apply(object).match(/\s(\w*)/)[1];
}

/**
 * @desc 是否是某个特定类型
 * @param { Object } object 待判断数据
 * @param {string} type 类型
 * @return {Boolean}
 */
export function isType(object, type) {
  return Object.prototype.toString.apply(object).match(/\s(\w*)/)[1] === type;
}
/**
 * @desc 处理小数位
 * @param {value} 需要处理小数位的值 (Number 类型)
 * @param {num} 需要多少个小数位
 */

export function getDecimal(value, num) {
  const v =
    Object.prototype.toString.call(value) === "[object Number]"
      ? value
      : Number(value);
  return v.toFixed(num);
}

/**
 * DfsTraverse
 */

export function DfsTraverse(data, key, target, cb) {
  if (!Array.isArray(data)) return;
  let arr = [...data];
  while (arr.length) {
    const currentItem = arr.shift();
    if (currentItem[key].indexOf(target) !== -1) {
      cb && cb(currentItem);
    }
    if (currentItem.children && currentItem.children.length) {
      arr = [...currentItem.children, ...arr];
    }
  }
}
export const getMergedMenuStructure = (
  customMenuStructure,
  menuStructure,
  OPERATION,
  Logger
) => {
  const mergedMenuStructure = [...menuStructure];
  customMenuStructure.forEach(customMenu => {
    const { operation = OPERATION.INSERT_AFTER, target } =
      customMenu.custom || {};
    let mergedMenuIndex = -1;
    if (target) {
      mergedMenuIndex = mergedMenuStructure.findIndex(
        mergedMenu => mergedMenu.name === target
      );
    } else {
      mergedMenuIndex = mergedMenuStructure.length - 1;
    }
    switch (operation) {
      case OPERATION.DELETE:
        mergedMenuStructure.splice(mergedMenuIndex, 1);
        break;
      case OPERATION.INSERT_BEFORE:
        mergedMenuStructure.splice(mergedMenuIndex, 0, customMenu);
        break;
      case OPERATION.INSERT_AFTER:
        mergedMenuStructure.splice(mergedMenuIndex + 1, 0, customMenu);
        break;
      case OPERATION.REPLACE:
        mergedMenuStructure.splice(mergedMenuIndex, 1, customMenu);
        break;
      default:
        Logger.error(
          `operation ${operation} in custom menu structure is not supported`
        );
        break;
    }
    delete customMenu.custom;
  });
  return mergedMenuStructure;
};

/**
 * @desc 二进制文件下载
 * @param {blob} file 文件流
 */
export function downloadFile(res) {
  const contentDisposition = res.headers["content-disposition"];
  if (!contentDisposition) {
    res.data.text().then(res => {
      const { message } = JSON.parse(res);
      UiUtils.errorMessage(message);
    });
    return;
  }
  res.fileName = decodeURIComponent(
    res.headers["content-disposition"]
      .split(";")[2]
      .split("=")[1]
      .replace(/^\"|\"$/g, "")
  ).replace("+", " ");
  const { data, fileName } = res;
  if (window.navigator.msSaveBlob) {
    window.navigator.msSaveBlob(data, fileName);
  } else {
    const link = document.createElement("a");
    link.href = window.URL.createObjectURL(data);
    const body = document.querySelector("body");
    link.download = fileName;
    body.appendChild(link);
    link.click();
    body.removeChild(link);
    window.URL.revokeObjectURL(link.href);
  }
}

/**
 * 是否包含有意义的值，过滤前端非意义数据
 * @param {*} value value
 * @returns Boolean
 */
const emptys = new Set([null, undefined, NaN, "null", "NaN", "undefined", ""]);
export function hasValue(value) {
  return !(emptys.has(value) || /^\s*$/.test(String(value)));
}

/**
 * 浅检查对象是否是无值对象
 * @param {*} Object 待检查对象
 * @returns Boolean 是否是无值对象
 */

export function isDirty(obj) {
  return Object.values(obj).some(value => {
    if (getType(value) === "Object") {
      return Object.keys(value).length > 0;
    } else {
      return String(obj).length > 0;
    }
  });
}

/**
 * 文件格式校验
 * @param {name, type} 文件名， 文件类型正则
 * @return Boolean
 */

export function isFileType(name, type) {
  return type.test(name.substring(name.lastIndexOf(".") + 1).toLowerCase());
}

/**
 * TODO 防止请求堆积实现
 * 轮询接口通用封装
 * @param {*} promise promise 需要轮询的接口(当接口return结果为false类型值, 继续轮询；当return结果为true类型，停止轮询)
 * @param {*} time time 间隔轮询时间
 * @param  {...any} params params 接口参数
 * @returns res 轮询结果
 */
export function polling(promise, time, ...params) {
  let timer;
  const poller = new Promise((resolve, reject) => {
    timer = interval(() => {
      try {
        Promise.resolve(promise(...params)).then(res => {
          if (res) {
            resolve(res);
            unInterval(timer);
          }
        });
      } catch (e) {
        unInterval(timer);
        reject(e);
      }
    }, time);
  });
  poller.stop = () => {
    unInterval(timer);
  };
  return poller;
}

/**
 * 开启循环事件， setInterval 模拟实现
 * @param {*} func
 * @param {*} time
 * @returns
 */
export function interval(func, time) {
  let startTime = Date.now();
  const config = { stop: false };
  const check = () => {
    if (!config.stop) {
      if (Date.now() - startTime > time) {
        func();
        startTime = Date.now();
      }
      window.requestAnimationFrame(check);
    }
  };
  check();
  return config;
}

/**
 * 取消循环事件， clearInterval 模拟实现
 * @param {*} config
 */
export function unInterval(config) {
  config.stop = true;
}

/**
 * 深对比对象，watch 防止循环
 * @param {*} newest 旧对象/数组
 * @param {*} oldest 新对象/数组
 * @return Boolean 相同：true, 不相同：false
 */
export function deepCompare(newest, oldest) {
  // 同一个对象引用
  if (oldest === newest) return true;

  // 类型不同
  const ntype = getType(newest);
  const otype = getType(oldest);
  if (ntype !== otype) return false;

  // 字段数量不同
  const okeys = Object.keys(oldest);
  const nkeys = Object.keys(newest);
  if (okeys.length !== nkeys.length) return false;

  // 不同的对象引用，同字段数量
  for (const [key] of Object.entries(newest)) {
    if (!okeys.includes(key)) {
      return false;
    }

    const iold = oldest[key];
    const inew = newest[key];

    if (typeof iold === "object" && typeof inew === "object") {
      if (!deepCompare(iold, inew)) return false;
    } else {
      if (iold !== inew) return false;
    }
  }

  // 默认返回
  return true;
}

/**
 * 消息收集，用于收集多个异步操作，将这些异步操作合并为单次统一处理
 * @params wait 异步消息最长收集间隔时间,算一次收集完成
 * @return promise
 *
 * 基于类设计：防止多个collector event 混淆
 * use:
 *
 * const EventColletor = new EventCollect(wait);
 * EventColletor.collect(event).then(events => {
 *    // do something
 * })
 *
 * ep:
 *  async postingJournal(data) {
      const params = data ? [data.journalId] : this.selectedRowKeys;
      // 多个异步过账收集参数，一次性批量处理
      postCollector.collect(params).then(async paramsall => {
        const res = await journalService("postingJournal", paramsall.flat());
        this.setPostStatus(res);
      });
    },
 */

export function EventCollect(wait) {
  this.Event = [];
  this.timer = null;
  this.collect = function(event) {
    this.Event.push(event);
    // 缓存收集的数据
    const cache = [...this.Event];
    return new Promise((resolve, reject) => {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        // 当次收集数据和总数据不一致则说明当此promise过期，reject; 只返回收集完成后的状态
        if (cache.length !== this.Event.length) {
          reject();
        }
        resolve(this.Event);
        this.timer = null;
        this.Event = [];
      }, wait);
    });
  };
}

/**
 * desc：更改穿梭框表达式的字段成符合解析表达式接口的字段
 * @param {Object} exprObj 穿梭框获取到的表达式
 * @returns 返回符合解析接口的入参字段
 */
export function formatRequestParams(exprObj = {}) {
  const expr = _cloneDeep(exprObj);
  const dimMemberExps = {};
  if (Object.keys(expr).length) {
    Object.keys(expr).map(item => {
      let propsName = item;
      if (item === "memberType") {
        propsName = "member";
      }
      dimMemberExps[propsName] = expr[item];
    });
  }
  return JSON.stringify(dimMemberExps);
}
/*
 * 树节点过滤，不改变数据结构，不改变原数据
 * @param {*} tree 树对象数组
 * @param {*} key 树对象的属性字段名
 * @param {*} value 模糊匹配字符串
 * @return Array 新的树对象数组
 */
export function filterTree(tree, key, value, children = "children") {
  const arr = [];
  for (const item of tree) {
    const temp = _cloneDeep(item);
    if (temp[key].indexOf(value) > -1) {
      arr.push(temp);
      continue;
    }
    if (temp[children]) {
      temp[children] = filterTree(temp[children], key, value);
    }
    if (temp[children] && temp[children].length > 0) {
      arr.push(temp);
    }
  }
  return arr;
}

// 页面UI 交互相关 start-----------------------------------

/**
 *
 * @param {*} e 输入框的事件源对象
 * @param {*} record 对象，表格行对象或某页面绑定对象
 * @param {*} prop 属性名称
 * 使用场景：常见使用场景，某属性字段限制必须输入数值（如：100，100.111 等）
 * 参考模考：汇率管理
 * 使用方式：yn-input 组件内绑定相关事件
 *          @focus= dealNumberFocusChange
 *          @change=dealNumberFocusChange
 *          @blur=dealNumberBlurInput
 */
// 日记账往来抵销要求可以输入负数
const typeList = ["journal", "currentOffset"];
export function dealNumberFocusChange(e, record, prop, _self, type) {
  const { value } = e ? e.target : { value: record[prop] };
  let exp1 = /^[\+]?\d*?\.?\d*?$/;
  const exp2 = /^(?:[\+]?\d+(?:\.\d+)?)?$/;
  if (typeList.includes(type)) {
    exp1 = /^[\+\-]?\d*?\.?\d*?$/;
  }
  if (!value) return;
  if (!value.match(exp1)) {
    _self.$set(record, prop, record[`t_${prop}`]);
  } else {
    _self.$set(record, `t_${prop}`, record[prop]);
  }
  if (value.match(exp2)) {
    _self.$set(record, `o_${prop}`, record[prop]);
  }
}

export function dealNumberBlurInput(e, record, prop, _self, type) {
  const { value } = e ? e.target : { value: record[prop] };
  let exp1 = /^(?:[\+]?\d+(?:\.\d+)?|\.\d*?)?$/;
  if (typeList.includes(type)) {
    exp1 = /^(?:[\+\-]?\d+(?:\.\d+)?|\.\d*?)?$/;
  }
  if (!value) return;
  if (!value.match(exp1)) {
    _self.$set(record, prop, record[`t_${prop}`]);
  } else {
    if (value.match(/^\.\d+$/)) _self.$set(record, prop, 0 + record[prop]);
    if (value.match(/^\.$/)) _self.$set(record, prop, 0);
    _self.$set(record, `o_${prop}`, record[prop]);
  }
}

// 页面UI 交互相关 end  -----------------------------------

/**
 * 深赋值：target值优先策略(当target.**.key 有普通值，保持，否则覆盖)
 * @param {*} target
 * @param {*} source
 */
export function mergeT(target, source, method) {
  for (const key in source) {
    if (isType(target[key], "Object")) {
      mergeT(target[key], source[key]);
    } else if (isType(target[key], "Array")) {
      continue;
    } else {
      target[key] = hasValue(target[key]) ? target[key] : source[key];
    }
  }
}
/**
 * 深赋值：source值优先策略(无论何种情况，source.**.key值覆盖target.**.key)
 * @param {*} target
 * @param {*} source
 */
export function mergeS(target, source) {
  for (const key in source) {
    if (typeof source[key] === "object") {
      mergeS(target[key], source[key]);
    } else {
      target[key] = source[key];
    }
  }
}

/**
 *  后端返回的该字段 可能是 false “false” "FALSE"
 *  这三种都 返回 false
 * @param {Boolean, String} val
 * @returns val
 */
export function getBooleanValue(val) {
  if (val === "FALSE" || val === "false") return false;
  return val;
}

const TASK_STATUS_POLLING = ["NOT_STARTED", "RUNNING"]; // 需要轮询的状态
const TASK_STATUS_END = ["FINISHED", "ABNORMAL_TERMINATION"]; // 结束的状态
// 异步任务轮询任务状态
export function pollTaskStatus(taskId, cb, failCb) {
  const loop = async timer => {
    setTimeout(async () => {
      await commonService("queryMergeTaskStatus", taskId).then(res => {
        const { data } = res.data;
        if (TASK_STATUS_POLLING.indexOf(data) !== -1) {
          // 继续轮询
          loop(timer);
        } else if (data === TASK_STATUS_END[0]) {
          // 成功 下一步
          cb && cb();
        } else {
          // 保存失败
          failCb && failCb();
        }
      });
    }, timer);
  };
  loop(POLLING_INTERVAL);
}

/**
 * 获取请求路径中的第一个path的值
 * @param {*} url 请求路径url
 * @returns string 第一个path的值
 */
export function getFirstPathNameFromUrl(url = "") {
  if (url.match(/https?:\/\//g)) {
    url = url.split(/https?:\/\//g)[1];
    url = url.split("/")[1];
  } else {
    if (url.indexOf("/") === 0) {
      url = url.split("/")[1];
    } else {
      url = url.split("/")[0];
    }
  }
  return url;
}

/**
 * 职责链模式
 * @param {*} fn function next action
 * @returns run return fn value
 */

export class Chain {
  after = null;
  fn = null;
  constructor(fn) {
    this.fn = fn;
  }
  next(next) {
    this.after = new Chain(next);
    return this.after;
  }
  run(...arg) {
    const result = this.fn.apply(this, arg);
    if (result === "next") {
      return this.after && this.after.run.apply(this.after, arg);
    }
    return result;
  }
}
export function getServiceUrl(baseUrl) {
  let url =
    baseUrl && baseUrl.indexOf("//") < 0 && baseUrl.indexOf("http") < 0
      ? "".concat(window.location.origin).concat(baseUrl)
      : baseUrl.indexOf("//") === 0
        ? "".concat(window.location.protocol).concat(baseUrl)
        : baseUrl;
  // 移除所有常见的服务名
  url = url.replace(/\/(consolidation|console)$/g, "");
  return url;
}

/**
 * 新建多语言数组
 * @param {Array} languages 支持的多语言类型数组
 * @param {*} code 当前语言code
 * @param {*} value 当前语言值
 */
export function genLanguages(value, lang) {
  const languages = store.state.languages;
  const code = store.state.lang;
  const list = lang && lang.length > 0 ? [...lang] : [...languages];
  const current = list.find(item => item.languageCode === code);
  if (current) {
    current.text = value;
  } else {
    list.push({
      languageCode: code,
      text: value
    });
  }
  const langs = list.map(item => {
    return {
      languageCode: item.languageCode || item.code,
      text: item.text || ""
    };
  });
  return langs;
}

/**
 * 同步多语言数组
 * @param {*} value 当前语言值
 * @param {Array} lang 支持的多语言类型数组
 * @returns
 */
export function syncLanguages(value, lang) {
  const code = store.state.lang;
  const langData =
    lang && lang.length
      ? lang
      : [
        {
          languageCode: code,
          text: value
        }
      ];
  return langData.map(item => {
    if (item.languageCode === code) {
      item.text = value;
    }
    if (item.languageCode === "zh_CN" && item.text === "") {
      item.text = value;
    }
    return item;
  });
}

export function copyLanguages(lang, index) {
  return lang.map(item => {
    if (item.text) {
      item.text = `${$t_process("duplicate_copy", [item.text])}${index}`;
    }
    return item;
  });
}
