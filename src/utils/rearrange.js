/**
 * @description 给数据列表添加 key、label
 * @param {Array} data
 */
function addKeyToData(data) {
  const loop = list => {
    list.forEach(item => {
      item.key = item.id;
      item.label = item.name;
      if (item.children && item.children.length) {
        loop(item.children);
      }
    });
  };
  loop(data);
}

/**
 *  根据 id 查找 item
 * @param {Array} data
 * @param {string} id
 * @returns 返回 data 中 item
 */
function getNameFromId(data, id) {
  if (!id) return "";
  let list = [...data];
  while (list.length) {
    const currentItem = list.shift();
    if (currentItem.key === id) {
      return currentItem;
    }
    if (currentItem.children && currentItem.children.length) {
      list = [...list, ...currentItem.children];
    }
  }
}

/**
 *
 * @param {Array} tree
 * @returns 拍平之后的对象：key 为 item 的key，value为item
 */
function paveTree(tree) {
  const treeMap = {};
  const loop = data => {
    data.forEach(item => {
      treeMap[item.key] = item;
      if (item.children && item.children.length) {
        loop(item.children);
      }
    });
  };
  loop(tree);
  return treeMap;
}

/**
 *
 * @param {Array} arr 需要过滤的数组
 * @param {*} id 根据 id 来进行过滤
 * @returns 去重之后的数组
 */
function uniqueItem(arr, id) {
  const map = {};
  return arr.filter(item => {
    if (!map[item[id]]) {
      map[item[id]] = 1;
      return true;
    }
    return false;
  });
}

export { addKeyToData, getNameFromId, paveTree, uniqueItem };
