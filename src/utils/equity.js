/**
 * @desc addField 添加字段 key 等
 * @param {data} 数据
 */
export function addKeyToData(data) {
  if (!data || !data.length) return;
  let arr = [...data];
  while (arr.length) {
    const currentItem = arr.shift();
    currentItem.key = currentItem.id;
    currentItem.label = `${currentItem.memberCode} ${currentItem.name}`;
    currentItem.memberName = currentItem.name;
    currentItem.memberCodeAndName = `${currentItem.memberCode} ${currentItem.name}`;
    if (currentItem.children && currentItem.children.length > 0) {
      arr = arr.concat(currentItem.children);
    }
  }
  return arr;
}

/**
 * @desc 根据 id 获取 name
 * @param {data} 数据
 * @param {id} id
 */
export function findName(data, id) {
  if (!data || !data.length) return;
  let arr = [...data];
  while (arr.length) {
    const currentItem = arr.shift();
    if (currentItem.id === id) {
      const Obj = {
        code: currentItem.memberCode,
        name: currentItem.name,
        codeAndName: `${currentItem.memberCode} ${currentItem.name}`,
        id: currentItem.id
      };
      return Obj;
    }
    if (currentItem.children && currentItem.children.length > 0) {
      arr = arr.concat(currentItem.children);
    }
  }
  return null;
}

/**
 * 处理表格数据（grid）
 * @param {Array} data
 * @param {Number} offset
 */
export function handleTableData(data, offset) {
  return data.map((item, index) => {
    item["iCPCodeAndName"] = `${item.iCPCode} ${item.iCPName}`;
    item["entityCodeAndName"] = `${item.entityCode} ${item.entityName}`;
    const rowKey = item.objectId;
    const res = {};
    res.serialNumber = {
      v: index + offset + 1,
      objectId: rowKey,
      rowKey,
      level: 1
    };
    res.key = item.objectId;
    res.objectId = item.objectId;
    Object.keys(item).forEach(keyName => {
      if (keyName !== "objectId") {
        res[keyName] = {
          v: item[keyName],
          oldV: item[keyName],
          objectId: rowKey,
          customValidate: true, // 业务方，单元格校验
          rowKey
        };
      }
    });
    return res;
  });
}

/**
 * 数组根据 某个属性去重
 * @param {Array} arr
 * @param {String} key
 */
export function uniqueByAtrr(arr, key) {
  const map = new Map();
  return arr.filter(item => !map.has(item[key]) && map.set(item[key], true));
}
