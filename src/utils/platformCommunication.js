import UrlUtils from "yn-p1/libs/utils/UrlUtils";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { APPS } from "@/config/SETUP";
import Logger from "yn-p1/libs/modules/log/logger";
const MESSAGE_TYPE = {
  activation: "iframe_message_switch_tab_notify",
  updateTab: "iframe_message_updateTab",
  exitTab: "iframe_message_save_tab_exit"
};
/**
 * @description 平台通讯
 *  1.打开页签
 *  2.关闭页签需要提示保存
 *  3.关闭页签
 *  4.获取参数
 *
 *  wiki:
 *  `https://www.tapd.cn/42097586/markdown_wikis/show/#1142097586001013894`.
 *
 *  `https://www.tapd.cn/65863259/markdown_wikis/show/#1165863259001014199`
 *
 * */
export default class PlatformCommunication {
  static init(selfTab) {
    this.selfTab = !!selfTab;
  }
  /**
   * @description 打开页签
   * @param{Object} {title:string,url:string,tabIdent:string}
   *
   */

  static openTab(tabInfo) {
    const { title, id, params = {} } = tabInfo;
    let { uri } = tabInfo;
    let finalUrl = uri;
    // 添加业务唯一标识，用于
    // 判断 uri 是否一个完整链接 比如dm模块： http://**************:7070/C1-DM
    // 否则 需要拼接前缀
    if (uri.indexOf("http://") === -1 && uri.indexOf("https://") === -1) {
      // uri 需要将 consolidation 去掉， 因为 baseUrl 会携带。且 表单 也是走的本地路由。
      // 所以不用考虑表单前缀
      uri = uri.replace("consolidation/#", "");
      const urlPrefixIndex = location.href.indexOf("#") + 1;
      const baseUrl = location.href.slice(0, urlPrefixIndex);
      const serviceName = UrlUtils.getQuery("serviceName");
      const menuId = UrlUtils.getQuery("menuId");
      const appId = UrlUtils.getQuery("appId");
      if (uri.indexOf("?") === -1) {
        finalUrl = `${uri}?serviceName=${serviceName}&menuId=${menuId}&appId=${appId}`;
      } else {
        finalUrl = `${uri}&serviceName=${serviceName}&menuId=${menuId}&appId=${appId}`;
      }
      // 根据此参数判断是否需要添加 baseUrl，默认需要
      if (!params.isAbsolutePath) {
        finalUrl = `${baseUrl}${finalUrl}`;
      }
    }
    const data = {
      type: "iframe_message_open",
      params: {
        title,
        url: finalUrl,
        key: id
      }
    };
    window.top.postMessage(JSON.stringify(data), "*");
  }
  /**
   * @description 关闭页签需要给出提示
   * @param{Boolean}type 平台是否自己关闭页签
   * */
  static savePrompt(selfSaveCheck) {
    Logger.info(selfSaveCheck);
    const data = {
      type: "iframe_message_updateTab",
      tabKey: UrlUtils.getQuery("inTab"), // 平台打开iframe url中inTab或tabKey参数
      params: {
        selfSaveCheck: !!selfSaveCheck
      }
    };
    window.top.postMessage(JSON.stringify(data), "*");
  }
  /**
   * @description 获取查询参数 根据url inTab  当url 传参满足不了需求时，考虑将参数放入session
   *
   * */
  static getQueryParam() {
    const inTab = UrlUtils.getQuery("inTab");

    const mrOpenJournal = (
      UrlUtils.getQuery("mrOpenJournal") + ""
    ).toLowerCase();
    if (mrOpenJournal === "true") {
      const key = Object.keys(sessionStorage).find(key => key.includes(inTab));
      return JSON.parse(sessionStorage.getItem(key));
    }

    return DsUtils.getSessionStorageItem(inTab, {
      storagePrefix: APPS.NAME,
      isJson: true
    });
  }
  /**
   * @description 设置查询参数 将参数放入session，tabKey是唯一标识
   * 当url 传参满足不了需求时，考虑将参数放入session
   * tabKey 与openTab 入参tabKey 保持一致
   *
   * */
  static setQuertParam(tabKey, data) {
    DsUtils.setSessionStorageItem(tabKey, data || "zh_CN", {
      storagePrefix: APPS.NAME,
      isJson: true
    });
  }
  static closeTab(tabKey) {
    window.top.postMessage(
      JSON.stringify({
        type: "iframe_message_save_tab_saved",
        tabKey: tabKey || UrlUtils.getQuery("inTab")
      }),
      "*"
    );
  }
  static cancelSaveTab(tabKey) {
    window.top.postMessage(
      JSON.stringify({
        type: "iframe_message_save_tab_cancel",
        tabKey: tabKey || UrlUtils.getQuery("inTab")
      }),
      "*"
    );
  }
  static exitTab(tabKey) {
    window.top.postMessage(
      JSON.stringify({
        type: "iframe_message_save_tab_exit",
        tabKey: tabKey || UrlUtils.getQuery("inTab")
      }),
      "*"
    );
  }
  /**
   * @description 激活二级菜单，刷新页面操作，在组件created方法里面调用
   * @param{Funcction} cb 激活以后的回调函数
   * */
  static activeTabCb(cb) {
    if (this.selfTab) return;
    window.addEventListener(
      "message",
      event => {
        const { data } = event;
        const messageObj = data ? JSON.parse(data) : {};
        const { type, tabKey } = messageObj;
        const currIframeTabKey = UrlUtils.getQuery("inTab");
        if (type === MESSAGE_TYPE.activation && tabKey === currIframeTabKey) {
          cb && cb();
        }
      },
      false
    );
  }

  static updateTab(obj) {
    const { title, ...param } = obj;
    const data = {
      type: MESSAGE_TYPE.updateTab,
      tabKey: UrlUtils.getQuery("inTab"), // 平台打开iframe url中inTab或tabKey参数
      params: {
        title,
        ...param
      }
    };
    window.top.postMessage(JSON.stringify(data), "*");
  }
}
