import lang from "@/mixin/lang";
const { $t_structures } = lang;
export const JOURNAL_LIST_PATH = "/journal/list";
export const POLLING_INTERVAL = 5000; // 轮询间隔时长

export const TRANSFER_TIPS_WORD = {
  // 穿梭框提示文案
  shareMember: () => $t_structures("current_feature_does_not_support"),
  restrictedCompany: () => $t_structures("only_internal_company_reconciliation")
};
export const CARD_LAYOUT_BREAK_POINT = {
  M: 600,
  L: 600,
  XL: 850,
  XXL: 1500,
  XXXL: 2400
};
export const CARD_LAYOUT_LAYOUT = {
  M: {
    col: 2,
    gutter: ["3rem", 0]
  },
  L: {
    col: 2,
    gutter: ["3rem", 0]
  },
  XL: {
    col: 3,
    gutter: ["3rem", 0]
  },
  XXL: {
    col: 4,
    gutter: ["3rem", 0]
  },
  XXXL: {
    col: 5,
    gutter: ["3rem", 0]
  }
};
