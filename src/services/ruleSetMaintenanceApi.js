import { BACKEND } from "../config/SETUP";
const baseUrl = BACKEND.BASE_URL;
const ruleSetMaintenanceApi = {
  ruleSetMaintenanceSave: `${baseUrl}/ruleSet/save`,
  getRuleSetDetail: `${baseUrl}/ruleSet/query`,
  searchTableData: `${baseUrl}/ruleSet/search`,
  getTableData: `${baseUrl}/ruleSet/list`,
  deleteRuleSet: `${baseUrl}/ruleSet/delete`,
  editRuleSet: `${baseUrl}/ruleSet/edit`
};
export default ruleSetMaintenanceApi;
