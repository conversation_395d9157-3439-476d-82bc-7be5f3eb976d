import { BACKEND } from "../../config/SETUP";
const ecsBaseUrl = BACKEND.ECS_CONSOLE_URL;
const mrBaseUrl = BACKEND.MR_BASE_URL;
const ecsUrl = BACKEND.ECS_CONSOLE_BASE_URL;

const taskFlowTempApi = {
  getTotalForMenu: `${ecsBaseUrl}/task/getTotalForMenu`, // 获取,
  getSceneList: `${mrBaseUrl}/sceneElement/select`, // 获取,
  getTaskList: `${ecsUrl}/console/client/task/getTaskByCondition`, // 获取todolist,
  getNoticeList: `${ecsBaseUrl}/notice/getNoticeListForPC`, // 获取公告,
  getIconByLoginName: `${ecsBaseUrl}/user/getIconByLoginName`, // 获取头像名称
  getSceneViewData: `${mrBaseUrl}/scene/visitRanking`, // 获取最经浏览,
  downloadAttachment: `${ecsBaseUrl}/notice/downloadAttachment`
};
export default taskFlowTempApi;
