import { fetch } from "../baseService";
import api from "./api";
import { getSafetyData } from "@/utils/common";
/**
 * @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      const data = getSafetyData(res.data, "items", []);
      if (res.data) {
        res.data.items = data;
      } else {
        res.data = [];
      }
    }
    return res;
  },
  post: res => res
};

const todoServiceConfig = {
  getTotalForMenu: function(params) {
    params.serviceName = "console";
    return {
      method: "post",
      url: `${api.getTotalForMenu}`,
      params: params
    };
  },
  getTaskList: function(params) {
    params.serviceName = "console";
    return {
      url: `${api.getTaskList}`,
      params: params,
      method: "post"
    };
  },
  getSceneList: function(params) {
    return {
      method: "get",
      url: `${api.getSceneList}`
    };
  },
  getNoticeList: function(params) {
    params.serviceName = "console";
    return {
      method: "post",
      url: `${api.getNoticeList}`,
      params: params
    };
  },
  getIconByLoginName: function(params) {
    params.serviceName = "console";
    return {
      method: "post",
      url: `${api.getIconByLoginName}?loginName=${params.loginName}`,
      params: params
    };
  },
  getSceneViewData: function(params) {
    const query = params ? `?limit=${params}` : "";
    return {
      method: "get",
      url: `${api.getSceneViewData}${query}`
    };
  }
};

/**
 * @desc 重分类相关请求处理
 * @param {string} method
 * @param {object} params
 */
const todoService = (type, params) => {
  const config = todoServiceConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default todoService;
