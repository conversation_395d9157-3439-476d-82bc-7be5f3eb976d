import { fetch } from "../baseService";
import api from "../api";
import { getSafetyData } from "@/utils/common";
/**
 * @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      const data = getSafetyData(res.data, "items", []);
      if (res.data) {
        res.data.items = data;
      } else {
        res.data = [];
      }
    }
    return res;
  },
  post: res => res
};
const typeMapConfig = {
  getDimMember(params) {
    const { dimId, dimMemberParentId } = params;
    return {
      method: "get",
      iscode: false,
      url: `${api.getDimMember}?query=dimId='${dimId}'and dimMemberParentId='${dimMemberParentId}'&limit=1000&offset=0&orderBy=dimMemberPos&totalResults=true`
    };
  },
  // 对账穿梭框 成员权限控制接口
  // dimMembersWithAuth(params) {
  //   const { dimId, dimMemberParentId } = params;
  //   return {
  //     method: "get",
  //     url: `${api.dimMembersWithAuth}?dimId=${dimId}&dimMemberParentId=${dimMemberParentId}&dimMemberStatus=enable`
  //   };
  // },
  dimMembersWithAuth(reqParams) {
    const { apiName, parentId, ...others } = reqParams;
    const params = {
      authData: "Y", // 开启数据权限控制
      parentId, // 父节点id，查询根节点成员参数为空
      _isAsync: true, // /异步
      ...others
    };
    return {
      method: "post",
      url: `${api.dimMembersWithAuth}/${apiName}/selectDisplayData`,
      params
    };
  },
  // 对账 组织 权限 搜索
  dimMembersWithAuthSearch(reqParams) {
    const { apiName, dimMemberType, searchValue, ...others } = reqParams;
    const params = {
      authData: "Y", // 开启数据权限控制
      query: `${dimMemberType} like '*${searchValue}*'`, // 查询条件
      searchReturnParent: false, // 查询是否返回祖先节点
      ...others
    };
    return {
      method: "post",
      url: `${api.dimMembersWithAuth}/${apiName}/selectDisplayData`,
      params
    };
  },
  getDimMemberByMemberId(params) {
    const { dimId, dimMemberParentId } = params;
    return {
      method: "get",
      iscode: false,
      url: `${api.getDimMember}?query=dimId='${dimId}'and dimMemberParentId='${dimMemberParentId}'and dimMemberStatus='enable'&limit=1000&offset=0&orderBy=dimMemberPos`
    };
  },
  // 针对审计线索 穿梭框
  queryDimMembersByCondition(params) {
    const { dimId, dimMemberParentId } = params;
    return {
      method: "get",
      iscode: false,
      url: `${api.queryDimMembersByCondition}?query=dimId='${dimId}'and dimMemberParentId='${dimMemberParentId}'and dimMemberStatus='enable'&limit=1000&offset=0`
    };
  },

  // 根据DimCode获取成员
  getMemberByDimCode(dimCode) {
    return {
      method: "get",
      url: `${api.getMemberByDimCode}/${dimCode}`
    };
  },

  getLevels(params) {
    const { dimId } = params;
    return {
      method: "get",
      url: `${api.getLevels}/${dimId}`
    };
  },
  getDimAttrs(params) {
    const { dimId } = params;
    return {
      method: "get",
      iscode: false,
      url: `${api.getDimAttrs}/?query=dimId='${dimId}'`
    };
  },
  getDimAttrsById(params) {
    const { attrId } = params;
    return {
      method: "get",
      url: `${api.getDimAttrsById}/${attrId}`
    };
  },
  getVariables(params) {
    const { dimId } = params;
    return {
      method: "get",
      url: `${api.getVariables}?dimId=${dimId}`
    };
  },
  getDimSubsets: function(params) {
    // 获取维度下面的子集
    const query = encodeURIComponent(
      `dimId='${params.dimId}' and dimSubsetName like '*${params.searchValue}*'`
    );
    return {
      method: "get",
      url: `${api.getDimSubsets}?query=${query}&orderBy=subsetPos:asc&limit=1000&offset=0`
    };
  },
  searchDimMembers(params) {
    const { dimId, word } = params;
    return {
      method: "get",
      iscode: false,
      url: `${api.getDimMember}?query=dimId='${dimId}'and dimMemberName like'*${word}*'`
    };
  },
  getDimInfoById(dimId) {
    return {
      method: "get",
      url: `${api.getDimInfoById}${dimId}`
    };
  },
  dimAttrTypes() {
    return {
      method: "get",
      url: `${api.dimAttrTypes}`
    };
  },
  transferSearchMember(params) {
    const url = `${api.getDimMember}/?query=${
      params.dimMemberType ? params.dimMemberType : "dimMemberName"
    } like'*${params.searchValue}*' and dimId='${
      params.dimId
    }'and dimMemberStatus='enable'&orderBy=dimMemberTotalPos`;
    return {
      iscode: false,
      method: "get",
      url: url
    };
  },
  getExpMember(params) {
    return {
      method: "post",
      url: `${api.getExpMember}`,
      params
    };
  },
  parseICPExp(params) {
    return {
      method: "post",
      url: `${api.parseICPExp}`,
      params
    };
  },
  // 解析 类别 维度表达式成员
  parseCateGoryExp(params) {
    return {
      method: "post",
      url: `${api.parseCateGoryExp}`,
      params
    };
  },
  getAudittraiMembers(params) {
    return {
      method: "post",
      url: `${api.getAudittraiMembers}`,
      params
    };
  },
  getExpMemberWithCode(params) {
    return {
      method: "post",
      url: `${api.getExpMemberWithCode}`,
      params
    };
  },
  // ICP树型下拉列表
  getCompanyTree(params) {
    const url = params
      ? `${api.getCompanyTree}?searchWord=${encodeURIComponent(params)}`
      : `${api.getCompanyTree}`;
    return {
      method: "get",
      url
    };
  },
  // Entity树型下拉列表
  getEntityTree(params) {
    const url = params
      ? `${api.getEntityTree}?searchWord=${encodeURIComponent(params)}`
      : `${api.getEntityTree}`;
    return {
      method: "get",
      url
    };
  },
  // 查看具体持股信息
  getEquityManagements(params) {
    const { version, year, period, id } = params;
    return {
      method: "get",
      url: `${api.getEquityManagements}/${version}/${year}/${period}/${id}`
    };
  },
  // 获取多语言
  getEnableLanguages: function(params) {
    return {
      method: "post",
      url: `${api.getEnableLanguages}`
    };
  },
  getSystemMenuList(params) {
    return {
      method: "post",
      url: `${api.getSystemMenuList}`,
      params: {
        access: true,
        appId: params.appId,
        serviceName: "console",
        loginName: params.userLoginName
      }
    };
  },
  getCurrentUser() {
    return {
      method: "get",
      url: `${api.getCurrentUser}`
    };
  },
  getSubsetById(dimId) {
    return {
      method: "get",
      iscode: false,
      url: `${api.getSubsetById}?query=dimId='${dimId}'&orderBy=subsetPos:asc`
    };
  },
  // 根据 维度id 查询 apiName
  queryApiName(dimId) {
    return {
      method: "get",
      url: `${api.queryApiName}/${dimId}`
    };
  },
  // 修改小数点位数 pageName的值有三种：equityManagement(股权管理页面)、currentOffset（往来抵消页面）、rateManagement（汇率管理页面）
  selectUserScale(pageName) {
    return {
      method: "get",
      url: `${api.selectUserScale}/${pageName}`
    };
  },
  // 查询设置的小数点位数
  saveOrUpdateUserScale(params) {
    return {
      method: "post",
      url: `${api.saveOrUpdateUserScale}`,
      params
    };
  },
  queryMergeTaskStatus(taskId) {
    return {
      method: "get",
      url: `${api.queryMergeTaskStatus}/${taskId}`
    };
  },
  // 根据dimCode 查询一层成员 分页
  getFirstDimMemberList(params) {
    return {
      method: "post",
      url: `${api.getFirstDimMemberList}`,
      params
    };
  },
  // 根据dimMemberId 获取子成员数据 分页
  getCommonDimMemberList(params) {
    return {
      method: "post",
      url: `${api.getCommonDimMemberList}`,
      params
    };
  },
  // 根据入参 keyCode（模糊查询） 或者dimMemberId 查询成员 信息 分页
  getQueryDimMemberList(params) {
    return {
      method: "post",
      url: `${api.getQueryDimMemberList}`,
      params
    };
  },
  // 查询 第一个叶子成员信息
  getFirstDimMember(params) {
    const { dimCode, needPermission = false, pageName } = params;
    return {
      method: "get",
      url: `${api.getFirstDimMember}?dimCode=${dimCode}&needPermission=${needPermission}&pageName=${pageName}`,
      params
    };
  },
  // 获取唯一标识
  getLastSettingV(params) {
    const { tag, key } = params;
    return {
      method: "get",
      url: `${api.getLastSettingV}?tag=${tag}&key=${key}`
    };
  },
  // 设置唯一标识：
  saveOrUpdateUserSetting(params) {
    return {
      method: "post",
      url: `${api.saveOrUpdateUserSetting}`,
      params
    };
  },
  // 获取权限集信息
  getMetadataRecords: function(apiName) {
    return {
      method: "get",
      iscode: false,
      url: `${api.getMetadataRecords}?query=apiName=${apiName}`
    };
  },
  filterParamMap(params) {
    return {
      method: "post",
      url: `${api.filterParamMap}`,
      params
    };
  },
  getDimInfoBycodeAndName(params) {
    const { dimCode, memberName } = params;
    return {
      method: "get",
      url: `${
        api.getMemberInfo
      }?dimCode=${dimCode}&memberName=${encodeURIComponent(memberName)}`
    };
  },
  transPageDim(params) {
    return {
      method: "post",
      url: `${api.transPageDim}`,
      params
    };
  },
  // 根据条件获取用户
  loadUserByCondition: function(params) {
    return {
      method: "post",
      url: `${api.loadUserByCondition}`,
      params
    };
  },
  // 获取角色
  getRoleList: function(params) {
    return {
      method: "post",
      url: `${api.getRoleList}`,
      params
    };
  },
  // 获取群组
  getGroupList: function(params) {
    return {
      method: "post",
      url: `${api.getGroupList}`,
      params
    };
  },
  // 获取岗位
  dimPost: function(params) {
    return {
      method: "post",
      url: `${api.dimPost}`,
      params
    };
  },
  // 组织机构
  dimOrg: function(params) {
    return {
      method: "post",
      url: `${api.dimOrg}`,
      params
    };
  },
  msgTemplates: function(params) {
    return {
      method: "get",
      url: `${api.msgTemplates}?orderBy=createDate:desc&offset=0&limit=100&showName=createBy,channelId&query=enabled=true`
    };
  },
  // 平台文件批量下载
  batchDownloadFile: function(params) {
    return {
      method: "post",
      responseType: "blob",
      url: `${api.batchDownloadFile}`,
      params
    };
  },
  // 平台文件能否预览
  canPreview: function(params) {
    return {
      method: "post",
      url: `${api.canPreview}`,
      params
    };
  },
  // 平台文件上传
  uploadAttachment: function(params) {
    return {
      method: "post",
      url: `${api.uploadAttachment}`,
      params
    };
  },
  downloadFile: function(fileId) {
    return {
      method: "post",
      responseType: "blob",
      url: `${api.downloadFile}?fileId=${fileId}`
    };
  },
  selectByObjectIds(params) {
    return {
      method: "post",
      url: `${api.selectByObjectIds}`,
      params
    };
  }
};

/**
 * @desc 组织架构相关请求处理
 * @param {string} method
 * @param {object} params
 */
const commonService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default commonService;
