import { fetch } from "../baseService";
import api from "../api";
import { getSafetyData } from "@/utils/common";
/**
 * @desc   @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      const data = getSafetyData(res.data, "items", []);
      if (res.data) {
        res.data.items = data;
      } else {
        res.data = [];
      }
    }
    return res;
  },
  post: res => res
};

const typeMapConfig = {
  // 查询当前用户pov值
  selectUserPov: function() {
    return {
      method: "get",
      url: `${api.selectUserPov}`
    };
  },

  // 新增或修改用户pov
  saveOrUpdateUserPov: function(params) {
    return {
      method: "post",
      url: `${api.saveOrUpdateUserPov}`,
      params
    };
  }
};

/**
 * @desc Pov功能接口
 * @param {string} method
 * @param {object} params
 */
const povService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default povService;
