import { fetch } from "../baseService";
import api from "../api";
import { getSafetyData } from "@/utils/common";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
/**
 * @desc   @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      const data = getSafetyData(res.data, "items", []);
      if (res.data) {
        res.data.items = data;
      } else {
        res.data = [];
      }
    }
    return res;
  },
  post: res => res
};

const typeMapConfig = {
  // 汇率管理权限
  getExchangerateAuth: function(refId) {
    const resourceType = "MetadataObject";
    const bizAppId = UrlUtils.getQuery("appId");
    return {
      method: "get",
      iscode: false,
      url: `${api.getEquityAuth}?query=refId='${refId}' and resourceType='${resourceType}' and privilegeCode in ('rate-read','rate-edit') and bizAppId='${bizAppId}'`
    };
  },
  // 查询汇率管理列表
  getRateList: function(params) {
    return {
      method: "post",
      url: `${api.getRateList}`,
      params
    };
  },

  getTransCurrency: function(params) {
    return {
      method: "get",
      url: `${api.getTransCurrency}`,
      params
    };
  },

  // 保存汇率
  saveRate: function(params) {
    return {
      method: "post",
      url: `${api.saveRate}`,
      params
    };
  },

  // 查询合并任务
  queryMergeTask: function(params) {
    return {
      method: "post",
      url: `${api.queryMergeTask}`,
      params
    };
  },

  // 查询批量子任务
  getSubTasksById: function(id) {
    return {
      method: "get",
      url: `${api.getSubTasksById}/${id}`
    };
  }
};

/**
 * @desc 汇率管理模块接口
 * @param {string} method
 * @param {object} params
 */
const exchangerateService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default exchangerateService;
