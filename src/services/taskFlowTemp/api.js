import { BACKEND } from "../../config/SETUP";
const baseUrl = BACKEND.BASE_URL;
const mrBaseUrl = BACKEND.MR_BASE_URL;

const taskFlowTempApi = {
  getDimMembersTree: `${baseUrl}/journal/getDimTree`, // 获取维度成员接口,
  saveTemplate: `${baseUrl}/taskFlowTemplate/saveTemplate`, // 新增、编辑模板
  saveGroup: `${baseUrl}/taskFlowTemplate/saveGroup`, // 新增、编辑分组
  deleteGroup: `${baseUrl}/taskFlowTemplate/deleteGroup`, // 删除分组
  deleteTemplate: `${baseUrl}/taskFlowTemplate/deleteTemplate`, // 删除模板
  getTemplateTree: `${baseUrl}/taskFlowTemplate/trees`, // 左侧树型列表
  saveTemplateDetail: `${baseUrl}/taskFlowTemplate/saveTemplateDetail`, // 保存模板内容
  getTemplateInfo: `${baseUrl}/taskFlowTemplate/templateDetail`, // 查看模板内容
  getAllDims: `${baseUrl}/taskFlowTemplate/allDims`, // 获取所有维度
  updateGroupTemp: `${baseUrl}/taskFlowTemplate/edit`, // 修改分组、模板 名称/说明
  checkDuplicateName: `${baseUrl}/taskFlowTemplate/checkDuplicateName`, // 重名校验
  getGroups: `${baseUrl}/taskFlowTemplate/groups`, // 获取所有模板分组
  dragMoveNode: `${baseUrl}/taskFlowTemplate/dragMove`, // 拖拽移动
  copyTemplate: `${baseUrl}/taskFlowTemplate/copyTemplate`, // 复制模板
  getTemplateBase: `${baseUrl}/taskFlowTemplate/findById`, // 获取分组、模板基础信息
  getSceneList: `${mrBaseUrl}/sceneElement/tree`, // 获取我的场景所有数据
  tiRecords: `${baseUrl}/metadata/tiRecords`
};
export default taskFlowTempApi;
