import { fetch } from "../baseService";
import api from "./api";
import { getSafetyData } from "@/utils/common";
/**
 * @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      const data = getSafetyData(res.data, "items", []);
      if (res.data) {
        res.data.items = data;
      } else {
        res.data = [];
      }
    }
    return res;
  },
  post: res => res
};

const typeMapConfig = {
  //  新增、编辑模板
  saveTemplate(params) {
    return {
      method: "post",
      url: `${api.saveTemplate}`,
      params
    };
  },
  // 新增、编辑分组
  saveGroup(params) {
    return {
      method: "post",
      url: `${api.saveGroup}`,
      params
    };
  },
  // 删除分组
  deleteGroup(id) {
    return {
      method: "get",
      url: `${api.deleteGroup}/${id}`
    };
  },
  // 删除模板
  deleteTemplate(id) {
    return {
      method: "get",
      url: `${api.deleteTemplate}/${id}`
    };
  },
  // 获取模板左侧树
  getTemplateTree() {
    return {
      method: "get",
      url: `${api.getTemplateTree}`
    };
  },
  // 保存模板内容
  saveTemplateDetail(params) {
    return {
      method: "post",
      url: `${api.saveTemplateDetail}`,
      params
    };
  },
  // 获取模板基础信息
  getTemplateBase(id) {
    return {
      method: "get",
      url: `${api.getTemplateBase}/${id}`
    };
  },
  // 查看模板内容
  getTemplateInfo(id) {
    return {
      method: "get",
      url: `${api.getTemplateInfo}/${id}`
    };
  },
  // 修改分组/模板 名称说明
  updateGroupTemp(params) {
    return {
      method: "post",
      url: `${api.updateGroupTemp}`,
      params
    };
  },
  // 重名校验
  checkDuplicateName(params) {
    return {
      method: "post",
      url: `${api.checkDuplicateName}`,
      params
    };
  },
  // 获取所有模板分组
  getGroups() {
    return {
      method: "get",
      url: `${api.getGroups}`
    };
  },
  // 拖拽移动
  dragMoveNode(params) {
    const { sourceId, targetId, moveType } = params;
    return {
      method: "get",
      url: `${api.dragMoveNode}?sourceId=${sourceId}&targetId=${targetId}&moveType=${moveType}`
    };
  },
  // 复制模板
  copyTemplate(id) {
    return {
      method: "get",
      url: `${api.copyTemplate}/${id}`
    };
  },
  // 获取维度下拉树列表
  getDimMembersTree(params) {
    return {
      method: "get",
      url: `${api.getDimMembersTree}?dimCode=${params}`
    };
  },
  // 获取所有维度
  getAllDims() {
    return {
      method: "get",
      url: `${api.getAllDims}`
    };
  },
  // 获取场景表单列表
  getSceneList() {
    return {
      method: "get",
      url: `${api.getSceneList}`
    };
  },
  tiRecords() {
    return {
      method: "get",
      url: `${api.tiRecords}?totalResults=true&orderBy=scriptPos:desc&limit=9990&offset=0`
    };
  }
};

/**
 * @desc 重分类相关请求处理
 * @param {string} method
 * @param {object} params
 */
const taskFlowTempService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default taskFlowTempService;
