import { fetch } from "../baseService";
import api from "../api";
import { getSafetyData } from "@/utils/common";
/**
 * @desc   @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      const data = getSafetyData(res.data, "items", []);
      if (res.data) {
        res.data.items = data;
      } else {
        res.data = [];
      }
    }
    return res;
  },
  post: res => res
};

const typeMapConfig = {
  // 往来抵销列表
  getCurrentOffsetsList: function(params) {
    const { offsetName, limit, offset } = params;
    return {
      method: "get",
      iscode: false,
      url: `${api.getCurrentOffsetsList}?query=offsetName like'*${offsetName}*'&orderBy=createDate:desc&limit=${limit}&offset=${offset}&totalResults=true`
    };
  },

  // 往来抵销详情
  getCurrentOffsetDetail: function(offsetId) {
    return {
      method: "get",
      url: `${api.getCurrentOffsetDetail}?offsetId=${offsetId}`
    };
  },

  // 新增往来抵销
  saveCurrentOffset: function(params) {
    return {
      method: "post",
      url: `${api.saveCurrentOffset}`,
      params
    };
  },

  // 编辑往来抵销
  updateCurrentOffset: function(params) {
    return {
      method: "post",
      url: `${api.updateCurrentOffset}`,
      params
    };
  },

  // 删除
  deleteCurrentOffset: function(params) {
    return {
      method: "post",
      url: `${api.deleteCurrentOffset}`,
      params
    };
  },

  // 获取资产差异/负债科目列表
  getAccountMembers: function() {
    return {
      method: "get",
      url: `${api.getAccount}`
    };
  },

  // 获取抵销科目列表
  getRAMembers: function() {
    return {
      method: "get",
      url: `${api.getRA}`
    };
  },

  // 校验规则重名
  checkCurrentOffsetName: function(params) {
    return {
      method: "post",
      url: `${api.checkCurrentOffsetName}`,
      params
    };
  }
};

/**
 * @desc 往来抵销模块接口
 * @param {string} method
 * @param {object} params
 */
const currentOffsetService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default currentOffsetService;
