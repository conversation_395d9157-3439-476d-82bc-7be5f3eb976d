import { fetch } from "../baseService";
import api from "./api";
import { getSafetyData } from "@/utils/common";
import commonApi from "../api.js";
/**
 * @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      const data = getSafetyData(res.data, "items", []);
      if (res.data) {
        res.data.items = data;
      } else {
        res.data = [];
      }
    }
    return res;
  },
  post: res => res
};

const typeMapConfig = {
  //  人员
  loadUserByCondition(params) {
    return {
      method: "post",
      url: `${api.loadUserByCondition}`,
      params
    };
  },
  //  组织人员
  getOrgUserManageAccessTree(params) {
    return {
      method: "post",
      url: `${api.getOrgUserManageAccessTree}`,
      params
    };
  },
  //  指定群组
  getGroupListForPage(params) {
    return {
      method: "post",
      url: `${api.getGroupListForPage}`,
      params
    };
  },
  //  指定岗位
  getPostManageAccessTree(params) {
    return {
      method: "post",
      url: `${api.getPostManageAccessTree}`,
      params
    };
  },
  //  流程角色
  getRoleListForPage(params) {
    return {
      method: "post",
      url: `${api.getRoleListForPage}`,
      params
    };
  },
  // 流程控制树表
  getProcessTree(params) {
    return {
      method: "post",
      url: `${api.getProcessTree}`,
      params
    };
  },
  // 新建分组
  addgroup(params) {
    return {
      method: "post",
      url: `${api.addgroup}`,
      params
    };
  },
  // 新建流程
  addprocess(params) {
    return {
      method: "post",
      url: `${api.addprocess}`,
      params
    };
  },

  // 获取模板列表
  getlist(params) {
    const { value } = params;
    return {
      method: "get",
      url: `${api.getlist}?searchText=${encodeURIComponent(value)}`
    };
  },
  // 获取模板详情
  getDetail(params) {
    const { id } = params;
    return {
      method: "get",
      url: `${api.getDetail}/${id}`
    };
  },
  // 查询系统设置信息通用接口（合并体提交时用户可选合并后提交参数为paramCode='submitAfterMerge'）
  mddAppParam(query = decodeURIComponent("paramCode='submitAfterMerge'")) {
    return {
      method: "get",
      url: `${api.mddAppParam}${query}`
    };
  },
  // 合并体提交时弹窗二次确认是否需要执行合并
  mergeSubmitOperation(params) {
    return {
      method: "post",
      url: `${api.mergeSubmitOperation}`,
      params
    };
  },
  // 保存模板详情
  setDetail(params) {
    return {
      method: "post",
      url: `${api.setDetail}`,
      params
    };
  },
  // 获取节点详情
  getnode(params) {
    const { id } = params;
    return {
      method: "get",
      url: `${api.getnode}/${id}`
    };
  },
  // 获取多语言信息
  getTextMapByDataId(params) {
    const { id } = params;
    return {
      method: "post",
      url: `${api.getTextMapByDataId}?dataId=${id}`
    };
  },
  // 获取分组
  getallGroup() {
    return {
      method: "get",
      url: `${api.allGroup}`
    };
  },
  // 修改分组民名称
  editgroup(params) {
    return {
      method: "post",
      url: `${api.editgroup}`,
      params
    };
  },
  // 修改分组民名称
  editTemplateGroup(params) {
    return {
      method: "post",
      url: `${api.editTemplateGroup}`,
      params
    };
  },
  // 删除模板(GET)
  deleteTemplate(params) {
    const { id } = params;
    return {
      method: "get",
      url: `${api.deleteTemplate}/${id}`
    };
  },
  // 删除分组(GET)
  deleteGroup(params) {
    const { id } = params;
    return {
      method: "get",
      url: `${api.deleteGroup}/${id}`
    };
  },
  // 上移/下移(GET)
  move(params) {
    const { id, moveType } = params;
    return {
      method: "get",
      url: `${api.move}?objectId=${id}&moveType=${moveType}`
    };
  },
  // 解析表达式(POST)
  parseMoreExp(params) {
    return {
      method: "post",
      url: `${api.parseMoreExp}`,
      params
    };
  },
  // 复制模板(GET)
  copy(params) {
    const { id } = params;
    return {
      method: "get",
      url: `${api.copy}/${id}`,
      params
    };
  },
  // 模板状态开启/关闭(GET)
  setStatus(params) {
    const { id, flag } = params;
    return {
      method: "get",
      url: `${api.setStatus}/${id}/${flag}`,
      params
    };
  },

  // 点击提交，审核，驳回跳页面接口
  processOperation(params) {
    return {
      method: "post",
      url: `${api.processOperation}`,
      params
    };
  },
  // 流程统计接口
  getProcessStatsList(params) {
    return {
      method: "post",
      url: `${api.getProcessStatsList}`,
      params
    };
  },
  // 流程统计列表详情
  getProcessStatsItemList(params) {
    return {
      method: "post",
      url: `${api.getProcessStatsItemList}`,
      params
    };
  },
  // 流程日志记录列表
  getProcessLogList(params) {
    return {
      method: "post",
      url: `${api.getProcessLogList}`,
      params
    };
  },
  // 流程附件记录列表
  getProcessAttachmentList(params) {
    return {
      method: "post",
      url: `${api.getProcessAttachmentList}`,
      params
    };
  },
  // 流程附件上传
  processAttachmentUpload(params) {
    return {
      method: "post",
      url: `${api.processAttachmentUpload}`,
      params
    };
  },
  // 流程附件删除
  deleteProcessAttachment(params) {
    const { attachmentId } = params;
    return {
      method: "get",
      url: `${api.deleteProcessAttachment}?attachmentId=${attachmentId}`
    };
  },
  // 填报列表
  getOperationReportList(params) {
    return {
      method: "post",
      url: `${api.getOperationReportList}`,
      params
    };
  },
  // 启动
  startOperation(params) {
    return {
      method: "post",
      url: `${api.startOperation}`,
      params
    };
  },
  // 合并
  mergeOperation(params) {
    return {
      method: "post",
      url: `${api.mergeOperation}`,
      params
    };
  },
  // 计算
  computeOperation(params) {
    return {
      method: "post",
      url: `${api.computeOperation}`,
      params
    };
  },
  // 折算
  convertedOperation(params) {
    return {
      method: "post",
      url: `${api.convertedOperation}`,
      params
    };
  },

  // 驳回
  rejectOperation(params) {
    return {
      method: "post",
      url: `${api.rejectOperation}`,
      params
    };
  },

  // 审核
  auditOperation(params) {
    return {
      method: "post",
      url: `${api.auditOperation}`,
      params
    };
  },

  // 提交
  submitOperation(params) {
    return {
      method: "post",
      url: `${api.submitOperation}`,
      timeout: 60000,
      params
    };
  },

  // 获取组织架构树
  getTemplateMemberList(params) {
    return {
      method: "post",
      url: `${api.getTemplateMemberList}`,
      params
    };
  },
  downloadFile(params) {
    const { attachmentPath } = params;
    return {
      method: "get",
      responseType: "blob",
      url: `${api.downloadFile}?filePath=${attachmentPath}`
    };
  },
  // 获取校验
  getProcessCheckList(params) {
    return {
      method: "post",
      url: `${api.getProcessCheckList}`,
      params
    };
  },
  // 获取校验
  operationList() {
    return {
      method: "get",
      url: `${api.operationList}`
    };
  },
  // 填报下的操作
  operationReport(params) {
    return {
      method: "post",
      url: `${api.operationReport}`,
      params
    };
  },
  // 填报下的操作详情
  getOperationReportItem(params) {
    return {
      method: "post",
      url: `${api.getOperationReportItem}`,
      params
    };
  },
  // 附件下载统计
  operationAttachment(params) {
    return {
      method: "post",
      url: `${api.operationAttachment}`,
      params
    };
  },
  // 附件下载统计
  getProcessNodeButtonList(id) {
    return {
      method: "get",
      url: `${api.getProcessNodeButtonList}?objectId=${id}`
    };
  },
  // 附件下载统计
  getPageStats(params) {
    return {
      method: "post",
      url: `${api.getPageStats}`,
      params
    };
  },
  batchGetProcessStatsItemList(params) {
    return {
      method: "post",
      url: `${api.batchGetProcessStatsItemList}`,
      params
    };
  },
  // 流程控制计算/折算批量操作
  batchTIOperation(params) {
    return {
      method: "post",
      url: `${api.batchTIOperation}`,
      params
    };
  },
  // 流程控制提交批量操作
  submitBatchOperation(params) {
    return {
      method: "post",
      url: `${api.submitBatchOperation}`,
      params
    };
  },
  // 流程控制审核批量操作
  auditBatchOperation(params) {
    return {
      method: "post",
      url: `${api.auditBatchOperation}`,
      params
    };
  },
  // 流程控制驳回批量操作
  rejectBatchOperation(params) {
    return {
      method: "post",
      url: `${api.rejectBatchOperation}`,
      params
    };
  },
  // 流程控制启动批量操作
  startBatchOperation(params) {
    return {
      method: "post",
      url: `${api.startBatchOperation}`,
      params
    };
  },
  // 流程控制启动批量操作
  batchPushMessage(params) {
    return {
      method: "post",
      url: `${api.batchPushMessage}`,
      params
    };
  },
  exportProcessStatsItem(params) {
    return {
      method: "post",
      responseType: "blob",
      url: `${api.exportProcessStatsItem}`,
      params
    };
  },
  // 流程监控合并组下拉树
  getScopeTree(params) {
    return {
      method: "post",
      url: `${api.getScopeTree}`,
      params
    };
  },
  // 流程监控列表
  getProcessMonitor(params) {
    return {
      method: "post",
      url: `${api.getProcessMonitor}`,
      params
    };
  },
  // 流程穿透图-排名
  getMonitorDiagram(params) {
    return {
      method: "post",
      url: `${api.getMonitorDiagram}`,
      params
    };
  },
  // 流程排名
  getMonitorRank(params) {
    return {
      method: "post",
      url: `${api.getMonitorRank}`,
      params
    };
  },
  // 流程导出
  exportProcessRankItem(params) {
    return {
      method: "post",
      responseType: "blob",
      url: `${api.exportProcessRankItem}`,
      params
    };
  },
  // 流程表导出
  exportProcessMonitor(params) {
    return {
      method: "post",
      responseType: "blob",
      url: `${api.exportProcessMonitor}`,
      params
    };
  },
  // 新增编辑模板信息接口
  addEditTemplate(params) {
    return {
      method: "post",
      url: `${api.addEditTemplate}`,
      params
    };
  },
  // 模板重名校验接口
  checkTemplateName(params) {
    return {
      method: "post",
      url: `${api.checkTemplateName}`,
      params
    };
  },
  // 模板删除接口
  deleteRuleTemplate(params) {
    return {
      method: "get",
      url: `${api.deleteRuleTemplate}?objectId=${params.objectId}&templateFlag=${params.templateFlag}&templateModule=${params.templateModule}`
    };
  },
  // 模板树形接口
  getTemplateTree(templateModule) {
    return {
      method: "get",
      url: `${api.getTemplateTree}?templateModule=${templateModule}`
    };
  },
  // 模板拖拽移动
  dragMove(params) {
    return {
      method: "get",
      url: `${api.dragMove}?sourceId=${params.sourceId}&targetId=${params.targetId}&moveType=${params.moveType}`
    };
  },
  // 根据模板ID查询流程校验配置详细信息
  getTemplateInfoById(objectId) {
    return {
      method: "get",
      url: `${api.getTemplateInfoById}?objectId=${objectId}`
    };
  },
  // 新增修改流程校验配置信息
  addEditTemplateInfo(params) {
    return {
      method: "post",
      url: `${api.addEditTemplateInfo}`,
      params
    };
  },
  // 下载导入模板（GET 请求）
  exportTemplate(name) {
    return {
      method: "get",
      responseType: "blob",
      url: `${api.exportTemplate}/${name}`
    };
  },
  // 导入Excel（POST 请求）
  importTemplate(params) {
    return {
      method: "post",
      url: `${api.importTemplate}`,
      params
    };
  },
  // 下载失败清单(GET请求)
  exportFailedList({ filePath, fileName }) {
    return {
      method: "get",
      responseType: "blob",
      url: `${api.exportFailedList}?filePath=${filePath}&fileName=${fileName}`
    };
  },

  // 获取维度下所有成员
  getDimMembers: function(params) {
    const searchValue = encodeURIComponent(params.searchValue);
    const querydimMemberName = params.searchValue
      ? `${
        params.dimMemberType ? params.dimMemberType : "dimMemberName"
      } like'*${searchValue}*' and `
      : `dimMemberParentId='${params.dimMemberParentId}' and `;
    return {
      method: "get",
      url: `${commonApi["getDimMember"]}/?query=${querydimMemberName}dimId='${params.dimId}' and dimMemberStatus='enable'&orderBy=dimMemberPos`
    };
  },
  /**
   * 获取沙箱列表
   */

  getSandboxList: function(params) {
    return {
      method: "get",
      url: `${api.getSandboxList}?orderBy = "sandboxPos: desc"`
    };
  },
  // 执行脚本
  executeScript: function(params) {
    return {
      method: "post",
      url: `${api.executeScript}`,
      params: params
    };
  },
  // 执行脚本记录
  executeScriptLog: function(params) {
    return {
      method: "post",
      url: `${api.executeScriptLog}`,
      params: params
    };
  },
  // 执行脚本记录
  executeScriptResult: function(params) {
    return {
      method: "post",
      url: `${api.executeScriptResult}`,
      params: params
    };
  },
  // 新增合并任务
  saveMergeTask: function(params) {
    return {
      method: "post",
      url: `${api.saveMergeTask}`,
      params: params
    };
  },
  // 获取单条脚本数据
  getScriptItem: function(params) {
    return {
      method: "get",
      url: `${api.scriptMetadata}/${params.id}`
    };
  },
  // 根据表达式获取下啦数据
  getSelectTreeByExp: function(params) {
    return {
      method: "post",
      url: `${api.getSelectTreeByExp}`,
      params: params
    };
  }
};

/**
 * @desc 流程控制请求处理
 * @param {string} method
 * @param {object} params
 */
const processService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default processService;
