import { BACKEND } from "../../config/SETUP";
const baseUrl = BACKEND.BASE_URL;
const ecsConsoleBaseUrl = BACKEND.ECS_CONSOLE_BASE_URL;
const ecsConsoleUrl = BACKEND.ECS_CONSOLE_URL;

const processApi = {
  // 人员
  loadUserByCondition: `${ecsConsoleBaseUrl}/console/client/user/loadUserByCondition`,
  // 组织人员
  getOrgUserManageAccessTree: `${baseUrl}/dim/v2/dimObject/getOrgUserManageAccessTree`,
  // 指定群组
  getGroupListForPage: `${ecsConsoleUrl}/group/getGroupListForPage`,
  // 指定岗位
  getPostManageAccessTree: `${baseUrl}/dim/v2/dimPost/getPostManageAccessTree`,
  // 流程角色
  getRoleListForPage: `${ecsConsoleUrl}/role/getRoleListForPage`,
  // 流程管理树形列表
  getProcessTree: `${baseUrl}/process/getProcessTree`,

  // 新建分组
  addgroup: `${baseUrl}/processTemplate/group`,
  // 新建流程
  addprocess: `${baseUrl}/processTemplate/template`,
  // 获取模板列表
  getlist: `${baseUrl}/processTemplate/list`,
  // 保存模板详情
  setDetail: `${baseUrl}/processTemplate/templateDetail`,
  // 获取模板详情
  getDetail: `${baseUrl}/processTemplate/templateDetail`,
  // 获取节点详情
  getnode: `${baseUrl}/processTemplate/node`,
  // 获取多语言信息
  getTextMapByDataId: `${baseUrl}/i18n/v2/dynamic/getTextMapByDataId`,
  // 获取分组
  allGroup: `${baseUrl}/processTemplate/allGroup`,
  // 修改分组民名称
  editgroup: `${baseUrl}/processTemplate/editGroupName`,
  // 修改分组民名称
  editTemplateGroup: `${baseUrl}/processTemplate/editTemplateGroup`,
  // 删除模板(GET)
  deleteTemplate: `${baseUrl}/processTemplate/deleteTemplate`,
  // 删除分组(GET)
  deleteGroup: `${baseUrl}/processTemplate/deleteGroup/`,
  // 上移/下移(GET)
  move: `${baseUrl}/processTemplate/move`,
  // 解析表达式(POST)
  parseMoreExp: `${baseUrl}/exp/parseMoreExp`,
  // 复制模板(GET)
  copy: `${baseUrl}/processTemplate/copy`,
  // 模板状态开启/关闭(GET)
  setStatus: `${baseUrl}/processTemplate/status`,
  // 下载导入模板（GET 请求）
  exportTemplate: `${baseUrl}/processTemplate/exportTemplate`,
  // 导入Excel（POST 请求）
  importTemplate: `${baseUrl}/processTemplate/importTemplate`,
  // 下载失败清单(GET请求)
  exportFailedList: `${baseUrl}/processTemplate/exportFailedList`,

  // 点击提交，审核，驳回跳页面接口
  processOperation: `${baseUrl}/process/processOperation`,
  // 流程统计接口
  getProcessStatsList: `${baseUrl}/process/getProcessStatsList`,
  // 流程统计列表详情
  getProcessStatsItemList: `${baseUrl}/process/getProcessStatsItemList`,
  // 流程日志记录列表
  getProcessLogList: `${baseUrl}/process/getProcessLogList`,
  // 流程附件记录列表
  getProcessAttachmentList: `${baseUrl}/process/getProcessAttachmentList`,
  // 流程附件上传
  processAttachmentUpload: `${baseUrl}/process/processAttachmentUpload`,
  // 流程附件删除
  deleteProcessAttachment: `${baseUrl}/process/deleteProcessAttachment`,
  // 填报列表
  getOperationReportList: `${baseUrl}/process/getOperationReportList`,
  // 启动
  startOperation: `${baseUrl}/process/startOperation`,
  // 合并
  mergeOperation: `${baseUrl}/process/mergeOperation`,
  // 计算
  computeOperation: `${baseUrl}/process/computeOperation`,
  // 折算
  convertedOperation: `${baseUrl}/process/convertedOperation`,

  // 驳回
  rejectOperation: `${baseUrl}/process/rejectOperation`,
  // 审核
  auditOperation: `${baseUrl}/process/auditOperation`,
  // 提交
  submitOperation: `${baseUrl}/process/submitOperation`,
  // 获取组织架构树
  getTemplateMemberList: `${baseUrl}/process/getTemplateMemberList`,
  // 下载文件
  downloadFile: `${baseUrl}/fastdfs/downloadFile`,
  // 获取校验
  getProcessCheckList: `${baseUrl}/process/getProcessCheckList`,
  // 获取异步
  operationList: `${baseUrl}/process/operationList`,
  // 填报下的操作
  operationReport: `${baseUrl}/process/operationReport`,
  // 填报下的操作详情
  getOperationReportItem: `${baseUrl}/process/getOperationReportItem`,
  // 附件下载
  operationAttachment: `${baseUrl}/process/operationAttachment`,
  // 附件下载
  getProcessNodeButtonList: `${baseUrl}/process/getProcessNodeButtonList`,
  // 获取统计信息
  getPageStats: `${baseUrl}/process/getPageStats`,
  // 批量获取获取统计信息
  batchGetProcessStatsItemList: `${baseUrl}/process/batchGetProcessStatsItemList`,
  // 流程控制计算/折算批量操作
  batchTIOperation: `${baseUrl}/process/batchTIOperation`,
  // 流程控制提交批量操作
  submitBatchOperation: `${baseUrl}/process/submitBatchOperation`,
  // 流程控制审核批量操作
  auditBatchOperation: `${baseUrl}/process/auditBatchOperation`,
  // 流程控制驳回批量操作
  rejectBatchOperation: `${baseUrl}/process/rejectBatchOperation`,
  // 流程控制启动批量操作
  startBatchOperation: `${baseUrl}/process/startBatchOperation`,
  // 流程控制导出明细
  exportProcessStatsItem: `${baseUrl}/process/exportProcessStatsItem`,
  // 流程控制流程控制催办批量操作
  batchPushMessage: `${baseUrl}/process/batchPushMessage`,

  /** 流程监控 */
  // 流程监控合并组下拉树
  getScopeTree: `${baseUrl}/process/monitor/getScopeTree`,
  // 流程监控列表
  getProcessMonitor: `${baseUrl}/process/monitor/getProcessMonitor`,
  // 流程穿透图
  getMonitorDiagram: `${baseUrl}/process/monitor/getMonitorDiagram`,
  // 流程排名
  getMonitorRank: `${baseUrl}/process/monitor/getMonitorRank`,
  // 流程穿透图导出
  exportProcessRankItem: `${baseUrl}/process/monitor/exportProcessRankItem`,
  // 流程表导出
  exportProcessMonitor: `${baseUrl}/process/monitor/exportProcessMonitor`,

  // ------------------规则跳转配置-------------------------------
  // 新增编辑模板信息接口
  addEditTemplate: `${baseUrl}/commonTemplate/addEditTemplate`,
  // 模板重名校验接口
  checkTemplateName: `${baseUrl}/commonTemplate/checkTemplateName`,
  // 模板删除接口
  deleteRuleTemplate: `${baseUrl}/commonTemplate/deleteTemplate`,
  // 模板树形接口
  getTemplateTree: `${baseUrl}/commonTemplate/getTemplateTree`,
  // 模板拖拽移动
  dragMove: `${baseUrl}/commonTemplate/dragMove`,
  // 根据模板ID查询流程校验配置详细信息
  getTemplateInfoById: `${baseUrl}/process/verification/getTemplateInfoById`,
  // 新增修改流程校验配置信息
  addEditTemplateInfo: `${baseUrl}/process/verification/addEditTemplateInfo`,
  // 新增合并任务
  saveMergeTask: `${baseUrl}/mergetask/saveMergeTask`,
  // 查询系统设置信息通用接口（合并体提交时用户可选合并后提交参数为paramCode='submitAfterMerge'）
  mddAppParam: `${baseUrl}/metadata/mddAppParam?query=`,
  // 合并体提交时弹窗二次确认是否需要执行合并
  mergeSubmitOperation: `${baseUrl}/process/mergeSubmitOperation`,

  // 脚本执行
  getSandboxList: `${baseUrl}/metadata/mddSandboxes`, // 获取沙箱列表,
  scriptMetadata: `${baseUrl}/metadata/tiRecords`, // 脚本元对象接口 TI脚本所有数据
  executeScript: `${baseUrl}/bff/script/executeScript`, // 执行
  executeScriptResult: `${baseUrl}/bff/script/script-execute-result`, // 执行状态轮询
  executeScriptLog: `${baseUrl}/bff/script/script-execute-log`, // 执行记录
  getSelectTreeByExp: `${baseUrl}/bff/script/getSelectTreeByExp` // 根据表达式获取tree
};
export default processApi;
