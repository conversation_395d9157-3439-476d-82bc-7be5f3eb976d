import { fetch } from "../baseService";
import api from "../api";
import { getSafetyData } from "@/utils/common";
/**
 * @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      const data = getSafetyData(res.data, "items", []);
      if (res.data) {
        res.data.items = data;
      } else {
        res.data = [];
      }
    }
    return res;
  },
  post: res => res
};

const typeMapConfig = {
  // 重分类列表查询
  getReclassificationList: function(params) {
    return {
      method: "post",
      url: `${api.getReclassificationList}`,
      params
    };
  },
  // 新增、编辑重分类
  operateReclassification: function(params) {
    return {
      method: "post",
      url: `${api.operateReclassification}`,
      params
    };
  },
  // 删除重分类
  deleteReclassification: function(objectIds) {
    return {
      method: "get",
      url: `${api.deleteReclassification}?objectIds=${objectIds}`
    };
  },
  getAccountMembers: function() {
    return {
      method: "get",
      url: `${api.getAccount}`
    };
  },
  updateReclassName: function(ruleName) {
    return {
      method: "get",
      url: `${api.updateReclassName}?ruleName=${encodeURIComponent(ruleName)}`
    };
  }
};

/**
 * @desc 重分类相关请求处理
 * @param {string} method
 * @param {object} params
 */
const rearrangeService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default rearrangeService;
