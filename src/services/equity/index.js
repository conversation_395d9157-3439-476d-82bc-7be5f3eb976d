import { fetch } from "../baseService";
import api from "../api";
import { getSafetyData } from "@/utils/common";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
/**
 * @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      const data = getSafetyData(res.data, "items", []);
      if (res.data) {
        res.data.items = data;
      } else {
        res.data = [];
      }
    }
    return res;
  },
  post: res => res
};
const typeMapConfig = {
  // 股权管理列表
  getEquityManagerList: function(params) {
    return {
      method: "post",
      url: `${api.getEquityManagerList}`,
      params
    };
  },
  // 持股方、被持股方搜索项列表
  getICPAndEntityList: function(params) {
    // 参数添加页面维,codeType 0 持股方、1 被持股方
    const { version, year, period, codeType } = params;
    return {
      method: "get",
      url: `${api.getICPAndEntityList}?version=${version}&year=${year}&period=${period}&codeType=${codeType}`
    };
  },
  // 股权管理架构图 typ: 1 法人架构图、 2: 最终持股图
  getLegalSchemaDiagram: function(params) {
    return {
      method: "post",
      url: `${api.getLegalSchemaDiagram}`,
      params
    };
  },
  // 股权管理新增校验
  operateEquityManagers: function(params) {
    return {
      method: "post",
      url: `${api.operateEquityManagers}`,
      params
    };
  },
  // 股权管理删除
  deleteEquityManagers: function(params) {
    const { objectIds, version, year, period } = params;
    return {
      method: "get",
      url: `${api.deleteEquityManagers}?objectIds=${objectIds}&version=${version}&year=${year}&period=${period}`
    };
  },

  // 股权管理 导出、下载模板
  dowloadEquityTemplate: function(params) {
    return {
      method: "post",
      url: `${api.dowloadEquityTemplate}`,
      responseType: "blob",
      params
    };
  },
  // 股权导入
  importEquityTemplate: function(params) {
    return {
      method: "post",
      url: `${api.importEquityTemplate}`,
      params
    };
  },
  // 获取导入结果
  getTaskEquityResult: function(id) {
    return {
      method: "get",
      url: `${api.getTaskEquityResult}?taskId=${id}`
    };
  },
  // 下载导入结果模板
  exportEquityResult: function(id) {
    return {
      method: "get",
      responseType: "blob",
      url: `${api.exportEquityResult}?taskId=${id}`
    };
  },
  // 锁定股权切片数据
  lockEquity: function(params) {
    const { version, year, period } = params;
    return {
      method: "get",
      url: `${api.lockEquity}?version=${version}&year=${year}&period=${period}`
    };
  },
  // 解锁该切片
  unlockEquity: function(params) {
    const { version, year, period } = params;
    return {
      method: "get",
      url: `${api.unlockEquity}?version=${version}&year=${year}&period=${period}`
    };
  },
  // 获取股权管理功能权限
  getEquityAuth: function (params) {
    const { refId } = params;
    const resourceType = "MetadataObject";
    const bizAppId = UrlUtils.getQuery("appId");
    return {
      method: "get",
      iscode: false,
      url: `${api.getEquityAuth}?query=refId='${refId}' and resourceType='${resourceType}' and privilegeCode in ('equity-read','equity-add','equity-copy','equity-delete','equity-edit','equity-import','equity-export') and bizAppId='${bizAppId}'`
    };
  },
  // 获取锁定信息
  getLockUser: function (params) {
    const { version, year, period } = params;
    return {
      method: "get",
      url: `${api.getLockUser}?version=${version}&year=${year}&period=${period}`
    };
  }
};

/**
 * @desc 股权管理相关请求处理
 * @param {string} method
 * @param {object} params
 */
const equityService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default equityService;
