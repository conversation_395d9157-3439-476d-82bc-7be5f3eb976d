import DsUtils from "yn-p1/libs/utils/DsUtils";
import { statusMessage } from "../locales/statusMessage";
import UiUtils from "yn-p1/libs/utils/UiUtils";

// import api from "./api";

// const dsInstance = DsUtils.getInstance(api.baseUrl);

/**
 * @desc 设置请求config
 * @param {string} url
 * @param {string} method
 * @param {object} params
 */
const setConfig = (url, method, params) => {
  const config = {
    url
  };
  config.method = method;
  if (method === "put" || method === "post") {
    config.params = params;
  } else if (method === "delete") {
    config.url = `${config.url}/${params}`;
  } else if (method === "patch") {
    config.params = params;
    config.url = `${config.url}/${params.objectId}`;
  } else if (method === "get") {
    if (params && params.special_post) {
      // 处理后端自定义的特殊POST
      config.url = `${config.url}/${params.id}`;
      delete config.special_post;
    } else {
      config.params = {
        params: params
      };
    }
  } else {
    config.params = params;
  }
  return config;
};

/**
 * @desc 设置请求成功返回值
 * @param {object} result
 */
const setSuccessResult = (result, status = 200, headers) => {
  const locale = sessionStorage.getItem("locale");
  return {
    status: 200, // 前端统一为200
    data: result,
    headers: headers,
    error: null,
    massage: statusMessage[status]
      ? statusMessage[status][locale]
      : statusMessage[200][locale]
  };
};

/**
 * @desc 设置失败时状态
 */
const setFailResult = (error, status = 500) => {
  const locale = sessionStorage.getItem("locale") || "zh";
  const massage = statusMessage[status]
    ? statusMessage[status][locale]
    : statusMessage[500][locale];
  UiUtils.errorMessage(error.message || error.massage || massage);
  return {
    status: status, // 前端统一为500
    data: null,
    error,
    massage: error.message ? error.message : massage
  };
};

/**
 * @desc 请求数据
 * @param {string} url
 * @param {string} method
 * @param {object} params
 * @param {object} config 当默认配置不能满足时 特别是url需要拼接的情况，以及url有参数body也有参数的情况，自己外部传config和axios一致
 */
export const fetch = (url, method, params, config = null) => {
  return new Promise((resolve, reject) => {
    const configure = config || setConfig(url, method, params);
    return DsUtils[configure.method](
      configure.url,
      configure.params ? configure.params : configure,
      configure.responseType ? { responseType: configure.responseType } : null
    )
      .then(res => {
        if (res) {
          // 登录验证拦截
          if (
            res.headers.loginstatus &&
            res.headers.loginstatus === "Time-Out"
          ) {
            // 弹出框提示登录
            UiUtils.error({
              title: "重新登录",
              content: "登录失效，请重新登录！"
            });
            return;
          }
          const result = res.data;
          // 成功的状态，后端成功时根据method不同返回的状态也不同
          if (res.status === 200 || res.status === 201 || res.status === 204) {
            // if (res.data.messageType === "ERROR") {
            //   // 业务逻辑错误
            //   if (result) {
            //     reject(setFailResult(result, res.status, res.headers));
            //     // resolve(setSuccessResult(result, res.status, res.headers));
            //   }
            // } else {
            resolve(setSuccessResult(result, res.status, res.headers));
            // }
          } else {
            reject(setFailResult(result, res.status, res.headers));
          }
        }
      })
      .catch(error => {
        // const result = error.response ? error.response.data : error;
        // const status = error.response ? error.response.status : 500;
        // reject(setFailResult(result, status));
        reject(error);
      });
  });
};
