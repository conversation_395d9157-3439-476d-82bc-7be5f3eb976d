import { BACKEND } from "../config/SETUP";
const baseUrl = BACKEND.BASE_URL;
const custom_url = BACKEND.CONSOLIDATION_MDD_BASE_CUSTOM__URL; // bff
const ecsConsoleBaseUrl = BACKEND.ECS_CONSOLE_BASE_URL;
const mrBaseUrl = BACKEND.MR_BASE_URL; // mr/bff
const dashboardBaseUrl = BACKEND.DASHBOARD_BASE_URL; // dashboard
const ecsPlatform = BACKEND.VUE_APP_ECS_PLATFORM;
import ruleSetMaintenanceApi from "./ruleSetMaintenanceApi";

const api = {
  getConsolidationServerVer: `${baseUrl}/version`, // 获取后端版本号（合并3.0） 包含mdd
  getDashboardServerVer: `${dashboardBaseUrl}/bff/version`, // 获取 dashboard 版本号
  getMrServerVer: `${mrBaseUrl}/version`, // 获取 mr 版本号
  getMddServerVer: `${baseUrl}/bff/version`, // 获取 mdd 版本号
  // 组织架构模块 start
  getVersionTree: `${baseUrl}/organizationManages/getVersionTree`, //  获取版本下拉树
  getYearTree: `${baseUrl}/organizationManages/getYearTree`, // 获取年下拉树
  getPeriodTree: `${baseUrl}/organizationManages/getPeriodTree`, // 获取 期间下拉树
  getScopeTree: `${baseUrl}/organizationManages/getScopeTree`, // 获取合并组下拉树
  getOrganizationMenuTree: `${baseUrl}/organizationManages/getOrganizationMenuTree`, // 获取组织架构树 数据
  addSameLevelNode: `${baseUrl}/organizationManages/saveIdenticalDimMemberRelations`, // 添加同级合并组、组织
  addChildLevelNode: `${baseUrl}/organizationManages/saveChildDimMemberRelations`, // 添加 子集合并组、组织
  moveNodeByType: `${baseUrl}/organizationManages/operationMemberRelation`, // 移动tree node
  deleteNodeById: `${baseUrl}/organizationManages`, //  删除tree 节点
  copyMonthFramework: `${baseUrl}/organizationManages/copyMonthFramework`, // 复制月度架构
  getOrganizationPage: `${baseUrl}/organizationManages/getOrganizationPage`, // 表格数据接口
  saveOrganizationTableData: `${baseUrl}/organizationManages/saveMonthFramework`, // 表格数据接口
  getTableOrganizationList: `${baseUrl}/organizationManages/getOrganizationList`, // 获取表格筛选组织列表数据
  getEquityChart: `${baseUrl}/organizationManages/getEquityChart`, // 获取组织架构股权图 数据
  copyFinalShareholding: `${baseUrl}/organizationManages/copyFinalShareholding`, // 复制系统计算持股比例到最终持股
  dowloadOrganizationTemplate: `${baseUrl}/organizationManages/exportTemplate`, // 下载模板
  importOrganizationTemplate: `${baseUrl}/organizationManages/importTemplate`, // 导入
  getTaskOrganizationResult: `${baseUrl}/organizationManages/getTaskResult`, // 获取导入结果
  exportOrganizationResult: `${baseUrl}/organizationManages/exportResult`, // 下载导入结果文件
  batchCopyShareholding: `${baseUrl}/organizationManages/batchCopyShareholding`, // 批量复制当前合并组系统持股到最终持股
  batchScopeCopyShareholding: `${baseUrl}/organizationManages/batchScopeCopyShareholding`, // 批量复制指定合并组系统持股到最终持股
  allPermission: `${baseUrl}/organizationManages/allPermission`, // 是否有全部的成员权限
  updateOrganizationEntityChildNum: `${baseUrl}/organizationManages/updateOrganizationEntityChildNum`, // 修改合并组数量
  appParams: `${baseUrl}/metadata/mddAppParam`, // 获取系统参数
  checkOrganizationProcessExists: `${baseUrl}/organizationManages/checkOrganizationProcessExists`, // 1、检查目标版本年期间下无组织架构数据；2、目标版本年期间下所有组织/合并组节点流程控制中的流程状态均为未启动。
  getHoldingDetailMemberAttrValues: `${baseUrl}/organizationManages/getHoldingDetailMemberAttrValues`, // 获取股权项目是否显示属性值
  // 组织架构模块 end

  // 公共模块 start
  getCurrentUser: `${custom_url}/user/getCurrentUser`,
  getSystemMenuList: `${ecsConsoleBaseUrl}/console/client/appMenu/getMenu`,
  getDimMember: `${baseUrl}/metadata/dimMembers`, // 获取合并组下拉树
  getLevels: `${custom_url}/dimMembers/get-levels`, // 获取层级树
  dimAttrs: `${baseUrl}/metadata/dimAttrs`, // 维度对应的属性
  getDimAttrs: `${baseUrl}/metadata/dimAttrs`, // 获取属性
  getDimAttrsBff: `${baseUrl}/bff/dimAttrs`, // 穿梭框搜索属性 是 bff
  getDimAttrsById: `${custom_url}/dimAttrs`, //  获取属性的孩子
  dimAttrTypes: `${baseUrl}/metadata/dimAttrTypes`, // 维度属性类型接口
  getVariables: `${custom_url}/variables/selectVariablesByDimId`, // 获取变量
  getDimInfoById: `${baseUrl}/metadata/dims/`,
  getEquityManagements: `${baseUrl}/equityManagements`, // 查看具体持股信息
  getExpMember: `${baseUrl}/exp/parseExp`, // 根据表达式 解析维度成员
  parseCateGoryExp: `${baseUrl}/exp/parseCateGoryExp`, // 解析 类别 维度 成员
  getExpMemberWithCode: `${baseUrl}/exp/parseMoreExp`, // 根据表达式 解析维度成员(返参带有membercode字段)
  getCompanyTree: `${baseUrl}/equityManagements/getCompanyTree`, // ICP树型下拉列表 、 持股公司树
  getEntityTree: `${baseUrl}/equityManagements/getEntityTree`, // Entity树型下拉列表
  getSubsetById: `${baseUrl}/metadata/dimSubsets`, // 获取子集通过维度id
  getEnableLanguages: `${baseUrl}/i18n/v2/language/getEnableLanguages`, // 获取语种信息
  queryApiName: `${baseUrl}/metadata/dims`, // 根据 维度id 查询维度 apiName
  saveOrUpdateUserScale: `${baseUrl}/defineScale/saveOrUpdateUserScale`, // 修改小数点位数
  selectUserScale: `${baseUrl}/defineScale/selectUserScale`, // 查询设置的小数点位数
  queryMergeTaskStatus: `${baseUrl}/mergetask/queryMergeTaskStatus`, // 查询异步任务状态
  getFirstDimMemberList: `${baseUrl}/getFirstDimMemberList`, // 根据 dimCode获取第一层成员数据 分页
  getCommonDimMemberList: `${baseUrl}/getCommonDimMemberList`, // 根据dimMemberId获取子节点数据 分页
  getQueryDimMemberList: `${baseUrl}/getQueryDimMemberList`, // 查询维度成员信息 分页
  getFirstDimMember: `${baseUrl}/getFirstDimMember`, // 查询第一个叶子节点信息
  getLastSettingV: `${baseUrl}/userSetting/selectUserSetting`, // 获取上一次保存的唯一标识
  saveOrUpdateUserSetting: `${baseUrl}/userSetting/saveOrUpdateUserSetting`, // 保存唯一标识
  getDimSubsets: `${baseUrl}/metadata/dimSubsets`, // 获取维度下子集
  transPageDim: `${baseUrl}/transPageDim`, // 转换页面维
  loadUserByCondition: `${ecsConsoleBaseUrl}/console/client/user/loadUserByCondition`,
  getRoleList: `${ecsConsoleBaseUrl}/console/client/role/getRoleList`,
  getGroupList: `${ecsConsoleBaseUrl}/console/client/group/getGroupList`,
  dimPost: `${baseUrl}/metadata/dimPost/selectDisplayData`,
  dimOrg: `${baseUrl}/metadata/dimOrg/selectDisplayData`,
  msgTemplates: `${ecsConsoleBaseUrl}/metadata/msgTemplates`,
  batchDownloadFile: `${ecsPlatform}/file/v2/network/batchDownloadFile`,
  canPreview: `${ecsPlatform}/file/v2/network/canPreview`,
  uploadAttachment: `${ecsPlatform}/file/v2/network/uploadAttachment`,
  downloadFile: `${ecsPlatform}/file/v2/network/downloadFile`,
  filePreview: `${ecsPlatform}/file/v2/network/filePreview`,
  selectByObjectIds: `${baseUrl}/bff/dimMembers/selectByObjectIds`, // 返回成员对象集合
  // 公共模块 end

  // 对账模块  start
  getDataByCube: `${baseUrl}/reconciliationSetting/getDataByCube`, // 根据cube获取已选和待选维度 数据
  saveData: `${baseUrl}/reconciliationSetting/saveData`, // 对账设置保存接口
  getCubes: `${baseUrl}/reconciliationSetting/getCubes`, // 获取cube列表 数据
  getData: `${baseUrl}/reconciliationSetting/getData`, // 对账设置查询 数据
  getMembersTree: `${baseUrl}/reconciliationSetting/getMembersTree`, // 获取维度成员下拉树 数据
  getListReconciliation: `${baseUrl}/reconciliation/listReconciliation`, // 获取对账单文件夹列表
  saveReconciliation: `${baseUrl}/reconciliation/saveReconciliation`, // 保存对账单基础信息
  deleteReconciliation: `${baseUrl}/reconciliation/deleteReconciliation`, //  删除对账单
  getReconciliationById: `${baseUrl}/reconciliation/get`, // 根据id获取 对账报表信息
  editReconciliation: `${baseUrl}/reconciliation/editFolder`, // 编辑 对账信息
  getMemberByDimCode: `${baseUrl}/reconciliation/getDimMembersTree`, // 根据 dimCode 获取维度成员
  cateGoryDimMembersTree: `${baseUrl}/reconciliation/cateGoryDimMembersTree`, // 类别 Category 维度 获取成员下拉列表接口
  getReconciliationConfigById: `${baseUrl}/reconciliation/getReconciliationConfig`, // 根据id获取对账配置信息
  getCustomDim: `${baseUrl}/reconciliation/getCustomDim`, // 获取自定义维度成员
  getMembersByDimsExps: `${baseUrl}/reconciliation/getMembersByDimsExps`, // 获取自定义维度成员
  getAccount: `${baseUrl}/reconciliation/getAccount`, // 获取 指定科目下拉树 数据
  getAccountByAttr: `${baseUrl}/reconciliation/getAccountByAttr`, // 根据科目类别属性获取科目
  getRA: `${baseUrl}/reconciliation/getRA`, // 获取 插式账户下拉树 数据
  saveReconciliationConfig: `${baseUrl}/reconciliation/saveReconciliationConfig`, // 保存 对账配置
  saveAsReconciliationConfig: `${baseUrl}/reconciliation/saveAs`, // 保存 对账配置
  generateReport: `${baseUrl}/reconciliation/generateReport`, // 生成对账报告
  asyncGenerateReport: `${baseUrl}/reconciliation/asyncGenerateReport`, // 异步生成对账报告
  getReconciliationByTaskId: `${baseUrl}/mergetask/reconciliation`, // 根据任务Id查询对账报告结果
  expandChildren: `${baseUrl}/reconciliation/expandChildren`, // 对账报告，父项展开，及子项加载更多
  columnSearchList: `${baseUrl}/reconciliation/columnSearchList`, // 对账报告，获取过滤列表
  searchReport: `${baseUrl}/reconciliation/searchReport`, // 搜索对账报表
  dragMove: `${baseUrl}/reconciliation/dragMove`, // 树节点移动
  reconciliationExportFile: `${baseUrl}/reconciliation/exportFile`, // 对账导出
  // dimMembersWithAuth: `${baseUrl}/dimMembersWithAuth`, // 对账穿梭框 控制成员权限接口 已弃用
  dimMembersWithAuth: `${baseUrl}/metadata`, // 对账穿梭框 控制成员权限接口 mdd 服务
  getPublicAuth: `${baseUrl}/reconciliation/publicAuth`, // 获取当前用户是否有 余额对账报表 公有报表权限
  getVoucherPublicAuth: `${baseUrl}/voucherReconciliation/publicAuth`, // 获取当前用户是否有 凭证对账报表 公有报表权限
  userMemory: `${baseUrl}/reconciliation/userMemory`,
  reconciliationRefresh: `${baseUrl}/reconciliation/refresh`, // 对账报告更改匹配项小数位数
  expandAll: `${baseUrl}/reconciliation/expandAll`, // 全部展开
  checkJournalTemplateExist: `${baseUrl}/reconciliation/checkJournalTemplateExist`, // 对账配置，校验日记账模板是否存在
  parseICPExp: `${baseUrl}/exp/parseICPExp`, // 对账穿梭框解析，只解析内部往来公司
  entityAuth: `${baseUrl}/reconciliation/entityAuth`,
  reconciliationDrillDim: `${baseUrl}/reconciliation/reconciliationDrillDim`,
  getFormRelationCell: `${baseUrl}/reconciliation/get-form-relation-cell`, // 获取钻取路径
  getReconciliationCoordinationSetting: `${baseUrl}/reconciliationSetting/getReconciliationCoordinationSetting`, // 查询对账协同状态设置
  saveReconciliationCoordinationSetting: `${baseUrl}/reconciliationSetting/saveReconciliationCoordinationSetting`, // 保存对账协同状态设置
  hasReconciliationCoordinationAuth: `${baseUrl}/reconciliation/hasReconciliationCoordinationAuth`, // 是否有对账协同权限
  updateCoordinationStatus: `${baseUrl}/reconciliation/updateCoordinationStatus`, // 调整对账状态
  deleteStatementCoordination: `${baseUrl}/reconciliationSetting/deleteStatementCoordination`, // 删除对账协同状态设置
  // 对账模块  end

  // 股权管理 start
  copyShareholdingRatio: `${baseUrl}/equityManagements/copyShareholdingRatio`, // 复制持股比例
  getEquityManagerList: `${baseUrl}/equityManagements/getEquityManagerList`, // 股权管理列表
  getICPAndEntityList: `${baseUrl}/equityManagements/getICPAndEntityList`, // 持股方、被持股方搜索项列表
  getLegalSchemaDiagram: `${baseUrl}/equityManagements/getLegalSchemaDiagram`, // 股权管理架构图
  operateEquityManagers: `${baseUrl}/equityManagements/operateEquityManagers`, // 股权管理新增修改校验 新增、修改
  deleteEquityManagers: `${baseUrl}/equityManagements/deleteEquityManagers`, // 股权管理删除
  dowloadEquityTemplate: `${baseUrl}/equityManagements/exportTemplate`, // 股权管理下载模板
  importEquityTemplate: `${baseUrl}/equityManagements/importTemplate`, // 导入
  getTaskEquityResult: `${baseUrl}/equityManagements/getTaskResult`, // 获取导入结果
  exportEquityResult: `${baseUrl}/equityManagements/exportResult`, // 下载导入结果文件
  lockEquity: `${baseUrl}/equityManagements/lock`, //  股权管理加锁
  unlockEquity: `${baseUrl}/equityManagements/unLock`, // 股权管理解锁
  getEquityAuth: `${baseUrl}/metadata/functionalSecurities`, // 获取股权管理权限。
  getMetadataRecords: `${baseUrl}/metadataSetup/metadataRecords`, // 获取权限集信息
  getLockUser: `${baseUrl}/equityManagements/getLockUser`, // 获取切换锁定用户信息
  // 股权管理 end

  // 日记账模块 start
  getJournalTempTree: `${baseUrl}/journal/buildWholeTree`, // 日记账模板左侧树
  addGroupNode: `${baseUrl}/journal/addGroupNode`, // 新增分组
  updateGroupNode: `${baseUrl}/journal/updateGroupNode`, // 编辑分组
  deleteGroupNode: `${baseUrl}/journal/deleteGroupNode`, // 删除分组
  addJournalTemplate: `${baseUrl}/journal/addJournalTemplate`, // 新增模板
  updateJournalTemplate: `${baseUrl}/journal/updateJournalTemplate`, // 修改模板
  deleteJournalTemplate: `${baseUrl}/journal/deleteJournalTemplate`, // 删除模板
  selectGroupNode: `${baseUrl}/journal/selectGroupNode`, // 查询模板
  getCubeLists: `${baseUrl}/journal/getCubeList`, // 获取模型列表
  checkTemplateName: `${baseUrl}/journal/checkTemplateName`, // 日记账模板名称校验
  getTempGroupList: `${baseUrl}/journal/getGroupNodeList`, // 获取模板分组列表
  moveGroupNode: `${baseUrl}/journal/moveGroupNode`, // 拖拽节点
  getDimMembersTree: `${baseUrl}/journal/getDimTree`, // 获取维度成员接口
  addJournal: `${baseUrl}/journal/addJournal`, // 新增日记账
  queryAllTemplate: `${baseUrl}/journal/queryAllTemplate`, // 新增日记账获取模板list
  queryJournal: `${baseUrl}/journal/queryJournal`, // 日记账列表查询
  drillingJournal: `${baseUrl}/journal/drillingJournal`, // 日记账详细信息
  drillingJournalBase: `${baseUrl}/journal/drillingJournalBase`, // 日记账详情基本信息
  queryJournalDims: `${baseUrl}/journal/queryJournalDims`, // 日记账详情维度信息
  queryJournalDimsByTemplateId: `${baseUrl}/journal/queryJournalDimsByTemplateId`, // 日记账详情维度信息
  drillingJournalContents: `${baseUrl}/journal/drillingJournalContents`, // 日记账详情内容信息
  batchDeleteJournal: `${baseUrl}/journal/batchDeleteJournal`, // 批量删除日记账
  updateJournal: `${baseUrl}/journal/updateJournal`, // 修改日记账基本信息
  updateJournalContent: `${baseUrl}/journal/updateJournalContent`, // 修改日记账内容
  queryDimMembers: `${baseUrl}/journal/queryDimMembers`, // 查询期间的所有成员
  getShowDims: `${baseUrl}/journal/getShowDims`, // 日记账列表、查询可变维度列表
  queryJournalContent: `${baseUrl}/journal/queryJournalContent`, // 日记账查询
  postingJournal: `${baseUrl}/journal/postingJournal`, // 批量过账
  cancelPostingJournal: `${baseUrl}/journal/cancelPostingJournal`, // 批量取消过账
  copyJournal: `${baseUrl}/journal/copyJournal`, // 复制日记账
  exportJournalTemplate: `${baseUrl}/journal/exportJournalTemplate`, // 导出日记账模板
  exportJournals: `${baseUrl}/journal/exportJournals`, // 批量导出日记账
  importJournals: `${baseUrl}/journal/importJournals`, // 导入日记账
  downloadImportResult: `${baseUrl}/journal/downloadImportResult`, // 下载导入失败清单
  getTaskResult: `${baseUrl}/journal/getTaskResult`, // 查询导入结果
  queryDimMembersById: `${baseUrl}/journal/queryDimMembersById`, // 查询维度下拉选项
  expParsingWithFilter: `${baseUrl}/journal/expParsingWithFilter`, // 查询维度下拉选项
  queryDimMembersByCondition: `${baseUrl}/journal/queryDimMembersByCondition`, // 针对审计线索 穿梭框（属性为 单体、合并体）过滤
  queryAudittrailMembers: `${baseUrl}/journal/getDimTree`, // 针对审计线索 下拉列表
  getAudittraiMembers: `${baseUrl}/journal/expParsing`,
  getUserColumns: `${baseUrl}/defineColumns/selectUserColumn`, // 查询当前用户自定义列信息
  saveOrUpdateUserColumn: `${baseUrl}/defineColumns/saveOrUpdateUserColumn`, // 新增或修改自定义列信息
  checkUserAuth: `${baseUrl}/journal/checkUserAuth`, // 钻取日记账详情权限校验。
  generateJournalId: `${baseUrl}/journal/generateJournalId`, // 生成日记账id
  journalCheckParam: `${baseUrl}/journal/checkParam`, // 差异对账，新增日记账校验传参
  getContinuedYearList: `${baseUrl}/journal/getContinuedYearList`, // 获取持续年列表
  checkSpecialAccount: `${baseUrl}/journal/checkSpecialAccount`, // 校验特殊科目成员410499
  filterParamMap: `${baseUrl}/journal/filterParamMap`, // 过滤掉入参 成员的中 的父项
  getMemberInfo: `${baseUrl}/journal/getMemberInfo`, // 获取成员信息
  journalFilesRelation: `${baseUrl}/journal/journalFilesRelation`, // 日记账上传附件
  getJournalFiles: `${baseUrl}/journal/getJournalFiles`, // 查询日记账附件列表
  deleteJournalFilesRelation: `${baseUrl}/journal/deleteJournalFilesRelation`, // 删除日记账附件
  // 日记账模块 end

  // 往来抵销 start
  getCurrentOffsetsList: `${baseUrl}/metadata/currentOffsets`, // 往来抵销分页列表 metadata
  deleteCurrentOffset: `${baseUrl}/currentOffset/deleteCurrentOffset`, // 删除往来抵销规则
  saveCurrentOffset: `${baseUrl}/currentOffset/saveCurrentOffset`, // 新增往来抵销
  updateCurrentOffset: `${baseUrl}/currentOffset/updateCurrentOffset`, // 编辑往来抵销
  getCurrentOffsetDetail: `${baseUrl}/currentOffset/getCurrentOffsetDetail`, // 往来抵销详情
  checkCurrentOffsetName: `${baseUrl}/currentOffset/checkCurrentOffsetName`, // 往来抵销规则名称重复校验
  getOffsetAccount: `${baseUrl}/currentOffset/getOffsetAccount`, // 获取往来抵消资产/负债列表数据

  // 往来抵销 end

  // 重分类模块 start
  getReclassificationList: `${baseUrl}/reclassification/getReclassificationList`, // 重分类列表查询
  operateReclassification: `${baseUrl}/reclassification/operateReclassification`, // 新增编辑重分类
  deleteReclassification: `${baseUrl}/reclassification/deleteReclassification`, // 删除重分类
  updateReclassName: `${baseUrl}/reclassification/checkReclassification`, // 重分类规则名称校验
  // 重分类模块 end

  // 汇率管理 start
  getRateList: `${baseUrl}/rateManages/getRateList`, // 查询汇率列表
  saveRate: `${baseUrl}/rateManages/saveRate`, // 保存汇率列表
  getTransCurrency: `${baseUrl}/rateManages/getTransCurrency`, // 查询标记信息cube转换货币
  // 汇率管理 end

  // POV功能 start
  saveOrUpdateUserPov: `${baseUrl}/pov/saveOrUpdateUserPov`, // 新增或修改用户pov
  selectUserPov: `${baseUrl}/pov/selectUserPov`, // 查询当前用户pov值
  // POV功能 start

  // 合并任务查询 start
  queryMergeTask: `${baseUrl}/mergetask/queryMergeTask`, // 查询合并任务
  getSubTasksById: `${baseUrl}/mergetask/getSubTasksById`, // 查询批量子任务
  // 合并任务查询 end

  ...ruleSetMaintenanceApi,
  // 维度成员引用
  relationPage: `${baseUrl}/relation/relationPage`,
  moduleTypeList: `${baseUrl}/relation/moduleTypeList`
};

export default api;
