import { BACKEND } from "../../config/SETUP";
const baseUrl = BACKEND.BASE_URL;

const emailApi = {
  // 删除邮件规则
  deleteEmail: `${baseUrl}/emailNotifyRule/delete`,
  // 新增邮件规则
  saveEmail: `${baseUrl}/emailNotifyRule/save`,
  // 编辑邮件规则
  editEmail: `${baseUrl}/emailNotifyRule/edit`,
  // 列表/搜索
  searchEmail: `${baseUrl}/emailNotifyRule/list`,
  // 查看规则详情
  queryEmail: `${baseUrl}/emailNotifyRule/query/`,
  // 获取邮件模板列表
  emailTemplates: `${baseUrl}/emailNotifyRule/emailTemplates`,
  // 发送对账邮件（GET）
  sendReconciliationMail: `${baseUrl}/emailNotifyRule/sendReconciliationMail/`,
  // 验证对账报表数据有效性
  taskExpire: `${baseUrl}/reconciliation/taskExpire/`
};
export default emailApi;
