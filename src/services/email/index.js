import { fetch } from "../baseService";
import api from "./api";
import { getSafetyData } from "@/utils/common";
/**
 * @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      const data = getSafetyData(res.data, "items", []);
      if (res.data) {
        res.data.items = data;
      } else {
        res.data = [];
      }
    }
    return res;
  },
  post: res => res
};

const typeMapConfig = {
  //  删除邮件规则
  deleteEmail(params) {
    return {
      method: "post",
      url: `${api.deleteEmail}`,
      params
    };
  },
  // 新增邮件规则
  saveEmail(params) {
    return {
      method: "post",
      url: `${api.saveEmail}`,
      params
    };
  },
  // 编辑邮件规则
  editEmail(params) {
    return {
      method: "post",
      url: `${api.editEmail}`,
      params
    };
  }, // 列表/搜索
  searchEmail({ searchRuleName, pageNum, pageSize }) {
    return {
      method: "get",
      url: `${api.searchEmail}?pageSize=${pageSize}&pageNum=${pageNum}&searchRuleName=${searchRuleName}`
    };
  },
  // 查看规则详情
  queryEmail(id) {
    return {
      method: "get",
      url: `${api.queryEmail}/${id}`
    };
  },
  // 获取邮件模板列表
  emailTemplates(params) {
    return {
      method: "get",
      url: `${api.emailTemplates}`
    };
  },
  // 发送对账邮件（GET）
  sendReconciliationMail({ taskId, emailType }) {
    return {
      method: "get",
      url: `${api.sendReconciliationMail}?taskId=${taskId}&emailType=${emailType}`
    };
  },
  // 验证对账报表数据有效性
  taskExpire(taskId) {
    return {
      method: "get",
      url: `${api.taskExpire}/${taskId}`
    };
  }
};

/**
 * @desc 流程控制请求处理
 * @param {string} method
 * @param {object} params
 */
const emailService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default emailService;
