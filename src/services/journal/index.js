import { fetch } from "../baseService";
import api from "../api";
import { getSafetyData } from "@/utils/common";
/**
 * @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      const data = getSafetyData(res.data, "items", []);
      if (res.data) {
        res.data.items = data;
      } else {
        res.data = [];
      }
    }
    return res;
  },
  post: res => res
};

const typeMapConfig = {
  // 日记账模板左侧树
  getJournalTempTree: function(params) {
    return {
      method: "get",
      url: `${api.getJournalTempTree}`
    };
  },
  // 新增分组
  addGroupNode: function(params) {
    return {
      method: "post",
      url: `${api.addGroupNode}`,
      params
    };
  },
  // 编辑分组
  updateGroupNode: function(params) {
    return {
      method: "post",
      url: `${api.updateGroupNode}`,
      params
    };
  },
  // 删除分组
  deleteGroupNode: function(params) {
    return {
      method: "post",
      url: `${api.deleteGroupNode}?groupNodeId=${params.groupNodeId}`
    };
  },
  // 新增模板
  addJournalTemplate: function(params) {
    return {
      method: "post",
      url: `${api.addJournalTemplate}`,
      params
    };
  },
  // 查询模板
  selectGroupNode: function(params) {
    return {
      method: "get",
      url: `${api.selectGroupNode}?groupNodeId=${params.groupNodeId}`
    };
  },
  // 修改模板
  updateJournalTemplate: function(params) {
    return {
      method: "post",
      url: `${api.updateJournalTemplate}`,
      params
    };
  },
  // 删除模板
  deleteJournalTemplate: function(params) {
    return {
      method: "post",
      url: `${api.deleteJournalTemplate}?leafId=${params.leafId}`
    };
  },
  checkTemplateName: function(params) {
    const { templateId, templateName } = params;
    return {
      method: "get",
      url: `${
        api.checkTemplateName
      }?templateId=${templateId}&templateName=${encodeURIComponent(
        templateName
      )}`
    };
  },
  // 获取 cube 列表
  getCubeLists: function(params) {
    return {
      method: "get",
      url: `${api.getCubeLists}`
    };
  },
  // 获取模板分组列表
  getTempGroupList: function(params) {
    return {
      method: "get",
      url: `${api.getTempGroupList}`
    };
  },
  // 节点拖拽
  moveGroupNode(params) {
    const { srcId, targetId, direction } = params;
    return {
      method: "post",
      url: `${api.moveGroupNode}?srcId=${srcId}&targetId=${targetId}&direction=${direction}`
    };
  },
  // 获取维度下拉树列表
  getDimMembersTree(params) {
    let dimCode = params;
    let needFormat = false;
    if (typeof params !== "string") {
      dimCode = params.dimCode;
      needFormat = params.needFormat;
    }
    return {
      method: "get",
      url: `${api.getDimMembersTree}?dimCode=${dimCode}&needFormat=${needFormat}`
    };
  },
  // 获取维度下拉树列表
  addJournal(params) {
    return {
      method: "post",
      url: `${api.addJournal}`,
      params
    };
  },
  // 新增日记账获取模板list
  queryAllTemplate() {
    return {
      method: "get",
      url: `${api.queryAllTemplate}`
    };
  },
  // 日记账列表查询
  queryJournal(params) {
    return {
      method: "post",
      url: `${api.queryJournal}`,
      params
    };
  },
  // 日记账详情基本信息
  drillingJournalBase(params) {
    return {
      method: "get",
      url: `${api.drillingJournalBase}?journalId=${params}`,
      params
    };
  },
  // 日记账详情维度信息
  queryJournalDims(params) {
    return {
      method: "get",
      url: `${api.queryJournalDims}?journalId=${params}`,
      params
    };
  },

  // 日记账详情维度信息,根据模板的Id获取
  queryJournalDimsByTemplateId(params) {
    return {
      method: "get",
      url: `${api.queryJournalDimsByTemplateId}?templateId=${params}`
    };
  },

  // 日记账详情内容信息
  drillingJournalContents(params) {
    return {
      method: "get",
      url: `${api.drillingJournalContents}?journalId=${params}`,
      params
    };
  },
  // 批量删除日记账
  batchDeleteJournal(params) {
    return {
      method: "post",
      url: `${api.batchDeleteJournal}`,
      params
    };
  },
  // 修改日记账基本信息
  updateJournal(params) {
    return {
      method: "post",
      url: `${api.updateJournal}`,
      params
    };
  },
  // 日记账列表、查询可变维列表
  getShowDims() {
    return {
      method: "get",
      url: `${api.getShowDims}`
    };
  },
  // 修改日记账内容
  updateJournalContent(params) {
    return {
      method: "post",
      url: `${api.updateJournalContent}`,
      params
    };
  },
  // 查询期间的所有成员
  queryDimMembers() {
    return {
      method: "get",
      url: `${api.queryDimMembers}`
    };
  },
  // 日记账查询
  queryJournalContent(params) {
    return {
      method: "post",
      url: `${api.queryJournalContent}`,
      params
    };
  },
  // 批量过账
  postingJournal(params) {
    return {
      method: "post",
      url: `${api.postingJournal}`,
      params
    };
  },
  // 批量取消过账
  cancelPostingJournal(params) {
    return {
      method: "post",
      url: `${api.cancelPostingJournal}`,
      params
    };
  },
  // 复制日记账
  copyJournal(params) {
    return {
      method: "post",
      url: `${api.copyJournal}`,
      params
    };
  },
  // 导出日记账模板
  exportJournalTemplate(params) {
    return {
      method: "post",
      responseType: "blob",
      url: `${api.exportJournalTemplate}`,
      params
    };
  },
  // 批量导出日记账
  exportJournals(params) {
    return {
      method: "post",
      responseType: "blob",
      url: `${api.exportJournals}`,
      params
    };
  },
  // 导入日记账
  importJournals(params) {
    return {
      method: "post",
      url: `${api.importJournals}/${params.get("importType")}`,
      params
    };
  },
  // 下载导入失败清单
  downloadImportResult(taskId) {
    return {
      method: "get",
      responseType: "blob",
      url: `${api.downloadImportResult}?taskId=${taskId}`
    };
  },
  // 查询导入结果
  getTaskResult(taskId) {
    return {
      method: "get",
      url: `${api.getTaskResult}?taskId=${taskId}`
    };
  }, // 查询导入结果
  queryDimMembersById(dimId) {
    return {
      method: "get",
      url: `${api.queryDimMembersById}?dimId=${dimId}`
    };
  },
  // 生成日记账id
  generateJournalId() {
    return {
      method: "get",
      url: `${api.generateJournalId}`
    };
  },
  // 查询维度下拉选项
  expParsingWithFilter(params) {
    return {
      method: "post",
      url: `${api.expParsingWithFilter}`,
      params
    };
  },
  // 针对 审计线索 查询下拉列表
  queryAudittrailMembers(dimCode) {
    return {
      method: "get",
      url: `${api.queryAudittrailMembers}?dimCode=${dimCode}`
    };
  },
  // 查询用户自定义列值
  getUserColumns(type) {
    return {
      method: "get",
      url: `${api.getUserColumns}/${type}`
    };
  },
  // 设置用户自定义列值
  saveOrUpdateUserColumn(params) {
    return {
      method: "post",
      url: api.saveOrUpdateUserColumn,
      params
    };
  },
  // 钻取日记账详情权限校验
  checkUserAuth(journalId) {
    // journalId: 日记账的id
    return {
      method: "get",
      url: `${api.checkUserAuth}/${journalId}`
    };
  },

  /**
   * 获取持续年列表
   * @param {string} fromYearId 页面维中当前年成员id
   * @returns
   */
  getContinuedYearList(fromYearId) {
    return {
      method: "get",
      url: `${api.getContinuedYearList}/?fromYearId=${fromYearId}`
    };
  },

  /**
   * 校验特殊科目成员410499
   * @returns
   */
  checkSpecialAccount(params) {
    return {
      method: "post",
      url: `${api.checkSpecialAccount}`,
      params
    };
  },
  /**
   * 删除日记账附件
   * @param {Object} params
   * @returns
   */
  deleteJournalFilesRelation(params) {
    return {
      method: "post",
      url: `${api.deleteJournalFilesRelation}`,
      params
    };
  },

  /**
   * 查询日记账附件列表
   * @param {*} params
   * @returns
   */
  getJournalFiles(params) {
    return {
      method: "post",
      url: `${api.getJournalFiles}`,
      params
    };
  },

  /**
   * 上传日记账附件
   * @param {*} params
   * @returns
   */
  journalFilesRelation(params) {
    return {
      method: "post",
      url: `${api.journalFilesRelation}`,
      params
    };
  }
};

/**
 * @desc 日记账相关请求处理
 * @param {string} method
 * @param {object} params
 */
const journalService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default journalService;
