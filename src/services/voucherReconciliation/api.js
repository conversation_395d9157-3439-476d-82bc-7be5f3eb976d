import { BACKEND } from "../../config/SETUP";
const baseUrl = BACKEND.BASE_URL;
// const ecsConsoleBaseUrl = BACKEND.ECS_CONSOLE_BASE_URL;
// const ecsConsoleUrl = BACKEND.ECS_CONSOLE_URL;

const voucherApi = {
  // 新增对账报表
  saveVoucherReconciliation: `${baseUrl}/voucherReconciliation/saveVoucherReconciliation`,
  // 获取对账报表列表
  voucherReconciliationList: `${baseUrl}/voucherReconciliation/voucherReconciliationList`,
  // 查看对账报表
  queryVoucherReconciliation: `${baseUrl}/voucherReconciliation/queryVoucherReconciliation`,
  // 删除对账报表
  deleteVoucherReconciliation: `${baseUrl}/voucherReconciliation/deleteVoucherReconciliation`,
  // 查看对账报表配置
  queryVoucherReconciliationConf: `${baseUrl}/voucherReconciliation/queryVoucherReconciliationConf`,
  // 保存对账报表配置
  saveVoucherReconciliationConf: `${baseUrl}/voucherReconciliation/saveVoucherReconciliationConf`,
  // 保存对账报表配置
  dragMove: `${baseUrl}/voucherReconciliation/dragMove`,

  // 保存对账字段设置
  saveField: `${baseUrl}/voucherReconciliation/saveField`,
  // 查询对账字段设置
  queryField: `${baseUrl}/voucherReconciliation/queryField`,

  // 保存对账规则设置
  saveRule: `${baseUrl}/voucherReconciliation/saveRule`,
  // 查询对账规则设置详情
  queryRule: `${baseUrl}/voucherReconciliation/queryRule`,
  // 分页查询对账字段设置表
  ruleListPageable: `${baseUrl}/voucherReconciliation/ruleListPageable`,
  // 获取匹配字段下拉列表
  fieldList: `${baseUrl}/voucherReconciliation/fieldList`,
  // 删除对账规则设置
  deleteRule: `${baseUrl}/voucherReconciliation/deleteRule`,
  // 获取规则下拉
  ruleList: `${baseUrl}/voucherReconciliation/ruleList`,

  // 生成对账报表
  generateReport: `${baseUrl}/voucherReconciliation/generateReport`,
  // 根据父节点展开子项（GET）
  expandChildren: `${baseUrl}/voucherReconciliation/expandChildren`,
  // 全部展开（GET）
  expandAll: `${baseUrl}/voucherReconciliation/expandAll`,
  // 获取每一列的下拉搜索列表（GET）
  columnSearchList: `${baseUrl}/voucherReconciliation/columnSearchList`,
  // 根据小数位数刷新报表（GET）
  refresh: `${baseUrl}/voucherReconciliation/refresh`,
  // 根据列成员搜索（POST）
  searchReport: `${baseUrl}/voucherReconciliation/searchReport`,
  // 根据匹配标识查找节点id(GET)
  nodeListByMatchFlag: `${baseUrl}/voucherReconciliation/nodeListByMatchFlag`,
  // 匹配（POST）
  batchDoMatch: `${baseUrl}/voucherReconciliation/batchDoMatch`,
  // 撤销匹配（POST）
  batchUnDoMatch: `${baseUrl}/voucherReconciliation/batchUnDoMatch`,
  // 导出（GET）
  export: `${baseUrl}/voucherReconciliation/export`,
  // 获取 指定科目下拉树 数据
  getAccount: `${baseUrl}/reconciliation/getAccount`
};
export default voucherApi;
