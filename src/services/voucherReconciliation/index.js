import { fetch } from "../baseService";
import api from "./api";
import { getSafetyData } from "@/utils/common";
/**
 * @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      if (res.status === 200 && typeof res.data === "object") {
        res.data.items = getSafetyData(res.data, "items", []);
      }
      // if (typeof res.data === "string") {
      //   res.data = {};
      // }
      // res.data.items = getSafetyData(res.data, "items", []);
    }
    return res;
  },
  post: res => res
};
const typeMapConfig = {
  saveData(params) {
    return {
      method: "post",
      url: `${api.saveData}`,
      params
    };
  },
  getCubes() {
    return {
      method: "get",
      url: `${api.getCubes}`
    };
  },
  getData() {
    return {
      method: "get",
      url: `${api.getData}`
    };
  },
  getMembersTree(dimId) {
    return {
      method: "get",
      url: `${api.getMembersTree}?dimId=${dimId}`
    };
  },
  getDataByCube(cubeCode) {
    return {
      method: "get",
      url: `${api.getDataByCube}?cubeCode=${cubeCode}`
    };
  },
  // 保存对账规则设置
  saveRule(params) {
    return {
      method: "post",
      url: `${api.saveRule}`,
      params
    };
  },
  // 查询对账规则设置详情
  queryRule(id) {
    return {
      method: "get",
      url: `${api.queryRule}/${id}`
    };
  },
  // 分页查询对账字段设置表
  ruleListPageable(params) {
    return {
      method: "get",
      url: `${api.ruleListPageable}?pageSize=${params.pageSize}&pageNum=${params.pageNum}&searchRuleName=${params.searchRuleName}`
    };
  },
  // 获取匹配字段下拉列表
  fieldList(params) {
    return {
      method: "get",
      url: `${api.fieldList}`
    };
  },
  // 删除对账规则设置
  deleteRule(params) {
    return {
      method: "post",
      url: `${api.deleteRule}`,
      params
    };
  },
  // 保存对账字段设置
  saveField(params) {
    return {
      method: "post",
      url: `${api.saveField}`,
      params
    };
  },
  // 查询对账字段设置
  queryField() {
    return {
      method: "get",
      url: `${api.queryField}`
    };
  },
  // 获取对账报表列表
  getListReconciliation() {
    return {
      method: "get",
      url: `${api.voucherReconciliationList}`
    };
  },
  // 新增对账报表
  saveReconciliation(params) {
    return {
      method: "post",
      url: `${api.saveVoucherReconciliation}`,
      params
    };
  },
  // 编辑对账报表
  editReconciliation(params) {
    return {
      method: "post",
      url: `${api.saveVoucherReconciliation}`,
      params
    };
  },
  // 查看对账报表信息
  getReconciliationById(id) {
    return {
      method: "get",
      url: `${api.queryVoucherReconciliation}/${id}`
    };
  },
  // 另存为对账报表配置
  saveAsReconciliationConfig(params) {
    return {
      method: "post",
      url: `${api.saveVoucherReconciliation}`,
      params
    };
  },
  // 删除对账报表
  deleteReconciliation(id) {
    return {
      method: "get",
      url: `${api.deleteVoucherReconciliation}/${id}`
    };
  },
  // 查询报表查询信息
  getReconciliationConfigById(id) {
    return {
      method: "get",
      url: `${api.queryVoucherReconciliationConf}/${id}`
    };
  },
  // 保存对账报表配置
  saveReconciliationConfig(params) {
    return {
      method: "post",
      url: `${api.saveVoucherReconciliationConf}`,
      params
    };
  },
  // 拖动
  dragMove(params) {
    const { sourceId, targetId, moveType } = params;
    return {
      method: "get",
      url: `${api.dragMove}?sourceId=${sourceId}&targetId=${targetId}&moveType=${moveType}`,
      params
    };
  },
  // 获取规则下拉
  ruleList(id) {
    return {
      method: "get",
      url: `${api.ruleList}`
    };
  },
  getMemberByDimCode(dimCode) {
    return {
      method: "get",
      url: `${api.getMemberByDimCode}/${dimCode}`
    };
  },
  cateGoryDimMembersTree() {
    return {
      method: "get",
      url: `${api.cateGoryDimMembersTree}`
    };
  },
  getCustomDim() {
    return {
      method: "get",
      url: `${api.getCustomDim}`
    };
  },
  getRA() {
    return {
      method: "get",
      url: `${api.getRA}`
    };
  },
  asyncGenerateReport(params) {
    return {
      method: "post",
      url: `${api.asyncGenerateReport}`,
      params
    };
  },
  getPublicAuth(params) {
    return {
      method: "get",
      url: `${api.getPublicAuth}`
    };
  },
  getReconciliationByTaskId({ taskId, limit, offset }) {
    return {
      method: "get",
      url: `${api.getReconciliationByTaskId}?taskId=${taskId}&limit=${limit}&offset=${offset}`
    };
  },
  userMemory(params) {
    const { type, ...requestParams } = params;
    const tempObj = {
      method: type === "save" ? "post" : "get",
      url: `${api.userMemory}`
    };
    if (type === "save") {
      tempObj.params = requestParams;
    }
    return {
      ...tempObj
    };
  },
  reconciliationRefresh(params) {
    return {
      method: "post",
      url: `${api.reconciliationRefresh}`,
      params
    };
  },
  reconciliationExportFile(params) {
    return {
      method: "post",
      responseType: "blob",
      url: `${api.reconciliationExportFile}`,
      params
    };
  },
  journalCheckParam(params) {
    return {
      method: "post",
      url: `${api.journalCheckParam}`,
      params
    };
  },
  checkJournalTemplateExist(params) {
    return {
      method: "post",
      url: `${api.checkJournalTemplateExist}`,
      params
    };
  },
  entityAuth(id) {
    return {
      method: "get",
      url: `${api.entityAuth}/${id}`
    };
  },
  reconciliationDrillDim() {
    return {
      method: "get",
      url: `${api.reconciliationDrillDim}`
    };
  },
  getFormRelationCell(params) {
    return {
      method: "post",
      url: `${api.getFormRelationCell}`,
      params
    };
  },

  // 生成对账报表
  generateReport(params) {
    return {
      method: "post",
      url: `${api.generateReport}`,
      params
    };
  },
  // 根据父节点展开子项（GET）
  expandChildren(params) {
    return {
      method: "post",
      params,
      url: `${api.expandChildren}`
    };
  },
  // 全部展开（GET）
  expandAll(params) {
    const { reportId, offset, limit } = params;
    return {
      method: "get",
      url: `${api.expandAll}?reportId=${reportId}&offset=${offset}&limit=${limit}`
    };
  },
  // 获取每一列的下拉搜索列表（GET）
  columnSearchList(params) {
    const { reportId, columnName, matchFlag } = params;
    return {
      method: "get",
      url: `${api.columnSearchList}?reportId=${reportId}&matchFlag=${matchFlag}&columnName=${columnName}`
    };
  },
  // 根据小数位数刷新报表（GET）
  refresh(params) {
    const { refreshId, reportId, decimalPlace } = params;
    return {
      method: "get",
      url: `${api.refresh}?refreshId=${refreshId}&reportId=${reportId}&decimalPlaces=${decimalPlace}`
    };
  },
  getAccount() {
    return {
      method: "get",
      url: `${api.getAccount}`
    };
  },
  // 根据列成员搜索（POST）
  searchReport(params) {
    return {
      method: "post",
      url: `${api.searchReport}`,
      params
    };
  },
  // 根据匹配标识查找节点id(GET)
  nodeListByMatchFlag(params) {
    const { reportId, matchFlag, offset, limit } = params;
    return {
      method: "get",
      url: `${api.nodeListByMatchFlag}?reportId=${reportId}&matchFlag=${matchFlag}&offset=${offset}&limit=${limit}`
    };
  },
  // 匹配（POST）
  batchDoMatch(params) {
    return {
      method: "post",
      url: `${api.batchDoMatch}`,
      params
    };
  },
  // 撤销匹配（POST）
  batchUnDoMatch(params) {
    return {
      method: "post",
      url: `${api.batchUnDoMatch}`,
      params
    };
  },
  // 导出（GET）
  export(params) {
    const { reportId, fileType, memberShowType } = params;
    return {
      method: "get",
      responseType: "blob",
      url: `${api.export}?reportId=${reportId}&fileType=${fileType}&memberShowType=${memberShowType}`
    };
  }
};

/**
 * @desc 报账相关请求处理
 * @param {string} method
 * @param {object} params
 */
const voucherReconciliation = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default voucherReconciliation;
