import { fetch } from "../baseService";
import api from "../api";
import { getSafetyData } from "@/utils/common";
/**
 * @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      if (res.status === 200 && typeof res.data === "object") {
        res.data.items = getSafetyData(res.data, "items", []);
      }
    }
    return res;
  },
  post: res => res
};
const typeMapConfig = {
  saveData(params) {
    return {
      method: "post",
      url: `${api.ruleSetMaintenanceSave}`,
      params
    };
  },
  editRuleSet(params) {
    return {
      method: "post",
      url: `${api.editRuleSet}`,
      params
    };
  },
  getDetail(id) {
    return {
      method: "get",
      url: `${api.getRuleSetDetail}/${id}`
    };
  },
  getTableData(obj) {
    const { pageSize = 10, pageNum } = obj;
    return {
      method: "get",
      url: `${api.getTableData}?pageSize=${pageSize}&pageNum=${pageNum}&totalResults=true`
    };
  },
  searchTableData(params) {
    return {
      method: "post",
      url: `${api.searchTableData}`,
      params
    };
  },
  deleteRuleSet(params) {
    return {
      method: "post",
      url: `${api.deleteRuleSet}`,
      params
    };
  }
};

/**
 * @desc 报账相关请求处理
 * @param {string} method
 * @param {object} params
 */
const ruleSetMaintenanceService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default ruleSetMaintenanceService;
