import { fetch } from "../baseService";
import api from "../api";
import { getSafetyData } from "@/utils/common";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
/**
 * @desc 根据rest处理不同请求方式，使用getSafetyData来获取数据而不是直接res.data.items，保证数据安全可靠
 */
const handleCore = {
  get: res => {
    if (res.status === 200) {
      if (res.status === 200 && typeof res.data === "object") {
        res.data.items = getSafetyData(res.data, "items", []);
      }
      // if (typeof res.data === "string") {
      //   res.data = {};
      // }
      // res.data.items = getSafetyData(res.data, "items", []);
    }
    return res;
  },
  post: res => res
};
const typeMapConfig = {
  saveData(params) {
    return {
      method: "post",
      url: `${api.saveData}`,
      params
    };
  },
  getCubes() {
    return {
      method: "get",
      url: `${api.getCubes}`
    };
  },
  getData() {
    return {
      method: "get",
      url: `${api.getData}`
    };
  },
  getMembersTree(dimId) {
    return {
      method: "get",
      url: `${api.getMembersTree}?dimId=${dimId}`
    };
  },
  getDataByCube(cubeCode) {
    return {
      method: "get",
      url: `${api.getDataByCube}?cubeCode=${cubeCode}`
    };
  },
  getListReconciliation() {
    return {
      method: "get",
      url: `${api.getListReconciliation}`
    };
  },
  saveReconciliation(params) {
    return {
      method: "post",
      url: `${api.saveReconciliation}`,
      params
    };
  },
  editReconciliation(params) {
    return {
      method: "post",
      url: `${api.editReconciliation}`,
      params
    };
  },
  deleteReconciliation(id) {
    return {
      method: "get",
      url: `${api.deleteReconciliation}/${id}`
    };
  },
  getReconciliationById(id) {
    return {
      method: "get",
      url: `${api.getReconciliationById}/${id}`
    };
  },
  getMemberByDimCode(dimCode) {
    return {
      method: "get",
      url: `${api.getMemberByDimCode}/${dimCode}`
    };
  },
  // 类别维度获取下拉列表
  cateGoryDimMembersTree() {
    return {
      method: "get",
      url: `${api.cateGoryDimMembersTree}`
    };
  },
  getReconciliationConfigById(id) {
    return {
      method: "get",
      url: `${api.getReconciliationConfigById}/${id}`
    };
  },
  getCustomDim() {
    return {
      method: "get",
      url: `${api.getCustomDim}`
    };
  },
  getAccount() {
    return {
      method: "get",
      url: `${api.getAccountByAttr}`
    };
  },
  getRA() {
    return {
      method: "get",
      url: `${api.getRA}`
    };
  },
  saveReconciliationConfig(params) {
    return {
      method: "post",
      url: `${api.saveReconciliationConfig}`,
      params
    };
  },
  saveAsReconciliationConfig(params) {
    return {
      method: "post",
      url: `${api.saveAsReconciliationConfig}`,
      params
    };
  },
  generateReport(params) {
    return {
      method: "post",
      url: `${api.generateReport}`,
      params
    };
  },
  asyncGenerateReport(params) {
    return {
      method: "post",
      url: `${api.asyncGenerateReport}`,
      params
    };
  },
  dragMove(params) {
    const { sourceId, targetId, moveType } = params;
    return {
      method: "get",
      url: `${api.dragMove}?sourceId=${sourceId}&targetId=${targetId}&moveType=${moveType}`,
      params
    };
  },
  getPublicAuth(params) {
    return {
      method: "get",
      url: `${api.getPublicAuth}`
    };
  },
  getReconciliationByTaskId({ taskId, limit, offset }) {
    return {
      method: "get",
      url: `${api.getReconciliationByTaskId}?taskId=${taskId}&limit=${limit}&offset=${offset}`
    };
  },
  expandChildren({ taskId, limit, offset, parentId }) {
    return {
      method: "get",
      url: `${api.expandChildren}?taskId=${taskId}&offset=${offset}&limit=${limit}&parentId=${parentId}`
    };
  },
  columnSearchList({ taskId, columnName }) {
    return {
      method: "get",
      url: `${api.columnSearchList}?taskId=${taskId}&columnName=${columnName}`
    };
  },
  searchReport(params) {
    return {
      method: "post",
      url: `${api.searchReport}`,
      params
    };
  },
  userMemory(params) {
    const { type, ...requestParams } = params;
    const tempObj = {
      method: type === "save" ? "post" : "get",
      url: `${api.userMemory}`
    };
    if (type === "save") {
      tempObj.params = requestParams;
    }
    return {
      ...tempObj
    };
  },
  reconciliationRefresh(params) {
    return {
      method: "post",
      url: `${api.reconciliationRefresh}`,
      params
    };
  },
  reconciliationExportFile(params) {
    return {
      method: "post",
      responseType: "blob",
      url: `${api.reconciliationExportFile}`,
      params
    };
  },
  expandAll(params) {
    const { taskId, offset, limit } = params;
    return {
      method: "get",
      url: `${api.expandAll}?taskId=${taskId}&offset=${offset}&limit=${limit}`
    };
  },
  journalCheckParam(params) {
    return {
      method: "post",
      url: `${api.journalCheckParam}`,
      params
    };
  },
  checkJournalTemplateExist(params) {
    return {
      method: "post",
      url: `${api.checkJournalTemplateExist}`,
      params
    };
  },
  entityAuth(id) {
    return {
      method: "get",
      url: `${api.entityAuth}/${id}`
    };
  },
  reconciliationDrillDim() {
    return {
      method: "get",
      url: `${api.reconciliationDrillDim}`
    };
  },
  getFormRelationCell(params) {
    return {
      method: "post",
      url: `${api.getFormRelationCell}`,
      params
    };
  },
  // 获取 余额对账报表 功能权限
  getBalanceReconciliationAuth: function(params) {
    const { refId } = params;
    const resourceType = "MetadataObject";
    const bizAppId = UrlUtils.getQuery("appId");
    const authCode = [
      "balanceReconciliation-read",
      "balanceReconciliation-add",
      "balanceReconciliation-edit",
      "balanceReconciliation-delete",
      "balanceReconciliation-edit-config",
      "balanceReconciliation-email-caution",
      "balanceReconciliation-voucher-drilling",
      "balanceReconciliation-adjustmentVoucher",
      "balanceReconciliation-reconciliation-synergy"
    ];
    return {
      method: "get",
      iscode: false,
      url: `${
        api.getEquityAuth
      }?query=refId='${refId}' and resourceType='${resourceType}' and privilegeCode in (${authCode.join(
        ","
      )}) and bizAppId='${bizAppId}'`
    };
  },
  // 获取 凭证对账报表 功能权限
  getVoucherReconciliationAuth: function(params) {
    const { refId } = params;
    const resourceType = "MetadataObject";
    const bizAppId = UrlUtils.getQuery("appId");
    const authCode = [
      "voucherReconciliation-read",
      "voucherReconciliation-add",
      "voucherReconciliation-edit",
      "voucherReconciliation-delete",
      "voucherReconciliation-edit-config",
      "voucherReconciliation-manual-match"
    ];
    return {
      method: "get",
      iscode: false,
      url: `${
        api.getEquityAuth
      }?query=refId='${refId}' and resourceType='${resourceType}' and privilegeCode in (${authCode.join(
        ","
      )}) and bizAppId='${bizAppId}'`
    };
  },
  /**
   * 获取凭证对账报表 公有报表 权限
   * @returns
   */
  getVoucherPublicAuth() {
    return {
      method: "get",
      url: `${api.getVoucherPublicAuth}`
    };
  },
  // 查询对账协同状态设置
  getReconciliationCoordinationSetting() {
    return {
      method: "get",
      url: `${api.getReconciliationCoordinationSetting}`
    };
  },
  /**
   * 保存对账协同状态设置
   * @param { Object } params { "id": "", "name": ""}
   * @returns
   */
  saveReconciliationCoordinationSetting(params) {
    return {
      method: "post",
      url: `${api.saveReconciliationCoordinationSetting}`,
      params
    };
  },
  // 是否有对账协同权限
  deleteStatementCoordination(id) {
    return {
      method: "get",
      url: `${api.deleteStatementCoordination}/${id}`
    };
  },
  // 是否有对账协同权限
  hasReconciliationCoordinationAuth() {
    return {
      method: "get",
      url: `${api.hasReconciliationCoordinationAuth}`
    };
  },
  // 调整对账状态
  /**
   *
   * @param {Object} params
   * @example
   * updateCoordinationStatus({
        "taskId": "", //对账任务id
        "reconciliationId": "",//对账报表的id
        "statusType": "", //组织方对账状态:entityReconciliationStatus, 往来方对账状态:icpReconciliationStatus;
        "statusId": "", //选择的对账状态字段的id
        "nodeIds": [
          "" //选择的小计行id 集合
        ]
      })
   * @returns
   */
  updateCoordinationStatus(params) {
    return {
      method: "post",
      url: `${api.updateCoordinationStatus}`,
      params
    };
  }
};

/**
 * @desc 报账相关请求处理
 * @param {string} method
 * @param {object} params
 */
const reconciliationService = (type, params) => {
  const config = typeMapConfig[type](params);
  const { method } = config;
  return fetch(api, method, params, config || null).then(res =>
    handleCore[method] ? handleCore[method](res) : res
  );
};
export default reconciliationService;
