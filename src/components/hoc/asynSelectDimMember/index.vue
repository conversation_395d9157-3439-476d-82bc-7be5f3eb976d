<template>
  <yn-select-tree
    ref="selector"
    v-bind="$attrs"
    searchMode="custom"
    loadMoreChildrenIndicator="hasMore"
    :value="currValue"
    :allowClear="false"
    :datasource="dataSource"
    :nonleafselectable="leafselectable"
    :allRootLoaded="allRootLoaded"
    :loadMoreData="loadMoreData"
    :customloader="customloader"
    :itemexpandable="checkExpandable"
    :selectedItems="selectedItems"
    :treeExpandedKeys="treeExpandedKeys"
    @customSearch="onCustomeSearch"
    @dropdownVisibleChange="close"
    @change="onChange"
    @treeExpand="treeExpand"
    v-on="$listeners"
  />
</template>
<script>
import "yn-p1/libs/components/yn-select-tree";
import commonService from "@/services/common";
/**
 * 钩子:
 *  loadComplete 加载完第一页根节点信息后的回调
 * props:
 *  dimCode
 *  isSetDefaultVal  //  当传入的value ，不存在时，根据此属性选中第一个叶子节点或者第一个节点
 *  customSearch:自定义搜索
 *  customLoadChildren：自定义加载更多
 *  limit：一页显示 多少条 数据
 * 对外提供的方法:
 *  changeVal   再触发change以后 emit 调用changeVal 事件，将选中的值传出去
 *
 */
export default {
  name: "AsynSelectDimMember",
  props: {
    dimCode: {
      type: String,
      default: ""
    },
    checkTabaseType: {
      // 开启数值存储校验
      type: Boolean,
      default: true
    },
    memberExp: {
      type: Object,
      default: null
    },
    customSearch: {
      type: Function,
      require: false,
      default: null
    },
    customLoadChildren: {
      type: Function,
      require: false,
      default: null
    },
    loadComplete: {
      type: Function,
      require: false,
      default: null
    },
    formatter: {
      // 对 key 进行自定义格式化
      type: [Function],
      default(item) {
        return item.objectId;
      }
    },
    formatterLabel: {
      // 对 label 进行自定义格式化
      type: [Function],
      default(item) {
        return item.label || item.dimMemberRealName;
      }
    },
    runChangeVal: {
      // 有默认值的时候首次是否执行 changeVal 事件。
      type: Boolean,
      default: true
    },
    // 组件外部请求的数据信息 {data:'对应dataSource属性',hasMore:"对应allRootLoaded"}
    dataInfo: {
      type: Object,
      require: false,
      default: null
      // validator(obj) {
      //   if (obj) {
      //     const { hasMore, data } = obj;
      //     return typeof hasMore === "boolean" && Array.isArray(data);
      //   }
      //   return true;
      // }
    },
    unformatter: {
      type: [Function],
      default(key) {
        // 对 key 进行反解格式化
        return key;
      }
    },
    dropdownVisibleChange: {
      type: Function,
      require: false,
      default: null
    },
    limit: {
      type: Number,
      default: () => 10
    },
    needPermission: {
      // 是否开启权限
      type: [Boolean],
      default: false
    },
    needFilterShared: {
      // 是否过滤副本
      type: Boolean,
      default: false
    },
    value: {
      type: [String, Array],
      default: ""
    },
    isSetDefaultVal: {
      type: Boolean,
      default: false
    },
    pageName: {
      // 模块使用标识，用于特殊过滤。如： 股权下拉过滤。
      type: String,
      default: undefined
    },
    needFilterAudittrail: {
      type: Boolean,
      default: false
    },
    beforeChange: {
      require: false,
      type: Function,
      default: null
    },
    setEmpty: {
      require: false,
      type: Function,
      default: null
    },
    hasAllMembers: {
      type: Boolean,
      require: false,
      default: false
    }
  },
  data() {
    return {
      isFirstSetVal: true,
      dataSource: [],
      cacheRootLoadNodeNum: 0,
      cacheAllRootLoaded: false, // 第一次根节点状态
      cacheDataSource: [],
      allRootLoaded: false,
      selectTreeValues: [],
      selectedItems: [],
      isManualSetup: false, // 手动设置 标识
      searchValue: "",
      treeExpandedKeys: []
    };
  },
  computed: {
    leafselectable() {
      const { nonleafselectable } = this.$attrs;
      return this.checkTabaseType ? true : nonleafselectable;
    },
    currValue() {
      const { selectTreeValues, $attrs } = this;
      const { multiple } = $attrs;
      return multiple
        ? selectTreeValues
        : typeof selectTreeValues === "string"
          ? selectTreeValues
          : selectTreeValues[0];
    }
  },
  watch: {
    selectedItems: {
      handler(newVal) {
        this.$emit("selectedItems", newVal);
      },
      deep: true,
      immediate: true
    },
    dataInfo: {
      handler(newVal) {
        if (this.isEmpty(newVal)) return;
        this.init();
      },
      immediate: true
    },
    memberExp: {
      handler(newVal) {
        if (this.isEmpty(newVal) || this.dataInfo) return;
        this.init();
      },
      immediate: true
    },
    dimCode: {
      handler(newVal) {
        if (!newVal || this.memberExp || this.dataInfo) return;
        this.init();
      },
      immediate: true
    },
    value: {
      handler(newVal, oldVal) {
        // 防止重复watch
        if (this.isManualSetup) {
          this.isManualSetup = false;
          return;
        }
        this.setDefault(newVal);
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async refreshData() {
      await this.init();
      const defaultValue = this.currValue;
      // 将当前值 作为默认值 选中
      await this.setDefault(defaultValue);
      //  将当前 选中的成员 信息返回
      return this.currValue;
    },
    async init(cbFn) {
      // 根据dimCode 查询第一层节点（root） 根据返回的信息判断根节点是否有分页 allRootLoaded
      const {
        needPermission,
        needFilterAudittrail,
        limit,
        memberExp,
        dimCode,
        needFilterShared,
        pageName,
        hasAllMembers
      } = this;
      //  组件外部已经将第一页的根节点数据处理好
      if (this.dataInfo) {
        const { hasMore, limit } = this.dataInfo;
        this.allRootLoaded = !hasMore;
        this.cacheAllRootLoaded = !hasMore;
        this.cacheRootLoadNodeNum = limit;
        const formatData = this.formatData(this.dataInfo);
        this.dataSource = formatData.data;
        this.cacheDataSource = this.dataSource;
        cbFn && cbFn();
        return;
      }
      await this.requestDataByParams({
        params: {
          offset: 0,
          limit,
          needPermission,
          needFilterAudittrail,
          needFilterShared,
          memberExp,
          dimCode,
          pageName,
          hasAllMembers
        },
        type: "getFirstDimMemberList", // 根节点
        cb: res => {
          const { hasMore } = res;
          this.allRootLoaded = !hasMore;
          this.cacheRootLoadNodeNum = limit;
          this.cacheAllRootLoaded = !hasMore;
          const formatData = this.formatData(res);
          const treeData = formatData.data || [];
          this.dataSource = treeData;
          this.cacheDataSource = this.dataSource;
          cbFn && cbFn();
          this.loadComplete && this.loadComplete();
        }
      });
    },
    // 获取节点数据根节点或者非根节点数据
    async requestDataByParams(requestParams) {
      const { params, cb, type } = requestParams;
      await commonService(type, params).then(res => {
        cb && cb(res.data);
      });
    },
    checkExpandable(item) {
      return !item.isLeaf;
    },
    async customloader(node) {
      if (this.customLoadChildren) {
        return this.customLoadChildren(node);
      } else {
        const {
          limit,
          needPermission,
          needFilterAudittrail,
          needFilterShared,
          memberExp
        } = this;
        let requestResult = {};
        await this.requestDataByParams({
          params: {
            offset: 0,
            limit,
            needPermission,
            needFilterAudittrail,
            needFilterShared,
            memberExp,
            dimMemberId: node.objectId,
            pageName: this.pageName
          },
          type: "getCommonDimMemberList",
          cb: res => {
            requestResult = this.formatData(res, false);
          }
        });
        return Promise.resolve(requestResult);
      }
    },
    isDisable(item) {
      const { nonleafselectable } = this.$attrs;
      const { dimMemberHasChildren, dimMemberTabaseType } = item;
      const isTabaseTypeNumber =
        this.checkTabaseType === true &&
        dimMemberTabaseType === "parent_number";
      const leafselectable = isTabaseTypeNumber ? true : nonleafselectable;
      const isSelectedParent =
        typeof leafselectable === "boolean" ? leafselectable : true;
      const isParent = dimMemberHasChildren === "TRUE";
      return typeof item.disabled === "boolean"
        ? item.disabled
        : isParent
          ? isTabaseTypeNumber
            ? false
            : !isSelectedParent
          : false;
    },
    formatData(requestData, isSearch) {
      const { items: data, offset, hasMore } = requestData;
      const DATALEN = data ? data.length : 0;
      if (!Array.isArray(data) || !DATALEN) return [];
      // 格式化数据
      const treeData = {
        data: [],
        hasMore: hasMore
      };
      // PS:格式化数据，需要给node 添加indexes 索引属性，用于组件加载更多使用
      const { nonleafselectable } = this.$attrs;
      const formatData = [];
      data.forEach((item, index) => {
        const { dimMemberHasChildren, dimMemberTabaseType } = item;
        const isTabaseTypeNumber =
          this.checkTabaseType === true &&
          dimMemberTabaseType === "parent_number";
        const leafselectable = isTabaseTypeNumber ? true : nonleafselectable;
        const isSelectedParent =
          typeof leafselectable === "boolean" ? leafselectable : true;
        const tempObj = this.getNode({
          ...item,
          indexes: offset + index,
          key: this.formatter(item),
          isLeaf: isSearch ? true : item.dimMemberHasChildren === "FALSE"
        });
        if (index === DATALEN - 1) {
          tempObj.hasMore = hasMore;
        }
        formatData.push(tempObj);
        // 搜索时，组件配置父项不能选中，将父项删除掉
        if (
          !isTabaseTypeNumber &&
          !isSelectedParent &&
          dimMemberHasChildren === "TRUE" &&
          isSearch
        ) {
          formatData.pop();
        }
      });
      treeData.data = formatData;
      return treeData;
    },
    formatSearchResult(requestData) {
      const { items } = requestData;
      const loop = data => {
        data.forEach(treeNode => {
          const {
            children,
            dimMemberRealName,
            // objectId,
            label,
            dimMemberHasChildren,
            dbCodeIndex
          } = treeNode;
          treeNode.key = this.formatter(treeNode);
          treeNode.label = this.formatterLabel({
            label,
            dimMemberRealName,
            dbCodeIndex
          });
          treeNode.disabled = this.isDisable(treeNode);
          treeNode.isLeaf = dimMemberHasChildren !== "TRUE";
          if (
            dimMemberHasChildren === "TRUE" &&
            children &&
            children.length > 0
          ) {
            loop(children);
          }
        });
      };
      loop(items);
      return items;
    },
    // 模拟class 获取节点对象，统一选中节点，树节点属性
    getNode({
      dimId,
      dimCode,
      disabled,
      dimMemberCode,
      objectId,
      dimMemberHasChildren,
      dimMemberRealName,
      dimMemberTabaseType,
      indexes,
      key,
      isLeaf,
      value,
      label,
      dbCodeIndex
    }) {
      const { nonleafselectable } = this.$attrs;
      const isTabaseTypeNumber =
        this.checkTabaseType === true &&
        dimMemberTabaseType === "parent_number";
      const leafselectable = isTabaseTypeNumber ? true : nonleafselectable;
      const isSelectedParent =
        typeof leafselectable === "boolean" ? leafselectable : true;
      const isParent = dimMemberHasChildren === "TRUE";
      return {
        dimId,
        dbCodeIndex,
        dimCode,
        dimMemberCode,
        objectId,
        dimMemberHasChildren,
        dimMemberTabaseType,
        dimMemberRealName,
        indexes,
        disabled:
          typeof disabled === "boolean"
            ? disabled
            : isParent
              ? isTabaseTypeNumber
                ? false
                : !isSelectedParent
              : false,
        key,
        value: value || key,
        isLeaf:
          typeof isLeaf === "boolean"
            ? isLeaf
            : dimMemberHasChildren === "FALSE",
        label: this.formatterLabel({ label, dimMemberRealName, dbCodeIndex })
      };
    },
    async loadMoreData(node) {
      const currTreeNode = node || this.dataSource.slice(-1)[0];
      const { dimCode, objectId } = currTreeNode;
      const {
        limit,
        needPermission,
        needFilterAudittrail,
        needFilterShared,
        cacheRootLoadNodeNum,
        memberExp
      } = this;
      // 分页加载 node 判断走那个接口，node为空 走获取第一层级的接口，有值 走根据dimMemberId 获取子项的
      let requestResult = {};
      const requestParams = {
        type: !node ? "getFirstDimMemberList" : "getCommonDimMemberList",
        params: {
          offset: !node ? cacheRootLoadNodeNum : node.children.length,
          limit,
          needPermission,
          needFilterAudittrail,
          needFilterShared,
          dimCode,
          memberExp,
          dimMemberId: objectId,
          pageName: this.pageName
        },
        cb: res => {
          requestResult = this.formatData(res);
          // 控制根节点加载更多 需要动态设置 allRootLoaded = !hasMore;
          !node && (this.allRootLoaded = !requestResult.hasMore);
        }
      };
      if (!node) {
        this.cacheRootLoadNodeNum = cacheRootLoadNodeNum + limit;
        delete requestParams.params.dimMemberId;
      } else {
        delete requestParams.params.dimCode;
      }
      await this.requestDataByParams(requestParams);
      return Promise.resolve(requestResult);
    },
    close(open) {
      if (!open) {
        this.treeExpandedKeys = [];
        this.dataSource = JSON.parse(JSON.stringify(this.cacheDataSource));
        this.cacheRootLoadNodeNum = this.limit;
        this.allRootLoaded = this.cacheAllRootLoaded;
        this.searchValue = "";
        this.dropdownVisibleChange &&
          this.dropdownVisibleChange({ dimCode: this.dimCode });
      }
    },
    onCustomeSearch({ searchValue } = {}) {
      this.$refs.selector.setMenuLoading(true);
      this.searchValue = searchValue;
      if (!searchValue) {
        this.treeExpandedKeys = [];
        this.init(() => {
          this.$refs.selector.setMenuLoading(false);
        });
        return;
      }
      if (this.customSearch) {
        this.customSearch(searchValue).then(res => {
          const data = this.formatSearchResult(res.data);
          this.allRootLoaded = true;
          this.dataSource = data;
          const pIds = this.getParentId(data);
          this.treeExpandedKeys = pIds;
          this.$refs.selector.setMenuLoading(false);
        });
      } else {
        const {
          limit,
          needPermission,
          needFilterAudittrail,
          needFilterShared,
          dimCode,
          memberExp,
          pageName,
          hasAllMembers
        } = this;
        this.searchByParams(
          {
            offset: 0,
            limit,
            needPermission,
            needFilterAudittrail,
            needFilterShared,
            keyWord: searchValue,
            dimCode,
            memberExp,
            pageName,
            hasAllMembers
          },
          res => {
            // const { data, hasMore } = this.formatData(res, true);
            const data = this.formatSearchResult(res);
            // this.allRootLoaded = !hasMore;
            this.allRootLoaded = true;
            this.dataSource = data;
            const pIds = this.getParentId(data);
            this.treeExpandedKeys = pIds;
            this.$refs.selector.setMenuLoading(false);
          }
        ).finally(() => {
          this.$refs.selector.setMenuLoading(false);
        });
      }
    },
    getParentId(data) {
      const tempArr = [...data];
      const pIds = [];
      while (tempArr.length) {
        const currNode = tempArr.shift();
        const { children, key } = currNode;
        if (children && children.length > 0) {
          Array.prototype.unshift.apply(tempArr, children);
          pIds.push(key);
        }
      }
      return pIds;
    },
    async searchByParams(searchInfo, cb) {
      return await commonService("getQueryDimMemberList", searchInfo).then(
        res => {
          cb && cb(res.data);
          return res;
        }
      );
    },
    // 设置默认值
    async setDefault(defaultValue) {
      // 判断是否存在默认选中的值，不存在根据 isSetDefaultVal 选中第一个叶子节点或者第一个节点，或者不处理
      if (typeof defaultValue === "string" && defaultValue) {
        defaultValue = [defaultValue];
      }
      let selectedItemsInfo = await this.getNodeByDimMemberIds(defaultValue);
      // 如果默认值，不存在，并且需要设置默认值
      if (!selectedItemsInfo.length) {
        if (this.isSetDefaultVal) {
          await this.selectDefaultNode();
          selectedItemsInfo = this.selectedItems;
        } else {
          this.setEmpty && (await this.setEmpty());
        }
      }
      // 调用传入的change事件
      const currValue = this.currValue;
      // 当前有值，才执行changeVal事件
      if (currValue && currValue[0]) {
        this.$emit(
          "changeVal",
          currValue,
          selectedItemsInfo,
          this.runChangeVal
        );
      }
    },
    // 获取节点信息根据dimMemberIds
    async getNodeByDimMemberIds(dimMemberIds) {
      const tempArr = [];
      const {
        dimCode,
        needPermission,
        needFilterAudittrail,
        needFilterShared,
        limit,
        memberExp,
        pageName,
        hasAllMembers
      } = this;
      const searchParams = {
        dimCode,
        needPermission,
        needFilterAudittrail,
        needFilterShared,
        limit,
        offset: 0,
        memberExp,
        pageName,
        hasAllMembers
      };
      (dimMemberIds || []).forEach(dimMemberId => {
        if (dimMemberId) {
          searchParams.dimMemberId = this.unformatter(dimMemberId);
          tempArr.push(this.searchByParams(searchParams));
        }
      });
      const slef = this;
      // 设置 值之前删除待选
      this.selectedItems.splice(0, this.selectedItems.length);
      this.selectTreeValues.splice(0, this.selectTreeValues.length);
      const selectedItemsInfo = [];
      await Promise.all(tempArr).then(res => {
        res.forEach(item => {
          const treeNode = item.data.items || [];
          // 如有有一个成员 存在 ，则不需要回显第一个叶子节点
          if (treeNode.length) {
            const value = this.formatter(treeNode[0]);
            const node = this.getNode({
              ...treeNode[0],
              key: value
            });
            slef.selectTreeValues.push(value);
            selectedItemsInfo.push(node);
            slef.selectedItems.push(node);
          }
        });
      });
      return selectedItemsInfo;
    },
    async selectDefaultNode() {
      // 父项不可选，默认 选中第一个叶子节点，父项可选，选中第一个节点
      const isSelectedFirstNode = this.isSelectedFirstNode();
      if (isSelectedFirstNode) {
        // 选中第一个节点
        await this.selectedFirstNode();
      } else {
        await this.selectedFirstLeafNode();
      }
    },
    // 是否选中第一个节点
    isSelectedFirstNode() {
      const { nonleafselectable } = this.$attrs;
      const isSelectedFirsetNode =
        typeof nonleafselectable === "boolean" ? nonleafselectable : true;
      return isSelectedFirsetNode;
    },
    // 选中第一个节点
    async selectedFirstNode() {
      await new Promise(resolve => {
        const loop = () => {
          setTimeout(() => {
            if (this.dataSource.length) {
              const firstNode = this.dataSource[0] || {};
              const value = this.formatter(firstNode);
              const node = this.getNode({ ...firstNode, key: value });
              this.$set(this, "selectTreeValues", [value]);
              this.$set(this, "selectedItems", [node]);
              resolve();
            } else {
              loop(0);
            }
          }, 300);
        };
        loop();
      });
    },
    // 选中 第一个叶子节点
    async selectedFirstLeafNode() {
      // 根据dimCode获取第一个叶子节点信息
      const {
        dimCode,
        needPermission,
        needFilterAudittrail,
        needFilterShared,
        pageName
      } = this;
      await commonService("getFirstDimMember", {
        dimCode,
        needPermission,
        needFilterAudittrail,
        needFilterShared,
        pageName
      }).then(res => {
        const value = this.formatter(res.data);
        const node = this.getNode({ ...res.data, key: value });
        this.$set(this, "selectTreeValues", [value]);
        this.$set(this, "selectedItems", [node]);
      });
    },
    setV(e, val) {
      this.isManualSetup = true;
      this.selectTreeValues = Array.isArray(e) ? e : [e];
      this.selectedItems.splice(0, this.selectedItems.length);
      this.selectedItems = Array.isArray(val) ? val : [val];
      this.$emit("changeVal", e, [...this.selectedItems]);
    },
    onChange(e, val) {
      if (e === this.currValue) return;
      if (this.beforeChange) {
        this.beforeChange().then(() => {
          this.setV(e, val);
        });
      } else {
        this.setV(e, val);
      }
    },
    isEmpty(obj) {
      return obj
        ? Object.keys(JSON.parse(JSON.stringify(obj))).length === 0
        : true;
    },
    treeExpand(treeExpandedKeys) {
      this.treeExpandedKeys = treeExpandedKeys;
    }
  }
};
</script>
