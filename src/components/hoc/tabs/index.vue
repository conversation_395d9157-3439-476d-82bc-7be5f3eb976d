<template>
  <yn-layout
    id="components-layout-demo-side"
    style="min-height: 100vh; height: 100%"
  >
    <div :class="['consolidated_layout-header', !selfTab ? 'new-nav' : '']">
      <div
        :class="
          navState
            ? 'shrik_content shrik_content_open'
            : 'shrik_content shrik_content_close'
        "
        :style="{ width: (navState ? 46 : navWidth) + 'px' }"
      >
        <yn-icon
          :type="navState ? 'menu-unfold' : 'menu-fold'"
          class="shrinkBtn"
          @click="changeNavState"
        />
      </div>
      <yn-tabs
        hideAdd
        type="editable-card"
        class="yn-tabs-wrapper"
        :activeKey="activeKey"
        :style="{ 'margin-left': (navState ? 46 : navWidth) + 'px' }"
        @edit="onEdit"
        @change="changeTargetTab"
      >
        <yn-tab-pane v-for="item in tabArr" :key="item.id">
          <span slot="tab" class="tabPane">
            <span
              v-tooltip="{ title: item.title, visibleOnOverflow: true }"
              class="tabText"
            >
              {{ item.title }}
            </span>
          </span>
        </yn-tab-pane>
      </yn-tabs>
    </div>
    <yn-layout>
      <yn-layout-sider
        theme="light"
        :width="navWidth"
        :class="['consolidated_sider', !selfTab ? 'new-nav' : '']"
      >
        <Menu />
      </yn-layout-sider>
      <yn-layout-content class="consolidation-cont">
        <router-view
          v-if="isShowNewTab"
          @hook:mounted="handleChildrenMounted"
        />
        <template v-else>
          <template v-for="tab in tabArr">
            <template v-if="tab.noKeepAlive">
              <div v-if="activeKey === tab.id" :key="tab.id" class="tab-cont">
                <router-view
                  :name="tab.router"
                  :menuId="tab.id"
                  :activeKey="activeKey"
                  :params="tab"
                  @hook:mounted="handleChildrenMounted"
                />
              </div>
            </template>
            <template v-else>
              <div v-show="activeKey === tab.id" :key="tab.id" class="tab-cont">
                <router-view
                  :name="tab.router"
                  :menuId="tab.id"
                  :activeKey="activeKey"
                  :params="tab"
                  @hook:mounted="handleChildrenMounted"
                />
              </div>
            </template>
          </template>
        </template>
      </yn-layout-content>
    </yn-layout>
  </yn-layout>
</template>
<script>
import "yn-p1/libs/components/yn-layout/";
import "yn-p1/libs/components/yn-layout-header/";
import "yn-p1/libs/components/yn-layout-sider/";
import "yn-p1/libs/components/yn-layout-content/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-tabs/";
import "yn-p1/libs/components/yn-tab-pane/";
import { mapState } from "vuex";
import Menu from "@/components/hoc/menu/Menu.vue";
import savePromptMixin from "@/mixin/savePrompt.js";

export default {
  name: "Tabs",
  components: {
    Menu
  },
  mixins: [savePromptMixin],
  data() {
    return {
      navState: 0,
      navWidth: 200,
      mapRouterOptions: {},
      isShowNewTab: false // 是否显示新版导航模式
    };
  },
  computed: {
    ...mapState({
      tabArr: state => state.common.tabs,
      activeKey: state => state.common.tabActiveId
    })
  },
  watch: {
    "$route.query": {
      handler(newVal) {
        const selfTab = newVal.selfTab;
        this.isShowNewTab = !selfTab;
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    if (this.selfTab) {
      this.$emit("changeSpinning");
    }
  },
  methods: {
    handleChildrenMounted() {
      this.$emit("changeSpinning");
    },
    changeNavState() {
      this.navState = Number(!this.navState);
      this.navWidth = this.navState ? 0 : 200;
    },
    changeTargetTab(targetKey) {
      this.gotabMixin(targetKey);
    },
    changeTab(targetKey) {
      this.$store.commit("common/selectTab", targetKey);
    },
    onEdit(targetKey, action) {
      this.savePromptMixin().then(res => {
        this[action](targetKey);
      });
    },
    remove(targetKey) {
      this.closetabMixin(targetKey);
    }
  }
};
</script>
<style lang="less" scoped>
.new-nav {
  display: none;
}
.shrik_content {
  // height: 35px;
  float: left;
}
.consolidated_layout-header {
  // height: 35px;
  background: @yn-body-background;
}
.consolidated_sider {
  border: 1px solid @yn-border-color-base;
  border-width: 1px 1px 0 0;
  overflow-y: auto;
}
.shrinkBtn {
  position: absolute;
  left: 16px;
  top: 10px;
}
.consolidation-cont {
  overflow-x: auto !important;
}
.tab-cont {
  height: 100%;
}
/deep/.ant-tabs-bar {
  margin: 0;
}
/deep/.ant-tabs-nav-wrap {
  min-height: @rem36;
}
/deep/.yn-tabs-wrapper .ant-tabs-tab .tabPane {
  line-height: 1.5;
}

.yn-tabs-wrapper /deep/.ant-tabs-tab .tabPane .tabText {
  display: inline-block;
  min-width: 2em;
  max-width: 8em;
  vertical-align: text-top;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
