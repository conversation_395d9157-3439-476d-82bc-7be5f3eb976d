<template>
  <div>
    <yn-modal
      :title="title || $t_process('specify_user')"
      :visible="true"
      :okText="$t_common('ok')"
      :cancelText="$t_common('cancel')"
      width="auto"
      class="applicable-user-modal"
      destroyOnClose
      @ok="handleApplicableUsers"
      @cancel="handleCancelApplicableUsers"
    >
      <div class="applicableUsersBox">
        <div v-for="(i, n) in arr" :key="n" class="itemBox">
          <div class="unionBox">
            <yn-select
              v-if="n > 0"
              :value="setType"
              :allowClear="clearFalse"
              :disabled="n > 1"
              @change="handleChangeUnion"
            >
              <yn-select-option value="union">
                {{ $t_common("or") }}
              </yn-select-option>
              <yn-select-option value="join">
                {{ $t_common("and") }}
              </yn-select-option>
            </yn-select>
          </div>
          <div class="labelBox">
            <yn-select
              :key="i.key"
              style="width: 100%"
              :value="i.val"
              :allowClear="false"
              @change="handleChangeType($event, n)"
            >
              <yn-select-option
                v-for="(item, index) in apvlUserType"
                :key="index"
                :value="item.value"
                :disabled="item.disabled"
              >
                {{ item.label }}
              </yn-select-option>
            </yn-select>
          </div>
          <div class="equal">=</div>
          <div class="valueBox">
            <div v-if="i.val === 'org'" class="wrapBox" @click="openTransfer">
              <yn-tag-value :values="i.list" @itemclose="removeOrg" />
              <svg
                t="1640572262073"
                class="icon icon-button-xs"
                viewBox="0 0 1092 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="2557"
                width="40"
                height="40"
                @click="openTransfer"
              >
                <path
                  d="M273.066667 34.133333A102.4 102.4 0 0 1 375.466667 136.533333v750.933334A102.4 102.4 0 0 1 273.066667 989.866667H136.533333A102.4 102.4 0 0 1 34.133333 887.466667V136.533333A102.4 102.4 0 0 1 136.533333 34.133333h136.533334z m0 88.746667H136.533333a13.653333 13.653333 0 0 0-13.175466 10.0352L122.88 136.533333v750.933334a13.653333 13.653333 0 0 0 10.0352 13.175466L136.533333 901.12h136.533334a13.653333 13.653333 0 0 0 13.175466-10.0352L286.72 887.466667V136.533333a13.653333 13.653333 0 0 0-10.0352-13.175466L273.066667 122.88zM955.733333 34.133333A102.4 102.4 0 0 1 1058.133333 136.533333v750.933334a102.4 102.4 0 0 1-102.4 102.4h-136.533333a102.4 102.4 0 0 1-102.4-102.4V136.533333A102.4 102.4 0 0 1 819.2 34.133333h136.533333z m0 88.746667h-136.533333a13.653333 13.653333 0 0 0-13.175467 10.0352L805.546667 136.533333v750.933334a13.653333 13.653333 0 0 0 10.0352 13.175466L819.2 901.12h136.533333a13.653333 13.653333 0 0 0 13.175467-10.0352L969.386667 887.466667V136.533333a13.653333 13.653333 0 0 0-10.0352-13.175466L955.733333 122.88zM609.211733 611.669333a44.373333 44.373333 0 0 1 4.3008 57.7536l-4.3008 4.983467-65.1264 65.1264 65.1264 65.194667a44.373333 44.373333 0 0 1 4.3008 57.7536l-4.3008 4.983466a44.373333 44.373333 0 0 1-57.821866 4.3008l-4.9152-4.3008-96.597334-96.529066a44.373333 44.373333 0 0 1-4.3008-57.7536l4.3008-4.983467L546.474667 611.669333a44.373333 44.373333 0 0 1 62.737066 0zM535.893333 197.768533l4.983467 4.3008 96.529067 96.529067a44.373333 44.373333 0 0 1 4.3008 57.7536l-4.3008 4.983467-96.529067 96.529066a44.373333 44.373333 0 0 1-67.037867-57.7536l4.3008-4.983466 65.1264-65.1264-65.1264-65.194667a44.373333 44.373333 0 0 1-4.3008-57.7536l4.3008-4.983467a44.373333 44.373333 0 0 1 57.7536-4.3008z"
                  p-id="2558"
                />
              </svg>
            </div>
            <yn-select-tree
              v-else-if="i.val === 'user'"
              ref="selector"
              :value="i.apvlUserIds"
              :selectedItems="i.selectedItems"
              searchMode="custom"
              :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
              :datasource="i.list"
              :multiple="!clearFalse"
              :nonleafselectable="true"
              :loadMoreData="loadMoreData"
              :allRootLoaded="!userObj.hasMore"
              @dropdownVisibleChange="dropdownVisibleChange"
              @customSearch="onCustomeSearch"
              @change="onChange($event, i)"
            />
            <yn-select-tree
              v-else-if="i.val === 'group'"
              ref="selector"
              :value="i.apvlUserIds"
              :selectedItems="i.selectedItems"
              searchMode="custom"
              :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
              :datasource="i.list"
              :multiple="!clearFalse"
              :nonleafselectable="true"
              :loadMoreData="loadMoreDataGroup"
              :allRootLoaded="!groupObj.hasMore"
              @dropdownVisibleChange="dropdownVisibleChangeGroup"
              @customSearch="onCustomeSearchGroup"
              @change="onChange($event, i)"
            />
            <yn-select-tree
              v-else
              :value="i.apvlUserIds"
              :selectedItems="i.selectedItems"
              searchMode="multiple"
              :filterOption="filterOption"
              :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
              :datasource="i.list"
              :multiple="!clearFalse"
              :nonleafselectable="true"
              @change="onChange($event, i)"
            />
          </div>
          <div class="removeBox">
            <yn-icon-button v-if="n > 0" type="delete" @click="removeItem(i)" />
          </div>
        </div>
        <div class="addBtn">
          <yn-button v-if="arr.length < 5" type="link" @click="addItem">
            {{ $t_process("add_rules") }}
          </yn-button>
        </div>
      </div>
    </yn-modal>
    <yn-modal
      :title="$t_common('selections')"
      :visible="visible"
      :okText="$t_common('ok')"
      :cancelText="$t_common('cancel')"
      width="auto"
      class="applicable-user-modal"
      destroyOnClose
      @ok="handleOK"
      @cancel="handleCancel"
    >
      <div>
        <yn-transfer-upgrade
          :showTap="true"
          :dataTapSource="dataTapSource"
          :hideDimMemberType="true"
          :dataSource="treeData"
          :excludeData="[]"
          :treeDataOriginalSource="treeDataOriginal"
          :targetDataSource="treeData2"
          :customloader="onLoadData"
          :rules="rules"
          :isUnion="false"
          :isExclude="false"
          :treeSearchDisabled="searchDisabled"
          @change="handleChange"
          @checkChange="checkChange"
          @search="searchDimMember"
          @setTreeDataOriginal="setTreeDataOriginal"
        />
      </div>
    </yn-modal>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-tag/";
import "yn-p1/libs/components/yn-transfer-upgrade/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-tree/";
import cloneDeep from "lodash/cloneDeep";
import commonService from "@/services/common";
export default {
  name: "ApplicableUsers",
  components: {},
  props: {
    userScope: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ""
    },
    enableUnlimited: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      clearFalse: false,
      setType: "union", // 交并集，union:并集；join：交集
      arr: [{ key: this.createUniqueId(), apvlUserIds: [], list: [] }],
      localUsers: [],
      localGroups: [],
      apvlUserType: [
        // 适用对象
        { label: this.$t_common("user"), value: "user", disabled: false },
        { label: this.$t_common("group"), value: "group", disabled: false },
        { label: this.$t_common("role"), value: "role", disabled: false },
        { label: this.$t_common("post"), value: "post", disabled: false },
        {
          label: this.$t_common("organization"),
          value: "org",
          disabled: false
        }
      ],
      userObj: {
        pageSize: 20,
        pageNum: 1,
        hasMore: true,
        total: 0
      },
      groupObj: {
        pageSize: 20,
        pageNum: 1,
        hasMore: true,
        total: 0
      },
      orgData: [],
      // 穿梭框相关
      visible: false,
      dataTapSource: [
        { key: "member", label: this.$t_common("member"), type: "member" }
      ],
      rules: [
        {
          key: 0,
          title: this.$t_common("member_and_descendant"),
          scopeType: "self-and-descendant"
        },
        { key: 1, title: this.$t_common("member"), scopeType: "self" },
        {
          key: 2,
          title: this.$t_common("descendant"),
          scopeType: "descendant"
        }
      ],
      treeData: [],
      treeData2: [],
      treeDataOriginal: [],
      searchDisabled: false,
      userScopeObj: {}
    };
  },
  computed: {},
  watch: {
    userScope: {
      deep: true,
      immediate: true,
      handler(v) {
        if (v.length > 0) {
          this.setType = v[0].setType;
          const mappingObj = {};
          v.slice(1).forEach(item => {
            const { val } = item;
            mappingObj[val] = item;
          });
          const user = mappingObj.user;
          const group = mappingObj.group;
          const role = mappingObj.role;
          const post = mappingObj.post;
          const org = mappingObj.org;
          const arr = [];
          if (user) {
            const random = this.createUniqueId();
            const { apvlUserIds, apvlUserNames } = user;
            const selectedItems = apvlUserIds.map((key, index) => {
              return {
                label: apvlUserNames[index],
                key
              };
            });
            arr.push({
              key: random,
              apvlUserIds: apvlUserIds,
              selectedItems: selectedItems,
              apvlUserNames: selectedItems.map(item => item.label),
              val: "user",
              list: []
            });
          }
          if (group) {
            const random = this.createUniqueId();
            const { apvlUserIds, apvlUserNames } = group;
            const selectedItems = apvlUserIds.map((key, index) => {
              return {
                label: apvlUserNames[index],
                key
              };
            });
            arr.push({
              key: random,
              apvlUserIds: apvlUserIds,
              selectedItems: selectedItems,
              apvlUserNames: selectedItems.map(item => item.label),
              val: "group",
              list: []
            });
          }
          if (role) {
            const random = this.createUniqueId();
            const { apvlUserIds, apvlUserNames } = role;
            const selectedItems = apvlUserIds.map((key, index) => {
              return {
                label: apvlUserNames[index],
                key
              };
            });
            arr.push({
              key: random,
              apvlUserIds: apvlUserIds,
              selectedItems: selectedItems,
              apvlUserNames: selectedItems.map(item => item.label),
              val: "role",
              list: []
            });
          }
          if (post) {
            const random = this.createUniqueId();
            const { apvlUserIds, apvlUserNames } = post;
            const selectedItems = apvlUserIds.map((key, index) => {
              return {
                label: apvlUserNames[index],
                key
              };
            });
            arr.push({
              key: random,
              apvlUserIds: apvlUserIds,
              selectedItems: selectedItems,
              apvlUserNames: selectedItems.map(item => item.label),
              val: "post",
              list: []
            });
          }
          if (org) {
            const random = this.createUniqueId();
            arr.push({
              key: random,
              apvlUserIds: [],
              selectedItems: [],
              val: "org",
              list: org.list
            });
          }
          if (arr.length) {
            this.arr = arr;
            const self = this;
            const getTypes = {
              user: function(item) {
                self.getLoadUserByCondition(result => {
                  item.list = result;
                  // self.onChange(
                  //   item.apvlUserIds,
                  //   {
                  //     key: item.key,
                  //     apvlUserIds: item.apvlUserIds,
                  //     selectedItems: item.selectedItems,
                  //     apvlUserNames: item.apvlUserNames,
                  //     val: "user",
                  //     list: item.list
                  //   },
                  //   true
                  // );
                });
              },
              role: function(item) {
                self.getRole(result => {
                  item.list = result;
                  // self.onChange(
                  //   item.apvlUserIds,
                  //   {
                  //     key: item.key,
                  //     apvlUserIds: item.apvlUserIds,
                  //     selectedItems: item.selectedItems,
                  //     apvlUserNames: item.apvlUserNames,
                  //     val: "role",
                  //     list: item.list
                  //   },
                  //   true
                  // );
                });
              },
              group: function(item) {
                self.getGroup(result => {
                  item.list = result;
                  // self.onChange(
                  //   item.apvlUserIds,
                  //   {
                  //     key: item.key,
                  //     apvlUserIds: item.apvlUserIds,
                  //     selectedItems: item.selectedItems,
                  //     apvlUserNames: item.apvlUserNames,
                  //     val: "group",
                  //     list: item.list
                  //   },
                  //   true
                  // );
                });
              },
              post: function(item) {
                self.dimPost(result => {
                  item.list = result;
                  // self.onChange(
                  //   item.apvlUserIds,
                  //   {
                  //     key: item.key,
                  //     apvlUserIds: item.apvlUserIds,
                  //     selectedItems: item.selectedItems,
                  //     apvlUserNames: item.apvlUserNames,
                  //     val: "post",
                  //     list: item.list
                  //   },
                  //   true
                  // );
                });
              },
              org: self.dimOrg
            };
            this.arr.forEach(item => {
              const e = item.val;
              getTypes[e](item);
            });
            this.apvlUserType.forEach(item => {
              const d = this.arr.find(jtem => jtem.val === item.value);
              if (d) {
                item.disabled = true;
              } else {
                item.disabled = false;
              }
            });
          }
        }
      }
    }
  },
  beforeDestroyed() {},
  created() {},
  mounted() {},
  methods: {
    handleCancelApplicableUsers() {
      this.$emit("handleCancelApplicableUsers");
    },
    handleApplicableUsers() {
      this.$emit("handleApplicableUsers", this.arr, this.setType);
    },
    handleChangeUnion(e) {
      this.setType = e;
    },
    handleChangeType(e, i) {
      this.arr[i].val = e;
      this.arr[i].list = [];
      this.arr[i].apvlUserIds = [];
      const self = this;
      const getTypes = {
        user: () => {
          self.getLoadUserByCondition(result => {
            self.arr[i].list = result;
          });
        },
        role: () => {
          self.getRole(result => {
            self.arr[i].list = result;
          });
        },
        group: () => {
          self.getGroup(result => {
            self.arr[i].list = result;
          });
        },
        post: () => {
          self.dimPost(result => {
            this.arr[i].list = result;
          });
        },
        org: self.dimOrg
      };
      getTypes[e]();
      this.apvlUserType.forEach(item => {
        const d = this.arr.find(jtem => jtem.val === item.value);
        if (d) {
          item.disabled = true;
        } else {
          item.disabled = false;
        }
      });
    },
    getListData(arr) {
      const list = Array.isArray(arr) ? arr : [];
      return this.enableUnlimited
        ? [
          {
            key: "all",
            value: "all",
            label: this.$t_common("unlimited")
          },
          ...list
        ]
        : list;
    },
    getLoadUserByCondition(callback) {
      commonService("loadUserByCondition", {
        pageSize: 20,
        pageNum: 1,
        condition: this.searchValue
      }).then(res => {
        if (res.data.success) {
          this.userObj = {
            pageNum: res.data.data.pageNum,
            pageSize: 20,
            total: res.data.data.total,
            hasMore: res.data.data.hasNextPage
          };
          res.data.data.list.forEach(item => {
            const apvlUserIds =
              this.arr.find(i => i.val === "user") &&
              this.arr.find(i => i.val === "user").apvlUserIds
                ? this.arr.find(i => i.val === "user").apvlUserIds
                : [];
            if (apvlUserIds.length === 1 && apvlUserIds[0] === "all") {
              item.disableCheckbox = true;
            } else {
              item.disableCheckbox = false;
            }
            item.key = item.userId;
            item.value = item.userId;
            item.label = `${item.userName}(${item.loginName})`;
            if (
              this.localUsers.findIndex(user => user.key === item.userId) === -1
            ) {
              this.localUsers.push({ label: item.userName, key: item.userId });
            }
          });
          if (
            this.searchValue &&
            this.$t_common("unlimited").indexOf(this.searchValue) < 0
          ) {
            callback(res.data.data.list);
          } else {
            callback(this.getListData(res.data.data.list));
          }
        }
      });
    },
    getGroup(callback) {
      commonService("getGroupList", {
        pageSize: 20,
        pageNum: 1,
        queryValue: this.searchValueGroup
      }).then(res => {
        if (res.data.success) {
          this.groupObj = {
            pageNum: res.data.data.pageNum,
            pageSize: 20,
            total: res.data.data.total,
            hasMore: res.data.data.hasNextPage
          };
          res.data.data.list.forEach(item => {
            const apvlUserIds =
              this.arr.find(i => i.val === "group") &&
              this.arr.find(i => i.val === "group").apvlUserIds
                ? this.arr.find(i => i.val === "group").apvlUserIds
                : [];
            if (apvlUserIds.length === 1 && apvlUserIds[0] === "all") {
              item.disableCheckbox = true;
            } else {
              item.disableCheckbox = false;
            }
            item.key = item.groupId;
            item.value = item.groupId;
            item.label = item.groupName;
            if (
              this.localGroups.findIndex(user => user.key === item.groupId) ===
              -1
            ) {
              this.localGroups.push({
                label: item.groupName,
                key: item.groupId
              });
            }
          });
          if (
            this.searchValueGroup &&
            this.$t_common("unlimited").indexOf(this.searchValueGroup) < 0
          ) {
            callback(res.data.data.list);
          } else {
            callback(this.getListData(res.data.data.list));
          }
        }
      });
    },
    loadMoreData() {
      return new Promise(resolve => {
        if (!this.userObj.hasMore) {
          resolve({
            hasMore: this.userObj.hasMore,
            data: []
          });
        } else {
          commonService("loadUserByCondition", {
            pageSize: this.userObj.pageSize,
            pageNum: this.userObj.pageNum + 1,
            condition: this.searchValue
          }).then(res => {
            if (res.data.success) {
              this.userObj = {
                pageNum: res.data.data.pageNum,
                pageSize: 20,
                total: res.data.data.total,
                hasMore: res.data.data.hasNextPage
              };
              res.data.data.list.map(item => {
                const apvlUserIds =
                  this.arr.find(i => i.val === "user") &&
                  this.arr.find(i => i.val === "user").apvlUserIds
                    ? this.arr.find(i => i.val === "user").apvlUserIds
                    : [];
                if (apvlUserIds.length === 1 && apvlUserIds[0] === "all") {
                  item.disableCheckbox = true;
                } else {
                  item.disableCheckbox = false;
                }
                item.key = item.userId;
                item.value = item.userId;
                item.label = `${item.userName}(${item.loginName})`;
                if (
                  this.localUsers.findIndex(
                    user => user.key === item.userId
                  ) === -1
                ) {
                  this.localUsers.push({
                    label: item.userName,
                    key: item.userId
                  });
                }
              });
              this.arr
                .find(item => item.val === "user")
                .list.push(...res.data.data.list);
              resolve({
                hasMore: this.userObj.hasMore,
                data: []
              });
            }
          });
        }
      });
    },
    loadMoreDataGroup() {
      return new Promise(resolve => {
        if (!this.groupObj.hasMore) {
          resolve({
            hasMore: this.groupObj.hasMore,
            data: []
          });
        } else {
          commonService("getGroupList", {
            pageSize: this.groupObj.pageSize,
            pageNum: this.groupObj.pageNum + 1,
            condition: this.searchValue
          }).then(res => {
            if (res.data.success) {
              this.groupObj = {
                pageNum: res.data.data.pageNum,
                pageSize: 20,
                total: res.data.data.total,
                hasMore: res.data.data.hasNextPage
              };
              res.data.data.list.map(item => {
                const apvlUserIds =
                  this.arr.find(i => i.val === "group") &&
                  this.arr.find(i => i.val === "group").apvlUserIds
                    ? this.arr.find(i => i.val === "group").apvlUserIds
                    : [];
                if (apvlUserIds.length === 1 && apvlUserIds[0] === "all") {
                  item.disableCheckbox = true;
                } else {
                  item.disableCheckbox = false;
                }
                item.key = item.groupId;
                item.value = item.groupId;
                item.label = item.groupName;
                if (
                  this.localGroups.findIndex(
                    user => user.key === item.groupId
                  ) === -1
                ) {
                  this.localGroups.push({
                    label: item.groupName,
                    key: item.groupId
                  });
                }
              });
              this.arr
                .find(item => item.val === "group")
                .list.push(...res.data.data.list);
              resolve({
                hasMore: this.groupObj.hasMore,
                data: []
              });
            }
          });
        }
      });
    },
    onCustomeSearch({ searchValue } = {}) {
      this.searchValue = searchValue;
      const userData = this.arr.find(item => item.val === "user");
      this.getLoadUserByCondition(result => {
        userData.list = result;
      });
    },
    onCustomeSearchGroup({ searchValue } = {}) {
      this.searchValueGroup = searchValue;
      const groupData = this.arr.find(item => item.val === "group");
      this.getGroup(result => {
        groupData.list = result;
      });
    },
    dropdownVisibleChange(isOpen) {
      if (!isOpen) {
        this.searchValue = "";
        const userData = this.arr.find(item => item.val === "user");
        this.getLoadUserByCondition(result => {
          userData.list = result;
          this.onChange(
            userData.apvlUserIds,
            {
              key: userData.key,
              apvlUserIds: userData.apvlUserIds,
              selectedItems: userData.selectedItems,
              apvlUserNames: userData.apvlUserNames,
              val: "user",
              list: userData.list
            },
            true
          );
        });
      }
    },
    dropdownVisibleChangeGroup(isOpen) {
      if (!isOpen) {
        this.searchValueGroup = "";
        const groupData = this.arr.find(item => item.val === "group");
        this.getGroup(result => {
          groupData.list = result;
          this.onChange(
            groupData.apvlUserIds,
            {
              key: groupData.key,
              apvlUserIds: groupData.apvlUserIds,
              selectedItems: groupData.selectedItems,
              apvlUserNames: groupData.apvlUserNames,
              val: "group",
              list: groupData.list
            },
            true
          );
        });
      }
    },
    getRole(callback) {
      commonService("getRoleList", {}).then(res => {
        if (res.data.success) {
          const roleList = res.data.data.list.map(item => {
            item.key = item.roleId;
            item.value = item.roleId;
            item.label = item.roleName;
            item.disableCheckbox = item.grouped;
            if (item.roleCount) {
              const data = item.children.map(j => {
                j.key = j.roleId;
                j.value = j.roleId;
                j.label = j.roleName;
                j.disableCheckbox = j.grouped;
                delete j.children;
                return j;
              });
              item.children = data;
              return item;
            } else {
              delete item.children;
            }
            return item;
          });
          callback(this.getListData(roleList));
        }
      });
    },

    dimPost(callback) {
      commonService("dimPost", {
        offset: 0,
        limit: 1000,
        totalResults: true
      }).then(res => {
        res.data.items.map(item => {
          item.key = item.objectId;
          item.value = item.objectId;
          item.label = item.name;
          item.disableCheckbox = true;
          item.isLeaf = item.isLeaf.toString().toUpperCase() === "TRUE";
          if (!item.isLeaf) {
            item.children = [];
            this.dimPostChild(item.objectId, result => {
              item.children = result;
            });
          }
        });
        callback(this.getListData(res.data.items));
      });
    },
    dimPostChild(objId, callback) {
      commonService("dimPost", {
        offset: 0,
        limit: 1000,
        totalResults: true,
        parentId: objId
      }).then(res => {
        res.data.items.map(item => {
          item.key = item.objectId;
          item.value = item.objectId;
          item.label = item.name;
          item.disableCheckbox = false;
          item.isLeaf = item.isLeaf.toString().toUpperCase() === "TRUE";
        });
        callback(res.data.items);
      });
    },
    dimOrg() {
      commonService("dimOrg", {
        offset: 0,
        limit: 1000,
        totalResults: true
      }).then(res => {
        const data = res.data.items;
        this.treeData = [];
        this.treeDataOriginal = [];
        data.forEach(item => {
          const obj = {
            title: item.name,
            key: item.objectId,
            type: "member",
            isLeaf: item.isLeaf.toString().toUpperCase() === "TRUE"
          };
          this.treeData.push(obj);
        });
        this.treeDataOriginal = cloneDeep(this.treeData);
      });
    },
    onLoadData(node) {
      if (node.type === "member") {
        return new Promise(resolve => {
          commonService("dimOrg", {
            offset: 0,
            limit: 1000,
            totalResults: true,
            parentId: node.key
          }).then(res => {
            const data = res.data.items;
            const dimMemberList = [];
            data.forEach(item => {
              const obj = {
                title: item.name,
                key: item.objectId,
                type: "member",
                scopedSlots: { title: "custom" },
                isLeaf: item.isLeaf.toString().toUpperCase() === "TRUE"
              };
              dimMemberList.push(obj);
            });
            node.children = dimMemberList;
            return resolve(dimMemberList);
          });
        });
      }
    },
    onChange(e, i, flag) {
      const index = this.arr.findIndex(item => item.key === i.key);
      this.arr[index].apvlUserIds = e;
      const s = i.val;
      if (s === "user" || s === "group") {
        if (e && e.length) {
          if (e[0] === "all") {
            this.arr[index].apvlUserNames = [this.$t_common("unlimited")];
          } else {
            if (!flag) {
              this.arr[index].apvlUserNames = e.map(j => {
                return s === "user"
                  ? this.localUsers.find(a => a.key === j).label
                  : this.localGroups.find(a => a.key === j).label;
              });
            }
          }
        } else {
          this.arr[index].apvlUserNames = [];
        }

        // 这俩是一层结构，平铺
        if (e[0] === "all") {
          this.arr[index].list.map(item => {
            if (item.key === "all") {
              item.disableCheckbox = false;
            } else {
              item.disableCheckbox = true;
            }
          });
        } else if (e.length === 0) {
          this.arr[index].list.map(item => {
            item.disableCheckbox = false;
          });
        } else {
          this.arr[index].list.map(item => {
            if (item.key === "all") {
              item.disableCheckbox = true;
            } else {
              item.disableCheckbox = false;
            }
          });
        }
      } else {
        if (e && e.length) {
          if (e[0] === "all") {
            this.arr[index].apvlUserNames = [this.$t_common("unlimited")];
          } else {
            const selectedItems = [];
            this.arr[index].apvlUserNames = e.map(j => {
              const w = [];
              this.arr[index].list.forEach(e => {
                if (e.children && e.children.length) {
                  e.children.forEach(s => {
                    if (s.key === j) {
                      w.push(s.label);
                      selectedItems.push({
                        key: s.key,
                        label: s.label
                      });
                    }
                  });
                }
              });
              return w;
            });
            this.arr[index].selectedItems = selectedItems;
          }
        } else {
          this.arr[index].apvlUserNames = [];
        }

        // 这俩是一层结构，平铺
        if (e[0] === "all") {
          this.arr[index].list.map(item => {
            if (item.key === "all") {
              item.disableCheckbox = false;
            } else {
              if (item.children && item.children.length) {
                item.children.map(jtem => (jtem.disableCheckbox = true));
              }
            }
          });
        } else if (e.length === 0) {
          this.arr[index].list.map(item => {
            if (item.key === "all") {
              item.disableCheckbox = false;
            }
            if (item.children && item.children.length) {
              item.children.map(jtem => (jtem.disableCheckbox = false));
            }
          });
        } else {
          this.arr[index].list.map(item => {
            if (item.key === "all") {
              item.disableCheckbox = true;
            } else {
              if (item.children && item.children.length) {
                item.children.map(jtem => (jtem.disableCheckbox = false));
              }
            }
          });
        }
      }
    },
    removeOrg(v) {
      const orgList = this.arr.find(item => item.val === "org").list;
      const index = orgList.findIndex(item => item.apvlUserId === v);
      if (index > -1) {
        orgList.splice(index, 1);
      }
    },
    removeItem(i) {
      const index = this.arr.findIndex(item => item.key === i.key);
      if (index > 0) {
        this.arr.splice(index, 1);
      }
      this.apvlUserType.forEach(item => {
        const d = this.arr.find(jtem => jtem.val === item.value);
        if (d) {
          item.disabled = true;
        } else {
          item.disabled = false;
        }
      });
    },
    addItem() {
      const random = this.createUniqueId();
      if (this.arr.find(item => item.key === random)) {
        this.addItem();
      } else {
        this.arr.push({ key: random, apvlUserIds: [], list: [] });
      }
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    createUniqueId() {
      // 生成10-12位不等的字符串
      return Number(
        Math.random()
          .toString()
          .substr(2)
      ).toString(36); // 转换成十六进制
    },
    handleOK: async function() {
      const orgDataList = this.treeData2.map((item, index) => {
        const obj = {
          apvlUserpos: index,
          apvlUserId: item.key.split("-")[0],
          apvlUserType: "org",
          scopeType: item.memberTypeValue,
          setType: this.setType,
          value: item.key.split("-")[0],
          label: `${item.label}(${
            item.memberTypeValue === "self"
              ? this.$t_common("member")
              : item.memberTypeValue === "descendant"
                ? this.$t_common("descendant")
                : this.$t_common("member_and_descendant")
          })`,
          orgName: item.label
        };
        return obj;
      });
      this.arr.find(item => item.val === "org").list = orgDataList;
      this.visible = false;
    },
    handleCancel() {
      this.visible = false;
    },
    handleChange(data) {
      this.treeData2 = data;
    },
    checkChange(item) {
      this.treeData2.push(...item);
    },
    setTreeDataOriginal(treeData) {
      // 主要是为了异步加载数据事同步，否则搜索时候清空，会导致treeData里的数据不全
      this.treeDataOriginal = treeData;
    },
    searchDimMember: async function(value, type) {
      if (value) {
        const dimMembersList = [];
        if (type === "member") {
          commonService("dimOrg", {
            offset: 0,
            limit: 1000,
            totalResults: true,
            query: `name like '*${value}*'`
          }).then(res => {
            const data = res.data.items;
            data.forEach(item => {
              const obj = {
                title: item.name,
                key: item.objectId,
                type: "member",
                scopedSlots: { title: "custom" },
                isLeaf: true
              };
              dimMembersList.push(obj);
            });
            this.treeData = dimMembersList;
          });
        }
      } else {
        // 清空时数据恢复
        this.treeData = this.treeDataOriginal;
      }
    },
    openTransfer() {
      this.visible = true;
      const orgData = this.arr.find(item => item.val === "org");
      if (orgData && orgData.list && orgData.list.length) {
        this.treeData2 = orgData.list.map(item => {
          return {
            key: item.apvlUserId + "-" + item.scopeType,
            label: item.orgName,
            memberType:
              item.scopeType === "self-and-descendant"
                ? 0
                : item.scopeType === "self"
                  ? 1
                  : 2,
            memberTypeValue: item.scopeType,
            objectId: item.apvlUserId + "-" + item.scopeType,
            scopedSlots: Object,
            shardim: undefined,
            title: item.label,
            type: "member",
            value: item.apvlUserId
          };
        });
      } else {
        this.$set(this, "treeData2", []);
      }
    }
  }
};
</script>
<style lang="less">
@import "~yn-p1/libs/components/style/default.less";
.applicableUsersBox {
  .addBtn {
    padding-left: 3.75rem;
    button {
      &:hover,
      &:active,
      &:visited,
      &:focus {
        background: @yn-body-background;
      }
    }
  }
  .itemBox {
    > div {
      padding: 0 0.625rem;
    }
    display: flex;
    align-items: center;
    padding: 0.625rem 0;
    .unionBox {
      width: 3.75rem;
    }
    .labelBox {
      width: 7.5rem;
    }
    .valueBox {
      width: 28.125rem;
      .wrapBox {
        border: 1px solid @yn-border-color-base;
        border-radius: 0.25rem;
        padding: 0 0.3125rem;
        position: relative;
        height: 2rem;
        display: flex;
        align-items: center;
        .icon {
          cursor: pointer;
          color: @yn-label-color;
          width: 1rem;
          height: 1rem;
          padding: 0.25rem;
          box-sizing: content-box;
          min-width: 1rem;
          &:hover {
            color: @yn-primary-5;
          }
        }
        .yn-tag-value {
          margin-right: 0.3125rem;
          .yn-tag-value-item.ant-tag {
            margin-top: 0 !important;
          }
        }
      }
    }
    .removeBox {
      width: 2.5rem;
    }
  }
}
</style>
