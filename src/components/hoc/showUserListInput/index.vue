<template>
  <div class="ant-input-affix-wrapper">
    <div
      :class="[
        'showUserListInput',
        'ant-input',
        { disabled: disabled, 'ant-input-disabled': disabled }
      ]"
      @click="handleSelectUserModal"
      @mouseleave="() => (showClearBtn = false)"
      @mouseenter="handleMouseenter"
    >
      <div class="input-content">
        <span
          v-tooltip="{
            visibleOnOverflow: true,
            title: text
          }"
          class="text"
        >
          {{ text }}
        </span>
        <yn-icon-svg
          v-if="!showClearBtn || disabled"
          type="field-people"
          @click.stop="handleSelectUserModal"
        />
        <yn-icon-svg v-else type="delete-border" @click.stop="handleClear" />
      </div>
      <ApplicableUsers
        v-if="showModal"
        v-bind="$attrs"
        :userScope="userScope"
        @handleApplicableUsers="handleApplicableUsers"
        @handleCancelApplicableUsers="handleCancelApplicableUsers"
      />
    </div>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-icon/";
import ApplicableUsers from "./applicableUsers.vue";

export default {
  name: "ShowUserListInput",
  components: {
    ApplicableUsers
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showModal: false,
      userScope: [],
      showClearBtn: false,
      APV_USER_TYPE_MAPPING: Object.freeze({
        user: this.$t_common("user"),
        group: this.$t_common("group"),
        role: this.$t_common("role"),
        post: this.$t_common("post"),
        org: this.$t_common("organization")
      })
    };
  },
  computed: {
    text() {
      if (!this.userScope) return "";
      const text = [];
      this.userScope.forEach((item, index) => {
        const { val, apvlUserNames = [], list } = item;
        if (!index) {
          text.push(item.setType === "union" ? "【并集】" : "【交集】");
        } else if (val === "org") {
          const tempStr = list
            .map(node => {
              return node.label;
            })
            .join("、");
          if (tempStr) {
            text.push(`${this.APV_USER_TYPE_MAPPING[val]}：${tempStr}`);
          }
        } else {
          const tempStr = apvlUserNames.join("、");
          if (tempStr) {
            text.push(`${this.APV_USER_TYPE_MAPPING[val]}：${tempStr}`);
          }
        }
      });
      if (text.length > 1) {
        return text.slice(0, 1).join() + text.slice(1).join("；") + "；";
      } else {
        return "";
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.$set(this, "userScope", newVal || []);
      },
      immediate: true
    }
  },
  methods: {
    handleSelectUserModal() {
      if (this.disabled) return;
      this.showModal = true;
    },
    handleHover() {
      this.showClearBtn = true;
    },
    handleApplicableUsers(result, setType) {
      // 过滤有效值
      const userInfo = this.getFilteredUserInfo(result);
      let userScope = [];
      if (userInfo.length) {
        userScope = [{ setType }, ...userInfo];
      }
      this.$set(this, "userScope", userScope);
      this.$emit("change", userScope.length ? userScope : "");
      this.handleCancelApplicableUsers();
    },
    getFilteredUserInfo(userInfo) {
      return userInfo.filter(item => {
        const { val, list, apvlUserIds } = item;
        if (val === "org") {
          return list.length;
        } else {
          return apvlUserIds.length;
        }
      });
    },
    handleCancelApplicableUsers() {
      this.showModal = false;
    },
    handleMouseenter() {
      if (this.text) {
        this.showClearBtn = true;
      }
    },
    handleClear() {
      this.userScope.splice(0);
    }
  }
};
</script>
<style lang="less" scoped>
.showUserListInput {
  border: 1px solid @yn-border-color-base;
  border-radius: @yn-border-radius-base;
  cursor: pointer;
}
.showUserListInput:hover {
  border-color: #3b99f7;
}
.showUserListInput.disabled:hover {
  border-color: @yn-border-color-base;
}
.showUserListInput.disabled {
  color: @yn-text-color-secondary;
  background-color: @yn-disabled-bg-color;
  cursor: not-allowed;
  opacity: 1;
}
.input-content {
  width: 100%;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  padding-left: @yn-padding-s;
  .text {
    width: calc(100% - @rem32);
  }
  /deep/.ynicon {
    color: @yn-disabled-color;
    margin-right: @yn-margin-s;
  }
}
</style>
