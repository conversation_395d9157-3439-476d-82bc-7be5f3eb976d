<template>
  <div class="tree-grid-cont">
    <Grid
      ref="grid"
      v-bind="$attrs"
      gridType="treeGrid"
      :mixCellWidth="200"
      :dataSource="showTableData"
      :cellNode="renderNode"
      v-on="$listeners"
    />
    <div v-if="showTableData.length > 0" ref="footerCont">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
import Grid from "../grid";
import Logger from "yn-p1/libs/modules/log/logger";
import cloneDeep from "lodash/cloneDeep";
import omit from "lodash/omit";
const EXPAND_STATUS = ["icon-jiantouyou", "icon-jiantouxia"]; // 0 收起   1 展开
const ROOT_PID = "-1";
export default {
  name: "TreeGrid",
  components: {
    Grid
  },
  inheritAttrs: false,
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    expandedRowKeys: {
      type: Array,
      default: () => []
    },
    cellNode: {
      type: Function,
      default: () => ""
    }
  },
  data() {
    return {
      showTableData: [],
      mapData: new Map(),
      expandedKeys: [],
      gridHeight: 46,
      parentContH: 0,
      currScrollTop: 0,
      $gridIdent: "reconciliationReport" // 表格标识
    };
  },
  watch: {
    dataSource: {
      handler(newVal) {
        this.setMapData(newVal);
      },
      deep: true,
      immediate: true
    },
    expandedRowKeys: {
      handler(newVal) {
        // this.showTableData = [...this.getShowTableData(newVal)];
        this.expandedKeys = cloneDeep(newVal);
        this.updateTable();
        this.$nextTick(() => {
          try {
            this.$refs.grid.$refs.simpleGrid.$el.querySelectorAll(
              ".ht_master .wtHolder"
            )[0].scrollTop = this.currScrollTop;
            this.currScrollTop = 0;
          } catch {}
        });
      },
      immediate: true
    }
  },
  methods: {
    getRowDataById(id) {
      return this.mapObj.get(id) || {};
    },
    updateTable(data) {
      this.showTableData = this.getShowTableData(this.expandedKeys);
      this.$refs.grid && this.$refs.grid.updateTableData(this.showTableData);
    },
    setMapData(treeData) {
      const mapObj = new Map();
      const loop = data => {
        data.forEach(item => {
          const { children, objectId } = item;
          mapObj.set(objectId, item);
          if (children && children.length > 0) {
            loop(children);
          }
        });
      };
      loop(treeData);
      this.mapData = mapObj;
    },
    getShowTableData(expandedRowKeys = []) {
      if (!expandedRowKeys.length) {
        return this.dataSource.map(item => {
          return omit(item, ["children"]);
        });
      } else {
        const res = [];
        for (const item of this.mapData.values()) {
          const { parentId, children, ...otherProps } = item;
          Logger.log(children);
          const { v: pId } = parentId;
          if (pId === ROOT_PID || pId === "") {
            res.push({
              parentId,
              children,
              ...otherProps
            });
          } else if (expandedRowKeys.indexOf(pId) !== -1) {
            //  向上级寻找，是否上级也是展开状态，如果是，则展示，否则不展示
            let isContinue = true;
            let tempPId = this.mapData.get(pId).parentId.v;
            let isShow = true;
            while (isContinue) {
              // 是根节点
              if (tempPId === ROOT_PID || !tempPId) {
                isShow = true;
                isContinue = false;
                continue;
              } else if (expandedRowKeys.indexOf(tempPId) === -1) {
                // 某一层父项没有展开
                isShow = false;
                isContinue = false;
              } else {
                // 继续向上查找
                tempPId = this.mapData.get(tempPId).parentId.v;
              }
            }
            if (isShow) {
              res.push({
                parentId,
                children,
                ...otherProps
              });
            }
          }
        }
        return res;
      }
    },
    // 渲染表体单元格
    renderNode(cellContext) {
      const nodeHtml = this.cellNode && this.cellNode(cellContext);
      const { col } = cellContext;
      const expand = this.$refs.grid.selection ? 1 : 0;
      return col === expand
        ? this.renderRowHeader(cellContext, nodeHtml)
        : nodeHtml;
    },
    // 渲染行头
    renderRowHeader(cellContext, nodeHtml) {
      const { value, td, rowId } = cellContext;
      const childDiv = document.createElement("div");
      const wordTag = document.createElement("span");
      wordTag.textContent = value;
      childDiv.className = "header-col";
      const iconButton = document.createElement("i");
      const { children, level } = this.mapData.get(rowId) || {};
      if (children && children.length && !this.$refs.grid.searchObj.length) {
        const iconClass =
          this.expandedKeys.indexOf(rowId) !== -1
            ? EXPAND_STATUS[1]
            : EXPAND_STATUS[0];
        iconButton.className = `iconfont icon-button icon-hover ${iconClass} row-header-icon-btn`;
        // TODO 边界值考虑
        iconButton.style.cssText = `margin-left:${level * 24}px`;
        childDiv.appendChild(iconButton);
        td.onclick = e => {
          this.clickExpandCollapseBtn(e, cellContext);
        };
      } else {
        iconButton.style.cssText = `margin-left:${level * 24}px`;
        childDiv.appendChild(iconButton);
      }
      childDiv.appendChild(nodeHtml || wordTag);
      td.className += " grid-header";
      td.appendChild(childDiv);

      return childDiv;
    },
    clickExpandCollapseBtn(e, cellContext) {
      this.currScrollTop = this.$refs.grid.$refs.simpleGrid.$el.querySelectorAll(
        ".ht_master .wtHolder"
      )[0].scrollTop;
      const currDom = e.currentTarget.querySelector(".icon-button");
      const { className, classList } = currDom;
      const [collapseClass, expandClass] = EXPAND_STATUS;
      const { rowId } = cellContext;
      const status = className.indexOf(expandClass) !== -1; // true 展开    false 收起
      if (status) {
        // 折叠操作
        classList.remove(expandClass);
        classList.add(collapseClass);
        this.expandedKeys.splice(this.expandedKeys.indexOf(rowId), 1);
      } else {
        // 展开操作
        classList.remove(collapseClass);
        classList.add(expandClass);
        this.expandedKeys.push(rowId);
      }
      this.$emit("expand", this.expandedKeys);
    }
  }
};
</script>
<style lang="less">
.tree-grid-cont td.simple-cell-readonly {
  background-color: @yn-component-background;
}
</style>
<style lang="less" scoped>
.tree-grid-cont {
  width: 100%;
  height: 100%;
  /deep/i.row-header-icon-btn {
    width: auto;
    margin: 0 8px;
  }
}
</style>
