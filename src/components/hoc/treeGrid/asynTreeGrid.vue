<template>
  <div class="tree-grid-cont">
    <Grid
      ref="grid"
      v-bind="$attrs"
      gridType="treeGrid"
      :mixCellWidth="200"
      :dataSource="showTableData"
      :cellNode="renderNode"
      :addRow="addRow"
      :deleteRow="true"
      v-on="$listeners"
    />
    <div v-if="showTableData.length > 0" ref="footerCont">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
import Grid from "../grid";
import AppUtils from "yn-p1/libs/utils/AppUtils";
const EXPAND_STATUS = ["icon-jiantouyou", "icon-jiantouxia"]; // 0 收起   1 展开
const ROOT_PID = "-1";
// 加载更多标识
const LOAD_MORE_IDENT = "loadMoreRow";
// 加载更多文案
export default {
  name: "AsynTreeGrid",
  components: {
    Grid
  },
  inheritAttrs: false,
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    expandedRowKeys: {
      type: Array,
      default: () => []
    },
    cellNode: {
      type: Function,
      default: () => ""
    },
    getChildrenData: {
      type: Function,
      default: () => {
        return Promise.resolve(res => {
          res([]);
        });
      }
    }
  },
  data() {
    return {
      showTableData: [],
      mapData: {},
      expandedKeys: [],
      gridHeight: 46,
      parentContH: 0,
      currScrollTop: 0,
      addRow: null,
      deleteRow: true
    };
  },
  watch: {
    dataSource: {
      handler(newVal) {
        this.setMapData(newVal);
        this.showTableData = this.getShowTableData();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getRowDataById(id) {
      return this.mapData[id] || {};
    },
    updateTable(data) {
      // 清空过滤条件
      this.$refs.grid.deleteFilteItemById("all");
      this.showTableData = this.getShowTableData();
      this.$refs.grid.$refs.simpleGrid.updateBody(this.showTableData);
    },
    setMapData(treeData) {
      const mapObj = {};
      const loop = data => {
        data.forEach(item => {
          const { children, objectId } = item;
          mapObj[objectId] = item;
          if (children && children.length > 0) {
            loop(children);
          }
        });
      };
      loop(treeData);
      this.mapData = mapObj;
    },
    getShowTableData() {
      return [...this.dataSource];
    },
    // 渲染表体单元格
    renderNode(cellContext) {
      const nodeHtml = this.cellNode && this.cellNode(cellContext);
      const { col } = cellContext;
      return !col ? this.renderRowHeader(cellContext, nodeHtml) : nodeHtml;
    },
    // 渲染行头
    renderRowHeader(cellContext, nodeHtml) {
      const { value, td, rowId } = cellContext;
      const childDiv = document.createElement("div");
      const wordTag = document.createElement("span");
      wordTag.textContent = value;
      childDiv.className = "header-col";
      const iconButton = document.createElement("i");
      const { level, hasChildren } = this.mapData[rowId] || {}; // 根据hasChildren 判断是否显示展开折叠按钮
      if (hasChildren && !this.$refs.grid.searchObj.length) {
        const iconClass =
          this.expandedKeys.indexOf(rowId) !== -1
            ? EXPAND_STATUS[1]
            : EXPAND_STATUS[0];
        iconButton.className = `iconfont icon-button icon-hover ${iconClass} row-header-icon-btn`;
        // TODO 边界值考虑
        iconButton.style.cssText = `margin-left:${level * 24}px`;
        childDiv.appendChild(iconButton);
        iconButton.addEventListener("mousedown", e => {
          // 切换
          this.clickExpandCollapseBtn(e, cellContext);
          e.stopPropagation();
        });
      } else {
        iconButton.style.cssText = `margin-left:${level * 24}px`;
        const loadMoreDom = this.getLoadMoreDom();
        childDiv.appendChild(iconButton);
        childDiv.appendChild(loadMoreDom);
      }
      childDiv.appendChild(nodeHtml || wordTag);
      td.className += " grid-header";
      td.appendChild(childDiv);

      return childDiv;
    },
    getLoadMoreDom(cellContext) {
      const loadMoreDom = document.createElement("span");
      loadMoreDom.className = "icon-button load-more-btn";
      loadMoreDom.addEventListener("mousedown", e => {
        // 切换
        // this.clickExpandCollapseBtn(e, cellContext);
        const { rowId } = cellContext;
        const rowData = this.getRowDataById(rowId);
        const { pos, level } = rowData;
        this.requestChildrenData({
          id: rowId,
          offset: pos,
          limit: this.$attrs.paginationInfo.pageSize,
          level
        }).then(res => {
          // 删除 “加载更多”行
          const loadMoreRowIndex = this.showTableData.findIndex(
            item => item.id === rowId
          );
          this.deleteRowData([rowData], loadMoreRowIndex);
          this.insetRowData(res, pos + 1, level, rowId);
        });
        e.stopPropagation();
      });
      return loadMoreDom;
    },
    clickExpandCollapseBtn(e, cellContext) {
      this.currScrollTop = this.$refs.grid.$refs.simpleGrid.$el.querySelectorAll(
        ".ht_master .wtHolder"
      )[0].scrollTop;
      const { target: currDom } = e;
      const { className, classList } = currDom;
      const [collapseClass, expandClass] = EXPAND_STATUS;
      const { rowId } = cellContext;
      const status = className.indexOf(expandClass) !== -1; // true 展开    false 收起
      const rowData = this.getRowDataById(rowId);
      if (status) {
        // 折叠操作
        classList.remove(expandClass);
        classList.add(collapseClass);
        this.closeEvents(rowData);
        this.expandedKeys.splice(this.expandedKeys.indexOf(rowId), 1);
      } else {
        // 展开操作
        classList.remove(collapseClass);
        classList.add(expandClass);
        this.expandEvents(rowData);
        this.expandedKeys.push(rowId);
      }
      this.$emit("expand", this.expandedKeys);
    },
    // 异步展开
    expandEvents(rowData) {
      const { level, id } = rowData;
      const { paginationInfo } = this.$attrs;
      this.requestChildrenData({
        id,
        limit: paginationInfo.pageSize,
        offset: 0,
        level
      }).then(res => {
        this.insetRowData(res, 0, level, id);
      });
    },
    // 插入数据
    insetRowData(res, startIndex, level, id) {
      const { data, hasMore } = res.data;
      data.forEach((item, index) => {
        item.pos = startIndex + index;
        this.mapData[item.id] = item;
      });
      const addRowData = [...data];
      if (hasMore) {
        addRowData.push(this.getEmptyRowDataByLevel(level));
      }

      const rowIndex = this.getRowIndexById(id.v);
      addRowData.forEach((item, index) => {
        this.$set(this, "addRow", item);
        this.showTableData.splice(rowIndex + 1, 0, item);
        this.$nextTick(() => {
          this.$refs.grid.$refs.simpleGrid.insertRows(rowIndex + index + 2, 1);
        });
        // setTimeout(index => {
        //   this.$refs.grid.$refs.simpleGrid.insertRows(rowIndex + index + 2, 1);
        // }, 300, index);
        // this.$nextTick(() => {
        // });
      });
      this.showTableData.splice(rowIndex + 1, 0, ...addRowData);
      // this.$set(this, "addRow", addRowData);
      // this.$refs.grid.$refs.simpleGrid.insertRows(startIndex, 1);
    },
    getRowIndexById(id) {
      return this.showTableData.findIndex(item => item.id.v === id);
    },
    async requestChildrenData(params) {
      const { id, limit, offset, level } = params;
      return await this.getChildrenData({ id, limit, offset, level });
    },
    // 获取空数据
    getEmptyRowDataByLevel(level) {
      const emptyRowData = {};
      const { dataIndex } = this.$attrs.columns[0];
      const SPECIAL_FIELDS = ["id", "objectId", "key", "level", dataIndex];
      const ident = LOAD_MORE_IDENT + AppUtils.generateUniqueId();
      const tempObj = {
        v: "",
        rowkey: "",
        objectId: ""
      };
      const addPropToObj = {
        addEmptyObj(targetObj, key) {
          if (SPECIAL_FIELDS.slice(0, 1).indexOf(key) !== -1) {
            targetObj[key] = tempObj;
          } else if (key === "children") {
            targetObj[key] = [];
          }
        },
        addLevel(targetObj, key, level) {
          if (key === "level") {
            targetObj[key] = level;
          }
        },
        addIdent(targetObj, key) {
          if (SPECIAL_FIELDS.slice(0, 2).indexOf(key) !== -1) {
            targetObj[key] = ident;
          }
        },
        addLoadMoreWord(targetObj, key) {
          if (key === ident) {
            targetObj[key] = Object.assign(tempObj, {
              v: this.$t_common("load_more")
            });
          }
        }
      };
      Object.keys(this.showTableData[0]).forEach(key => {
        addPropToObj.addEmptyObj(emptyRowData, key);
        addPropToObj.addLevel(emptyRowData, key, level);
        addPropToObj.addIdent(emptyRowData, key);
        addPropToObj.addLoadMoreWord(emptyRowData, key);
        emptyRowData.hasChildren = false;
      });
    },
    // 闭合事件
    closeEvents(rowData) {
      const { id, parentId } = rowData;
      const { paginationInfo } = this.$attrs;
      const startRow = Object.keys(this.mapData).findIndex(
        currId => currId === id
      );
      const children = this.getChildrenByPId(parentId.v);
      const childrenLen = children.length;
      this.deleteRowData(children, startRow);
      const tableDataLen = Object.keys(this.mapData).length;
      this.$refs.grid.$refs.simpleGrid.removeRows(startRow, childrenLen);
      // 判断 是否还有更多 数据，如果是，则请求向后端请求数据
      if (paginationInfo.hasMore) {
        this.loadMore({
          offset: tableDataLen + 1,
          limit: childrenLen
        });
      }
    },
    deleteRowData(children, startRow) {
      const ids = children.map(item => item.id);
      this.$refs.grid.$refs.simpleGrid.removeRows(startRow, children.length);
      this.showTableData = this.showTableData.filter(
        item => ids.indexOf(item.id) !== -1
      );
      this.$set(this, "showTableData", this.showTableData);
    },
    // 根据pid 获取所有（当前表格数据的）子项
    getChildrenByPId(pId) {
      const mapData = this.mapData;
      const listData = Object.values(this.mapData);
      const childrens = [];
      listData.forEach(rowData => {
        const { parentId, id } = rowData;
        if (parentId.v !== ROOT_PID && pId) {
          const currPId = parentId.v;
          let isContinue = true;
          while (isContinue) {
            const currRowData = mapData[currPId];
            const { parentId: ppId } = currRowData;
            if (ppId.v === pId) {
              isContinue = false;
              childrens.push(currRowData);
              delete this.mapData[currPId];
            } else if (ppId.v === ROOT_PID || ppId.v === "") {
              isContinue = false;
            }
          }
        } else if (parentId.v === pId) {
          delete this.mapData[id];
          childrens.push(rowData, id);
        }
      });
      return childrens;
    }
  }
};
</script>
<style lang="less">
.tree-grid-cont td.simple-cell-readonly {
  background-color: @yn-component-background;
}
</style>
<style lang="less" scoped>
.tree-grid-cont {
  width: 100%;
  height: 100%;
  /deep/i.row-header-icon-btn {
    width: auto;
    margin: 0 8px;
  }
}
</style>
