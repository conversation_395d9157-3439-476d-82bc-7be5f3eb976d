<template>
  <yn-modal
    :title="$t_process('select_members_title')"
    :visible="true"
    @cancel="handleCancel"
  >
    <template slot="footer">
      <yn-button key="back" @click="handleCancel">
        {{ $t_common("cancel") }}
      </yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        {{ $t_common("ok") }}
      </yn-button>
    </template>
    <yn-alert
      v-show="tipsWord"
      class="transfer-tips"
      type="info"
      :showIcon="true"
    >
      <span slot="message">
        {{ tipsWord }}
      </span>
      <yn-icon-svg slot="icon" type="information" />
    </yn-alert>
    <dimension-transfer
      ref="transfer"
      :dimInfo="dimInfo"
      :subsetForm="subsetForm"
    />
  </yn-modal>
</template>
<script>
import { mapActions } from "vuex";
import DimensionTransfer from "./DimensionTransfer.vue";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import "yn-p1/libs/components/yn-alert/";
export default {
  name: "Addmember",
  components: { DimensionTransfer },
  props: {
    dimInfo: {
      type: Object,
      default() {
        return {
          dimId: "123",
          dimName: "123"
        };
      }
    },
    ifEmptySelectedAll: {
      type: Boolean,
      default: true
    },
    selectedData: {
      type: Object,
      default: () => {}
    },
    closeAnalysis: Boolean,
    closeTransfer: {
      type: Function,
      default() {
        return () => {};
      }
    },
    // 是否需要过滤掉共享成员
    filterSharedMember: {
      type: Boolean,
      default: false
    },
    tipsWord: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      submitLoading: false,
      subsetForm: {
        show: true,
        type: "edit",
        item: {}
      }
    };
  },
  computed: {},
  created() {},
  methods: {
    ...mapActions("subset/", ["getSubsetExpMember"]),
    formattree(tree, id) {
      const loop = item => {
        item.map(childitem => {
          childitem.label = childitem["title"];
          childitem.attrId = id;
          childitem.type = "attr";
          if (childitem.children && childitem.children.length > 0) {
            childitem.dimMemberHasChildren = true;
            loop(childitem.children);
          }
        });
      };
      tree.map(treelist => {
        if (treelist.constructor === Object) {
          treelist.label = treelist["title"];
          treelist.attrId = id;
          treelist.type = "attr";
          if (treelist.children && treelist.children.length > 0) {
            treelist.dimMemberHasChildren = true;
            loop(treelist.children);
          }
        }
      });
    },
    handleCancel() {
      if (this.closeAnalysis) {
        this.closeTransfer();
      } else {
        this.$emit("closeTransfer");
      }
    },
    async handleSubmit() {
      this.submitLoading = true;
      // 调用关闭回调 并传入表达式
      const expression = this.$refs.transfer.handleSubMitEXP();
      const dataList = {
        memberType: this.$t_common("member"),
        member: this.$t_common("member"),
        level: this.$t_common("level"),
        attr: this.$t_common("attribute"),
        subset: this.$t_common("subset"),
        variable: this.$t_common("variable")
      };
      const hasData = Object.keys(dataList).some(key => {
        return expression[key] && expression[key].length > 0;
      });
      // const bool = Object.keys(expression).every(item => !expression[item]);
      if (!this.ifEmptySelectedAll) {
        if (!hasData && !expression.allMember) {
          this.submitLoading = false;
          UiUtils.errorMessage(this.$t_common("dim_not_empty"));
          return;
        }
      }
      if (this.closeAnalysis) {
        await this.closeTransfer(expression);
      } else {
        this.$emit("closeTransfer", expression);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.mdd-transfer {
  li,
  div,
  span {
    font-size: @yn-font-size-base;
  }
}
/deep/.ant-modal {
  width: auto !important;
}
.transfer-tips {
  margin-top: -0.625rem;
  margin-bottom: 0.625rem;
  height: 2.5rem;
  &.ant-alert-info {
    background-color: @yn-link-bg-color;
    border-radius: 0.25rem;
    // border: 1px solid @yn-border-color-base;
  }
  .ant-alert-message {
    font-size: 0.875rem;
    color: @yn-text-color;
  }
}
</style>
