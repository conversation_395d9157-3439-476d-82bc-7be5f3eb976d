<!-- tab 模式 -->
<template>
  <yn-modal
    :title="title || $t_process('select_members_title')"
    :visible="true"
    @cancel="handleCancel"
  >
    <template slot="footer">
      <yn-button key="back" @click="handleCancel">
        {{ $t_common("cancel") }}
      </yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        {{ $t_common("ok") }}
      </yn-button>
    </template>
    <yn-alert
      v-show="tipsWord"
      class="transfer-tips"
      type="info"
      :showIcon="true"
    >
      <span slot="message">
        {{ tipsWord }}
      </span>
      <yn-icon-svg slot="icon" type="information" />
    </yn-alert>
    <yn-radio-group
      :value="value"
      size="large"
      buttonStyle="solid"
      class="yn-tabs-radio-button transfer-tab"
      @change="changeTab"
    >
      <yn-radio-button
        v-for="item in dimInfos"
        :key="item.dimId"
        class="tab-item"
        :value="item.dimId"
      >
        {{ item.dimName }}
      </yn-radio-button>
    </yn-radio-group>
    <div
      v-for="item in dimInfos"
      v-show="item.dimId === value"
      :key="item.dimId"
    >
      <dimension-transfer
        :ref="'transfer' + item.dimId"
        v-bind="$attrs"
        :dimInfo="item"
        :subsetForm="subsetForm[item.dimId]"
      />
    </div>
    <!-- <yn-tabs class="transfer-tab" @change="changeTab">
      <yn-tab-pane
        v-for="item in dimInfos"
        :key="item.dimId"
        :tab="item.dimName"
      >
        <dimension-transfer
          :ref="'transfer' + item.dimId"
          v-bind="$attrs"
          :dimInfo="item"
          :subsetForm="subsetForm[item.dimId]"
        />
      </yn-tab-pane>
    </yn-tabs> -->
  </yn-modal>
</template>
<script>
import { mapActions } from "vuex";
import DimensionTransfer from "./DimensionTransfer.vue";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import "yn-p1/libs/components/yn-alert/";
import "yn-p1/libs/components/yn-radio-button/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-tabs/";
import "yn-p1/libs/components/yn-tab-pane/";
export default {
  name: "TabTransfer",
  components: { DimensionTransfer },
  props: {
    title: {
      type: String,
      default: ""
    },
    dimInfos: {
      type: Array,
      default() {
        return [
          {
            dimId: "",
            dimName: ""
          }
        ];
      }
    },
    required: {
      type: Boolean,
      default: false
    },
    ifEmptySelectedAll: {
      type: Boolean,
      default: true
    },
    selectedData: {
      type: Object,
      default: () => {}
    },
    closeAnalysis: {
      type: Boolean,
      default: false
    },
    closeTransfer: {
      type: Function,
      default() {
        return () => {};
      }
    },
    // 是否需要过滤掉共享成员
    filterSharedMember: {
      type: Boolean,
      default: false
    },
    tipsWord: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      value: "",
      submitLoading: false,
      subsetForm: {}
    };
  },
  computed: {},
  watch: {
    dimInfos: {
      handler() {
        this.subsetForm = this.dimInfos.reduce((pre, next) => {
          pre[next.dimId] = {
            show: true,
            type: "edit",
            item: {}
          };
          return pre;
        }, {});
        this.value = this.dimInfos[0].dimId;
      },
      immediate: true
    }
  },
  created() {},
  methods: {
    ...mapActions("subset/", ["getSubsetExpMember"]),
    changeTab(event) {
      this.value = event.target.value;
    },
    handleCancel() {
      if (this.closeAnalysis) {
        this.closeTransfer();
      } else {
        this.$emit("closeTransfer");
      }
    },
    async handleSubmit() {
      this.submitLoading = true;
      // 调用关闭回调 并传入表达式
      const expressions = this.dimInfos.map(item => {
        return this.$refs["transfer" + item.dimId]
          ? this.$refs["transfer" + item.dimId][0].handleSubMitEXP()
          : {};
      });
      const keys = Object.keys({
        memberType: this.$t_common("member"),
        member: this.$t_common("member"),
        level: this.$t_common("level"),
        attr: this.$t_common("attribute"),
        subset: this.$t_common("subset"),
        variable: this.$t_common("variable")
      });
      const hasData = expressions.some(expression => {
        return keys.some(key => {
          return (
            (expression[key] && expression[key].length > 0) ||
            expression.allMember
          );
        });
      });
      if (!this.ifEmptySelectedAll && this.required && !hasData) {
        this.submitLoading = false;
        UiUtils.errorMessage(this.$t_common("dim_not_empty"));
        return;
      }
      if (this.closeAnalysis) {
        await this.closeTransfer(expressions);
      } else {
        this.$emit("closeTransfer", expressions);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.mdd-transfer {
  li,
  div,
  span {
    font-size: @yn-font-size-base;
  }
}
/deep/.ant-modal {
  width: auto !important;
}
/deep/ .transfer-new .ant-tabs-tab {
  padding: 0.3125rem 0.75rem !important;
}
/deep/ .ant-modal-body {
  padding-top: 0.75rem;
}
.transfer-tab {
  margin-bottom: 0.75rem;
  .tab-item {
    min-width: 4.625rem;
    text-align: center;
  }
}
.transfer-tips {
  margin-top: -0.625rem;
  margin-bottom: 0.625rem;
  height: 2.5rem;
  &.ant-alert-info {
    background-color: @yn-link-bg-color;
    border-radius: 0.25rem;
    // border: 1px solid @yn-border-color-base;
  }
  .ant-alert-message {
    font-size: 0.875rem;
    color: @yn-text-color;
  }
}
</style>
