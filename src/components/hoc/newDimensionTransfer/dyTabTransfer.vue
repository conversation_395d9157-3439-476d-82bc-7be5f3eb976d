<!-- tab 模式 -->
<template>
  <yn-modal :title="title" :visible="visible" @cancel="handleCancel">
    <template slot="footer">
      <yn-button key="back" @click="handleCancel"> 取消 </yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        确定
      </yn-button>
    </template>
    <yn-alert
      v-show="tipsWord"
      class="transfer-tips"
      type="info"
      :showIcon="true"
    >
      <span slot="message">
        {{ tipsWord }}
      </span>
      <yn-icon-svg slot="icon" type="information" />
    </yn-alert>
    <yn-radio-group
      :value="value"
      size="large"
      buttonStyle="solid"
      class="yn-tabs-radio-button transfer-tab"
      @change="changeTab"
    >
      <yn-radio-button
        v-for="item in dimInfos.slice(0, MAXSHOW)"
        :key="item.dimId"
        class="tab-item"
        :value="item.dimId"
      >
        {{ item.dimName }}
        <svg-icon
          :isIconBtn="false"
          type="close"
          @click.native.stop="handlerRemove(item)"
        />
      </yn-radio-button>
    </yn-radio-group>
    <yn-dropdown v-if="dimInfos.length > MAXSHOW" placement="bottomRight">
      <svg-icon type="ellipsis" class="tab-more" />
      <yn-menu slot="overlay">
        <yn-menu-item
          v-for="item in dimInfos.slice(MAXSHOW)"
          :key="item.dimId"
          :class="['tab-menu', { active: value === item.dimId }]"
          @click="changeItem(item)"
        >
          {{ item.dimName }}
          <svg-icon
            :isIconBtn="false"
            class="tab-menu-close"
            type="close"
            @click.native.stop="handlerRemove(item)"
          />
        </yn-menu-item>
      </yn-menu>
    </yn-dropdown>
    <yn-button type="text" @click="addDim">添加维度</yn-button>
    <div
      v-for="item in dimInfos"
      v-show="item.dimId === value"
      :key="item.dimId"
    >
      <dimension-transfer
        :ref="'transfer' + item.dimId"
        v-bind="$attrs"
        :dimInfo="item"
        :subsetForm="subsetForm[item.dimId]"
      />
    </div>
  </yn-modal>
</template>
<script>
import { mapActions } from "vuex";
import DimensionTransfer from "./DimensionTransfer.vue";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import "yn-p1/libs/components/yn-alert/";
import "yn-p1/libs/components/yn-radio-button/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-tabs/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-tab-pane/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-menu/";
const dataList = {
  memberType: "成员",
  member: "成员",
  level: "层级",
  attr: "属性",
  subset: "子集",
  variable: "变量"
};
export default {
  name: "TabTransfer",
  components: { DimensionTransfer },
  props: {
    title: {
      type: String,
      default: "选择成员"
    },
    visible: {
      type: Boolean,
      default: false
    },
    dimInfos: {
      type: Array,
      default() {
        return [
          {
            dimId: "",
            dimName: ""
          }
        ];
      }
    },
    ifEmptySelectedAll: {
      type: Boolean,
      default: true
    },
    selectedData: {
      type: Object,
      default: () => {}
    },
    closeAnalysis: {
      type: Boolean,
      default: false
    },
    closeTransfer: {
      type: Function,
      default() {
        return () => {};
      }
    },
    // 是否需要过滤掉共享成员
    filterSharedMember: {
      type: Boolean,
      default: false
    },
    tipsWord: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      value: "",
      MAXSHOW: 5,
      submitLoading: false,
      subsetForm: {}
    };
  },
  computed: {},
  watch: {
    visible: {
      handler() {
        this.subsetForm = this.dimInfos.reduce((pre, next) => {
          pre[next.dimId] = {
            show: true,
            type: "edit",
            item: {}
          };
          return pre;
        }, {});
        this.value = this.dimInfos[0] ? this.dimInfos[0].dimId : "";
      },
      immediate: true
    }
  },
  created() {},
  methods: {
    ...mapActions("subset/", ["getSubsetExpMember"]),
    changeTab(event) {
      this.value = event.target.value;
    },
    changeItem(item) {
      this.value = item.dimId;
    },
    handleCancel() {
      if (this.closeAnalysis) {
        this.closeTransfer();
      } else {
        this.$emit("closeTransfer");
      }
    },
    async handleSubmit() {
      this.submitLoading = true;
      // 调用关闭回调 并传入表达式
      const expressions = this.dimInfos.map(item => {
        return this.$refs["transfer" + item.dimId]
          ? this.$refs["transfer" + item.dimId][0].handleSubMitEXP()
          : {};
      });
      const keys = Object.keys(dataList);
      const hasData = expressions.some(expression => {
        return keys.some(key => {
          return (
            (expression[key] && expression[key].length > 0) ||
            expression.allMember
          );
        });
      });
      if (!this.ifEmptySelectedAll && !hasData) {
        this.submitLoading = false;
        UiUtils.errorMessage("维度成员不能为空");
        return;
      }
      if (this.closeAnalysis) {
        await this.closeTransfer(expressions);
      } else {
        this.$emit("closeTransfer", expressions);
      }
      this.submitLoading = false;
    },
    addDim() {
      this.$emit("add");
    },
    handlerRemove(item) {
      this.$emit("remove", item);
      this.$nextTick(() => {
        if (this.value === item.dimId) {
          this.value = this.dimInfos[0] ? this.dimInfos[0].dimId : "";
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.mdd-transfer {
  li,
  div,
  span {
    font-size: @yn-font-size-base;
  }
}
.tab-menu {
  display: flex;
  color: @yn-text-color-secondary;
  &.active {
    color: #fff;
    background: @yn-primary-color;
  }
  .tab-menu-close {
    margin-left: auto;
    padding-left: 0.5rem;
  }
}
/deep/.ant-modal {
  width: auto !important;
}
/deep/ .transfer-new .ant-tabs-tab {
  padding: 0.3125rem 0.75rem !important;
}
/deep/ .ant-modal-body {
  padding-top: 0.75rem;
}
.transfer-tab {
  margin-bottom: 0.75rem;
  .tab-item {
    min-width: 4.625rem;
    margin-right: 8px;
    text-align: center;
  }
}
.transfer-tips {
  margin-top: -0.625rem;
  margin-bottom: 0.625rem;
  height: 2.5rem;
  &.ant-alert-info {
    background-color: @yn-link-bg-color;
    border-radius: 0.25rem;
    // border: 1px solid @yn-border-color-base;
  }
  .ant-alert-message {
    font-size: 0.875rem;
    color: @yn-text-color;
  }
}
</style>
