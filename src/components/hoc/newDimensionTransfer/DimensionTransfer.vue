<template>
  <yn-spin :spinning="spinning">
    <yn-transfer-upgrade
      class="transfer-new"
      :showTap="true"
      :dataTapSource="dataTapSource"
      :union="union"
      :attrUnion="attrUnion"
      :allMember="allMember"
      :dataSource="dataSource"
      :treeDataOriginalSource="treeDataOriginal"
      :targetDataSource="treeData2"
      :excludeData="excludeData"
      :customloader="customloader"
      @change="handleChange"
      @changeExclude="changeExclude"
      @changeAllMember="changeAllMember"
      @changeUnion="changeUnion"
      @checkChange="checkChange"
      @search="searchDimMember"
      @setTreeDataOriginal="setTreeDataOriginal"
    />
  </yn-spin>
</template>

<script>
import api from "@/services/api";
import { mapActions } from "vuex";
import "yn-p1/libs/components/yn-spin/";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import "yn-p1/libs/components/yn-transfer-upgrade/";
import "yn-p1/libs/components/yn-modal/";
import commonService from "@/services/common";
import DIM_INFO from "@/constant/dimMapping";
import { getBooleanValue } from "../../../utils/common";
import UiUtils from "yn-p1/libs/utils/UiUtils";

const formatlist = (tree = []) => {
  const list = [];
  const loop = item => {
    item.forEach(childitem => {
      list.push({
        title: childitem.title,
        key: childitem.key
      });
      if (childitem.children && childitem.children.length > 0) {
        loop(childitem.children);
      }
    });
  };
  loop(tree);
  return list;
};

let apiName = null;

export default {
  name: "DimensionTransfer",
  props: {
    dimInfo: {
      type: Object,
      default: () => {}
    },
    selectedData: {
      type: Object,
      default: () => {}
    },
    customLoaderMember: {
      type: Function, // 自定义树返回
      default: null
    },
    customSearchMember: {
      type: Function, // 自定义搜索返回
      default: null
    }
  },
  data() {
    return {
      dataTapSource: [
        { key: "member", label: this.$t_common("member"), type: "member" },
        { key: "level", label: this.$t_common("level"), type: "level" },
        { key: "attr", label: this.$t_common("attribute"), type: "attr" },
        { key: "subset", label: this.$t_common("subset"), type: "subset" },
        { key: "variable", label: this.$t_common("variable"), type: "variable" }
      ],
      spinning: false,
      union: false,
      attrUnion: true, // 属性的交并集
      dataSource: [], // 备选项
      treeDataOriginal: [],
      treeData2: [], // 已选项数组
      excludeData: [],
      allMember: false,
      dimAttrTypes: [],
      allAttrs: [],
      subsetList: [],
      $audittrailId: DIM_INFO.Audittrail // 审计线索维度id
    };
  },
  watch: {
    dimInfo: {
      async handler(newVal) {
        try {
          await this.getApiName();
        } catch (err) {
          UiUtils.errorMessage(
            `${this.$t_common("error_permissions")}, ${err}`
          );
        }
        this.initData(newVal);
      },
      immediate: true
    }
  },
  methods: {
    ...mapActions("subset/", ["getDySubsetMember"]),
    async getApiName() {
      const { dimId, permissionFilter } = this.dimInfo;
      if (!permissionFilter) return;
      await commonService("queryApiName", dimId).then(res => {
        if (res.data && res.data.apiName) {
          apiName = res.data.apiName;
        }
      });
    },
    resetData() {
      // 重置数据
      this.union = false;
      this.attrUnion = true;
      this.dataSource = [];
      this.treeDataOriginal = [];
      this.treeData2 = [];
      this.excludeData = [];
      this.allMember = false;
    },
    async initData(newVal) {
      // 父级调用 开始初始化组件
      this.spinning = true;
      this.resetData();
      this.currentDim = {
        dimId: newVal.dimId,
        label: newVal.dimName
      };
      if (!this.currentDim.dimId) {
        return;
      }
      await this.getDimAttrTypes();
      this.initDataSource(); // 设置tab项
      await this.initLoadSubset();
      this.getAllAttr(this.currentDim.dimId);
      await this.getDataSource();
      if (newVal.dynamicOrStatic === "dynamic") {
        this.echoTargetDataDynamic(newVal); // 已选成员回显
      } else {
        this.echoTargetData(newVal.selectedItem);
      }
      this.spinning = false;
    },
    initLoadSubset() {
      const { dimId } = this.dimInfo;
      return commonService("getDimSubsets", { dimId, searchValue: "" }).then(
        res => {
          if (res.data.items && res.data.items.length > 0) {
            const childrenArr = res.data.items.map(item => {
              const { dimSubsetName, objectId } = item;
              return {
                isLeaf: true,
                key: objectId,
                title: dimSubsetName,
                type: "subset",
                value: objectId
              };
            });
            this.subsetList = childrenArr;
            this.dataSource.forEach(item => {
              if (item.type === "subset") {
                item.children = childrenArr;
              }
            });
          }
        }
      );
    },
    getAllAttr(dimId) {
      return commonService("getDimAttrs", {
        dimId
      }).then(res => {
        const childrenArr =
          res.data &&
          res.data.items.map(item => {
            const { objectId, dimAttrName, dimAttrTypeId } = item;
            const dimAttrTypeName = this.dimAttrTypes[dimAttrTypeId];
            return {
              key: objectId,
              title: dimAttrName,
              dimAttrTypeName: dimAttrTypeName,
              attrId: objectId
            };
          });
        this.allAttrs = childrenArr;
      });
    },
    async getDimAttrTypes(params) {
      return await commonService("dimAttrTypes").then(res => {
        if (res.status === 200) {
          res.data.items.forEach(dimAttrItem => {
            this.$set(
              this.dimAttrTypes,
              dimAttrItem.objectId,
              dimAttrItem.dimAttrTypeName
            );
          });
        }
      });
    },
    handleChange(data) {
      this.treeData2 = data;
    },
    changeExclude(data) {
      this.excludeData = data;
    },
    changeAllMember(e) {
      this.allMember = e;
    },
    changeUnion(obj) {
      if (obj.type === "all") {
        this.union = obj.union;
      } else {
        this.attrUnion = obj.union;
      }
    },
    checkChange(item) {
      // 判断子集
      const isHasOtherM1 = this.treeData2.filter(
        item => item.type !== "subset"
      );
      const isHasSubset1 = this.treeData2.filter(
        item => item.type === "subset"
      );
      // 如果传过来的是数组
      if (Array.isArray(item)) {
        if (item.length === 0) {
          return;
        }
        // 如果我选的数组里包含子集
        if (
          item.filter(i => i.type === "subset").length > 0 &&
          ((isHasOtherM1 && isHasOtherM1.length > 0) ||
            this.allMember ||
            this.excludeData.length > 0)
        ) {
          UiUtils.infoMessage(this.$t_common("subset_warn1"));
        } else if (
          (item.filter(i => i.type === "subset").length > 0 &&
            isHasSubset1 &&
            isHasSubset1.length > 0) ||
          item.filter(i => i.type === "subset").length > 1
        ) {
          UiUtils.infoMessage(this.$t_common("subset_warn2"));
        } else if (
          isHasSubset1 &&
          isHasSubset1.length > 0 &&
          item.filter(i => i.type === "subset").length === 0
        ) {
          UiUtils.infoMessage(this.$t_common("subset_warn3"));
        } else if (this.checkOnly) {
          // 单选情况下treeData2始终只能有一条数据
          this.treeData2 = [];
          this.treeData2.push(...item);
        } else {
          this.treeData2.push(...item);
        }
      } else {
        if (
          item.type === "subset" &&
          ((isHasOtherM1 && isHasOtherM1.length > 0) ||
            this.allMember ||
            this.excludeData.length > 0)
        ) {
          UiUtils.infoMessage(this.$t_common("subset_warn1"));
        } else if (
          item.type === "subset" &&
          isHasSubset1 &&
          isHasSubset1.length > 0
        ) {
          UiUtils.infoMessage(this.$t_common("subset_warn2"));
        } else if (
          isHasSubset1 &&
          isHasSubset1.length > 0 &&
          item.type !== "subset"
        ) {
          UiUtils.infoMessage(this.$t_common("subset_warn3"));
        } else if (this.checkOnly) {
          // 单选情况下treeData2始终只能有一条数据
          this.treeData2 = [];
          this.treeData2.push(item);
        } else {
          this.treeData2.push(item);
        }
      }
    },
    searchDimMember(value, type, dimMemberType) {
      const fnName = `searchData_${type}`;
      if (value) {
        this[fnName](value, type, dimMemberType);
      } else {
        this.dataSource = this.treeDataOriginal;
      }
    },

    async searchData_member(searchValue, type, dimMemberType) {
      const dimId = this.currentDim.dimId;
      if (this.customSearchMember) {
        this.dataSource = await this.customSearchMember({
          searchValue,
          type,
          dimMemberType,
          dimId
        });
        return;
      }
      let methodName = "transferSearchMember";
      let params = {
        dimMemberType,
        searchValue,
        dimId
      };
      if (this.dimInfo.permissionFilter) {
        methodName = "dimMembersWithAuthSearch";
        const { requestParams = {} } = this.dimInfo;
        params = {
          dimMemberType,
          searchValue,
          apiName,
          ...requestParams
        };
      }
      commonService(methodName, params).then(res => {
        if (res.status !== 200) return;
        // 转换 数据
        let searchRes = [];
        if (res.data && res.data.items) {
          searchRes = res.data.items.map(item => {
            const { dimMemberName, objectId } = item;
            return {
              type: "member",
              isLeaf: true,
              title: dimMemberName,
              key: objectId,
              dimMemberCode: item.dimMemberCode,
              dimMemberShowName: item.dimMemberShowName,
              fromSearch: true,
              shardim: getBooleanValue(item.dimMemberShared),
              scopedSlots: { title: "custom" },
              disabled:
                typeof item.readOnly === "undefined" ? false : item.readOnly
            };
          });
        }
        this.dataSource = searchRes;
      });
    },
    getAttrMember(params) {
      return new Promise((resolve, reject) => {
        commonService("getDimAttrsById", params)
          .then(res => {
            if (res.status === 200) {
              resolve(res);
            } else {
              reject(res.error);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    getDimAttrs(params) {
      return new Promise((resolve, reject) => {
        commonService("getDimAttrs", params)
          .then(res => {
            if (res.status === 200) {
              resolve(res);
            } else {
              reject(res.error);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    async searchData_attr(searchValue) {
      const dimId = this.currentDim.dimId;
      // 处理属性
      const dimAttrTypeMap = {
        "00573eeba3414db1a0fd98e3906334bb": "text",
        "5be13bf15f624d17acdc8884a2b93033": "number",
        "6082a59827054a39abd88d96807d955e": "enum",
        cef7535b40f742b19fd7404d0bee6eb4: "reference",
        "5dd730eb444811ebb7a4fb72802d4a92": "date",
        "11eb96ade50cec6e8a0d23623d3fe01c": "bm" // 别名
      };
      const indexTodimAttrTypeIdMap = {};
      const dimAttrsRes = await DsUtils.get(
        `${api.dimAttrs}?query=dimId='${dimId}'`,
        "get"
      );
      const promises = [];
      dimAttrsRes.data.items.forEach((item, index) => {
        indexTodimAttrTypeIdMap[index] = item.dimAttrTypeId;
        promises.push(
          DsUtils.get(`${api.getDimAttrsBff}/${item.objectId}`, "get")
        );
      });
      const resArr = await Promise.all(promises);
      const searchRes = [];
      resArr.forEach((item, pos) => {
        if (item.data && Array.isArray(item.data)) {
          item.data.forEach(it => {
            if (
              dimAttrTypeMap[indexTodimAttrTypeIdMap[pos]] === "enum" ||
              dimAttrTypeMap[indexTodimAttrTypeIdMap[pos]] === "reference"
            ) {
              const loop = data => {
                data.forEach(item => {
                  if (Array.isArray(item.children) && item.children.length) {
                    loop(item.children);
                  }
                  if (String(item.title).indexOf(searchValue) !== -1) {
                    searchRes.push({
                      key:
                        dimAttrsRes.data.items[pos].objectId + "-" + item.key,
                      attrId: dimAttrsRes.data.items[pos].objectId,
                      title: item.title,
                      type: "attr",
                      isLeaf: true,
                      fromSearch: true
                    });
                  }
                });
              };
              if (it.children && it.children.length) {
                loop(it.children);
              }
              if (String(it.title).indexOf(searchValue) !== -1) {
                searchRes.push({
                  key: dimAttrsRes.data.items[pos].objectId + "-" + it.key,
                  attrId: dimAttrsRes.data.items[pos].objectId,
                  title: it.title,
                  type: "attr",
                  isLeaf: true,
                  fromSearch: true
                });
              }
            } else if (String(it).indexOf(searchValue) !== -1) {
              searchRes.push({
                key: dimAttrsRes.data.items[pos].objectId + "-" + it, // 标记唯一key
                attrId: dimAttrsRes.data.items[pos].objectId,
                title: it,
                type: "attr",
                isLeaf: true,
                fromSearch: true
              });
            }
          });
        }
      });
      this.dataSource = searchRes;
    },
    async searchData_subset(searchValue) {
      const dimId = this.currentDim.dimId;
      // 处理变量
      commonService("getDimSubsets", { dimId, searchValue }).then(res => {
        if (res.data.items && res.data.items.length > 0) {
          this.dataSource = res.data.items.map(item => {
            const { dimSubsetName, objectId } = item;
            return {
              isLeaf: true,
              key: objectId,
              title: dimSubsetName,
              type: "subset",
              value: objectId
            };
          });
        }
      });
    },
    async searchData_variable(searchValue, type, dimMemberType) {
      const dimId = this.currentDim.dimId;
      // 处理变量
      const valUrl = searchValue
        ? `${api["getVariables"]}?dimId=${dimId}&variableName=${searchValue}`
        : `${api["getVariables"]}?dimId=${dimId}`;
      const varRes = await DsUtils.get(valUrl, "get");
      let searchRes = [];
      if (varRes.data) {
        searchRes = varRes.data.map(item => {
          const { text, variableDimMemberId, objectId } = item;
          return {
            isLeaf: true,
            key: `${objectId}_${variableDimMemberId}`,
            title: text,
            type: "variable",
            fromSearch: true
          };
        });
      }
      this.dataSource = searchRes;
    },
    async searchData_level(searchValue, type, dimMemberType) {
      const dimId = this.currentDim.dimId;
      const levelRes = await DsUtils.get(`${api.getLevels}/${dimId}`, "get");
      levelRes.data = levelRes.data.filter(v => {
        return v.toString().indexOf(searchValue) !== -1;
      });
      let searchRes = [];
      if (levelRes && levelRes.data) {
        searchRes = levelRes.data.map(item => {
          return {
            isLeaf: true,
            key: `${dimId}_level_${item}`,
            title: item + "",
            type: "level",
            fromSearch: true
          };
        });
      }
      this.dataSource = searchRes;
    },
    setTreeDataOriginal(treeData) {
      // 主要是为了异步加载数据事同步，否则搜索时候清空，会导致treeData里的数据不全
      this.treeDataOriginal = treeData;
    },
    customloader(treeNode) {
      const { type } = treeNode;
      return this[`customloader_${type}`](treeNode);
    },
    customloader_member(treeNode) {
      const { dimId, key } = treeNode;
      if (this.customLoaderMember) {
        return new Promise(resolve => {
          this.customLoaderMember({
            parentId: key,
            dimId: this.currentDim.dimId
          })
            .then(res => {
              treeNode.children = res;
              resolve(res);
            })
            .catch(() => {
              resolve([]);
            });
        });
      }
      let methodName = "getDimMemberByMemberId";
      let reqParams = {
        dimId,
        dimMemberParentId: key
      };
      if (dimId === this.$data.$audittrailId) {
        methodName = "queryDimMembersByCondition";
      }
      if (this.dimInfo.permissionFilter) {
        methodName = "dimMembersWithAuth";
        const { requestParams = {} } = this.dimInfo;
        reqParams = {
          apiName,
          parentId: key,
          ...requestParams
        };
      }
      return new Promise((resolve, reject) => {
        commonService(methodName, reqParams)
          .then(res => {
            let dimMemberList = [];
            if (res.data && res.data.items) {
              dimMemberList = res.data.items.map(item => {
                const {
                  dimMemberName,
                  objectId,
                  dimMemberDbCode,
                  dimMemberHasChildren,
                  dimMemberShared,
                  dimMemberCode,
                  dimMemberShowName,
                  readOnly
                } = item;
                return {
                  dimId,
                  title: dimMemberName,
                  dimMemberCode,
                  dimMemberShowName,
                  key: objectId,
                  type: "member",
                  dimMemberDbCode,
                  isLeaf: !getBooleanValue(dimMemberHasChildren),
                  value: dimMemberName,
                  shardim: getBooleanValue(dimMemberShared),
                  scopedSlots: { title: "custom" },
                  disabled: typeof readOnly === "undefined" ? false : readOnly
                };
              });
            }
            treeNode.children = dimMemberList;
            resolve(dimMemberList);
          })
          .catch(() => {
            resolve([]);
          });
      });
    },
    customloader_attr(treeNode) {
      const { attrId, dimAttrTypeName, key } = treeNode;
      return new Promise((resolve, reject) => {
        if (!attrId && key && key === this.currentDim.dimId) {
          // 根节点
          commonService("getDimAttrs", {
            dimId: key
          })
            .then(res => {
              if (key === this.$data.$audittrailId) {
                res.data.items = res.data.items.filter(
                  item => item.dimAttrCode === "Adjustment"
                );
              }
              const childrenArr =
                res.data &&
                res.data.items.map(item => {
                  const { objectId, dimAttrName, dimAttrTypeId } = item;
                  const dimAttrTypeName = this.dimAttrTypes[dimAttrTypeId];
                  return {
                    type: "attr",
                    key: objectId,
                    title: dimAttrName,
                    dimAttrTypeName: dimAttrTypeName,
                    isLeaf: false,
                    disableCheckbox: true,
                    attrId: objectId
                  };
                });
              resolve(childrenArr || []);
              this.dataSource[1].children = childrenArr;
              this.allAttrs = childrenArr;
            })
            .catch(() => {
              resolve([]);
            });
        }
        if (attrId) {
          commonService("getDimAttrsById", {
            attrId
          })
            .then(res => {
              const attrs = [];
              if (res.data && res.data.length > 0) {
                if (typeof res.data[0] === "object") {
                  const tempAttr = res.data;
                  this._recursionTree(res.data, attrId);
                  Array.prototype.push.apply(attrs, tempAttr);
                } else {
                  res.data.map(item => {
                    attrs.push({
                      attrId: attrId,
                      isLeaf: true,
                      key: attrId + "_" + (item.key || item), // 用于唯一的key值
                      title: item,
                      type: "attr",
                      dimAttrTypeName: dimAttrTypeName,
                      attrKey: attrId + "_" + (item.key || item)
                    });
                  });
                }
              }
              if (attrs.length === 0) {
                treeNode.isLeaf = true;
              }
              treeNode.children = attrs;
              return resolve(attrs);
            })
            .catch(() => {
              resolve([]);
            });
        }
      });
    },
    customloader_level(treeNode) {
      return new Promise((resolve, reject) => {
        const { key } = treeNode;
        commonService("getLevels", {
          dimId: key
        })
          .then(res => {
            const dimId = this.currentDim && this.currentDim.dimId;
            const levelarr =
              res.data &&
              res.data.map(item => {
                return {
                  key: `${dimId}_level_${item}`,
                  label: item + "",
                  type: "level",
                  title: item + "",
                  isLeaf: true
                };
              });
            resolve(levelarr || []);
          })
          .catch(() => {
            resolve([]);
          });
      });
    },
    initDataSource() {
      this.dataSource[0] = {
        key: this.currentDim.dimId,
        label: this.$t_common("level"),
        title: this.$t_common("level"),
        type: "level",
        children: [],
        dimMemberHasChildren: true,
        disableCheckbox: true
      };
      this.dataSource[1] = {
        key: this.currentDim.dimId,
        label: this.$t_common("attribute"),
        title: this.$t_common("attribute"),
        type: "attr",
        children: [],
        dimMemberHasChildren: true,
        disableCheckbox: true
      };
      this.dataSource.push({
        title: this.$t_common("all_members"),
        key: "all",
        isLeaf: true,
        id: "all",
        type: "member"
      });
      this.dataSource.push({
        key: this.currentDim.dimId,
        objectId: this.currentDim.dimId,
        dimId: this.currentDim.dimId,
        label: this.currentDim.label,
        title: this.currentDim.label,
        type: "member",
        dimMemberHasChildren: true,
        children: [],
        disableCheckbox: true
      });
    },
    async getDataSource() {
      const dimId = this.currentDim && this.currentDim.dimId;
      await commonService("getVariables", {
        dimId
      }).then(res => {
        if (res.status === 200 && res.data) {
          res.data.map(item => {
            const { text, objectId } = item;
            this.dataSource.push({
              isLeaf: true,
              key: objectId,
              title: text,
              type: "variable",
              value: objectId
            });
          });
        }
      });
      await commonService("getSubsetById", dimId).then(res => {
        if (res.status === 200 && res.data) {
          res.data.items.map(item => {
            const { dimSubsetName, objectId } = item;
            this.dataSource.push({
              isLeaf: true,
              key: objectId,
              title: dimSubsetName,
              type: "subset",
              value: objectId
            });
          });
        }
      });
      this.dataSource = [...this.dataSource];
      this.treeDataOriginal = this.dataSource;
    },
    async echoTargetData(selectedList) {
      this.treeData2 = selectedList;
    },
    async echoTargetDataDynamic(newVal) {
      // 点击编辑动态子集获取动态子集下的维度成员
      if (!newVal.members || !Object.keys(newVal.members).length) return;
      this.dimExps = JSON.parse(JSON.stringify(newVal.members));
      const {
        attr,
        attrUnion,
        level,
        memberType,
        union,
        variable,
        subset,
        excludeMember,
        excludeLevel,
        excludeVariable,
        excludeAttr,
        allMember
      } = this.dimExps;
      this.attrUnion = attrUnion;
      this.union = union;
      this.allMember = allMember;
      this.setTargetDataSource_memberType(memberType || [], "treeData2");
      this.setTargetDataSource_attr(attr || [], "treeData2");
      this.setTargetDataSource_level(level || [], "treeData2");
      this.setTargetDataSource_variable(variable || [], "treeData2");
      this.setTargetDataSource_subset(subset || [], "treeData2");
      this.setTargetDataSource_memberType(excludeMember || [], "excludeData");
      this.setTargetDataSource_attr(excludeAttr || [], "excludeData");
      this.setTargetDataSource_level(excludeLevel || [], "excludeData");
      this.setTargetDataSource_variable(excludeVariable || [], "excludeData");
    },
    setTargetDataSource_memberType(memberTypeData, dataName) {
      // 动态子集回显成员
      memberTypeData.map(item => {
        this.defaultSelectedRule = "self"; // 默认穿梭规则维成员
        switch (item.memberTypeValue) {
          case "self":
            item.memberType = 1;
            break;
          case "descendant":
            item.memberType = 2;
            break;
          case "self-and-descendant":
            item.memberType = 0;
            break;
          case "self-and-son":
            item.memberType = 4;
            break;
          case "leaf":
            item.memberType = 3;
            break;
          case "parent":
            item.memberType = 7;
            break;
          case "self-and-parent":
            item.memberType = 8;
            break;
          case "ancestor":
            item.memberType = 9;
            break;
          case "self-and-ancestor":
            item.memberType = 10;
            break;
          default:
            item.memberType = 5;
        }
        this[dataName].push({
          objectId: item.memberId,
          value: item.memberId,
          key: `${item.memberId}-${item.memberTypeValue}`,
          label: item.memberName,
          title: item.memberName,
          memberTypeValue: item.memberTypeValue,
          memberType: item.memberType,
          type: "member",
          memberTypeName: item.memberTypeName,
          shardim: item.dimMemberShared === true,
          scopedSlots: { title: "custom" }
        });
      });
    },
    setTargetDataSource_attr(attrData, dataName) {
      // 动态子集回显属性
      attrData.map(item => {
        const { attrId, attrValue, attrType } = item;
        Array.isArray(attrValue) &&
          attrValue.map(async valItem => {
            let label = valItem;
            if (attrType === "关联" || attrType === "枚举") {
              await this.getAttrMember({
                attrId: item.attrId
              }).then(res => {
                const enmulist = formatlist(res.data);
                const enmuItem = enmulist.filter(item => item.key === valItem);
                label = enmuItem[0].title;
              });
            }
            this[dataName].push({
              attrId,
              key: attrId + "_" + valItem,
              label,
              title: `属性(${label})`,
              type: "attr",
              value: attrId + "_" + valItem
            });
          });
      });
    },
    setTargetDataSource_level(levelData, dataName) {
      // 动态子集回显层级
      const dimId = this.currentDim && this.currentDim.dimId;
      levelData.map(item => {
        this[dataName].push({
          key: `${dimId}_level_${item}`,
          label: item + "",
          title: `层级(${item})`,
          type: "level",
          memberTypeName: "层级"
        });
      });
    },
    setTargetDataSource_variable(variableData, dataName) {
      // 动态子集回显变量
      variableData.map(item => {
        const { memberId, variableId, variableName } = item;
        this[dataName].push({
          key: `${variableId}_${memberId}`,
          label: variableName,
          title: `变量(${variableName})`,
          type: "variable",
          value: `${variableId}_${memberId}`
        });
      });
    },
    setTargetDataSource_subset(subsetData, dataName) {
      subsetData = subsetData.map(subsetkey => {
        return {
          subsetName: this.subsetList.filter(item => item.key === subsetkey)[0]
            .title,
          key: subsetkey
        };
      });
      subsetData.map(item => {
        const { key, subsetName } = item;
        this[dataName].push({
          key: key,
          label: subsetName,
          title: `子集(${subsetName})`,
          type: "subset",
          value: key
        });
      });
    },
    handleSubMitEXP() {
      // 获取提交表达式
      const obj = {
        attr: [],
        level: [],
        memberType: [],
        member: [],
        variable: [],
        excludeAttr: [],
        excludeLevel: [],
        excludeMember: [],
        excludeVariable: [],
        subset: []
      };
      this.treeData2.forEach((item, key) => {
        const { type } = item;
        // const dataType = type === "memberType" ? "member" : type;
        const dataType = type === "member" ? "memberType" : type;
        this[`getVal_${type}`](item, obj[dataType]);
      });
      this.excludeData.forEach(item => {
        const { type } = item;
        let excludeType;
        if (type === "attr") {
          excludeType = "excludeAttr";
        }
        if (type === "level") {
          excludeType = "excludeLevel";
        }
        if (type === "member") {
          excludeType = "excludeMember";
        }
        if (type === "variable") {
          excludeType = "excludeVariable";
        }
        this[`getVal_${type}`](item, obj[excludeType]);
      });
      const submitExps = {};
      // 过滤，将有值的传出
      for (const k in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, k) && obj[k].length) {
          submitExps[k] = obj[k];
        }
      }
      submitExps.attrUnion = this.attrUnion;
      submitExps.union = this.union;
      submitExps.allMember = this.allMember;
      return submitExps;
    },
    getVal_attr(item, data) {
      const { attrId, value, label } = item;
      const alreadyExist = data.find(i => {
        return i.attrId === attrId;
      });
      const currentAttr = this.allAttrs.find(i => {
        return i.attrId === attrId;
      });
      let val;
      if (
        (currentAttr.dimAttrTypeName === "枚举" ||
          currentAttr.dimAttrTypeName === "关联") &&
        value.indexOf("_") !== -1
      ) {
        val = value.slice(value.indexOf("_") + 1);
      } else {
        val = label;
      }
      if (alreadyExist) {
        alreadyExist.attrValue.push(val);
      } else {
        data.push({
          attrId: attrId,
          attrValue: [val],
          attrType: currentAttr.dimAttrTypeName
        });
      }
    },
    getVal_level(item, data) {
      data.push(item.label);
    },
    getVal_member(item, data) {
      const { label, value, memberTypeValue, shardim } = item;
      data.push({
        memberName: label,
        memberTypeValue: memberTypeValue,
        memberId: value,
        dimMemberShared: shardim
      });
    },
    getVal_variable(item, data) {
      const { label, value } = item;
      data.push({
        variableName: label,
        variableId: value.split("_")[0]
      });
    },
    getVal_subset(item, data) {
      const { value } = item;
      data.push(value);
    },
    _recursionTree(data, attrId) {
      for (let i = 0, LEN = data.length; i < LEN; i++) {
        const childrenArr = data[i].children;
        if (childrenArr && childrenArr.length) {
          this._recursionTree(childrenArr, attrId);
          data[i].isLeaf = false;
        } else {
          data[i].isLeaf = true;
        }
        data[i].key = attrId + "_" + (data[i].key || data[i].title);
        data[i].type = "attr";
        data[i].attrId = attrId;
      }
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .c1-transfer__tabs span {
  font-size: @yn-font-size-base;
}
.transfer-new {
  /deep/.c1-mdd-transfer-upgrade {
    .ant-input,
    div,
    ul,
    li,
    span,
    p {
      font-size: @yn-font-size-base !important;
      font-weight: 400;
    }
    .ant-tree-title i {
      margin-right: 5px;
      font-size: @yn-font-size-sm;
      color: @yn-auxiliary-color;
    }
  }
}
</style>
