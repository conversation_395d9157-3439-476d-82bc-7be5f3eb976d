<template>
  <yn-modal
    wrapClassName="transfer-modal"
    :visible="visible"
    :title="$t_process('select_form')"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="transfer-modal-content">
      <div class="content-left">
        <div class="left-header">
          <span class="header-title">{{ $t_process("selectable_range") }}</span>
          <yn-checkbox
            :checked="checkedAll"
            class="header-all"
            @change="onChange"
          >
            {{ $t_common("select_all") }}
          </yn-checkbox>
        </div>
        <yn-input-search
          class="content-search"
          :placeholder="$t_common('please_enter_search_content')"
          @search="searchEvent($event, 'left')"
        />
        <yn-tree
          :checkedKeys="checkedKeys"
          checkable
          checkStrictly
          :expandedKeys="expandedKeys"
          :autoExpandParent="autoExpandParent"
          :selectedKeys="selectedKeys"
          :treeData="leftSearchKey ? leftSearchList : dataJson"
          @expand="onExpand"
          @select="onSelect"
          @check="onCheck"
        >
          <template slot="custom" slot-scope="item">
            <span v-if="item.title.includes(leftSearchKey)">
              <span>{{
                item.title.substr(0, item.title.indexOf(leftSearchKey))
              }}</span>
              <span class="search-key">{{ leftSearchKey }}</span>
              <span>{{
                item.title.substr(
                  item.title.indexOf(leftSearchKey) + leftSearchKey.length
                )
              }}</span>
            </span>
            <span v-else>{{ item.title }}</span>
          </template>
        </yn-tree>
      </div>
      <div class="content-right">
        <div class="right-header">
          <span>{{ $t_process("selected_ranger") }}</span>
          <yn-button type="link" @click="clearAll">
            {{ $t_common("clear") }}
          </yn-button>
        </div>
        <yn-input-search
          class="content-search"
          :placeholder="$t_process('please_search_here')"
          @search="searchEvent($event, 'right')"
        />
        <div class="choose-list">
          <transition-group name="flip-list" tag="span">
            <span
              v-for="(item, index) in rightSearchKey
                ? rightSearchList
                : chooseList"
              :key="item.key"
              class="list-form-name"
              draggable
              @dragstart="dragstart(index)"
              @dragenter="dragenter($event, index)"
              @dragover="dragover($event)"
            >
              <span v-tooltip="{ visibleOnOverflow: true, title: item.title }">
                {{ item.title }}
              </span>
              <svg-icon
                class="item-icon"
                :isIconButton="true"
                :title="$t_common('delete')"
                type="icon-shanchu"
                @onClick="deleteItem(item, index)"
              />
            </span>
          </transition-group>
        </div>
      </div>
    </div>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-tree/";
import "yn-p1/libs/components/yn-icon-button/";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-checkbox/";

import { buildTree } from "../../../utils/taskFlowTemp";

import taskFlowServeice from "@/services/taskFlowTemp";
import { findParentKeys } from "@/utils/taskFlowTemp.js";
import { mapState } from "vuex";

import cloneDeep from "lodash/cloneDeep";
export default {
  name: "TransferLike",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    clickFormInfo: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      expandedKeys: [],
      autoExpandParent: true,
      checkedKeys: [],
      selectedKeys: [],
      chooseList: [],
      dragStartIndex: "", // 拖拽初始索引
      checkedAll: false,
      leftSearchKey: "",
      leftSearchList: [],
      rightSearchKey: "",
      rightSearchList: []
    };
  },
  computed: {
    ...mapState({
      dataJson: state => state.processStore.formList,
      pavingData: state => state.processStore.pavingFormList
    })
  },
  watch: {
    visible: {
      async handler(v) {
        if (v) {
          this.enchoData(this.clickFormInfo);
        }
      }
    },
    checkedKeys: {
      immediate: true,
      handler(val) {
        this.chooseList = val
          .map(key => this.pavingData.find(item => item.key === key))
          .filter(item => !!item);
      }
    },
    checkedAll: {
      immediate: true,
      handler(v) {
        if (v) {
          this.checkedKeys = this.getTreeDataKey();
        } else {
          this.checkedKeys = [];
        }
      }
    }
  },
  methods: {
    // 设置反显（展开父项目、选中已选项）
    enchoData(str) {
      this.leftSearchKey = "";
      if (str) {
        this.checkedKeys = str.split(";");
        this.expandedKeys = findParentKeys(this.pavingData, this.checkedKeys);
      } else {
        this.checkedKeys = [];
        this.expandedKeys = [];
      }
    },
    // 获取所有场景
    getAllSceneList() {
      taskFlowServeice("getSceneList").then(res => {
        // console.log(res);
      });
    },
    handleCancel() {
      this.$emit("update:visible", false);
    },
    handleOk() {
      this.$emit(
        "getForm",
        this.chooseList.map(item => item.key)
      );
      this.handleCancel();
    },
    onExpand(expandedKeys) {
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    onCheck(checkedKeys, info) {
      const { checked } = checkedKeys;
      this.checkedKeys = checked;
    },
    onSelect(selectedKeys, info) {
      this.selectedKeys = selectedKeys;
    },
    deleteItem(item, index) {
      this.checkedKeys.splice(index, 1);
    },
    // 拖拽相关
    dragstart(index) {
      this.dragStartIndex = index;
    },
    dragenter(e, index) {
      e.preventDefault();
      if (this.dragStartIndex !== index) {
        const moveItem = this.chooseList[this.dragStartIndex];
        this.chooseList.splice(this.dragStartIndex, 1);
        this.chooseList.splice(index, 0, moveItem);
        this.dragStartIndex = index;
      }
    },
    dragover(e) {
      e.preventDefault();
    },
    // 获取树行数据的key
    getTreeDataKey() {
      const copyTree = cloneDeep(this.dataJson);
      const keys = [];
      while (copyTree.length) {
        const currentData = copyTree.shift();
        keys.push(currentData.key);
        if (currentData.children && currentData.children.length) {
          copyTree.unshift(...currentData.children);
        }
      }
      return keys;
    },
    onChange(checked, e) {
      this.checkedAll = checked;
    },
    clearAll() {
      this.checkedKeys = [];
      this.checkedAll = false;
    },
    searchEvent(e, type) {
      this[`${type}SearchKey`] = e;
      this.expandedKeys = [];
      if (!e) {
        return;
      }
      const searchList = [];
      if (type === "left") {
        const keys = this.pavingData
          .filter(item => item.title.includes(e))
          .map(item => item.key);
        const parentKeys = findParentKeys(this.pavingData, keys);
        this.expandedKeys = [...new Set(parentKeys)];
        const treeKeys = [...new Set([...keys, ...parentKeys])];
        const treeItem = this.pavingData.filter(item =>
          treeKeys.includes(item.key)
        );
        // 构建树
        this.leftSearchList = buildTree(treeItem);
      } else {
        this.chooseList.forEach(item => {
          if (item.title.includes(e)) {
            searchList.push(item);
          }
        });
        this.rightSearchList = searchList;
      }
    }
  }
};
</script>

<style lang="less">
.transfer-modal {
  .ant-modal {
    width: 728px !important;
  }
  .ant-modal-body {
    padding: 0;
    height: 484px;
  }
}
</style>
<style lang="less" scoped>
.search-key {
  color: @yn-error-color;
}
.flip-list-move {
  transition: transform 1s;
}
.transfer-modal-content {
  display: flex;
  height: 100%;
  .content-left {
    flex: 1;
    height: 100%;
    border-right: 1px solid @yn-border-color-base;
    padding: @yn-padding-xxxl;
    width: 13.75rem;
    /deep/.yn-tree {
      height: 390px;
      overflow: auto;
    }
    .left-header {
      display: flex;
      justify-content: space-between;
      .header-title {
        font-size: @rem14;
        color: @yn-heading-color;
      }
      .header-all {
        /deep/.ant-checkbox + span {
          padding-right: 0;
          font-size: @rem14;
          color: @yn-text-color-secondary;
        }
      }
    }
    .content-search {
      margin: @yn-margin-s 0;
    }
  }
  .content-right {
    flex: 1;
    height: 100%;
    width: 13.75rem;
    padding: @yn-padding-xxxl @yn-padding-xxxl @yn-padding-s;
    .right-header {
      display: flex;
      justify-content: space-between;
      line-height: @rem32;
      margin-top: -@rem6;
    }
    .content-search {
      margin: @yn-margin-xs 0 @yn-margin-s 0;
    }
    .choose-list {
      display: flex;
      flex-direction: column;
      overflow: scroll;
      height: 390px;
      .list-form-name {
        height: @rem32;
        display: inline-block;
        line-height: @rem32;
        padding: 0 @rem10 0 @rem20;
        display: flex;
        justify-content: space-between;
        position: relative;
        .item-icon {
          display: none;
        }
        &:hover {
          background: @yn-table-header-bg;
          cursor: pointer;
          .item-icon {
            font-size: @rem8;
            display: block;
          }
        }
        &:hover::before {
          content: url("../../../image/drag-and-drop.svg");
          position: absolute;
          left: 0px;
          top: 2px;
        }
      }
    }
  }
}
</style>
