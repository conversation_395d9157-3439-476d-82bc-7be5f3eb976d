<template>
  <div
    ref="dimListInput"
    :class="contClass"
    @click="showTransfer($event, showDimList)"
  >
    <div class="input-content">
      <template v-if="dynamicOrStatic === 'static'">
        <template v-if="showDimList.length">
          <span
            v-for="item in showDimList"
            :key="item.dimMemberId"
            class="dim-cont"
            :title="item.dimMemberName"
          >
            {{ item.dimMemberName }}
          </span>
        </template>
        <template v-else>
          <span class="placeholder">{{
            placeholder || $t_common("input_message")
          }}</span>
        </template>
        <span v-if="hideDimNum" class="dim-cont hideNum">{{
          `+${hideDimNum}`
        }}</span>
      </template>
      <div
        v-else
        :class="[
          'input-text',
          dimInfo.members && Object.values(dimInfo.members).length
            ? 'input-text-choose'
            : ''
        ]"
      >
        {{
          dimInfo.members && Object.values(dimInfo.members).length
            ? $t_process("selected_view_members")
            : $t_common("input_select")
        }}
      </div>
      <svg-icon
        class="icon-cont"
        type="icon-chuansuokuang"
        :isIconBtn="false"
        @click="showTransfer($event, showDimList)"
      />
      <add-member
        v-if="visible"
        v-bind="$attrs"
        :closeAnalysis="true"
        :dimInfo="transferData"
        :ifEmptySelectedAll="ifEmptySelectedAll"
        :closeTransfer="closeTransfer"
      />
    </div>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-icon/";
import AddMember from "@/components/hoc/newDimensionTransfer";
import commonService from "@/services/common";
import cloneDeep from "lodash/cloneDeep";
export default {
  name: "ShowDimListInput",
  components: { AddMember },
  props: {
    dimInfo: {
      type: Object,
      default() {
        return {
          selectedItem: []
        };
      }
    },
    ifEmptySelectedAll: {
      type: Boolean,
      default: true
    },
    dynamicOrStatic: {
      type: String,
      default: "static"
    },
    placeholder: {
      type: String,
      default: ""
    },
    analyticExpName: {
      type: String,
      default: "getExpMember"
    }
  },
  data() {
    return {
      exprObj: null,
      showDimList: [],
      hideDimNum: 0,
      dimList: [],
      visible: false,
      contWidth: 0,
      transferData: {}
    };
  },
  computed: {
    contClass() {
      const arr = [
        "dim-list-input",
        this.dynamicOrStatic === "dynamic" ? "expase-text" : ""
      ];
      return arr;
    }
  },
  watch: {
    dimInfo: {
      handler(newVal) {
        const { selectedItem } = newVal;
        if (selectedItem && selectedItem.length) {
          this.setShowDimInfo(cloneDeep(selectedItem));
        } else {
          this.showDimList = [];
          this.hideDimNum = 0;
        }
        this.transferData = cloneDeep(newVal);
      },
      immediate: true,
      deep: true
    },
    visible: {
      handler(nv) {
        if (nv && Object.keys(this.dimInfo).length) {
          const data = cloneDeep(this.dimInfo);
          this.transferData = data;
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.contWidth = this.getContWidth();
      this.setShowDimInfo(this.dimList);
    });
  },
  methods: {
    setShowDimInfo(dimList) {
      this.dimList = [...dimList];
      if (dimList.length) {
        const showDimList = this.getShowDimArr(dimList);
        this.showDimList = [...showDimList];
        this.hideDimNum = dimList.length - showDimList.length;
      }
    },
    getShowDimArr(dimList = []) {
      let index = 0;
      let noStrop = true;
      const showDimList = [];
      const MAX_WIDTH = 84; // 块的  最大宽度
      const BLOCK_MARGIN = 2;
      const BLOCK_PADDING_BORDER = 6;
      let cumulativeWidth = 0; // 累计有效宽度
      while (noStrop) {
        const currDimName =
          dimList[index].dimMemberName || dimList[index].label;
        let wordWidth = this.getPxWidth(currDimName);
        if (wordWidth > MAX_WIDTH) {
          // 不能 超过最大宽度
          wordWidth = MAX_WIDTH;
        }
        const totalWidth =
          cumulativeWidth + wordWidth + BLOCK_MARGIN + BLOCK_PADDING_BORDER; // 累计宽度

        if (totalWidth < this.contWidth) {
          cumulativeWidth = totalWidth;
          showDimList.push(dimList[index]);
        } else {
          noStrop = false;
        }
        if (index === dimList.length - 1) {
          noStrop = false;
        }
        index++;
      }
      const surplusWordWidth =
        this.getPxWidth(`+${dimList.length - showDimList.length}`) +
        BLOCK_MARGIN;
      if (cumulativeWidth + surplusWordWidth > this.contWidth) {
        showDimList.pop();
      }
      return showDimList;
    },
    getContWidth() {
      const MARGIN_PADDING_BORDER_WIDTH = 45;
      return (
        (this.$refs.dimListInput && this.$refs.dimListInput.offsetWidth) -
        MARGIN_PADDING_BORDER_WIDTH
      );
    },
    getPxWidth(word) {
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");
      context.font = "bold 14px Arial";
      var dimension = context.measureText(word);
      return dimension.width;
    },
    showTransfer() {
      this.visible = true;
    },
    async closeTransfer(exprObj) {
      this.exprObj = exprObj;
      if (!exprObj) {
        this.visible = false;
        return;
      }
      if (this.dynamicOrStatic === "dynamic") {
        this.$emit("getExp", exprObj);
        this.visible = false;
        return;
      }
      const { dimId } = this.dimInfo;
      const dimMemberExps = this.formatRequestParams(exprObj);
      const apiName = this.analyticExpName;
      const parseParams = {
        expDtoList: [
          {
            dimId,
            dimMemberExps: dimMemberExps
          }
        ]
      };
      if (this.$attrs.filterSharedMember) {
        // 是否需要过滤掉共享成员
        parseParams.needFilterShared = true;
      }
      // 解析是否需要添加 权限参数
      if (this.dimInfo.permissionFilter) {
        parseParams.realData = true;
      }
      await commonService(apiName, parseParams).then(res => {
        this.setData(res.data);
        this.$nextTick(() => {
          this.visible = false;
        });
      });
    },
    formatRequestParams(exprObj = {}) {
      const expr = cloneDeep(exprObj);
      const dimMemberExps = {};
      if (Object.keys(expr).length) {
        Object.keys(expr).map(item => {
          let propsName = item;
          if (item === "memberType") {
            propsName = "member";
          }
          dimMemberExps[propsName] = expr[item];
        });
      }
      return JSON.stringify(dimMemberExps);
    },
    setData(data) {
      const { dimId, dimName, dimCode } = this.dimInfo;
      const selectedItem = [];
      data[0] &&
        data[0].members &&
        data[0].members.map(item => {
          const {
            dimMemberName,
            objectId: dimMemberId,
            dimMemberShared,
            dimCode,
            dimMemberCode,
            dimMemberDbCode,
            dbCodeIndex
          } = item;
          selectedItem.push({
            dimMemberId,
            dimMemberName,
            dimCode,
            dimMemberCode,
            dimMemberDbCode,
            key: `${dimMemberId}-self`,
            label: dimMemberName,
            memberType: 1,
            memberTypeValue: "self",
            objectId: `${dimMemberId}-self`,
            shardim: dimMemberShared,
            title: `${this.$t_common("member")}(${dimMemberName})`,
            type: "member",
            value: dimMemberId,
            dbCodeIndex
          });
        });
      this.setShowDimInfo(selectedItem);
      this.$emit("change", {
        dimId,
        dimName,
        dimCode,
        selectedItem
      });
    }
  }
};
</script>
<style lang="less" scoped>
.expase-text {
  display: flex;
  align-items: center;
}
.dim-list-input {
  width: 200px;
  height: @rem32;
  overflow: hidden;
  line-height: @rem32;
  border: 1px solid @yn-border-color-base;
  border-radius: @yn-border-radius-base;
  padding: 0 @rem32 0 @rem10;
  position: relative;
  .input-content {
    width: 100%;
    height: @rem30;
    padding: 3px 0;
    display: flex;
    align-items: center;
    .placeholder {
      color: @yn-auxiliary-color;
    }
    .input-text {
      height: @rem24;
      line-height: @rem24;
      color: @yn-auxiliary-color;
    }
    .input-text-choose {
      color: @yn-text-color;
    }
  }
  .icon-cont {
    position: absolute;
    top: 0;
    display: inline-block;
    height: @rem30;
    line-height: @rem30;
    right: @rem8;
    cursor: pointer;
    color: @yn-disabled-color;
  }
  .dim-cont {
    max-width: 84px;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 0.125rem;
    padding: 0px 2px 0px 2px;
    overflow: hidden;
    color: @yn-text-color-secondary;
    background-color: @yn-background-color;
    border: 1px solid @yn-border-color-base;
    border-radius: @yn-border-radius-base;
    display: inline-block;
    height: @rem24;
    line-height: @rem24;
    cursor: default;
  }
  .hideNum {
    margin-right: 0;
  }
  &:hover {
    border-color: @yn-primary-5;
    cursor: pointer;
  }
}
</style>
