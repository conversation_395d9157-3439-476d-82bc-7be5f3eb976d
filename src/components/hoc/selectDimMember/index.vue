<template>
  <yn-select-tree
    v-bind="$attrs"
    :size="size"
    searchMode="single"
    forceRender
    :value="dimVal"
    :allowClear="allowClear"
    :nonleafselectable="!allowOnlyLeaf"
    :datasource="dataSourceAll"
    @clear="handleClear"
    @change="handleOnChange($event)"
  />
</template>
<script>
// 选择维度成员组件
// 适用范围：TODO
import "yn-p1/libs/components/yn-select-tree";
import commonService from "@/services/common";
import cloneDeep from "lodash/cloneDeep";

export default {
  name: "SelectDimMember",
  props: {
    // 选中结果对应数据中的属性 名称(用于不传递treeData时，选中的value值对应树数据中的属性 例如：id or memberCode)
    valuePropsName: {
      type: String,
      default: "id"
    },
    isDefaultSelFirstMember: {
      type: Boolean,
      default: false
    },
    treeData: {
      type: Array || null,
      default: () => null
    },
    // 承载更新数据相关的对象
    dataObj: {
      type: Object, // default: () => []
      default: () => {}
    },
    // 绑定的对象属性
    fieldName: {
      type: String,
      default: ""
    },
    // 维度Code  Version  Period  Year ReportingCurrency-报告币种 TransCurrency-转换币种
    dimCode: {
      type: String,
      default: ""
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    // 只允许选择叶子节点
    allowOnlyLeaf: {
      type: Boolean,
      default: false
    },
    // 是否触发未保存提示
    isShowConfirmSave: {
      type: Boolean,
      default: false
    },
    // 配合属性isShowConfirmSave使用，当isShowConfirmSave=true 时，需要传入参数
    confirmObj: {
      type: Object,
      default: () => {
        return {
          toBeSavedObj: () => {}, // 需要保存的对象
          commitPath: "" // store commit 提交
        };
      }
    },
    size: {
      type: String,
      default: "default" // small
    }
  },
  data() {
    return {
      dimVal: "", // 选择的维度
      dataSourceAll: [] // 数据源
    };
  },
  watch: {
    dataObj: {
      handler(newVal) {
        if (newVal && this.fieldName) {
          this.dimVal = newVal[this.fieldName];
        }
      },
      deep: true
    },
    treeData: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.dataSourceAll = cloneDeep(this.treeData);
          this.setDefaultValue();
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    // 没有传入数据源，则请求数据
    if (!this.treeData) {
      this.getDataSourceByDimCode();
    }
  },
  methods: {
    getDataSourceByDimCode() {
      return commonService("getMemberByDimCode", this.dimCode).then(res => {
        this.dataSourceAll = this.convertData(res.data);
        this.dataSourceShow = cloneDeep(this.dataSourceAll);
        this.$nextTick(() => {
          this.setDefaultValue();
        });
      });
    },

    // 设置默认选中值，默认为第一个叶子节点
    setDefaultValue() {
      // 设置默认为 POV 中有值
      if (this.dataObj && this.fieldName) {
        this.dimVal = this.dataObj[this.fieldName];
      }
      if (!this.isDefaultSelFirstMember) return;
      // 以下，如果是非POV包含的维度，设置默认选中第一个叶子节点
      // if (!this.dimVal) {
      if (!this.dimVal && !this.getPovTypeList().includes(this.fieldName)) {
        const data = this.dataSourceAll;
        if (!data || !data.length) return;
        let res = [...data];
        while (res.length) {
          const { children, ...obj } = res.shift();
          if (children && children.length > 0) {
            res = children.concat(res);
          } else {
            this.dimVal = obj.id;
            if (this.dataObj && this.fieldName) {
              // 初始化的时候就设置成响应式
              this.$set(this.dataObj, this.fieldName, obj.id);
            }
            this.$emit("changeDimVal", obj.id);
            res = [];
          }
        }
      }
    },

    convertData(data) {
      const loop = data => {
        data.map(item => {
          const { id, name, children } = item;
          item.key = this.valuePropsName ? item[this.valuePropsName] : id;
          item.label = name;
          if (children && children.length) {
            loop(children);
          }
        });
      };
      loop(data);
      return data;
    },

    handleClear() {
      this.dimVal = null;
      this.dataObj[this.fieldName] = null;
      this.$emit("update:dataObj", this.dataObj);
      this.$emit("changeDimVal", "");
    },
    handleDropdownVisibleChange(isShow, propName) {
      if (isShow) {
        this.dataSourceShow = cloneDeep(this.dataSourceAll);
      }
    },

    handleOnChange(value) {
      const dealChange = () => {
        this.dimVal = value;
        this.dataObj[this.fieldName] = value;
        this.$emit("update:dataObj", this.dataObj);
        this.$emit("changeDimVal", value);
      };
      if (
        this.isShowConfirmSave &&
        this.confirmObj &&
        Object.values(this.confirmObj.toBeSavedObj).length
      ) {
        this.savePromptMixin().then(() => {
          dealChange();
          this.confirmObj.commitPath &&
            this.$store.commit(this.confirmObj.commitPath);
        });
      } else {
        dealChange();
      }
    }
  }
};
</script>
<style lang="less" scoped></style>
