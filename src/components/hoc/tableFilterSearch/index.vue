<template>
  <div class="table-search" :style="{ width: panelWidth }" @click.stop>
    <yn-input-search
      v-model="searchValue"
      :placeholder="$t_common('please_enter_keywords')"
      class="input-style"
      @search="handSearch(true)"
      @change="inputSearch"
    />
    <yn-checkbox-group
      v-if="searchResultList.length > 0"
      :value="chooseList"
      class="checkbox-group-style"
      @change="onChange"
    >
      <yn-checkbox
        v-for="(value, index) in searchResultList"
        :key="index + value"
        :value="value"
      >
        {{ value }}
      </yn-checkbox>
    </yn-checkbox-group>
    <yn-empty v-else />
    <div class="search-btns">
      <yn-button size="small" class="btns-reset" @click="handleReset">
        {{ $t_common("reset") }}
      </yn-button>
      <yn-button
        type="primary"
        size="small"
        :disabled="searchResultList.length === 0"
        class="btns-sure"
        @click="handSearch(false)"
      >
        {{ $t_common("ok") }}
      </yn-button>
    </div>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-checkbox-group/";
export default {
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    clearFilters: {
      type: Function,
      default: () => {}
    },
    panelWidth: {
      type: String,
      default: "250px"
    }
  },
  data() {
    return {
      searchValue: null,
      chooseList: [],
      chooseListHistory: [],
      searchResultList: []
    };
  },
  beforeDestroy() {
    document.body.removeEventListener("click", this._click_event);
    this._click_event = null;
  },
  mounted() {
    this.searchResultList = this.dataSource.slice(0);
    this._click_event = e => {
      this.searchValue = "";
      this.chooseList = [...this.chooseListHistory];
      this.searchResultList = this.dataSource.slice(0);
    };
    document.body.addEventListener("click", this._click_event);
  },
  methods: {
    inputSearch() {
      if (!this.searchValue) {
        this.searchResultList = [...this.dataSource];
        this.chooseList = [];
        return;
      }
      this.searchResultList = this.dataSource.filter(value =>
        value.includes(this.searchValue)
      );
      this.chooseList = [...this.searchResultList];
    },
    handleReset() {
      this.clearFilters({ closeDropdown: true });
      this.chooseList = [];
      this.chooseListHistory = [];
      this.searchResultList = this.dataSource.slice(0);
      this.searchValue = "";
      this.$emit("handSearch", []);
    },
    clearSelectedItems() {
      this.chooseList = [];
      this.chooseListHistory = [];
    },
    setSelectedItems(selectedItems) {
      this.chooseList = [...selectedItems];
      this.chooseListHistory = [...selectedItems];
    },
    onChange(checkData) {
      this.$set(this, "chooseList", checkData);
    },
    handSearch(isSearch) {
      if (isSearch && !this.searchValue) {
        return;
      }
      if (this.searchResultList.length > 0) {
        this.searchValue = null;
        this.clearFilters({ closeDropdown: true });
        this.searchResultList = this.dataSource.slice(0);
        this.$emit("handSearch", this.chooseList);
        this.chooseListHistory = [...this.chooseList];
      }
    }
  }
};
</script>

<style scoped lang="less">
.table-search {
  width: 250px;
  padding: @yn-padding-s;
  .checkbox-group-style {
    overflow: scroll;
    padding: @yn-padding-s;
    max-height: 205px;
    width: 100%;
    padding-bottom: 0;
    .ant-checkbox-wrapper {
      margin-left: 0;
      white-space: nowrap;
      display: block;
      margin-bottom: 0.5rem;
    }
  }
  .input-style {
    width: calc(100% - 0.5rem);
  }
  .search-btns {
    width: calc(100% + @rem16);
    margin-left: -@yn-margin-s;
    text-align: right;
    border-top: 1px solid @yn-border-color-base;
    padding: @yn-padding-s @rem6 0 0;
    .btns-reset {
      width: 50px;
      margin-right: @yn-margin-s;
    }
    .btns-sure {
      width: 50px;
    }
  }
}
</style>
