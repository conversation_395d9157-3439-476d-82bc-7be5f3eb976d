<template>
  <yn-popconfirm
    v-if="dimInfo.length > 1"
    ref="popconfirm"
    placement="bottomLeft"
    :okText="$t_common('ok')"
    :visible="popVisible"
    :cancelText="$t_common('cancel')"
    overlayClassName="dimpage-popconfirm"
    @confirm="search"
    @cancel="cancelEvent"
  >
    <label slot="icon"></label>
    <template slot="title">
      <yn-form
        ref="filterForm"
        :form="pageDimForm"
        layout="horizontal"
        :class="`${componentId} filter-form`"
      >
        <yn-form-item
          v-for="item in formData"
          :key="item.dimCode"
          :label="item.dimName"
          :labelCol="formItemLayout.labelCol"
          :wrapperCol="formItemLayout.wrapperCol"
        >
          <asyn-select-dimMember
            :ref="item.dimCode"
            v-decorator="[item.dimCode, { initialValue: item.dimMemberId }]"
            v-bind="item.attr"
            :dimCode="item.dimCode"
            :allowClear="false"
            :nonleafselectable="false"
            forceRender
            searchMode="single"
            :dropDownClass="componentId"
            @changeVal="handlerChange"
          />
        </yn-form-item>
      </yn-form>
    </template>
    <div ref="text" :class="`${componentId} bread`" @click="showPop">
      <template v-for="(item, index) in showDimInfo">
        <span :key="`${item.dimCode}_text`" class="item">
          {{ item.dimMemberName }}
        </span>
        <span v-if="index !== showDimInfo.length - 1" :key="item.dimCode">
          /
        </span>
      </template>
      <svg-icon type="down" class="open-tilter" :isIconBtn="false" />
    </div>
  </yn-popconfirm>
  <div
    v-else
    ref="text"
    :class="`${componentId} bread singleDimCont`"
    @click="showSelectPanel"
  >
    <template v-for="(item, index) in showDimInfo">
      <span :key="`${item.dimCode}_text`" class="item">
        {{ item.dimMemberName }}
      </span>
      <span v-if="index !== showDimInfo.length - 1" :key="item.dimCode">
        /
      </span>
    </template>
    <svg-icon type="down" class="open-tilter" :isIconBtn="false" />
    <template v-for="item in formData">
      <asyn-select-dimMember
        :key="item.dimCode"
        :ref="item.dimCode"
        v-model="item.dimMemberId"
        :width="200"
        class="singleDim"
        v-bind="item.attr"
        :dimCode="item.dimCode"
        :allowClear="false"
        :nonleafselectable="false"
        forceRender
        searchMode="single"
        :dropDownClass="componentId"
        @dropdownVisibleChange="dropdownVisibleChange"
        @changeVal="handlerChange"
      />
    </template>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-form/";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import cloneDeep from "lodash/cloneDeep";
export default {
  name: "PageDimSelect",
  components: {
    AsynSelectDimMember
  },
  props: {
    dimInfo: {
      type: Array,
      default: () => [] // [{dimCode:"",dimName:"",dimMemberId:"",dimMemberName:"",attr:{hasAllMembers:true,needPermission:true}}]
    }
  },
  data() {
    return {
      pageDimForm: this.$form.createForm(this, "pageDimForm"),
      formItemLayout: {
        labelCol: {
          span: 4
        },
        wrapperCol: {
          span: 20
        }
      },
      formData: [],
      showDimInfo: [], // [{dimCode:"",dimName:"",dimMemberId:"",dimMemberName:"",attr:{hasAllMembers:true,needPermission:true}}]
      popVisible: false,
      componentId: "ident_" + AppUtils.generateUniqueId(),
      isRefresh: false,
      isChange: false //  单个下拉树是否改变值
    };
  },
  watch: {
    dimInfo: {
      handler() {
        this.formData = [...this.dimInfo];
        this.showDimInfo = cloneDeep(this.formData);
      },
      immediate: true
    },
    popVisible: {
      handler(newVal) {
        if (!newVal) {
          this.formData.forEach(item => {
            const { dimCode } = item;
            this.$refs[dimCode][0].$refs.selector.closeMenu();
          });
        }
      }
    }
  },
  created() {
    document.body.addEventListener("click", this.clickBody);
  },
  destroyed() {
    document.body.removeEventListener("click", this.clickBody);
  },
  methods: {
    clickBody(e) {
      let currDom = e.target;
      const targetClassName = ["yn-select-menu", "filter-form", "bread"];
      function isTargetDom(currDom) {
        if (!currDom) return false;
        const { className } = currDom;
        if (typeof className !== "string") return false;
        return className.split(" ").some(name => {
          return targetClassName.indexOf(name) !== -1;
        });
      }
      let isBreak = false;
      while (!isBreak && currDom) {
        if (isTargetDom(currDom)) {
          // 不是当前 组件触发的，隐藏弹窗
          if (currDom.className.indexOf(this.componentId) === -1) {
            this.cancelEvent();
          }
          isBreak = true;
        } else if (currDom.tagName === "BODY") {
          isBreak = true;
          this.cancelEvent();
        }
        currDom = currDom.parentElement;
      }
    },
    cancelEvent() {
      this.formData = cloneDeep(this.showDimInfo);
      this.popVisible = false;
      const formVal = {};
      this.showDimInfo.forEach(item => {
        const { dimCode, dimMemberId } = item;
        formVal[dimCode] = dimMemberId;
      });
      this.pageDimForm.setFieldsValue(formVal);
    },
    search() {
      this.pageDimForm.validateFields((err, values) => {
        if (err) return;
        this.$emit("changeDimVal", values, [...this.formData]);
        // 合并组 不是全部成员，才存入pov
        if (values.Scope !== "allScope") {
          const savePovParams = {};
          Object.keys(values).forEach(key => {
            savePovParams[key.toLocaleLowerCase()] = values[key];
          });
          this.saveOrUpdateUserPov(savePovParams);
        }
        this.showDimInfo = cloneDeep(this.formData);
        this.popVisible = false;
      });
    },
    handlerChange(dimMemberId, treeNodes) {
      const { dimCode: currDimCode, dimMemberRealName } = treeNodes[0];
      this.formData.some(item => {
        const { dimCode } = item;
        if (dimCode === currDimCode) {
          if (item.dimMemberId !== dimMemberId) {
            this.isChange = true;
          }
          item.dimMemberId = dimMemberId;
          item.dimMemberName = dimMemberRealName;
          return true;
        }
        return false;
      });
    },
    showPop() {
      this.savePromptMixin().then(() => {
        this.popVisible = true;
      });
    },
    async refresh() {
      this.getData();
    },
    async getData() {
      const promiseArr = [];
      this.formData.forEach(item => {
        const currCompont = this.$refs[item.dimCode];
        promiseArr.push(
          currCompont &&
            currCompont[0].refreshData &&
            currCompont[0].refreshData(item.dimCode)
        );
      });
      await Promise.all(promiseArr);
    },
    showSelectPanel() {
      this.savePromptMixin().then(() => {
        const [curDim] = this.formData;
        this.$refs[curDim.dimCode][0].$refs.selector.openMenu();
      });
    },
    dropdownVisibleChange(value) {
      if (!value) {
        const [{ dimCode, dimMemberId }] = this.formData;
        // 合并组，全部成员 不入pov
        if (dimMemberId !== "allScope") {
          this.saveOrUpdateUserPov({
            [dimCode.toLocaleLowerCase()]: dimMemberId
          });
        }
        this.showDimInfo = cloneDeep(this.formData);
        if (this.isChange) {
          this.$emit(
            "changeDimVal",
            {
              [dimCode]: dimMemberId
            },
            [...this.formData]
          );
        }
        this.isChange = false;
      }
    }
  }
};
</script>
<style lang="less">
.bread {
  cursor: pointer;
  .open-tilter .svg-icon {
    font-size: 0.8rem;
  }
}
.ant-popover.dimpage-popconfirm {
  width: 16.25rem;
  .ant-popover-buttons {
    border-top: 1px solid @yn-border-color-base;
    padding: 0.5rem;
  }
  .ant-popover-message-title {
    padding: 1rem 1rem 0;
  }
  .ant-popover-inner-content {
    padding: 0;
  }
  .ant-popover-message {
    font-weight: 400;
    padding-bottom: 0;
    font-size: 0.875rem;
  }
  .ant-form-item {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
  }
}
</style>
<style lang="less" scoped>
.singleDimCont {
  position: relative;
}
.singleDim {
  opacity: 0;
  position: absolute;
  top: 0;
}
.bread {
  color: @yn-link-color;
  font-size: 0.875rem;
  .open-tilter .svg-icon {
    font-size: 0.8rem;
  }
}
</style>
