<template>
  <div
    v-show="visible"
    ref="filterPopCont"
    class="filter-pop"
    :style="{ left: position.left + 'px', top: position.top + 'px' }"
  >
    <header class="header">
      <yn-input-search
        ref="searchInput"
        v-model="searchWord"
        :placeholder="$t_common('please_in_search')"
        @search="okEvent(true)"
        @change="onSearch"
      />
    </header>
    <yn-spin size="small" :spinning="spinning">
      <div class="list">
        <yn-checkbox-group
          v-if="showData.length > 0"
          :value="checkData"
          @change="onChange"
        >
          <yn-checkbox
            v-for="item in showData"
            :key="item.id || item.code || item"
            :value="item.id || item.code || item"
          >
            {{ currWord(item) }}
          </yn-checkbox>
          <br />
        </yn-checkbox-group>
        <yn-empty v-else />
      </div>
    </yn-spin>
    <footer class="footer">
      <yn-button size="small" @click="reset">
        {{
          $t_common("reset")
        }}
      </yn-button>
      <yn-button
        type="primary"
        size="small"
        :disabled="showData.length === 0"
        @click="okEvent(false)"
      >
        {{ $t_common("ok") }}
      </yn-button>
    </footer>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-checkbox-group/";
import "yn-p1/libs/components/yn-checkbox/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-tooltip/";
import _debounce from "lodash.debounce";
import cloneDeep from "lodash/cloneDeep";
export default {
  name: "FilterPop",
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    pos: {
      type: Object,
      default() {
        return {
          left: 0,
          top: 0
        };
      }
    },
    visible: {
      type: Boolean,
      default: false
    },
    name: {
      type: String,
      default: ""
    },
    ident: {
      type: String,
      default: ""
    },
    checkedArr: {
      type: Array || Object,
      default: () => []
    },
    requestFilterPopData: {
      type: [Function, null],
      require: false,
      default: null
    },
    cellType: {
      type: String,
      require: false,
      default: null
    },
    formatFilterWord: {
      type: Function,
      require: false,
      default: null
    }
  },
  data() {
    return {
      checkData: [],
      showData: [],
      searchWord: "",
      position: { left: -999, top: 0 },
      orgData: [],
      spinning: false
    };
  },
  computed: {
    mapData() {
      const mapData = {};
      this.orgData.forEach(item => {
        if (typeof item === "object") {
          mapData[item.id || item.code] = item.name;
        } else {
          mapData[item] = item;
        }
      });
      return mapData;
    }
  },
  watch: {
    dataSource: {
      handler(newVal) {
        this.orgData = cloneDeep(this.dataSource);
        this.showData = cloneDeep(this.dataSource);
      },
      deep: true,
      immediate: true
    },
    checkedArr: {
      handler(newVal) {
        this.$set(this, "checkData", newVal);
      },
      deep: true
    },
    visible: {
      async handler(value) {
        if (this.requestFilterPopData && !this.showData.length) {
          this.spinning = true;
          this.showData = await this.requestFilterPopData(this.ident);
          this.spinning = false;
          this.orgData = cloneDeep(this.showData);
        }
        if (!value) {
          this.$set(this, "checkData", this.checkedArr);
        }
        this.$nextTick(() => {
          if (!value) return;
          this.position = {
            left:
              this.$el.offsetWidth + this.pos.left > this.pos.limit
                ? this.pos.limit - this.$el.offsetWidth
                : this.pos.left,
            top: this.pos.top
          };
        });
      }
    }
  },
  mounted() {
    document.body.addEventListener("click", this.clickBody);
  },
  destroyed() {
    document.body.removeEventListener("click", this.clickBody);
  },
  methods: {
    clickBody(e) {
      let dom = e.target;
      if (
        dom &&
        typeof dom.className === "string" &&
        dom.className.indexOf("filter-icon") !== -1
      ) {
        return;
      }
      const stopTagName = ["html", "body"];
      let notStop = true;
      while (notStop) {
        if (
          dom &&
          typeof dom.className === "string" &&
          dom.className.indexOf("filter-pop") === -1 &&
          stopTagName.indexOf(dom.nodeName.toLocaleLowerCase()) === -1
        ) {
          dom = dom.parentNode;
        } else {
          notStop = false;
        }
      }
      if (
        dom &&
        typeof dom.className === "string" &&
        dom.className.indexOf("filter-pop") === -1
      ) {
        this.$emit("update:visible", false);
        this.resetFormInfo();
      }
    },
    onSearch: _debounce(function() {
      this.showData = this.orgData.filter(item => {
        if (this.formatFilterWord) {
          const currName = this.currWord(item);
          return currName.indexOf(this.searchWord) !== -1;
        } else if (typeof item === "object") {
          return item.name.indexOf(this.searchWord) !== -1;
        } else {
          return item.indexOf(this.searchWord) !== -1;
        }
      });
      if (this.searchWord) {
        this.checkData = this.showData.map(
          item => item.id || item.code || item
        );
      } else {
        this.checkData = [];
      }
    }, 200),
    onChange(checkData) {
      this.$set(this, "checkData", checkData);
    },
    reset() {
      this.checkData = [];
      this.$emit("setFilterInfo", {
        type: this.ident,
        title: this.name,
        searchArr: []
      });
      this.resetFormInfo();
      this.$emit("update:checkedArr", this.checkData);
    },
    okEvent(isSearch) {
      if (isSearch && !this.searchWord) {
        return;
      }
      this.savePromptMixin(this.$attrs.edited).then(() => {
        if (this.showData.length > 0) {
          this.$emit("update:visible", false);
          this.resetFormInfo();
          this.$emit("update:checkedArr", this.checkData);
          this.$emit("setFilterInfo", {
            type: this.ident,
            title: this.name,
            searchArr: this.getCheckedData()
          });
        }
      });
    },
    // 关闭过滤弹窗清除输入框信息及 恢复checkbox数据
    resetFormInfo() {
      this.searchWord = "";
      this.$refs.searchInput.$el.querySelector("input").value = "";
      this.showData = cloneDeep(this.orgData);
    },
    getCheckedData() {
      return this.checkData.map(item => {
        return {
          id: item,
          name: this.mapData[item]
        };
      });
    },
    currWord(item) {
      if (this.requestFilterPopData) {
        if (this.formatFilterWord) {
          return this.formatFilterWord(this.ident, item, this.cellType);
        }
        return item.name || item;
      } else {
        return item.name || item;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.filter-pop {
  z-index: 1060;
  position: absolute;
  top: 0;
  background: @yn-body-background;
  overflow: auto;
  width: 250px;
  padding: @rem8 0 @rem8 @rem8;
  border-radius: 0.25rem;
  // box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  box-shadow: @yn-border-shadow-3;
}
.header {
  padding-right: @rem8;
  height: 42px;
}
.list {
  max-height: 120px;
  overflow: auto;
  padding: @rem8;
  /deep/.ant-checkbox-wrapper {
    margin-left: 0;
    margin-bottom: 0.5rem;
    display: block;
  }
}
.footer {
  height: 34px;
  width: calc(100% + @rem8);
  margin-left: -@rem8;
  display: flex;
  justify-content: right;
  padding: @rem8 0;
  border-top: 1px solid @yn-border-color-base;
  & > button {
    margin-right: @rem10;
  }
}
</style>
