// import Logger from "yn-p1/libs/modules/log/logger";
import { cloneDeep } from "lodash";

/**
 * @desc 所有关于表格筛选功能
 * */
export default {
  watch: {
    filterCheckDimInfo: {
      handler(newVal, oldVal) {
        if (!this.bool) return;
        this.pagination.limit = this.firstPageNum;
        this.pagination.offset = 0;
        this.pagination.pageNum = 0;
        // 关闭所有过滤弹窗
        this.hideFilter();
        // // 没有过滤条件，则清空展开项, simpel-grid 默认支持树形
        // if (Object.keys(newVal).every(key => !newVal[key].length)) {
        //   this.$emit("reloadData");
        // }
        // 调用查询接口
        this.loadMore({
          isFirstPage: true,
          searchObj: this.searchObj
        }).then(res => {
          this.lastRecord = cloneDeep(newVal);
          this.clearToBeSaveObj();
          const { data, hasMore, refresh = true } = res;
          if (!refresh) return;
          this.pagination.hasMore = hasMore;
          try {
            this.$refs.simpleGrid.onBackTop(); // 滚动到顶部
          } catch (e) {
            // 当上一次 数据为空时，simpleGrid 实例不存在
          }
          // 过滤不需要 存储之前的缓存信息
          this.$refs.simpleGrid.clearEditVMap();
          // 股权搜索下新增直接return;
          // if (res.tableType) {
          //   return;
          // }
          // treeGrid 在过滤以后重新设置表格高度
          if (this.gridType === "treeGrid") {
            const {
              setGridHeight,
              expandedRowKeys,
              getShowTableData
            } = this.$parent;
            // 如果，没有过滤条件，则按照 展开的节点信息，展示表格
            if (!this.searchObj.length) {
              this.tableData = getShowTableData(expandedRowKeys);
            } else {
              this.tableData = data;
            }
            setGridHeight && setGridHeight(this.searchObj.length ? data : null);
          } else {
            this.tableData = data;
          }
          this.updateTableData(this.tableData, hasMore);
          // this.$refs.simpleGrid.updateBody(this.tableData);
        });
        // });
      },
      deep: true
    }
  },
  data() {
    return {
      bool: true, // 清空条件是否需要走 watch  默认 true 走， 不走设置 false
      lastRecord: {}, // 上一次过滤选中维度信息
      filterCheckDimInfo: {} // 表头过滤选中的维度信息
    };
  },
  methods: {
    // 图标高亮
    getHighlightClass(cellContext) {
      const { colName } = cellContext;
      const tempArr = this.filterCheckDimInfo[colName];
      return tempArr && tempArr.length ? "filter-active" : "";
    },
    showFilter(event, context) {
      const { colName, td } = context;
      this.showFilterPop[colName] = !this.showFilterPop[colName];
      const { left, top, limit } = this.getPopPosByTd(td);
      this.popPos = {
        left,
        top,
        limit
      };
    },
    hideFilter() {
      Object.keys(this.showFilterPop).forEach(item => {
        this.showFilterPop[item] = false;
      });
    },
    getScrollPos() {
      const { scrollLeft, scrollTop } = this.$refs.gridCont.querySelector(
        ".wtHolder"
      );
      return {
        scrollLeft,
        scrollTop
      };
    },
    deleteFilteItemById(type, delDimMemberId) {
      this.savePromptMixin(this.$attrs.edited).then(() => {
        if (type === "all") {
          this.clearAll();
        } else {
          const { title, searchArr } = this.mapFilterInfo[type];
          const checkedArr = [];
          const res = searchArr.filter(item => {
            const { id } = item;
            if (id !== delDimMemberId) {
              checkedArr.push(id);
              return true;
            }
            return false;
          });
          this.setFilterInfo({
            title,
            type,
            searchArr: res
          });
          this.filterCheckDimInfo[type] = checkedArr;
        }
      });
    },
    clearAll(bool = true) {
      Object.keys(this.filterCheckDimInfo).forEach(item => {
        this.filterCheckDimInfo[item].splice(
          0,
          this.filterCheckDimInfo[item].length
        );
      });
      this.mapFilterInfo = {};
      this.searchObj.splice(0, this.searchObj.length);
      this.bool = bool;
    },
    setFilterInfo(info) {
      const { type, searchArr } = info;
      // 过滤条件 为空，则删除对应的维度信息
      if (searchArr.length > 0) {
        this.mapFilterInfo[type] = info;
      } else {
        delete this.mapFilterInfo[type];
      }
      this.$set(this, "filterCheckDimInfo", { ...this.filterCheckDimInfo });
      this.searchObj.splice(0, this.searchObj.length);
      // 顺序
      if (this.filterOrder.length > 0) {
        this.filterOrder.forEach(item => {
          const currSearchObj = this.mapFilterInfo[item] || {};
          const { searchArr = [] } = currSearchObj;
          // 必须存在维度成员
          if (searchArr.length > 0) {
            this.searchObj.push(this.mapFilterInfo[item]);
          }
        });
      } else {
        this.$set(this, "searchObj", Object.values(this.mapFilterInfo));
      }
    }
  }
};
