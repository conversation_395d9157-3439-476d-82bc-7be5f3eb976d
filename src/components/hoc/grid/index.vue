<template>
  <div ref="gridCont" class="simple-grid-cont">
    <button v-if="false" @click="getSettings">settings</button>
    <filter-list
      :searchObj="searchObj"
      v-bind="$attrs"
      @deleteFilteItemById="deleteFilteItemById"
    />
    <yn-simple-grid
      ref="simpleGrid"
      v-bind="$attrs"
      :header="header"
      :cell="cell"
      :menuOptions="menuOptions"
      :headerColumns="headerColumns"
      :hasMore="hasMore"
      :pagination="pagination"
      :dataSource="tableData"
      :data="simpleGridInstance"
      :options="options"
      :simpleOptions="simpleOptions"
      :loadMore="onLoadMore"
      :customValidate="onCustomValidate"
      :customBeforeColumnResize="handleBeforeColumnResize"
      :scrollTable="scrollEvent"
      v-on="$listeners"
      @mousedown.native="handlerDataRecord"
      @beforeCopy="beforeCopy"
      @beforePaste="beforePaste"
      @pasteEnd="pasteEnd"
      @afterSelectionEnd="afterSelectionEnd"
      @onBeforeShowDropDown="onBeforeShowDropDown"
      @onAfterCellModifiedEnd="onAfterCellModifiedEnd"
      @modifyColWidth="modifyColWidth"
    >
      <template v-if="!tableData.length" v-slot:empty>
        <yn-spin :spinning="cwLoading" size="large">
          <slot v-show="$scopedSlots.empty" name="empty"></slot>
          <yn-empty v-show="!$scopedSlots.empty" class="grid-empty" />
        </yn-spin>
      </template>
    </yn-simple-grid>
    <filter-pop
      v-for="(item, keyName) in filterObject || filterInfo"
      :ref="keyName"
      :key="keyName"
      v-bind="$attrs"
      :ident="keyName"
      :name="item.name"
      :checkedArr.sync="filterCheckDimInfo[keyName]"
      :pos="popPos"
      :dataSource="item.data"
      :requestFilterPopData="$attrs.requestFilterPopData"
      :visible.sync="showFilterPop[keyName]"
      @setFilterInfo="setFilterInfo"
    />
    <div
      v-show="showVerificationInfo"
      ref="errorTipsCont"
      class="error-tips-cont"
      :style="errorTipsStyle"
    >
      <span class="tips">{{ verificationPopInfo.word }}</span>
    </div>
    <!-- <slot name="copy"></slot> -->
  </div>
</template>
<script>
// 组件引入方式
import YnSimpleGrid from "yn-p1/libs/components/yn-simple-grid";
import SimpleGridDataProvider from "yn-p1/libs/components/yn-simple-grid/js/SimpleGridDataProvider";
import FilterList from "@/views/ownershipManagement/filterList.vue";
import cellTypeMap from "@/constant/cellTypeMap";
import FilterPop from "./filterPop.vue";
import cloneDeep from "lodash/cloneDeep";
import gridFilter from "./gridFilter";
import commonService from "@/services/common";
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-icon/";
import Logger from "yn-p1/libs/modules/log/logger";
import _debounce from "lodash/debounce";
import selection from "./components/selection";
import copy from "./components/copyEx";

import { genVDOM } from "@/views/process/control/jdom/utils";
const TD_PREFIX = "td"; // td
const ERROR_TIPS_CLASS_NAME = "verification-failed"; // 错误提示类名
const MIX_CELL_WIDTH = 100;
const GRID_TYPE = ["grid", "treeGrid"]; // 表格类型
const ERROR_TIPS_MIN_WIDTH = 200;
const equityColumnsMap = {
  iCPName: "iCP",
  iCPCode: "iCP",
  iCPCodeAndName: "iCP",
  entityName: "entity",
  entityCode: "entity",
  entityCodeAndName: "entity"
}; // 股权持股方、被持股方列映射。
const dynamicColumnsIdent = [
  "organizationManagementList",
  "reconciliationReport",
  "voucherReconciliationReport",
  "DetailsTable",
  "processScope"
];
export default {
  components: {
    YnSimpleGrid,
    FilterList,
    FilterPop
  },
  mixins: [gridFilter, selection, copy],
  props: {
    hasMore: {
      type: Boolean,
      default: true
    },
    tableReadOnly: {
      type: Boolean,
      default: false
    },
    filterObject: {
      type: [Object, null],
      default: () => null
    },
    gridType: {
      type: String,
      validator(value) {
        return GRID_TYPE.indexOf(value) !== -1;
      },
      default: "grid" // grid 或者 treeGrid
    },
    /**
     * 与yn-table基本 相似 一致  识别的属性 ，grid组件内部进行转换
     * [{
     * title:"string 显示的文本",
     * dataIndex:"string 映射的关键字"
     * width:"单元格宽度",
     * isFilter:"boolean true or false 是否显示过滤按钮，内置过滤组件"
     * cellType:"string 固定值单元格类型 详见：constant/cellTypeMap"
     * readOnly:"单元格只读状态，cell权重最高"
     * align:"string 只识别right 默认left"（表头表体一致的对齐方式）等属性
     * }]
     *
     */
    columns: {
      type: Array,
      default: () => []
    },
    dataSource: {
      // 与源yn-table 一致
      type: Array,
      default: () => []
    },
    paginationInfo: {
      // 与源yn-table 属性一致
      type: Object,
      default: () => ({})
    },
    loadMore: {
      // 滚动加载回调，搜索回调（搜索会传入isFirstPage,及过滤信息）
      type: Function,
      default: () => Promise.resolve({})
    },
    cellClass: {
      // 设置单元格td dom 的class
      type: [Function, null],
      default: () => ""
    },
    renderHeaderCell: {
      // 设置表头单元格 td dom 的 class
      type: Function,
      default: () => ""
    },
    dropDownData: {
      // 下拉类型的 下拉值
      type: Object,
      default: () => ({})
    },
    filterOrder: {
      // 过滤后 显示的维度顺序
      type: Array,
      default: () => []
    },
    validateCell: {
      // 编辑完成的后调 用于校验单元格的值
      type: Function,
      default: () => {
        return () => {};
      }
    },
    errorRowInfo: {
      // 错误行信息，{"行的唯一标识key":{key:"行的唯一标识",type:"error or warn"}}
      type: Object,
      default: () => ({})
    },
    firstPageNum: {
      // 第一页的数据，尽可能的大一点
      type: Number,
      default: 20
    },
    mixCellWidth: {
      // 最小的单元格宽度
      type: Number,
      default: MIX_CELL_WIDTH
    },
    ident: {
      // 单元格 class 的唯一标识
      type: String,
      default: () => TD_PREFIX
    },
    cellNode: {
      // render 表体单元格的回调
      type: Function,
      default: () => ""
    },
    option: {
      type: Object,
      default() {
        return {};
      }
    },
    fixedColumns: {
      type: Object,
      default() {
        return {};
      }
    },
    customBeforeColumnResize: {
      type: [Function, null],
      required: false,
      default: null
    },
    scrollTable: {
      type: [Function, null],
      require: false,
      default: null
    }
  },
  data() {
    return {
      tableData: [],
      headerColumns: [{}],
      searchObj: [],
      header: {
        row: {
          renderer: cellContext => {}
        },
        column: {
          classname: cellContext => {
            return this.setHeadClass(cellContext);
          },
          renderer: cellContext => {
            return this.setHeadIconFn(cellContext);
          }
        }
      },
      menuOptions: {},
      cell: {
        classname: cellContext => {
          return this.setCellClass(cellContext);
        },
        renderer: cellContext => {
          return this.setCellNode(cellContext);
        }
      },
      simpleOptions: {
        customValidate: true
      },
      pagination: {
        // 分页，如果是false 全量加载
        limit: 10, // 条数
        offset: 0, // 页数
        pageNum: 0,
        totalCount: 0,
        hasMore: false
      },
      options: {
        // 全局配置
        readOnly: this.tableReadOnly, // 设置整个表单的状态是否只读
        rowHeights: 35,
        // renderAllRows: true,
        colWidths: this.setColWidths,
        // colWidths: 200,
        fixedColumnsLeft: this.fixedColumns.left || 0,
        fixedColumnsRight: this.fixedColumns.right || 0,
        // fixedColumnsLeft: 0,
        // fixedColumnsRight: 0,
        fixedRowsTop: 2,
        manualRowResize: false, // 表头行高 禁止拖动
        copyPaste: true,
        ...this.option
        // comments: false, // 备注信息
        // contextMenu: true // 右键菜单
      },
      showFilterPop: {}, // 显示过滤弹框 colName
      popPos: { left: "", top: "" }, // 弹窗位置信息
      mapFilterInfo: {}, // 映射对象 key 是columns
      verificationInfo: {}, // 校验信息
      showVerificationInfo: false, // 展示校验信息
      verificationPopInfo: {}, // 错误提示位置、文案信息
      autoSizeCellWidth: [],
      originCellWidth: [], // 从prop 里面读取原始设置列宽
      toBeSavedObj: {}, // 待保存数据
      mapColumns: {}, // 映射columns
      cw: {}, // 列宽集合
      currColWidthObj: {}, // 当前列宽对象
      cwObjectId: "", // 修改列宽短保存接口需要该字段
      cwLoading: true, // 获取列宽接口 loading
      columnIndexWidthMap: {} // 映射列索引、宽度
    };
  },
  computed: {
    errorTipsStyle() {
      const { width, top, left } = this.verificationPopInfo;
      return {
        width: width + "px",
        top: top + "px",
        left: left + "px"
      };
    },
    filterInfo() {
      const res = {};
      this.columns.forEach(item => {
        const { isFilter, dataIndex, title } = item;
        if (isFilter) {
          res[dataIndex] = {
            data: [],
            name: title
          };
        }
      });
      return res;
    }
  },
  watch: {
    filterInfo: {
      handler(newVal) {
        const res = {};
        const keys = Object.keys(newVal);
        if (!keys.length) return;
        keys.forEach(item => {
          res[item] = false;
        });
        this.$set(this, "showFilterPop", res);
      },
      immediate: true,
      deep: true
    },
    columns: {
      handler(newVal) {
        // 监听表头变化 强制刷新表头信息
        this.$emit("headerReady", false);
        this.initPagination();
        this.transferHeader();
        this.getCellWidths();
        // 动态列 设置列宽。
        if (dynamicColumnsIdent.includes(this.ident)) {
          this.setDynamicColumnWidth();
        } else {
          this.$emit("headerReady", true);
        }
      },
      deep: true
    },
    paginationInfo: {
      handler(newVal) {
        const { hasMore, pageSize, total, current, offset } = newVal;
        this.pagination.hasMore = hasMore;
        this.pagination.limit = pageSize;
        this.pagination.totalCount = total;
        this.pagination.offset = offset;
        this.pagination.pageNum = current;
      },
      deep: true,
      immediate: true
    }
  },
  async created() {
    this.initTable();
    try {
      this.cwLoading = false;
      this.cw = await this.getColumnWidth();
      this.$set(this, "currColWidthObj", this.cw);
    } catch (err) {
      Logger.error("获取列宽接口报错：", err);
    }
    this.transferTableData();
    this.transferHeader();
  },
  mounted() {
    document.addEventListener("mouseover", this.showValidateInfo);
  },
  destroyed() {
    document.removeEventListener("mouseover", this.showValidateInfo);
  },
  methods: {
    scrollEvent() {
      // 滚动关闭过滤面板
      Object.keys(this.showFilterPop).forEach(item => {
        this.showFilterPop[item] = false;
      });
      this.scrollTable && this.scrollTable();
    },
    handleBeforeColumnResize() {
      this.customBeforeColumnResize && this.customBeforeColumnResize();
      this.hideFilter && this.hideFilter();
    },
    updateTableData(data = this.tableData, cb) {
      this.tableData = cloneDeep(data);
      this.verificationInfo = {};
      try {
        this.$refs.simpleGrid.updateBody(this.tableData, this.hasMore, cb);
        // this.$refs.simpleGrid.onBackTop();
        this.$nextTick(() => {
          this.$refs.simpleGrid &&
            this.$refs.simpleGrid.scrollViewportTo({
              row: 1,
              col: this.fixedColumns.left || 0
            });
        }, cb);
      } catch (e) {}
    },
    setFixedColumns(str) {
      // 只能设置从左侧冻结列
      this.options.fixedColumnsLeft = Number(str);
      const columns = this.headerColumns[0];
      const LEN = Object.keys(columns).length;
      const fixedRightNum = this.options.fixedColumnsRight;
      const fixedNum = Number(str) - 1;
      Object.keys(columns).forEach((key, index) => {
        columns[key].fixed = "";
        if (fixedNum >= index) {
          columns[key].fixed = "left";
        } else if (LEN - index <= fixedRightNum) {
          columns[key].fixed = "right";
        }
      });
      this.$set(this.headerColumns, [columns]);
      this.$refs.simpleGrid.hot.getPlugin("mergeCells").merge(1, 4, 1, 7);
    },
    setColWidths(col) {
      if (!this.autoSizeCellWidth.length) {
        this.getCellWidths();
      }
      const cwKey =
        this.columns[col] &&
        (equityColumnsMap[this.columns[col].dataIndex] ||
          this.columns[col].dataIndex);
      let width =
        (this.cw && this.columns[col] && this.cw[cwKey]) ||
        this.autoSizeCellWidth[col];
      if (typeof width === "string" && width.indexOf("calc") !== -1) {
        width = width.replace("calc", "");
        const finallyWidth =
          width > this.mixCellWidth ? width : this.mixCellWidth;
        return finallyWidth;
      }
      return width || this.mixCellWidth;
    },
    initPagination() {
      this.$set(this, "pagination", {
        limit: 10, // 条数
        offset: 0, // 页数
        pageNum: 0,
        totalCount: 0,
        hasMore: false
      });
    },
    initTable() {
      const params = {};
      const mapObj = {
        left: "fixedColumnsLeft",
        right: "fixedColumnsRight"
      };
      Object.keys(this.fixedColumns).forEach(k => {
        params[mapObj[k]] = this.fixedColumns[k];
      });
      this.simpleGridInstance = new SimpleGridDataProvider(params);
    },
    transferTableData() {
      this.tableData = cloneDeep(this.dataSource);
    },
    transferHeader() {
      const headerColumns = [{}];
      const widths = [];
      this.mapColumns = {};
      const { fixedColumnsLeft, fixedColumnsRight } = this.options;
      const LEN = this.columns.length;
      this.columns.forEach((item, index) => {
        const { dataIndex, title, width, cellType, readOnly } = item;
        widths.push(width || 0);
        this.mapColumns[dataIndex] = item;
        let fixed = "";
        if (index <= fixedColumnsLeft - 1) {
          fixed = "left";
        } else if (LEN - index <= fixedColumnsRight) {
          fixed = "right";
        }
        headerColumns[0][dataIndex] = {
          v: title,
          cellDataTypeId: cellTypeMap[cellType],
          readOnly: typeof readOnly === "undefined" ? true : readOnly,
          fixed
        };
      });
      this.originCellWidth = widths;
      this.$set(this, "headerColumns", headerColumns);
    },
    getCellWidths() {
      const colWidths = this.originCellWidth;
      const { clientWidth } = this.$refs.gridCont;
      // eslint-disable-next-line no-eval
      const totalWidth = eval(colWidths.join("+"));
      const emptyArr = colWidths.filter(item => !item);
      const colWidth = (clientWidth - totalWidth - 10) / emptyArr.length;
      colWidths.forEach((item, index) => {
        if (!item) {
          colWidths.splice(index, 1, "calc" + colWidth);
        }
      });
      this.autoSizeCellWidth = [...colWidths];
    },
    async getColumnWidth() {
      this.cwLoading = true;
      if (this.ident) {
        return new Promise(resolve => {
          commonService("getLastSettingV", {
            tag: "columnWidth",
            key: this.ident
          })
            .then(res => {
              if (res.data && res.data.data) {
                this.cwObjectId = res.data.data.objectId;
                resolve(JSON.parse(res.data.data.value));
              } else {
                resolve("");
              }
            })
            .catch(() => {
              resolve("");
            })
            .finally(() => {
              this.cwLoading = false;
            });
        });
      } else {
        this.cwLoading = false;
        return "";
      }
    },
    saveColumnWidth: _debounce(function(info) {
      const dataIndex = this.columns.map(column => column.dataIndex);
      const params = {};
      dataIndex.forEach((key, index) => {
        const k = equityColumnsMap[key] || key;
        params[k] = info[index];
      });
      // 动态列维 此次没有上次保存的列 此次拖拽后导致上次列消失。
      if (dynamicColumnsIdent.includes(this.ident)) {
        const diffDim = Object.keys(this.cw).filter(
          item => dataIndex.indexOf(item) === -1
        );
        diffDim.forEach(k => {
          params[k] = this.cw[k];
        });
      }
      Logger.info("params is: ", params);
      this.$set(this, "currColWidthObj", params);
      this.ident &&
        commonService("saveOrUpdateUserSetting", {
          tag: "columnWidth",
          objectId: this.cwObjectId,
          key: this.ident,
          value: JSON.stringify(params)
        });
    }, 500),
    updateCell(row, col, props, showWord) {
      this.$refs.simpleGrid.updateEditVMap(props);
      this.$refs.simpleGrid.hot.setDataAtCell(row, col, showWord);
      const cell = this.$refs.simpleGrid.getCellMeta(row, col);
      const { rowId } = cell;
      const record = this.getRecord(rowId);
      const key = this.columns[col].dataIndex;
      const isValue = typeof record[key] === "string";
      isValue
        ? this.$set(record, key, showWord)
        : (this.$set(record[key], "v", showWord),
        this.$set(record[key], "oldV", showWord));
    },
    // 保存列宽
    modifyColWidth(width, column) {
      this.$emit("modifyColWidth");
      typeof column === "number" && (this.columnIndexWidthMap[column] = width);
      if (this.saveWidth) return;
      if (
        Object.keys(this.columnIndexWidthMap).length === this.columns.length
      ) {
        this.saveColumnWidth(this.columnIndexWidthMap);
        this.saveWidth = true;
        setTimeout(() => {
          this.columnIndexWidthMap = {};
          this.saveWidth = false;
        });
      }
    },
    async setDynamicColumnWidth() {
      this.cw = await this.getColumnWidth();
      this.columns.forEach((column, index) => {
        this.$refs.simpleGrid &&
          this.$refs.simpleGrid.setManualColumnSize(
            index,
            this.cw[column.dataIndex],
            true
          );
      });
      this.$emit("headerReady", true);
    },
    getSettings() {
      // console.log(
      //   "getSettings===",
      //   this.simpleGridInstance.getSettings(),
      //   this.simpleGridInstance
      // );
    },
    setHeadClass(cellContext) {
      const { colName } = cellContext;
      const { align } = this.mapColumns[colName] || {};
      if (align === "right") {
        return `${colName} head-right `;
      }
      return ` ${colName} `;
    },
    setHeadIconFn(cellContext) {
      const { colName, td } = cellContext;
      let hasFilterIcon = "";
      for (let i = 0; i < this.columns.length; i++) {
        const { dataIndex, isFilter } = this.columns[i];
        if (dataIndex === colName) {
          hasFilterIcon = isFilter;
          break;
        }
      }
      const tempVal =
        this.renderHeaderCell && this.renderHeaderCell(cellContext);
      if (tempVal) {
        return tempVal;
      }
      const childDiv = document.createElement("div");
      const spanTag = document.createElement("span");
      spanTag.className = `header-col-text `;
      spanTag.textContent = cellContext.value;
      childDiv.appendChild(spanTag);
      childDiv.className = "header-col";
      if (hasFilterIcon) {
        const highlightClass = this.getHighlightClass(cellContext);
        const filterIcon = genVDOM({
          template: `<yn-icon-button type='icon-filter-default' size="smallest"  class=' ${colName}-filter-icon ${highlightClass}'/>`
        });
        childDiv.appendChild(filterIcon);
        filterIcon.addEventListener("mousedown", e => {
          // 关闭其他 筛选框
          const { colName } = cellContext;
          Object.keys(this.showFilterPop).forEach(item => {
            if (item !== colName) {
              this.showFilterPop[item] = false;
            }
          });
          this.showFilter(e, cellContext);
          e.stopImmediatePropagation();
        });
      }
      td.className += " grid-header";
      // td.appendChild(childDiv);
      return this.getSelectHead(cellContext) || childDiv;
    },
    afterSelectionEnd() {
      this.$emit("afterSelectionEnd", arguments);
    },
    async onBeforeShowDropDown(cellContext, callback) {
      this.$emit("beforeShowDropDown", cellContext);
      const { colName, cellDataTypeId } = cellContext;
      if (cellTypeMap[cellDataTypeId] !== "dropDown") return;
      cellContext.droplistObj = this.dropDownData[colName] || [];
      callback(cellContext); // 固定写法
    },
    onLoadMore(record) {
      return new Promise(resolve => {
        this.loadMore({
          searchObj: this.searchObj,
          record
        }).then(res => {
          resolve({ ...res, pId: "root" });
        });
      });
    },
    setCellClass(cellContext) {
      try {
        let className = this.cellClass && this.cellClass(cellContext);
        const { id, rowId, colName } = cellContext;
        const tdIdent = `${this.ident}_${id.replace(",", "_")}`;
        className += ` ${tdIdent}`;
        // 表头设置靠左还是靠右
        if (this.mapColumns[colName].align === "right") {
          className += " cell-right";
        }

        if (Object.keys(this.errorRowInfo).indexOf(rowId) !== -1) {
          const { type } = this.errorRowInfo[rowId];
          className += ` ${type}-cell ${colName}-cell`;
        }
        return ` ${className} ${colName}-cell`;
      } catch (e) {
        return this.cellClass && this.cellClass(cellContext);
      }
    },
    setCellNode(cellContext) {
      const { td, rowId } = cellContext;
      td.dataset.id = rowId;
      const showText = this.cellNode && this.cellNode(cellContext);
      if (showText) {
        return showText;
      }
      // checkbox of selection
      const select = this.getSelectCell(cellContext);
      return select || cellContext.value || "";
    },
    // 单元格失去焦点回调
    onAfterCellModifiedEnd(com, soucre, cell) {
      this.$emit("changeCellVal");
      const editCells = com.getEditVMap();
      Object.keys(editCells).forEach(item => {
        const { originVal, checkedKeys, rowId: rowKey, colName } = editCells[
          item
        ];
        this.setTobeSaveObj({
          rowKey,
          colName,
          val: checkedKeys || originVal
        });
      });
    },
    setTobeSaveObj(obj) {
      const { rowKey, colName, val } = obj;
      if (!rowKey) return;
      if (this.toBeSavedObj[rowKey]) {
        this.toBeSavedObj[rowKey][colName] = val;
      } else {
        this.toBeSavedObj[rowKey] = {
          [colName]: val
        };
      }
    },
    deleteTobeSaveObjById(id) {
      delete this.toBeSavedObj[id];
    },
    getToBeSaveObj() {
      return this.toBeSavedObj;
    },
    // 重新获取 toBeSaveedObj
    getNewToBeSaveObj() {
      this.clearToBeSaveObj();
      const editCells = this.$refs.simpleGrid.getEditVMap();
      Object.keys(editCells).forEach(item => {
        const { originVal, checkedKeys, rowId: rowKey, colName } = editCells[
          item
        ];
        if (this.toBeSavedObj[rowKey]) {
          this.toBeSavedObj[rowKey][colName] = checkedKeys || originVal;
        } else {
          this.toBeSavedObj[rowKey] = {
            [colName]: checkedKeys || originVal
          };
        }
      });
      return this.toBeSavedObj;
    },
    clearToBeSaveObj() {
      this.$set(this, "toBeSavedObj", {});
    },
    onCustomValidate(cell) {
      this.$emit("changeCellVal", cell);
      // 校验逻辑
      // dom 添加class
      const { id } = cell;
      const domIdent = `${this.ident}_${id.replace(",", "_")}`;
      const { res, paperwork } = this.validateCell(cell);
      cell.verifyState = res;
      cell.custom_verifyClass = ` ${ERROR_TIPS_CLASS_NAME}`;
      if (typeof res === "boolean" && !res) {
        this.verificationInfo[domIdent] = paperwork;
      } else {
        delete this.verificationInfo[domIdent];
      }
    },
    showValidateInfo(e) {
      const className = e.target.className || "";
      if (
        typeof className === "string" &&
        className.indexOf(ERROR_TIPS_CLASS_NAME) !== -1
      ) {
        const tdIdent = this.getTdIdentByClassName(className);
        // 设置错误提示弹窗位置信息
        // 设置错误提示文案信息
        this.showVerificationInfo = true;
        const { left, top, width } = this.getPopPosByTd(e.target);
        this.verificationPopInfo = {
          left:
            width > ERROR_TIPS_MIN_WIDTH
              ? left
              : left - (ERROR_TIPS_MIN_WIDTH - width),
          top,
          width,
          word: this.verificationInfo[tdIdent] || ""
        };
      } else {
        this.showVerificationInfo = false;
      }
    },
    getPopPosByTd(tdDom) {
      const { offsetHeight, offsetWidth } = tdDom;
      const { top: gridTop, left: gridLeft, width: gridWidth } = this.$refs
        .gridCont
        ? this.$refs.gridCont
          .querySelector(".simple-grid-wrapper")
          .getBoundingClientRect()
        : {};
      const { top: tdTop, left: tdLeft1 } = tdDom.getBoundingClientRect();
      const { clientHeight: filterH } = document.querySelector(
        ".filterCondition"
      ) || { clientHeight: 0 };
      return {
        left: tdLeft1 - gridLeft,
        top: tdTop - gridTop + offsetHeight + filterH,
        width: offsetWidth,
        limit: gridWidth
      };
    },
    getTdIdentByClassName(className) {
      const tempArr = className.split(" ");
      return tempArr.filter(item => {
        return !item.indexOf(this.ident);
      })[0];
    }
  }
};
</script>
<style lang="less" scoped>
.simple-grid-cont {
  position: relative;
  height: 100%;
  display: flex;
  flex-flow: column;
}
// 临时方案 等simpleGrid 提供二开口子，删除此代码
/deep/.handsontable td {
  border-color: @yn-border-color-base !important;
}
/deep/.htColHeaderBg {
  background-color: @yn-table-header-bg;
}
/deep/.hot-container > * {
  font-size: 14px;
}
/deep/.row-header-span {
  height: 100%;
  display: flex;
  align-items: center;
}
/deep/.icon-filter {
  font-size: 12px;
}
/deep/.header-col {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
}
/deep/.simple-cell-readonly {
  background-color: @yn-disabled-bg-color;
}
/deep/.filter-active {
  color: @yn-primary-color !important;
}
/deep/.icon-button {
  top: 0;
  height: 16px;
  line-height: 16px;
  color: @yn-label-color;
  cursor: pointer;
  margin-left: @rem4;
  margin-top: 1px;
}
/deep/.icon-button:hover {
  background: @yn-icon-bg-color;
  color: @yn-primary-color;
  border-radius: @yn-border-radius-base;
}
/deep/.handsontable td.verification-failed {
  border: 1px solid @yn-error-color !important;
}
.error-tips-cont {
  position: absolute;
  display: flex;
  justify-content: end;
  min-width: 200px; // 与ERROR_TIPS_MIN_WIDTH对应
  & > .tips {
    display: inline-block;
    position: absolute;
    height: 24px;
    line-height: 26px;
    padding: 0 12px;
    color: @yn-error-color;
    background: @yn-error-bg-color;
    z-index: 100;
    box-shadow: inset 0 0 5px 0 @yn-border-color-base;
    border-bottom-left-radius: @yn-border-radius-base;
    border-bottom-right-radius: @yn-border-radius-base;
  }
}
/deep/.error-cell {
  background: @yn-error-bg-color !important;
}
/deep/.warn-cell {
  background: @yn-border-color-base;
}
// 后续添加若校验提示色
/deep/.handsontable td {
  padding: 0 16px 0 16px;
  vertical-align: middle;
}
/deep/.handsontable td.cell-right {
  text-align: right;
}
/deep/.handsontable .simple-cell-right {
  padding-right: 16px;
}
/deep/.handsontable textarea {
  line-height: 32px;
}
/deep/.simple-grid-wrapper td.simple-cell-editing {
  background: @yn-body-background;
}
/deep/.head-right {
  .row-header-span,
  .header-col {
    justify-content: right;
  }
}
/deep/.simple-cell-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/deep/.handsontableInputHolder {
  .handsontableInput {
    max-width: 30px !important;
    max-height: 35px !important;
  }
}
/deep/.simple-cell {
  min-width: 100px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/deep/.header-col {
  position: relative;
  .header-col-text {
    display: inline-block;
    min-width: 30px;
    // width: 100%;
    max-width: 100%;
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
/deep/.head-right {
  .header-col-text {
    text-align: right;
  }
}
.grid-empty {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
  flex-flow: column;
}
</style>
