.dom-checkbox {
  position: relative;
  top: -0.09em;
  display: inline-block !important;
  line-height: 1;
  white-space: nowrap;
  vertical-align: middle;
  outline: none;
  cursor: pointer;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: @yn-text-color;
  font-size: 14px;
  font-variant: tabular-nums;
  width: 16px;
  height: 16px !important;

  .dom-input {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    cursor: pointer;
    opacity: 0;
  }
  .dom-state {
    border: 1px solid @yn-auxiliary-color;
    position: relative;
    top: 0;
    left: 0;
    display: block;
    width: 16px;
    height: 16px;
    background-color: @yn-body-background;
    border-radius: 2px;
    border-collapse: separate;
    transition: all 0.3s;
    &::after {
      position: absolute;
      display: table;
      top: 50%;
      left: 22%;
      width: 6px;
      height: 9px;
    }
  }
  &.full::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid @yn-primary-color;
    border-radius: 2px;
    visibility: hidden;
    animation: antCheckboxEffect 0.36s ease-in-out;
    animation-fill-mode: backwards;
    content: "";
  }

  &.full .dom-state {
    background-color: @yn-primary-color;
    border-color: @yn-primary-color;
    &::after {
      border: 2px solid @yn-body-background;
      border-top: 0;
      border-left: 0;
      transform: rotate(45deg) scale(1) translate(-50%, -50%);
      opacity: 1;
      transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
      content: " ";
    }
  }

  &.empty .dom-state {
    &::after {
      border: 2px solid @yn-body-background;
      border-top: 0;
      border-left: 0;
      transform: rotate(45deg) scale(1) translate(-50%, -50%);
      opacity: 0;
      transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
      content: " ";
    }
  }

  &.middle .dom-state {
    border-color: @yn-primary-color;
    background-color: @yn-body-background;
    &::after {
      position: absolute;
      display: table;
      top: 50%;
      left: 50%;
      width: 8px;
      height: 8px;
      background-color: @yn-primary-color;
      border: 0;
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
      content: " ";
    }
  }

  &.disabled {
    cursor: not-allowed;
    .dom-state {
      border-color: @yn-auxiliary-color !important;
      background-color: @yn-disabled-bg-color;
    }
  }
}
