import checkbox from "./checkbox";
import "./selection.less";
import selectionTree from "./selectionTree";
export default {
  props: {
    // 选中的行条目
    selection: {
      type: [<PERSON>rray, Boolean],
      default: () => false
    },
    hasSelectAllBox: {
      type: Boolean,
      default: true
    },
    hasSelectBox: {
      type: Function,
      default: null
    },
    isAsyncLoad: {
      type: Boolean,
      default: false
    },
    autoSelectChild: {
      type: Boolean,
      default: false
    },
    overSystem: {
      type: Boolean,
      default: false
    },
    // 反选的值
    unSelection: {
      type: [Array, Boolean],
      default: () => false
    },
    // 标识行的唯一字段， 同 yn-table
    rowKey: {
      type: String,
      default: "objectId"
    },
    // 是否可选中
    selectable: {
      type: [Boolean, Function],
      default: true
    },
    selectTitle: {
      type: String,
      default: ""
    },
    // 展示树子节点选择工具，可以通过父节点选中子节点，默认选中父节点不选中子节点
    showTreeSelect: {
      type: <PERSON>olean,
      default: false
    },
    // 每次传给grid组件的原始数据的平铺结构
    // sourceMap: {
    //   type: Object,
    //   default: () => null
    // },
    getCustomSelect: {
      type: Function,
      default: null
    }
  },
  mixins: [selectionTree],
  data() {
    return {
      // 行号记录
      rowMap: {},
      sourceMap: {},
      // Map 类型内部缓存，方便增删改查，提高性能
      _selection: new Map(), // 正选
      _unSelection: new Set(), // 反选
      _selectParentId: new Set(), // 选中的父项
      _dataSourceMap: {},
      // 是否全选：描述当前状态
      hasSelectAll: false,
      // 是否点击过全选且未再点击过取消全选：描述历史状态（用来标识，以记录反选数据）
      hasSelectedAll: false,
      // 是否处于部分选中状态
      indeterminate: false,
      currentSelect: null // 最后一次操作的数据 + 类型
    };
  },
  computed: {
    selectId() {
      return this.selection
        ? this.selection.map(item => item[this.rowKey])
        : [];
    },
    // 是否部分选中 动态渲染动态更新
    isIndeterminate() {
      if (!this.selection) return false;
      const selectId = this.selectId;
      return (
        Object.values(this.sourceMap || {}).some(
          item =>
            !item._isLoadMore &&
            !selectId.includes(item[this.rowKey]) &&
            this.isSelectable(item)
        ) && this.selection.length > 0
      );
    },
    isAllSelect() {
      if (!this.selection) return false;
      const selectId = this.selectId;
      return !Object.values(this.sourceMap || {}).some(
        item =>
          !item._isLoadMore &&
          !selectId.includes(item[this.rowKey]) &&
          this.isSelectable(item)
      );
    },
    isAllUnSelect() {
      if (!this.selection) return false;
      return this.selection.length === 0;
    }
  },
  watch: {
    // 异步数据，展开折叠数据，加载更多数据，分页数据监听，保证全选逻辑不出错
    dataSource(value) {
      this.rowMap = {};
      if (this.hasSelectAllBox && this.hasSelectAll) {
        this.selectAll();
        this.$emit("selectChange", [...this.$data._selection.values()]);
      }
    },
    tableData: {
      handler() {
        this.sourceMap = {};
        this.selection && this.getRecord(); // 防止row的引用地址变更没有及时更新sourceMap
      },
      deep: true
    },
    isIndeterminate(value) {
      if (value) {
        // 部分选
        this.hasSelectAll = false;
        this.indeterminate = true;
      } else {
        if (this.selection.length > 0 && this.$data._unSelection.size === 0) {
          // 全选
          this.indeterminate = false;
          this.hasSelectAll = true;
        } else if (
          this.selection.length > 0 &&
          this.$data._unSelection.size > 0
        ) {
          // 包含隐藏的反选
          this.indeterminate = true;
          this.hasSelectAll = false;
        } else {
          // 空选
          this.hasSelectAll = false;
          this.indeterminate = false;
        }
      }
      this.$refs.simpleGrid.setCell(0, 0, {
        noRealTime: false
      });
    },
    columns: {
      handler() {
        this.addSelectionColumn();
      }
    },
    selection: {
      handler(newVal, oldValue) {
        if (newVal && !oldValue) {
          // 进入选择模式
          this.addSelectionColumn();
          this.$data._selection = new Map(
            this.selection.map(item => [item[this.rowKey], item])
          );
          this.hasSelectAll = false;
          this.hasSelectedAll = false;
          this.indeterminate = this.$data._selection.length > 0;
          this.currentSelect = null;
          this.rowMap = {};
          this.$data._unSelection.clear();
        } else if (newVal && oldValue) {
          // 已经在选择模式
          this.$data._selection = new Map(
            this.selection.map(item => [item[this.rowKey], item])
          );
          if (this.isAllSelect) {
            this.indeterminate = false;
            this.hasSelectAll = true;
          }
          if (this.isAllUnSelect) {
            this.indeterminate = false;
            this.hasSelectAll = false;
          }
          if (
            this.hasSelectAll ||
            (this.selection && this.selection.length === 0)
          ) {
            if (!this.currentSelect) {
              setTimeout(() => {
                this.updateTableData();
                this.$emit("expandedRowsChange", []);
                this.getRecord(); // 防止row的引用地址变更没有及时更新sourceMap
              }, 0);
            }
          }
        } else if (!newVal && oldValue) {
          // 退出选择模式
          this.columns.shift();
          this.hasSelectAll = false;
          this.hasSelectedAll = false;
          this.indeterminate = false;
          this.currentSelect = null;
          this.rowMap = {};
          this.$data._selection.clear();
          this.$data._unSelection.clear();
          this.$emit("selectChange", []);
        }
      },
      immediate: true
    },
    hasSelectedAll() {
      this.$data._unSelection.clear();
    }
  },
  methods: {
    // 自定义单元格内容
    getSelectCell(cellContext) {
      const { col, td, rowId } = cellContext;
      const rowData = this.getRecord(rowId);
      const hasSelectBox = !this.hasSelectBox || this.hasSelectBox(rowData);
      if (this.selection) {
        const wrap = document.createElement("div");
        wrap.className = "dom-select-container";
        if (hasSelectBox) {
          const check = this.getCheckBox(cellContext, rowData);
          check && wrap.appendChild(check);
        }
        // selectionTree
        const tree = this.getTreeBox(cellContext, rowData);
        tree && wrap.appendChild(tree);
        if (col === 0 && !this.overSystem) {
          return wrap;
        } else if (this.overSystem && col === 1) {
          setTimeout(() => {
            this.$nextTick(() => {
              const target = td.parentNode.children[0];
              const old = target.querySelector(".dom-select-container");
              old && target.removeChild(old);
              target.appendChild(wrap);
            });
          }, 0);
        }
      }
    },
    // 自定义表头单元格内容
    getSelectHead(cellContext) {
      const { col } = cellContext;
      if (this.selection && col === 0) {
        this.checkAllBox = this.hasSelectAllBox
          ? checkbox({
            value: this.hasSelectAll,
            indeterminate: this.indeterminate,
            onCheckAllChange: isCheck => {
              this.$data._unSelection.clear();
              if (isCheck) {
                this.hasSelectedAll = true;
                this.hasSelectAll = true;
                this.indeterminate = false;
                this.selectAll();
                this.$data._selectParentId = new Set(
                  this.tableData.map(item => item[this.rowKey])
                );
              } else {
                this.hasSelectAll = false;
                this.hasSelectedAll = false;
                this.indeterminate = false;
                this.$data._selection.clear();
                this.$data._selectParentId.clear();
              }
              this.currentSelect = null;
              this.$emit("selectChange", [...this.$data._selection.values()]);
              this.$emit("onCheckAllChange", isCheck);
              this.$emit("expandedRowsChange", []);
              this.getRecord(); // 防止row的引用地址变更没有及时更新sourceMap
            }
          })
          : "";
        if (this.selectTitle) {
          const div = document.createElement("div");
          this.hasSelectAllBox && div.appendChild(this.checkAllBox);
          const span = document.createElement("span");
          span.innerHTML = this.selectTitle;
          span.className = "select-title";
          div.appendChild(span);
          return div;
        }
        return this.checkAllBox;
      }
    },
    isSelectable(params) {
      return typeof this.selectable === "boolean"
        ? this.selectable
        : this.selectable(params);
    },
    addSelectionColumn() {
      if (this.selection) {
        const hasSelect = this.columns.some(
          item => item.dataIndex === "_select"
        );
        if (!hasSelect) {
          this.columns.unshift({
            title: "",
            readOnly: true,
            dataIndex: "_select",
            key: "_select",
            cellType: "text",
            width: 80
          });
        }
      }
    },
    // 全选逻辑： 删除反选，删除不可选，选中已选，选中异步新加载项
    selectAll() {
      this.$data._selection = new Map([
        ...this.selection.map(item => [item[this.rowKey], item]),
        ...Object.values(this.sourceMap || {})
          .map(item => {
            return this.isSelectable(item) &&
              !this.$data._unSelection.has(item[this.rowKey])
              ? [item[this.rowKey], item]
              : null;
          })
          .filter(item => item)
      ]);
    },
    // 选择框逻辑: 关键处理全选后反选保留剩余和多次点击选中多条两种情况
    getCheckBox(cellContext, rowData) {
      const { row } = cellContext;
      if (!rowData) return null;
      const rowKey = rowData[this.rowKey];
      this.rowMap[rowKey] = row;
      const selectable = this.isSelectable(rowData);
      // 全选时，动态添加选中异步加载的子节点
      if (
        this.hasSelectAllBox &&
        this.hasSelectAll &&
        this.isSelectable(rowData)
      ) {
        this.$data._selection.set(rowKey, rowData);
        rowData.hasChildren && this.$data._selectParentId.add(rowKey);
        this.$data._unSelection.delete(rowKey);
        this.$data._unSelection.delete(rowData.parentId);
      }
      // 选中父项后，动态添加选中异步加载的子节点
      if (
        this.autoSelectChild &&
        this.$data._selectParentId.has(rowData.parentId)
      ) {
        !rowData._isLoadMore &&
          this.$data._selection.set(rowKey, this.sourceMap[rowKey]);
        this.$data._unSelection.delete(rowKey);
      }
      // 存在子项选中
      const hasChildSelect = (rowData.children || []).some(item =>
        this.$data._selection.has(item[this.rowKey])
      );
      // 存在子项没有选中
      const notAll =
        rowData.children && rowData.children.length > 0
          ? rowData.children.some(
            item =>
              !item._isLoadMore &&
                !this.$data._selection.has(item[this.rowKey])
          )
          : false;
      // 取消选中父项后，动态移除异步加载的子节点
      if (this.$data._unSelection.has(rowData.parentId)) {
        this.$data._selection.delete(rowKey);
      }
      // 被选中，或者父项被选中
      const isSelect =
        (this.hasSelectAllBox && this.hasSelectAll) ||
        this.$data._selection.has(rowKey) ||
        (this.autoSelectChild &&
          (this.$data._selectParentId.has(rowKey) ||
            this.$data._selectParentId.has(rowData.parentId)));
      const isIndeterminate = !this.hasSelectAll && hasChildSelect && notAll;
      return checkbox({
        value: isSelect,
        data: rowData,
        indeterminate: this.autoSelectChild ? isIndeterminate : false,
        disabled: !selectable,
        indeterminateChange: rowData.hasChildren
          ? (value, data) => {
            // 可以支持自定义选中哪些数据
            const selected = this.getCustomSelect
              ? this.getCustomSelect(rowKey)
              : [rowKey];

            this.addChildKey(selected, rowData);
            selected.forEach(item => {
              this.$data._selection.set(item, this.sourceMap[item]);
              this.$data._unSelection.delete(item);
            });
            if (this.autoSelectChild && rowData.hasChildren) {
              this.$data._selectParentId.add(rowKey);
            }
            this.syncSelection(isSelect, data, row);
          }
          : null,
        onChange: (isSelect, data) => {
          this.currentSelect = rowKey + "_" + isSelect;
          // 可以支持自定义选中哪些数据
          const selected = this.getCustomSelect
            ? this.getCustomSelect(rowKey)
            : [rowKey];
          // 选中
          if (isSelect) {
            // 选中父项，则子项自动选中
            this.addChildKey(selected, rowData);
            selected.forEach(item => {
              this.sourceMap[item] &&
                this.$data._selection.set(item, this.sourceMap[item]);
              this.$data._unSelection.delete(item);
              this.$data._unSelection.delete(rowData.parentId);
            });
            if (this.autoSelectChild) {
              if (rowData.hasChildren) {
                this.$data._selectParentId.add(rowKey);
              } else {
                const children = this.sourceMap[rowData.parentId].children;
                const notAll =
                  children && children.length > 0
                    ? children.some(
                      item =>
                        !item._isLoadMore &&
                          !this.$data._selection.has(item[this.rowKey])
                    )
                    : false;
                if (notAll) {
                  this.$data._selectParentId.delete(rowData.parentId);
                } else {
                  this.$data._selectParentId.add(rowData.parentId);
                }
              }
            }
          } else {
            // 取消选中
            selected.forEach(item => {
              if (this.hasSelectedAll) {
                this.$data._unSelection.add(item);
              }
              this.$data._selection.delete(item);
            });
            if (this.autoSelectChild) {
              if (rowData.hasChildren) {
                // 取消选中父节点，移除所有子项选中
                this.$data._selectParentId.delete(rowKey);
                (rowData.children || []).forEach(item => {
                  if (this.hasSelectedAll) {
                    !item._isLoadMore &&
                      this.$data._unSelection.add(item[this.rowKey]);
                  }
                  this.$data._selection.delete(item[this.rowKey]);
                });
              } else {
                // 取消选中子项，移除父项选中（父项选中代表全选）
                this.$data._selectParentId.delete(rowData.parentId);
                this.$data._selection.delete(rowData.parentId);
              }
            }
          }
          this.syncSelection(isSelect, data, row);
        }
      });
    },
    // 同步外部数据
    syncSelection(isSelect, data, row) {
      this.$emit("unSelectChange", [...this.$data._unSelection.values()]);
      this.$emit(
        "selectChange",
        [...this.$data._selection.values()],
        isSelect,
        data
      );
      this.$nextTick(() => {
        this.refreshTableSilent(row);
      });
    },
    // 自动添加子节点id
    addChildKey(origin, rowData) {
      if (
        this.autoSelectChild &&
        rowData.children &&
        rowData.children.length > 0
      ) {
        const temp = [];
        rowData.children.forEach(item => {
          if (item._isLoadMore) return;
          temp.push(item[this.rowKey]);
        });
        origin.push(...temp);
      }
    },
    /**
     * 静默刷新表格
     * @param {*} row
     */
    refreshTableSilent(row) {
      this.$refs.simpleGrid.setCell(row, 1, {
        noRealTime: false
      });
    },
    getRecord(rowId) {
      if (this.sourceMap[rowId]) return this.sourceMap[rowId];
      return this.deepDo(this.tableData, item => {
        this.sourceMap[item[this.rowKey]] = item;
        if (item[this.rowKey] === rowId) {
          return item;
        }
      });
    },
    // 重置选择，需要手动重置
    resetSelection() {
      this.rowMap = {};
      this.hasSelectAll = false;
      this.hasSelectedAll = false;
      this.indeterminate = false;
      this.currentSelect = null;
      this.$data._selection.clear();
      this.$data._unSelection.clear();
      this.$data._selectParentId.clear();
      this.$data._selectTreeParentId.clear();
      this.$emit("selectChange", []);
    },
    // tool
    deepDo(data, callback) {
      // 深度优先遍历非递归方式
      const nodes = [...data];
      if (nodes.length) {
        while (nodes.length) {
          const item = nodes.shift();
          const { children } = item;
          const result = callback(item);
          if (result) return result;
          if (children) {
            for (let i = 0, LEN = children.length; i < LEN; i++) {
              nodes.unshift(children[i]);
            }
          }
        }
      }
    }
  }
};
