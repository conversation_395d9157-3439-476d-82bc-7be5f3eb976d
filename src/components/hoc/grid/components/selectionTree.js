import icon from "@/views/process/control/jdom/icon";
import tooltip from "./tooltip";
export default {
  props: {
    treeSelection: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 保存所有选中的父项
      _selectTreeParentId: new Set()
    };
  },
  watch: {
    hasSelectAll(value) {
      if (value) {
        // 全选
        for (const key in this.sourceMap) {
          this.sourceMap[key].children &&
            this.$data._selectTreeParentId.add(key);
        }
        if (!this.currentSelect) {
          this.updateTableData();
        }
      }
    },
    hasSelectedAll(value) {
      // 取消全选
      if (!value) {
        this.$data._selectTreeParentId.clear();
        if (!this.currentSelect) {
          this.updateTableData();
        }
      }
    },
    currentSelect(currentSelect) {
      if (!currentSelect) return;
      const [key, action] = currentSelect.split("_");
      if (action === "true") {
        this.addSelectParent(key);
      } else {
        this.removeSelectParent(key);
      }
    }
  },
  getAllChildNumber() {},
  created() {
    document.body.addEventListener("mouseover", () => {
      document.querySelector(".dom-custom-tooltip") &&
        (document.querySelector(".dom-custom-tooltip").style.display = "none");
    });
  },
  methods: {
    // 选中父节点，批量选中子节点逻辑
    getTreeBox(cellContext, rowData) {
      if (!this.treeSelection) return "";
      // const { rowId } = cellContext;
      // const rowData = this.tableData[row - 1];
      // const rowData = this.sourceMap[rowId];
      if (!rowData) return null;
      const rowKey = rowData[this.rowKey];
      if (!this.sourceMap[rowKey] || !this.sourceMap[rowKey].children) return;
      const isSelect = this.$data._selectTreeParentId.has(rowKey);
      const treeBox = icon("icon-c1_select-all-subsets", {
        class: "icon-select-tree-child" + (isSelect ? " selected" : ""),
        isIconBtn: false
      });
      treeBox.onmouseover = e => {
        const rect = treeBox.getBoundingClientRect();
        tooltip({
          content: this.$data._selectTreeParentId.has(rowKey)
            ? this.$t_process("click_unselect")
            : this.$t_process("click_select"),
          rect
        });
        e.stopPropagation();
        return false;
      };
      treeBox.onmouseleave = e => {
        document.querySelector(".dom-custom-tooltip").style.display = "none";
      };
      treeBox.onclick = () => {
        let child = [];
        if (this.$data._selectTreeParentId.has(rowKey)) {
          this.$data._selectTreeParentId.delete(rowKey);
          child = this.removeSelectChild(rowKey); // 移除子项选中 // TODO 存在当存在某个组织在多个不同的合并组下的多个行号
          setTimeout(() => {
            this.removeSelectParent(rowKey); // 移除关联父项选中
            // TODO 移除关联项的父项选中，当存在某个组织在多个不同的合并组下时，同步取消选中多个合并组的选择状态
          }, 0);
        } else {
          this.$data._selectTreeParentId.add(rowKey);
          child = this.addSelectChild(rowKey);
          setTimeout(() => {
            this.addSelectParent(rowKey);
          }, 0);
        }
        this.$emit("selectChange", [...this.$data._selection.values()]);
        this.$nextTick(() => {
          (child || []).forEach(item => {
            const rowNum = this.rowMap[item[this.rowKey]];
            if (rowNum === undefined) return;
            this.$refs.simpleGrid.setCell(rowNum, 0, {
              noRealTime: false
            });
          });
        });
      };
      return treeBox;
    },
    // 获取所有后代
    getAllChild(row, arr = []) {
      if (row && row.children) {
        arr.push(...row.children);
        row.children.forEach(item => {
          this.getAllChild(item, arr);
        });
        return arr;
      }
    },
    addSelectParent(rowKey) {
      const parentId = this.sourceMap[rowKey]
        ? this.sourceMap[rowKey].parentId.v
        : "";
      if (!parentId) return;
      const isBrotherAllSelect = this.isBrotherAllSelect(rowKey);
      if (isBrotherAllSelect) {
        this.$data._selectTreeParentId.add(parentId);
        this.addSelectParent(parentId);
      }
    },
    // 兄弟节点是否被全选
    isBrotherAllSelect(rowKey) {
      const parentId = this.sourceMap[rowKey]
        ? this.sourceMap[rowKey].parentId.v
        : "";
      const parent = this.sourceMap[parentId];
      const brother = parent ? parent.children || [] : [];
      const isBrotherAllSelect = brother.every(item => {
        const Key = item[this.rowKey];
        const isSubSelect = item.children
          ? this.$data._selectTreeParentId.has(Key)
          : true;
        return this.$data._selection.has(Key) && isSubSelect;
      });
      return isBrotherAllSelect;
    },
    // 选中了所有父级
    addAllParent(rowKey) {
      const parentId = this.sourceMap[rowKey]
        ? this.sourceMap[rowKey].parentId.v
        : "";
      if (parentId) {
        this.$data._selectTreeParentId.add(parentId);
        this.addAllParent(parentId);
      }
    },
    // 移除选中所有父级
    removeSelectParent(rowKey) {
      const parentId = this.sourceMap[rowKey]
        ? this.sourceMap[rowKey].parentId.v
        : "";
      if (parentId) {
        this.$data._selectTreeParentId.delete(parentId);
        this.removeSelectParent(parentId);
      }
    },
    addSelectChild(rowKey) {
      const rowData = this.sourceMap[rowKey];
      const child = this.getAllChild(rowData);
      const selectableChild = [];
      child.forEach(item => {
        const selected = this.getCustomSelect
          ? this.getCustomSelect(item[this.rowKey])
          : [item[this.rowKey]];
        selected.forEach(slt => {
          const data = this.sourceMap[slt];
          if (this.selectable(data)) {
            this.$data._selection.set(data[this.rowKey], data);
            selectableChild.push(data);
          }
          if (item.children) {
            this.$data._selectTreeParentId.add(data[this.rowKey]);
          }
        });
      });
      return selectableChild;
    },
    removeSelectChild(rowKey) {
      const rowData = this.sourceMap[rowKey];
      const child = this.getAllChild(rowData);
      const selectableChild = [];
      child.forEach(item => {
        const selected = this.getCustomSelect
          ? this.getCustomSelect(item[this.rowKey])
          : [item[this.rowKey]];
        selected.forEach(slt => {
          const data = this.sourceMap[slt];
          if (this.selectable(data)) {
            this.$data._selection.delete(data[this.rowKey]);
            selectableChild.push(data);
          }
          if (item.children) {
            this.$data._selectTreeParentId.delete(data[this.rowKey]);
          }
        });
      });
      return selectableChild;
    }
  }
};
