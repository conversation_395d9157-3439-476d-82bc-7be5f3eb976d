import "./checkbox.less";
export default function({
  value = false,
  disabled = false,
  indeterminate = false,
  onChange = null,
  onCheckAllChange = null,
  indeterminateChange = null,
  data = null
}) {
  let _value = value;
  let _indeterminate = indeterminate;
  const div = document.createElement("div");
  div.className = "dom-checkbox";

  const input = document.createElement("input");
  input.className = "dom-input";
  input.disabled = disabled;
  input.type = "checkbox";

  const span = document.createElement("span");
  span.className = "dom-state";

  div.classList.remove("full");
  div.classList.remove("empty");
  div.classList.remove("middle");

  if (indeterminate) {
    // middle state
    div.classList.add("middle");
  } else if (value) {
    // empty state
    div.classList.add("full");
  } else {
    // full state
    div.classList.add("empty");
  }

  if (disabled) {
    div.classList.add("disabled");
  }
  div.onmousedown = e => {
    if (_indeterminate) {
      _value = true;
      div.classList.remove("middle");
      div.classList.add("full");
      _indeterminate = false;
      // 如果有 indeterminateChange 事件，说明是子父项全选选择框
      indeterminateChange && indeterminateChange(_indeterminate, data);
    } else {
      _value = !_value;
      div.classList.toggle("empty");
      div.classList.toggle("full");
      // 仅单选选择框有 onChange 事件，
      onChange && onChange(_value, data);
    }
    // 如果有 onCheckAllChange 事件，说明是全选选择框
    onCheckAllChange && onCheckAllChange(_value);
    e.stopPropagation();
    return false;
  };

  div.appendChild(input);
  div.appendChild(span);
  return div;
}
