function tooltip({ content, rect }) {
  const div =
    document.querySelector(".dom-custom-tooltip") ||
    document.createElement("div");
  div.className = "dom-custom-tooltip";
  div.style.cssText = `
    position: absolute;
    width: 100%;
    left: 0;
    top: 0
  `;
  div.innerHTML = `
        <div class="ant-tooltip ant-tooltip-placement-top" style="
        left:${rect.left + rect.width / 2}px;
        top: ${0}px;
        transform: translate(-50%, ${rect.top - rect.height}px)
        ">
          <div class="ant-tooltip-content">
            <div class="ant-tooltip-arrow"></div>
            <div role="tooltip" class="ant-tooltip-inner">
              <span>
                ${content}
              </span>
            </div>
          </div>
        </div>
    `;
  document.body.appendChild(div);
}

export default tooltip;
