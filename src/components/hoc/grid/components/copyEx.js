export default {
  data() {
    return {
      activeRecord: null, // 活动数据
      targetCoords: null // 目标单元格
    };
  },
  mounted() {
    document.body.addEventListener("copy", this.copyGlobal);
  },
  beforeDestroy() {
    document.body.removeEventListener("copy", this.copyGlobal);
  },
  methods: {
    copyGlobal() {
      this.copyText = this.formatCopy(
        window.getSelection().toString() || this.copyText
      );
    },
    async syncData() {
      if (!this.activeRecord) return;
      const key = this.columns[this.targetCoords[0].startCol].dataIndex;
      const text = await this.getClipboardContent();
      const isValue = typeof this.activeRecord[key] === "string";
      isValue
        ? this.$set(this.activeRecord, key, text)
        : (this.$set(this.activeRecord[key], "v", text),
        this.$set(this.activeRecord[key], "oldV", text));
    },
    async pasteEnd(data, coords) {
      const readOnly = this.columns[this.targetCoords[0].startCol].readOnly;
      const canPaste = this.columns[this.targetCoords[0].startCol].paste;
      if (readOnly === false && canPaste !== false) {
        const text = await this.getClipboardContent();
        const cell = this.$refs.simpleGrid.getCellMeta(
          this.targetCoords[0].startRow,
          this.targetCoords[0].startCol
        );
        cell.originVal = text;
        cell.value = text;
        this.onCustomValidate(cell);
        this.$nextTick(() => {
          this.$refs.simpleGrid.setCell(
            coords[0].startRow,
            coords[0].startCol,
            {
              value: text,
              originVal: text,
              noRealTime: false
            }
          );
        });
      }
    },
    beforePaste(data, coords) {
      this.targetCoords = coords;
      const readOnly = this.columns[this.targetCoords[0].startCol].readOnly;
      const canPaste = this.columns[this.targetCoords[0].startCol].paste;
      if (readOnly === false && canPaste !== false) {
        this.syncData();
      }
      return false;
    },
    beforeCopy(data, coords) {
      document.body.removeEventListener("copy", this.copyGlobal);
      const relData = this.transformData(coords);
      this.copyToClipboard(this.formatCopy(relData));
      document.body.addEventListener("copy", this.copyGlobal);
    },
    transformData(coords) {
      const { endRow, endCol, startCol, startRow } = coords[0];
      const rowLen = endRow - startRow + 1;
      const colLen = endCol - startCol + 1;
      let arrMap;
      if (rowLen > 1) {
        arrMap = new Array(rowLen)
          .fill("")
          .map(item => new Array(colLen).fill(""));
        for (let i = 0; i < rowLen; i++) {
          for (let j = 0; j < colLen; j++) {
            const tdIdent = `${this.ident}_${i + startRow}_${j + startCol}`;
            const dom = this.getRelCell(tdIdent);
            arrMap[i][j] = dom.innerText;
          }
        }
      } else {
        arrMap = new Array(colLen).fill("");
        for (let i = 0; i < colLen; i++) {
          const tdIdent = `${this.ident}_${startRow}_${i + startCol}`;
          const dom = this.getRelCell(tdIdent);
          arrMap[i] = dom.innerText;
        }
      }
      return arrMap;
    },
    getRelCell(tdIdent) {
      const left = document.querySelector(`.ht_clone_left .${tdIdent}`);
      const right = document.querySelector(`.ht_clone_right .${tdIdent}`);
      const core = document.querySelector(`.${tdIdent}`);
      return left || right || core;
    },
    handlerDataRecord(e) {
      let td = e.target;
      while (td.classList && !td.classList.contains("simple-cell")) {
        td = td.parentNode;
      }
      const id = td && td.dataset ? td.dataset.id : "";
      if (!id) {
        this.activeRecord = null;
        return;
      }
      this.activeRecord = this.getRecord(id);
    },
    formatCopy(data) {
      if (typeof data !== "object") {
        return data;
      }
      if (typeof data[0] === "string") {
        return data.join("\t");
      } else {
        return data.map(item => item.join("\t")).join("\n");
      }
    },
    copyToClipboard(text) {
      // navigator.clipboard
      //   .writeText(text)
      //   .then(() => {})
      //   .catch(err => {
      //     console.error("无法复制此文本：", err);
      //   });
      const textArea = document.createElement("textArea");
      textArea.value = text;
      document.body.appendChild(textArea);

      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
      this.copyText = text;
    },
    async getClipboardContent() {
      // const text = await navigator.clipboard.readText();
      // return text;

      // const input = document.createElement("input");
      // document.body.appendChild(input);
      // input.focus();
      // document.execCommand("paste");
      // const pasteData = input.value;
      // document.body.removeChild(input);
      // return pasteData;

      return this.copyText;
    }
  }
};
