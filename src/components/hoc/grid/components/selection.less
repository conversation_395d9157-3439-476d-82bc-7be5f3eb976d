.dom-selection-container {
  display: flex;
  align-items: center;
  .dom-select-tree {
    width: 16px;
    height: 16px;
  }
}

.dom-icon.icon-select-tree-child {
  color: @yn-label-color;
  &.selected {
    color: @yn-primary-color;
  }
  &.readonly:hover {
    color: @yn-primary-color !important;
    background: transparent !important;
  }
  &:hover {
    color: @yn-primary-color !important;
    .icon {
      background: @yn-icon-bg-color;
    }
  }
}

.simple-cell._select-cell {
  padding: 0 1rem !important;
}
._select .select-title {
  margin-left: 1rem;
}
