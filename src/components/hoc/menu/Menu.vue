<template>
  <yn-menu
    class="menu_content"
    mode="inline"
    :selectedKeys="[currActiveItemKey]"
    :openKeys="openKeys"
    @openChange="onOpenChange"
    @click="menuClick"
  >
    <template v-for="item in menuInfo">
      <yn-sub-menu
        v-if="item.children && item.children.length > 0"
        :key="item.menuId"
      >
        <span slot="title">
          <span> {{ item["title"] }}</span>
        </span>
        <yn-menu-item
          v-for="menuItem in item.children"
          :key="menuItem.menuId"
          :itemData="menuItem"
        >
          {{ menuItem["title"] }}
        </yn-menu-item>
      </yn-sub-menu>
      <yn-menu-item v-else :key="item.menuId">
        {{ item["title"] }}
      </yn-menu-item>
    </template>
  </yn-menu>
</template>
<script>
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-sub-menu/";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { mapState } from "vuex";
import { APPS } from "@/config/SETUP";
import savePromptMixin from "@/mixin/savePrompt.js";
export default {
  name: "Menu",
  mixins: [savePromptMixin],
  data() {
    return {
      openKeys: [], // 展开的父项集合
      currActiveItemKey: "", // 当前 选中的菜单项key
      curClickItemKey: "", // 当前点击的菜单项key
      lang:
        DsUtils.getSessionStorageItem("lang", {
          storagePrefix: APPS.NAME,
          isJson: true
        }) || "zh_CN",
      mapRouterOptions: {}
    };
  },
  computed: {
    ...mapState("common", {
      tabs: state => state.tabs,
      menuInfo: state => state.menuList,
      menuMapObj: state => state.menuMapObj,
      tabActiveId: state => state.tabActiveId
    })
  },
  watch: {
    tabActiveId: {
      handler(newVal) {
        const { parentId } = this.menuMapObj[newVal] || {};
        if (parentId && this.openKeys.indexOf(parentId) === -1) {
          this.openKeys.push(parentId);
        }
        if (this.menuMapObj[newVal]) {
          this.currActiveItemKey = newVal;
        }
      }
    }
  },
  mounted() {
    const mapObj = {};
    const loop = data => {
      data &&
        data.forEach(option => {
          const { children, path, meta } = option;
          if (meta.type === "consolidation_module") {
            mapObj[path] = option;
          }
          if (children && children.length > 0) {
            loop(children);
          }
        });
    };
    loop(this.$router.options.routes);
    this.mapRouterOptions = mapObj;
  },
  methods: {
    // 菜单点击事件，处理 添加tab或者激活tab
    menuClick(e) {
      const { key } = e;
      this.curClickItemKey = key;
      this.changeTab();
    },
    changeTab() {
      this.currActiveItemKey = this.curClickItemKey;
      const { menuId, uri, ...others } = this.menuMapObj[this.curClickItemKey];
      const urlParamIndex = location.href.indexOf("?");
      const urlParams = location.href.slice(urlParamIndex + 1);
      const uriStr = `${uri}${uri.indexOf("?") === -1 ? "?" : "&"}${urlParams}`;
      // 判断是否有此tab 信息
      const { name, meta } = this.getRouterInfo(uri);
      this.newtabMixin({
        id: menuId,
        uri: uriStr,
        params: {},
        noKeepAlive: !!meta.noKeepAlive, // 是否需要缓存
        router: name || "systemTab", // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
        ...others
      });
    },
    getRouterInfo(uri) {
      const path =
        Object.keys(this.mapRouterOptions).find(item => {
          return uri.indexOf(item) !== -1;
        }) || "";
      const defaultObj = {
        name: "",
        meta: {}
      };
      return path ? this.mapRouterOptions[path] : defaultObj;
    },
    onOpenChange(openKeys) {
      this.$set(this, "openKeys", openKeys);
    }
  }
};
</script>
<style lang="less" scoped>
.menu_content {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  border-right: 0;
}
</style>
