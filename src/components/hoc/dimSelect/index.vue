<template>
  <yn-modal
    :title="title"
    :visible="visible"
    class="dimSelect"
    width="60rem"
    @cancel="handleCancel"
  >
    <template slot="footer">
      <yn-button key="back" @click="handleCancel"> 取消 </yn-button>
      <yn-button key="submit" type="primary" @click="handleSubmit">
        下一步
      </yn-button>
    </template>
    <div class="content-wrap">
      <yn-input-search
        v-model="search"
        class="search-input"
        placeholder="请输入"
      />
      <div class="check-area">
        <yn-checkbox-group v-model="checked" :options="list">
          <span slot="label" slot-scope="{ labelSlot }" :title="labelSlot">{{
            labelSlot
          }}</span>
        </yn-checkbox-group>
      </div>
    </div>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-checkbox-group/";
import "yn-p1/libs/components/yn-checkbox/";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ""
    },
    selected: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      search: "",
      checked: []
    };
  },
  computed: {
    list() {
      return this.search
        ? this.data.filter(item => item.labelSlot.match(this.search))
        : this.data;
    }
  },
  watch: {
    visible() {
      this.search = "";
    },
    selected() {
      this.checked = this.selected;
    }
  },
  methods: {
    handleCancel() {
      this.$emit("update:visible", false);
      this.$emit("cancel");
    },
    async handleSubmit() {
      this.$emit("update:visible", false);
      this.$emit("update:selected", this.checked);
      this.$emit("ok");
    }
  }
};
</script>

<style lang="less" scoped>
.content-wrap {
  height: 20rem;
  display: flex;
  flex-direction: column;
  .search-input {
    width: 40%;
    margin-bottom: 0.75rem;
  }
}
.check-area {
  height: 0;
  flex: 1;
  overflow: scroll;
  /deep/ .ant-checkbox-group {
    width: 100%;
  }
  /deep/ .ant-checkbox-wrapper {
    margin-bottom: 0.75rem;
    width: 20%;
    margin-left: 0;
    padding-right: 0.5rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
<style lang="less">
.dimSelect .ant-modal-mask,
.dimSelect .ant-modal-wrap {
  z-index: 1001 !important;
}
</style>
