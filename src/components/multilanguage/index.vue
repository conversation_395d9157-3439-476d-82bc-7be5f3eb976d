<template>
  <yn-modal
    :title="$t_common('multilingual_setting')"
    :visible="languageVisible"
    :bodyStyle="bodyStyle"
    @cancel="cancelEvent"
    @ok="okEvent"
  >
    <yn-spin :spinning="loading">
      <yn-form :form="multiForm" v-bind="formLayout">
        <yn-form-item
          v-for="item in multilanguageArr"
          :key="item.code"
          :label="item.name"
        >
          <yn-input
            v-decorator="[
              item.code,
              { initialValue: item.text ? item.text : '' }
            ]"
            :maxLength="maxLength"
            :placeholder="$t_common('input_message')"
            :disabled="item.disabled"
          />
        </yn-form-item>
      </yn-form>
    </yn-spin>
  </yn-modal>
</template>
<script>
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-form-item/";
import cloneDeep from "lodash/cloneDeep";
import { mapState } from "vuex";
export default {
  name: "LanguageModal",
  props: {
    languageVisible: {
      type: Boolean,
      default: false
    },
    maxLength: {
      type: Number,
      default: 64
    },
    languageList: {
      type: Array,
      default: () => []
    },
    isSaveAs: {
      type: Boolean,
      default: false
    },
    // 当前语言内容
    curText: {
      type: String,
      default: ""
    },
    // 返回值是否要加入当前语种的值，默认true 加入当前语种的值
    addCurLang: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      formLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 }
      },
      multiForm: this.$form.createForm(this, { name: "multilanguage" }),
      multilanguageArr: [],
      bodyStyle: {
        minHeight: "80px"
      }
    };
  },
  computed: {
    ...mapState({
      // 多语言语种
      languages: state => {
        return state.common.languages;
      },
      // 当前语言环境
      lang: state => state.common.lang
    })
  },
  watch: {
    languageVisible(val) {
      if (val) {
        // 不存在返回
        if (!this.languageList) return;
        // 位空，重置值
        if (this.languageList.length === 0) {
          this.multilanguageArr = this.multilanguageArr.map(item => {
            item.text = "";
            return item;
          });
          return;
        }
        // 有值赋值
        this.multilanguageArr = this.syncLanguages();
      }
    },
    languages: {
      immediate: true,
      deep: true,
      handler(val) {
        if (!val || !Array.isArray(val) || val.length === 0) {
          this.loading = true;
          // 如果 languages 语种为空 重新获取一边接口
          this.$store
            .dispatch("common/getEnableLanguages")
            .then(() => {
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          this.multilanguageArr = this.syncLanguages();
        }
      }
    },
    multilanguageArr(value) {
      const temp = {};
      value &&
        value.forEach(item => {
          temp[item.code || item.languageCode] = item.text;
        });
      this.multiForm.setFieldsValue(temp);
      const updateLanguage = this.getLanguageVal();
      this.$emit("update:languageList", updateLanguage);
    },
    curText() {
      const updateLanguage = this.getLanguageVal();
      this.$emit("update:languageList", updateLanguage);
    }
  },
  mounted() {
    this.multilanguageArr = this.syncLanguages();
  },
  methods: {
    syncLanguages() {
      return cloneDeep(this.languages).map(item => {
        const cur = this.languageList.find(
          jItem => jItem.languageCode === item.code
        );
        if (cur && cur.text) {
          item.text = this.isSaveAs
            ? this.$t_process("duplicate_copy", [cur.text])
            : cur.text;
        }
        return item;
      });
    },
    cancelEvent() {
      this.multiForm.resetFields();
      const updateLanguage = this.getLanguageVal();
      this.$emit("cancelMultilanguage", updateLanguage);
    },
    okEvent() {
      const updateLanguage = this.getLanguageVal();
      this.$emit("cancelMultilanguage", updateLanguage);
    },
    getLanguageVal() {
      const updateLanguage = this.multilanguageArr.map(item => {
        const inputText = this.languageVisible
          ? this.multiForm.getFieldsValue()[item.code || item.languageCode]
          : item.text;
        return {
          languageCode: item.code || item.languageCode,
          text: inputText
        };
        // 如果不在中文简体环境下，且zh_CN的值为空，将curText 赋值到中文简体上，这是不准确的，如果多语言弹框
        // 第一次输入，打开多语言弹框，中文是当前语言第一次输入，当关闭多语言，再次改变值，打开弹框，中文展示的是历史值

        // 如果不在中文简体环境下，且zh_CN的值为空，将curText 赋值到中文简体上
        // const cText = item.text ? item.text.replace(/(^s*)|(s*$)/g, "") : "";
        // const inputText = this.multiForm.getFieldsValue()[item.code];
        // return {
        //   languageCode: item.code,
        //   text:
        //     (item.code === "zh_CN" && !cText
        //       ? this.curText || inputText
        //       : inputText) || ""
        // };
        // const cText = item.text ? item.text.replace(/(^s*)|(s*$)/g, "") : "";
      });
      if (this.addCurLang) {
        updateLanguage.push({
          languageCode: this.lang,
          text: this.curText
        });
      }
      return updateLanguage.filter(item => item.text);
    }
  }
};
</script>

<style lang="less" scoped></style>
