<template>
  <yn-modal
    :title="
      `${$t_structures('ownership_info')}（${clickInfo.text.replace(
        '<br/>',
        ''
      )}）`
    "
    :visible="showHoldingInfo"
    :width="560"
    @cancel="closeEvent"
  >
    <div class="modal-content">
      <yn-radio-group v-model="tabValue" class="left">
        <yn-radio-button value="shareHolder">
          {{ $t_structures("as_investor") }}
        </yn-radio-button>
        <yn-radio-button value="stakeHolder">
          {{ $t_structures("as_investee") }}
        </yn-radio-button>
      </yn-radio-group>
      <yn-tabs :activeKey="tabValue">
        <!-- 作为持股方 -->
        <yn-tab-pane key="shareHolder" tab="">
          <yn-spin :spinning="shareLoading">
            <holding-info-table
              tableType="shareTable"
              :dataSource="shareTableData"
            />
          </yn-spin>
        </yn-tab-pane>
        <!-- 作为被持股方 -->
        <yn-tab-pane key="stakeHolder" tab="">
          <holding-info-table
            tableType="stakeTable"
            :dataSource="stakeTableData"
          />
        </yn-tab-pane>
      </yn-tabs>
    </div>
    <template slot="footer">
      <yn-button type="primary" @click="closeEvent">
        {{ $t_common("close") }}
      </yn-button>
    </template>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-tabs/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-tab-pane/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-radio-button/";
import commonService from "@/services/common";
import HoldingInfoTable from "./holdingInfoTable.vue";

export default {
  components: {
    HoldingInfoTable
  },
  props: {
    companyName: {
      type: String,
      default: ""
    },
    showHoldingInfo: {
      type: Boolean,
      default: false
    },
    pageDimObj: {
      type: Object,
      default: () => {}
    },
    clickInfo: {
      type: Object,
      default: () => ({
        id: "",
        text: ""
      })
    }
  },
  data() {
    return {
      tabValue: "shareHolder", // 持股方
      shareLoading: true, // 持股方loading
      shareTableData: [], // tab1 table表格数据
      shareDatas: [], // 可选项
      stakeTableData: [] // tab2 表格table 数据
    };
  },
  mounted() {
    this.getTableDatas();
  },
  methods: {
    // 关闭弹窗
    closeEvent() {
      this.$emit("closeHoldingInfo", false);
    },
    // 获取持股信息列表数据
    async getTableDatas(pageDimObj) {
      const { period, version, year } = this.pageDimObj;
      await commonService("getEquityManagements", {
        period,
        version,
        year,
        id: this.clickInfo.id
      }).then(res => {
        if (res.status === 200) {
          const shareDatas = [];
          res.data.ICP &&
            res.data.ICP.map(icpItem => {
              icpItem.key = icpItem.objectId;
              icpItem.name = icpItem.entityName;
              shareDatas.push({
                id: icpItem.name,
                name: icpItem.name
              });
              return icpItem;
            });
          this.shareDatas = shareDatas;
          res.data.Entity &&
            res.data.Entity.map(entityItem => {
              entityItem.key = entityItem.objectId;
              entityItem.name = entityItem.iCPName;
              return entityItem;
            });
          this.shareTableData = res.data.ICP || [];
          this.stakeTableData = res.data.Entity || [];
          this.shareLoading = false;
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.modal-content {
  /deep/.ant-tabs-bar {
    border-bottom: none;
  }
  /deep/.ant-tabs {
    height: calc(100% - 90px) !important;
    .ant-tabs-top-content,
    .ant-tabs-bottom-content {
      height: 100% !important;
    }
  }
  /deep/.ant-tabs-nav-wrap {
    height: 0px;
  }
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    background: #1e88e5;
    color: #ffffff;
  }
  /deep/ .ant-table-title {
    background: #fef1e9;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .search-conditions {
    display: flex;
    .condition-tag {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-right: 8px;
      .condition-title {
        font-size: 12px;
        color: #1a253b;
      }
      .seachWord {
        background: #fff;
        display: inline-block;
        padding: 5px;
        margin: 2px 6px 2px 0;
        .clearSearchItem {
          margin-left: 12px;
          color: #1a253b;
          cursor: pointer;
        }
      }
    }
  }
  .clear-all {
    cursor: pointer;
    font-size: 12px;
    color: #0052cc;
  }
}
</style>
