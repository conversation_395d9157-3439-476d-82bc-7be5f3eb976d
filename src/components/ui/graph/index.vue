<template>
  <div ref="graphChartCont" class="graph-chart-cont">
    <div class="toolBar">
      <div class="toolbar-left">
        <yn-dropdown v-if="dropDownMenu && dropDownMenu.length > 0">
          <span class="dropMenu">
            <svg-icon
              type="icon-Process"
              :class="chartLayout"
              :title="$t_structures('switch_views')"
              @click="changeChartLayout"
            />
            <yn-icon type="down" />
          </span>
          <yn-menu
            slot="overlay"
            :selectedKeys="selectedKeys"
            @click="handleClick"
          >
            <yn-menu-item v-for="item in dropDownMenu" :key="item.id">
              <span>{{ item.name }}</span>
            </yn-menu-item>
          </yn-menu>
        </yn-dropdown>
        <svg-icon
          v-else
          type="icon-Process"
          :class="chartLayout"
          :title="$t_structures('switch_views')"
          @click="changeChartLayout"
        />
      </div>
      <div class="toolBar_right">
        <div class="slider">
          <yn-icon type="minus" class="sliderBtn" @click="zoomValueMinus" />
          <div class="sliderCont">
            <yn-slider
              v-model="zoomValue"
              :min="zoomRange.min"
              :max="zoomRange.max"
              @change="setZoomVal_customer"
            />
          </div>
          <yn-icon type="plus" class="sliderBtn" @click="zoomValuePlus" />
          <span class="count-text">{{ `${zoomValue}%` }}</span>
        </div>
        <yn-divider type="vertical" />
        <!-- 展开或者闭合所有节点  -->
        <svg-icon
          :type="iconType"
          @click="openOrCloseAllNodes"
          :title="puckerBtnTitle"
        />
        <!-- 设置按钮 -->
        <yn-popover
          v-model="chartSettingVisible"
          title=""
          trigger="click"
          class="icon-setting"
          placement="bottomRight"
        >
          <template slot="content">
            <yn-radio-group
              class="ragio-group-cont"
              v-model="nodeTitleType"
              @change="handleChartSetting"
            >
              <yn-radio value="NAME">{{ $t_common("name") }}</yn-radio>
              <yn-radio value="CODE">{{
                $t_structures("unique_identifier")
              }}</yn-radio>
              <yn-radio value="NAME_AND_CODE">{{
                `${$t_structures("unique_identifier")}+${$t_common("name")}`
              }}</yn-radio>
            </yn-radio-group>
          </template>
          <svg-icon type="icon-set-up" :title="$t_structures('settings')" />
        </yn-popover>
        <!-- 刷新按钮 -->
        <svg-icon
          type="icon-shuaxin"
          :title="$t_structures('refresh')"
          @click="refreshChart"
        />
      </div>
    </div>
    <div class="chart-cont">
      <graph-chart
        v-if="isShowChart"
        ref="graphChart"
        :graphData="data"
        :graphConfig="graphConfig"
        :graphLayout="chartLayout"
        :zoomRange="zoomRange"
        :openOrCloseAllNode="iconType == 'icon-zhedie' ? 'open' : 'close'"
        :pageDimObj="pageDimObj"
        @setZoomVal="setZoomVal"
        @nodeClick="handleNodeClick"
      />
    </div>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-slider/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-popover/";
import "yn-p1/libs/components/yn-radio/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-icon-button/";

import commonService from "@/services/common";

import GraphChart from "./Graph.vue";
const ZOOM_RANGE_MIN = 10;
const ZOOM_RANGE_MAX = 200;
const DEFAULT_ZOOM_VALUE = 100;
export default {
  name: "Graph",
  components: {
    GraphChart
  },
  props: {
    graphData: Object,
    graphConfig: Object,
    dropDownMenu: Array, // 如果存在，则会出现 下拉菜单
    nodeClick: Function,
    pageDimObj: {
      type: Object,
      default: () => {}
    },
    pageType: {
      type: String,
      default: "organizationManagementList"
    }
  },
  data() {
    return {
      data: {},
      selectedKeys: [],
      chartLayout: "tb", // tb or lr
      iconType: "icon-zhedie", // icon-zhankai
      chartSettingVisible: false,
      cacheNodeTitleType: "NAME",
      nodeTitleType: "NAME", // 图标的节点名称类型 NAME CODE NAME_AND_CODE
      nodeTitleTypeId: null, // 节点类型 id。 根据id 传给后端进行修改
      zoomValue: DEFAULT_ZOOM_VALUE,
      isShowChart: true,
      zoomRange: {
        max: ZOOM_RANGE_MAX,
        min: ZOOM_RANGE_MIN
      }
    };
  },
  watch: {
    graphData(newVal) {
      this.isShowChart = false;
      this.iconType = "icon-zhedie";
      this.$nextTick(() => {
        this.isShowChart = true;
        const { rootId, links, nodes } = newVal;
        const newNodes =
          nodes &&
          nodes.map(item => {
            if (item.id === rootId) {
              item.color = "#003F9E";
              item.fontColor = "#fff";
            }
            const { text, ...otherProps } = item;
            item.name = text;
            return {
              name: text,
              text: this.getNodeName(item),
              ...otherProps
            };
          });
        this.data = {
          rootId,
          links,
          nodes: newNodes
        };
      });
    },
    chartSettingVisible(newVal) {
      if (!newVal) {
        this.saveOrUpdateUserSetting({
          objectId: this.nodeTitleTypeId,
          tag: "userIndexName",
          key: this.pageType,
          value: this.nodeTitleType
        });
      }
    }
  },
  computed: {
    puckerBtnTitle() {
      return this.iconType === "icon-zhedie"
        ? this.$t_structures("collapse_all")
        : this.$t_structures("expand_all");
    }
  },
  created() {
    this.getSettingType();
  },
  mounted() {
    const self = this;
    document.addEventListener(
      "click",
      e => {
        const events = e || window.event;
        self.closeSettingPopover(events);
      },
      true
    );
  },
  destroyed() {
    document.removeEventListener("click", this.closeSettingPopover);
  },
  methods: {
    // 获取设置类型
    getSettingType() {
      commonService("getLastSettingV", {
        key: this.pageType, // 持股方
        tag: "userIndexName"
      }).then(res => {
        if (res.data.data.value) {
          this.nodeTitleType = res.data.data.value;
          this.nodeTitleTypeId = res.data.data.objectId;
        } else {
          this.nodeTitleType = "NAME";
        }
      });
    },
    // 保存设置类型
    saveOrUpdateUserSetting(params) {
      return commonService("saveOrUpdateUserSetting", params);
    },

    getNodeName(node) {
      let nodeTitleType = this.nodeTitleType;
      if (nodeTitleType === "CODE") {
        nodeTitleType = "memberCodeIndex";
      }
      const propName = nodeTitleType;
      if (nodeTitleType === "NAME_AND_CODE") {
        return `${node["memberCodeIndex"]}<br/>${node["name"]}`;
      } else {
        return node[propName];
      }
    },
    handleNodeClick(nodeObject, event) {
      this.$emit("nodeClick", nodeObject, event);
    },
    closeSettingPopover(events) {
      let currDom = events.target;
      let isBreak = true;
      while (isBreak) {
        if (
          typeof currDom.className === "string" &&
          currDom.className.indexOf("ant-popover-inner") !== -1
        ) {
          isBreak = false;
        } else if (currDom.tagName === "BODY") {
          isBreak = false;
          this.chartSettingVisible = false;
        } else {
          currDom = currDom.parentNode;
        }
      }
    },
    // 处理
    handleChartSetting(e) {
      let nodeTitleType = e.target.value;
      this.nodeTitleType = nodeTitleType;
      this.zoomValue = DEFAULT_ZOOM_VALUE;
      this.isShowChart = false;
      if (nodeTitleType === "CODE") {
        nodeTitleType = "memberCodeIndex";
      }
      const { nodes, links, rootId } = this.data;
      const newNodes = nodes
        ? nodes.map(item => {
            const propName = nodeTitleType;
            if (nodeTitleType === "NAME_AND_CODE") {
              item.text = `${item["memberCodeIndex"]}<br/>${item["name"]}`;
            } else {
              item.text = item[propName];
            }
            return item;
          })
        : [];
      this.$nextTick(() => {
        this.isShowChart = true;
        this.data = {
          rootId,
          links,
          nodes: newNodes
        };
      });
    },
    // 切换下来菜单项
    handleClick(e) {
      // 如果 不存在 key 则 将key 放置数组第一项
      if (this.selectedKeys.indexOf(e.key) === -1) {
        this.selectedKeys.splice(0, 1, e.key);
      } else {
        // 存在相同的key 则置空数据
        this.selectedKeys.splice(0, 1);
      }
      this.$emit("changeMenu", this.selectedKeys);
    },
    changeChartLayout() {
      this.isShowChart = false;
      if (this.chartLayout === "tb") {
        this.chartLayout = "lr";
      } else {
        this.chartLayout = "tb";
      }
      this.zoomValue = DEFAULT_ZOOM_VALUE;
      this.$nextTick(() => {
        this.isShowChart = true;
      });
    },
    changeNodeTitle() {
      this.chartSettingVisible = true;
    },
    openOrCloseAllNodes() {
      if (this.iconType === "icon-zhedie") {
        this.iconType = "icon-zhankai";
      } else {
        this.iconType = "icon-zhedie";
      }
    },
    refreshChart() {
      this.$refs.graphChart.$refs.seeksRelationGraph.expandAllNode();
      this.$refs.graphChart.$refs.seeksRelationGraph.refresh();
      this.zoomValue = DEFAULT_ZOOM_VALUE;
    },
    zoomValueMinus() {
      let currVal = this.zoomValue;
      const result = (currVal -= Number(ZOOM_RANGE_MIN));
      if (result < this.zoomRange.min) {
        this.zoomValue = this.zoomRange.min;
      } else {
        this.zoomValue = result;
        this.$refs.graphChart.$refs.seeksRelationGraph.zoom(-ZOOM_RANGE_MIN);
      }
    },
    zoomValuePlus() {
      let currVal = this.zoomValue;
      const result = (currVal += Number(ZOOM_RANGE_MIN));
      if (result > this.zoomRange.max) {
        this.zoomValue = this.zoomRange.max;
      } else {
        this.zoomValue = result;
        this.$refs.graphChart.$refs.seeksRelationGraph.zoom(ZOOM_RANGE_MIN);
      }
    },
    setZoomVal(val) {
      this.zoomValue = val;
    },
    setZoomVal_customer(val) {
      const seeksRelationGraph = this.$refs.graphChart.$refs.seeksRelationGraph;
      const oldZoomVal = seeksRelationGraph.graphSetting.canvasZoom;
      const diff = val - oldZoomVal;
      this.$refs.graphChart.$refs.seeksRelationGraph.zoom(diff);
    },
    closeChart() {
      this.$emit("changeGraphViewState", false);
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.ant-spin-nested-loading {
  height: calc(100% - 100px);
}
.graph-chart-cont {
  height: 100%;
  background: @yn-component-background;
  position: relative;
  padding: @yn-padding-xl @yn-padding-xl;
  border-top-left-radius: @yn-console-content-radius !important;
  border-top-right-radius: @yn-console-content-radius !important;
}
.ragio-group-cont {
  min-width: 290px;
}
.toolBar {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.toolBar_right {
  color: @yn-text-color-secondary;
  .chartBtns {
    font-size: @yn-font-size-lg;
    padding: @yn-padding-s;
    cursor: pointer;
    color: @yn-label-color;
  }
  .icon-setting {
    margin: 0 @rem8;
  }
  /deep/.ant-divider-vertical {
    margin: 0 @rem24;
  }
}
.slider {
  height: 32px;
  line-height: 32px;
  background: @yn-body-background;
  width: 216px;
  border: 1px solid @yn-border-color-base;
  border-radius: @yn-border-radius-base;
  padding: 0 0 0 @yn-padding-l;
  font-size: @yn-font-size-sm;
  color: @yn-label-color;
  display: inline-block;
  & .sliderCont {
    width: 135px;
    display: inline-block;
    height: 22px;
    position: relative;
    top: -5px;
    margin-right: @yn-margin-s;
  }
  & > span {
    display: inline-block;
    width: 30px;
    text-align: center;
    position: relative;
    top: -2px;
  }
  .sliderBtn {
    position: relative;
    top: -2px;
  }
  .count-text {
    color: @yn-text-color;
  }
}
.dropMenu {
  font-size: @yn-font-size-lg;
  color: @yn-text-color-secondary;
  cursor: pointer;
}
.icon-style {
  color: @yn-label-color;
}
.tb,
.lr {
  margin-right: 5px;
  color: @yn-label-color;
}
.tb {
  transform: rotate(90deg);
}
.toolbar-left {
  .iconfont {
    color: @yn-label-color;
  }
}
.chart-cont {
  height: calc(100% - @rem34);
}
</style>
