<template>
  <div style="width: 100; height: 100%">
    <SeeksRelationGraph
      ref="seeksRelationGraph"
      class="graphChart"
      :options="graphOptions"
      :onNodeExpand="onNodeExpand"
      :onNodeCollapse="onNodeCollapse"
      :onNodeClick="onNodeClick"
    />

    <!-- 持股信息列表弹窗 -->
    <HoldingInfoModal
      v-if="showInfoModal"
      :pageDimObj="pageDimObj"
      :clickInfo="clickInfo"
      :showHoldingInfo="showInfoModal"
      @closeHoldingInfo="closeInfoModal"
    />
  </div>
</template>

<script>
import HoldingInfoModal from "./holdingInfoModal.vue";
import SeeksRelationGraph from "@/components/ui/relation-graph/index.vue";
import cloneDeep from "lodash/cloneDeep";
import lang from "@/mixin/lang";
const { $t_equity, $t_structures } = lang;
// 图标共用配置
const defaultConfig = {
  backgrounImageNoRepeat: true,
  allowShowMiniToolBar: false,
  defaultFocusRootNode: false,
  layouts: [
    {
      label: $t_structures("core"),
      layoutName: "tree",
      layoutClassName: "seeks-layout-tree",
      defaultJunctionPoint: "border"
      // min_per_height: "160",
      // min_per_width:120
    }
  ],
  disableDragNode: true,
  allowShowMiniNameFilter: false,
  defaultLineShape: 4,
  defaultJunctionPoint: "tb",
  defaultNodeShape: 1,
  defaultNodeWidth: "200",
  defaultNodeHeight: "60",
  defaultNodeColor: "#0052cc1a",
  defaultNodeFontColor: "#1A253B",
  defaultNodeBorderWidth: 0,
  defaultLineTextRotate: 0, // @modify 扩展属性 线上文本 旋转角度
  defaultLineTextPosition: "center", // @modify 扩展属性 线上 文本居中
  defaultLineMarker: {
    markerWidth: 12,
    markerHeight: 12,
    refX: 4,
    refY: 6.5,
    data: "M2,2 L2,11 L10,6 L2,2"
  }
};
export default {
  name: "GraphChart",
  components: { SeeksRelationGraph, HoldingInfoModal },
  props: {
    graphLayout: {
      type: String,
      default: "tb"
    },
    graphConfig: {
      type: Object,
      default: () => {
        return {
          defaultLineShape: 4,
          defaultNodeWidth: "200",
          defaultNodeHeight: "60",
          defaultNodeColor: "#0052cc1a",
          defaultNodeFontColor: "#2A2A2A",
          defaultNodeFontWeight: "600",
          defaultNodeRadius: "4px",
          defaultLineFontColor: "#003F9E",
          defaultLineFontWeight: "600",
          defaultLineColor: "#BCC1CC",
          defaultLineFontOffset: { x: 20, y: 0 }
        };
      }
    },
    graphData: {
      type: Object,
      default: () => {}
    },
    zoomRange: {
      type: Object,
      default: () => {}
    },

    openOrCloseAllNode: {
      type: String,
      default: ""
    },
    pageDimObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      graphOptions: { ...defaultConfig, ...this.graphConfig },
      showInfoModal: false, // 是否展示持股信息
      clickInfo: {
        id: "",
        text: ""
      }
    };
  },
  watch: {
    graphLayout: {
      handler(newVal) {
        const config = cloneDeep({ ...defaultConfig, ...this.graphConfig });
        if (newVal === "tb") {
          config.layouts[0].min_per_height = 160;
          config.layouts[0].min_per_width = 300;
          config.layouts[0].max_per_width = 300;
          config.layouts[0].from = "top";
          config.defaultLineTextRotate = 0; // @modify 扩展属性 线上文本 旋转角度 只针对tb
          config.defaultExpandHolderPosition = "bottom";
          // config.defaultJunctionPoint = "tb";
        } else {
          const {
            label,
            layoutName,
            layoutClassName,
            defaultJunctionPoint
          } = config.layouts[0];
          config.layouts = [
            {
              label,
              layoutName,
              layoutClassName,
              defaultJunctionPoint,
              from: "left",
              defaultNodeShape: 0,
              defaultLineShape: 1,
              min_per_height: 100,
              min_per_width: 550,
              max_per_width: 950
            }
          ];
          config.defaultExpandHolderPosition = "right";
          config.defaultJunctionPoint = "lr";
        }
        config.handleMousewheel = this.handleMousewheel;
        this.graphOptions = config;
        this.$refs.seeksRelationGraph &&
          this.$refs.seeksRelationGraph.setOptions(config, () => {});
      },
      immediate: true
    },
    graphData: {
      handler(newVal) {
        if (
          newVal &&
          newVal.nodes.length > 0 &&
          this.$refs.seeksRelationGraph
        ) {
          const { nodes, rootId } = newVal;
          for (let i = 0, LEN = nodes.length; i < LEN; i++) {
            if (nodes[i].id === rootId) {
              nodes[i].color = "#003F9E";
              nodes[i].fontColor = "#fff";
              break;
            }
          }
          this.$refs.seeksRelationGraph.setJsonData(newVal, seeksRGGraph => {
            // 这些写上当图谱初始化完成后需要执行的代码
          });
        }
      }
    },
    openOrCloseAllNode: {
      handler(newVal) {
        if (newVal === "open") {
          this.$refs.seeksRelationGraph.expandAllNode();
        } else {
          this.$refs.seeksRelationGraph.collapseAllNode();
        }
      }
    }
  },
  created() {},
  mounted() {
    if (
      this.graphData &&
      this.graphData.nodes &&
      this.graphData.nodes.length > 0 &&
      this.$refs.seeksRelationGraph
    ) {
      this.$nextTick(() => {
        this.$refs.seeksRelationGraph.setJsonData(
          cloneDeep(this.graphData),
          seeksRGGraph => {
            // 这些写上当图谱初始化完成后需要执行的代码
          }
        );
      });
    }
  },
  methods: {
    exxportEvent() {
      // 手动缩放
      // 获取当前缩放的信息：this.$refs.seeksRelationGraph.graphSetting.canvasZoom
      // this.$refs.seeksRelationGraph.zoom(100)
      this.$refs.seeksRelationGraph.downloadAsImage("pdf", "");
    },
    handleMousewheel(zoomVal) {
      const { max, min } = this.zoomRange;
      if (zoomVal >= min && zoomVal <= max) {
        this.$emit("setZoomVal", zoomVal);
      } else {
        return false;
      }
      return true;
    },
    onNodeCollapse(node, e) {
      // 暂留口子，后期添加展开全部回调
    },
    onNodeExpand(node, e) {
      // 暂留口子，后期添加展开全部回调
    },
    onNodeClick(nodeObject, $event) {
      // TODO
      this.clickInfo.id = nodeObject.id;
      this.clickInfo.text = nodeObject.text;
      this.showInfoModal = true;
      this.$emit("nodeClick", nodeObject, $event);
    },
    closeInfoModal(isShow) {
      this.showInfoModal = isShow;
    }
  }
};
</script>
<style lang="less" scoped>
/deep/div.rel-map {
  background: @yn-component-background;
}
</style>
