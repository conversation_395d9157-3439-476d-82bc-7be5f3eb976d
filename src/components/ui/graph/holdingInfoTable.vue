<template>
  <div>
    <filter-list
      :searchObj="searchObj"
      @deleteFilteItemById="deleteFilteItemById"
    />
    <yn-table
      bordered
      class="holdingInfoTable"
      :columns="columns"
      :dataSource="tableData"
      @change="onChange"
    >
      <div
        slot="table.filterDropdown_shareHolder"
        slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters }"
        style="padding: 8px 0 8px 8px; width: 250px"
      >
        <filter-checkbox-group
          :checkboxList="shareCheckboxList"
          :checkedList="shareCheckedList"
          @changeChecked="handleChecked($event, 'share')"
          @handleSearch="
            () => handleSearch(selectedKeys, confirm, 'share', clearFilters)
          "
          @handleReset="() => handleReset(clearFilters, 'share')"
        />
      </div>
      <div
        slot="table.filterDropdown_ismCompany"
        slot-scope="{ setSelectedKeys, selectedKeys, confirm, clearFilters }"
        style="padding: 8px 0 8px 8px; width: 250px"
      >
        <filter-checkbox-group
          :checkboxList="companyCheckboxList"
          :checkedList="companyCheckedList"
          @changeChecked="handleChecked($event, 'company')"
          @handleSearch="
            () => handleSearch(selectedKeys, confirm, 'company', clearFilters)
          "
          @handleReset="() => handleReset(clearFilters, 'company')"
        />
      </div>
      <template slot="directShareHolding" slot-scope="text, record">
        <span class="fullNumberCell">
          {{ `${record.directShareHolding}%` }}
        </span>
      </template>
    </yn-table>
  </div>
</template>
<script>
import lang from "@/mixin/lang";
const { $t_structures } = lang;
const columns = [
  {
    title: $t_structures("investee"),
    dataIndex: "name",
    width: "40%",
    filtered: false,
    scopedSlots: {
      customRender: "shareHolder",
      filterIcon: "filterIcon",
      filterDropdown: "filterDropdown_shareHolder"
    }
  },
  {
    title: $t_structures("direct_shareholding_ratio"),
    dataIndex: "directShareHolding",
    width: "30%",
    scopedSlots: {
      customRender: "directShareHolding"
    },
    sorter: (a, b) => a.directShareHolding - b.directShareHolding
  },
  {
    title: $t_structures("parent_company_or_not"),
    dataIndex: "isHolding",
    width: "30%",
    filtered: false,
    scopedSlots: {
      customRender: "ismCompany",
      filterDropdown: "filterDropdown_ismCompany"
    }
  }
];
import "yn-p1/libs/components/yn-table/";
import FilterCheckboxGroup from "@/views/ownershipManagement/filterCheckboxGroup.vue";
import FilterList from "@/views/ownershipManagement/filterList.vue";
export default {
  name: "HoldingInfoTable",
  components: {
    FilterCheckboxGroup,
    FilterList
  },
  props: {
    dataSource: {
      type: Array,
      default() {
        return [];
      }
    },
    tableType: {
      type: String,
      default() {
        return "";
      }
    }
  },
  data() {
    return {
      columns: [...columns],
      searchObj: [],
      tableData: [],
      cacheTableData: [],
      shareCheckedList: [],
      shareCheckboxList: [],
      mappingShareCheckboxObj: {},
      companyCheckboxList: [
        { id: "Y", name: "Y" },
        { id: "N", name: "N" }
      ],
      companyCheckedList: []
    };
  },
  watch: {
    tableType: {
      handler() {
        this.setColumns();
      },
      immediate: true
    },
    dataSource: {
      handler(newVal) {
        if (newVal && newVal.length) {
          const mappingShareCheckboxObj = {};
          const checkboxList = newVal.map(item => {
            const { objectId: id, name } = item;
            const tempObj = { id, name };
            mappingShareCheckboxObj[id] = tempObj;
            return tempObj;
          });
          this.tableData = [...newVal];
          this.cacheTableData = [...newVal];
          this.mappingShareCheckboxObj = mappingShareCheckboxObj;
          this.shareCheckboxList = checkboxList;
          this.shareCheckedList = [];
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.columns = this.columns.map(item => {
      item.filtered === false;
      return { ...item };
    });
  },
  methods: {
    onChange() {},
    handleChecked(checkedList, type) {
      this[`${type}CheckedList`] = checkedList;
    },
    setSearchObj() {
      const res = [];
      const searchArr = this.shareCheckedList.map(item => {
        return this.mappingShareCheckboxObj[item];
      });
      if (searchArr.length) {
        res.push({
          title: this.getTitle(),
          type: "share",
          searchArr: searchArr
        });
        this.columns[0].filtered = true;
      } else {
        this.columns[0].filtered = false;
      }
      const searchArr1 = this.companyCheckedList.map(item => {
        return {
          id: item,
          name: item
        };
      });
      if (searchArr1.length) {
        res.push({
          title: this.$t_structures("parent_company_or_not"),
          type: "company",
          searchArr: searchArr1
        });
        this.columns[2].filtered = true;
      } else {
        this.columns[2].filtered = false;
      }
      this.columns = [...this.columns];
      this.searchObj = [...res];
      this.setFilteTableData();
    },
    setFilteTableData() {
      let tableData = [];
      const { companyCheckedList, shareCheckedList } = this;
      if (this.searchObj.length > 0) {
        tableData = this.dataSource.filter(item => {
          const { objectId, isHolding } = item;
          return (
            (companyCheckedList.length
              ? companyCheckedList.indexOf(isHolding) !== -1
              : true) &&
            (shareCheckedList.length
              ? shareCheckedList.indexOf(objectId) !== -1
              : true)
          );
        });
      } else {
        tableData = [...this.dataSource];
      }
      this.$set(this, "tableData", tableData);
    },
    handleSearch(selectedKeys, confirm, type, clearFilters) {
      confirm();
      this.setSearchObj();
    },
    handleReset(clearFilter, type) {
      clearFilter();
      this.$set(this, `${type}CheckedList`, []);
      this.setSearchObj();
    },
    setColumns() {
      const cellName = this.getTitle();
      const [columns0, columns1, columns2] = columns;
      const { dataIndex, width, filtered, scopedSlots } = columns0;
      this.columns = [
        {
          title: cellName,
          dataIndex,
          width,
          filtered,
          scopedSlots
        },
        columns1,
        columns2
      ];
    },
    getTitle() {
      return this.tableType === "shareTable"
        ? this.$t_structures("investee")
        : this.$t_structures("investor");
    },
    deleteFilteItemById(type, id) {
      if (type !== "all") {
        // 根据 type 删除 organizationFilterIds mergerMethodFilterIds 值
        const propName =
          type === "share" ? "shareCheckedList" : "companyCheckedList";
        const res = this[propName].filter(item => item !== id);
        this[propName] = res;
      } else {
        this.shareCheckedList.splice(0, this.shareCheckedList.length);
        this.companyCheckedList.splice(0, this.companyCheckedList.length);
        this.tableData = [...this.dataSource];
      }
      // 设置 表格过滤对象
      this.setSearchObj();
      this.filterTableData();
    },
    filterTableData() {
      const { shareCheckedList, companyCheckedList } = this;
      const tableData = this.dataSource.filter(item => {
        const { objectId, isHolding } = item;
        return (
          (shareCheckedList.length
            ? shareCheckedList.indexOf(objectId) !== -1
            : true) &&
          (companyCheckedList.length
            ? companyCheckedList.indexOf(isHolding) !== -1
            : true)
        );
      });
      this.tableData = [...tableData];
    }
  }
};
</script>
<style lang="less" scoped>
.fullNumberCell {
  width: 100%;
  text-align: right;
  display: inline-block;
}
</style>
