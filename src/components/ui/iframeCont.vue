<template>
  <div class="iframeCont">
    <yn-spin v-if="loading" size="large" class="spin-cont" />
    <iframe ref="iframe"></iframe>
  </div>
</template>
<script>
export default {
  name: "IframeCont",
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: true
    };
  },
  watch: {
    params: {
      handler(val) {
        this.$nextTick(() => {
          const iframe = this.$refs.iframe;
          iframe.src = val.uri;
          iframe.className = "hide";
          if (iframe.attachEvent) {
            iframe.attachEvent("onload", this.load);
          } else {
            iframe.onload = this.load;
          }
        });
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    load() {
      this.loading = false;
    }
  }
};
</script>
<style lang="less" scoped>
.iframeCont,
.spin-cont,
.iframeCont iframe {
  width: 100%;
  height: 100%;
}
.spin-cont {
  margin-top: 10%;
}
.iframeCont iframe {
  border: 0;
}
</style>
