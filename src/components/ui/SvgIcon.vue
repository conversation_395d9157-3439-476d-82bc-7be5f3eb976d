<template>
  <yn-tooltip v-if="isIconBtn" :title="title" :placement="placement">
    <yn-icon-button :size="size" @click="handleClick">
      <i :class="`iconfont ${iconClass}`">
        <svg class="icon svg-icon" aria-hidden="true">
          <use :xlink:href="`#${type}`" />
        </svg>
      </i>
    </yn-icon-button>
  </yn-tooltip>
  <yn-tooltip v-else :title="title" :placement="placement">
    <i :class="`iconfont ${iconClass}`" @click="handleClick">
      <svg class="icon svg-icon" aria-hidden="true">
        <use :xlink:href="`#${type}`" />
      </svg>
    </i>
  </yn-tooltip>
</template>
<script>
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-icon-button/";
export default {
  name: "SvgIcon",
  props: {
    type: {
      type: String,
      default: ""
    },
    iconClass: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    },
    placement: {
      type: String,
      default: "top"
    },
    isIconBtn: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: "default"
    }
  },
  methods: {
    handleClick() {
      this.$emit("onClick");
      this.$emit("click"); // 兼容click
    }
  }
};
</script>

<style lang="less" scoped>
.svg-icon {
  font-size: @rem16;
}
</style>
