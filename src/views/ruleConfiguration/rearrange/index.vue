<template>
  <div class="rule-rearrange">
    <yn-spin :spinning="spinning" size="large">
      <yn-page-list
        v-if="cloneTableData.length"
        ref="pageList"
        :pageTitle="pageTitle"
        :tableConfig="tableConfig"
        :toolsConfig="toolsConfig"
        @table_rowSelectChange="onSelectChange"
        @table_change="handleSearch"
      >
        <template v-slot:[`tools.title`]>
          <template v-if="isDelete">
            <span class="choose-item">
              已选 {{ selectedRowKeysAll.length }} 条
            </span>
            <yn-button
              class="delete-confirm"
              type="primary"
              :disabled="!selectedRowKeysAll.length"
              @click="deleteData"
            >
              删除
            </yn-button>
            <yn-button @click="handleCancelDel">
              取消
            </yn-button>
          </template>
          <template v-else>
            <yn-input-search
              v-model="searchWord"
              class="head-search"
              placeholder="请输入规则名称"
              @search="onSearch"
            />
          </template>
        </template>
        <template v-if="!isDelete" v-slot:[`tools.btns`]>
          <yn-button type="primary" @click="addRearrage">
            新增
          </yn-button>
          <yn-button class="btn-delete" @click="() => (isDelete = !isDelete)">
            删除
          </yn-button>
          <yn-divider class="head-divider" type="vertical" />
          <svg-icon
            title="刷新"
            type="icon-shuaxin"
            @click="getRearrangeList"
          />
        </template>
        <template v-slot:[`table.operation`]="record">
          <div class="table-operation">
            <a
              href="javascript:;"
              class="yn-a-link table-operate"
              @click.stop="editRule(record)"
            >
              编辑
            </a>
            <yn-popconfirm
              title="你要删除当前规则吗？"
              placement="bottomRight"
              okText="删除"
              cancelText="取消"
              @confirm="confirmDelete(record)"
            >
              <a
                class="a-delete yn-a-link table-operate"
                href="javascript:;"
                @click.stop
              >
                删除
              </a>
            </yn-popconfirm>
            <svg-icon
              class="cell-operation-btns"
              type="icon-c1_cr_form_enter"
              :isIconBtn="false"
            />
          </div>
        </template>
      </yn-page-list>
      <Empty v-else>
        <span slot="title">重分类</span>
        <span slot="desc">当前重分类规则为空，马上开始新增吧</span>
        <yn-button slot="btn" type="primary" @click="addRearrage">
          新增重分类
        </yn-button>
      </Empty>
    </yn-spin>
  </div>
</template>

<script>
import Empty from "../empty.vue";

import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-popover";
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-pagination/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-page-list";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import { mapActions, mapMutations, mapState } from "vuex";
import rearrangeService from "@/services/rearrange";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import deleteConfirModal from "../deleteConfirm";
import cloneDeep from "lodash/cloneDeep";
const TABNAME = "rearrangeRule";
const TAB_URI = "ruleConfig/rearrangeRule";
const COLUMNS = [
  {
    title: "规则名称",
    dataIndex: "ruleName",
    key: "ruleName"
  },
  {
    title: "判断逻辑",
    dataIndex: "calculationFactor",
    key: "calculationFactor"
  },
  {
    title: "操作",
    key: "operation",
    width: "8rem",
    scopedSlots: {
      customRender: "operation"
    }
  }
];
export default {
  components: { Empty },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isDelete: false,
      pageTitle: {
        title: "重分类"
      },
      toolsConfig: {
        options: [
          [
            {
              slotName: "btns"
            }
          ]
        ]
      },
      tableConfig: {
        pagination: {
          total: 0,
          current: 1,
          pageSize: 20,
          currentPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: total => `总计${total}条`
        },
        columns: COLUMNS,
        dataSource: [],
        customRow: this.handlerCustomRow,
        rowSelection: false
      },
      tableData: [],
      cloneTableData: [],
      total: 0,
      currentPage: 1,
      currentPageSize: 20,
      pageSizeOptions: ["10", "20", "50", "100"],
      selectedRowKeysAll: [],
      deleteRuleSetIds: [],
      tableSelectedRowKeyAll: [],
      searchWord: "",
      spinning: false
    };
  },
  computed: {
    ...mapState({
      activeKey: state => state.common.tabActiveId
    })
  },
  watch: {
    activeKey: {
      handler(newKey) {
        if (newKey === this.params.id) {
          this.getRearrangeList();
        }
      }
    },
    isDelete: {
      handler(newVal) {
        this.tableConfig.rowSelection = newVal;
        if (newVal) {
          this.$set(this.tableConfig, "columns", COLUMNS.slice(0, 2));
        } else {
          this.$set(this.tableConfig, "columns", COLUMNS);
        }
      }
    }
  },
  created() {
    this.getRearrangeList();
    this.getAccountData();
    this.activeTabCbMixin(this.getRearrangeList);
  },
  methods: {
    ...mapActions({
      getAccountData: "rearrange/getAccountData"
    }),
    ...mapMutations({
      updatePageStaus: "rearrange/updatePageStaus",
      addTab: "common/addTab"
    }),
    getRearrangeList(isFirstPage = false) {
      const {
        tableConfig: {
          pagination: { pageSize: currentPageSize, current: currentPage }
        }
      } = this;
      // currentPageSize * (currentPage - 1),
      const params = {
        ruleName: this.searchWord,
        offset: !isFirstPage ? currentPageSize * (currentPage - 1) : 0,
        limit: currentPageSize
      };
      this.spinning = true;
      rearrangeService("getReclassificationList", params)
        .then(res => {
          const { data, totalCount } = res.data.data;
          let tableData = [];
          if (data && data.length) {
            data &&
              data.forEach(item => {
                item.key = item.objectId;
              });
            tableData = [...data];
            this.cloneTableData = cloneDeep(tableData);
          }
          this.tableConfig.dataSource = tableData;
          this.tableConfig.pagination.total = totalCount;
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    onSearch(e) {
      this.searchWord = e;
      this.getRearrangeList(!!e);
    },
    async deleteRearrange(objectIds) {
      const res = await rearrangeService("deleteReclassification", objectIds);
      return res;
    },
    addRearrage() {
      const id = AppUtils.generateUniqueId();
      const newAddTabInfo = {
        id,
        router: TABNAME,
        title: "新增重分类规则",
        uri: TAB_URI,
        routerName: TABNAME,
        params: {
          ruleInfo: {
            key: id
          }
        },
        newTabParams: {
          status: "newAdd"
        }
      };
      this.newTabMixin(newAddTabInfo);
      this.updatePageStaus({
        key: id,
        status: "newAdd"
      });
    },
    handleSearch({ pagingInfo }) {
      const { current, pageSize } = pagingInfo;
      const { pagination } = this.tableConfig;
      this.$set(this.tableConfig, "pagination", {
        ...pagination,
        current,
        pageSize
      });
      this.getRearrangeList();
    },
    handleCancelDel() {
      this.isDelete = !this.isDelete;
      this.selectedRowKeysAll.splice(0);
      this.tableSelectedRowKeyAll.splice(0);
    },
    onSelectChange(selectedRow, selectedRowKeysAll) {
      const res = selectedRowKeysAll.filter(item => {
        return this.deleteRuleSetIds.indexOf(item) === -1;
      });
      this.$set(this, "tableSelectedRowKeyAll", selectedRowKeysAll);
      this.$set(this, "selectedRowKeysAll", res);
    },
    async deleteData() {
      if (this.selectedRowKeysAll.length === 0) {
        UiUtils.infoMessage("请选择需要删除的项");
        return;
      }
      deleteConfirModal({
        vm: this,
        okCb: () => {
          this.spinning = true;
          rearrangeService("deleteReclassification", this.selectedRowKeysAll)
            .then(res => {
              UiUtils.successMessage("删除成功");
              this.deleteRuleSetIds.push(...this.selectedRowKeysAll);
              // 清除 已选信息
              this.selectedRowKeysAll.splice(0, this.selectedRowKeysAll.length);
              this.getRearrangeList();
            })
            .finally(() => {
              this.spinning = false;
              this.isDelete = false;
            });
        }
      });
    },
    newTabMixin(tabInfo) {
      const {
        id,
        router,
        title,
        uri,
        routerName,
        params,
        newTabParams
      } = tabInfo;
      this.newtabMixin({
        id,
        router,
        title,
        uri,
        routerName,
        params,
        newTabParams
      });
    },
    editRule(record) {
      const { ruleName: title, key: id } = record;
      const tabList = this.$store.state.common.tabs;
      const openItem = tabList
        .filter(tab => !tab.menuId)
        .find(tab => tab.title === title);
      const tabId = openItem ? openItem.id : id;
      const params = {
        id
      };
      const tabInfo = {
        id: tabId,
        router: TABNAME,
        title,
        uri: TAB_URI,
        routerName: TABNAME,
        params,
        newTabParams: {
          status: "edit"
        }
      };
      this.newTabMixin(tabInfo);
      this.updatePageStaus({
        key: tabId,
        status: "edit"
      });
    },
    handlerCustomRow(record, index) {
      return {
        on: {
          click: () => {
            if (this.isDelete) return;
            const { ruleName: title, key: id } = record;
            const tabList = this.$store.state.common.tabs;
            const openItem = tabList
              .filter(tab => !tab.menuId)
              .find(tab => tab.title === title);
            const tabId = openItem ? openItem.id : id;
            const params = {
              id
            };
            const drillInfo = {
              id: tabId,
              router: TABNAME,
              title,
              uri: TAB_URI,
              routerName: TABNAME,
              params,
              newTabParams: {
                status: "detail"
              }
            };
            this.newTabMixin(drillInfo);
            this.updatePageStaus({
              key: tabId,
              status: "detail"
            });
          }
        }
      };
    },
    async confirmDelete(record) {
      const id = [record.key];
      this.spinning = true;
      rearrangeService("deleteReclassification", id)
        .then(res => {
          const ident = this.selectedRowKeysAll.indexOf(record.key);
          if (ident !== -1) {
            this.deleteRuleSetIds.push(record.key);
            this.selectedRowKeysAll.splice(ident, 1);
          }
          UiUtils.successMessage("删除成功");
          this.closetabMixin(id);
          this.getRearrangeList();
        })
        .finally(() => {
          this.spinning = false;
        });
    }
  }
};
</script>
<style scoped lang="less">
.rule-rearrange {
  width: 100%;
  height: 100%;
  .rearrange-container,
  .rearrange-content,
  /deep/.ant-spin-nested-loading,
  /deep/.ant-spin-container {
    height: 100%;
  }
  background: @yn-background-color;
  /deep/.ant-table-row-cell-break-word {
    padding: 1px @rem16;
  }
  .a-delete {
    margin-left: @yn-padding-l;
  }
  .icon-right {
    display: inline-block;
    transform: rotate(-90deg);
    width: @rem18;
    height: @rem18;
  }
  .btn-delete {
    margin: 0 @yn-margin-xl 0 @yn-margin-s;
  }
  .head-divider {
    height: @rem16;
    margin: 0 @yn-padding-s 0 0;
  }
  .delete-confirm {
    margin: 0 @yn-margin-s 0 @yn-margin-xl;
  }
  .cell-operation-btns {
    color: @yn-label-color;
  }
}
</style>
