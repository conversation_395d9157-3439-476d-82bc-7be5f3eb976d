<template>
  <div class="adaptation-range">
    <div class="range-title">
      适配范围
      <span class="title-desc">适配范围只能选择子项成员</span>
    </div>
    <div v-if="currentPageStatus === 'detail'" class="range-detail">
      <span>适配范围包含“版本”、“年”、“期间”、“组织”，</span>
      <yn-button class="click-see" type="text" @click="seeAllmember">
        点击查看
      </yn-button>
    </div>
    <yn-form
      v-else
      :colon="false"
      class="dim-form"
      :form="dimForm"
      v-bind="formLayout"
    >
      <yn-card-layout
        :breakpoint="breakpoint"
        :M="layout.M"
        :L="layout.L"
        :XL="layout.XL"
        :XXL="layout.XXL"
        :XXXL="layout.XXXL"
      >
        <yn-form-item
          v-for="(item, index) in dimList"
          :key="item.objectId"
          class="dim-form-item"
          :label="item.dimName"
        >
          <ShowDimListInput
            v-decorator="[
              item.dimCode,
              {
                rules: [{ required: true, message: `请选择${item.dimName}` }],
                initialValue: item.selectedItem
              }
            ]"
            :dimInfo="item"
            :filterSharedMember="item.dimCode === 'Entity'"
            :tipsWord="getTipsWord(item)"
            @change="v => getExparseMembers(v, index)"
          />
        </yn-form-item>
      </yn-card-layout>
    </yn-form>
    <yn-drawer
      wrapClassName="range-drawer"
      title="适配范围"
      :visible="rangeVisible"
      :width="440"
      @close="() => (rangeVisible = !rangeVisible)"
    >
      <div class="drawer-content" :style="{ maxHeight: `${contentHeight}px` }">
        <div
          v-for="(item, index) in dimList"
          :key="item.dimName"
          class="range-version"
        >
          <p class="version-title">{{ item.dimName }}</p>
          <p v-for="member in item.selectedItem" :key="member.objectId">
            {{ member.dimMemberName }}
          </p>
          <yn-divider
            v-show="index !== dimList.length - 1"
            class="logic-divider"
          />
        </div>
      </div>
    </yn-drawer>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-card-layout/";

import { mapState } from "vuex";
import _cloneDeep from "lodash/cloneDeep";

import ShowDimListInput from "../../../components/hoc/ShowDimListInput.vue";
import DIM_INFO from "@/constant/dimMapping";
import { TRANSFER_TIPS_WORD } from "@/constant/common.js";
import {
  CARD_LAYOUT_BREAK_POINT,
  CARD_LAYOUT_LAYOUT
} from "@/constant/common.js";
export default {
  components: { ShowDimListInput },
  props: {
    noRange: {
      type: Boolean,
      default: false
    },
    rangeInfo: {
      type: Object,
      default: () => ({})
    },
    currentPageStatus: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      breakpoint: CARD_LAYOUT_BREAK_POINT,
      layout: CARD_LAYOUT_LAYOUT,
      dimForm: this.$form.createForm(this, "dimForm"),
      formLayout: {
        labelCol: { span: 7 },
        wrapperCol: { span: 17 }
      },
      rangeVisible: false,
      contentHeight: 0,
      dimList: [
        {
          dimName: "版本",
          dimCode: "Version",
          objectId: DIM_INFO.Version,
          dimId: DIM_INFO.Version,
          selectedItem: []
        },
        {
          dimName: "年",
          dimCode: "Year",
          objectId: DIM_INFO.Year,
          dimId: DIM_INFO.Year,
          selectedItem: []
        },
        {
          dimName: "期间",
          dimCode: "Period",
          objectId: DIM_INFO.Period,
          dimId: DIM_INFO.Period,
          selectedItem: []
        },
        {
          dimName: "组织",
          dimCode: "Entity",
          objectId: DIM_INFO.Entity,
          dimId: DIM_INFO.Entity,
          selectedItem: []
        }
      ],
      rangeParams: "" // 适配范围参数
    };
  },
  computed: {
    ...mapState("rearrange", {
      pageStatus: state => state.pageStatus
    })
  },
  watch: {
    rangeVisible: {
      handler(v) {
        if (v) {
          this.getPageHeight();
        }
      }
    }
  },
  async created() {
    await this.initData();
    this.getRangeParams();
  },

  methods: {
    getTipsWord(dimInfo) {
      if (dimInfo.dimCode === "Entity") {
        return TRANSFER_TIPS_WORD.shareMember();
      }
      return "";
    },
    getRangeParams() {
      const rangeP = {};
      this.dimList.forEach(item => {
        const code =
          item.dimCode.slice(0, 1).toLowerCase() + item.dimCode.slice(1);
        const key = `${code}MemberCodeIndexs`;
        rangeP[key] = item.selectedItem
          .map(dimMember => dimMember.dbCodeIndex || dimMember.dimMemberCode)
          .join(",");
      });
      this.rangeParams = rangeP;
    },
    initData() {
      if (this.currentPageStatus === "newAdd") return;
      for (let i = 0; i < this.dimList.length; i++) {
        const item = this.dimList[i];
        const k = `${item.dimCode.toLowerCase()}Members`;
        item.selectedItem = this.rangeInfo[k];
      }
    },
    wrapPromise(p) {
      return new Promise(resolve => {
        p.then(res => {
          resolve({
            res,
            isOk: true
          });
        }).catch(err => {
          resolve({
            err,
            isOk: false
          });
        });
      });
    },
    formatRequestParams(exprObj = {}, flag = false) {
      const expr = JSON.parse(JSON.stringify(exprObj));
      const dimMemberExps = {};
      if (Object.keys(expr).length) {
        Object.keys(expr).map(item => {
          let propsName = item;
          // 入参表达式 成员 传 member
          if (item === "memberType") {
            propsName = "member";
          }
          // 返回参数 反显 需要 memberType
          if (item === "member") {
            propsName = "memberType";
          }
          dimMemberExps[propsName] = expr[item];
        });
      }
      if (flag) return dimMemberExps;
      return JSON.stringify(dimMemberExps);
    },
    getPageHeight() {
      const pageH = window.innerHeight - 45;
      this.contentHeight = pageH;
    },
    seeAllmember() {
      this.rangeVisible = true;
    },
    getFormValue(formName) {
      this[formName].validateFields((err, value) => {
        if (!err) {
        }
      });
    },
    getExparseMembers(info, i) {
      const { selectedItem } = info;
      const copyDimList = _cloneDeep(this.dimList);
      copyDimList[i].selectedItem = selectedItem;
      this.$set(this, "dimList", copyDimList);
      this.getRangeParams();
      this.$emit("addSaveCb");
    }
  }
};
</script>

<style lang="less">
.range-drawer {
  .ant-drawer-body {
    padding: 0;
  }
}
</style>
<style scoped lang="less">
.adaptation-range {
  width: 100%;
  .range-title {
    font-size: @rem14;
    color: @yn-heading-color;
    font-weight: 600;
    margin-bottom: @yn-margin-l;
    .title-desc {
      font-size: @rem12;
      color: @yn-label-color;
      font-weight: normal;
    }
  }
  .range-detail {
    margin-top: @yn-padding-xl;
    margin-bottom: @rem24;
    color: @yn-text-color-secondary;
    font-size: @rem14;
    .click-see {
      color: @yn-chart-1;
      margin-left: @yn-padding-s;
      cursor: pointer;
    }
  }
}
.drawer-content {
  padding: @yn-padding-xxxl;
  overflow-y: scroll;
}
.error-text {
  color: @yn-error-color;
}
.version-title {
  font-size: @rem14;
  color: @yn-label-color;
}
</style>
<style lang="less">
.adaptation-range {
  .yn-card-layout {
    overflow: hidden;
  }
  .dim-list-input {
    width: 100%;
  }
}
</style>
