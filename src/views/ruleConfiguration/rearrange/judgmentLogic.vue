<template>
  <div class="judgment-logic">
    <div class="logic-title">判断逻辑</div>
    <div v-if="isEmpty" class="logic-empty">
      <span class="empty-text">
        当前判断逻辑为空，请
        <span class="text-add" @click="addLogic"> 添加</span>
      </span>
      <span v-show="isEmpty && noJudge" class="err-text">请添加判断逻辑</span>
    </div>
    <div v-else class="logic-content">
      <span class="content-common">{{ viewSubject }}</span>
      <div
        v-for="(item, index) in viewConditionList"
        :key="index"
        class="content-condition"
      >
        <span class="content-common common-operation">{{
          item.operationSymbol
        }}</span>
        <span class="content-common">{{ item.accountName }}</span>
      </div>
      <span class="content-common common-operation">{{ viewOperator }}</span>
      <span class="content-common">{{ viewInputNumber }}</span>
      <!-- <span
        v-show="currentPageStatus !== 'detail'"
        class="logic-edit"
        @click="editLogic"
      >
        编辑
      </span> -->
      <yn-button
        v-show="currentPageStatus !== 'detail'"
        class="logic-edit"
        type="text"
        @click="editLogic"
      >
        编辑
      </yn-button>
    </div>
    <yn-drawer
      :width="440"
      title="判断逻辑"
      :visible="visible"
      :maskClosable="false"
      wrapClassName="wrap-drawer"
      @close="onCloseDrawer"
    >
      <div class="drawer-content">
        <!-- <SelectDimMember
          :allowClear="false"
          :dataObj.sync="dataObj"
          fieldName="subject"
          :dimVal="dataObj['subject']"
          :treeData="accountData"
          @changeDimVal="changeDimVal"
        /> -->
        <AsynSelectDimMember
          forceRender
          dimCode="Account"
          :value="viewSubjectId"
          searchMode="single"
          :allowClear="false"
          :nonleafselectable="true"
          :isSetDefaultVal="false"
          @changeVal="onChange($event, 'main')"
        />
        <span v-show="!subject && showTips" class="error-tips">请选择科目</span>
        <yn-row
          v-for="(item, index) in conditionList"
          :key="index"
          class="margin-top"
        >
          <yn-col :span="8">
            <yn-select
              v-model="item.operationSymbol"
              :allowClear="false"
              class="select-operator"
            >
              <yn-select-option value="+">加</yn-select-option>
              <yn-select-option value="-">减</yn-select-option>
            </yn-select>
            <span v-show="!item.operationSymbol && showTips" class="error-tips">
              请选择
            </span>
          </yn-col>
          <yn-col :span="14">
            <AsynSelectDimMember
              dimCode="Account"
              :value="item.accountId"
              searchMode="single"
              :allowClear="false"
              :nonleafselectable="true"
              :isSetDefaultVal="false"
              @changeVal="onChange($event, index)"
            />
            <span
              v-show="!item.accountId && dataObj[index + 'error']"
              class="error-tips"
            >
              请选择科目
            </span>
          </yn-col>
          <yn-col :span="2">
            <svg-icon
              title="删除"
              type="icon-shanchuicon"
              @onClick="deleteCondition(index)"
            />
          </yn-col>
        </yn-row>
        <div class="sub-btns">
          <yn-button
            type="text"
            :disabled="!isCanAddCondition"
            @click="addCondition"
          >
            添加条件
          </yn-button>
        </div>
        <yn-divider class="logic-divider" />
        <yn-row>
          <yn-col :span="8">
            <yn-select v-model="operator" class="select-operator">
              <yn-select-option value=">">大于</yn-select-option>
              <yn-select-option value="=">等于</yn-select-option>
              <yn-select-option value="<">小于</yn-select-option>
            </yn-select>
            <span v-show="!operator && showTips" class="error-tips">
              请选择
            </span>
          </yn-col>
          <yn-col :span="16">
            <yn-input
              v-model="inputNumber"
              class="input-number"
              placeholder="请输入常数"
              @blur="validateNum"
            />
            <span v-show="numberError" class="error-tips">
              请输入常数且小数位只能在0-9位之内
            </span>
          </yn-col>
        </yn-row>
      </div>
      <div class="drawer-btns">
        <yn-button class="btn" @click="onCloseDrawer">
          取消
        </yn-button>
        <yn-button class="btn" type="primary" @click="saveEvent">
          保存
        </yn-button>
      </div>
    </yn-drawer>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-row/";
import "yn-p1/libs/components/yn-col/";
import "yn-p1/libs/components/yn-select-option/";

import { mapState } from "vuex";
import cloneDeep from "lodash/cloneDeep";
import { getNameFromId } from "../../../utils/rearrange";
import AsynSelectDimMember from "../../../components/hoc/asynSelectDimMember";

export default {
  components: { AsynSelectDimMember },
  props: {
    noJudge: {
      type: Boolean,
      default: false
    },
    judgInfo: {
      type: Object,
      default: () => ({})
    },
    currentPageStatus: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      showTips: false,
      numberError: false,
      isEmpty: true,
      visible: false,
      subject: undefined,
      viewSubject: "",
      inputNumber: "",
      viewinputNumber: "",
      operator: undefined,
      viewOperator: "",
      conditionList: [], // 条件列表
      viewConditionList: [],
      judgeParams: "",
      viewSubjectId: "",
      dataObj: {
        subject: ""
      } // 下拉组件选择的值
    };
  },
  computed: {
    ...mapState("rearrange", {
      pageStatus: state => state.pageStatus,
      accountData: state => state.accountData
    }),
    isCanAddCondition() {
      if (this.conditionList.length === 0) {
        return this.subject;
      }
      const len = this.conditionList.length;
      const item = this.conditionList[len - 1];
      if (item && item.accountId) return true;
      return false;
    }
  },
  async created() {
    if (!this.selfTab) {
      await this.$store.dispatch("rearrange/getAccountData");
    }
    this.initData();
  },
  methods: {
    initData() {
      if (this.currentPageStatus === "newAdd") return;
      this.isEmpty = false;
      const {
        judgmentFactor,
        resultConstant,
        judgeLogicVO: { mainAccountName, itemVOList, mainAccountId }
      } = this.judgInfo;
      this.viewSubject = mainAccountName;
      this.subject = mainAccountId;
      this.viewSubjectId = mainAccountId;
      this.dataObj.subject = mainAccountId;
      this.viewOperator = judgmentFactor;
      this.viewInputNumber = resultConstant;
      this.viewConditionList = cloneDeep(itemVOList);
    },
    addLogic() {
      this.visible = true;
    },
    addCondition() {
      if (!this.isCanAddCondition) return;
      const len = this.conditionList.length;
      this.dataObj[len + ""] = "";
      this.conditionList.push({
        operationSymbol: "+",
        accountId: undefined
      });
    },
    deleteCondition(index) {
      this.conditionList.splice(index, 1);
      const copyDataObj = {};
      Object.keys(this.dataObj).forEach(key => {
        if (key === "subject" || key < index) {
          copyDataObj[key] = this.dataObj[key];
        }
        if (key > index) {
          copyDataObj[String(key - 1)] = this.dataObj[key];
        }
      });
      this.$set(this, "dataObj", copyDataObj);
    },
    editLogic() {
      Object.assign(this, {
        subject: this.subject,
        operator: this.viewOperator,
        inputNumber: this.viewInputNumber + ""
      });
      this.dataObj.subject = this.subject;
      this.conditionList = cloneDeep(this.viewConditionList);
      this.conditionList.forEach((item, index) => {
        this.dataObj[index] = item.accountId;
      });
      this.visible = true;
    },
    onCloseDrawer() {
      this.visible = false;
    },
    getJudgeParams() {
      const judgmentFactor = this.viewOperator;
      const constant = +this.viewInputNumber;
      const mainAccountId = this.subject;
      const mainAccountName = this.viewSubject;
      return {
        judgmentFactor,
        constant,
        judgeLogicVO: {
          mainAccountId,
          mainAccountName,
          mainAccountCodeIndex: getNameFromId(this.accountData, mainAccountId)
            .dbCodeIndex,
          itemVOList: this.viewConditionList
        }
      };
    },
    // 检验是否输入、输入有误
    checkData() {
      this.validateNum();
      if (
        this.conditionList.filter(
          item => !item.accountId || !item.operationSymbol
        ).length
      ) {
        return false;
      }
      if (!this.subject || !this.operator || this.numberError) {
        return false;
      }
      return true;
    },
    saveEvent() {
      // this.showTips = !!this.conditionList.filter(item => !item.accountId).length;
      this.showTips = true;
      Object.keys(this.dataObj).forEach(key => {
        const copyObj = cloneDeep(this.dataObj);
        if (!this.dataObj[key]) {
          copyObj[`${key}error`] = "error";
          this.$set(this, "dataObj", copyObj);
        }
      });
      const bool = this.checkData();
      if (!bool) return;
      Object.assign(this, {
        viewSubject: getNameFromId(this.accountData, this.subject).name,
        viewSubjectId: this.subject,
        viewOperator: this.operator,
        viewInputNumber: Number(this.inputNumber)
      });
      this.viewConditionList = this.conditionList.map(item => {
        const curItem = getNameFromId(this.accountData, item.accountId);
        return {
          ...item,
          accountName: curItem.name,
          accountCodeIndex: curItem.dbCodeIndex
        };
      });
      this.onCloseDrawer();
      this.isEmpty = false;
      this.$emit("addSaveCb");
    },
    validateNum() {
      const bool = isNaN(Number(this.inputNumber));
      if (!this.inputNumber.indexOf(".")) {
        this.numberError = true;
        return;
      }
      if (bool || !this.inputNumber) {
        this.numberError = true;
      } else {
        const numberVal = this.inputNumber + ""; // 转换成string类型，后端返回的树number类型
        const decimal = numberVal.split(".")[1];
        const decimalLen = decimal && decimal.length;
        this.numberError = decimalLen > 9;
      }
    },
    // changeDimVal(index) {
    //   delete this.dataObj[`${index}error`];
    //   const newObj = this.dataObj;
    //   this.$set(this, "dataObj", this.dataObj);
    //   Object.keys(newObj).forEach(item => {
    //     if (isNaN(Number(item))) {
    //       this.subject = newObj[item];
    //     } else {
    //       this.conditionList[item].accountId = newObj[item];
    //     }
    //   });
    // },
    onChange(e, type) {
      if (type === "main") {
        this.subject = e;
      } else {
        this.conditionList[type].accountId = e;
      }
    }
  }
};
</script>

<style lang="less">
.wrap-drawer {
  .ant-drawer-body {
    padding: 0;
  }
}
</style>
<style scoped lang="less">
.disabled-color {
  color: @yn-disabled-color;
}
.click-color {
  color: @yn-chart-1;
}
.margin-top {
  margin-top: @yn-padding-xxxl;
  /deep/.ant-col-14 {
    padding-left: @yn-padding-s;
  }
}
.judgment-logic {
  width: 100%;
  margin-top: 0;
  .logic-title {
    font-size: @rem14;
    color: @yn-heading-color;
    font-weight: 600;
  }
  .logic-empty {
    margin-top: @yn-padding-xl;
    width: 100%;
    height: @rem36;
    line-height: @rem36;
    text-align: center;
    background: @yn-background-color;
    position: relative;
    .empty-text {
      font-size: @rem14;
      color: @yn-label-color;
      .text-add {
        color: @yn-link-color;
        cursor: pointer;
      }
    }
  }
  .err-text {
    position: absolute;
    left: 0;
    top: @rem28;
    display: inline-block;
    color: @yn-error-color;
  }

  .logic-content {
    display: flex;
    flex-wrap: wrap;
    margin-top: @yn-padding-l;
    .content-common {
      display: inline-block;
      height: @rem32;
      line-height: @rem32;
      box-sizing: border-box;
      padding: 0 @yn-padding-xl;
      background: @yn-background-color;
      border-radius: @rem4;
      border: 1px solid rgba(225, 229, 235, 1);
      font-size: @rem14;
      color: @yn-text-color-secondary;
      margin-right: @yn-margin-s;
      margin-bottom: @yn-margin-s;
    }
    .content-condition {
      display: flex;
      align-items: center;
    }
    .common-operation {
      font-size: @rem16;
      padding: 0;
      width: @rem32;
      text-align: center;
      color: @yn-label-color;
    }
    .logic-edit {
      display: inline-block;
      height: @rem32;
      line-height: @rem32;
      font-size: @rem14;
      color: @yn-chart-1;
      cursor: pointer;
    }
  }
}
.drawer-content {
  width: 100%;
  padding: @rem22 @yn-padding-xxxl 0;
  .error-tips {
    padding-left: @rem12;
    display: inline-block;
    color: @yn-error-color;
  }
}
.drawer-btns {
  width: 100%;
  position: absolute;
  bottom: 0;
  height: 3rem;
  line-height: 3rem;
  text-align: right;
  padding-right: @yn-padding-xl;
  background: @yn-component-background;
  border-top: 1px solid @yn-border-color-base;
  .btn {
    &:first-child {
      margin-right: @yn-padding-xl;
    }
    width: 5rem;
  }
}
.select-sub {
  width: 100% !important;
}
.sub-item,
.input-number {
  padding-left: @yn-padding-s;
}
.sub-btns {
  font-size: @rem14;
  margin-top: @yn-margin-xxxl;
  .btn-add {
    margin-right: @yn-margin-xl;
    cursor: pointer;
  }
  .btn-delete {
    cursor: pointer;
  }
}
.logic-divider {
  margin: @yn-margin-xl 0;
}
.select-operator {
  width: 100%;
}
</style>
