<template>
  <div class="subject-setting">
    <div class="setting-title">
      科目设置
    </div>
    <!-- 展示表头的搜索条件 -->
    <div v-if="isShowSearch" class="search-conditions">
      <template v-for="(condition, listIndex) in searchConditionList">
        <yn-tag
          v-for="(filterItem, itemIndex) in condition.searchList"
          :key="filterItem"
          closable
          @close="clearSearchItem(itemIndex, listIndex)"
        >
          {{ `${$data.$subjectName[condition.tag]}：${filterItem}` }}
        </yn-tag>
      </template>
      <yn-button type="text" class="condition-none" @click="allClear">
        全部清除
      </yn-button>
    </div>
    <yn-table
      bordered
      class="subject-table"
      :columns="columns"
      :data-source="tableData"
      :scroll="{ x: 800 }"
    >
      <span slot="table.outSubjectTitle" class="has-star">分出科目</span>
      <span slot="table.inSubjectTitle" class="has-star">分入科目</span>
      <div
        slot="table.outSubject"
        slot-scope="text, record, index"
        class="td-out valid-prop"
      >
        <span v-if="currentPageStatus === 'detail'" class="td-text">
          {{
            getPaveAccountData &&
              getPaveAccountData[text] &&
              getPaveAccountData[text].name
          }}
        </span>
        <!-- <yn-select-tree
          v-else
          v-model="record.reclassificationOutId"
          :class="[
            'select-table',
            !record.reclassificationOutId && record.errorTips
              ? 'valid-error-border'
              : ''
          ]"
          showSearch
          searchMode="single"
          :allowClear="false"
          :nonleafselectable="true"
          placeholder="请选择分出科目"
          :datasource="accountData"
          @change="onChange($event, index, 'out')"
        /> -->
        <AsynSelectDimMember
          v-else
          :class="[
            'select-table',
            !record.reclassificationOutId && record.errorTips
              ? 'valid-error-border'
              : ''
          ]"
          forceRender
          :value="record.reclassificationOutId"
          dimCode="Account"
          searchMode="single"
          placeholder="请选择分出科目"
          :allowClear="false"
          :needFilterShared="true"
          :nonleafselectable="true"
          :isSetDefaultVal="false"
          :runChangeVal="false"
          @changeVal="(e, info, bool) => onChange(e, index, 'out', bool)"
        />
        <span
          v-show="!record.reclassificationOutId && record.errorTips"
          class="invalid-tip-text"
        >
          请选择分出科目
        </span>
      </div>
      <div
        slot="table.inSubject"
        slot-scope="text, record, index"
        class="td-in valid-prop"
      >
        <span v-if="currentPageStatus === 'detail'" class="td-text">
          {{
            getPaveAccountData &&
              getPaveAccountData[text] &&
              getPaveAccountData[text].name
          }}
        </span>
        <!-- <yn-select-tree
          v-else
          v-model="record.reclassificationInId"
          :class="[
            'select-table',
            !record.reclassificationInId && record.errorTips
              ? 'valid-error-border'
              : ''
          ]"
          showSearch
          searchMode="single"
          :allowClear="false"
          placeholder="请选择分入科目"
          :datasource="searching ? searchResult : accountData"
          @change="onChange($event, index, 'in')"
        /> -->
        <AsynSelectDimMember
          v-else
          :class="[
            'select-table',
            !record.reclassificationInId && record.errorTips
              ? 'valid-error-border'
              : ''
          ]"
          forceRender
          :value="record.reclassificationInId"
          dimCode="Account"
          searchMode="single"
          placeholder="请选择分入科目"
          :allowClear="false"
          :needFilterShared="true"
          :nonleafselectable="false"
          :isSetDefaultVal="false"
          :runChangeVal="false"
          @changeVal="(e, info, bool) => onChange(e, index, 'in', bool)"
        />
        <span
          v-show="!record.reclassificationInId && record.errorTips"
          class="invalid-tip-text"
        >
          请选择分入科目
        </span>
      </div>
      <!-- 搜索 -->
      <div
        :slot="
          currentPageStatus === 'detail' ? 'table.filterDropdownOutSubject' : ''
        "
        slot-scope="{ confirm, clearFilters }"
      >
        <table-search
          ref="outSubject"
          panelWidth="500px"
          :dataSource="outSubjectData"
          :clearFilters="clearFilters"
          @handSearch="
            chooseList => handSearch(confirm, chooseList, 'outSubject')
          "
        />
      </div>
      <template
        :slot="
          currentPageStatus === 'detail' ? 'table.filterDropdownInsubject' : ''
        "
        slot-scope="{ confirm, clearFilters }"
      >
        <table-search
          ref="inSubject"
          panelWidth="500px"
          :dataSource="inSubjectData"
          :clearFilters="clearFilters"
          @handSearch="
            chooseList => handSearch(confirm, chooseList, 'inSubject')
          "
        />
      </template>
      <template slot="table.operation" slot-scope="text, record, index">
        <a
          :disabled="tableData.length === 1"
          class="delete-text"
          href="javascript:;"
          @click="deleteRecord(index)"
        >
          删除
        </a>
      </template>
    </yn-table>
    <span v-show="tableData.length === 0 && noTableData" class="no-data">
      请添加科目
    </span>
    <div
      v-show="currentPageStatus !== 'detail'"
      class="footer-btn"
      @click="handleAdd"
    >
      + 添加
    </div>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-tag/";
import "yn-p1/libs/components/yn-button/";

import TableSearch from "@/components/hoc/tableFilterSearch";
import { mapState } from "vuex";
import { getNameFromId, uniqueItem } from "../../../utils/rearrange";
import cloneDeep from "lodash/cloneDeep";
import AsynSelectDimMember from "../../../components/hoc/asynSelectDimMember";
export default {
  components: { TableSearch, AsynSelectDimMember },
  props: {
    itemList: {
      type: Array,
      default: () => []
    },
    currentPageStatus: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      columns: [
        {
          title: "序号",
          dataIndex: "sortId",
          key: "sortId",
          width: 80,
          customRender: (text, row, index) => {
            const sortIndex = "sort-index";
            return <span class={sortIndex}>{index + 1}</span>;
          }
        },
        {
          dataIndex: "reclassificationOutId",
          key: "reclassificationOutId",
          filtered: false,
          scopedSlots: {
            customRender: "outSubject",
            title: "outSubjectTitle",
            filterIcon: "filterIcon",
            filterDropdown: "filterDropdownOutSubject"
          }
        },
        {
          dataIndex: "reclassificationInId",
          key: "reclassificationInId",
          filtered: false,
          scopedSlots: {
            customRender: "inSubject",
            title: "inSubjectTitle",
            filterIcon: "filterIcon",
            filterDropdown: "filterDropdownInsubject"
          }
        }
      ],
      tableData: [
        {
          key: 0,
          sortId: 0,
          reclassificationInId: "",
          reclassificationOutId: ""
        }
      ],
      cloneTableData: [
        {
          key: 0,
          sortId: 0,
          reclassificationInId: "",
          reclassificationOutId: ""
        }
      ],
      noTableData: false,
      $subjectName: {
        outSubject: "分出科目",
        inSubject: "分入科目"
      },
      searchConditionList: [
        {
          tag: "outSubject",
          searchList: []
        },
        {
          tag: "inSubject",
          searchList: []
        }
      ],
      isShowSearch: false,
      subjectParams: "",
      getPaveAccountData: "",
      searching: false,
      searchResult: []
    };
  },
  computed: {
    ...mapState("rearrange", {
      pageStatus: state => state.pageStatus,
      accountData: state => state.accountData,
      paveAccountData: state => state.paveAccountData
    }),
    outSubjectData({ cloneTableData }) {
      return cloneTableData.map(item => item.reclassificationOutName);
    },
    inSubjectData({ cloneTableData }) {
      return uniqueItem(cloneTableData, "reclassificationInName").map(
        item => item.reclassificationInName
      );
    }
  },
  watch: {
    searchConditionList: {
      deep: true,
      immediate: true,
      handler(nv, ov) {
        this.isShowSearch = nv
          .map(item => item.searchList.length)
          .reduce((pre, next) => pre + next, 0);
        // TODO 调查询接口
        if (!this.isShowSearch) {
          this.tableData = this.cloneTableData;
        } else {
          this.tableData = this.cloneTableData.filter(item => {
            if (nv[0].searchList.length === 0 && nv[1].searchList.length) {
              return nv[1].searchList.includes(item.reclassificationInName);
            }
            if (nv[0].searchList.length && nv[1].searchList.length === 0) {
              return nv[0].searchList.includes(item.reclassificationOutName);
            }
            if (nv[0].searchList.length && nv[1].searchList.length) {
              return (
                nv[0].searchList.includes(item.reclassificationOutName) &&
                nv[1].searchList.includes(item.reclassificationInName)
              );
            }
            return true;
          });
        }
      }
    },
    paveAccountData: {
      immediate: true,
      handler(nv) {
        this.getPaveAccountData = nv;
      }
    },
    currentPageStatus: {
      immediate: true,
      handler(nv) {
        if (nv !== "detail") {
          if (this.isShowSearch) {
            // 编辑态下清除查询条件。
            this.allClear();
          }
          this.columns.push({
            title: "操作",
            key: "operation",
            width: 80,
            scopedSlots: {
              customRender: "operation"
            }
          });
        } else {
          this.columns[this.columns.length - 1].key === "operation" &&
            this.columns.splice(this.columns.length - 1, 1);
        }
      }
    },
    itemList: {
      handler() {
        this.initData();
      }
    }
  },
  async created() {
    if (!this.selfTab) {
      await this.$store.dispatch("rearrange/getAccountData");
    }
    await this.initData();
    this.getSubjectParams();
  },
  methods: {
    initData() {
      if (this.currentPageStatus === "newAdd") return;
      this.tableData = this.itemList.map(item => ({
        ...item,
        key: item.objectItemId
      }));
      this.cloneTableData = cloneDeep(this.tableData);
    },
    // 获取科目维度成员
    addErrorToData() {
      const len = this.tableData.length;
      len === 0 && (this.noTableData = true);
      const lastItem = this.tableData[len - 1];
      if (
        lastItem &&
        (!lastItem.reclassificationInId || !lastItem.reclassificationOutId)
      ) {
        this.$set(lastItem, "errorTips", "error-tips");
        return true;
      }
      return false;
    },
    getSubjectParams() {
      return this.tableData.map(item => ({
        objectItemId: item.objectItemId,
        rowId: item.rowId,
        rowName: item.rowName,
        reclassificationInCodeIndex: item.reclassificationInCodeIndex,
        reclassificationInId: item.reclassificationInId,
        reclassificationOutCodeIndex: item.reclassificationOutCodeIndex,
        reclassificationOutId: item.reclassificationOutId
      }));
    },
    onChange(e, index, type, bool = true) {
      // 首次不进行操作。
      if (!bool) return;
      const curItem = getNameFromId(this.accountData, e);
      const field = `reclassification${type
        .slice(0, 1)
        .toUpperCase()}${type.slice(1)}`;
      this.tableData[index][`${field}Id`] = curItem.id;
      this.tableData[index][`${field}CodeIndex`] = curItem.dbCodeIndex;
      this.$emit("addSaveCb");
    },
    handleAdd() {
      const len = this.tableData.length;
      if (this.addErrorToData()) return;
      this.tableData.push({
        key: len + 1,
        sortId: len + 1,
        reclassificationInId: "",
        reclassificationOutId: ""
      });
    },
    deleteRecord(i) {
      this.tableData.splice(i, 1);
      this.$emit("addSaveCb");
    },
    handSearch(confirm, chooseList, type) {
      confirm({ closeDropdown: true });
      const index = type === "outSubject" ? 0 : 1;
      this.searchConditionList[index].searchList = [...chooseList];
      const bool = !!(
        this.searchConditionList[index].searchList &&
        this.searchConditionList[index].searchList.length
      );
      this.columns[index + 1].filtered = bool;
    },
    clearSearchItem(itemIndex, listIndex) {
      this.searchConditionList[listIndex].searchList.splice(itemIndex, 1);
      this.$refs[this.searchConditionList[listIndex].tag] &&
        this.$refs[this.searchConditionList[listIndex].tag].setSelectedItems(
          this.searchConditionList[listIndex].searchList
        );
      if (this.searchConditionList[listIndex].searchList.length === 0) {
        this.columns[listIndex + 1].filtered = false;
      }
    },
    allClear() {
      this.searchConditionList.forEach((item, index) => {
        item.searchList = [];
        this.columns[index + 1].filtered = false;
        this.$refs[item.tag] && this.$refs[item.tag].clearSelectedItems();
      });
    },
    onCustomeSearch(searchObj) {
      const { searchValue } = searchObj;
      if (!searchValue) {
        this.searchResult = cloneDeep(this.accountData);
        return;
      }
      this.searching = true;
      const res = [];
      const loop = arr => {
        arr.map(item => {
          if (item.name.includes(searchValue)) {
            const tarItem = JSON.parse(JSON.stringify(item));
            tarItem.children = [];
            res.push(item);
          }
          if (item.children && item.children.length) {
            loop(item.children);
          }
        });
      };
      loop(this.accountData);
      this.searchResult = [...res];
    },
    dropdownVisibleChange(bool) {
      if (!bool) {
        this.searching = "";
        this.searchResult = [];
      }
    }
  }
};
</script>

<style scoped lang="less">
@import "../../../commonLess/common.less";

.error-tips {
  border: 1px solid @yn-error-color;
}
.subject-setting {
  width: 100%;
  margin-top: @yn-margin-xxxl;
  .setting-title {
    font-size: @rem14;
    color: @yn-heading-color;
    font-weight: 600;
    margin-bottom: @yn-margin-l;
  }
  .search-conditions {
    margin-bottom: 0.5rem;
    /deep/.ant-tag {
      background-color: rgb(235, 238, 244);
      color: @yn-text-color-secondary;
    }
  }
  .subject-table {
    /deep/.ant-table-tbody > tr > td {
      padding: 0;
    }
    .td-out,
    .td-in {
      position: relative;
      width: 100%;
      .td-text {
        display: inline-block;
        height: @rem32;
        width: 100%;
        padding-left: @yn-padding-xl;
        line-height: @rem32;
      }
      /deep/.yn-select-tree {
        border: none;
      }
      .tip-text {
        position: absolute;
        display: inline-block;
        top: @rem36;
        left: 0;
        width: 50%;
        height: @rem24;
        line-height: @rem24;
        background: @yn-error-bg-color;
        color: @yn-error-color;
        padding-left: @yn-padding-xs;
      }
    }
    .select-table {
      position: relative;
      width: 100%;
      /deep/.ant-select-selection {
        border: none;
      }
    }
    .delete-text {
      margin-left: @yn-margin-xl;
    }
    /deep/ .ant-table-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .condition-none {
      cursor: pointer;
      font-size: @rem12;
      color: @yn-chart-1;
      line-height: @rem24;
    }
  }
  .no-data {
    color: @yn-error-color;
  }
  .footer-btn {
    width: 100%;
    height: @rem36;
    background: @yn-component-background;
    border: 1px dashed rgba(225, 229, 235, 1);
    margin-top: @yn-margin-l;
    line-height: @rem36;
    text-align: center;
    cursor: pointer;
  }
  .has-star::after {
    display: inline-block;
    margin-left: @yn-margin-xs;
    color: @yn-error-color;
    font-size: @rem14;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
  }
  .sort-index {
    display: inline-block;
    height: @rem32;
    line-height: @rem32;
    width: 100%;
    margin-left: @yn-margin-xl;
  }
}
</style>
