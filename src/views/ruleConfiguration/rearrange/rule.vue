<template>
  <div v-if="isShow" class="rearrange-detail">
    <!-- 新增重分类规则标题 -->
    <yn-page-title
      v-if="currentPageStatus === 'newAdd'"
      title="新增重分类规则"
    />
    <!-- 编辑或者查看详情的时候 -->
    <yn-page-title v-else :title="ruleName" class="edit-title">
      <template v-slot:extraRender>
        <div v-if="editName" class="edit-name">
          <!-- 编辑名称 -->
          <yn-input
            id="editNameInput"
            v-model="ruleName"
            class="head-input"
            :allowClear="false"
            @blur="closeInput"
          />
        </div>
        <!-- 显示名称 -->
        <svg-icon
          v-show="currentPageStatus === 'edit'"
          type="icon-edit"
          class="edit-pan"
          title="编辑"
          @onClick="handleEditName"
        />
        <yn-button
          v-show="currentPageStatus === 'detail'"
          type="primary"
          class="edti-btn"
          @click="handleEdit"
        >
          编辑
        </yn-button>
      </template>
    </yn-page-title>

    <div
      :class="[
        'detail-container',
        'cs-body-headerComponent',
        currentPageStatus === 'detail'
          ? 'detail-container-info'
          : 'detail-container-edit'
      ]"
    >
      <!-- 新增时填写名称 -->
      <div v-show="currentPageStatus === 'newAdd'" class="container-base-info">
        <div class="head-info-title">基础信息</div>
        <yn-row>
          <yn-col :span="8">
            <yn-col :span="8" class="info-title">
              <span>规则名称</span>
            </yn-col>
            <yn-col :span="16">
              <yn-input
                v-model="ruleName"
                :class="[!ruleName && showTips ? 'error-tips' : '']"
                placeholder="请输入"
                @change="inputChange('add')"
                @blur="closeInput('add')"
              />
              <span v-show="isLongName && ruleName" class="err-tip">
                字符长度超过64位限制，请检查
              </span>
              <span v-show="isRepeatName && ruleName" class="err-tip">
                规则名称已经存在
              </span>
              <span v-show="!ruleName && showTips" class="err-tip">请输入</span>
            </yn-col>
          </yn-col>
        </yn-row>
      </div>
      <!-- 适配范围 -->
      <AdaptationRange
        ref="adaptationRangeRef"
        :currentPageStatus="currentPageStatus"
        :rangeInfo="rangeInfo"
        @addSaveCb="addSaveCb"
      />
      <!-- 判断逻辑 -->
      <JudgmentLogic
        ref="JudgmentLogicRef"
        :currentPageStatus="currentPageStatus"
        :noJudge="noJudge"
        :judgInfo="judgInfo"
        @addSaveCb="addSaveCb"
      />
      <!-- 科目设置 -->
      <SubjectSetting
        ref="subjectSettingRef"
        :currentPageStatus="currentPageStatus"
        :itemList="itemList"
        @addSaveCb="addSaveCb"
      />
    </div>

    <div
      v-show="currentPageStatus === 'newAdd' || currentPageStatus === 'edit'"
      class="detail-footer"
    >
      <yn-button class="footer-btn" @click="handleUpdate">取消</yn-button>
      <yn-button
        :loading="btnLoading"
        class="footer-btn"
        type="primary"
        @mousedown="handleSave"
      >
        保存
      </yn-button>
    </div>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-row/";
import "yn-p1/libs/components/yn-col/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-page-title/";
import AdaptationRange from "./adaptationRange.vue";
import JudgmentLogic from "./judgmentLogic.vue";
import SubjectSetting from "./subjectSetting.vue";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import { mapState, mapMutations } from "vuex";
import rearrangeService from "@/services/rearrange";
import cloneDeep from "lodash/cloneDeep";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
export default {
  components: { AdaptationRange, JudgmentLogic, SubjectSetting },
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      ruleName: "",
      cloneRuleName: "",
      judgInfo: {},
      rangeInfo: {},
      itemList: [],
      showTips: false,
      editName: false,
      noJudge: false,
      btnLoading: false,
      currentPage: false,
      afterAddInfo: "",
      currentPageStatus: "",
      isLongName: false,
      isRepeatName: false,
      directSave: false,
      isShow: false
    };
  },
  computed: {
    ...mapState("rearrange", {
      pageStatus: state => state.pageStatus
    })
  },
  watch: {
    pageStatus: {
      deep: true,
      immediate: true,
      handler(nv) {
        let tabId;
        if (this.params) {
          tabId = this.params.id;
        } else {
          tabId = UrlUtils.getQuery("inTab");
        }
        this.currentPageStatus = nv[tabId];
      }
    }
  },
  created() {
    this.initPageStatus();
    this.initData();
  },
  methods: {
    ...mapMutations({
      updatePageStaus: "rearrange/updatePageStaus",
      updateTabTitle: "common/updateTabTitle"
    }),
    initPageStatus() {
      // 新版本的导航 不需要走这里面在created 里面处理
      if (!this.selfTab) {
        const { status } = this.getTabParamsMixin();
        this.currentPageStatus = status;
        return;
      } else {
        const { status } = this.params ? this.params.params : {};
        if (status) {
          this.currentPageStatus = status;
        }
      }
    },
    handleTransferEhcoData(list) {
      return list.map(item => ({
        dimMemberId: item.objectId,
        dimMemberName: item.dimMemberName,
        dimCode: "",
        key: `${item.objectId}-self`,
        label: item.dimMemberName,
        memberType: 1,
        memberTypeValue: "self",
        objectId: `${item.objectId}-self`,
        shardim: "",
        title: `成员(${item.dimMemberName})`,
        type: "member",
        value: item.objectId,
        dimMemberCode: item.dbCodeIndex || item.dimMemberCode
      }));
    },
    async initData(data) {
      if (this.currentPageStatus === "newAdd") {
        this.isShow = true;
        return;
      }
      const { id, form } = this.selfTab
        ? this.params.params
        : this.getTabParamsMixin() || {};
      let ruleInfo = data;
      if (!data) {
        ruleInfo = await this.getRuleInfoById(id);
      }
      // 如果ruleInfo为空，则没有查询到数据
      if (!ruleInfo) {
        UiUtils.errorMessage("重分类详情不存在");
      }
      this.ruleInfo = ruleInfo;
      // 如果 从当前系统其他（成员关联 ） 地方跳转过来，需要重新请求数据
      if (form) {
        await this.$store.dispatch("rearrange/getAccountData");
      }
      this.isShow = true;
      const {
        ruleName,
        judgmentFactor,
        resultConstant,
        judgeLogicVO,
        versionMembers,
        yearMembers,
        periodMembers,
        entityMembers,
        itemList
      } = ruleInfo;
      this.updateTabTitle({
        id: id,
        title: ruleName
      });
      this.ruleName = ruleName;
      this.cloneRuleName = cloneDeep(ruleName);
      this.judgInfo = {
        judgmentFactor,
        resultConstant,
        judgeLogicVO
      };
      this.rangeInfo = {
        versionMembers: this.handleTransferEhcoData(versionMembers),
        yearMembers: this.handleTransferEhcoData(yearMembers),
        periodMembers: this.handleTransferEhcoData(periodMembers),
        entityMembers: this.handleTransferEhcoData(entityMembers)
      };
      this.itemList = itemList;
    },
    async getRuleInfoById(id) {
      const {
        data: {
          data: { data: ruleInfo }
        }
      } = await rearrangeService("getReclassificationList", {
        objectId: id
      });
      return ruleInfo[0];
    },
    handleEditName() {
      this.editName = true;
      this.$nextTick(() => {
        const editInput = document.getElementById("editNameInput");
        editInput.focus();
      });
    },
    closeInput(type) {
      this.editName = false;
      if (this.ruleName.length > 64) {
        if (type === "add") {
          this.isLongName = true;
          this.isRepeatName = false;
        } else {
          this.ruleName = this.cloneRuleName;
          UiUtils.errorMessage("字符长度超过64位限制，请检查");
        }
        return;
      } else {
        this.isLongName = false;
      }
      if (this.ruleName === this.cloneRuleName) return;
      this.addSaveEventCb(this.closeNewAddTab);
      if (this.directSave) return;
      rearrangeService("updateReclassName", this.ruleName).then(res => {
        if (!res.data.data) {
          if (type === "add") {
            this.isRepeatName = true;
          } else {
            UiUtils.errorMessage("规则名称已存在");
            this.ruleName = this.cloneRuleName;
          }
        } else {
          this.isRepeatName = false;
        }
      });
    },
    handleEdit() {
      this.currentPage = true;
      let tabId = this.params && this.params.id;
      if (!this.selfTab) {
        tabId = UrlUtils.getQuery("inTab");
      }
      this.updatePageStaus({
        key: tabId,
        status: "edit"
      });
    },
    handleUpdate() {
      let tabId = this.params && this.params.id;
      if (!this.selfTab) {
        tabId = UrlUtils.getQuery("inTab");
      }
      if (this.currentPage) {
        this.savePromptMixin().then(res => {
          this.updatePageStaus({
            key: tabId,
            status: "detail"
          });
          if (res && res.status === "notSave") {
            this.ruleName = this.cloneRuleName;
          }
          this.$refs.adaptationRangeRef.initData();
          this.$refs.JudgmentLogicRef.initData();
          this.$refs.subjectSettingRef.initData();
        });
      } else {
        this.savePromptMixin().then(() => {
          // 如果需要停留在当前页面 则调用 cacel
          // PlatformCommunication.cancelSaveTab(tabId);
          this.closetabMixin(tabId);
        });
      }
    },
    // 科目校验，分出科目与分入科目不允许相同、分出科目不允许重复
    validateSubject(list) {
      const indexList = [];
      const outErrorMap = {};
      list.forEach((item, index) => {
        if (!outErrorMap[item.reclassificationOutId]) {
          outErrorMap[item.reclassificationOutId] = [index + 1];
        } else {
          outErrorMap[item.reclassificationOutId].push(index + 1);
        }
        if (
          item.reclassificationInId &&
          item.reclassificationOutId &&
          item.reclassificationInId === item.reclassificationOutId
        ) {
          indexList.push(index + 1);
        }
      });
      const outError = Object.values(outErrorMap).some(item => item.length > 1);
      if (indexList.length || outError) {
        const indexTips = indexList.length
          ? `第${indexList.join("、")}行分出科目与分入科目重复`
          : "";
        let tip = "";
        const keys = Object.keys(outErrorMap);
        keys.forEach((err, index) => {
          if (outErrorMap[err].length > 1) {
            tip += `${outErrorMap[err].join("、")}${
              index !== keys.length - 1 ? "和" : ""
            }`;
          }
        });
        const outErrorTips = outError ? `第${tip}行分出科目重复` : "";
        indexTips && UiUtils.errorMessage(indexTips);
        outErrorTips && UiUtils.errorMessage(outErrorTips);
        return false;
      }
      return true;
    },
    async handleSave() {
      if (document.activeElement.tagName === "INPUT") {
        // 没失去焦点直接点保存
        this.directSave = true;
      }
      const bool = this.$refs.subjectSettingRef.addErrorToData();
      this.showTips = true;
      this.noJudge = true;
      const range = await this.$refs.adaptationRangeRef.dimForm.validateFields();
      const rangeParams = this.$refs.adaptationRangeRef.rangeParams;
      const judgeParams = this.$refs.JudgmentLogicRef.getJudgeParams();
      const itemList = this.$refs.subjectSettingRef.getSubjectParams();
      const subBool = this.validateSubject(itemList);
      // 名称非空校验
      if (!this.ruleName && this.currentPageStatus === "edit") {
        UiUtils.errorMessage("规则名称为空，请输入");
        this.directSave = false;
        return Promise.reject("规则名称为空!");
      }
      if (
        bool ||
        range.errors ||
        !subBool ||
        this.isLongName ||
        this.isRepeatName ||
        !judgeParams.judgeLogicVO.mainAccountId // “判断逻辑” 为空
      ) {
        this.directSave = false;
        return Promise.reject("页面编辑有误!");
      }
      this.btnLoading = true;
      const queryParams = {
        ruleName: this.ruleName,
        ...judgeParams,
        ...rangeParams,
        itemList
      };
      const id = UrlUtils.getQuery("inTab");
      if (this.currentPageStatus === "edit") {
        const ruleInfoObj = this.ruleInfo;
        const { objectId, rowId, rowName } = ruleInfoObj;
        queryParams.objectId = objectId;
        queryParams.rowId = rowId;
        queryParams.rowName = rowName;
        if (this.ruleName.length > 64) {
          this.btnLoading = false;
          this.directSave = false;
          // 失去焦点的时候已经提示
          // UiUtils.errorMessage("重分类名称不能超过64个！");
          return Promise.reject("名称超长!");
        }
      }
      await rearrangeService("operateReclassification", queryParams)
        .then(res => {
          UiUtils.successMessage("保存成功");
          this.updateTabTitle({
            id: this.params ? this.params.id : id,
            title: this.ruleName
          });
          this.clearCommonSaveEventsMixin();
          rearrangeService("getReclassificationList", {
            objectId: res.data.data
          }).then(res => {
            this.afterAddInfo = res.data.data.data[0];
            this.initData(res.data.data.data[0]);
          });
          this.updatePageStaus({
            key: this.params ? this.params.id : id,
            status: "detail"
          });
        })
        .catch(() => {
          this.ruleName = this.cloneRuleName;
        })
        .finally(() => {
          this.btnLoading = false;
          this.directSave = false;
        });
    },
    addSaveEventCb(notSaveCb) {
      this.addCallBackFnMixin(
        this.handleSave.bind(this),
        "您要保存对“重分类”所做的更改吗"
      );
    },
    closeNewAddTab() {
      let currKey = this.params && this.params.params.ruleInfo.key;
      if (!this.selfTab) {
        currKey = UrlUtils.getQuery("inTab");
      }
      this.closetabMixin(currKey);
    },
    addSaveCb() {
      this.addSaveEventCb(this.closeNewAddTab);
    },
    inputChange(type) {
      if (this.ruleName.length > 64) {
        if (type === "add") {
          this.isLongName = true;
          this.isRepeatName = false;
        } else {
          this.ruleName = this.cloneRuleName;
          UiUtils.errorMessage("字符长度超过64位限制，请检查");
        }
        return;
      } else {
        this.isLongName = false;
      }
      this.addSaveCb();
    }
  }
};
</script>

<style scoped lang="less">
.rearrange-detail {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .edit-title {
    position: relative;
    margin: 0.25rem 0;
  }
  .edit-name {
    width: calc(100% - 1.5rem);
    position: absolute;
    left: @yn-padding-l;
    /deep/ input {
      background: @yn-background-color;
    }
  }
  .err-tip {
    position: absolute;
    left: 0;
    top: @rem32;
    display: inline-block;
    color: @yn-error-color;
  }
  .error-tips {
    /deep/.ant-input {
      border: 1px solid @yn-error-color;
      border-radius: @rem4;
      outline: none;
      box-shadow: none;
    }
  }
  .header-title {
    display: inline-block;
    font-size: @rem16;
    font-weight: 600;
  }
  .detail-header {
    width: 100%;
    height: 3.5rem;
    background: @yn-component-background;
    padding: @yn-padding-l @yn-padding-xxxxl;
    box-sizing: border-box;
  }
  .padding-style {
    padding: @yn-padding-xl @yn-padding-xxxxl !important;
  }
  .margin-style {
    margin-top: @yn-margin-xs;
  }
  .container-base-info {
    margin-top: @yn-margin-xl;
    margin-bottom: 1.5rem;
  }
  .detail-container-edit {
    height: calc(100% - 6.5rem);
  }
  .detail-container-info {
    height: calc(100% - 3rem);
  }
  .detail-container {
    width: 100%;
    margin-top: @rem8;
    background: @yn-component-background;
    padding: @yn-padding-l @yn-padding-xl 0 @yn-padding-xl;
    overflow-y: auto;
    .head-info-title {
      font-size: @rem14;
      color: @yn-heading-color;
      font-weight: 600;
      margin-bottom: @yn-margin-l;
    }
    .info-title {
      text-align: right;
      height: @rem32;
      line-height: @rem32;
      padding-right: @yn-padding-s;
      color: @yn-text-color-secondary;
      font-size: @rem14;
    }
  }

  .detail-footer {
    width: calc(100% - 1.5rem);
    text-align: right;
    margin: 0 @yn-margin-l;
    padding: 0.5rem @yn-padding-l 0.5rem 0;
    background: @yn-component-background;
    border-top: 1px solid @yn-border-color-base;
    .footer-btn {
      min-width: 5rem;
      &:first-child {
        margin-right: @yn-margin-s;
      }
    }
  }
}
/deep/.edit-pan {
  float: left;
  margin-left: @yn-margin-xl;
}
</style>
