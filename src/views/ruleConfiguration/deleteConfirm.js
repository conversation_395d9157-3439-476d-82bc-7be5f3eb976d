import UiUtils from "yn-p1/libs/utils/UiUtils";

export default function deleteConfirModal({
  title = "删除已选规则",
  content = "规则删除后，列表不再显示",
  iconCb,
  okText = "删除",
  cancelText = "取消",
  okCb,
  onOk, // okCb、onOk 都是 确定回调
  cancelcb,
  vm
}) {
  if (!iconCb) {
    iconCb = () =>
      vm.$createElement("yn-icon", {
        props: {
          type: "exclamation-circle",
          theme: "filled"
        }
      });
  }
  UiUtils.confirm({
    title,
    content,
    icon: iconCb,
    okText,
    cancelText,
    onOk: okCb || onOk,
    cancel: cancelcb
  });
}
