<template>
  <div class="read-only-rule-set">
    <p class="title">基本信息</p>
    <span class="base-info">
      <span class="method-name-lable">合并方法名称:</span>
      <span class="method-name-val">
        {{ ruleSetData.mergeMethod && ruleSetData.mergeMethod.methodName }}
      </span>
    </span>
    <yn-divider />
    <p class="title rule-set-title">TI规则集</p>
    <span class="tips">以下选择的TI规则，TI规则执行的顺序是从上到下</span>
    <ul class="rule-set-list">
      <li v-for="item in ruleSetData.tiRules || []" :key="item.ruleId">
        {{ item.ruleName }}
      </li>
    </ul>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-divider/";
export default {
  name: "ReadOnlyRuleSet",
  props: {
    ruleSetData: {
      type: Object,
      default: () => {
        return {
          id: "",
          mergeMethod: {},
          tiRules: []
        };
      }
    }
  }
};
</script>
<style lang="less" scoped>
.rule-set-list {
  li {
    height: @rem36;
    line-height: @rem36;
    color: @yn-text-color;
  }
}
.base-info {
  height: @rem32;
  line-height: @rem32;
  display: inline-block;
  .method-name-lable {
    display: inline-block;
    width: 6.25rem;
    text-align: right;
    margin-right: @rem8;
    color: @yn-text-color-secondary;
    font-weight: 600;
  }
}
.rule-set-title {
  margin-bottom: @rem4;
}
.title {
  font-weight: 600;
  height: @rem22;
  line-height: @rem22;
}
.tips {
  color: @yn-label-color;
  margin-bottom: @rem12;
  height: @rem22;
  line-height: @rem22;
  display: inline-block;
}
.read-only-rule-set {
  /deep/.ant-divider-horizontal {
    margin: 1rem 0;
  }
}
</style>
