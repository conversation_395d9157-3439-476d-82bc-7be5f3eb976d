<template>
  <yn-drawer
    title="添加TI规则"
    width="20rem"
    :visible="true"
    :maskClosable="false"
    @close="onClose"
  >
    <yn-spin :spinning="spinning">
      <p class="title">请选择需要的TI规则</p>
      <ul class="ti-rule-list">
        <li v-for="(item, index) in listData" :key="item.ruleId">
          <yn-checkbox
            :checked="item.isChecked"
            @change="setCheckedData($event, item.ruleId, index)"
          >
            {{ item.ruleName }}
          </yn-checkbox>
        </li>
      </ul>
    </yn-spin>
    <div class="tiRules">
      <yn-button :style="{ marginRight: '8px' }" @click="onClose">
        取消
      </yn-button>
      <yn-button type="primary" @click="onClose('save')">
        确定
      </yn-button>
    </div>
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-checkbox/";
import "yn-p1/libs/components/yn-spin/";
import commonService from "@/services/common";
import cloneDeep from "lodash/cloneDeep";
export default {
  name: "TiRuleList",
  props: {
    checkedList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      listData: [],
      checkedData: [],
      mapCheckedObj: {},
      spinning: true
    };
  },
  watch: {
    checkedList: {
      handler(newVal) {
        if (newVal.length > 0) {
          const tempObj = {};
          newVal.forEach(item => {
            const { ruleId } = item;
            tempObj[ruleId] = item;
          });
          this.mapCheckedObj = tempObj;
          this.$set(this, "checkedData", cloneDeep(newVal));
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.getTiRuleList();
  },
  methods: {
    async getTiRuleList() {
      try {
        await commonService("getMemberByDimCode", "TiRuleSets").then(res => {
          const { data } = res;
          const loop = data => {
            data.forEach(item => {
              const { children, id, name } = item;
              item.ruleId = id;
              item.ruleName = name;
              item.isChecked = !!this.mapCheckedObj[id];
              if (children && children.length > 0) {
                loop(children);
              }
            });
          };
          // 将checkedList 里面的值 在node 上添加isChecked=true
          loop(data);
          this.spinning = false;
          this.$set(this, "listData", data);
        });
      } catch (e) {
        this.spinning = false;
      }
    },
    setCheckedData(checked, ruleId, index) {
      this.listData[index].isChecked = checked;
      if (!checked) {
        this.checkedData = this.checkedData.filter(item => {
          return item.ruleId !== ruleId;
        });
        this.$set(this, "checkedData", this.checkedData);
      } else {
        const { ruleId, ruleName } = this.listData[index];
        this.checkedData.push({
          ruleId,
          key: ruleId,
          ruleName
        });
      }
      this.$set(this, "listData", this.listData);
    },
    onClose(type) {
      let data = [];
      if (type === "save") {
        data = cloneDeep(this.checkedData);
      }
      this.$emit("closeRuleListDrawer", {
        type,
        checkedList: data
      });
    }
  }
};
</script>
<style lang="less" scoped>
.title {
  margin-bottom: @rem8;
}
.ti-rule-list li {
  height: @rem36;
  line-height: @rem36;
}
.tiRules {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid @yn-border-color-base;
  padding: 10px 16px;
  background: @yn-body-background;
  text-align: right;
  z-index: 1;
}
</style>
