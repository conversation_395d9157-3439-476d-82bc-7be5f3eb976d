<template>
  <div class="rule-set-cont">
    <yn-spin :spinning="spinning">
      <template v-if="!spinning">
        <empty v-if="!listData.length">
          <span slot="title">{{ title }}</span>
          <span slot="desc">当前{{ title }}为空，马上开始新增吧</span>
          <yn-button slot="btn" type="primary" @click="addRuleSet">
            新增{{ ruleSetText }}
          </yn-button>
        </empty>
        <rule-set-list
          v-else
          ref="ruleSetList"
          :orgData="listData"
          :total="total"
          @addRuleSet="addRuleSet"
          @operationRuleSet="showRuleSetDrawer"
          @changePagination="changePagination"
        />
      </template>
      <!-- 占位 防止有数据时闪动空状态 start-->
      <template v-else>
        <div style="width:100%;height:100%"></div>
      </template>
      <!-- 占位 end-->
      <rule-set-drawer
        v-if="isShowRuleSetDrawer"
        :operationType="operationType"
        :ruleSetId="ruleSetId"
        :isEdit="isEdit"
        @operationRuleSet="showRuleSetDrawer"
        @refreshTableData="refreshTableData"
      />
    </yn-spin>
  </div>
</template>
<script>
import Empty from "../empty.vue";
import RuleSetDrawer from "./ruleSetDrawer";
import RuleSetList from "./ruleSetList";
import ruleSetMaintenanceService from "@/services/ruleSetMaintenance";
import "yn-p1/libs/components/yn-spin/";
import cloneDeep from "lodash/cloneDeep";
const RULE_SET = "规则集";
const TITLE = RULE_SET + "维护";
const FIRST_PAGE = {
  pageNum: 0,
  pageSize: 10
};
export default {
  name: "RuleSetMaintenance",
  components: { Empty, RuleSetDrawer, RuleSetList },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      title: TITLE,
      ruleSetText: RULE_SET,
      listData: [],
      total: 0,
      spinning: true,
      isShowRuleSetDrawer: false,
      checkedList: [],
      isShowRuleList: false,
      ruleSetId: "", // 编辑的规则集id
      operationType: "add",
      pagination: cloneDeep(FIRST_PAGE),
      isEdit: false // 是否是编辑状态
    };
  },
  async created() {
    await this.getListData(true);
    this.memberReferenceJumpEvent();
  },
  methods: {
    memberReferenceJumpEvent() {
      const defaultParams = this.selfTab
        ? this.params.params
        : this.getTabParamsMixin() || {};
      const { id: ruleSetId } = defaultParams.MRJ || {};
      if (ruleSetId) {
        this.showRuleSetDrawer({
          openStatus: true,
          ruleSetId,
          isEdit: false
        });
      }
    },
    async getListData() {
      this.spinning = true;
      await ruleSetMaintenanceService("getTableData", FIRST_PAGE).then(res => {
        const { total, items = [] } = res.data.data || [];
        this.total = total;
        this.$set(this, "listData", items);
        this.spinning = false;
      });
    },
    refreshTableData(isRefresh) {
      if (!this.$refs.ruleSetList) {
        this.getListData();
      } else {
        this.$refs.ruleSetList.$refs.pageList.filterForm.resetFields();
        this.$refs.ruleSetList.queryTableData(isRefresh);
      }
    },
    addRuleSet() {
      this.operationType = "add";
      this.ruleSetId = "";
      this.isShowRuleSetDrawer = true;
      this.isEdit = false;
    },
    showRuleSetDrawer(obj) {
      const { openStatus, ruleSetId, isEdit = false } = obj;
      this.operationType = ruleSetId ? "edit" : "add";
      this.ruleSetId = ruleSetId;
      this.isShowRuleSetDrawer = openStatus;
      this.isEdit = isEdit;
    },
    changePagination(obj) {
      this.pagination = { ...obj };
    }
  }
};
</script>
<style lang="less" scoped>
.rule-set-cont {
  width: 100%;
  height: 100%;
  background: @yn-background-color;
  /deep/.ant-spin-nested-loading,
  /deep/.ant-spin-container {
    height: 100%;
  }
  /deep/.rule-empty .empty-title {
    font-weight: 600;
  }
}
</style>
