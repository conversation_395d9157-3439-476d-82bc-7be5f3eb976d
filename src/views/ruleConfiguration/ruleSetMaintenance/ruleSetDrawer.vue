<template>
  <yn-drawer
    :title="title"
    placement="right"
    :closable="true"
    :visible="true"
    :maskClosable="false"
    @close="onClose"
  >
    <yn-spin :spinning="spinning">
      <template v-if="operationType === 'add' || isEditStatus">
        <p class="title">基本信息</p>
        <yn-form
          :form="form"
          class="base-info"
          v-bind="{ labelCol: { span: 8 }, wrapperCol: { span: 16 } }"
        >
          <yn-form-item label="合并方法名称">
            <yn-select-tree
              ref="selector"
              v-decorator="[
                'methodId',
                {
                  rules: [
                    {
                      required: true,
                      message: '请选择合并方法名称',
                      initialValue: methodsName
                    }
                  ],
                  initialValue:
                    ruleSetData &&
                    ruleSetData.mergeMethod &&
                    ruleSetData.mergeMethod.methodId
                }
              ]"
              :datasource="showTreeData"
              searchMode="custom"
              forceRender
              nonleafselectable
              @customSearch="onCustomeSearch"
              @dropdownVisibleChange="dropdownVisibleChange"
            />
          </yn-form-item>
        </yn-form>
        <p class="title ti-rule-set-title">TI规则集</p>
        <template v-if="listData.length > 0">
          <span class="tips">通过排序决定TI规则集执行顺序</span>
          <yn-list
            rowKey="key"
            itemLayout="horizontal"
            class="ti-rule-set"
            size="small"
            :dataSource="listData"
            :drag="true"
          >
            <yn-list-item slot="renderItem" slot-scope="item">
              <svg-icon
                class="list-drag-icon"
                type="icon-c1_cr_drag-and-drop"
              />
              {{ item.ruleName }}
            </yn-list-item>
          </yn-list>
          <yn-button type="link" class="add-rule-btn" @click="addRule">
            请添加TI规则
          </yn-button>
        </template>
        <template v-else>
          <div class="empty-list">
            <span>当前TI规则集为空，请 </span>
            <yn-button type="link" class="add-rule-btn" @click="addRule">
              添加TI规则
            </yn-button>
            <p v-if="isChecked" class="error-tips">
              请添加TI规则
            </p>
          </div>
        </template>
      </template>
      <read-only-rule-set v-else :ruleSetData="ruleSetData" />
    </yn-spin>
    <ti-rule-list
      v-if="isShowRuleList"
      :checkedList="listData"
      @closeRuleListDrawer="closeRuleListDrawer"
    />
    <div class="drawer-footer">
      <yn-button
        v-if="isEditStatus || operationType === 'add'"
        :style="{ marginRight: '8px' }"
        @click="onClose"
      >
        取消
      </yn-button>
      <yn-button
        v-if="!isEditStatus && operationType === 'edit'"
        type="primary"
        :style="{ marginRight: '8px' }"
        @click="onEdit"
      >
        编辑
      </yn-button>
      <yn-button
        v-else
        type="primary"
        :loading="loading"
        @click="onClose('save')"
      >
        保存
      </yn-button>
    </div>
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-list/";
import "yn-p1/libs/components/yn-list-item/";
import ReadOnlyRuleSet from "./readOnlyRuleSet.vue";
import commonService from "@/services/common";
import ruleSetMaintenanceService from "@/services/ruleSetMaintenance";
import cloneDeep from "lodash/cloneDeep";
import { DfsTraverse } from "@/utils/common.js";
import TiRuleList from "./tiRuleList";
const EDIT_RULE_SET = "编辑规则集";
const RULE_SET_INFO = "规则集详情";
const ADD_RULE_SET = "新增规则集";
export default {
  name: "RuleSetDrawer",
  components: {
    ReadOnlyRuleSet,
    TiRuleList
  },
  props: {
    ruleSetId: {
      type: String,
      default: ""
    },
    visible: {
      type: Boolean,
      default: false
    },
    operationType: {
      type: String,
      default() {
        return "";
      } // add or edit
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      spinning: false,
      dataSource: [], // 原始数据
      showTreeData: [], // 展示树的数据
      form: null,
      methodsName: "", // 合并方法名称
      listData: [], // 列表数据
      isChecked: false, // 是否校验
      isShowRuleList: false, // 是否显示添加规则抽屉
      loading: false,
      ruleSetData: {},
      isEditStatus: false
    };
  },
  computed: {
    title() {
      if (this.ruleSetId) {
        return this.isEditStatus ? EDIT_RULE_SET : RULE_SET_INFO;
      } else {
        return ADD_RULE_SET;
      }
    }
  },
  watch: {
    ruleSetId: {
      async handler(newVal) {
        this.spinning = true;
        await this.getConsolidatedMethod();
        if (newVal) {
          // 请求规则集信息
          await this.getRuleSetData(newVal);
        }
        this.spinning = false;
      },
      immediate: true
    },
    isEdit: {
      handler(newVal) {
        this.isEditStatus = newVal;
      },
      immediate: true
    }
  },
  created() {
    this.form = this.$form.createForm(this, {
      name: "baseInfoForm"
    });
  },
  methods: {
    async getConsolidatedMethod() {
      try {
        await commonService("getMemberByDimCode", "ConsolidatedMethod").then(
          res => {
            const data = this.convertData(res.data);
            this.showTreeData = cloneDeep(data);
            this.dataSource = cloneDeep(data);
          }
        );
      } catch (e) {
        this.spinning = false;
      }
    },
    convertData(data) {
      const loop = data => {
        data.map(item => {
          const { id, name, children } = item;
          item.key = id;
          item.label = name;
          if (children && children.length) {
            loop(children);
          }
        });
      };
      loop(data);
      return data;
    },
    onCustomeSearch(e) {
      const { searchValue } = e;
      const res = [];
      DfsTraverse(this.dataSource, "label", searchValue, item => {
        res.push(item);
      });
      this.$set(this, "showTreeData", res);
    },
    dropdownVisibleChange(isOpen) {
      if (!isOpen) {
        this.$set(this, "showTreeData", cloneDeep(this.dataSource));
      }
    },
    async getRuleSetData(id) {
      try {
        await ruleSetMaintenanceService("getDetail", id).then(res => {
          const { data } = res.data || {};
          const { mergeMethod = {}, tiRules = [] } = data;
          this.ruleSetData = res.data.data;
          this.methodsName = mergeMethod.methodName;
          this.listData = tiRules.map(item => {
            const { ruleId: key } = item;
            return {
              key,
              ...item
            };
          });
        });
      } catch (e) {
        this.spinning = false;
      }
      this.spinning = false;
    },
    async onClose(type) {
      if (type === "save") {
        this.loading = true;
        await this.saveEvent();
      } else {
        if (this.isEditStatus) {
          this.isEditStatus = false;
          this.getRuleSetData(this.ruleSetId);
          return;
        }
        this.closeDrawer();
      }
    },
    async saveEvent() {
      const param = this.getSaveParam();
      const res = await this.checkSaveParams(param);
      if (!res) {
        this.loading = false;
        return;
      }
      if (this.ruleSetId) {
        param.objectId = this.ruleSetId;
      }
      try {
        await ruleSetMaintenanceService(
          this.ruleSetId ? "editRuleSet" : "saveData",
          param
        ).then(() => {
          this.loading = true;
          this.closeDrawer(true);
        });
      } catch (e) {
        this.loading = false;
      }
    },
    closeDrawer(isRefresh) {
      this.$emit("operationRuleSet", {
        openStatus: false
      });
      if (isRefresh) {
        this.$emit("refreshTableData", !this.ruleSetId);
      }
    },
    async checkSaveParams(params) {
      this.isChecked = true;
      const { ruleId = [] } = params;
      let res = true;
      try {
        await this.form.validateFields();
      } catch {
        this.loading = false;
        res = false;
      }
      if (!ruleId.length) {
        res = false;
      }
      return res;
    },
    getSaveParam() {
      const ruleId = this.listData.map(item => item.ruleId);
      return {
        ...this.form.getFieldsValue(),
        ruleId
      };
    },
    addRule() {
      this.isShowRuleList = true;
    },
    closeRuleListDrawer(obj) {
      const { checkedList, type } = obj;
      this.isShowRuleList = false;
      if (type === "save") {
        this.setListData(checkedList);
      }
    },
    setListData(checkedList) {
      const mapObj = {};
      checkedList.forEach(item => {
        const { ruleId } = item;
        mapObj[ruleId] = item;
      });
      let res = [];
      this.listData.forEach(item => {
        const { ruleId } = item;
        // 在已选列表中是否存在，存在则放入listData
        item.key = ruleId;
        if (mapObj[ruleId]) {
          res.push(item);
          delete mapObj[ruleId];
        }
      });
      res = res.concat(Object.values(mapObj));
      this.$set(this, "listData", res);
    },
    onEdit() {
      this.isEditStatus = true;
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.ant-drawer-body {
  padding-right: 0;
  overflow: hidden;
}
/deep/.ant-spin-nested-loading {
  height: calc(100% - 27px);
  overflow: auto;
}
/deep/.ant-spin-nested-loading .ant-spin-container {
  padding-right: 1.5rem;
}
.list-drag-icon {
  margin-right: @rem4;
  color: @yn-label-color;
  cursor: all-scroll;
}
.add-rule-btn {
  // margin-top: @rem12;
  display: inline-block;
  height: @rem22;
  line-height: @rem22;
  cursor: pointer;
  color: @yn-chart-1;
  padding: 0 @rem8;
}
.error-tips {
  color: @yn-error-color;
  text-align: left;
}
.title {
  font-weight: 600;
  height: @rem22;
  line-height: @rem22;
}
.ti-rule-set-title {
  margin-bottom: @rem4;
}
.tips {
  color: @yn-label-color;
  margin-bottom: @rem12;
  display: inline-block;
  height: @rem22;
  line-height: @rem22;
}
.empty-list {
  height: @rem36;
  line-height: @rem36;
  text-align: center;
  background: @yn-background-color;
  color: @yn-label-color;
}
.ti-rule-set {
  /deep/.ant-list-item {
    padding-left: 0;
  }
  /deep/.yn-list-row:hover {
    background: @yn-table-header-bg;
  }
}
.drawer-footer {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  padding: @rem10 @rem16;
  background: @yn-component-background;
  text-align: right;
  z-index: 1;
  box-shadow: inset 0px 1px 0px 0px rgb(225 229 235);
}
</style>
