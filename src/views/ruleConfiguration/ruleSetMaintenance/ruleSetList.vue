<template>
  <div class="rule-set-list">
    <yn-page-list
      ref="pageList"
      :pageTitle="pageTitle"
      :pageHeader="pageHeader"
      :tableConfig="tableConfig"
      :toolsConfig="toolsConfig"
      @table_rowSelectChange="handlerSelectChange"
      @table_change="handleSearch"
      @pageHeader_search="searchTableData"
      @pageHeader_reset="
        handleSearch({ pagingInfo: { current: 1, pageSize: 10 } })
      "
    >
      <template v-slot:[`tools.title`]>
        <template v-if="tableConfig.rowSelection">
          <span class="selected-number">
            已选{{ selectedRowKeysAll.length }}条
          </span>
          <yn-button
            class="batche-delete-rule"
            type="primary"
            :disabled="!selectedRowKeysAll.length"
            @click="deleteRuleSetConfirm(selectedRowKeysAll)"
          >
            删除
          </yn-button>
          <yn-button @click="perationBatchDeleteRule(false)">取消</yn-button>
        </template>
        <template v-else>
          <yn-button class="delete-rules" type="primary" @click="addRule">
            新增
          </yn-button>
          <yn-button @click="perationBatchDeleteRule(true)">删除</yn-button>
        </template>
      </template>
      <template v-if="!tableConfig.rowSelection" v-slot:[`tools.btns`]>
        <svg-icon title="刷新" type="icon-shuaxin" @click="refresh" />
      </template>
      <template v-slot:[`table.operation`]="record">
        <a
          href="javascript:;"
          class="yn-a-link edit-btn"
          @click="editRuleSet(record.key, true)"
        >
          编辑
        </a>
        <yn-popconfirm
          title="你要删除当前规则吗？"
          placement="bottomRight"
          okText="删除"
          cancelText="取消"
          @confirm="deleteRuleSet(record.key)"
        >
          <a class="yn-a-link" href="javascript:;" @click.stop>删除</a>
        </yn-popconfirm>
        <svg-icon
          class="cell-operation-btns"
          type="icon-c1_cr_form_enter"
          :isIconBtn="false"
          @onClick="editRuleSet(record.key)"
        />
      </template>
    </yn-page-list>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-page-list";
import cloneDeep from "lodash/cloneDeep";
import deleteConfirModal from "../deleteConfirm";
import ruleSetMaintenanceService from "@/services/ruleSetMaintenance";
import UiUtils from "yn-p1/libs/utils/UiUtils";
const COLUMNS = [
  {
    dataIndex: "mergeMethod",
    key: "mergeMethod",
    title: "合并方法",
    width: "15rem"
  },
  {
    dataIndex: "tiRule",
    key: "tiRule",
    title: "TI规则集"
  },
  {
    key: "operation",
    scopedSlots: {
      customRender: "operation"
    },
    width: "8rem",
    title: "操作"
  }
];
export default {
  name: "RuleSetList",
  props: {
    orgData: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      pageTitle: {
        title: "规则集维护"
      },
      toolsConfig: {
        options: [
          [
            {
              slotName: "btns"
            }
          ]
        ]
      },
      pageHeader: {
        filterKey: "",
        collapsed: true,
        hideOptions: ["setting"], // 折叠：collapse，设置：setting，分割线：divider， 查询：query，重置：reset
        formProrps: {
          layout: "horizontal"
        },
        formOptions: [
          {
            label: "合并方法",
            field: "mergeMethod",
            element: "yn-input",
            props: {
              placeholder: "请输入"
            }
          },
          {
            label: "TI规则集",
            field: "tiRule",
            element: "yn-input",
            props: {
              placeholder: "请输入"
            }
          }
        ]
      },
      tableConfig: {
        loading: false,
        pagination: {
          total: 0,
          current: 1,
          pageSize: 20,
          currentPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: total => `总计${total}条`
        },
        dataSource: [],
        customRow: this.handlerCustomRow,
        rowSelection: false,
        columns: COLUMNS
      },
      deleteRuleSetIds: [], // 行上删除时，记录删除的id消息
      tableSelectedRowKeyAll: [], // 当取消选中行时，清空此数组
      selectedRowKeysAll: [] // 当前 checkbox 选中的行id集合
    };
  },
  watch: {
    total: {
      handler(newVal) {
        this.tableConfig.pagination.total = newVal;
      },
      immediate: true
    },
    orgData: {
      handler(newVal) {
        if (newVal) {
          this.tableConfig.dataSource = cloneDeep(newVal);
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    addRule() {
      this.$emit("operationRuleSet", {
        openStatus: true
      });
    },
    handleReset() {
      this.form.resetFields();
      this.queryTableData(true);
    },
    async refresh() {
      const {
        mergeMethod,
        tiRule
      } = this.$refs.pageList.$refs.ynPageHeader.$refs.filterForm.form.getFieldsValue();
      if (!mergeMethod && !tiRule) {
        await this.queryTableData(true);
      } else {
        await this.searchTableData({ mergeMethod, tiRule });
      }
    },
    async queryTableData(isRefresh) {
      this.tableConfig.loading = true;
      // 没有查询条件
      let pageObj = {};
      if (!isRefresh) {
        const { pageSize, current } = this.tableConfig.pagination;
        pageObj = {
          pageSize,
          pageNum: current
        };
      }
      await this.getCurrPageData(pageObj);
      this.tableConfig.loading = false;
    },
    // 分页查询
    async getCurrPageData(pageObj) {
      const { pageSize = 10, pageNum = 1 } = pageObj || {};
      await ruleSetMaintenanceService("getTableData", {
        pageSize,
        pageNum
      }).then(res => {
        const { total = 0, items = [] } = res.data.data || [];
        const { pagination } = this.tableConfig;
        this.tableConfig.pagination = {
          ...pagination,
          total: total || 1,
          current: pageNum
        };
        this.tableConfig.dataSource = items;
      });
    },
    // 查询表格数据
    async searchTableData(params) {
      this.tableConfig.loading = true;
      const { mergeMethod: searchMethodName, tiRule: searchTiName } = params;
      const { pagination } = this.tableConfig;
      const { pageSize, current } = pagination;
      await ruleSetMaintenanceService("searchTableData", {
        searchMethodName,
        searchTiName,
        pageSize,
        pageNum: current
      }).then(res => {
        const { total = 0, items = [] } = res.data.data || [];
        this.$set(this.tableConfig, "pagination", {
          ...pagination,
          total: total || 1
        });
        this.tableConfig.dataSource = items;
      });
      this.tableConfig.loading = false;
    },
    handlerCustomRow(record, index) {
      return {
        on: {
          click: () => {
            if (this.isDelete) return;
            this.editRuleSet(record.key);
          }
        }
      };
    },
    editRuleSet(ruleSetId, isEdit) {
      this.$emit("operationRuleSet", {
        openStatus: true,
        ruleSetId,
        isEdit
      });
    },
    perationBatchDeleteRule(type) {
      this.tableConfig.rowSelection = type;
      if (!type) {
        this.deleteRuleSetIds.splice(0);
        this.selectedRowKeysAll.splice(0);
        this.tableSelectedRowKeyAll.splice(0);
        this.$set(this.tableConfig, "columns", COLUMNS);
      } else {
        this.$set(this.tableConfig, "columns", COLUMNS.slice(0, 2));
      }
    },
    deleteRuleSetConfirm(ruleSetIds) {
      deleteConfirModal({
        vm: this,
        onOk: () => {
          this.deleteRuleSet(ruleSetIds);
        }
      });
    },
    deleteRuleSet(ruleSetIds) {
      this.tableConfig.loading = true;
      let params = ruleSetIds;
      if (!Array.isArray(params)) {
        params = [ruleSetIds];
      }
      ruleSetMaintenanceService("deleteRuleSet", params).then(res => {
        if (res.data.success) {
          // 批量删除
          if (Array.isArray(ruleSetIds)) {
            this.perationBatchDeleteRule(false);
          } else {
            // 行上 单个 删除
            this.resetSelectRowInfo(ruleSetIds);
          }
          UiUtils.successMessage("删除成功");
        }
        this.searchTableData(this.$refs.pageList.filterForm.getFieldsValue());
      });
    },
    resetSelectRowInfo(ruleSetId) {
      this.deleteRuleSetIds.push(ruleSetId);
      const tempArr = this.selectedRowKeysAll.filter(item => {
        return this.deleteRuleSetIds.indexOf(item) === -1;
      });
      this.$set(this, "selectedRowKeysAll", tempArr);
    },
    handlerSelectChange(selectReord, selectedRowKeys) {
      const res = selectedRowKeys.filter(item => {
        return this.deleteRuleSetIds.indexOf(item) === -1;
      });
      this.$set(this, "tableSelectedRowKeyAll", selectedRowKeys);
      this.$set(this, "selectedRowKeysAll", res);
    },
    handleSearch({ pagingInfo }) {
      const { current, pageSize } = pagingInfo;
      const { pagination } = this.tableConfig;
      this.$set(this.tableConfig, "pagination", {
        ...pagination,
        current,
        pageSize
      });
      this.changeTablePagination();
    },
    changeTablePagination() {
      const { current: pageNum, pageSize } = this.tableConfig.pagination;
      this.$emit("changePagination", {
        pageSize,
        pageNum
      });
      const {
        mergeMethod,
        tiRule
      } = this.$refs.pageList.$refs.ynPageHeader.$refs.filterForm.form.getFieldsValue();
      if (!mergeMethod && !tiRule) {
        this.queryTableData();
      } else {
        this.searchTableData({ mergeMethod, tiRule });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.rule-set-list {
  height: 100%;
  /deep/.ant-table-row-cell-break-word {
    padding: 1px @yn-padding-xl;
  }
  .batche-delete-rule,
  .delete-rules {
    margin-right: @rem8;
  }
  .edit-btn {
    margin-right: @yn-margin-l;
  }
  .cell-operation-btns {
    color: @yn-label-color;
  }
}
</style>
