<template>
  <div class="rule-currentoffset">
    <yn-spin
      v-if="cloneTableData.length"
      :spinning="spinning"
      size="large"
      wrapperClassName="currentoffset-load-cont"
    >
      <yn-page-list
        ref="pageList"
        :pageTitle="pageTitle"
        :tableConfig="tableConfig"
        :toolsConfig="toolsConfig"
        @table_rowSelectChange="onSelectChange"
        @table_change="handleSearch"
      >
        <template v-slot:[`tools.title`]>
          <template v-if="isDelete">
            <span class="choose-item">
              已选 {{ selectedRowKeysAll.length }} 条
            </span>
            <yn-button
              class="delete-confirm"
              type="primary"
              :disabled="!selectedRowKeysAll.length"
              @click="deleteData"
            >
              删除
            </yn-button>
            <yn-button @click="handleCancelDel">
              取消
            </yn-button>
          </template>
          <template v-else>
            <yn-input-search
              v-model="searchWord"
              class="head-search"
              placeholder="请输入规则名称"
              @search="onSearch"
            />
          </template>
        </template>
        <template v-if="!isDelete" v-slot:[`tools.btns`]>
          <yn-button type="primary" @click="addCurrentoffset">
            新增
          </yn-button>
          <yn-button class="btn-delete" @click="() => (isDelete = !isDelete)">
            删除
          </yn-button>
          <yn-divider class="head-divider" type="vertical" />
          <svg-icon
            title="刷新"
            type="icon-shuaxin"
            @click="getCurrentoffsetList"
          />
        </template>
        <template v-slot:[`table.operation`]="record">
          <div class="table-operation">
            <a
              href="javascript:;"
              class="yn-a-link"
              @click.stop="editRule(record)"
            >
              编辑
            </a>
            <yn-popconfirm
              title="你要删除当前规则吗？"
              placement="bottomRight"
              okText="删除"
              cancelText="取消"
              @confirm="confirmDelete(record)"
            >
              <a class="a-delete yn-a-link" href="javascript:;" @click.stop>
                删除
              </a>
            </yn-popconfirm>
            <svg-icon
              class="cell-operation-btns"
              type="icon-c1_cr_form_enter"
              :isIconBtn="false"
            />
          </div>
        </template>
      </yn-page-list>
    </yn-spin>
    <yn-spin
      v-else
      :spinning="spinning"
      size="large"
      wrapperClassName="currentoffset-load-cont"
    >
      <Empty>
        <span slot="title">往来抵销</span>
        <span slot="desc">当前往来抵销规则为空，马上开始新增吧！</span>
        <yn-button slot="btn" type="primary" @click="addCurrentoffset">
          新增往来抵销规则
        </yn-button>
      </Empty>
    </yn-spin>
  </div>
</template>
<script>
import Empty from "../empty.vue";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-page-list";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import { mapActions, mapMutations, mapState } from "vuex";
import currentOffsetService from "@/services/currentoffset";
import cloneDeep from "lodash/cloneDeep";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import deleteConfirModal from "../deleteConfirm";
const TABNAME = "currentoffsetRule";
const TAB_URI = "/ruleConfig/currentoffsetRule";
const COLUMNS = [
  {
    title: "规则名称",
    dataIndex: "offsetName",
    key: "offsetName"
  },
  {
    title: "操作",
    key: "operation",
    width: "8rem",
    scopedSlots: {
      customRender: "operation"
    }
  }
];
export default {
  components: { Empty },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isDelete: false,
      pageTitle: {
        title: "往来抵销"
      },
      toolsConfig: {
        options: [
          [
            {
              slotName: "btns"
            }
          ]
        ]
      },
      tableConfig: {
        pagination: {
          total: 0,
          current: 1,
          pageSize: 20,
          currentPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: total => `总计${total}条`
        },
        columns: COLUMNS,
        dataSource: [],
        customRow: this.handlerCustomRow,
        rowSelection: false
      },
      cloneTableData: [],
      selectedRowKeysAll: [],
      tableSelectedRowKeyAll: [],
      deleteRuleSetIds: [],
      searchWord: "",
      spinning: false
    };
  },
  computed: {
    ...mapState({
      activeKey: state => state.common.tabActiveId
    })
  },
  watch: {
    activeKey: {
      handler(newKey) {
        if (newKey === this.params.id) {
          this.getCurrentoffsetList();
        }
      }
    },
    isDelete: {
      handler(newVal) {
        this.tableConfig.rowSelection = newVal;
        if (newVal) {
          this.$set(this.tableConfig, "columns", COLUMNS.slice(0, 1));
        } else {
          this.$set(this.tableConfig, "columns", COLUMNS);
        }
      }
    }
  },
  created() {
    this.getCurrentoffsetList();
    this.getAccountData();
    this.getRAData();
    this.activeTabCbMixin(this.getCurrentoffsetList);
  },
  methods: {
    ...mapActions({
      getAccountData: "currentoffset/getAccountData",
      getRAData: "currentoffset/getRAData"
    }),
    ...mapMutations({
      updatePageStaus: "currentoffset/updatePageStaus",
      addTab: "common/addTab"
    }),
    getCurrentoffsetList(isFirstPage = false) {
      const {
        tableConfig: {
          pagination: { pageSize: currentPageSize, current: currentPage }
        }
      } = this;
      const params = {
        offsetName: this.searchWord || "",
        offset: !isFirstPage ? currentPageSize * (currentPage - 1) : 0,
        limit: currentPageSize
      };
      this.spinning = true;
      currentOffsetService("getCurrentOffsetsList", params)
        .then(res => {
          const { items: data, totalResults: total } = res.data;
          let tableData = [];
          if (data && data.length) {
            data &&
              data.forEach(item => {
                item.key = item.objectId;
              });
            tableData = [...data];
            this.cloneTableData = cloneDeep(tableData);
            this.$forceUpdate();
          }
          this.tableConfig.pagination.total = total;
          this.tableConfig.dataSource = [...tableData];
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    onSearch(e) {
      this.searchWord = e;
      this.getCurrentoffsetList(!!e);
    },

    addCurrentoffset() {
      const id = AppUtils.generateUniqueId();
      const params = {
        ruleInfo: {
          key: id
        }
      };
      const newAddTabInfo = {
        id,
        router: TABNAME,
        title: "新增往来抵销规则",
        uri: TAB_URI,
        routerName: TABNAME,
        params,
        newTabParams: {
          status: "newAdd"
        }
      };
      this.newTabMixin(newAddTabInfo);
      this.updatePageStaus({
        key: id,
        status: "newAdd"
      });
    },

    handleSearch({ pagingInfo }) {
      const { current, pageSize } = pagingInfo;
      const { pagination } = this.tableConfig;
      this.$set(this.tableConfig, "pagination", {
        ...pagination,
        current,
        pageSize
      });
      this.getCurrentoffsetList();
    },

    handleCancelDel() {
      this.isDelete = !this.isDelete;
      this.selectedRowKeysAll.splice(0);
      this.tableSelectedRowKeyAll.splice(0);
    },

    onSelectChange(selectedRow, selectedRowKeysAll) {
      const res = selectedRowKeysAll.filter(item => {
        return this.deleteRuleSetIds.indexOf(item) === -1;
      });
      this.$set(this, "tableSelectedRowKeyAll", selectedRowKeysAll);
      this.$set(this, "selectedRowKeysAll", res);
    },

    deleteData() {
      if (this.selectedRowKeysAll.length === 0) {
        UiUtils.infoMessage("请选择需要删除的项");
        return;
      }
      deleteConfirModal({
        vm: this,
        onOk: () => {
          this.spinning = true;
          currentOffsetService("deleteCurrentOffset", this.selectedRowKeysAll)
            .then(res => {
              UiUtils.successMessage("删除成功");
              this.getCurrentoffsetList();
              this.deleteRuleSetIds.push(...this.selectedRowKeysAll);
              this.selectedRowKeysAll.splice(0);
            })
            .finally(() => {
              this.spinning = false;
              this.isDelete = false;
            });
        }
      });
    },

    newTabMixin(tabInfo) {
      const {
        id,
        router,
        title,
        uri,
        routerName,
        params,
        newTabParams
      } = tabInfo;
      this.newtabMixin({
        id,
        router,
        title,
        uri,
        routerName,
        params,
        newTabParams
      });
    },

    editRule(record) {
      const { offsetName: title, key: objectId } = record;
      const tabList = this.$store.state.common.tabs;
      const openItem = tabList
        .filter(tab => !tab.menuId)
        .find(tab => tab.title === title);
      const tabId = openItem ? openItem.id : objectId;
      const params = {
        offsetId: objectId,
        offsetName: title
      };
      const tabInfo = {
        id: tabId,
        router: TABNAME,
        title: title,
        uri: TAB_URI,
        routerName: TABNAME,
        params,
        newTabParams: {
          status: "edit"
        }
      };
      this.newTabMixin(tabInfo);
      this.updatePageStaus({
        key: tabId,
        status: "edit"
      });
    },
    handlerCustomRow(record, index) {
      return {
        on: {
          click: () => {
            if (this.isDelete) return;
            const { offsetName: title, key: objectId } = record;
            const tabList = this.$store.state.common.tabs;
            const openItem = tabList
              .filter(tab => !tab.menuId)
              .find(tab => tab.title === title);
            const tabId = openItem ? openItem.id : objectId;
            const params = { offsetId: objectId, offsetName: title };
            const drillInfo = {
              id: tabId,
              router: TABNAME,
              title: title,
              uri: TAB_URI,
              routerName: TABNAME,
              params,
              newTabParams: {
                status: "detail"
              }
            };
            this.newTabMixin(drillInfo);
            this.updatePageStaus({
              key: tabId,
              status: "detail"
            });
          }
        }
      };
    },
    async confirmDelete(record) {
      const id = [record.objectId];
      this.spinning = true;
      currentOffsetService("deleteCurrentOffset", id)
        .then(res => {
          UiUtils.successMessage("删除成功");
          const ident = this.selectedRowKeysAll.indexOf(record.objectId);
          if (ident !== -1) {
            this.deleteRuleSetIds.push(record.objectId);
            this.selectedRowKeysAll.splice(ident, 1);
          }
          this.closetabMixin(id);
          this.getCurrentoffsetList();
        })
        .finally(() => {
          this.spinning = false;
        });
    }
  }
};
</script>
<style scoped lang="less">
.currentoffset-container {
  height: 100%;
}

.rule-currentoffset {
  width: 100%;
  height: 100%;
  background: @yn-background-color;
  /deep/.currentoffset-load-cont,
  /deep/.ant-spin-container {
    height: 100%;
  }
  .delete-confirm {
    margin: 0 @yn-margin-s 0 @yn-margin-xl;
  }
  .table-operation {
    display: flex;
    align-items: center;
    .a-delete {
      margin-left: @yn-padding-l;
    }
    .icon-right {
      display: inline-block;
      transform: rotate(-90deg);
      width: @rem18;
      height: @rem18;
    }
  }
  .btn-delete {
    margin: 0 @yn-margin-xl 0 @yn-margin-s;
  }
  .head-divider {
    height: @rem16;
    margin: 0 @yn-padding-s 0 0;
  }
  .icon-shuaxin {
    padding: 0;
  }
  .cell-operation-btns {
    color: @yn-label-color;
  }
  /deep/.ant-table-row-cell-break-word {
    padding: 1px @rem16;
  }
}
</style>
