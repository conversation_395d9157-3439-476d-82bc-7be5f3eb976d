<template>
  <div class="subject-setting">
    <yn-spin :spinning="spinning" :scroll="{ x: 800 }" size="large">
      <div class="setting-title">
        抵销规则明细
        <template v-if="currentPageStatus === 'detail'">
          <yn-input-number
            v-model="accuracyValue"
            addonBefore="小数位数"
            :min="0"
            :max="9"
            :step="1"
            :precision="0"
            class="set-accuracy"
            @blur="setPrecision"
          />
          <span class="label-accuracy">小数位数：</span>
          <yn-divider class="divider" type="vertical" />
          <svg-icon
            class="icon-btn refresh-icon"
            type="icon-shuaxin"
            title="刷新"
            @click="$emit('handleRefreshData')"
          />
        </template>
      </div>

      <div v-if="isShowSearch" class="search-conditions">
        <template v-for="(condition, listIndex) in searchConditionList">
          <yn-tag
            v-for="(filterItem, itemIndex) in condition.searchList"
            :key="filterItem"
            closable
            @close="clearSearchItem(itemIndex, listIndex)"
          >
            {{ `${$data.$subjectName[condition.tag]}：${filterItem}` }}
          </yn-tag>
        </template>
        <yn-button type="text" class="condition-none" @click="allClear">
          全部清除
        </yn-button>
      </div>
      <yn-table
        key="objectId"
        bordered
        class="subject-table"
        :columns="columns"
        :data-source="
          currentPageStatus === 'detail' ? detailTableData : tableData
        "
        rowKey="objectId"
      >
        <div style="clear: both;height:1px;"></div>
        <span slot="table.raMemberTitle" class="has-star">抵销科目</span>
        <span slot="table.offsetMethodTitle" class="has-star">抵销方式</span>
        <span slot="table.offsetLimitTitle">抵销限额</span>
        <span slot="table.assetDifferenceTitle">资产/费用差异科目</span>
        <span slot="table.liabilityDifferenceTitle">负债/收入差异科目</span>

        <!-- 抵销科目 -->
        <div
          slot="table.raMemberSubject"
          slot-scope="text, record, index"
          class="td-item valid-prop"
        >
          <span v-if="currentPageStatus === 'detail'" class="td-text">
            {{ record.raMemberName }}
          </span>
          <!-- <yn-select-tree
            v-else
            v-model="record.raMemberId"
            :class="[
              'select-table',
              !record.raMemberId && record.errorTips ? 'valid-error-border' : ''
            ]"
            showSearch
            searchMode="custom"
            :allowClear="false"
            :nonleafselectable="false"
            placeholder="请选择"
            :datasource="
              searching_raMemberId ? searchResult_raMemberId : raData
            "
            @customSearch="onCustomeSearch($event, 'raMemberId')"
            @change="onChange($event, index, 'raMemberId')"
            @dropdownVisibleChange="
              handleDropdownVisibleChange($event, 'raMemberId')
            "
          /> -->
          <AsynSelectDimMember
            v-else
            :class="[
              'select-table',
              !record.raMemberId && record.errorTips ? 'valid-error-border' : ''
            ]"
            forceRender
            :value="record.raMemberId"
            dimCode="RA"
            searchMode="single"
            :allowClear="false"
            :needFilterShared="true"
            :nonleafselectable="false"
            :isSetDefaultVal="false"
            :runChangeVal="false"
            @changeVal="
              (e, info, bool) => onChange(e, index, 'raMemberId', bool)
            "
          />
          <span
            v-show="!record.raMemberId && record.errorTips"
            class="invalid-tip-text"
          >
            请选择
          </span>
        </div>
        <!-- 抵销方式 -->
        <div
          slot="table.offsetMethodSubject"
          slot-scope="text, record, index"
          class="td-item valid-prop"
        >
          <span v-if="currentPageStatus === 'detail'" class="td-text">
            {{ record.offsetMethod }}
          </span>
          <yn-select-tree
            v-else
            :value="record.offsetMethod"
            :class="[
              'select-table',
              !record.offsetMethod && record.errorTips
                ? 'valid-error-border'
                : ''
            ]"
            searchMode="custom"
            :allowClear="false"
            :nonleafselectable="true"
            placeholder="请选择"
            :datasource="offsetMethodList"
            @change="onChange($event, index, 'offsetMethod')"
          />
          <span
            v-show="!record.offsetMethod && record.errorTips"
            class="invalid-tip-text"
          >
            请选择
          </span>
        </div>
        <!-- 抵销限额 -->
        <div
          slot="table.offsetLimitSubject"
          slot-scope="text, record"
          class="td-item valid-prop"
        >
          <span v-if="currentPageStatus === 'detail'" class="td-text txt-money">
            {{ record.offsetLimit | toFinance(accuracy) }}
          </span>
          <yn-input
            v-else
            v-model="record.offsetLimit"
            placeholder="请输入"
            :class="[
              record.offsetLimit && !regOffsetLimit.test(record.offsetLimit)
                ? 'valid-error-border'
                : ''
            ]"
            @change="handleChangeOffsetLimit($event, record, 'offsetLimit')"
            @focus="handleChangeOffsetLimit($event, record, 'offsetLimit')"
            @blur="handleBlurOffsetLimit($event, record, 'offsetLimit')"
          />
          <span
            v-show="
              record.offsetLimit && !regOffsetLimit.test(record.offsetLimit)
            "
            class="invalid-tip-text"
          >
            请输入数值
          </span>
        </div>
        <!-- 资产差异科目 -->
        <div
          slot="table.assetDifferenceSubject"
          slot-scope="text, record, index"
          class="td-item"
        >
          <span v-if="currentPageStatus === 'detail'" class="td-text">
            {{ record.assetDifferenceName }}
          </span>
          <yn-select-tree
            v-else
            v-model="record.assetDifference"
            :class="['select-table']"
            showSearch
            searchMode="single"
            :allowClear="true"
            :nonleafselectable="false"
            placeholder="请选择"
            :datasource="
              searching_assetDifference
                ? searchResult_assetDifference
                : assetsExpenseAccount
            "
            @change="onChange($event, index, 'assetDifference')"
            @dropdownVisibleChange="
              handleDropdownVisibleChange($event, 'assetDifference')
            "
          />
          <!-- @customSearch="onCustomeSearch($event, 'assetDifference')" -->

          <!-- <AsynSelectDimMember
            v-else
            :class="['select-table']"
            forceRender
            :value="record.assetDifference"
            dimCode="Account"
            searchMode="single"
            :allowClear="true"
            :needFilterShared="true"
            :nonleafselectable="false"
            :isSetDefaultVal="false"
            @changeVal="onChange($event, index, 'assetDifference')"
          /> -->
        </div>
        <!-- 负债差异科目 -->
        <div
          slot="table.liabilityDifferenceSubject"
          slot-scope="text, record, index"
          class="td-item"
        >
          <span v-if="currentPageStatus === 'detail'" class="td-text">
            {{ record.liabilityDifferenceName }}
          </span>
          <yn-select-tree
            v-else
            v-model="record.liabilityDifference"
            :class="['select-table']"
            showSearch
            searchMode="single"
            :allowClear="true"
            :nonleafselectable="false"
            placeholder="请选择"
            :datasource="
              searching_liabilityDifference
                ? searchResult_liabilityDifference
                : liabilitiesIncomeAccount
            "
            @change="onChange($event, index, 'liabilityDifference')"
            @dropdownVisibleChange="
              handleDropdownVisibleChange($event, 'liabilityDifference')
            "
          />
          <!-- @customSearch="onCustomeSearch($event, 'liabilityDifference')" -->

          <!-- <AsynSelectDimMember
            v-else
            :class="['select-table']"
            forceRender
            :value="record.liabilityDifference"
            dimCode="Account"
            searchMode="single"
            :allowClear="true"
            :needFilterShared="true"
            :nonleafselectable="false"
            :isSetDefaultVal="false"
            @changeVal="onChange($event, index, 'liabilityDifference')"
          /> -->
        </div>

        <!-- 搜索 抵销科目 -->
        <div
          :slot="
            currentPageStatus === 'detail'
              ? 'table.filterDropdownRaMemberIdSubject'
              : ''
          "
          slot-scope="{ confirm, clearFilters }"
        >
          <table-search
            ref="raMemberIdSubject"
            :dataSource="searchConditionList[0].dataSource"
            :clearFilters="clearFilters"
            @handSearch="
              chooseList =>
                handFilterSearch(confirm, chooseList, 'raMemberIdSubject')
            "
          />
        </div>
        <!-- 搜索 抵销方式 -->
        <div
          :slot="
            currentPageStatus === 'detail'
              ? 'table.filterDropdownMethodSubject'
              : ''
          "
          slot-scope="{ confirm, clearFilters }"
        >
          <table-search
            ref="offsetMethodSubject"
            :dataSource="searchConditionList[1].dataSource"
            :clearFilters="clearFilters"
            @handSearch="
              chooseList =>
                handFilterSearch(confirm, chooseList, 'offsetMethodSubject')
            "
          />
        </div>
        <!-- 搜索 资产差异科目 -->
        <template
          :slot="
            currentPageStatus === 'detail'
              ? 'table.filterDropdownAssetDifferenceSubject'
              : ''
          "
          slot-scope="{ confirm, clearFilters }"
        >
          <table-search
            ref="assetDifferenceSubject"
            :dataSource="searchConditionList[2].dataSource"
            :clearFilters="clearFilters"
            @handSearch="
              chooseList =>
                handFilterSearch(confirm, chooseList, 'assetDifferenceSubject')
            "
          />
        </template>
        <!-- 搜索 负债差异科目 -->
        <template
          :slot="
            currentPageStatus === 'detail'
              ? 'table.filterDropdownLiabilityDifferenceSubject'
              : ''
          "
          slot-scope="{ confirm, clearFilters }"
        >
          <table-search
            ref="liabilityDifferenceSubject"
            :dataSource="searchConditionList[3].dataSource"
            :clearFilters="clearFilters"
            @handSearch="
              chooseList =>
                handFilterSearch(
                  confirm,
                  chooseList,
                  'liabilityDifferenceSubject'
                )
            "
          />
        </template>
        <template slot="table.operation" slot-scope="text, record, index">
          <a
            :disabled="tableData.length === 1"
            class="delete-text"
            href="javascript:;"
            @click="deleteRecord(index)"
          >
            删除
          </a>
        </template>
      </yn-table>
      <yn-pagination
        v-show="currentPageStatus === 'detail'"
        v-model="pageOptions.currentPage"
        :scroll="{ y: 564 }"
        class="currentoffset-pagination"
        :total="pageOptions.total"
        :showTotal="total => `总计${total}条`"
        hideOnSinglePage
        showSizeChanger
        showQuickJumper
        :pageSize.sync="pageOptions.currentPageSize"
        :pageSizeOptions="pageOptions.pageSizeOptions"
        @change="handleLocalSearch"
        @showSizeChange="handleLocalSearch"
      />
      <span
        v-show="tableData.length === 0 && currentPageStatus !== 'detail'"
        class="no-data"
      >
        请添加科目
      </span>
      <yn-button
        v-show="currentPageStatus !== 'detail'"
        type="dashed"
        block
        class="btn-add-item"
        @click="handleAdd"
      >
        <yn-icon-svg type="add" />&emsp;添加
      </yn-button>
    </yn-spin>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-pagination/";
import "yn-p1/libs/components/yn-input-number/";
import "yn-p1/libs/components/yn-tag/";
import "yn-p1/libs/components/yn-button/";
import { dealNumberFocusChange, dealNumberBlurInput } from "@/utils/common";
import TableSearch from "@/components/hoc/tableFilterSearch";
import { mapState } from "vuex";
import cloneDeep from "lodash/cloneDeep";
import commonService from "@/services/common";
import AsynSelectDimMember from "../../../components/hoc/asynSelectDimMember";
export default {
  components: { TableSearch, AsynSelectDimMember },
  props: {
    itemList: {
      type: Array,
      default: () => []
    },
    currentPageStatus: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      pageOptions: {
        total: 0,
        currentPage: 1,
        currentPageSize: 10,
        pageSizeOptions: ["10", "20", "50"]
      },
      columns: [
        {
          title: "序号",
          dataIndex: "sortId",
          key: "sortId",
          width: 70,
          customRender: (text, row, index) => {
            const sortIndex = "sort-index";
            return <span class={sortIndex}>{index + 1}</span>;
          }
        },
        {
          dataIndex: "raMemberId", // 抵销科目 raMemberId
          key: "raMemberId",
          filtered: false,
          width: 200,
          scopedSlots: {
            customRender: "raMemberSubject",
            title: "raMemberTitle",
            filterIcon: "filterIcon",
            filterDropdown: "filterDropdownRaMemberIdSubject"
          }
        },
        {
          dataIndex: "offsetMethod", // 抵销方式 offsetMethod
          key: "offsetMethod",
          filtered: false,
          width: 200,
          scopedSlots: {
            customRender: "offsetMethodSubject",
            title: "offsetMethodTitle",
            filterIcon: "filterIcon",
            filterDropdown: "filterDropdownMethodSubject"
          }
        },
        {
          dataIndex: "offsetLimit", // 抵销限额 offsetLimit
          key: "offsetLimit",
          filtered: false,
          width: 200,
          align: "right",
          scopedSlots: {
            customRender: "offsetLimitSubject",
            title: "offsetLimitTitle"
          }
        },
        {
          dataIndex: "assetDifference", // 资产差异科目 assetDifference
          key: "assetDifference",
          filtered: false,
          ellipsis: true,
          scopedSlots: {
            customRender: "assetDifferenceSubject",
            title: "assetDifferenceTitle",
            filterIcon: "filterIcon",
            filterDropdown: "filterDropdownAssetDifferenceSubject"
          }
        },
        {
          dataIndex: "liabilityDifference", // 负债差异科目 liabilityDifference
          key: "liabilityDifference",
          filtered: false,
          ellipsis: true,
          scopedSlots: {
            customRender: "liabilityDifferenceSubject",
            title: "liabilityDifferenceTitle",
            filterIcon: "filterIcon",
            filterDropdown: "filterDropdownLiabilityDifferenceSubject"
          }
        }
      ],
      $filterIconColumns: {
        raMemberIdSubject: 1,
        offsetMethodSubject: 2,
        assetDifferenceSubject: 4,
        liabilityDifferenceSubject: 5
      },
      tableData: [
        {
          key: 0,
          sortId: 0,
          objectId: 0,
          raMemberId: "",
          raMemberDbCode: "",
          offsetMethod: "",
          offsetLimit: "",
          assetDifference: "",
          liabilityDifference: ""
        }
      ],
      cloneTableData: [],
      detailTableData: [], // 详情态下的列表数据展示（本地分页后的数据）

      $subjectName: {
        raMemberIdSubject: "抵销科目",
        offsetMethodSubject: "抵销方式",
        assetDifferenceSubject: "资产差异科目",
        liabilityDifferenceSubject: "负债差异科目"
      },
      offsetMethodList: [
        { key: "抵大", label: "抵大" },
        { key: "抵小", label: "抵小" },
        { key: "按科目类别抵销", label: "按科目类别抵销" }
      ],
      searchConditionList: [
        {
          tag: "raMemberIdSubject",
          searchList: [],
          dataSource: []
        },
        {
          tag: "offsetMethodSubject",
          searchList: [],
          dataSource: []
        },
        {
          tag: "assetDifferenceSubject",
          searchList: [],
          dataSource: []
        },
        {
          tag: "liabilityDifferenceSubject",
          searchList: [],
          dataSource: []
        }
      ],
      accuracy: 2, // 详情页 精度位数，默认2
      accuracyValue: 2,
      regOffsetLimit: /^(-?\d+)(\.\d+)?$/, // 限制额度的匹配规则
      spinning: false, // 加载
      isShowSearch: false,
      getPaveAccountData: "",
      getPaveRAData: "",
      searching_liabilityDifference: false,
      searchResult_liabilityDifference: [],
      searching_raMemberId: false,
      searchResult_raMemberId: [],
      searching_assetDifference: false,
      searchResult_assetDifference: []
    };
  },
  computed: {
    ...mapState("currentoffset", {
      pageStatus: state => state.pageStatus,
      accountData: state => state.accountData,
      assetsExpenseAccount: state => state.assetsExpenseAccount,
      liabilitiesIncomeAccount: state => state.liabilitiesIncomeAccount,
      paveAccountData: state => state.paveAccountData,
      raData: state => state.raData,
      paveRAData: state => state.paveRAData
    })
  },
  watch: {
    searchConditionList: {
      deep: true,
      immediate: true,
      handler(nv, ov) {
        this.isShowSearch = nv
          .map(item => item.searchList.length)
          .reduce((pre, next) => pre + next, 0);
        if (!this.isShowSearch) {
          this.cloneTableData = this.tableData;
        } else {
          this.cloneTableData = this.tableData.filter(item => {
            let flag = true;
            flag =
              flag &&
              (nv[0].searchList.length === 0
                ? true
                : nv[0].searchList.includes(item.raMemberName)) &&
              (nv[1].searchList.length === 0
                ? true
                : nv[1].searchList.includes(item.offsetMethod)) &&
              (nv[2].searchList.length === 0
                ? true
                : nv[2].searchList.includes(
                  item.assetDifferenceName || "空"
                )) &&
              (nv[3].searchList.length === 0
                ? true
                : nv[3].searchList.includes(
                  item.liabilityDifferenceName || "空"
                ));
            return flag;
          });
        }
        this.fetchLocalListData();
      }
    },
    paveAccountData: {
      immediate: true,
      handler(nv) {
        this.getPaveAccountData = nv;
      }
    },

    paveRAData: {
      immediate: true,
      handler(nv) {
        this.getPaveRAData = nv;
      }
    },

    currentPageStatus: {
      immediate: true,
      handler(nv) {
        if (nv !== "detail") {
          if (this.isShowSearch) {
            // 编辑态下清除查询条件。
            this.allClear();
          }
          this.columns.push({
            title: "操作",
            key: "operation",
            width: 128,
            scopedSlots: {
              customRender: "operation"
            }
          });
        } else {
          this.columns[this.columns.length - 1].key === "operation" &&
            this.columns.splice(this.columns.length - 1, 1);
        }
      }
    },
    itemList: {
      handler() {
        this.initData();
      }
    }
  },

  async created() {
    this.getPrecision();
    await this.initData();
    this.getSubjectParams();
  },

  methods: {
    getPrecision() {
      commonService("selectUserScale", "currentOffset").then(res => {
        this._precision = res.data.data;
        this.handleSetAccuracy(this._precision.decimalPlaces);
      });
    },
    setPrecision() {
      this.accuracy = this.accuracyValue || 0;
      commonService("saveOrUpdateUserScale", {
        decimalPlaces: this.accuracyValue || 0,
        objectId: this._precision.objectId,
        pageName: "currentOffset"
      });
    },
    handleSetAccuracy(value) {
      if (value > 9) {
        this.accuracyValue = 9;
      } else if (value < 0 || !value) {
        this.accuracyValue = 0;
      } else {
        this.accuracyValue = value;
      }
      this.accuracy = this.accuracyValue;
    },
    initData() {
      if (this.currentPageStatus === "newAdd") return;
      this.tableData = this.itemList.map(item => ({
        ...item,
        key: item.raMemberId
      }));
      this.cloneTableData = cloneDeep(this.tableData);

      // 初始化列搜索内容
      const _arr_raMemberName = Array.from(
        new Set(
          this.cloneTableData.map(item => {
            return item.raMemberName;
          })
        )
      );
      const _arr_offsetMethod = Array.from(
        new Set(
          this.cloneTableData.map(item => {
            return item.offsetMethod;
          })
        )
      );
      const _arr_assetDifferenceName = Array.from(
        new Set(
          this.cloneTableData.map(item => {
            return item.assetDifferenceName || "空";
          })
        )
      );
      const _arr_liabilityDifferenceName = Array.from(
        new Set(
          this.cloneTableData.map(item => {
            return item.liabilityDifferenceName || "空";
          })
        )
      );
      this.searchConditionList[0].dataSource = cloneDeep(_arr_raMemberName);
      this.searchConditionList[1].dataSource = cloneDeep(_arr_offsetMethod);
      this.searchConditionList[2].dataSource = cloneDeep(
        _arr_assetDifferenceName
      );
      this.searchConditionList[3].dataSource = cloneDeep(
        _arr_liabilityDifferenceName
      );
    },

    // 获取本地分页数据
    fetchLocalListData() {
      this.pageOptions.total = this.cloneTableData.length;
      if (this.cloneTableData.length === 0) {
        this.detailTableData = [];
      } else if (
        this.cloneTableData.length < this.pageOptions.currentPageSize
      ) {
        this.detailTableData = JSON.parse(JSON.stringify(this.cloneTableData));
      } else {
        const temp = JSON.parse(JSON.stringify(this.cloneTableData));
        const skipNum =
          (this.pageOptions.currentPage - 1) * this.pageOptions.currentPageSize;
        this.detailTableData = temp.slice(
          skipNum,
          skipNum + this.pageOptions.currentPageSize
        );
      }
    },

    // 获取科目维度成员
    // 科目校验，抵销科目不允许重复
    // 抵销方式选抵大/抵小时，资产/负债差异科目必填；抵销方式选按科目类别抵销时，资产/负债差异科目只能一列有值
    addErrorToData() {
      const len = this.tableData.length;
      const lastItem = this.tableData[len - 1];
      if (lastItem && (!lastItem.raMemberId || !lastItem.offsetMethod)) {
        this.$set(lastItem, "errorTips", "error-tips");
        return true;
      }
      return false;
    },

    getSubjectParams() {
      const p = this.tableData.map(item => ({
        rowKey: item.raMemberId,
        raMemberId: item.raMemberId,
        raMemberDbCode: item.raMemberDbCode,
        offsetMethod: item.offsetMethod,
        offsetLimit: item.offsetLimit,
        assetDifference: item.assetDifference,
        liabilityDifference: item.liabilityDifference
      }));
      return [...p];
    },

    // 处理本地分页
    handleLocalSearch() {
      this.fetchLocalListData();
    },

    // type 搜索的列名称 抵销科目：raMemberId  资产差异：assetDifference 负债差异：liabilityDifference
    // 抵销科目对应的code赋值
    onChange(e, index, type, bool = true) {
      if (!bool) return;
      const getItemBykey = (data, key) => {
        let list = [...data];
        while (list.length) {
          const currentItem = list.shift();
          if (currentItem.key === key) {
            return currentItem;
          }
          if (currentItem.children && currentItem.children.length) {
            list = [...list, ...currentItem.children];
          }
        }
      };
      if (type === "raMemberId") {
        const item = getItemBykey(this.raData, e);
        this.$set(this.tableData[index], "raMemberDbCode", item.value);
        this.$set(this.tableData[index], "raMemberId", item.id);
      } else {
        this.$set(this.tableData[index], type, e);
      }
      this.getSubjectParams();
      this.$emit("addSaveCb");
    },

    handleChangeOffsetLimit(e, record, prop) {
      const { value } = e.target;
      e.target.focus();
      this.$set(record, prop, value.replace(/,/g, ""));
      dealNumberFocusChange(e, record, prop, this, "currentOffset");
      this.$emit("addSaveCb");
    },

    handleBlurOffsetLimit(e, record, prop) {
      dealNumberBlurInput(e, record, prop, this, "currentOffset");
    },

    handleAdd() {
      const len = this.tableData.length;
      if (this.addErrorToData()) return;
      this.tableData.push({
        key: len + 1,
        sortId: len + 1,
        objectId: len + 1,
        raMemberId: "",
        raMemberDbCode: "",
        offsetMethod: "",
        offsetLimit: "",
        assetDifference: "",
        liabilityDifference: ""
      });
    },

    deleteRecord(i) {
      this.tableData.splice(i, 1);
      this.$emit("addSaveCb");
    },

    handFilterSearch(confirm, chooseList, type) {
      confirm({ closeDropdown: true });
      const idx = this.searchConditionList.findIndex(v => {
        return v.tag === type;
      });
      this.searchConditionList[idx].searchList = [...chooseList];
      const showFilter = !!(
        this.searchConditionList[idx].searchList &&
        this.searchConditionList[idx].searchList.length
      );
      // this.columns[idx + 1].filtered = showFilter;
      this.columns[
        this.$data.$filterIconColumns[this.searchConditionList[idx].tag]
      ].filtered = showFilter;
    },

    clearSearchItem(itemIndex, listIndex) {
      const deleteSearchObj = this.searchConditionList[listIndex];
      deleteSearchObj.searchList.splice(itemIndex, 1);
      this.$refs[deleteSearchObj.tag] &&
        this.$refs[deleteSearchObj.tag].setSelectedItems(
          deleteSearchObj.searchList
        );
      if (deleteSearchObj.searchList.length === 0) {
        this.columns[
          this.$data.$filterIconColumns[deleteSearchObj.tag]
        ].filtered = false;
      }
    },

    allClear() {
      this.searchConditionList.forEach((item, index) => {
        item.searchList.splice(0);
        this.columns[index + 1].filtered = false;
        this.$refs[item.tag] && this.$refs[item.tag].clearSelectedItems();
      });
    },

    // colName 搜索的列名称 抵销科目：raMemberId  资产差异：assetDifference 负债差异：liabilityDifference
    // 数据源：资产差异|负债差异-> accountData   抵销科目-> raData
    onCustomeSearch(searchObj, colName) {
      const { searchValue } = searchObj;
      this.$set(this, `searching_${colName}`, true);
      const res = [];
      const loop = arr => {
        arr.map(item => {
          if (item.name.includes(searchValue)) {
            const tarItem = JSON.parse(JSON.stringify(item));
            tarItem.children = [];
            res.push(item);
          }
          if (item.children && item.children.length) {
            loop(item.children);
          }
        });
      };
      if (colName === "assetDifference" || colName === "liabilityDifference") {
        loop(this.accountData);
      } else {
        // raMemberId
        loop(this.raData);
      }
      this.$set(this, `searchResult_${colName}`, [...res]);
    },

    handleDropdownVisibleChange(bool, colName) {
      if (!bool) {
        this.$set(this, `searching_${colName}`, false);
        this.$set(this, `searchResult_${colName}`, []);
      }
    }
  }
};
</script>

<style scoped lang="less">
@import "../../../commonLess/common.less";

.error-tips {
  border: 1px solid @yn-error-color;
}
.subject-setting {
  width: 100%;
  margin-top: @yn-margin-xxl;
  .setting-title {
    font-size: @rem14;
    color: @yn-heading-color;
    font-weight: 600;
    margin-bottom: @yn-margin-l;
    .set-accuracy {
      float: right;
      font-weight: normal;
    }
    .label-accuracy {
      float: right;
      font-weight: normal;
      line-height: @rem34;
    }
    .refresh-icon {
      float: right;
      cursor: pointer;
      font-size: @rem16;
    }
    .divider {
      float: right;
      margin-top: @rem6;
      height: @rem20;
    }
  }
  .search-conditions {
    margin-bottom: 0.5rem;
    /deep/.ant-tag {
      background-color: rgb(235, 238, 244);
      color: @yn-text-color-secondary;
    }
  }
  .subject-table {
    /deep/.ant-table-tbody > tr > td {
      padding: 0;
    }
    .td-item {
      position: relative;
      width: 100%;
      .td-text {
        display: inline-block;
        height: @rem16;
        width: 100%;
        padding-left: @yn-padding-xl;
        line-height: @rem16;
        &.txt-money {
          text-align: right;
          padding-right: @rem6;
        }
      }

      /deep/.yn-select-tree {
        border: none;
      }
      /deep/ .ant-input {
        border: none;
      }
    }
    .select-table {
      position: relative;
      width: 100%;
      /deep/.ant-select-selection {
        border: none;
      }
    }
    .delete-text {
      margin-left: @yn-margin-xl;
    }
    .condition-none {
      // float: right;
      margin: 0 auto;
      cursor: pointer;
      font-size: @rem12;
      color: @yn-chart-1;
      line-height: @rem32;
      margin-right: @rem8;
    }
  }
  .no-data {
    color: @yn-error-color;
  }
  .footer-btn {
    width: 100%;
    height: @rem36;
    background: @yn-component-background;
    border: 1px dashed rgba(225, 229, 235, 1);
    margin-top: @yn-margin-l;
    line-height: @rem36;
    text-align: center;
    cursor: pointer;
  }
  .has-star::after {
    display: inline-block;
    margin-left: @yn-margin-xs;
    color: @yn-error-color;
    font-size: @rem14;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
  }
  .sort-index {
    display: inline-block;
    height: @rem32;
    line-height: @rem32;
    width: 100%;
    margin-left: @yn-margin-xl;
  }
  .currentoffset-pagination {
    height: 2.75rem;
    line-height: 2.75rem;
    margin-top: @yn-margin-xl;
    text-align: right;
    /deep/.ant-pagination-options-quick-jumper {
      margin-top: @rem6;
    }
  }
  .btn-add-item {
    margin-top: @yn-margin-l;
    .ynicon-add {
      margin-right: @rem4;
    }
  }
}
</style>
