<template>
  <div class="currentoffset-detail">
    <yn-page-title
      v-if="currentPageStatus === 'newAdd'"
      title="新增往来抵销规则"
    />
    <!-- 编辑或者查看详情的时候 -->
    <yn-page-title
      v-else
      :key="offsetId"
      :title="offsetName"
      class="edit-title"
    >
      <template v-slot:extraRender>
        <div v-if="editName" class="edit-name">
          <yn-input
            ref="editNameInputRef"
            v-model="offsetName"
            class="head-input"
            :allowClear="false"
            @blur="handleCloseInput"
          />
        </div>
        <svg-icon
          v-show="currentPageStatus === 'edit'"
          type="icon-edit"
          class="edit-pan"
          title="编辑"
          @click="handleEditName"
        />
        <yn-button
          v-show="currentPageStatus === 'detail'"
          type="primary"
          class="edit-btn"
          @click="handleEdit"
        >
          编辑
        </yn-button>
      </template>
    </yn-page-title>
    <div
      :class="[
        'detail-container',
        'cs-body-headerComponent',
        currentPageStatus === 'detail'
          ? 'detail-container-info'
          : 'detail-container-edit'
      ]"
    >
      <!-- 新增时填写名称 -->
      <div v-show="currentPageStatus === 'newAdd'" class="container-base-info">
        <div class="head-info-title">基础信息</div>
        <yn-row>
          <yn-col :span="8">
            <yn-col :span="8" class="info-title">
              <span class="has-star">规则名称</span>
            </yn-col>
            <yn-col :span="16">
              <yn-input
                v-model="offsetName"
                :class="[!offsetName && showTips ? 'error-tips' : '']"
                placeholder="请输入"
                @change="inputChange"
                @blur="handleCloseInput"
              />
              <span v-show="isLongName" class="err-tip">
                字符长度超过64位限制，请检查
              </span>
              <span v-if="isOffsetNameRepeat" class="err-tip">
                规则名称重复
              </span>
              <span v-else-if="!offsetName && showTips" class="err-tip">
                请输入
              </span>
            </yn-col>
          </yn-col>
        </yn-row>
      </div>
      <!-- 适配范围 -->
      <AdaptationRange
        ref="adaptationRangeRef"
        :currentPageStatus="currentPageStatus"
        :offsetInfo.sync="offsetInfo"
        @addSaveCb="addSaveCb"
      />
      <!-- 科目设置 -->
      <SubjectSetting
        ref="subjectSettingRef"
        :currentPageStatus="currentPageStatus"
        :itemList="itemList"
        @addSaveCb="addSaveCb"
        @handleRefreshData="handleRefresh"
      />
    </div>
    <div
      v-show="currentPageStatus === 'newAdd' || currentPageStatus === 'edit'"
      class="detail-footer"
    >
      <yn-button class="footer-btn" @click="handleCancel">取消</yn-button>
      <yn-button
        :loading="btnLoading"
        class="footer-btn"
        type="primary"
        @mousedown="handleSave"
      >
        保存
      </yn-button>
    </div>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-row/";
import "yn-p1/libs/components/yn-col/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-button/";
import AdaptationRange from "./adaptationRange.vue";
import SubjectSetting from "./subjectSetting.vue";
import "yn-p1/libs/components/yn-page-title/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
import { mapState, mapMutations, mapActions } from "vuex";
import currentoffsetService from "@/services/currentoffset";

const warnTitle = "您要保存对“往来抵销规则”所做的更改吗";
export default {
  components: { AdaptationRange, SubjectSetting },
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      offsetId: null, // 唯一标识ID
      isOffsetNameRepeat: false, // 规则名称是否重复
      isLongName: false, // 规则名称是否超长
      offsetName: "",
      cloneoffsetName: "",
      isEditOffsetNameExcLength: false, // 编辑状态下名称是否超出长度
      offsetInfo: {},
      itemList: [],
      showTips: false,
      editName: false,
      btnLoading: false,
      currentPage: false,
      afterAddInfo: "",
      currentPageStatus: "",
      regOffsetLimit: /^(-?\d+)(\.\d+)?$/, // 限制额度的匹配规则
      directSave: false // 是否直接保存
    };
  },
  computed: {
    ...mapState("currentoffset", {
      pageStatus: state => state.pageStatus
    })
  },
  watch: {
    pageStatus: {
      deep: true,
      immediate: true,
      handler(nv) {
        // 新版本的导航 不需要走这里面在created 里面处理
        if (!this.selfTab) {
          const {
            status,
            offsetId,
            offsetName,
            id,
            name
          } = this.getTabParamsMixin();
          this.currentPageStatus = status;
          this.offsetId = offsetId || id;
          this.offsetName = offsetName || name || "";
          return;
        }
        this.currentPageStatus = nv[this.params && this.params.id];
      }
    }
  },
  async created() {
    if (this.selfTab) {
      const { offsetId, status, id } = this.params ? this.params.params : {};
      this.offsetId = offsetId || id;
      this.currentPageStatus = status;
    } else {
      await this.getAccountData();
      await this.getRAData();
    }
    this.initData();
  },
  methods: {
    ...mapMutations({
      updatePageStaus: "currentoffset/updatePageStaus",
      updateTabTitle: "common/updateTabTitle"
    }),
    ...mapActions({
      getAccountData: "currentoffset/getAccountData",
      getRAData: "currentoffset/getRAData"
    }),
    handleTransferEhcoData(list) {
      return list.map(item => ({
        dimMemberId: item.objectId,
        dimMemberDbCode: item.dimMemberDbCode,
        dimMemberName: item.dimMemberName,
        dimCode: "",
        key: `${item.objectId}-self`,
        label: item.dimMemberName,
        memberType: 1,
        memberTypeValue: "self",
        objectId: `${item.objectId}-self`,
        shardim: "",
        title: `成员(${item.dimMemberName})`,
        type: "member",
        value: item.objectId,
        dimMemberCode: item.dimMemberCode
      }));
    },
    initData(data, callback) {
      if (this.currentPageStatus === "newAdd") return;
      const offsetId =
        (this.params && this.params.params.offsetId) || this.offsetId;
      const _offsetName = this.params && this.params.params.offsetName;
      if (!offsetId) return;
      currentoffsetService("getCurrentOffsetDetail", offsetId).then(res => {
        const {
          offsetName,
          periodList,
          scopeList,
          versionList,
          yearList,
          currentOffsetDetailVoList
        } = res.data;
        this.offsetName = this.cloneoffsetName =
          offsetName || _offsetName || "";
        this.updateTabTitle({
          id: offsetId,
          title: this.offsetName
        });
        this.offsetInfo = {
          versionList: this.handleTransferEhcoData(versionList),
          yearList: this.handleTransferEhcoData(yearList),
          periodList: this.handleTransferEhcoData(periodList),
          scopeList: this.handleTransferEhcoData(scopeList)
        };
        this.itemList = currentOffsetDetailVoList;
        callback && callback();
      });
    },

    handleRefresh() {
      this.$refs.subjectSettingRef.spinning = true;
      currentoffsetService("getCurrentOffsetDetail", this.offsetId).then(
        res => {
          const {
            periodList,
            scopeList,
            versionList,
            yearList,
            currentOffsetDetailVoList
          } = res.data;
          this.offsetInfo = {
            versionList: this.handleTransferEhcoData(versionList),
            yearList: this.handleTransferEhcoData(yearList),
            periodList: this.handleTransferEhcoData(periodList),
            scopeList: this.handleTransferEhcoData(scopeList)
          };
          this.$refs.subjectSettingRef.spinning = false;
          this.itemList = currentOffsetDetailVoList;
        }
      );
    },

    handleEditName() {
      this.editName = true;
      this.isEditOffsetNameExcLength = false;
      this.$nextTick(() => {
        this.$refs.editNameInputRef && this.$refs.editNameInputRef.focus();
      });
    },

    handleCloseInput() {
      this.editName = false;
      if (this.offsetName === this.cloneoffsetName) {
        return;
      }
      this.isLongName = this.offsetName.length > 64;
      if (this.currentPageStatus === "edit" && this.offsetName.length > 64) {
        UiUtils.errorMessage("字符长度超过64位限制，请检查");
        this.offsetName = this.cloneoffsetName;
        return;
      }
      this.addSaveEventCb(this.closeNewAddTab);
      if (this.directSave) return;
      currentoffsetService("checkCurrentOffsetName", {
        offsetName: this.offsetName
      }).then(res => {
        if (res.data) {
          if (this.currentPageStatus === "edit") {
            UiUtils.errorMessage("当前规则名称已被使用，请修改");
            this.offsetName = this.cloneoffsetName;
            this.isOffsetNameRepeat = false;
          } else {
            this.isOffsetNameRepeat = true;
          }
        } else {
          this.isOffsetNameRepeat = false;
        }
      });
    },

    handleEdit() {
      this.currentPage = true;
      let tabId = this.params && this.params.id;
      if (!this.selfTab) {
        tabId = UrlUtils.getQuery("inTab");
        const { offsetId, offsetName, id, name } = this.getTabParamsMixin();
        this.setTabParamsMixin(tabId, {
          status: "edit",
          offsetId: offsetId || id,
          offsetName: offsetName || name
        });
      }
      this.updatePageStaus({
        key: tabId,
        status: "edit"
      });
    },

    handleCancel() {
      let tabId = this.params && this.params.id;
      if (!this.selfTab) {
        tabId = UrlUtils.getQuery("inTab");
        const { offsetId, offsetName } = this.getTabParamsMixin();
        this.setTabParamsMixin(tabId, {
          status: "detail",
          offsetId,
          offsetName
        });
      }
      if (this.currentPage) {
        this.savePromptMixin().then(res => {
          this.updatePageStaus({
            key: tabId,
            status: "detail"
          });
          if (res && res.status === "notSave") {
            this.offsetName = this.cloneoffsetName;
          }
          this.$refs.adaptationRangeRef.initData();
          this.$refs.subjectSettingRef.initData();
        });
      } else {
        this.savePromptMixin().then(() => {
          this.closetabMixin(tabId);
        });
      }
    },

    // 科目校验，抵销科目不允许重复
    // 抵销方式选抵大/抵小时，资产/负债差异科目必填；抵销方式选按科目类别抵销时，资产/负债差异科目只能一列有值
    validateSubject(list) {
      if (!list.length) {
        UiUtils.errorMessage("抵销规则明细至少要一条数据！");
        return false;
      }
      const OFFSETMETHOD = {
        B: "抵大",
        S: "抵小",
        E: "按科目类别抵销"
      };
      const raMemberIdMap = {};
      const methodBSDifferenceError = [];
      const methodEDifferenceError = [];
      const offsetLimitError = [];
      list.forEach((item, index) => {
        if (item.offsetLimit && !this.regOffsetLimit.test(item.offsetLimit)) {
          offsetLimitError.push(index + 1);
        }
        if (!raMemberIdMap[item.raMemberId]) {
          raMemberIdMap[item.raMemberId] = [index + 1];
        } else {
          raMemberIdMap[item.raMemberId].push(index + 1);
        }
        if (
          (item.offsetMethod === OFFSETMETHOD.B ||
            item.offsetMethod === OFFSETMETHOD.S) &&
          (!item.assetDifference || !item.liabilityDifference)
        ) {
          methodBSDifferenceError.push(
            `第${index + 1}行，当抵销方式选抵大/抵小时，资产/负债差异科目必填！`
          );
        } else if (
          (item.offsetMethod === OFFSETMETHOD.E &&
            item.assetDifference &&
            item.liabilityDifference) ||
          (item.offsetMethod === OFFSETMETHOD.E &&
            !item.assetDifference &&
            !item.liabilityDifference)
        ) {
          methodEDifferenceError.push(
            `第${index +
              1}行，抵销方式为按科目类别抵销时，资产/负债差异科目有且必须一列有值！`
          );
        }
      });
      const repeatError = Object.values(raMemberIdMap).some(
        item => item.length > 1
      );
      if (
        repeatError ||
        methodBSDifferenceError.length ||
        methodEDifferenceError.length ||
        offsetLimitError.length
      ) {
        let tip = "";
        const keys = Object.keys(raMemberIdMap);
        const errorKeys = keys.filter(key => {
          if (raMemberIdMap[key].length > 1) {
            return key;
          }
        });
        errorKeys.forEach((err, index) => {
          if (raMemberIdMap[err].length > 1) {
            tip += `${raMemberIdMap[err].join("、")}${
              index !== errorKeys.length - 1 ? "和" : ""
            }`;
          }
        });
        const arr_tip_error = [];
        const repeatErrorTips = repeatError ? `第${tip}行抵销科目重复` : "";
        repeatErrorTips && arr_tip_error.push(repeatErrorTips);
        methodBSDifferenceError.length &&
          arr_tip_error.push(methodBSDifferenceError.join(","));
        methodEDifferenceError.length &&
          arr_tip_error.push(methodEDifferenceError.join(","));
        offsetLimitError.length &&
          arr_tip_error.push(
            `第${offsetLimitError.join("、")}行抵销限额输入不合法！`
          );

        if (arr_tip_error && arr_tip_error.length) {
          const render = h => {
            const _arr = [];
            arr_tip_error.forEach((item, index) => {
              _arr.push(h("span", {}, item));
              if (index < arr_tip_error.length - 1) {
                _arr.push(h("br"));
              }
            });
            return _arr;
          };
          UiUtils.errorMessage(h => {
            return h(
              "div",
              {
                style: {
                  position: "relative",
                  textAlign: "left",
                  paddingLeft: "30px"
                },
                id: "errorMessage"
              },
              render(h)
            );
          }, 3);
          // 解决errorMessage错误提示多行文本图标居中的情况，待UI组件更新后替换TODO
          setTimeout(() => {
            const errorMessage = document.querySelector(
              ".ant-message-notice-content"
            );
            errorMessage.style.position = "relative";
            const anticon = document.querySelector(
              ".ant-message-notice-content .anticon.anticon-close-circle"
            );
            anticon.style.position = "absolute";
            anticon.style.top = "10px";
            anticon.style.left = "10px";
          }, 10);
        }
        return false;
      }
      return true;
    },

    async handleSave() {
      if (document.activeElement.tagName === "INPUT") {
        // 没失去焦点直接点保存
        this.directSave = true;
      }
      return new Promise((resolve, reject) => {
        const bool = this.$refs.subjectSettingRef.addErrorToData();
        this.showTips = true;
        const range = this.$refs.adaptationRangeRef.dimForm.validateFields();
        const rangeParams = this.$refs.adaptationRangeRef.getRangeParams();
        const itemList = this.$refs.subjectSettingRef.getSubjectParams();
        const subBool = this.validateSubject(itemList);
        // 必填校验
        if (!this.offsetName && this.currentPageStatus === "edit") {
          UiUtils.errorMessage("规则名称为空，请输入");
          this.directSave = false;
          return;
        }
        if (this.offsetName.length > 64 || this.isOffsetNameRepeat) {
          // 失去焦点的时候提示过。
          // if (this.currentPageStatus === "edit") {
          //   UiUtils.errorMessage("规则名称超过64位限制, 请检查");
          // }
          this.directSave = false;
          return;
        }
        if (bool || range.errors || !itemList.length || !subBool) {
          this.directSave = false;
          return;
        }
        this.btnLoading = true;
        const queryParams = {
          offsetId: this.offsetId,
          offsetName: this.offsetName,
          ...rangeParams,
          currentOffsetDetailList: itemList
        };
        const self = this;
        const callback = offsetId => {
          UiUtils.successMessage("保存成功");
          offsetId && (this.offsetId = offsetId);
          self.clearCommonSaveEventsMixin();
          self.currentPageStatus = "detail";
          const params = { offsetId: offsetId, offsetName: self.offsetName };
          const id = UrlUtils.getQuery("inTab");
          this.setTabParamsMixin(id, {
            status: "detail",
            ...params
          });
          self.initData(null, () => {
            self.updatePageStaus({
              key: self.params ? self.params.id : "", // this.params.id,
              status: "detail"
            });
            self.updateTabTitle({
              id: self.params ? self.params.id : "",
              title: self.offsetName
            });
          });
        };
        const requestFunc = (methodName, cb) => {
          currentoffsetService(methodName, queryParams)
            .then(res => {
              const offsetId = res.data;
              cb && cb(offsetId || this.offsetId);
              resolve();
            })
            .catch(() => {
              this.offsetName = this.cloneoffsetName;
            })
            .finally(() => {
              this.directSave = false;
              this.btnLoading = false;
            });
        };
        if (this.currentPageStatus === "edit") {
          requestFunc("updateCurrentOffset", callback);
        } else {
          requestFunc("saveCurrentOffset", callback);
        }
      });
    },

    addSaveEventCb(notSaveCb) {
      this.addCallBackFnMixin(this.handleSave.bind(this), warnTitle);
    },
    closeNewAddTab() {
      this.closetabMixin(this.offsetId);
    },
    addSaveCb() {
      this.addSaveEventCb(this.closeNewAddTab);
    },
    inputChange() {
      this.addSaveCb();
    }
  }
};
</script>

<style scoped lang="less">
.currentoffset-detail {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .edit-title {
    position: relative;
    margin: 0.25rem 0;
  }
  .edit-name {
    width: calc(100% - 1.5rem);
    position: absolute;
    left: @yn-padding-l;
    /deep/ input {
      background: @yn-background-color;
    }
  }
  .err-tip {
    position: absolute;
    left: 0;
    top: @rem32;
    display: inline-block;
    color: @yn-error-color;
  }
  .error-tips {
    /deep/.ant-input {
      border: 1px solid @yn-error-color;
      border-radius: @rem4;
      outline: none;
      box-shadow: none;
    }
  }
  .header-title {
    display: inline-block;
    font-size: @rem16;
    font-weight: 600;
  }
  .detail-header {
    width: 100%;
    height: 3.5rem;
    background: @yn-component-background;
    padding: @yn-padding-l @yn-padding-xxxxl;
    box-sizing: border-box;
  }
  .padding-style {
    padding: @yn-padding-xl @yn-padding-xxxxl !important;
  }
  .margin-style {
    margin-top: @yn-margin-xs;
  }
  .container-base-info {
    margin-top: @yn-margin-xl;
  }
  .detail-container-edit {
    height: calc(100% - 6.5rem);
  }
  .detail-container-info {
    height: calc(100% - 3rem);
  }
  .detail-container {
    width: 100%;
    margin-top: @yn-margin-s;
    background: @yn-component-background;
    padding: @yn-padding-l @yn-padding-xl 0 @yn-padding-xl;
    overflow-y: auto;
    .head-info-title {
      font-size: 0.875rem;
      color: @yn-heading-color;
      font-weight: 500;
    }
    .info-title {
      text-align: right;
      height: @rem32;
      line-height: @rem32;
      padding-right: @yn-padding-s;
      color: @yn-text-color-secondary;
      font-size: @rem14;
    }
  }

  .detail-footer {
    width: calc(100% - 1.5rem);
    text-align: right;
    margin: 0 @yn-margin-l;
    padding: 0.5rem @yn-padding-l 0.5rem 0;
    background: @yn-component-background;
    border-top: 1px solid @yn-border-color-base;
    .footer-btn {
      min-width: 5rem;
      &:first-child {
        margin-right: @yn-margin-s;
      }
    }
  }
  .has-star::before {
    display: inline-block;
    margin-right: @yn-margin-xs;
    color: @yn-error-color;
    font-size: @rem14;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
  }
}
/deep/.edit-pan {
  float: left;
  margin-left: @yn-margin-xl;
}
</style>
<style lang="less">
.head-input {
  width: 100%;
  border: none;
  caret-color: @yn-primary-color;
  font-weight: 500;
  padding: 0;
}
.head-input:focus {
  box-shadow: none;
}
</style>
