<template>
  <div class="adaptation-range">
    <div class="range-title">适配范围</div>
    <div v-if="currentPageStatus === 'detail'" class="range-detail">
      <span>适配范围包含“版本”、“年”、“期间”、“合并组”，</span>
      <yn-button class="click-see" type="text" @click="seeAllmember">
        点击查看
      </yn-button>
    </div>
    <yn-form v-else :colon="false" :form="dimForm" v-bind="formLayout">
      <yn-card-layout
        :breakpoint="breakpoint"
        :M="layout.M"
        :L="layout.L"
        :XL="layout.XL"
        :XXL="layout.XXL"
        :XXXL="layout.XXXL"
      >
        <yn-form-item
          v-for="(item, index) in dimList"
          :key="item.objectId"
          class="dim-form-item"
          :label="item.dimName"
        >
          <ShowDimListInput
            v-decorator="[
              item.dimCode,
              {
                rules: [{ required: true, message: `请选择${item.dimName}` }],
                initialValue: item.selectedItem
              }
            ]"
            analyticExpName="getExpMemberWithCode"
            :dimInfo="item"
            @change="v => getExparseMembers(v, index)"
          />
        </yn-form-item>
      </yn-card-layout>
    </yn-form>
    <yn-drawer
      wrapClassName="range-drawer"
      title="适配范围"
      :visible="offsetVisible"
      :width="440"
      @close="() => (offsetVisible = !offsetVisible)"
    >
      <div class="drawer-content" :style="{ maxHeight: `${contentHeight}px` }">
        <div
          v-for="(item, index) in dimList"
          :key="item.dimName"
          class="range-version"
        >
          <p class="version-title">{{ item.dimName }}</p>
          <p v-for="member in item.selectedItem" :key="member.objectId">
            {{ member.dimMemberName }}
          </p>
          <yn-divider
            v-show="index !== dimList.length - 1"
            class="logic-divider"
          />
        </div>
      </div>
    </yn-drawer>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-card-layout/";
import _cloneDeep from "lodash/cloneDeep";
import _debounce from "lodash/debounce";

import ShowDimListInput from "../../../components/hoc/ShowDimListInput.vue";
import DIM_INFO from "@/constant/dimMapping";
import {
  CARD_LAYOUT_BREAK_POINT,
  CARD_LAYOUT_LAYOUT
} from "@/constant/common.js";
export default {
  components: { ShowDimListInput },
  props: {
    noRange: {
      type: Boolean,
      default: false
    },
    offsetInfo: {
      type: Object,
      default: () => ({})
    },
    currentPageStatus: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      breakpoint: CARD_LAYOUT_BREAK_POINT,
      layout: CARD_LAYOUT_LAYOUT,
      dimForm: this.$form.createForm(this, "dimForm"),
      formLayout: {
        labelCol: { span: 7 },
        wrapperCol: { span: 17 }
      },
      offsetVisible: false,
      contentHeight: 0,
      dimList: [
        {
          dimName: "版本",
          dimCode: "Version",
          objectId: DIM_INFO.Version,
          dimId: DIM_INFO.Version,
          selectedItem: []
        },
        {
          dimName: "年",
          dimCode: "Year",
          objectId: DIM_INFO.Year,
          dimId: DIM_INFO.Year,
          selectedItem: []
        },
        {
          dimName: "期间",
          dimCode: "Period",
          objectId: DIM_INFO.Period,
          dimId: DIM_INFO.Period,
          selectedItem: []
        },
        {
          dimName: "合并组",
          dimCode: "Scope",
          objectId: DIM_INFO.Scope,
          dimId: DIM_INFO.Scope,
          selectedItem: []
        }
      ]
    };
  },
  watch: {
    offsetInfo: {
      handler(nv) {
        if (nv.hasOwnProperty("versionList")) {
          this.initData();
        }
      }
    }
  },
  async created() {
    this.getPageHeight();
  },

  methods: {
    getRangeParams() {
      const rangeP = {};
      this.dimList.forEach(item => {
        const code =
          item.dimCode.slice(0, 1).toLowerCase() + item.dimCode.slice(1);
        const key = `${code}Exps`;
        rangeP[key] = item.selectedItem
          .map(dimMember => dimMember.dimMemberDbCode)
          .join(",");
      });
      return rangeP;
    },
    initData() {
      if (this.currentPageStatus === "newAdd") return;
      for (let i = 0; i < this.dimList.length; i++) {
        const item = this.dimList[i];
        const k = `${item.dimCode.toLowerCase()}List`;
        item.selectedItem = this.offsetInfo[k];
      }
    },

    getPageHeight() {
      const pageH = window.innerHeight - 45;
      this.contentHeight = pageH;
      window.onresize = _debounce(() => {
        const pageH = window.innerHeight - 45;
        this.contentHeight = pageH;
      }, 500);
    },

    seeAllmember() {
      this.offsetVisible = true;
    },

    getExparseMembers(info, i) {
      const { selectedItem } = info;
      const copyDimList = _cloneDeep(this.dimList);
      copyDimList[i].selectedItem = selectedItem;
      this.$set(this, "dimList", copyDimList);
      this.$emit("addSaveCb");
    }
  }
};
</script>

<style lang="less">
.range-drawer {
  .ant-drawer-body {
    padding: 0;
  }
}
</style>
<style scoped lang="less">
.adaptation-range {
  width: 100%;
  .range-title {
    font-size: @rem14;
    color: @yn-heading-color;
    font-weight: 600;
    margin-bottom: @yn-margin-l;
  }
  .range-detail {
    margin-top: @yn-padding-xl;
    color: @yn-text-color-secondary;
    font-size: @rem14;
    .click-see {
      color: @yn-chart-1;
      margin-left: @yn-padding-s;
      cursor: pointer;
    }
  }
}
.drawer-content {
  padding: @yn-padding-xxxl;
  overflow-y: scroll;
}
.error-text {
  color: @yn-error-color;
}
.version-title {
  font-size: @rem14;
  color: @yn-label-color;
}
</style>
<style lang="less">
.adaptation-range {
  .yn-card-layout {
    overflow: hidden;
  }
  .dim-list-input {
    width: 100%;
  }
}
</style>
