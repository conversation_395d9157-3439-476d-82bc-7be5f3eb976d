<template>
  <div class="filterCheckbox" @click.stop>
    <div class="searchCont">
      <yn-input-search
        v-model="searchWord"
        class="searchInput"
        :placeholder="$t_common('please_in_search')"
        @search="handleSearch(true)"
        @change="filterCheckboxList"
      />
    </div>

    <div v-if="dataList.length > 0" class="checkboxCont">
      <p v-for="item in dataList" :key="item.id">
        <yn-checkbox
          :value="item.id"
          :checked="checkedArr.indexOf(item.id) != -1"
          @change="handleChange"
        >
          {{ item.name }}
        </yn-checkbox>
      </p>
    </div>
    <yn-empty v-else class="filter-empty-image" />
    <div class="btnCont">
      <yn-button
        type="primary"
        size="small"
        :disabled="dataList.length === 0"
        class="filter_ok_btn filter_btn"
        @click="handleSearch(false)"
      >
        {{ $t_common("ok") }}
      </yn-button>
      <yn-button
        size="small"
        class="filter_cancel_btn filter_btn"
        @click="handleReset"
      >
        {{ $t_common("reset") }}
      </yn-button>
    </div>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-checkbox/";
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-checkbox-group/";
import "yn-p1/libs/components/yn-input-search/";
export default {
  name: "FilterCheckboxGroup",
  props: {
    checkboxList: {
      type: Array,
      default() {
        return [];
      }
    },
    checkedList: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      checkedArr: [],
      dataList: [],
      searchWord: ""
    };
  },
  watch: {
    checkedList: {
      handler(newVal) {
        this.$set(this, "checkedArr", newVal);
      },
      immediate: true
    },
    checkboxList: {
      handler(newVal) {
        this.searchWord = "";
        if (newVal.length) {
          this.$set(this, "dataList", newVal);
        }
      },
      immediate: true
    }
  },
  mounted() {
    this._click_event = e => {
      this.searchWord = "";
      this.dataList = [...this.checkboxList];
      this.$set(this, "checkedArr", [...this.checkedList]);
    };
    document.body.addEventListener("click", this._click_event);
  },
  beforeDestroy() {
    document.body.removeEventListener("click", this._click_event);
    this._click_event = null;
  },
  methods: {
    handleChange(isChecked, event) {
      const val = event.currentTarget.value;
      if (isChecked) {
        this.checkedArr.push(val);
      } else {
        const ident = this.checkedArr.indexOf(val);
        ident !== -1 && this.checkedArr.splice(ident, 1);
      }
    },
    handleSearch(isSearch) {
      if (isSearch && !this.searchWord) {
        return;
      }
      if (this.dataList.length > 0) {
        this.$emit("changeChecked", this.checkedArr);
        this.$emit("handleSearch");
        this.searchWord = "";
        this.dataList = [...this.checkboxList];
      }
    },
    handleReset() {
      this.$emit("handleReset");
      this.searchWord = "";
      this.dataList = [...this.checkboxList];
    },
    filterCheckboxList() {
      this.dataList = this.searchWord
        ? this.checkboxList.filter(item => {
          return item.name.indexOf(this.searchWord) !== -1;
        })
        : [...this.checkboxList];

      if (this.searchWord) {
        this.checkedArr = this.dataList.map(item => item.id);
      } else {
        this.checkedArr = [];
      }
    }
  }
};
</script>
<style lang="less" scoped>
.organizationTable /deep/.ant-table-filter-dropdown > div {
  padding: 0.5rem 0 0.5rem 0.5rem !important;
}
</style>
<style lang="less">
.filterCheckbox {
  width: 100%;
  max-height: 14.75rem;
  .checkboxCont {
    max-height: 7.5rem;
    overflow: auto;
    p {
      margin-bottom: 0.5rem;
    }
  }
}
.searchInput {
  margin-bottom: 10px;
  // padding-right: 8px;
}
.btnCont {
  height: 1.625rem;
  width: 100%;
}
.filter_cancel_btn {
  margin-right: 0.625rem;
}
.filter_ok_btn {
  margin-right: 0.5rem;
}
.searchCont {
  padding-right: 0.5rem;
}
.filter_btn {
  float: right;
}
</style>
