<template>
  <div v-if="showTable" class="tableCont">
    <div v-if="tableTitle" class="title">
      <p>{{ tableTitle }}</p>
    </div>
    <div class="grid-cont">
      <yn-spin :spinning="spinning">
        <Grid
          ref="grid"
          :hasMore="hasMore"
          :ident="$data.$gridIdent"
          :columns="columns"
          :dataSource="tableData"
          :dropDownData="{
            mergeMethod: selectData
          }"
          :filterOrder="['memberCodeIndex', 'organization', 'mergeMethod']"
          :filterObject="filterObject"
          :paginationInfo="pagination"
          :loadMore="loadMore"
          :cellClass="cellClass"
          :cellNode="cellNode"
          :validateCell="validateCell"
          :renderHeaderCell="renderHeaderCell"
          :errorRowInfo="errorRowInfo"
          :firstPageNum="firstPageNum"
          :mixCellWidth="200"
          @changeCellVal="changeCellVal"
        />
      </yn-spin>
    </div>
    <yn-popconfirm
      :title="copyTitle"
      :okText="$t_common('ok')"
      :cancelText="$t_common('cancel')"
      placement="topRight"
      @confirm="copySystemToFinally"
    >
      <span
        ref="copyBtnShadow"
        class="copy-btn-shadow"
        :style="copBtnShadowStyle"
      ></span>
    </yn-popconfirm>
  </div>
  <yn-empty v-else :description="$t_common('no_data')" class="empty">
    <yn-button
      v-if="hasAddPermission"
      type="primary"
      class="addMergeGroup"
      @click="addMergeGroup"
    >
      {{ $t_structures("add_scope") }}
    </yn-button>
    <yn-button
      v-if="hasAddPermission"
      :class="addOrganizationClass"
      @click="addOrganization"
    >
      {{ $t_structures("add_entity") }}
    </yn-button>
  </yn-empty>
</template>
<script>
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-button/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import savePromptMixin from "@/mixin/savePrompt.js";
import { EventBus } from "yn-p1/libs/utils/ComponentUtils";
import { mapState, mapGetters, mapMutations } from "vuex";
import organizationServer from "@/services/organization";
import commonService from "@/services/common";
import Grid from "@/components/hoc/grid/index.vue";
import cloneDeep from "lodash/cloneDeep";
import { Decimal } from "decimal.js";
import { genVDOM } from "@/views/process/control/jdom/utils";
import { TABLE_COLUMNS } from "../../constant.js";
import lang from "@/mixin/lang";

const { $t_structures, $t_equity } = lang;
import {
  PERMISSION_NAME_MAPPING,
  ADDNODETYPE,
  DIM_INFO_ENTITY,
  DIM_INFO_SCOPE
} from "../../constant";

let eventBusSaveTableData = "";
const CODE_CELL_WIDTH = [200, 300];
const DIFF_CELL = ["systemCalc", "finallyNum"];
const FIRST_PAGE_NUM = 30;
const SAVE_TIPS = $t_structures("save_arch_edit");
const CELL_READ_ONLY = "readOnly"; // 单元格只读
const NUMBER_CELL_IDENT = [
  "finallyNum",
  "systemCalc",
  "parentCompanyHolding",
  "minorityShareHolding",
  "finalMinorityShareHolding",
  "directShareHolding",
  "indirectShareHolding"
];
const COPY_TITLE = {
  parallel: $t_structures("sure_copy_equity"),
  level: $t_structures("sure_copy_equity_to_lose")
};
export default {
  name: "OrganizationTable",
  components: {
    Grid
  },
  mixins: [savePromptMixin],
  props: {
    loadTableData: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      spinning: false,
      tableData: [],
      mapTableData: {},
      tableTitle: "",
      pagination: {
        total: 0,
        current: 1,
        pageSize: FIRST_PAGE_NUM,
        hasMore: false,
        offset: 0
      },
      showTable: false, //  展示table
      selectData: [],
      columns: [],
      filterObject: {
        organization: {},
        mergeMethod: {},
        memberCodeIndex: {}
      },
      errorRowInfo: {},
      firstPageNum: FIRST_PAGE_NUM,
      copyBtnPos: {},
      cacheTableData: [],
      hasMore: false,
      $gridIdent: "organizationManagementList", // 表格标识
      holdingDetailMemberAttrValues: {} // 根据股权项目属性控制列是否显示
    };
  },
  computed: {
    ...mapState("organization", {
      tableColumsConfig: state => state.tableConfig,
      pageDimObj: state => state.pageDimObj,
      authData: state => state.authData,
      selectTreeNode: state => state.selectedTreeNode,
      offsetRule: state => state.offsetRule
    }),
    ...mapGetters("organization", ["authObj", "isAllPageDims"]),
    copBtnShadowStyle() {
      const { left, top } = this.copyBtnPos;
      return {
        left: left + "px",
        top: top + "px"
      };
    },
    hasAddPermission() {
      return this.authObj[PERMISSION_NAME_MAPPING.add];
    },
    addOrganizationClass() {
      return !Object.keys(this.selectTreeNode).length &&
      this.pageDimObj.mergeGroupVal === "allScope"
        ? "disabled"
        : "";
    },
    copyTitle() {
      return COPY_TITLE[this.offsetRule || "parallel"];
    }
  },
  watch: {
    tableColumsConfig: {
      handler(newVal) {
        this.columns = [...this.getColumns()];
        // this.$refs.grid.updateTableData();
        this.refreshTable();
      },
      deep: true
    },
    selectTreeNode: {
      async handler(newVal, oldVal) {
        const { key: newKey } = newVal || {};
        const { key: oldKey } = oldVal || {};
        if (newKey !== oldKey) {
          this.$set(this, "pagination", {
            total: 0,
            current: 1,
            pageSize: FIRST_PAGE_NUM,
            hasMore: false,
            offset: 0
          });
        }
        if (Object.keys(newVal).length) {
          if (this.$refs.grid) {
            // 清除过滤条件
            this.$refs.grid.clearAll();
            this.$refs.grid.$refs.simpleGrid.clearEditVMap(); // 清除编辑缓存
          }
          this.errorRowInfo = {}; // 清除报错信息
          //  选中节点
          this.tableTitle = newVal.memberName;
          this.showTable = true;
          if (Object.keys(this.pageDimObj).length >= 4) {
            await this.getData();
            this.columns = [...this.getColumns()];
            this.setTableOrganizationList();
          } else {
            this.showTable = false;
            this.$refs.grid && this.$refs.grid.updateTableData([]);
          }
        } else {
          // 没有选中节点信息
          this.$set(this, "tableData", []);
          this.showTable = false;
          this.$refs.grid && this.$refs.grid.updateTableData([]);
        }
        this.$store.commit("organization/clearToBeSaveObj");
        this.$emit("getSaveMessage", []);
      },
      immediate: true
    }
  },
  async mounted() {
    await this.getConsolidatedMethod();
    await this.checkDisplayConsolidationStructures();
    this.columns = [...this.getColumns()];
    eventBusSaveTableData = () => {
      this.saveTableData();
    };
    EventBus.on("refreshTable", this.refreshTable);
    EventBus.on("saveTableData", eventBusSaveTableData);
  },
  beforeDestroy() {
    EventBus.off("refreshTable", this.refreshTable);
    EventBus.off("saveTableData", eventBusSaveTableData);
  },
  methods: {
    ...mapMutations("organization", [
      "setDimensionTransferInfo",
      "setChildrenTreeData",
      "setSelectedTreeNode"
    ]),
    async refreshTable() {
      if (this.showTable) {
        await this.getData();
        this.setTableOrganizationList();
        this.$refs.grid.clearAll();
        this.$refs.grid.$refs.simpleGrid.updateBody(
          this.tableData,
          this.pagination.isLastPage
        );
      }
    },
    async getConsolidatedMethod() {
      await commonService("getMemberByDimCode", "ConsolidatedMethod").then(
        res => {
          let result = [];
          const selectData = [];
          const { data } = res;
          if (data && data.length) {
            result = data.map(item => {
              selectData.push({
                title: item.name,
                key: item.dimMemberCode
              });
              return item.name;
            });
          }
          this.$set(this, "selectData", selectData);
          this.filterObject["mergeMethod"] = {
            data: result,
            name: this.$t_common("consolidated_method")
          };
        }
      );
    },
    // 检查是否显示合并架构相关列
    async checkDisplayConsolidationStructures() {
      try {
        const res = await organizationServer("getHoldingDetailMemberAttrValues");
        if (res.status === 200) {
          this.holdingDetailMemberAttrValues = res.data || {};
        }
      } catch (error) {
        this.holdingDetailMemberAttrValues = {};
      }
    },
    async setTableOrganizationList() {
      await organizationServer(
        "getTableOrganizationList",
        this.getPageDim()
      ).then(res => {
        const organization = [];
        const organizationDimCode = [];
        const mappingOranization = {};
        const mappingOranizationDimCode = {};
        if (res.data) {
          res.data.map(item => {
            const { memberId, dimMemberName, memberCodeIndex } = item;
            const organizationItem = {
              id: memberId,
              name: dimMemberName,
              ...item
            };
            const organizationDimCodeItem = {
              id: memberId,
              name: memberCodeIndex,
              ...item
            };
            organization.push(organizationItem);
            organizationDimCode.push(organizationDimCodeItem);
            mappingOranization[memberId] = organizationItem;
            mappingOranizationDimCode[memberId] = organizationDimCodeItem;
          });
        }
        this.filterObject["organization"] = {
          name: this.$t_common("entity"),
          data: organization
        };
        this.filterObject["memberCodeIndex"] = {
          name: this.$t_structures("unique_identifier"),
          data: organizationDimCode
        };
      });
    },
    // 添加合并组
    addMergeGroup() {
      if (this.isAllPageDims) {
        this.setDimensionTransferInfo({
          visible: true,
          treeNode: {},
          type: ADDNODETYPE[1],
          dimInfo: { ...DIM_INFO_SCOPE(), selectedItem: [] }
        });
      } else {
        UiUtils.errorMessage(this.$t_structures("select_page_dimension_first"));
      }
    },
    // 添加组织
    addOrganization() {
      if (this.addOrganizationClass === "disabled") return;
      if (this.isAllPageDims) {
        this.setDimensionTransferInfo({
          visible: true,
          treeNode: {},
          type: ADDNODETYPE[2],
          dimInfo: { ...DIM_INFO_ENTITY(), selectedItem: [] }
        });
      } else {
        UiUtils.errorMessage(this.$t_structures("select_page_dimension_first"));
      }
    },
    getColumns() {
      const { cellType } = this.tableColumsConfig;
      const idCol = {
        title: this.$t_structures("unique_identifier"),
        dataIndex: "memberCodeIndex",
        width:
          cellType === "NAME_AND_CODE"
            ? CODE_CELL_WIDTH[0]
            : CODE_CELL_WIDTH[1],
        isFilter: true,
        cellType: "text",
        scopedSlots: {
          customRender: "organization",
          filterDropdown: "filterDropdown_organizationDimCode"
        }
      };
      const { hasLeaf } = this.selectTreeNode;
      const nameCol = {
        title: `${this.$t_common("entity")}（${hasLeaf ? "Scope" : "Entity"}）`,
        dataIndex: "organization",
        width: CODE_CELL_WIDTH[1],
        isFilter: true,
        cellType: "text"
      };
      const columns = [
        {
          title: this.$t_common("order_number"),
          width: 80,
          dataIndex: "serialNumber",
          cellType: "text" // 新增
        },
        {
          title: this.$t_common("consolidated_method"),
          dataIndex: "mergeMethod",
          isFilter: true, // 新增，grid 组件监听此值，默认展示表头下拉组件
          cellType: "dropDown",
          // readOnly: false // 默认是 true
          readOnly: !this.authObj[PERMISSION_NAME_MAPPING.edit],
          paste: false
        }
      ];

      // 根据 holdingDetailMemberAttrValues 控制直接持股比例列的显示
      if (this.holdingDetailMemberAttrValues.DirectShareholding !== false) {
        columns.push({
          title: $t_equity("direct_shareholding_ratio"),
          dataIndex: "directShareHolding",
          cellType: "text",
          align: "right",
          readOnly: true
        });
      }

      // 根据 holdingDetailMemberAttrValues 控制间接持股比例列的显示
      if (this.holdingDetailMemberAttrValues.IndirectShareholding !== false) {
        columns.push({
          title: $t_equity("indirect_shareholding_ratio"),
          dataIndex: "indirectShareHolding",
          cellType: "text",
          align: "right",
          readOnly: true
        });
      }

      columns.push({
        title: this.$t_structures("calculated_comp_shareholding_ratio"),
        dataIndex: "systemCalc",
        cellType: "text",
        align: "right",
        readOnly: true
      });

      // 根据 holdingDetailMemberAttrValues 控制 TABLE_COLUMNS 中列的显示
      const tableColumns = TABLE_COLUMNS();
      const filteredTableColumns = tableColumns.filter(column => {
        if (column.dataIndex === "parentCompanyHolding") {
          return this.holdingDetailMemberAttrValues.ParentCompanyholding !== false;
        }
        if (column.dataIndex === "minorityShareHolding") {
          return this.holdingDetailMemberAttrValues.MinorityShareholding !== false;
        }
        return true;
      });

      columns.splice(columns.length, 0, ...filteredTableColumns);

      // 根据 holdingDetailMemberAttrValues 控制母公司持股列的显示
      if (this.holdingDetailMemberAttrValues.IsHolding !== false) {
        columns.push({
          title: this.$t_equity("parent_company_holding"),
          dataIndex: "isHolding",
          cellType: "text",
          align: "right",
          readOnly: true
        });
      }

      // 根据 holdingDetailMemberAttrValues 控制最终集团内少数股东比例列的显示
      if (this.holdingDetailMemberAttrValues.FinalMinorityShareholding !== false) {
        columns.push({
          dataIndex: "finalMinorityShareHolding",
          title: this.$t_structures("minority_shareholder_ratio_in_group"),
          width: 250,
          align: "right",
          readOnly: !this.authObj[PERMISSION_NAME_MAPPING.edit],
          cellType: "text"
        });
      }

      if (cellType === "NAME") {
        columns.splice(1, 0, nameCol);
      } else if (cellType === "CODE") {
        columns.splice(1, 0, idCol);
      } else if (cellType === "NAME_AND_CODE") {
        columns.splice(1, 0, idCol);
        columns.splice(5, 0, nameCol);
      }

      // 根据 holdingDetailMemberAttrValues 控制最终持股比例列的显示
      if (this.holdingDetailMemberAttrValues.FinalShareholding !== false) {
        columns.push({
          dataIndex: "finallyNum",
          title: this.$t_structures("comprehensive_shareholding_ratio"),
          cellType: "text",
          readOnly: !this.authObj[PERMISSION_NAME_MAPPING.edit],
          align: "right"
        });
      }

      return columns;
    },
    async loadMore(params) {
      const { period, version, year, id } = this.getPageDim();
      const { pageSize: limit, offset } = this.pagination;
      if (!period || !version || !year || !id) return;
      const { isFirstPage, searchObj = [] } = params;
      const dataOffset = isFirstPage ? 0 : offset;
      if (isFirstPage) {
        this.pagination.offset = 0;
        this.spinning = true;
      }
      const searchParams = this.getSearchParams(searchObj);
      return await organizationServer("getOrganizationPage", {
        period,
        version,
        year,
        id,
        offset: dataOffset,
        limit: isFirstPage ? FIRST_PAGE_NUM : limit,
        ...searchParams
      })
        .then(res => {
          if (res.status === 200 && res.data) {
            const { list, total, isLastPage } = res.data;
            const data = this.transferTableData(list || [], dataOffset);
            this.cacheTableData.push(...cloneDeep(data));
            if (isFirstPage) {
              this.cacheTableData = cloneDeep(data);
            }
            this.setMapTableData(list);
            this.pagination.total = total;
            this.pagination.hasMore = !isLastPage;
            this.hasMore = !isLastPage;
            this.pagination.offset = dataOffset + FIRST_PAGE_NUM;
            this.pagination.current++;
            this.$set(this, "pagination", this.pagination);
            return {
              data,
              hasMore: !isLastPage
            };
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    getSearchParams(arr) {
      const res = {
        memberIdList: [],
        mergeMethodList: []
      };
      const mapObj = {
        organization: "memberIdList",
        memberCodeIndex: "memberIdList",
        mergeMethod: "mergeMethodList"
      };
      arr.forEach(item => {
        const { type, searchArr } = item;
        searchArr.map(obj => {
          const { id } = obj;
          if (res[mapObj[type]].indexOf(id) === -1) {
            res[mapObj[type]].push(id);
          }
        });
      });
      return res;
    },
    cellClass(cellContext) {
      const { row = 0, colName } = cellContext;
      if (this.cacheTableData.length < row) return;
      if (this.offsetRule === "parallel") {
        return this.diffCell(cellContext, DIFF_CELL);
      } else {
        // 最终持股、控股母公司比例
        const group1 = ["parentCompanyHolding", "finallyNum"];
        // 最终集团少数股东比例、集团内少数股东比例
        const group2 = ["minorityShareHolding", "finalMinorityShareHolding"];
        if (group1.indexOf(colName) !== -1) {
          return this.diffCell(cellContext, group1);
        } else if (group2.indexOf(colName) !== -1) {
          return this.diffCell(cellContext, group2);
        }
      }
    },
    diffCell(cellContext, group) {
      const { row = 0, colName } = cellContext;
      const [cellName1, cellName2] = group;
      const rowData = this.cacheTableData[row - 1] || {};
      const cell1Val = rowData[cellName1].v.replace("%", "");
      const cell2Val = rowData[cellName2].v.replace("%", "");
      if (group.indexOf(colName) !== -1 && cell1Val !== cell2Val) {
        return "diff-cell";
      }
    },
    cellNode(cellContext) {
      const { colName, value } = cellContext;
      if (NUMBER_CELL_IDENT.indexOf(colName) !== -1) {
        if (!isNaN(Number(value))) {
          return value ? `${new Decimal(value).toFixed(2)}%` : "";
        } else if (value === void 0) {
          return "0.00%";
        }
      }
    },
    renderHeaderCell(cellContext) {
      const { td, colName, value: cellValue } = cellContext;
      if (
        (colName === "finallyNum" && this.offsetRule === "parallel") ||
        (this.offsetRule === "level" && colName === "finalMinorityShareHolding")
      ) {
        const childDiv = document.createElement("div");
        const spanTag = document.createElement("span");
        spanTag.className = `header-col-text `;
        spanTag.textContent = cellValue;
        childDiv.appendChild(spanTag);
        childDiv.className = "header-col";
        const copyIcon = genVDOM({
          template: `<yn-icon-button type='icon-copy'  size="smallest" class='iconBtn ${colName}-filter-icon'/>`
        });
        if (this.authObj[PERMISSION_NAME_MAPPING.edit]) {
          childDiv.appendChild(copyIcon);
        }
        td.className += " grid-header";
        td.appendChild(childDiv);
        copyIcon.addEventListener("click", e => {
          if (this.tableData.length) {
            const {
              offsetTop: gridContTop,
              clientWidth: conteW
            } = document.querySelector(".grid-cont");
            const { clientWidth: tableW } = this.$refs.grid.$el.querySelector(
              ".wtHider"
            );
            const { left: tdLeft, width: tdW } = this.$refs.grid.getPopPosByTd(
              td
            );
            this.copyBtnPos = {
              top: gridContTop + 8,
              left: tableW > conteW ? conteW - 10 : tdLeft + tdW
            };
            this.$refs.copyBtnShadow.click();
          }
        });
        return childDiv;
      }
    },
    getTdDom(icon) {
      let tdDom = icon.parentElement;
      while (tdDom.tagName !== "TD") {
        tdDom = tdDom.parentElement;
      }
      return tdDom;
    },
    async getData() {
      this.spinning = true;
      const { period, version, year, id } = this.getPageDim();
      if (!period || !version || !year || !id) return;
      this.$emit("update:loadTableData", true);
      await organizationServer("getOrganizationPage", {
        period,
        version,
        year,
        id,
        offset: 0,
        limit: FIRST_PAGE_NUM
      })
        .then(res => {
          if (res.status === 200 && res.data) {
            const { list, total, isLastPage, endRow } = res.data;
            this.setMapTableData(list || []);
            this.tableData = [...this.transferTableData(list || [], 0)];
            this.$refs.grid.updateTableData(this.tableData);
            this.$set(this, "tableData", this.tableData);
            this.$refs.grid.$refs.simpleGrid.loading = false;
            this.cacheTableData = cloneDeep(this.tableData);
            this.pagination.total = total;
            this.pagination.hasMore = !isLastPage;
            this.hasMore = !isLastPage;
            this.pagination.offset = endRow;
            this.pagination.pageSize = FIRST_PAGE_NUM;
            this.pagination.current++;
            return res;
          }
        })
        .finally(() => {
          this.spinning = false;
          this.$emit("update:loadTableData", false);
        });
    },
    setMapTableData(data) {
      const res = {};
      data &&
      data.length &&
      data.forEach(item => {
        const { key } = item;
        res[key] = item;
      });
      this.mapTableData = Object.assign({}, this.mapTableData, res);
    },
    transferTableData(data, offset) {
      return data.map((item, index) => {
        const rowKey = item.key;
        const res = {};
        res.serialNumber = {
          v: index + offset + 1,
          objectId: rowKey,
          rowKey,
          level: 1
        };
        res.objectId = rowKey;
        Object.keys(item).forEach(keyName => {
          if (keyName !== "key" && keyName !== CELL_READ_ONLY) {
            res[keyName] = {
              v: item[keyName],
              objectId: rowKey,
              customValidate: true, // 业务方，单元格校验
              rowKey
            };
            if (item.readOnly) {
              res[keyName].readOnly = true;
            }
            if (keyName === "mergeMethod") {
              const matchItem = this.selectData.find(item => item.key === res[keyName].v);
              res[keyName].v = matchItem ? matchItem.title : res[keyName].v;
            }
          }
        });
        return res;
      });
    },
    // 复制系统级计算持股比例到最终持股
    copySystemToFinally() {
      this.savePromptMixin().then(() => {
        // 调用接口，复制系统持股比列到最终持股
        const pageDim = this.getPageDim();
        if (!this.tableData.length) return;
        this.spinning = true;
        organizationServer("copyFinalShareholding", {
          ...pageDim
        }).then(res => {
          if (res.status !== 200) return;
          this.refreshTable().then(() => {
            UiUtils.successMessage(this.$t_common("copy_success"));
          });
        });
      });
    },
    saveTableData() {
      if (!Object.keys(this.selectTreeNode).length) {
        UiUtils.errorMessage(this.$t_structures("select_tree_node"));
        return;
      }
      this.$emit("changeLoadingStatus", true);
      // 前端校验 最终持股是否有超过100的单元格
      const { verificationInfo = {} } = this.$refs.grid;
      if (Object.keys(verificationInfo).length > 0) {
        // 校验失败
        UiUtils.error({
          title: this.$t_process("validation_failed"),
          content: this.$t_process("modify_values_of_cells")
        });
        this.$emit("changeLoadingStatus", false);
        return Promise.reject(this.$t_structures("value_cell_input_operation"));
      }
      // 调用接口 如果没有返回强校验信息，则执行resolve 否则reject
      const params = this.getSaveParams();
      return organizationServer("saveOrganizationTableData", params).then(
        res => {
          this.errorRowInfo = {};
          const { messageList = [], pageInfo } = res.data;
          const { isLastPage } = pageInfo;
          const newTableData = pageInfo.list || [];
          this.pagination.hasMore = !isLastPage;
          this.pagination.offset = FIRST_PAGE_NUM;
          this.pagination.pageNum = 1;
          this.$emit("changeLoadingStatus", false); // 关闭loading
          // 将系统计算持股比例 回显到表格上
          if (newTableData.length) {
            this.tableData = this.transferTableData(newTableData, 0);
            // 将编辑后合并方法的值，重新复制给tableData
            const editVMap = this.$refs.grid.$refs.simpleGrid.editVMap || {};
            Object.values(editVMap).forEach(item => {
              const { row, colName, originVal } = item;
              if (colName === "mergeMethod") {
                this.tableData[row - 1][colName].v = originVal;
              }
            });
            this.$set(this, "tableData", this.tableData);
            this.cacheTableData = cloneDeep(this.tableData);
            this.setMapTableData(newTableData);
            this.$nextTick(() => {
              this.$refs.grid.updateTableData(this.tableData);
            });
          }
          // 消息盒子信息
          this.$emit("getSaveMessage", messageList);
          if (messageList.length) {
            messageList.map(item => {
              const { key, type } = item;
              this.errorRowInfo[key] = { key, type };
            });
            return Promise.reject(
              this.$t_structures("backend_verification_failed")
            );
          } else {
            // 保存以后需要清除 mixin 里面的保存回调
            this.errorRowInfo = {};
            this.$refs.grid.clearAll();
            const simpleGrid = this.$refs.grid.$refs.simpleGrid;
            simpleGrid.clearEditVMap(); // 清除编辑缓存
            this.$refs.grid.clearToBeSaveObj(); // 清除带保存信息
            this.clearCommonSaveEventsMixin();
            this.$store.commit("organization/clearToBeSaveObj");
            UiUtils.successMessage(this.$t_common("save_success"));
            return Promise.resolve(this.$t_common("save_success"));
          }
        },
        rej => {
          this.$emit("changeLoadingStatus", false);
        }
      );
    },
    // 获取 版、本年、 期间、选择treeNode id
    getPageDim() {
      const {
        versionVal: version,
        yearVal: year,
        monthVal: period
      } = this.pageDimObj;
      const id = this.selectTreeNode ? this.selectTreeNode.objectId : "";
      return {
        version,
        year,
        period,
        id
      };
    },
    getSaveParams() {
      const pageDim = this.getPageDim();
      const toBeSaveObj = this.$refs.grid.getToBeSaveObj();
      const res = Object.keys(toBeSaveObj).map(item => {
        const res = cloneDeep(this.mapTableData[item]);
        const {
          key: objectId,
          mergeMethod,
          finallyNum,
          dimMemberCode,
          organization,
          finalMinorityShareHolding
        } = Object.assign(res, toBeSaveObj[item]);
        return {
          objectId,
          mergeMethod,
          finallyNum: finallyNum.replace("%", ""),
          memberDbCode: dimMemberCode,
          organization,
          finalMinorityShareHolding: finalMinorityShareHolding.replace("%", "")
        };
      });
      return {
        offset: 0,
        limit: FIRST_PAGE_NUM,
        ...pageDim,
        organizationEquityRatioDtoList: res
      };
    },
    // 适配simplGrid
    validateCell(cell) {
      const { colName } = cell;
      const methodMapping = {
        finallyNum: this.validateCellFinallyNum.bind(this),
        finalMinorityShareHolding: this.validateCellFinallyNum.bind(this)
      };
      return methodMapping[colName] ? methodMapping[colName](cell) : "";
    },
    validateCellFinallyNum(cell) {
      const { originVal } = cell;
      const num = originVal.replace("%", "");
      let result = {
        res: true,
        paperwork: ""
      };
      if (!num) {
        result = {
          res: false,
          paperwork: this.$t_equity("enter_numerical")
        };
      } else if (isNaN(num)) {
        result = {
          res: false,
          paperwork: this.$t_equity("enter_correct_numerical")
        };
      } else if (num < 0 || num > 100) {
        result = {
          res: false,
          paperwork: this.$t_equity("between_0_100")
        };
      }
      return result;
    },
    // 单元格改变值回调
    changeCellVal(cell) {
      this.addCallBackFnMixin(this.saveTableData.bind(this), SAVE_TIPS, () => {
        this.$refs.grid.clearToBeSaveObj();
      });
      if (!cell) {
        return;
      } else if (
        ["finallyNum", "finalMinorityShareHolding"].indexOf(cell.colName) === -1
      ) {
        return;
      }
      // 系统持股比列，最终 持股比例不一样，文本变色
      const { row, col, value: finallyVal } = cell;
      const rowData = this.cacheTableData[row - 1] || {};
      const prop = {
        custom_className: "diff-cell"
      };
      const { anotherCellName, anotherCellNameIndex } = this.getAnotherColInfo(
        cell.colName
      );
      if (
        Number(finallyVal.replace("%", "")) !==
        Number(rowData[anotherCellName].v)
      ) {
        this.$refs.grid.$refs.simpleGrid.setCell(
          row,
          col,
          { ...prop, originVal: finallyVal },
          "add"
        );
        this.$refs.grid.$refs.simpleGrid.setCell(
          row,
          anotherCellNameIndex,
          prop,
          "add"
        );
      } else {
        // 删除 变色
        this.$refs.grid.$refs.simpleGrid.setCell(
          row,
          col,
          { ...prop, originVal: finallyVal },
          "delete"
        );
        this.$refs.grid.$refs.simpleGrid.setCell(
          row,
          anotherCellNameIndex,
          prop,
          "delete"
        );
      }
      this.cacheTableData[row - 1].finallyNum.v = finallyVal;
      this.$set(this, "cacheTableData", this.cacheTableData);
    },
    getAnotherColInfo(cellName) {
      const anotherCellNameMapping = {
        finalMinorityShareHolding: "minorityShareHolding",
        finallyNum: ["parentCompanyHolding", "systemCalc"]
      };
      const finallyNumCellAnother = {
        parallel: "systemCalc",
        level: "parentCompanyHolding"
      };
      let anotherCellName = anotherCellNameMapping[cellName];
      if (Array.isArray(anotherCellName)) {
        anotherCellName = finallyNumCellAnother[this.offsetRule];
      }
      const anotherCellNameIndex = this.columns.findIndex(item => {
        return item.dataIndex === anotherCellName;
      });
      return {
        anotherCellName,
        anotherCellNameIndex
      };
    },
    updateCacheData(editObj) {
      const editKeyArr = Object.keys(editObj);
      this.cacheTableData.forEach(item => {
        const { objectId } = item;
        if (editKeyArr.indexOf(objectId) !== -1) {
          const tempObj = editObj[objectId] || {};
          Object.keys(tempObj).forEach(editKey => {
            item[editKey].v = tempObj[editKey];
          });
        }
      });
      this.$set(this, "cacheTableData", this.cacheTableData);
    }
  }
};
</script>
<style lang="less">
.grid-cont {
  .header-col .header-col-text {
    width: auto !important;
  }
}
</style>
<style lang="less" scope>
.tableCont {
  padding: 1rem 1rem 0;
  height: 100%;
  overflow-y: hidden;
  position: relative;
  background: @yn-component-background;

  .title p {
    font-size: @yn-font-size-base;
    font-weight: 600;
    color: @yn-text-color;
    margin-bottom: 1rem;
  }

  .grid-cont {
    position: relative;
    height: calc(100% - 2.375rem);
  }
}

.contentStyle {
  padding-left: 1rem;
  display: block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: @yn-body-background;
}

.noEdit {
  position: relative;
}

.tdFullHeight {
  line-height: 2.1875rem;
  height: @rem34;
}

.finallyNumCont {
  text-align: right;
  position: relative;
}

.errorTips {
  position: absolute;
  width: 100%;
  top: 2.1875rem;
  left: 0;
  font-size: 0.875rem;
  line-height: 0.875rem;
  color: @yn-error-color;
  z-index: 1;
}

.errCell {
  border: 1px solid @yn-error-color;
}

.errorRow {
  background: @yn-error-bg-color;
}

.warnRow {
  background: @yn-border-color-base;
}

.textRight {
  text-align: right;
  padding-right: 1rem;
}

.copyIcon {
  padding-left: 0.625rem;
  cursor: pointer;
}

.diff-cell {
  color: @yn-warning-color;
}

.empty {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: @yn-component-background;
  margin: 0;
}

.addMergeGroup {
  margin-right: 0.625rem;
}

.disabled {
  cursor: not-allowed;
  color: @yn-disabled-color;
}

.copy-btn-shadow {
  position: absolute;
  z-index: -1;
  display: inline-block;
  width: @rem30;
  height: @rem30;
}

.finallyNum-cell,
/deep/ .systemCalc-cell {
  text-align: right;
  padding-right: @yn-padding-xl;
}

/deep/ .handsontable {
  /deep/ td.finallyNum-cell,
  /deep/ td.systemCalc-cell {
    text-align: right;
    padding-right: @yn-padding-xl;
  }
}
</style>
