<template>
  <div class="graph_content cs-body-headerComponent">
    <yn-spin :spinning="spinning">
      <graph
        :graphData="graphData"
        :graphConfig="graphConfig"
        :dropDownMenu="dropDownMenu"
        :pageType="$data.$pageType"
        :pageDimObj="chartPageDim"
      />
    </yn-spin>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-page-title/";
import Graph from "@/components/ui/graph";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import organizationServer from "@/services/organization";
import { mapState } from "vuex";
import cloneDeep from "lodash/cloneDeep";
import pageFiled from "../../constant";
export default {
  name: "OrganizationGraph",
  components: {
    Graph
  },
  props: {
    rootSpinning: {
      type: <PERSON><PERSON><PERSON>,
      default: () => false
    }
  },
  data() {
    return {
      cacheRequestParam: {}, // 查询的时候做缓存数据处理
      graphData: {},
      graphConfig: {
        defaultLineShape: 4,
        defaultNodeWidth: "200",
        defaultNodeHeight: "60",
        defaultNodeColor: "#0052cc1a",
        defaultNodeFontColor: "#2A2A2A",
        defaultNodeFontWeight: "600",
        defaultNodeRadius: "4px",
        defaultLineFontColor: "#0052CC",
        defaultLineFontWeight: "600",
        defaultLineColor: "#BCC1CC",
        defaultLineFontOffset: { x: 20, y: 0 }
      },
      dropDownMenu: [
        // TODO 二期在处理
        // {
        //   id: "showNonParentCompanyShareholding",
        //   name: "显示非母公司持股"
        // }
      ],
      spinning: false,
      $pageType: pageFiled.ORGANIZATION_MANAGEMENT_VIEW
    };
  },
  computed: {
    ...mapState("organization", ["pageDimObj"]),
    chartPageDim() {
      const {
        versionVal: version,
        yearVal: year,
        monthVal: period
      } = this.pageDimObj;
      return { version, year, period };
    }
  },
  watch: {
    pageDimObj: {
      handler() {
        this.getChartData();
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    getChartData() {
      const {
        mergeGroupVal: scopeMemberId,
        monthVal: period,
        versionVal: version,
        yearVal: year
      } = this.pageDimObj;
      if (scopeMemberId === "allScope") {
        return;
      }
      this.spinning = true;
      const allSelected = scopeMemberId && period && version && year;
      if (!allSelected) {
        UiUtils.errorMessage(this.$t_structures("select_page_dimension_first"));
        this.spinning = false;
        return;
      }
      if (this.isEquivalent(this.pageDimObj, this.cacheRequestParam)) return;
      this.cacheRequestParam = cloneDeep(this.pageDimObj);
      organizationServer("getEquityChart", {
        scopeMemberId,
        period,
        version,
        year
      })
        .then(res => {
          this.spinning = false;
          this.graphData = res.data;
        })
        .catch(() => {
          this.spinning = false;
          this.graphData = {
            rootId: null,
            links: null,
            nodes: []
          };
        })
        .finally(() => {
          this.$emit("update:rootSpinning", false);
        });
    },
    isEquivalent(obj1, obj2) {
      const keys1 = Object.keys(obj1);
      const keys2 = Object.keys(obj2);
      if (keys1.length !== keys2.length) return false;
      const res = keys1.every(key => {
        return obj1[key] === obj2[key];
      });
      return res;
    }
  }
};
</script>
<style lang="less">
.graph-chart-cont,
.seeks-layout-tree {
  background: @yn-component-background !important;
}
</style>
<style lang="less" scoped>
/deep/.ant-spin-nested-loading {
  height: calc(100% - 94px) !important;
}
/deep/.yn-page-title {
  background: @yn-component-background;
}
.graph_content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: column;
  overflow: hidden;
  background: @yn-component-background;
  border-top-left-radius: @yn-console-content-radius !important;
  border-top-right-radius: @yn-console-content-radius !important;
}
.graph_dim_cont {
  padding: 17px @rem32 0 @rem16;
  background: @yn-component-background;
}
.graph_chart_cont {
  height: calc(100vh - 130px);
  background: @yn-background-color;
  position: relative;
  padding: @rem16 @rem32;
}
.toolBar_right {
  color: @yn-text-color-secondary;
  .chartBtns {
    font-size: @rem16;
    padding: @rem8;
    cursor: pointer;
  }
}
.slider {
  height: 32px;
  line-height: 32px;
  background: @yn-body-background;
  width: 216px;
  border: 1px solid @yn-border-color-base;
  border-radius: 4px;
  padding: 0 0 0 12px;
  font-size: 12px;
  color: @yn-label-color;
  display: inline-block;
  & .sliderCont {
    width: 135px;
    display: inline-block;
    height: 22px;
    position: relative;
    top: -5px;
    margin-right: 8px;
  }
  & > span {
    display: inline-block;
    width: 30px;
    text-align: center;
    position: relative;
    top: -2px;
  }
  .sliderBtn {
    position: relative;
    top: -2px;
  }
}
.dropMenu {
  font-size: 16px;
  color: @yn-text-color-secondary;
  cursor: pointer;
}
.tb,
.lr {
  margin-right: 5px;
}
.tb {
  transform: rotate(90deg);
}
</style>
