<template>
  <yn-empty
    v-if="!hasPermisson && !spinning"
    :description="$t_structures('do_not_have_permission_contact_administrator')"
    :image="require('@/image/noPrivilige.png')"
    class="noPermissionPage"
  />
  <div v-else style="height: 100%">
    <div class="organizational_cont">
      <yn-spin :spinning="spinning">
        <div
          :class="{ organizational_head_cont: true, showGraph: isShowGraph }"
        >
          <organization-header
            :spinning.sync="spinning"
            :saveTableMessage="saveTableMessage"
            @changeGraphViewState="changeGraphViewState"
          />
        </div>
        <yn-lc-layout
          v-if="!isShowGraph"
          iconPosition="center"
          class="organizational_main cs-body-headerComponent"
          @onExtended="onExtended"
        >
          <div slot="left" ref="organizationTree" class="left-cont">
            <organization-left
              ref="organizationTree"
              :pageDimObj="pageDimObj"
              :rootSpinning.sync="spinning"
              :loadTableData="loadTableData"
              :getTableRef="getTableRef"
            />
          </div>
          <div slot="center">
            <organization-right
              ref="organizationTable"
              :loadTableData.sync="loadTableData"
              @getSaveMessage="getSaveMessage"
              @changeLoadingStatus="changeLoadingStatus"
            />
          </div>
        </yn-lc-layout>
        <OrganizationGraph
          v-if="isShowGraph"
          :pageDimParams="pageDimObj"
          :rootSpinning.sync="spinning"
          @changeGraphViewState="changeGraphViewState"
        />
      </yn-spin>
    </div>

    <add-member
      v-if="dimensionTransferInfo.visible"
      :ifEmptySelectedAll="false"
      :closeAnalysis="true"
      :dimInfo="dimInfo"
      :filterSharedMember="true"
      :tipsWord="tipsWord"
      :closeTransfer="closeTransfer"
    />
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-lc-layout/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-empty/";
import OrganizationHeader from "./organizationHeader";
import OrganizationLeft from "./organizationLeft";
import OrganizationRight from "./organizationRight/";
import OrganizationGraph from "./organizationGraph";
import defaultParams from "@/mixin/defaultParams";
import permissionSet from "./permissionSet";
import AddMember from "@/components/hoc/newDimensionTransfer";
import { TRANSFER_TIPS_WORD } from "@/constant/common.js";
import { ADDNODETYPE } from "../constant";
import { mapActions, mapGetters, mapMutations, mapState } from "vuex";
import organizationServer from "@/services/organization";
import cloneDeep from "lodash/cloneDeep";
// import noPrivilige from "";

export default {
  name: "Organizational",
  directives: {
    permissionSet
  },
  components: {
    OrganizationHeader,
    OrganizationLeft,
    OrganizationRight,
    OrganizationGraph,
    AddMember
    // noPrivilige
  },
  mixins: [defaultParams],
  data() {
    return {
      spinning: false,
      isShowGraph: false,
      saveTableMessage: [], //  保存表格信息
      tipsWord: TRANSFER_TIPS_WORD.shareMember(),
      loadTableData: false
    };
  },
  computed: {
    ...mapState("organization", [
      "dimensionTransferInfo",
      "pageDimObj",
      "treeData",
      "selectedTreeNode",
      "mapTreeData"
    ]),
    ...mapGetters("organization", ["authObj"]),
    hasPermisson() {
      const permissionVal = Object.values(this.authObj);
      if (permissionVal.length) {
        return permissionVal.some(item => item);
      } else {
        return false;
      }
    },
    dimInfo() {
      return cloneDeep(this.dimensionTransferInfo.dimInfo);
    }
  },
  watch: {
    pageDimObj: {
      handler() {
        this.saveTableMessage.splice(0, this.saveTableMessage.length);
        if (!this.isShowGraph) {
          this.spinning = true;
        }
      },
      deep: true,
      immediate: true
    }
  },
  async created() {
    await this.getOffsetRule();
    await this.getPermissionSet();
    this.$nextTick(() => {
      this.attachResize();
    });
  },
  methods: {
    ...mapMutations("organization", [
      "setDimensionTransferInfo",
      "setSelectedTreeNode",
      "setPageDimVal"
    ]),
    ...mapActions("organization", [
      "getPermissionSet",
      "getOffsetRule",
      "getRootTreeData",
      "getChildNodeByPId",
      "updateOrganizationEntityChildNum"
    ]),
    attachResize() {
      const that = this;
      const callback = (entries, observer) => {
        that.onExtended();
      };
      this.observer = new ResizeObserver(callback);
      this.$refs.organizationTree &&
        this.observer.observe(this.$refs.organizationTree);
    },
    changeLoadingStatus(status) {
      this.spinning = status;
    },
    changeGraphViewState(state) {
      this.isShowGraph = state;
    },
    getSaveMessage(data) {
      this.saveTableMessage = data;
    },
    getTableRef() {
      return this.$refs.organizationTable;
    },
    // 关闭 穿梭框，新增节点 ，并刷新
    async closeTransfer(expressionObj) {
      // 有表达式，走新增逻辑
      if (expressionObj) {
        const { memberType } = expressionObj;
        expressionObj = { member: memberType, ...expressionObj };
        delete expressionObj.memberType;
        await this.addChildNodeByExpObj(expressionObj);
      }
      this.setDimensionTransferInfo({
        visible: false
      });
    },
    // 通过表达式对象，添加树节点
    async addChildNodeByExpObj(expObj) {
      const { treeNode, type } = this.dimensionTransferInfo;
      const targetNode = this.getTargetTreeNode();
      const { key, parentId: pId } = targetNode;
      const isAddOrganization = type.indexOf("GROUP") === -1;
      const methodName = this.getAddNodeMethodName();
      const params = this.getAddNodeParams(targetNode);
      try {
        await organizationServer(methodName, {
          ...params,
          dimMemberExps: JSON.stringify(expObj)
        });
      } catch {
        return;
      }
      // 当前页面维 合并组下面 没有子项时，添加完子项刷新整颗树
      this.setSelectedTreeNode("");
      const { mergeGroupVal } = this.pageDimObj;
      if (mergeGroupVal === key) {
        await this.getRootTreeData({ hasFirst: false, isKeepChildren: true });
        this.$nextTick(() => {
          if (key !== "allScope") {
            this.setSelectedTreeNode(this.treeData[0].objectId);
          }
        });
      } else {
        // 刷节点（添加类型为同级时，刷新父项下面的子项，否则刷新当前父项）下面的子项
        await this.getChildNodeByPId(
          type === "SAMELEVELGROUP" ? pId : params.id
        );
        this.setSelectedTreeNode(targetNode.objectId);
      }
      if (isAddOrganization) {
        // 添加 组织，则需要刷新穿透 父项的子项个数
        if (mergeGroupVal !== key) {
          this.updateOrganizationEntityChildNum(
            methodName === "addSameLevelNode" ? treeNode.parentId : params.id
          );
        }

        const saveCb = this.$refs.organizationTable.saveTableData;
        // 添加组织以后 需要添加提示保存
        this.addCallBackFnMixin(saveCb);
      }
      // 选中当前节点
      this.setSelectedTreeNode(targetNode.objectId);
    },
    getTargetTreeNode() {
      const { mergeGroupVal } = this.pageDimObj;
      const { treeNode, type } = this.dimensionTransferInfo;
      const { hasRoot } = this.selectedTreeNode || {};
      let targetTreeNode;
      if (Object.keys(treeNode).length) {
        targetTreeNode = treeNode;
      } else if (mergeGroupVal === "allScope") {
        // “全部成员” 添加 合并组时，总是添加到根节点上
        if (type.indexOf("GROUP") !== -1) {
          targetTreeNode = {
            key: mergeGroupVal,
            objectId: mergeGroupVal
          };
        } else if (hasRoot) {
          // 添加 组织时，总是添加到对应合并组的根节点
          targetTreeNode = this.selectedTreeNode;
        } else {
          targetTreeNode = this.mapTreeData[
            this.getRootId(this.selectedTreeNode)
          ];
        }
      } else {
        targetTreeNode = this.treeData[0];
      }
      return targetTreeNode;
    },
    getRootId(treeNode) {
      let hasRoot = false;
      let tempTreeNode = treeNode;
      while (!hasRoot) {
        tempTreeNode = this.mapTreeData[tempTreeNode.parentId];
        hasRoot = tempTreeNode.hasRoot;
      }
      return tempTreeNode.objectId;
    },
    getAddNodeMethodName() {
      const [sameLevel] = ADDNODETYPE;
      return this.dimensionTransferInfo.type === sameLevel
        ? "addSameLevelNode"
        : "addChildLevelNode";
    },
    getAddNodeParams(targetNode) {
      const {
        monthVal: period,
        versionVal: version,
        yearVal: year,
        mergeGroupVal
      } = this.pageDimObj;
      const mergeGroupArr = ADDNODETYPE.slice(0, 2);
      const params = {
        period,
        version,
        year,
        id: targetNode.key,
        scopeMember:
          mergeGroupArr.indexOf(this.dimensionTransferInfo.type) !== -1
      };
      if (mergeGroupVal === "allScope" && targetNode.key === "allScope") {
        delete params.id;
        params.allScope = true;
      }
      return params;
    },
    onExtended() {
      setTimeout(
        self => {
          try {
            self.$refs.organizationTable.$refs.grid.$refs.simpleGrid.updateSettings();
          } catch (e) {}
        },
        300,
        this
      );
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.organizational_cont .ant-spin-nested-loading,
/deep/.organizational_cont .ant-spin-container {
  height: 100%;
  display: flex;
  flex-flow: column;
}
/deep/#centerDiv > div {
  height: 100%;
  background: @yn-background-color;
}
.left-cont {
  height: 100%;
}
.organizational_cont {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  position: relative;
  .showGraph {
    padding-left: 0;
  }
  .organizational_main {
    display: flex;
    flex-flow: row nowrap;
    border-top-left-radius: @yn-console-content-radius !important;
    border-top-right-radius: @yn-console-content-radius !important;
  }
}
.noPermissionPage {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-flow: column;
}
</style>
