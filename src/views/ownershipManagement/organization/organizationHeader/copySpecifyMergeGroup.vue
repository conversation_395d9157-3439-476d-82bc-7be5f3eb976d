<template>
  <yn-modal
    :title="$t_structures('specify_merge_group')"
    :visible="true"
    width="31.5rem"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <yn-spin :spinning="spinning">
      <yn-checkbox @change="handleSelectAll">
        {{ $t_common("select_all") }}
      </yn-checkbox>
      <yn-tree
        :checkedKeys="checkedKeys"
        checkable
        :treeData="treeData"
        :itemexpandable="itemExpandable"
        @check="onCheck"
      />
    </yn-spin>
    <template slot="footer">
      <yn-button key="back" @click="handleCancel">
        {{ $t_common("cancel") }}
      </yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="confirmLoading"
        @click="handleOk"
      >
        {{ $t_common("ok") }}
      </yn-button>
    </template>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-checkbox/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-tree/";
import organizationServer from "@/services/organization";
import UiUtils from "yn-p1/libs/utils/UiUtils";
export default {
  name: "CopySpecifyMergeGroup",
  props: {
    copyShareholdingRatioEvent: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      treeData: [],
      confirmLoading: false,
      checkedKeys: [],
      allTreeNodeId: [],
      mapTreeData: {},
      spinning: true
    };
  },
  async mounted() {
    await this.requestTreeData();
    this.spinning = false;
  },
  methods: {
    async requestTreeData() {
      await organizationServer("getScopeTree").then(res => {
        const { data = [] } = res;
        this.treeData = this.ergodicTreeData(data);
      });
    },
    ergodicTreeData(data) {
      const res = [...data];
      const allTreeNodeId = [];
      const mapTreeData = {};
      while (res.length) {
        const currTreeNode = res.shift();
        const { id, name, children } = currTreeNode;
        currTreeNode.key = id;
        currTreeNode.title = name;
        delete currTreeNode.scopedSlots;
        allTreeNodeId.push(id);
        mapTreeData[id] = currTreeNode;
        if (children && children.length) {
          Array.prototype.push.apply(res, children);
        }
      }
      this.$set(this, "mapTreeData", mapTreeData);
      this.$set(this, "allTreeNodeId", allTreeNodeId);
      return data;
    },
    async handleOk() {
      if (!this.checkedKeys.length) {
        UiUtils.errorMessage(this.$t_structures("merge_group_cannot_be_empty"));
        return;
      }
      this.confirmLoading = true;
      const checkedChildrenIds = this.getCheckedChildrenIds();
      await this.copyShareholdingRatioEvent(checkedChildrenIds).then(
        res => {
          this.handleCancel();
        },
        rej => {
          this.confirmLoading = false;
        }
      );
    },
    getCheckedChildrenIds() {
      return this.checkedKeys.filter(id => {
        return !this.mapTreeData[id].children;
      });
    },
    handleCancel() {
      this.$emit("update:isShowCopySpecMerGroupModal", false);
    },
    handleSelectAll(isChecked) {
      this.checkedKeys = isChecked ? this.allTreeNodeId : [];
    },
    onCheck(checkIds) {
      this.checkedKeys = checkIds;
    },
    itemExpandable(item) {
      const { isLeaf } = item;
      return !isLeaf;
    }
  }
};
</script>
