<template>
  <div class="organizational_head">
    <yn-page-title
      :allowBack="showBack"
      :title="$t_process('organization_structure')"
      @back="showGraph(false)"
    >
      <template v-slot:extraRender>
        <div class="titleExtraRender">
          <div class="pageDimSelectCont">
            <yn-divider type="vertical" class="titleDivider" />
            <PageDimSelect
              ref="pageDim"
              :dimInfo="pageDimInfo.slice(0, 3)"
              class="pageDimSelect firstPageDimSelect"
              @changeDimVal="changeDimVal"
            />
            <PageDimSelect
              ref="pageDimScope"
              :dimInfo="pageDimInfo.slice(3)"
              class="pageDimSelect"
              @changeDimVal="changeDimVal"
            />
          </div>
          <div v-if="!showBack" class="btnsCont">
            <yn-message-feedback
              v-if="showTableMessage"
              class="messageBox"
              :messageList="saveTableMessage"
            />
            <yn-button
              v-if="hasEditPermission"
              type="primary"
              @click="saveEvent"
            >
              {{ $t_structures("save_monthly_structure") }}
            </yn-button>
            <yn-button
              v-if="hasCopyPermission"
              :type="copyMonthlySchema"
              @click="copyEvent(true)"
            >
              {{ $t_structures("copy_monthly_structure") }}
            </yn-button>
            <yn-button :type="viewEquityStructure" @click="showGraph(true)">
              {{ $t_structures("view_ownership") }}
            </yn-button>
            <yn-divider type="vertical" class="titleDivider" />
            <div class="iconBtns">
              <yn-dropdown v-if="hasEditPermission">
                <yn-tooltip placement="left" :arrowPointAtCenter="true">
                  <template slot="title">
                    <div class="content-width">
                      <span
                        v-for="(text, index) in copyPromptMessage"
                        :key="index"
                      >
                        {{ text }}<br />
                      </span>
                    </div>
                  </template>
                  <svg-icon type="icon-copy1" class="iconBtn copy-icon" />
                </yn-tooltip>
                <yn-menu slot="overlay" @click="handleCopy">
                  <yn-menu-item key="copyCurrMergeGroup">
                    {{ $t_structures("copy_current_scopes") }}
                  </yn-menu-item>
                  <yn-menu-item key="showCopySpecifyMergeGroupModal">
                    {{ $t_structures("copy_selected_scope") }}
                  </yn-menu-item>
                </yn-menu>
              </yn-dropdown>
              <svg-icon
                :title="$t_common('refresh')"
                type="icon-shuaxin"
                class="iconBtn"
                @click="refreshEvent"
              />
              <!-- 导入 -->
              <svg-icon
                v-if="hasImportPermission"
                type="icon-bianzubeifen"
                class="export iconBtn"
                :title="$t_common('import')"
                @click="importFile"
              />
              <!-- 导出 -->
              <yn-dropdown
                v-if="hasExportPermission"
                v-model="showExportMenu"
                :trigger="['click']"
              >
                <yn-tooltip placement="left" :arrowPointAtCenter="true">
                  <template slot="title">
                    <div class="content-width">
                      {{ $t_common("export") }}
                    </div>
                  </template>
                  <svg-icon
                    class="search-icon-export iconBtn"
                    type="icon-a-c1_cr_export-dropdown"
                    @click.stop
                  />
                </yn-tooltip>
                <yn-menu slot="overlay" @click="handleExport">
                  <yn-menu-item
                    v-for="item in $data.$menuList"
                    :key="item.orgType"
                  >
                    {{ item.title }}
                  </yn-menu-item>
                </yn-menu>
              </yn-dropdown>
              <!-- 表格设置 -->
              <yn-popover
                v-model="visible"
                :title="`${$t_process('scopes')}/${$t_process('entity')}`"
                trigger="click"
                placement="bottomRight"
              >
                <template slot="content">
                  <yn-radio-group v-model="cellType" @change="changeColumn">
                    <yn-radio value="NAME">{{ $t_common("name") }}</yn-radio>
                    <yn-radio value="CODE">
                      {{ $t_structures("unique_identifier") }}
                    </yn-radio>
                    <yn-radio value="NAME_AND_CODE">
                      {{ $t_common("name") }}+{{
                        $t_structures("unique_identifier")
                      }}
                    </yn-radio>
                  </yn-radio-group>
                  <br />
                </template>
                <svg-icon
                  type="icon-set-up"
                  class="iconBtn"
                  :title="$t_common('settings')"
                />
              </yn-popover>
            </div>
          </div>
        </div>
      </template>
    </yn-page-title>
    <copy-month-structure
      v-if="showCopyMothStruModal"
      :title="$t_structures('copy_monthly_structure')"
      :pageDimObj="organizationPageDimObj"
      @copyAfterEvent="copyAfterEvent"
      @closeCopyModal="copyEvent"
    />
    <export-organization
      v-if="exportOrganizationVisible"
      :selectDimVisible.sync="exportOrganizationVisible"
      :hasTemplate="false"
      :orgType="orgType"
      :cellType="cellType"
      :pageDimObj="organizationPageDimObj"
      importType="organization"
    />
    <import-organization
      ref="importOrganization"
      importType="organization"
      :pageDimObj="organizationPageDimObj"
      @refreshTable="refreshEvent"
    />
    <copy-specify-merge-group-modal
      v-if="isShowCopySpecMerGroupModal"
      :isShowCopySpecMerGroupModal.sync="isShowCopySpecMerGroupModal"
      :copyShareholdingRatioEvent="copyShareholdingRatioEvent"
    />
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-popover/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-radio/";
import "yn-p1/libs/components/yn-checkbox/";
import "yn-p1/libs/components/yn-message-feedback/";
import "yn-p1/libs/components/yn-breadcrumb/";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-page-title/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import CopyMonthStructure from "@/views/ownershipManagement/copyMonthStructure.vue";
import PageDimSelect from "@/components/hoc/pageDimSelect/index.vue";
import ExportOrganization from "../../importOwnership/dimSelectDownload.vue";
import ImportOrganization from "../../importOwnership";
import CopySpecifyMergeGroupModal from "./copySpecifyMergeGroup.vue";
import { EventBus } from "yn-p1/libs/utils/ComponentUtils";
import organizationServer from "@/services/organization";
import commonService from "@/services/common";
import pageFiled, { PERMISSION_NAME_MAPPING } from "../../constant";
import { mapState, mapGetters, mapActions } from "vuex";
import lang from "@/mixin/lang";
const { $t_structures } = lang;
const dimCodeMapObj = {
  Period: "monthVal",
  Version: "versionVal",
  Year: "yearVal",
  Scope: "mergeGroupVal"
};
export default {
  name: "OrganizationHeader",
  inject: ["defaultParams"],
  components: {
    CopyMonthStructure,
    PageDimSelect,
    ExportOrganization,
    ImportOrganization,
    CopySpecifyMergeGroupModal
  },
  props: {
    saveTableMessage: {
      type: Array,
      default: () => []
    },
    spinning: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      versionVal: "",
      yearVal: "",
      monthVal: "",
      mergeGroupVal: "",
      hasChange: false, // 是否手动变更过
      showCopyMothStruModal: false, // 展示保存阅读架构
      cellType: "",
      preCellType: "", // 上一次的值
      showTableMessage: false,
      exportOrganizationVisible: false,
      visible: false,
      objectId: null, // 表格列记忆
      showExportMenu: false,
      orgType: 1,
      $menuList: [
        {
          orgType: 1,
          title: $t_structures("hierarchical_architecture")
        },
        {
          orgType: 0,
          title: $t_structures("parallel_architecture")
        },
        {
          orgType: 2,
          title: $t_structures("hierarchy_structure")
        }
      ],
      isShowCopySpecMerGroupModal: false,
      showBack: false,
      showSpecialScope: true,
      pageDimInfo: []
    };
  },
  computed: {
    ...mapState("organization", {
      toBeSavedObj: state => state.toBeSavedObj,
      authData: state => state.authData,
      pageDimObj: state => state.pageDimObj,
      offsetRule: state => state.offsetRule
    }),
    ...mapGetters("organization", ["authObj"]),
    hasEditPermission() {
      return this.authObj[PERMISSION_NAME_MAPPING.edit];
    },
    hasCopyPermission() {
      return this.authObj[PERMISSION_NAME_MAPPING.copy];
    },
    hasImportPermission() {
      return this.authObj[PERMISSION_NAME_MAPPING.import];
    },
    hasExportPermission() {
      return this.authObj[PERMISSION_NAME_MAPPING.export];
    },
    viewEquityStructure() {
      const { hasCopyPermission, hasEditPermission } = this;
      if (!hasCopyPermission && !hasEditPermission) {
        return "primary";
      }
      return "default";
    },
    copyMonthlySchema() {
      if (!this.hasEditPermission) {
        return "primary";
      }
      return "default";
    },
    copyPromptMessage() {
      const word = {
        parallel: [
          this.$t_structures("copy_current_scope_all"),
          this.$t_structures("copy_refer_scope_al")
        ],
        level: [
          this.$t_structures("copy_current_scope_less"),
          this.$t_structures("copy_refer_scope_less")
        ]
      };
      return word[this.offsetRule];
    },
    organizationPageDimObj() {
      return this.pageDimInfo.reduce((pre, next) => {
        pre[dimCodeMapObj[next.dimCode]] = next.dimMemberId;
        return pre;
      }, {});
    }
  },
  watch: {
    saveTableMessage(newVal) {
      this.showTableMessage = newVal && newVal.length > 0;
    },
    visible(newVal) {
      if (!newVal) {
        commonService("saveOrUpdateUserSetting", {
          value: this.cellType,
          objectId: this.objectId,
          tag: "userIndexName",
          key: pageFiled.PAGE_ORGANIZATION_LIST
        });
      }
    },
    showBack: {
      handler(newVal) {
        this.pageDimInfo[3].attr.hasAllMembers = !newVal;
      }
    }
  },
  beforeCreate() {
    commonService("getLastSettingV", {
      key: pageFiled.PAGE_ORGANIZATION_LIST, // 持股方
      tag: "userIndexName"
    }).then(res => {
      const { objectId, value } = res.data ? res.data.data : {};
      this.objectId = objectId;
      this.preCellType = value;
      this.cellType = value || "NAME";
      this.$store.commit("organization/changeTableConfig", {
        cellType: this.cellType
      });
    });
  },
  async created() {
    await this.initPageDimInfo();
  },
  methods: {
    ...mapActions("organization", ["getPermissionSet", "getOffsetRule"]),
    ...mapActions("organization", ["getPermissionSet"]),
    async initPageDimInfo() {
      const povObj = {};
      await this.selectUserPov(data => {
        if (!data) return;
        Object.keys(data).forEach(key => {
          povObj[key] = data[key].memberId;
        });
      });
      // pov和页面传参，四个维度
      const pageDimInfo = Object.assign(
        {},
        povObj,
        this.hasChange ? {} : this.defaultParams
      );
      const dimOrder = ["version", "year", "period", "scope"];
      const dimCodes = ["Version", "Year", "Period", "Scope"];
      const params = dimOrder.map((key, index) => {
        return {
          dimCode: dimCodes[index],
          dimMemberId: pageDimInfo[key],
          needPermission: key === "scope"
        };
      });
      await this.setFinalDimMember(params);
    },
    async setFinalDimMember(params) {
      const dimCodes = ["Version", "Year", "Period", "Scope"];
      // 过滤掉前端添加的合并组全部成员
      const requestParams = params.filter(item => {
        const { dimCode, dimMemberId } = item;
        return !(dimCode === "Scope" && dimMemberId === "allScope");
      });
      // 合并组添加的全部成员不参与校验, 校验维度是非存在，防止被并发删除
      const { data } = await commonService("transPageDim", requestParams);
      const mapObj = {};
      data.forEach(item => {
        const {
          dimCode,
          objectId: dimMemberId,
          dimName,
          dimMemberRealName: dimMemberName
        } = item;
        mapObj[dimCode] = {
          dimMemberId,
          dimCode,
          dimName,
          dimMemberName
        };
        this.$store.commit("organization/setPageDimVal", [
          dimCodeMapObj[dimCode],
          dimMemberId
        ]);
      });
      // 因为scope 没有参与校验，response data 没有scope全部成员，手动添加到map
      if (requestParams.length !== params.length) {
        mapObj["Scope"] = {
          dimCode: "Scope",
          dimMemberId: "allScope",
          dimMemberName: this.$t_common("all_members"),
          dimName: this.$t_common("scope")
        };
        this.$store.commit("organization/setPageDimVal", [
          dimCodeMapObj["Scope"],
          "allScope"
        ]);
      }
      this.pageDimInfo = dimCodes.map(key => {
        return {
          attr: {
            hasAllMembers: key === "Scope" && this.showSpecialScope,
            needPermission: key === "Scope"
          },
          ...mapObj[key]
        };
      });
    },
    handleExport(e) {
      const { key } = e;
      Object.assign(this, {
        exportOrganizationVisible: true,
        showExportMenu: false, // 关闭菜单
        orgType: key
      });
    },
    handleCopy({ key: eventName }) {
      this[eventName] && this[eventName]();
    },
    closeCopySpecifyMergeGroupModal() {
      this.isShowCopySpecMerGroupModal = false;
    },
    showCopySpecifyMergeGroupModal() {
      this.isShowCopySpecMerGroupModal = true;
    },
    async copyShareholdingRatioEvent(ids) {
      await new Promise((resvole, reject) => {
        this.savePromptMixin().then(
          () => {
            this.$emit("update:spinning", true);
            const {
              versionVal: version,
              monthVal: period,
              yearVal: year
            } = this.$store.state.organization.pageDimObj;
            organizationServer("batchScopeCopyShareholding", {
              version,
              period,
              year,
              scopeMemberIdList: ids
            }).then(res => {
              resvole();
              this.$emit("update:spinning", false);
              EventBus.trigger("refreshTable");
            });
          },
          rej => {
            reject(rej);
          }
        );
      });
    },
    // 复制当前合并组 批量复制系统持股到 最终持股，复制完成以后刷新表格
    copyCurrMergeGroup() {
      const COPY_TITLE = {
        parallel: this.$t_structures(
          "copy_system_shareholding_ratio_to_shareholding"
        ),
        level: this.$t_structures("will_proportion_of_controlling")
      };
      UiUtils.confirm({
        title: this.$t_structures("confirm_to_copy_all"),
        content: COPY_TITLE[this.offsetRule],
        okText: this.$t_common("ok"),
        okType: "primary",
        cancelText: this.$t_common("cancel"),
        onOk: () => {
          this.savePromptMixin().then(() => {
            this.$emit("update:spinning", true);
            const {
              versionVal: version,
              monthVal: period,
              yearVal: year,
              mergeGroupVal: id
            } = this.$store.state.organization.pageDimObj;
            let methodName = "batchCopyShareholding";
            const requestParams = {
              version,
              period,
              year,
              id
            };
            if (id === "allScope") {
              methodName = "batchScopeCopyShareholding";
              delete requestParams.id;
              const treeData = this.$store.state.organization.treeData || [];
              requestParams.scopeMemberIdList = treeData.map(item => {
                return item.memberId;
              });
            }
            organizationServer(methodName, requestParams).then(res => {
              this.$emit("update:spinning", false);
              EventBus.trigger("refreshTable");
            });
          });
        }
      });
    },
    saveEvent() {
      EventBus.trigger("saveTableData");
    },
    copyEvent(isShow) {
      this.showCopyMothStruModal = isShow;
    },
    changeColumn(e) {
      const cellType = e.target.value;
      this.savePromptMixin().then(
        () => {
          this.cellType = cellType;
          this.preCellType = cellType;
          this.$store.commit("organization/changeTableConfig", {
            cellType
          });
        },
        rej => {
          this.cellType = this.preCellType;
        }
      );
    },
    // 刷新事件
    refreshEvent() {
      this.savePromptMixin().then(async () => {
        await this.refreshFn();
      });
    },
    async refreshFn() {
      this.$emit("update:spinning", true);
      const params = this.pageDimInfo.map(item => {
        const {
          dimCode,
          dimMemberId,
          attr: { needPermission }
        } = item;
        return {
          dimCode,
          dimMemberId,
          needPermission
        };
      });
      await Promise.all([
        this.getOffsetRule(),
        this.getPermissionSet(),
        this.$refs.pageDim.refresh(),
        this.$refs.pageDimScope.refresh(),
        this.setFinalDimMember(params)
      ]);
      // 清除待保存项
      this.$store.commit("organization/clearToBeSaveObj");
      this.$store.commit("organization/setRefreshTreeStatus", true);
      this.$emit("update:spinning", false);
    },
    showGraph(status) {
      this.savePromptMixin().then(async () => {
        this.showSpecialScope = !status;
        this.initPageDimInfo();
        this.showBack = status;
        this.$emit("changeGraphViewState", status);
      });
    },
    changeDimVal(values = {}, valuesArr = []) {
      // 切换年版本期间，则为手动变更，合并组不关注
      if (valuesArr.length === 3) {
        this.hasChange = true;
      }
      const valuesMap = valuesArr.reduce((pre, next) => {
        pre[next.dimCode] = next;
        return pre;
      }, {});
      Object.keys(values).forEach(key => {
        this.pageDimInfo.forEach(item => {
          if (item.dimCode === key) {
            item.dimMemberId = values[key];
            item.dimMemberName = valuesMap[key].dimMemberName;
          }
        });
        this.$set(this, "pageDimInfo", this.pageDimInfo);
        this.$store.commit("organization/setPageDimVal", [
          dimCodeMapObj[key],
          values[key]
        ]);
      });
    },
    importFile() {
      this.$refs.importOrganization.openImportModal();
    },
    setInitStatus(status) {
      this.$store.commit("organization/setDimSelectStatus", status);
    },
    async copyAfterEvent({ version, year, period }) {
      if (!version || !year || !period) return;
      const { monthVal, versionVal, yearVal } = this.pageDimObj;
      const dimInfo = [
        { dimCode: "Period", dimMemberId: monthVal },
        { dimCode: "Version", dimMemberId: versionVal },
        { dimCode: "Year", dimMemberId: yearVal }
      ];
      const members = await this.getDimMembers(dimInfo);
      const isCopyToCurrPage = members.every(item => {
        const { dimCode, dbCodeIndex } = item;
        if (dimCode === "Period") {
          return dbCodeIndex === period;
        } else if (dimCode === "Version") {
          return dbCodeIndex === version;
        } else if (dimCode === "Year") {
          return dbCodeIndex === year;
        }
      });
      // 是否复制到前页面维 下，如果是，则刷新页面
      if (isCopyToCurrPage) {
        this.refreshFn();
        this.clearCommonSaveEventsMixin();
      }
    },
    async getDimMembers(members) {
      const requestParams = {
        dimCode: "Version",
        dimMemberId: "",
        hasAllMembers: false,
        needPermission: false
      };
      const requstArr = members.map(item => {
        const { dimCode, dimMemberId } = item;
        return commonService(
          "getQueryDimMemberList",
          Object.assign({}, requestParams, { dimCode, dimMemberId })
        );
      });
      const res = await Promise.all(requstArr);
      return res.map(item => {
        return item.data.items[0];
      });
    }
  }
};
</script>
<style lang="less">
.organizational_head {
  .yn-page-title {
    margin: 0.25rem 0;
  }
  .titleExtraRender {
    display: flex;
    justify-content: space-between;
    align-items: center;
    & > .pageDimSelectCont {
      display: flex;
      align-items: center;
      & > .firstPageDimSelect {
        margin-left: 0;
      }
    }
  }
}
</style>
<style lang="less" scoped>
.titleDivider {
  margin: 0 0.5rem;
  height: 1rem;
}
.pageDimSelect {
  float: left;
  margin-left: @yn-margin-xl;
}
.messageBox {
  display: inline-block;
}
.btnsCont {
  button {
    margin-left: @rem8;
  }
  & > .iconBtns {
    float: right;
    height: @rem32;
    line-height: @rem32;
  }
  .iconBtn {
    cursor: pointer;
    color: @yn-label-color;
    float: left;
    margin-right: @rem4;
  }
  .iconBtn:last-child {
    margin-right: 0;
  }
  .copy-icon {
    /deep/.svg-icon {
      font-size: 2rem;
    }
  }
  .search-icon-export {
    /deep/.svg-icon {
      font-size: 2rem;
    }
  }
}
// }
.dimCont {
  margin-top: @rem14;
}
.messageBox {
  /deep/ .newsBubbleButton {
    .error {
      background: @yn-error-color;
    }
  }
}
</style>
