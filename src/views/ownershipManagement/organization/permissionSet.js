import { PERMISSION_NAME_MAPPING} from "../constant";
const directiveEvent = {
  init(el, binding, vnode) {
    const { modifiers } = binding;
    const { deleteMoreBtn, deleteMenuItem, deleteDivider } = modifiers;
    // 删除下拉菜单项或者按钮
    if (deleteMenuItem) {
      this.deleteMenuItem(el, binding, vnode);
    }
    // 删除树节点更多按钮
    if (deleteMoreBtn) {
      this.deleteMoreBtn(el, binding, vnode);
    }
    // 删除分割线
    if (deleteDivider) {
      this.deleteDivider(el, binding, vnode);
    }
  },
  /**
   * @desc 隐藏树节点上 下拉菜单按钮。1.根节点没有新增权限 2.禁用的树节点 3.没有新增、删除、编辑 权限任意一种删除“更多按钮”
   * @params el {DOM}
   * @params binding{Object} 是否是禁用节点
   *
  */
  deleteMoreBtn(el, binding, vnode) {
    const { value: treeNode } = binding;
    const { disabled: isDisableItem, hasRoot, hasLeaf} = treeNode; // isDisableItem 是否是禁用的节点
    const authObj = vnode.context.authObj || {};
    // 如果权限对象 为空 ，则权限接口 没有请求完成
    if (!Object.keys(authObj).length) {
      return;
    }
    const hasAddPermission = authObj[PERMISSION_NAME_MAPPING.add];
    const hasEditPermission = authObj[PERMISSION_NAME_MAPPING.edit];
    const hasDeletePermission = authObj[PERMISSION_NAME_MAPPING.delete];
    // 是否有新增、编辑、删除权限
    const hasPermission = hasAddPermission || hasEditPermission || hasDeletePermission;
    // 1.根节点没有新增权限
    const case1 = (hasRoot && !hasAddPermission);
    // 2.禁用的树节点
    const case2 = (isDisableItem);
    //  3.没有新增、删除、编辑 权限
    const case3 = (!hasPermission);
    // 4.叶子节点没有编辑、删除权限
    const case4 = (hasLeaf && !hasEditPermission && !hasDeletePermission);
    // 5.没有新增，编辑权限，有删除权限，并且节点上不允许删除
    const case5 = (!hasAddPermission && !hasEditPermission && hasDeletePermission && !treeNode.canDeleted);
    const tempCaseArr = [case1, case2, case3, case4, case5];
    if (tempCaseArr.includes(true)) {
      el.parentNode && el.parentNode.removeChild(el);
    }
  },
  deleteMenuItem(el, binding, vnode) {
    const { permissionName, treeNode } = binding.value;// 权限名称
    const authdObj = vnode.context.authObj || {};
    const hasPermisson = authdObj[permissionName];
    const { canDeleted } = treeNode || {};
    if (typeof hasPermisson !== "boolean") { return; }// 权限还没有请求回来
    // 1.没有对应的权限 2.删除的菜单项，树节点不允许出现删除菜单项（!canDeleted） 删除对应的菜单项
    if ((!hasPermisson) || (hasPermisson && permissionName === PERMISSION_NAME_MAPPING.delete && !canDeleted)) {
      el.parentNode && el.parentNode.removeChild(el);
    }
  },
  deleteDivider(el, binding, vnode) {
    vnode.context.$nextTick(() => {
      const nextDom = el.nextElementSibling;
      const prevDom = el.previousElementSibling;
      if (!prevDom) {
        el.parentNode && el.parentNode.removeChild(el);
      } else if (prevDom.className.indexOf("ant-divider") !== -1) {
        el.parentNode && el.parentNode.removeChild(el);
      }
      if (!nextDom) {
        el.parentNode && el.parentNode.removeChild(el);
      } else if (nextDom.className.indexOf("ant-divider") !== -1) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    });
  }
};
export default {
  componentUpdated: function(el, binding, vnode, oldVnode) {
    if (vnode.context.authData.length) {
      directiveEvent.init(el, binding, vnode);
    }
  },
  inserted: function(el, binding, vnode, oldVnode) {
    directiveEvent.init(el, binding, vnode);
  }
};
