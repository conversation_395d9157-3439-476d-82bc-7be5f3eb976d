<template>
  <yn-spin :spinning="spinning">
    <div class="organizational_left">
      <yn-tree-menu
        :key="treeMenuKey"
        :searchConfig="searchConfig"
        :operateData="buttonOperate"
        :treePanelSkeleton="treePanelSkeleton"
        :treeConfig="treeConfig"
        width="100%"
        @operateClick="handleOperateClick"
        @expand="handleExpand"
        @select="clickNode"
        @drop="onDrop"
      >
        <template slot="tree.custom" slot-scope="item">
          <span class="treeNodeCont">
            <span :title="item.treeNodeName" class="nodeName">
              {{ item.treeNodeName }}
            </span>
            <yn-dropdown
              v-permissionSet.deleteMoreBtn="item"
              placement="bottomRight"
              overlayClassName="tree-menu-dropdown"
              :trigger="['click']"
            >
              <yn-icon-button type="more" @click.stop />
              <yn-menu slot="overlay" @click="handleMenuClick($event, item)">
                <template v-for="(menuItem, index) in getTreeMenuByNode(item)">
                  <yn-menu-item
                    v-if="menuItem != 'splitLine'"
                    :key="`${menuItem.eventName}`"
                    v-permissionSet.deleteMenuItem="{
                      permissionName: menuItem.permission,
                      treeNode: item
                    }"
                    :disabled="isDisabled(item, menuItem)"
                    :menuItem="menuItem"
                    style="font-size: 12px; padding: 5px 30px 5px 15px"
                  >
                    {{ menuItem.menuName }}
                  </yn-menu-item>
                  <yn-divider
                    v-else
                    :key="`${menuItem}${index}_divider`"
                    v-permissionSet.deleteDivider
                    style="margin: 0"
                  />
                </template>
              </yn-menu>
            </yn-dropdown>
          </span>
        </template>
      </yn-tree-menu>
    </div>
  </yn-spin>
</template>
<script>
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-tree/";
import "yn-p1/libs/components/yn-tree-menu/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-spin/";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import savePromptMixin from "@/mixin/savePrompt.js";
import organizationServer from "@/services/organization";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import { TRANSFER_TIPS_WORD } from "@/constant/common.js";
import { mapState, mapGetters, mapMutations, mapActions } from "vuex";
import permissionSet from "../permissionSet";
import lang from "@/mixin/lang";
const { $t_structures, $t_common } = lang;
import {
  PERMISSION_NAME_MAPPING,
  ADDNODETYPE,
  DIM_INFO_ENTITY,
  DIM_INFO_SCOPE,
  TREE_MENU_ITEM_LEAF,
  TREE_MENU_ITEM_PARENT,
  TREE_MENU_ITEM_ROOT,
  TREE_MENU_ITEM_DELETE_SHARE
} from "../../constant";

const menuData = {
  rootNode: [...TREE_MENU_ITEM_ROOT(), ...TREE_MENU_ITEM_DELETE_SHARE()],
  parentNode: TREE_MENU_ITEM_PARENT(),
  leafNode: TREE_MENU_ITEM_LEAF()
};
// 树上的操作按钮
const BUTTON_OPERATE = [
  {
    key: "addSubsetGroup",
    label: $t_structures("add_scope"),
    overflow: false,
    type: "primary",
    group: "common"
  },
  {
    key: "addOrganization",
    label: $t_structures("add_entity"),
    overflow: false,
    group: `common`
  }
];
export default {
  name: "OrganizationLeft",
  directives: {
    permissionSet
  },
  mixins: [savePromptMixin],
  props: {
    isShowGraph: Boolean,
    getTableRef: {
      type: Function,
      default: () => {
        return () => {};
      }
    },
    rootSpinning: {
      type: Boolean,
      default: false
    },
    loadTableData: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const context = this;
    return {
      searchConfig: {
        props: {
          placeholder: $t_common("input_message")
        },
        events: {
          search(value) {
            context.handleSearch(value);
          }
        }
      },
      treePanelSkeleton: {
        loading: false
      },
      treeConfig: {
        selectedKeys: [],
        expandedKeys: [], // // 默认展开节点
        loadData: this.onLoadData,
        draggable: false,
        treeData: this.treeData
      },
      treeMenuKey: null, // 树菜单对应的Key，用来解决异步加载菜单时，二次无法请求的情况
      menuData,
      mergeGroupDim: "",
      addNodeType: "", // 枚举类型  ADDNODETYPE
      searchWord: "",
      rootId: "",
      spinning: false, //  loading
      buttonOperate: [],
      triggerMenuEventTreeNode: {}, // 触发菜单项事件的 树节点信息
      mergeGroupDimInfo: {}, // 合并组维度信息
      organizationDimInfo: {}, // 组织维度信息
      titleType: "name", // 树节点名称类型
      transferData: {},
      tipsWord: TRANSFER_TIPS_WORD.shareMember()
    };
  },
  computed: {
    ...mapState("organization", {
      tableConfig: state => state.tableConfig,
      pageDimObj: state => state.pageDimObj,
      authData: state => state.authData,
      mapTreeData: state => state.mapTreeData,
      treeData: state => state.treeData,
      selectedTreeNode: state => state.selectedTreeNode,
      refreshTree: state => state.refreshTree,
      dimSelectStatus: state => state.dimSelectStatus
    }),
    ...mapGetters("organization", ["authObj", "isAllPageDims"])
  },
  watch: {
    pageDimObj: {
      async handler(newVal) {
        const {
          mergeGroupVal: id,
          monthVal: period,
          versionVal: version,
          yearVal: year
        } = newVal;
        if (id && period && version && year) {
          this.treeMenuKey = `${id}_${period}_${version}_${year}`;
          await this.getRootTreeData().catch(() => {
            this.$emit("update:rootSpinning", false);
          });
          this.$emit("update:rootSpinning", false);
          this.clearCommonSaveEventsMixin();
          this.$set(this.treeConfig, "expandedKeys", []);
          this.rootId = id;
          this.mergeGroupDim = id;
        } else {
          if (this.dimSelectStatus) {
            this.$emit("update:rootSpinning", false);
          }
        }
      },
      deep: true,
      immediate: true
    },
    authObj: {
      handler() {
        const {
          mergeGroupVal: id,
          monthVal: period,
          versionVal: version,
          yearVal: year
        } = this.pageDimObj;
        this.treeMenuKey = `${id}_${period}_${version}_${year}_${AppUtils.generateUniqueId()}`;
        if (this.authObj[PERMISSION_NAME_MAPPING.add]) {
          this.buttonOperate = BUTTON_OPERATE;
        } else {
          this.buttonOperate = [];
        }
        if (this.authObj[PERMISSION_NAME_MAPPING.edit]) {
          this.treeConfig.draggable = true;
        } else {
          this.treeConfig.draggable = false;
        }
      },
      immediate: true
    },
    treeData: {
      handler() {
        this.$set(this.treeConfig, "treeData", [...this.treeData]);
      },
      deep: true
    },
    selectedTreeNode(newVal) {
      if (this.buttonOperate.length) {
        this.buttonOperate[1].group = `common ${
          this.pageDimObj.mergeGroupVal === "allScope" &&
          !Object.keys(newVal).length
            ? "disabled"
            : ""
        }`;
      }
      this.treeConfig.selectedKeys = [newVal.objectId];
    },
    refreshTree(newVal) {
      if (newVal) {
        this.treeConfig.selectedKeys = [];
        this.treeConfig.expandedKeys = [];
        this.getRootTreeData();
        this.setRefreshTreeStatus(false);
      }
    }
  },
  methods: {
    ...mapMutations("organization", [
      "setDimensionTransferInfo",
      "setChildrenTreeData",
      "setSelectedTreeNode",
      "setRefreshTreeStatus"
    ]),
    ...mapActions("organization", [
      "getChildNodeByPId",
      "getRootTreeData",
      "updateOrganizationEntityChildNum",
      "moveAfterRefresh"
    ]),
    getTreeMenuByNode(treeNode) {
      const { hasRoot, hasLeaf } = treeNode;
      const { mergeGroupVal } = this.pageDimObj;
      if (hasRoot) {
        if (mergeGroupVal === "allScope") {
          return [...TREE_MENU_ITEM_ROOT(), ...TREE_MENU_ITEM_DELETE_SHARE()];
        } else {
          return [...TREE_MENU_ITEM_ROOT()];
        }
      } else if (hasLeaf) {
        return [...TREE_MENU_ITEM_LEAF()];
      } else {
        return [...TREE_MENU_ITEM_PARENT()];
      }
    },
    getTreeNodeTitle(treeNode) {
      const { memberName, memberCodeIndex, hasLeaf, childNum } = treeNode;
      let title = memberName;
      const titleType = this.titleType;
      if (titleType === "CODE") {
        title = memberCodeIndex;
      } else if (titleType === "NAME_AND_CODE") {
        title = `${memberCodeIndex} ${memberName}`;
      }
      if (!hasLeaf) {
        title += `（${childNum}）`;
      }
      return title;
    },
    handleExpand(keys) {
      this.treeConfig.expandedKeys = keys;
    },
    onLoadData(treeNode) {
      this.spinning = true;
      return new Promise(resolve => {
        if (treeNode.dataRef.children) {
          this.spinning = false;
          resolve();
          return;
        }
        this.getChildNodeByPId(treeNode.dataRef.objectId)
          .then(res => {
            resolve();
          })
          .finally(() => {
            this.spinning = false;
          });
      });
    },
    async handleSearch(word) {
      if (!this.isAllPageDims) return;
      this.searchWord = word;
      this.$emit("update:rootSpinning", true);
      this.getRootTreeData({
        searchWord: word,
        hasRoot: false,
        hasFirst: false,
        organizationType: this.tableConfig.cellType
      }).finally(() => {
        if (!word) {
          this.treeMenuKey = `${AppUtils.generateUniqueId()}`;
        }
        this.$emit("update:rootSpinning", false);
      });
      this.setSelectedTreeNode("");
      this.treeConfig.expandedKeys = [];
    },
    // 顶部操作区按钮点击事件
    handleOperateClick(item) {
      if (item.group.indexOf("disabled") === -1) {
        this.handleBtnClick(item.key);
      }
    },
    isDisabled(treeNode, menuItem) {
      const specialMenuItem = [
        "upMove",
        "downMove",
        "placement",
        "bottomSetting"
      ];
      const { eventName } = menuItem;
      if (specialMenuItem.indexOf(eventName) !== -1) {
        if (this.searchWord) return true; // 搜索移动操作都置灰
        const { parentId: pId, objectId } = treeNode.dataRef;
        const pNode = this.mapTreeData[pId] || {};
        // 全部合并组 作为查询条件，有多个根节点
        if (Array.isArray(pNode)) {
          if (pNode.length > 1) {
            // 判断索引，第一个up placement 置灰 最后一个 down bootmSetting 置灰
            const index = this.treeData.findIndex(item => {
              return item.objectId === objectId;
            });
            if (!index && ["upMove", "placement"].includes(eventName)) {
              return true;
            } else if (
              index === this.treeData.length - 1 &&
              ["downMove", "bottomSetting"].includes(eventName)
            ) {
              return true;
            }
            return false;
          } else if (pNode.length === 1) {
            return true;
          }
        }
        // 同层级只有一个节点
        if (pNode.children && pNode.children.length === 1) {
          return true;
        }
        let nodeIndex = -1;
        pNode.children &&
          pNode.children.filter((item, index) => {
            const { objectId: cId } = item;
            if (cId === objectId) {
              nodeIndex = index;
              return true;
            }
          });
        if (!nodeIndex) {
          // 第一个节点
          if (["upMove", "placement"].indexOf(eventName) !== -1) {
            return true;
          }
        } else if (nodeIndex === pNode.children.length - 1) {
          // 最后一个节点
          if (["downMove", "bottomSetting"].indexOf(eventName) !== -1) {
            return true;
          }
        }
        return false;
      }
      return false;
    },
    onDrop(info) {
      this.$emit("update:rootSpinning", true);
      // 拖入节点内不做处理
      if (!info.dropToGap) {
        UiUtils.errorMessage(this.$t_structures("cannot_move_across_levels"));
        this.$emit("update:rootSpinning", false);
        return;
      }
      const node = info.node;
      const nodeData = node.dataRef;
      const dragNode = info.dragNode;
      const dragNodeData = dragNode.dataRef;
      const endPos = node.pos.split("-").slice(-1)[0];
      const nodePos = info.dropPosition - endPos; // 放置节点位置，在目标节点上（1） 还是下（-1）
      const DRAGPOSTYPE = ["UP", "DOWN"];
      const params = {
        dragPosType: DRAGPOSTYPE[0], // UP or DOWWN
        targetNodeId: nodeData.objectId,
        operationType: "MOVE_DRAG",
        objectId: dragNodeData.objectId
      };
      // 同级移动
      if (nodeData.parentId === dragNodeData.parentId) {
        if (nodePos === 1) {
          params.dragPosType = DRAGPOSTYPE[1];
        }
      } else if (dragNodeData.pId === nodeData.objectId && nodePos === 1) {
        // 移动到父节点上，默认放置子节点第一个
        params.targetNodeId = nodeData.children[0].objectId;
        params.dragPosType = DRAGPOSTYPE[0];
      } else {
        // 移动到其他父节点下面了
        UiUtils.errorMessage(this.$t_structures("cannot_move_across_levels"));
        this.$emit("update:rootSpinning", false);
        return;
      }
      organizationServer("moveNodeByType", params)
        .then(() => {
          const cacheSelectKeys = this.treeConfig.selectedKeys[0];
          this.setSelectedTreeNode("");
          // 全部成员 拖动后，刷新整个树节点,否则，刷新子项
          let methodName = "getChildNodeByPId";
          let requestParams = dragNodeData.parentId;
          // 全部成员 拖动根节点
          if (
            this.pageDimObj.mergeGroupVal === "allScope" &&
            dragNodeData.parentId === "-1"
          ) {
            methodName = "getRootTreeData";
            requestParams = { isKeepChildren: true };
          }
          this[methodName](requestParams)
            .then(res => {
              this.setSelectedTreeNode(cacheSelectKeys);
              this.$emit("update:rootSpinning", false);
            })
            .finally(() => {
              this.$emit("update:rootSpinning", false);
            });
        })
        .catch(() => {
          this.$emit("update:rootSpinning", false);
        });
    },
    clickNode(selectKey, event) {
      if (!selectKey.length || this.loadTableData) return;
      this.savePromptMixin().then(() => {
        this.setSelectedTreeNode(selectKey);
        this.treeConfig.selectedKeys = selectKey;
      });
    },
    handleMenuClick(event, treeNode) {
      //  isRequestMoveNode 根据 这个 操作 moveNode 上下移动、置顶置顶、删除
      const { key } = event;
      let currMenuData = this.menuData;
      const { hasLeaf, hasRoot } = treeNode.dataRef;
      if (hasRoot) {
        currMenuData = currMenuData.rootNode;
      } else if (!hasLeaf) {
        currMenuData = currMenuData.parentNode;
      } else {
        currMenuData = currMenuData.leafNode;
      }
      // 过滤出当前菜单项信息
      const menuItemData = currMenuData.filter(item => {
        return typeof item === "object" && item.eventName === key;
      })[0];
      const addNodeKeys = [
        "addSubsetGroup",
        "addOrganization",
        "addMergeGroup",
        "deleteNode"
      ];
      // 除添加、删除节点以外 都在此处 显示loading
      if (addNodeKeys.indexOf(key) === -1) {
        this.$emit("update:rootSpinning", true);
      }
      // 上下移动，置顶置底
      if (menuItemData.isRequestMoveNode) {
        this.moveNode(treeNode, key);
      } else {
        // 删除 添加 节点
        this[event.key] && this[event.key](treeNode);
      }
    },
    moveNode(treeNode, type) {
      const { objectId } = treeNode;
      this.$emit("update:rootSpinning", true);
      const self = this;
      const OPERATIONENUM = {
        upMove: "MOVE_UP",
        downMove: "MOVE_DOWN",
        placement: "MOVE_TOP",
        bottomSetting: "MOVE_BOTTOM"
      };
      organizationServer("moveNodeByType", {
        objectId,
        operationType: OPERATIONENUM[type]
      })
        .then(res => {
          if (res.status !== 200) return;
          const { parentId: pId } = treeNode.dataRef;
          self.moveAfterRefresh(pId).then(res => {
            this.$emit("update:rootSpinning", false);
            const cacheSelectKeys = self.treeConfig.selectedKeys[0];
            self.setSelectedTreeNode("");
            self.$nextTick(() => {
              self.setSelectedTreeNode(cacheSelectKeys);
            });
          });
        })
        .catch(() => {
          this.$emit("update:rootSpinning", false);
        });
    },
    // 删除
    deleteNode(treeNode) {
      const { objectId: id, parentId: pId, hasLeaf, hasRoot } = treeNode;
      const self = this;
      const nodeTypeText = hasLeaf
        ? this.$t_common("entity")
        : this.$t_common("scope");
      UiUtils.confirm({
        title: this.$t_structures("confirm_to_delete", [nodeTypeText]),
        content: this.$t_structures("remove_from_tree", [nodeTypeText]),
        okText: this.$t_common("ok"),
        okType: "primary",
        cancelText: this.$t_common("cancel"),
        onOk() {
          const selectedTreeNode = self.selectedTreeNode;
          self.$emit("update:rootSpinning", true);
          organizationServer("deleteNodeById", {
            id,
            isAllScope: self.pageDimObj.mergeGroupVal === "allScope"
          })
            .then(async () => {
              self.setSelectedTreeNode("");
              // 全部成员，删除根节点
              if (self.pageDimObj.mergeGroupVal === "allScope" && hasRoot) {
                await self.getRootTreeData({ isKeepChildren: true });
                // 删除当前选中的节点
                if (selectedTreeNode.objectId === id) {
                  self.clearCommonSaveEventsMixin();
                } else {
                  self.setSelectedTreeNode(selectedTreeNode.objectId);
                }
                self.$emit("update:rootSpinning", false);
                return;
              }
              // 如果根节点下，子项还剩余一个节点 需要重新获取树
              if (
                self.mapTreeData[pId].children.length === 1 &&
                pId === self.treeData[0].objectId
              ) {
                await self.getRootTreeData();
                self.treeMenuKey = `${AppUtils.generateUniqueId()}`;
                self.clearCommonSaveEventsMixin();
              } else {
                await self.getChildNodeByPId(pId);
                await self.updateOrganizationEntityChildNum(pId);
              }
              if (selectedTreeNode.objectId === id) {
                self.setSelectedTreeNode(pId);
              } else {
                self.setSelectedTreeNode(selectedTreeNode.objectId);
              }
              self.$emit("update:rootSpinning", false);
            })
            .catch(() => {
              self.$emit("update:rootSpinning", false);
            });
        },
        onCancel() {
          self.$emit("update:rootSpinning", false);
        }
      });
    },
    // 添加同级合并组
    addMergeGroup(treeNode) {
      this.savePromptMixin().then(() => {
        // 如果没有合并组维度信息，则请求合并组维度信息
        this.setDimensionTransferInfo({
          visible: true,
          treeNode: treeNode,
          type: ADDNODETYPE[0],
          dimInfo: { ...DIM_INFO_SCOPE(), selectedItem: [] }
        });
      });
    },
    // 添加同级组织
    addOrganization(treeNode) {
      this.savePromptMixin().then(() => {
        this.setDimensionTransferInfo({
          visible: true,
          treeNode: treeNode.objectId ? treeNode : {},
          type: ADDNODETYPE[2],
          dimInfo: { ...DIM_INFO_ENTITY(), selectedItem: [] }
        });
      });
    },
    // 添加子集合并组
    addSubsetGroup(treeNode) {
      this.savePromptMixin().then(() => {
        this.setDimensionTransferInfo({
          visible: true,
          treeNode: Object.keys(treeNode).length ? treeNode : {},
          type: ADDNODETYPE[1],
          dimInfo: { ...DIM_INFO_SCOPE(), selectedItem: [] }
        });
      });
    },
    handleBtnClick(type) {
      if (this.isAllPageDims) {
        this[type] && this[type]({});
      } else {
        UiUtils.errorMessage(this.$t_structures("select_page_dimension_first"));
      }
    }
  }
};
</script>

<style lang="less" scoped>
.organizational_left {
  background: @yn-body-background;
  height: 100%;
  width: calc(100% - 1px);
}

.treeNodeCont {
  display: inline-block;
  width: 100%;
  position: relative;
  height: @rem28;
  line-height: @rem28;
  & > span.nodeName {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    width: calc(100% - @rem28);
  }
  & > span.ynicon-button-more {
    vertical-align: text-bottom;
  }
}
.yn-tree-menu {
  border-right: none;
  overflow: none;
}
.treeNodeCont:hover {
  & > .more {
    display: block;
  }
}
/deep/ .yn-tree-menu-operate-wrapper {
  margin-bottom: @rem8;
}
</style>
