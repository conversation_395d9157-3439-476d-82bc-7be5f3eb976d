<template>
  <div v-if="searchObj.length > 0" class="filterCondition">
    <div class="conditionItem">
      <template v-for="item in searchObj">
        <yn-tag
          v-for="filterItem in item.searchArr"
          :key="`${item.type}_${filterItem.id}`"
          closable
          @close="clearSearchItem(item.type, filterItem.id)"
        >
          {{ `${item.title}：${getShowDimMemberName(filterItem, item.type)}` }}
        </yn-tag>
      </template>
      <yn-button type="text" @click="clearAllSearchInfo">
        {{ $t_common("clear") }}
      </yn-button>
    </div>

    <!-- <div class="clearAll" @click="clearAllSearchInfo">全部清除</div> -->
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-tag/";
import "yn-p1/libs/components/yn-button/";
export default {
  name: "FilterList",
  props: {
    searchObj: {
      type: Array,
      default: () => {
        return [];
      }
    },
    showDimMemberName: {
      type: Function,
      require: false,
      default: null
    }
  },
  data() {
    return {};
  },
  methods: {
    // 删除筛选项
    clearSearchItem(type, id) {
      this.$emit("deleteFilteItemById", type, id);
    },
    // 外部控制维度成员显示的值，默认显示name
    getShowDimMemberName(filterItem) {
      if (this.showDimMemberName) {
        return this.showDimMemberName(filterItem);
      } else {
        return filterItem.name;
      }
    },
    // 清除所有 搜索条件信息
    clearAllSearchInfo() {
      this.$emit("deleteFilteItemById", "all");
    }
  }
};
</script>
<style lang="less" scoped>
.filterCondition {
  display: flex;
  padding-bottom: 0.5rem;
  justify-content: space-between;
  .conditionItem {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
    /deep/.ant-tag {
      background-color: rgb(235, 238, 244);
      color: @yn-text-color-secondary;
    }
  }
  .clearAll {
    align-items: center;
    display: flex;
    color: @yn-primary-color;
    font-size: 12px;
    justify-content: flex-end;
    flex: 0 1 66px;
    min-width: 50px;
    cursor: pointer;
    padding-bottom: 5px;
  }
}
</style>
