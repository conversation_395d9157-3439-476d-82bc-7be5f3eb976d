<template>
  <yn-modal
    :title="title"
    :okText="$t_common('save')"
    :visible="selectDimVisible"
    @ok="downloadTemplate"
    @cancel="cancelEvent"
  >
    <yn-form :hideRequiredMark="true" :form="dimForm" v-bind="layout">
      <yn-form-item
        v-for="(item, index) in dimSelectList"
        :key="item.dimCode"
        :colon="false"
        :label="item.label"
        class="dim-item"
      >
        <yn-select-tree
          v-decorator="[
            item.dimCode,
            {
              initialValue: [pageDim[item.dimCode]],
              rules: [
                {
                  required: true,
                  message: `${$t_common('input_select')}${
                    $data.$showTip[item.dimCode]
                  }`
                }
              ]
            }
          ]"
          :defer="true"
          :datasource="searching ? searchResult : item.dataSource"
          multiple
          :nonleafselectable="false"
          searchMode="custom"
          @customSearch="onCustomeSearch($event, index)"
          @dropdownVisibleChange="dropdownVisibleChange($event)"
        />
      </yn-form-item>
    </yn-form>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-select-tree";

import { downloadFile } from "../../../utils/common";
import { MEMBER_SHOW_TYPE } from "./constant";
import equityServer from "@/services/equity";
import organizationServer from "@/services/organization";
import lang from "@/mixin/lang";
const { $t_common } = lang;
export default {
  props: {
    title: {
      type: String,
      default: $t_common("export")
    },
    pageDimObj: {
      type: Object,
      default: () => ({})
    },
    selectDimVisible: {
      type: Boolean,
      default: false
    },
    hasTemplate: {
      // true：下载模板、false：导出文件
      type: Boolean,
      default: true
    },
    importType: {
      type: String,
      default: "equity"
    },
    // 组织架构导出的类型 0: 平行架构  1: 逐级架构
    orgType: {
      type: Number,
      default: 1
    },
    cellType: {
      type: String,
      default: "NAME"
    }
  },
  data() {
    return {
      dimForm: this.$form.createForm(this, "dimForm"),
      layout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 16 }
      },
      $showTip: {
        Version: $t_common("version"),
        Year: $t_common("year"),
        Period: $t_common("period")
      },
      dimSelectList: [
        {
          label: $t_common("version"),
          dimCode: "Version",
          dataSource: []
        },
        {
          label: $t_common("year"),
          dimCode: "Year",
          dataSource: []
        },
        {
          label: $t_common("period"),
          dimCode: "Period",
          dataSource: [] // key、label
        }
      ],
      searching: false,
      searchResult: []
    };
  },
  computed: {
    pageDim() {
      return this.getPageDim();
    }
  },
  created() {
    this.getDataSource();
  },
  methods: {
    addKeyToTreeData(data) {
      let leafCount = 0;
      const loop = data => {
        data.map(item => {
          const { name, id, children } = item;
          item.key = id;
          item.label = name;
          if (children && children.length) {
            loop(children);
          } else {
            leafCount++;
          }
        });
      };
      loop(data);
      data.leafCount = leafCount;
    },
    // TODO 获取各维度数据
    async getDataSource() {
      const requestList = [
        organizationServer("getVersionTree"),
        organizationServer("getYearTree"),
        organizationServer("getPeriodTree")
      ];
      await Promise.all(requestList).then(res => {
        for (let i = 0; i < res.length; i++) {
          this.addKeyToTreeData(res[i].data);
          this.dimSelectList[i].dataSource = res[i].data;
        }
      });
    },
    // 获取版本 年 期间 页面维
    getPageDim() {
      const {
        versionVal: Version,
        yearVal: Year,
        monthVal: Period
      } = this.pageDimObj;
      return {
        Version,
        Year,
        Period
      };
    },
    cancelEvent() {
      this.$emit("update:selectDimVisible", false);
    },
    downloadTemplate() {
      const type =
        this.importType.slice(0, 1).toUpperCase() + this.importType.slice(1);
      const apiMethod = `dowload${type}Template`;
      this.dimForm.validateFields((err, values) => {
        if (!err) {
          const reqP = Object.create(null);
          Object.keys(values).forEach(item => {
            const code = item.toLowerCase();
            reqP[`${code}List`] = values[item];
          });
          reqP.hasTemplate = this.hasTemplate;
          // 如果是组织架构需要添加 orgType 参数
          if (this.importType === "organization") {
            reqP.orgType = this.orgType;
            reqP.memberShowType = MEMBER_SHOW_TYPE[this.cellType];
          }
          const apiServer =
            this.importType === "equity" ? equityServer : organizationServer;
          apiServer(apiMethod, reqP).then(res => {
            downloadFile(res);
          });
          this.cancelEvent();
        }
      });
    },
    onCustomeSearch(e, index) {
      this.searching = true;
      const treeData = this.dimSelectList[index].dataSource;
      const { searchValue } = e;
      const res = [];
      const loop = data => {
        data.forEach(item => {
          if (item.label.indexOf(searchValue) > -1) {
            res.push(item);
          }
          if (item.children && item.children.length) {
            loop(item.children);
          }
        });
      };
      loop(treeData);
      this.searchResult = [...res];
    },
    dropdownVisibleChange(isShow) {
      if (!isShow) {
        this.searching = false;
        this.searchResult = [];
      }
    }
  }
};
</script>

<style lang="less" scoped></style>
