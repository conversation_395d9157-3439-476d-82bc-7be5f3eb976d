<template>
  <div class="import-equity-modal">
    <!-- 导入弹窗 -->
    <yn-modal
      :title="$t_common('import')"
      width="562px"
      :visible="importVisible"
      wrapClassName="import-modal"
      @ok="onHandleOk"
      @cancel="() => (importVisible = !importVisible)"
    >
      <span class="down-text" @click="downLoadTemplate">
        {{ $t_process("download_template") }}
      </span>
      <yn-upload-dragger
        name="file"
        class="upload-drag"
        :multiple="false"
        :fileList="fileList"
        :remove="handleRemove"
        :beforeUpload="beforeUpload"
      >
        <p class="ant-upload-drag-icon">
          <yn-icon type="inbox" />
        </p>
        <p class="upload-text">
          {{ $t_structures("drag_excel_to_upload") }}
        </p>
        <p class="upload-text-desc">
          {{ $t_structures("only_one_file_excel") }}
        </p>
      </yn-upload-dragger>
      <div class="import-type">
        <span class="type-title">{{
          $t_structures("select_upload_mode")
        }}</span>
        <yn-radio-group v-model="importTypeValue">
          <div
            v-for="item in $data.$importTypeList"
            :key="item.value"
            class="type-item"
          >
            <yn-radio :key="item.value" :value="item.value">
              {{ item.text }}
            </yn-radio>
            <span v-if="item.value === $data.$showDesc" class="type-desc">
              {{ $data.$type2Desc }}
            </span>
          </div>
        </yn-radio-group>
      </div>
      <template slot="footer">
        <yn-button key="back" @click="() => (importVisible = !importVisible)">
          {{ $t_common("cancel") }}
        </yn-button>
        <yn-button
          key="submit"
          type="primary"
          :loading="btnLoading"
          @click="onHandleOk"
        >
          {{ $t_common("ok") }}
        </yn-button>
      </template>
    </yn-modal>
    <!-- 进度条弹窗 -->
    <yn-modal
      :visible="stepVisible"
      :title="$t_structures('import_progress')"
      width="562px"
      :maskClosable="false"
      :mask="false"
      :closable="!importLoading"
      @cancel="() => (stepVisible = !stepVisible)"
    >
      <div class="import-status">
        <p>
          {{ $t_structures("total_import") }}
          <span class="color-red"> {{ fileList.length }} </span>
          {{ $t_structures("file") }}:
        </p>
        <div v-for="(file, index) in fileList" :key="index" class="file-info">
          <svg-icon type="icon-file-Excel" />
          <span class="file-name">{{ file.name }}({{ file.size }})</span>
          <span :class="`import-${importStatus}`">{{
            $data.$statusText[importStatus]
          }}</span>
          <yn-button
            v-show="importStatus === 'part' || importStatus === 'fail'"
            class="btn-download"
            type="link"
            @click="downLoadTemplate('export')"
          >
            {{ $t_structures("download_document") }}
          </yn-button>
        </div>
      </div>
      <span v-show="importStatus === 'part'" class="part-success">
        {{ $t_structures("import_info", [successedSheet, failedSheet]) }}
      </span>
      <div class="import-explain">
        {{ $t_common("explain") }}:
        <span class="explain-why">{{
          $t_structures("download_failed_file")
        }}</span>
        <p class="explain-detail">
          {{ $t_structures("cells_that_have_failed_verification") }}
        </p>
      </div>
      <template slot="footer">
        <yn-button
          :disabled="importLoading"
          type="primary"
          @click="closeStepModal"
        >
          {{ $t_common("close") }}
        </yn-button>
      </template>
    </yn-modal>
    <!-- 下载模板弹窗 -->
    <DimSelectDownload
      v-if="selectDimVisible"
      :selectDimVisible.sync="selectDimVisible"
      :hasTemplate="true"
      :importType="importType"
      v-bind="$attrs"
      :title="$t_process('download_template')"
    />
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-upload-dragger/";
import "yn-p1/libs/components/yn-radio-group/";

import UiUtils from "yn-p1/libs/utils/UiUtils";
import { getConstant } from "./constant";
import DimSelectDownload from "./dimSelectDownload.vue";

import { downloadFile } from "../../../utils/common";
import equityServer from "@/services/equity";
import organizationServer from "@/services/organization";
import lang from "@/mixin/lang";
const { $t_structures } = lang;

export default {
  components: { DimSelectDownload },
  props: {
    importType: {
      type: String,
      default: "equity"
    }
  },
  data() {
    return {
      $importTypeList: getConstant(this.importType, "IMPORT_TYPE_LIST"),
      $type2Desc: getConstant(this.importType, "TYPE_2_DESC"),
      $showDesc: getConstant(this.importType, "SHOW_DESC"),
      importTypeValue: getConstant(this.importType, "DEFAULT_CHOOSE"),
      $statusText: {
        ing: $t_structures("importing_in_progress"),
        fail: $t_structures("import_failed"),
        success: $t_structures("import_successful"),
        part: $t_structures("partially_successful")
      },
      btnLoading: false,
      importVisible: false,
      stepVisible: false,
      selectDimVisible: false,
      fileList: [],
      importLoading: false,
      downloadFileId: "",
      interfaceStatus: "", // 接口返回的状态
      successedSheet: 0, // 导入成功的 sheet
      failedSheet: 0 // 导入失败的 sheet
    };
  },
  computed: {
    importStatus() {
      let res = "";
      switch (this.interfaceStatus) {
        case "ERROR":
          res = "fail";
          break;
        case "FINISH":
          res = this.getSheetInfo();
          break;
        default:
          res = "ing";
          break;
      }
      return res;
    }
  },
  methods: {
    getConstant,
    getSheetInfo() {
      let result = "";
      switch (this.successedSheet === 0) {
        case false:
          result = this.failedSheet === 0 ? "success" : "part";
          break;
        case true:
          this.failedSheet !== 0 && (result = "fail");
          break;
        default:
          break;
      }
      return result;
    },
    openImportModal() {
      this.resetValue();
      this.importVisible = true;
    },
    beforeUpload(file) {
      const imgReg = /(xlsx|xls)$/;
      const filename = file.name;
      const imgSuffix = filename
        .substring(filename.lastIndexOf(".") + 1)
        .toLowerCase();
      const isJpgOrPng = imgReg.test(imgSuffix);
      if (!isJpgOrPng) {
        UiUtils.errorMessage(this.$t_process("upload_correct_format"));
        return;
      }
      this.fileList.splice(0, 1, file);
      return false;
    },
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      this.fileList.splice(index, 1);
    },
    // 下载导入失败的文档
    exportEquityResult(id) {
      const type =
        this.importType.slice(0, 1).toUpperCase() + this.importType.slice(1);
      const apiMethod = `export${type}Result`;
      const apiServer =
        this.importType === "equity" ? equityServer : organizationServer;
      apiServer(apiMethod, id).then(res => {
        downloadFile(res);
      });
    },
    downLoadTemplate(type) {
      if (type === "export") {
        this.exportEquityResult(this.downloadFileId);
        return;
      }
      this.selectDimVisible = true;
    },
    onHandleOk() {
      if (this.fileList.length === 0) {
        UiUtils.errorMessage(this.$t_structures("please_select_a_file"));
        return;
      }
      const file = this.fileList[0];
      const formData = new FormData();
      let timer;
      formData.append("file", file);
      formData.append("importType", this.importTypeValue);
      this.btnLoading = true;
      const type =
        this.importType.slice(0, 1).toUpperCase() + this.importType.slice(1);
      const importTemplateMethod = `import${type}Template`;
      const getTaskResultMethod = `getTask${type}Result`;
      const apiServer =
        this.importType === "equity" ? equityServer : organizationServer;
      apiServer(importTemplateMethod, formData)
        .then(res => {
          // 判断股权上传时是否有被枷锁。
          if (this.importType === "equity") {
            if (!res.data.success) {
              this.interfaceStatus = res.data.messageType;
              UiUtils.customMessage({
                content: res.data.data.join("；")
              });
              this.btnLoading = false;
              return;
            }
          }
          this.downloadFileId = res.data && res.data.data[0];
          this.importVisible = false; // 关闭文件弹窗
          this.btnLoading = false; // 文件弹窗确定按钮loading
          this.stepVisible = true; // 打开进度弹窗
          this.importLoading = true; // 进度弹窗关闭按钮是否可关。
          timer = setInterval(() => {
            apiServer(getTaskResultMethod, this.downloadFileId).then(
              resultInfo => {
                this.interfaceStatus = resultInfo.data.importStatus;
                if (resultInfo.data.importStatus !== "RUNNING") {
                  this.importLoading = false;
                  const count = resultInfo.data.resultInfo.split(",");
                  this.successedSheet = Number(count[0]);
                  this.failedSheet = Number(count[1]);
                  clearInterval(timer);
                }
              }
            );
          }, 1000);
        })
        .catch(err => {
          this.importVisible = false;
          this.btnLoading = false;
          UiUtils.errorMessage(
            `${this.$t_structures("import_interface_error")}：${err}`
          );
        });
    },
    closeStepModal() {
      this.stepVisible = false;
      if (this.interfaceStatus === "FINISH") {
        this.$emit("refreshTable");
      }
      this.resetValue();
    },
    resetValue() {
      this.fileList.splice(0);
      this.importLoading = false;
      this.downloadFileId = "";
      this.interfaceStatus = "";
      this.successedSheet = 0;
      this.failedSheet = 0;
      this.importTypeValue = getConstant(this.importType, "DEFAULT_CHOOSE");
    }
  }
};
</script>
<style lang="less" scoped>
.import-ing {
  color: @yn-label-color;
}
.import-success {
  color: @yn-success-color;
}
.import-fail {
  color: @yn-error-color;
}
.import-part {
  color: @yn-warning-color;
}
.down-text {
  display: inline-block;
  color: @yn-chart-1;
  position: absolute;
  top: 3.75rem;
  right: @rem24;
  cursor: pointer;
}
.import-type {
  margin-top: @rem16;
  .type-title {
    &::before {
      display: inline-block;
      margin-right: @rem4;
      color: @yn-error-color;
      font-size: @rem14;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: "*";
    }
  }
  .type-item {
    margin: @rem4 0;
  }
  .type-desc {
    display: inline-block;
    width: 100%;
    color: @yn-error-color;
    margin-left: @rem24;
    margin-top: @rem4;
    padding-right: @rem24;
  }
  /deep/.ant-radio-group {
    display: flex;
    flex-direction: column;
  }
}
.upload-drag {
  /deep/.ant-upload-drag {
    margin-top: @rem22;
  }
  .upload-text-desc {
    font-size: @rem14;
    color: @yn-disabled-color;
    text-align: center;
    line-height: @rem22;
    font-weight: 400;
  }
}
.import-status {
  .color-red {
    color: @yn-error-color;
  }
  .file-excel-icon {
    font-size: @rem20;
    color: @yn-success-color;
  }
  .file-info {
    margin-top: @rem24;
    .file-name {
      margin: 0 @rem32 0 @rem12;
    }
    .btn-download {
      padding: 0;
      margin-left: @rem16;
    }
  }
}
.part-success {
  display: inline-block;
  margin-top: @rem12;
  color: @yn-warning-color;
}
.import-explain {
  display: inline-block;
  margin-top: @rem32;
  color: @yn-label-color;
  .explain-why {
    margin-left: @rem5;
  }
  .explain-detail {
    margin-left: @rem36;
  }
}
</style>
