import lang from "@/mixin/lang";

const { $t_equity } = lang;
// 此处分开写 防止后期 翻译不好拼接
const EQUITY_IMPORT_TYPE_LIST = () => [
  {
    value: "all-cover",
    text: $t_equity("add_and_replace_equity_data")
  },
  {
    value: "update-new",
    text: $t_equity("do_not_modify_equity_data")
  },
  {
    value: "update-exist",
    text: $t_equity("only_modify_existing_equity_data")
  },
  {
    value: "update-all",
    text: $t_equity("add_and_update_equity_data")
  }
];
const EQUITY_TYPE_2_DESC = () => $t_equity("update_delete_origin");

const EQUITY_SHOW_DESC = () => EQUITY_IMPORT_TYPE_LIST()[0].value;

const EQUITY_DEFAULT_CHOOSE = () => EQUITY_IMPORT_TYPE_LIST()[1].value;

const ORGANIZATION_IMPORT_TYPE_LIST = () => [
  {
    value: "all-cover",
    text: $t_equity("add_and_replace_organizational_structure_data")
  },
  {
    value: "update-new",
    text: $t_equity("do_not_modify_existing_organizational_structure_data")
  },
  {
    value: "update-exist",
    text: $t_equity("only_modify_existing_organizational_structure_data")
  },
  {
    value: "update-all",
    text: $t_equity("add_and_update_organizational_structure")
  }
];
const ORGANIZATION_TYPE_2_DESC = () =>
  $t_equity("update_delete_origin_structure");

const ORGANIZATION_SHOW_DESC = () => ORGANIZATION_IMPORT_TYPE_LIST()[0].value;

const ORGANIZATION_DEFAULT_CHOOSE = () =>
  ORGANIZATION_IMPORT_TYPE_LIST()[1].value;

export const MEMBER_SHOW_TYPE = {
  CODE: 1,
  NAME: 0,
  NAME_AND_CODE: 2
};

const constantObj = () => ({
  EQUITY_IMPORT_TYPE_LIST: EQUITY_IMPORT_TYPE_LIST(),
  EQUITY_TYPE_2_DESC: EQUITY_TYPE_2_DESC(),
  EQUITY_SHOW_DESC: EQUITY_SHOW_DESC(),
  EQUITY_DEFAULT_CHOOSE: EQUITY_DEFAULT_CHOOSE(),
  ORGANIZATION_IMPORT_TYPE_LIST: ORGANIZATION_IMPORT_TYPE_LIST(),
  ORGANIZATION_TYPE_2_DESC: ORGANIZATION_TYPE_2_DESC(),
  ORGANIZATION_SHOW_DESC: ORGANIZATION_SHOW_DESC(),
  ORGANIZATION_DEFAULT_CHOOSE: ORGANIZATION_DEFAULT_CHOOSE(),
  getConstant
});

/**
 *
 * @param {String} type 导入的类型 equity/organization
 * @param {String} type2 constant 类型
 */
function getConstant(type, type2) {
  const suffix = type.toUpperCase();
  return constantObj()[`${suffix}_${type2}`];
}

export { getConstant };

