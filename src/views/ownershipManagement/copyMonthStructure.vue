<template>
  <yn-modal
    :title="title"
    :visible="true"
    :width="506"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template slot="footer">
      <yn-button key="back" @click="handleCancel">
        {{ $t_common("cancel") }}
      </yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="confirmLoading"
        @click="handleOk"
      >
        {{ $t_common("ok") }}
      </yn-button>
    </template>
    <yn-spin :spinning="!isRequestSuccess">
      <template v-for="(item, index) in formLayoutData">
        <yn-form :key="index" :form="item.from.fromObj" class="copyForm">
          <h3>{{ item.title }}</h3>
          <yn-form-item
            :label="$t_structures('from')"
            :labelCol="{ span: 3 }"
            :wrapperCol="{ span: 21 }"
          >
            <asyn-select-dimMember
              :ref="`${item.from.key}_from`"
              v-decorator="[
                `${item.from.key}`,
                {
                  rules: [{ required: true, message: item.from.requiredText }],
                  initialValue: pageDim[item.type]
                }
              ]"
              :dataInfo="item.dataInfo"
              :dimCode="`${item.type}`"
              :height="180"
              :limit="limit"
              :nonleafselectable="false"
              :formatter="formatter"
              @changeVal="handlerReallyVal"
            />
          </yn-form-item>
          <yn-form-item
            :label="$t_structures('to')"
            :labelCol="{ span: 3 }"
            :wrapperCol="{ span: 21 }"
          >
            <asyn-select-dimMember
              :ref="`${item.from.key}_to`"
              v-decorator="[
                `${item.to.key}`,
                {
                  rules: [{ required: true, message: item.to.requiredText }],
                  initialValue: undefined
                }
              ]"
              :dimCode="`${item.type}`"
              :dataInfo="item.dataInfo"
              :height="180"
              :nonleafselectable="false"
              :limit="limit"
              :formatter="formatter"
            />
          </yn-form-item>
        </yn-form>
      </template>
    </yn-spin>
  </yn-modal>
</template>
<script>
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-spin/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
import organizationServer from "@/services/organization";
import commonService from "@/services/common";
const DIM_CODE_ARR = ["Version", "Year", "Period"];
export default {
  name: "CopyMonthStructure",
  components: {
    AsynSelectDimMember
  },
  props: {
    isEquity: {
      type: Boolean,
      default: false
    },
    pageDimObj: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      formValue: {},
      fromVersion: this.$form.createForm(this, { name: "fromVersion" }),
      fromYear: this.$form.createForm(this, { name: "fromYear" }),
      fromPeriod: this.$form.createForm(this, { name: "fromPeriod" }),
      formLayoutData: [],
      isRequestSuccess: false,
      limit: 10,
      confirmLoading: false
    };
  },
  computed: {
    pageDim() {
      return this.getPageDim();
    }
  },
  async created() {
    const tempArr = DIM_CODE_ARR.map(dimCode => {
      return this.requestData(dimCode);
    });
    await Promise.all(tempArr).then(res => {
      const result = res.map((item, index) => {
        return this.createFormLayoutData(DIM_CODE_ARR[index], item);
      });
      this.formLayoutData = result;
    });
    this.isRequestSuccess = true;
  },
  methods: {
    // 获取版本 年 期间 页面维
    getPageDim() {
      const {
        versionVal: Version,
        yearVal: Year,
        monthVal: Period
      } = this.pageDimObj;
      return {
        Version,
        Year,
        Period
      };
    },
    handlerReallyVal(e, items) {
      this.$set(this.formValue, items[0].dimCode, e);
    },
    requestData(dimCode) {
      const { limit } = this;
      return new Promise(resolve => {
        commonService("getFirstDimMemberList", {
          dimCode,
          offset: 0,
          limit
        }).then(res => {
          resolve(res.data);
        });
      });
    },
    formatData(data) {
      return data.map((item, index) => {
        const {
          dimId,
          dimCode,
          dimMemberCode,
          objectId,
          dimMemberHasChildren,
          dimMemberRealName,
          dbCodeIndex
        } = item;
        return {
          dimId,
          dimCode,
          dimMemberCode,
          indexes: index,
          key: dbCodeIndex,
          objectId: objectId,
          isLeaf: dimMemberHasChildren === "FALSE",
          label: dimMemberRealName
        };
      });
    },
    formatter(item) {
      return item.dbCodeIndex;
    },
    createFormLayoutData(type, data) {
      const dataMap = {
        Version: this.$t_common("version"),
        Year: this.$t_common("year"),
        Period: this.$t_common("period")
      };
      return {
        type,
        title: dataMap[type],
        dataInfo: data,
        from: {
          key: `from${type}`,
          fromObj: this[`from${type}`],
          requiredText: `${this.$t_common(
            "input_select"
          )}【${this.$t_structures("from")}】${dataMap[type]}！`
        },
        to: {
          key: `to${type}`,
          requiredText: `${this.$t_common(
            "input_select"
          )}【${this.$t_structures("to")}】${dataMap[type]}！`
        }
      };
    },
    //  数据 映射成数组，便于后续前端对树节点的过滤
    handleCancel() {
      this.$emit("closeCopyModal", false);
    },
    handleOk() {
      this.confirmLoading = true;
      const promiseArr = [
        this.fromVersion.validateFields(),
        this.fromYear.validateFields(),
        this.fromPeriod.validateFields()
      ];
      Promise.all(promiseArr)
        .then(res => {
          // 调用接口
          const [{ toVersion }, { toYear }, { toPeriod }] = res;
          const {
            Version: fromVersion,
            Year: fromYear,
            Period: fromPeriod
          } = this.formValue;
          const params = {
            fromVersion,
            toVersion,
            fromYear,
            toYear,
            fromPeriod,
            toPeriod
          };
          if (this.isEquity) {
            this.copyEvent(params);
          } else {
            this.checkOrganizationProcessExists(params);
          }
        })
        .catch(e => {
          this.confirmLoading = false;
        });
    },
    copyEvent(params) {
      const { toVersion, toYear, toPeriod } = params;
      const requestApi = this.isEquity
        ? "copyShareholdingRatio"
        : "copyMonthFramework";
      organizationServer(requestApi, params)
        .then(res => {
          if (res.status === 200) {
            this.$emit("copyAfterEvent", {
              version: toVersion,
              year: toYear,
              period: toPeriod
            });
            // 如果股权复制持股比例，有用户加锁了
            const userInfo = res.data.data;
            if (userInfo && userInfo.userName) {
              const toast = {
                edit: this.$t_structures("editing_wait"),
                copy: this.$t_structures("copying_wait"),
                import: this.$t_structures("importing_wait")
              };
              UiUtils.errorMessage(
                `${userInfo.userName}${toast[userInfo.operateType]}`
              );
              return;
            }
            UiUtils.successMessage(`${this.$t_common("copy_success")}！`);
            this.$emit("closeCopyModal", false); // 关闭modal
          }
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    async checkOrganizationProcessExists(params) {
      const { toVersion, toYear, toPeriod } = params;
      const { fromVersion_to, fromYear_to, fromPeriod_to } = this.$refs;
      const requestParams = {
        year: fromYear_to[0].$refs.selector.$data.valueMap[toYear].objectId,
        version:
          fromVersion_to[0].$refs.selector.$data.valueMap[toVersion].objectId,
        period:
          fromPeriod_to[0].$refs.selector.$data.valueMap[toPeriod].objectId
      };
      const { data } = await organizationServer(
        "checkOrganizationProcessExists",
        requestParams
      );
      const { existsOrganization, existsProcess } = data.data;
      if (existsOrganization || existsProcess) {
        let content = this.$t_structures("dim_has_arch");
        if (existsOrganization && existsProcess) {
          content = this.$t_structures("dim_has_arch_start");
        }
        UiUtils.confirm({
          title: this.$t_structures("confirm_coverage_data"),
          content: `${content},${this.$t_structures(
            "copy_lose_origin_data"
          )}。`,
          onOk: () => {
            this.copyEvent(params);
          },
          onCancel: () => {
            this.confirmLoading = false;
          }
        });
      } else {
        this.copyEvent(params);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.copyForm/deep/.ant-form-item-label {
  padding-left: 10px;
  text-align: left;
  label {
    color: @yn-text-color-secondary;
  }
}
.copyForm/deep/.ant-row {
  margin-bottom: 12px;
}
</style>
<style lang="less">
.copyForm {
  width: 435px;
  margin: 0 auto;
}
</style>
