<template>
  <div v-if="isShow" ref="dimCont" class="dim-row">
    <div
      v-for="item in showFormOptions"
      :key="`${item.field}_${item.value}_${item.isSpecial}`"
      class="dim-item"
      :style="selectTreeStyle"
    >
      <label v-if="item.isShow" class="dim-label">{{ item.label }}</label>
      <asyn-select-dimMember
        :ref="item.dimCode"
        v-bind="$attrs"
        class="dim-row-item"
        forceRender
        :value="item.value"
        :dimCode="item.dimCode"
        searchMode="single"
        :allowClear="false"
        :hasAllMembers="item.dimCode === 'Scope' && item.isSpecial"
        :nonleafselectable="false"
        :isSetDefaultVal="true"
        :beforeChange="() => beforeChange(item.field)"
        :needPermission="permission.includes(item.dimCode)"
        :loadComplete="loadComplete"
        @changeVal="onChange($event, item.field)"
      />
    </div>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-row/";
import "yn-p1/libs/components/yn-col/";
import "yn-p1/libs/components/yn-select-tree";
import commonService from "@/services/common";
import savePromptMixin from "@/mixin/savePrompt.js";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
import lang from "@/mixin/lang";
const { $t_common, $t_equity } = lang;
const Responsive = {
  screenSize: {
    M: 1280,
    L: 1440,
    XL: 1680,
    XXL: 1920,
    XXXL: 2560
  },
  fontSize: {
    M: 12,
    L: 15,
    XL: 15,
    XXL: 16,
    XXXL: 20
  },
  fieldWidth: {
    M: 21,
    L: 20,
    XL: 21,
    XXL: 20,
    XXXL: 21.5
  },
  getSize: function(width) {
    if (width <= this.screenSize.M) {
      return "M";
    } else if (width <= this.screenSize.L) {
      return "L";
    } else if (width <= this.screenSize.XL) {
      return "XL";
    } else if (width <= this.screenSize.XXL) {
      return "XXL";
    } else if (width <= this.screenSize.XXXL) {
      return "XXXL";
    } else {
      return "XXXL";
    }
  }
};
const MAPPING_BOJ = {
  scope: "Scope",
  period: "Period",
  year: "Year",
  version: "Version"
};

export default {
  name: "DimSelect",
  components: { AsynSelectDimMember },
  mixins: [savePromptMixin],
  inject: ["defaultParams"],
  props: {
    hideMergeGroup: {
      type: Boolean,
      default: false
    },
    showCompany: {
      type: Boolean,
      default: false
    },
    defaultPageDim: {
      type: Object,
      default() {
        return {};
      }
    },
    isShowConfirmSave: Boolean,
    permission: {
      type: Array,
      require: false,
      default: () => []
    },
    showSpecialScope: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      colWidth: "25%",
      formOptions: [
        {
          field: "version",
          label: $t_common("version"),
          dimCode: "Version",
          data: [],
          isShow: true,
          value: ""
        },
        {
          field: "year",
          label: $t_common("year"),
          dimCode: "Year",
          data: [],
          isShow: true,
          value: ""
        },
        {
          field: "month",
          label: $t_common("period"),
          dimCode: "Period",
          data: [],
          isShow: true,
          value: ""
        },
        {
          field: "mergeGroup",
          label: $t_common("scope"),
          dimCode: "Scope",
          data: [],
          isShow: true,
          value: ""
        },
        {
          field: "mergeGroup",
          label: $t_common("scope"),
          dimCode: "Scope",
          data: [],
          isSpecial: true, // 包含全部成员
          isShow: false,
          value: ""
        },
        {
          field: "holdingCompay",
          label: $t_equity("shareholding_company"),
          dimCode: "ICP",
          data: [],
          isShow: false,
          value: ""
        }
      ],
      holdingCompayData: "",
      isShow: false,
      isRefreshing: false, // 刷新操作中
      povObj: {
        scope: "",
        period: "",
        year: "",
        version: ""
      }
    };
  },
  computed: {
    selectTreeStyle() {
      return {
        width: `${this.colWidth}`
      };
    },
    showFormOptions() {
      return this.formOptions.filter(item => {
        return item.isShow;
      });
    }
  },
  watch: {
    hideMergeGroup: {
      handler(newVal) {
        if (newVal) {
          this.filterOutVal("mergeGroup");
        }
      },
      immediate: true
    },
    showCompany: {
      handler(newVal) {
        this.formOptions[this.formOptions.length - 1].isShow = this
          .hideMergeGroup
          ? newVal
          : false;
      },
      immediate: true
    },
    showSpecialScope: {
      handler(newVal) {
        this.formOptions.forEach(item => {
          const { dimCode, isSpecial } = item;
          if (dimCode === "Scope") {
            const isShow = isSpecial ? newVal : !newVal;
            item.isShow = isShow;
          }
        });
        this.$set(this, "formOptions", this.formOptions);
      },
      immediate: true
    }
  },
  async created() {
    this.loadNum = 0;
    await this.selectUserPov(data => {
      if (!data) return;
      Object.keys(data).forEach(key => {
        this.povObj[key] = data[key].memberId;
      });
    });
    await this.getData();
    this.colWidth = this.calcColWidth();
    this.attachResize();
  },
  beforeDestroy() {
    this.observer && this.observer.disconnect();
  },
  methods: {
    loadComplete() {
      this.loadNum++;
      if (this.loadNum === this.showFormOptions.length) {
        this.$emit("setInitStatus", true);
      }
    },
    attachResize() {
      const that = this;
      const callback = (entries, observer) => {
        const newFieldWidth = that.calcColWidth();
        if (that.colWidth !== newFieldWidth) {
          that.colWidth = newFieldWidth;
        }
      };

      this.observer = new ResizeObserver(callback);
      this.$refs.dimCont && this.observer.observe(this.$refs.dimCont);
    },
    calcColWidth() {
      const domWidth = this.$refs.dimCont && this.$refs.dimCont.clientWidth;
      const windownWidth = this.getWindowWidth();
      const size = Responsive.getSize(windownWidth);
      const containerWidth = Math.floor(domWidth / Responsive.fontSize[size]);
      const colSize = Math.floor(containerWidth / Responsive.fieldWidth[size]);
      const colWidth = colSize < 1 ? "100%" : `${100 / colSize}%`;
      return colWidth;
    },
    getWindowWidth() {
      try {
        return window.top.innerWidth || window.innerWidth;
      } catch (error) {
        return window.innerWidth;
      }
    },
    filterOutVal(specifiedValue) {
      this.formOptions = this.formOptions.filter(item => {
        const { field } = item;
        return field !== specifiedValue;
      });
    },
    async getData(flag = true) {
      // 刷新操作
      const self = this;
      if (!flag) {
        this.isRefreshing = true;
        this.$emit("setInitStatus", false);
        const tempArr = [];
        this.showFormOptions.forEach(item => {
          const currCompont = self.$refs[item.dimCode];
          tempArr.push(
            currCompont &&
              currCompont[0].refreshData &&
              currCompont[0].refreshData(item.dimCode)
          );
        });
        await Promise.all(tempArr).then(res => {
          this.showFormOptions.forEach((item, index) => {
            const { field } = item;
            this.$emit("changeDimVal", this.getKeyName(field), res[index]);
          });
          // 初始化完成
          this.$emit("setInitStatus", true);
        });
        this.isRefreshing = false;
      } else {
        this.setDefaultVal();
        this.isShow = true;
      }
    },
    async setDefaultVal() {
      // props 接受其他地方传参
      const defaultVal = Object.assign({}, this.povObj, this.defaultParams);
      // 合并组 成员信息
      const scopeMemberId = defaultVal.scope;
      // 如果 合并组 给的是父项成员，信息，则选中 第一个叶子节点
      if (scopeMemberId) {
        // 根据成员id 获取成员信息
        const {
          data: { items }
        } = await commonService("getQueryDimMemberList", {
          dimCode: "Scope",
          dimMemberId: scopeMemberId,
          hasAllMembers: true,
          limit: 10,
          memberExp: null,
          needFilterAudittrail: false,
          needFilterShared: false,
          needPermission: true,
          offset: 0
        });
        if (items[0].dimMemberHasChildren === "TRUE") {
          delete defaultVal.scope;
        }
      }
      Object.keys(defaultVal).forEach(propName => {
        if (MAPPING_BOJ[propName]) {
          this.formOptions.forEach(item => {
            const { dimCode } = item;
            if (dimCode === MAPPING_BOJ[propName]) {
              item.value = defaultVal[propName];
            }
          });
        }
      });
    },
    getKeyName(field) {
      return field === "holdingCompay" ? field : `${field}Val`;
    },
    onChange(e, type) {
      // 刷新阶段，不处理change事件，当刷新操作完成，统一处理change，防止成员被删除，传入删除的成员ID 接口报错
      if (this.isRefreshing) return;
      // 存入pov
      const mappingKey = {
        mergeGroup: "scope",
        month: "period"
      };
      const dimIdent = mappingKey[type] ? mappingKey[type] : type;
      if (Object.keys(MAPPING_BOJ).indexOf(dimIdent) !== -1) {
        if (this.scopeVal !== e && e !== "allScope") {
          this.scopeVal = e;
          this.saveOrUpdateUserPov({ [dimIdent]: e });
        }
      }
      this.changeSelectVal(e, this.getKeyName(type));
    },
    beforeChange(type) {
      const bool = type === "holdingCompay";
      return this.savePromptMixin(bool);
    },
    changeSelectVal(e, type) {
      if (type === "mergeGroupVal") {
        this.formOptions.forEach(item => {
          if (item.dimCode === "Scope") {
            item.value = e;
          }
        });
      }
      this.$emit("changeDimVal", type, e);
    }
  }
};
</script>
<style lang="less" scoped>
.dim-row-item,
.dim-row-company {
  width: calc(100% - 6.5rem);
}
.dim-row {
  display: flex;
  flex-flow: row wrap;
  & > div {
    display: flex;
    flex: 0 1 auto;
    width: 33.33%;
    margin-bottom: @rem10;
  }
  .dim-label {
    text-align: right;
    height: @rem32;
    line-height: @rem32;
    font-size: @yn-font-size-base;
    color: @yn-text-color-secondary;
    padding-right: @yn-padding-s;
    display: inline-block;
    width: 6.5rem;
  }
}
</style>
