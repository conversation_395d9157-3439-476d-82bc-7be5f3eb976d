<template>
  <div class="final-gragh">
    <yn-spin :spinning="spinning">
      <Graph
        :pageDimObj="{
          version: equityPageDimObj.versionVal,
          year: equityPageDimObj.yearVal,
          period: equityPageDimObj.monthVal
        }"
        :graphData="graphData"
        :pageType="$data.$pageFiled.PAGE_EQUITY_FINALL"
      />
    </yn-spin>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-spin/";

import Graph from "../../../../components/ui/graph";
import pageFiled from "../../constant";

import equityService from "@/services/equity";

export default {
  components: { Graph },
  props: {
    equityPageDimObj: {
      type: Object,
      default: () => ({
        version: "",
        year: "",
        period: ""
      })
    }
  },
  data() {
    return {
      graphData: {},
      spinning: false,
      $pageFiled: pageFiled
    };
  },
  watch: {
    equityPageDimObj: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.getChartData(newVal);
      }
    }
  },
  methods: {
    getChartData(params) {
      this.spinning = true;
      const {
        yearVal: year,
        versionVal: version,
        monthVal: period,
        holdingCompay: icp
      } = params;
      // type: 1 法人架构图、2 最终持股图
      if (!year || !version || !period || !icp) {
        this.spinning = false;
        return;
      };
      equityService("getLegalSchemaDiagram", {
        year,
        version,
        period,
        icp,
        type: 2
      })
        .then(res => {
          this.spinning = false;
          res.data && (this.graphData = res.data);
        })
        .catch(() => {
          this.spinning = false;
        });
    }
  }
};
</script>

<style lang="less" scoped>
.final-gragh {
  width: 100%;
  height: 100%;
}
</style>
