<template>
  <div class="equity">
    <!-- <div class="equity-header"> -->
    <!-- 股权管理头部 -->
    <yn-spin
      class="page-loading cs-container"
      :spinning="pageLoading"
      size="large"
    >
      <Empty v-show="!equityAuth['equity-read'] && !pageLoading" />
      <template v-show="equityAuth['equity-read'] || pageLoading">
        <EquityHeader
          :isShowFinalGragh="isShowFinalGragh"
          :isShowStructureGragh="isShowStructureGragh"
          :pageLoading.sync="pageLoading"
          @showGrah="showGrah"
          @changeDimVal="changeDimVal"
          @refreshEvent="refreshPage"
        />
        <!-- </div> -->
        <!-- 股权管理内容 -->
        <div
          v-show="!isShowFinalGragh && !isShowStructureGragh"
          class="equity-content"
        >
          <!-- 股权管理菜单 -->
          <div class="content-menu">
            <div class="menu-btns">
              <yn-button
                v-if="showLockBtn && !editing"
                :loading="submitLoading"
                class="menu-btn mr8"
                type="primary"
                @click="btnEvent"
              >
                {{ $t_common("edit") }}
              </yn-button>
              <yn-button
                v-if="editing && equityAuth['equity-add']"
                class="menu-btn mr8"
                @click="addEvent"
              >
                {{ $t_structures("new_shareholding_relationship") }}
              </yn-button>
              <yn-button
                v-if="equityAuth['equity-copy']"
                class="menu-btn mr8"
                @click="copyEvent(true)"
              >
                {{ $t_structures("copy_shareholding_ratio") }}
              </yn-button>
            </div>
            <div class="menu-icons">
              <!-- 校验信息 -->
              <yn-message-feedback
                v-if="saveTableMessage.length"
                class="equity-meesage-box"
                :messageList="saveTableMessage"
              />
              <!-- 导入 -->
              <svg-icon
                v-show="equityAuth['equity-import']"
                type="icon-bianzubeifen"
                class="iconBtn export"
                :title="$t_common('import')"
                @click="importFile"
              />
              <!-- 导出 -->
              <svg-icon
                v-show="equityAuth['equity-export']"
                type="icon-bianzu"
                class="export iconBtn"
                :title="$t_common('export')"
                @click="() => (exportEquityVisible = !exportEquityVisible)"
              />
              <!-- 设置 -->
              <yn-popover
                v-model="popoverVisible"
                trigger="click"
                placement="bottomRight"
              >
                <template slot="content">
                  <span class="popover-title">
                    {{ this.$t_structures("investor") }}
                  </span>
                  <br />
                  <yn-radio-group
                    :value="columnsObj.shareCellType"
                    @change="e => changeColumn(e, 'share')"
                  >
                    <yn-radio value="Name">{{ $t_common("name") }}</yn-radio>
                    <yn-radio value="Code">
                      {{ $t_structures("unique_identifier") }}
                    </yn-radio>
                    <yn-radio value="CodeAndName">
                      {{
                        `${$t_structures("unique_identifier")}+${$t_common(
                          "name"
                        )}`
                      }}
                    </yn-radio>
                  </yn-radio-group>
                  <br />
                  <yn-checkbox
                    :checked="columnsObj.shareCellIsFreeze"
                    class="freeze-check"
                    @change="e => changeFreezeState(e, 'share')"
                  >
                    {{ $t_structures("frozen") }}
                  </yn-checkbox>
                  <br />
                  <span class="popover-title">
                    {{ $t_structures("investee") }}
                  </span>
                  <br />
                  <yn-radio-group
                    :value="columnsObj.stakeCellType"
                    @change="e => changeColumn(e, 'stake')"
                  >
                    <yn-radio value="Name">{{ $t_common("name") }}</yn-radio>
                    <yn-radio value="Code">
                      {{ $t_structures("unique_identifier") }}
                    </yn-radio>
                    <yn-radio value="CodeAndName">
                      {{
                        `${$t_structures("unique_identifier")}+${$t_common(
                          "name"
                        )}`
                      }}
                    </yn-radio>
                  </yn-radio-group>
                  <br />
                  <yn-checkbox
                    :checked="columnsObj.stakeCellIsFreeze"
                    class="freeze-check"
                    @change="e => changeFreezeState(e, 'stake')"
                  >
                    {{ $t_structures("frozen") }}
                  </yn-checkbox>
                </template>
                <svg-icon
                  type="icon-set-up"
                  class="iconBtn"
                  :title="$t_common('settings')"
                />
              </yn-popover>
              <yn-divider type="vertical" class="icon-divider" />
              <!-- 小数位 -->
              <div>
                {{ $t_structures("decimal_places") }}
                <yn-input-number
                  v-model="numVal"
                  class="input-number"
                  :precision="0"
                  :min="0"
                  :max="9"
                  @blur="setPrecision"
                />
              </div>
            </div>
          </div>
          <EquityTable
            ref="equityTable"
            :precision="numVal"
            :equityPageDimObj="equityPageDimObj"
            :columnsObj="columnsObj"
            :unlockEquity="unlockEquity"
            :editing="editing"
            :equityAuth="equityAuth"
            @getLastSettingV="getLastSettingV"
            @getSaveTableMessage="getSaveTableMessage"
          />
        </div>
        <div v-if="editing && !hiddenBtn" class="equity-footer">
          <yn-button class="menu-btn mr8" @click="resetData">
            {{ $t_common("cancel") }}
          </yn-button>
          <yn-button
            :loading="saveBtnLoading"
            class="menu-btn mr8"
            @click="saveEvent"
          >
            {{ $t_common("save") }}
          </yn-button>
          <yn-button
            :loading="submitLoading"
            class="menu-btn"
            type="primary"
            @click="btnEvent"
          >
            {{ $t_common("submit") }}
          </yn-button>
        </div>
      </template>
      <!-- 法人架构图 -->
      <div v-if="isShowStructureGragh" class="graph-content cs-container">
        <StructureGragh :equityPageDimObj="equityPageDimObj" />
      </div>
      <!-- 股权架构图-最终持股图 -->
      <div v-if="isShowFinalGragh" class="graph-content cs-container">
        <FianlGragh :equityPageDimObj="equityPageDimObj" />
      </div>
    </yn-spin>

    <!-- 复制持股比例 -->
    <copy-month-structure
      v-if="showCopyMothStruModal"
      :pageDimObj="equityPageDimObj"
      :isEquity="true"
      :title="$t_structures('copy_shareholding_ratio')"
      @closeCopyModal="copyEvent"
    />

    <import-equity
      ref="importEquity"
      importType="equity"
      :pageDimObj="equityPageDimObj"
      @refreshTable="refreshPage"
    />
    <export-equity
      v-if="exportEquityVisible"
      importType="equity"
      :pageDimObj="equityPageDimObj"
      :selectDimVisible.sync="exportEquityVisible"
      :hasTemplate="false"
    />
  </div>
</template>

<script>
import FianlGragh from "./finalGragh";
import EquityHeader from "./equityHeader";
import StructureGragh from "./structureGragh";
import CopyMonthStructure from "../copyMonthStructure.vue";
import ImportEquity from "../importOwnership";
import ExportEquity from "../importOwnership/dimSelectDownload.vue";
import Empty from "./empty.vue";

import UiUtils from "yn-p1/libs/utils/UiUtils";
import savePromptMixin from "@/mixin/savePrompt.js";
import pageFiled from "../constant";
import EquityTable from "./equityTable.vue";

import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-popover/";
import "yn-p1/libs/components/yn-checkbox/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-input-number/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-select-option/";
import "yn-p1/libs/components/yn-checkbox-group/";
import "yn-p1/libs/components/yn-upload-dragger/";
import "yn-p1/libs/components/yn-message-feedback/";
import defaultParams from "@/mixin/defaultParams";
import commonService from "@/services/common";
import equityServer from "@/services/equity";
import Logger from "yn-p1/libs/modules/log/logger";
import { mapState } from "vuex";
import lang from "@/mixin/lang";
const { $t_structures } = lang;
// const currentUserInfo = DsUtils.getSessionStorageItem("currentUserInfo", {
//   storagePrefix: APPS.NAME,
//   isJson: true
// });
// const dimCodeMapObj = {
//   Period: "monthVal",
//   Version: "versionVal",
//   Year: "yearVal",
//   Scope: "mergeGroupVal"
// };

export default {
  name: "Equity",
  components: {
    EquityHeader,
    FianlGragh,
    StructureGragh,
    CopyMonthStructure,
    ImportEquity,
    ExportEquity,
    EquityTable,
    Empty
  },

  mixins: [savePromptMixin, defaultParams],
  data() {
    return {
      hiddenBtn: false,
      equityPageDimObj: {}, // 头部 维度选择的数据 ()
      equityPageDimCodeObj: {}, // 头部 维度选择的数据的dbCodeIndex ()
      isShowFinalGragh: false, // 是否展示最终持股图
      isShowStructureGragh: false, // 是否展示法人架构图
      showImport: false, // 是否展示导入弹窗
      showCopyMothStruModal: false, // 复制持股比例弹窗
      numVal: 2, // 小数位数
      popoverVisible: false, // 设置单元格列的popover弹窗
      columnsObj: {
        // 单元格 name、id、nma+id 列的展示 默认 name
        shareCellType: "Name", // 默认持股方的name单元格
        shareCellIsFreeze: false, // 持股方是否冻结
        stakeCellType: "Name", // 默认被持股方的那么单元格
        stakeCellIsFreeze: false // 被持股方是否冻结
      },
      shareCellTypeId: "", // 保存持股方唯一标识的id
      stakeCellTypeId: "", // 保存被持股方唯一标识 id
      shareCellIsFreezeId: "", // 保存持股方冻结id
      stakeCellIsFreezeId: "", // 保存被持股方冻结id。
      exportEquityVisible: false,
      warnTitle: $t_structures("save_the_changes_made"),
      saveTableMessage: [], //  保存表格信息
      editing: false,
      equityAuth: {
        "equity-read": false, // 查看
        "equity-add": false, // 新增
        "equity-edit": false, // 编辑
        "equity-delete": false, // 删除
        "equity-copy": false, // 复制
        "equity-import": false, // 导入
        "equity-export": false // 导出
      },
      pageLoading: true,
      submitLoading: false,
      saveBtnLoading: false
    };
  },
  computed: {
    ...mapState("common", {
      currentUserInfo: state => state.currentUserInfo
    }),
    showLockBtn({ equityAuth }) {
      return equityAuth["equity-add"] || equityAuth["equity-edit"];
    }
  },
  watch: {
    popoverVisible(newVal) {
      if (!newVal) {
        const {
          shareCellTypeId,
          stakeCellTypeId,
          columnsObj,
          shareCellIsFreezeId,
          stakeCellIsFreezeId
        } = this;
        const {
          shareCellType,
          stakeCellType,
          shareCellIsFreeze,
          stakeCellIsFreeze
        } = columnsObj;
        commonService("saveOrUpdateUserSetting", {
          key: pageFiled.PAGE_EQUITY_STAKE, // 持股方
          tag: "userIndexName",
          objectId: stakeCellTypeId,
          value: shareCellType
        });
        commonService("saveOrUpdateUserSetting", {
          key: pageFiled.PAGE_EQUITY_SHARE, // 被持股方
          tag: "userIndexName",
          objectId: shareCellTypeId,
          value: stakeCellType
        });
        commonService("saveOrUpdateUserSetting", {
          key: pageFiled.PAGE_EQUITY_STAKE, // 持股方
          tag: "userFreezeColumn",
          objectId: shareCellIsFreezeId,
          value: shareCellIsFreeze
        });
        commonService("saveOrUpdateUserSetting", {
          key: pageFiled.PAGE_EQUITY_SHARE, // 被持股方
          tag: "userFreezeColumn",
          objectId: stakeCellIsFreezeId,
          value: stakeCellIsFreeze
        });
      }
    },
    equityPageDimObj: {
      deep: true,
      handler(nv, ov) {
        this.getLockUser(nv);
      }
    }
  },
  async created() {
    await this.getEquityAuth();
    this.getPrecision();
    // this.getLastSettingV();
  },
  methods: {
    async getEquityAuth() {
      const {
        data: { items }
      } = await commonService("getMetadataRecords", "equityManagerAuth");
      const refId = items.length ? items[0].objectId : "";
      await equityServer("getEquityAuth", { privilegeCode: "Delete", refId })
        .then(res => {
          if (res.data && res.data.items) {
            const vList = [];
            const { items } = res.data;
            items.forEach(item => {
              vList.push(item.value === "TRUE");
              this.equityAuth[item.privilegeCode] = item.value === "TRUE";
            });
            // 其他权限都包含查看权限
            this.equityAuth["equity-read"] = vList.some(v => v);
            // 新增权限包含 编辑权限
            if (this.equityAuth["equity-add"]) {
              this.equityAuth["equity-edit"] = true;
            }
          }
        })
        .finally(() => {
          // 如果没有读取 权限，则关闭loading
          if (!this.equityAuth["equity-read"]) {
            this.pageLoading = false;
          }
        });
    },
    getPrecision() {
      commonService("selectUserScale", "equityManagement").then(res => {
        this._precision = res.data.data;
        this.numVal = this._precision.decimalPlaces;
      });
    },
    setPrecision() {
      commonService("saveOrUpdateUserScale", {
        decimalPlaces: this.numVal,
        objectId: this._precision.objectId,
        pageName: "equityManagement"
      });
    },
    changeDimVal(dimCode, dimVal) {
      this.saveTableMessage.splice(0, this.saveTableMessage.length);
      this.$set(this.equityPageDimObj, dimCode, dimVal);
      this.$set(this.equityPageDimCodeObj, dimCode, dimVal);
    },
    showGrah(isShow, type) {
      if (
        this.saveTableMessage.filter(item => item.type === "warning").length
      ) {
        UiUtils.infoMessage(
          this.$t_equity("multiple_parent_nodes_in_version_year_period")
        );
        return;
      }
      const bool = !this.$refs.equityTable.edited;
      this.savePromptMixin(bool)
        .then(() => {
          this.hiddenBtn = isShow;
          this[`isShow${type}`] = isShow;
          !isShow && this.$refs.equityTable.$refs.grid.updateTableData();
        })
        .catch(() => {
          this.hiddenBtn = !isShow;
        });
    },
    // 导入
    importFile() {
      // this.showImport = true;
      this.$refs.importEquity.openImportModal();
    },
    cancelImport() {
      this.showImport = false;
    },
    onImport() {
      this.showImport = false;
    },
    // 刷新
    async refreshPage() {
      this.pageLoading = true;
      await this.getEquityAuth();
      await this.getLockUser(this.equityPageDimObj);
      this.$refs.equityTable.refshTable();
      this.pageLoading = false;
      // this.$refs.equityTable.getData();
      // this.$refs.equityTable.getRowList();
      // this.$refs.equityTable.getICPAndEntityList();
    },
    copyEvent(isShow) {
      this.showCopyMothStruModal = isShow;
    },
    addEvent() {
      this.$refs.equityTable.addRelation();
    },
    // 获取版本 年 期间 页面维
    getPageDim() {
      const {
        versionVal: version,
        yearVal: year,
        monthVal: period
      } = this.equityPageDimObj;
      return {
        version,
        year,
        period
      };
    },
    // 获取切片数据锁定的用户信息
    async getLockUser(pageDims) {
      const { version, year, period } = this.getPageDim();
      if (!version || !year || !period) return;
      await equityServer("getLockUser", { version, year, period }).then(res => {
        const {
          data: { data }
        } = res;
        if (data) {
          const { userId, operateType } = data;
          if (
            this.currentUserInfo.userId === userId &&
            operateType === "edit"
          ) {
            this.editing = true;
            this.$refs.equityTable.changeCellVal();
          }
        } else {
          this.editing = false;
        }
      });
    },
    // 提交/解锁
    unlockEquity() {
      const { version, year, period } = this.getPageDim();
      return equityServer("unlockEquity", { version, year, period }).then(
        res => {
          this.editing = false;
          return Promise.resolve(true);
        }
      );
    },
    // 没有用户锁定。 或者是自己在一端锁定。又在另一端点击。
    editEquity() {
      this.editing = true;
      this.$refs.equityTable.changeCellVal();
      this.$refs.equityTable.getData();
    },
    resetData() {
      this.editing = true;
      this.$refs.equityTable.getData();
    },
    async btnEvent() {
      if (this.saveBtnLoading) return;
      this.submitLoading = true;
      const { version, year, period } = this.getPageDim();
      if (this.editing) {
        // 解锁、删除操作列
        try {
          await this.$refs.equityTable.submitAndUnlock();
          this.$refs.equityTable.getData();
        } catch (e) {
        } finally {
          this.submitLoading = false;
        }
      } else {
        // 加锁
        equityServer("lockEquity", { version, year, period })
          .then(res => {
            const dataInfo = res.data && res.data.data;
            if (dataInfo && dataInfo.userName) {
              if (this.currentUserInfo.userId === dataInfo.userId) {
                this.editEquity();
                return;
              }
              const toast = {
                edit: this.$t_structures("editing_wait"),
                copy: this.$t_structures("copying_wait"),
                import: this.$t_structures("importing_wait")
              };
              UiUtils.errorMessage(
                `${dataInfo.userName}${toast[dataInfo.operateType]}`
              );
            } else {
              this.editEquity();
            }
          })
          .catch(err => {
            Logger.error(err);
          });
      }
      this.submitLoading = false;
    },
    async saveEvent() {
      if (this.submitLoading) return;
      this.saveBtnLoading = true;
      try {
        await this.$refs.equityTable.saveRelation();
      } catch (e) {
      } finally {
        this.saveBtnLoading = false;
      }
    },
    onChange(checkedValues, type) {
      this[`${type}Value`] = checkedValues;
    },
    // 设置列（id、名称、id + 名称）
    changeColumn(e, type) {
      const bool = !this.$refs.equityTable.edited;
      this.savePromptMixin(bool).then(() => {
        this.columnsObj[`${type}CellType`] = e.target.value;
      });
    },
    // 冻结列
    changeFreezeState(checked, type) {
      this.columnsObj[`${type}CellIsFreeze`] = checked;
    },
    // 消息盒子
    getSaveTableMessage(messageList) {
      this.saveTableMessage = messageList;
    },
    setSettginValue(v, type) {
      const { value: data } = v;
      const res = data.data.data.value;
      if (res) {
        this.columnsObj[type] = type.includes("Freeze") ? res === "true" : res;
      }
      this[`${type}Id`] = data.data.data.objectId;
    },
    // 获取唯一标识
    getLastSettingV() {
      const list = [
        commonService("getLastSettingV", {
          key: pageFiled.PAGE_EQUITY_STAKE, // 持股方 列
          tag: "userIndexName"
        }),
        commonService("getLastSettingV", {
          key: pageFiled.PAGE_EQUITY_SHARE, // 被持股方 列
          tag: "userIndexName"
        }),
        commonService("getLastSettingV", {
          // 持股方冻结
          key: pageFiled.PAGE_EQUITY_STAKE,
          tag: "userFreezeColumn"
        }),
        commonService("getLastSettingV", {
          // 被持股方冻结
          key: pageFiled.PAGE_EQUITY_SHARE,
          tag: "userFreezeColumn"
        })
      ];
      Promise.allSettled(list).then(res => {
        const [
          ShareholderColumn,
          HeldShareholderColumn,
          ShareholderFreeze,
          HeldShareholderFreeze
        ] = res;
        this.setSettginValue(ShareholderColumn, "shareCellType");
        this.setSettginValue(HeldShareholderColumn, "stakeCellType");
        this.setSettginValue(ShareholderFreeze, "shareCellIsFreeze");
        this.setSettginValue(HeldShareholderFreeze, "stakeCellIsFreeze");
      });
    },
    // 保存类型设置
    saveOrUpdateUserSetting(params) {
      return commonService("saveOrUpdateUserSetting", params);
    }
  }
};
</script>

<style scoped lang="less">
@import "../../../commonLess/common.less";

.popover-title {
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  color: @yn-text-color;
  margin-bottom: 10px;
}
.freeze-check {
  margin: 10px 0;
}
.upload-text {
  font-size: 14px;
  color: @yn-text-color;
}
.checkbox-group-style {
  padding: 8px;
  display: flex;
  flex-direction: column;
  max-height: 120px;
  overflow: scroll;
  .ant-checkbox-wrapper {
    margin-left: 0;
    margin-bottom: 0.5rem;
  }
}
</style>
<style lang="less" scoped>
.standFull {
  display: inline-block;
  width: 100%;
  height: 100%;
}
.equity {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .input-number {
    width: 3.625rem;
    margin-left: @rem8;
  }
  .mr8 {
    margin-right: @rem8;
  }
  .equity-content {
    width: 100%;
    height: calc(100% - 6.75rem);
    background: @yn-component-background;
    padding: @yn-padding-xl @yn-padding-xl 0;
    border-top-left-radius: @yn-console-content-radius !important;
    border-top-right-radius: @yn-console-content-radius !important;
  }
  .equity-footer {
    display: flex;
    align-items: center;
    height: 3.25rem;
    padding: 1rem 1rem;
    background: #ffffff;
    box-shadow: inset 0 1px 0 0 #e1e5eb;
    justify-content: end;
  }
  .graph-content {
    height: calc(100% - 3.25rem);
  }
  .content-menu {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .menu-icons {
      display: flex;
      align-items: center;
      height: @rem32;
      .iconBtn {
        cursor: pointer;
        font-size: @rem16;
        color: @yn-label-color;
        margin-right: @rem4;
      }
      .icon-divider {
        height: @rem16;
        margin: 0 @rem16 0 -@rem4;
      }
      .equity-meesage-box {
        margin-right: @rem8;
        display: inline-block;
        /deep/ .newsBubbleButton {
          .error {
            background: @yn-error-color;
          }
        }
        /deep/ .feedback-warning {
          font-size: @rem16;
          span {
            margin-left: @rem8;
          }
        }
      }
    }

    .menu-btns > button:first-child {
      background-color: @yn-primary-color;
      color: @yn-component-background;
    }
  }
  .page-loading {
    position: relative !important;
    /deep/ .ant-spin-spinning {
      max-height: 100vh;
    }
  }
}
</style>
