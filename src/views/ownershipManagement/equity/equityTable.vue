<template>
  <div v-if="showTable" class="content-table">
    <yn-spin :spinning="tableLoading">
      <Grid
        ref="grid"
        :ident="$data.$gridIdent"
        :columns="columns"
        :dataSource="tableData"
        :dropDownData="dropDownData"
        :filterOrder="filterOrder"
        :filterObject="filterObject"
        :paginationInfo="pagination"
        :loadMore="loadMore"
        :hasMore="hasMore"
        :cellClass="cellClass"
        :cellNode="cellNode"
        :cellMouseUp="cellMouseUp"
        :validateCell="validateCell"
        :renderHeaderCell="renderHeaderCell"
        :errorRowInfo="errorRowInfo"
        :firstPageNum="firstPageNum"
        :mixCellWidth="200"
        :addRow="addRow"
        :fixedColumns="fixedColumns"
        :select_customLoader="onCustomLoader"
        :select_loadMoreData="onLoadMoreData"
        :scrollTable="scrollTable"
        :edited="!edited"
        @modifyColWidth="modifyColWidth"
        @select_customSearch="onCustomSearch"
        @changeCellVal="changeCellVal"
        @beforeShowDropDown="beforeShowDropDown"
      />
    </yn-spin>

    <div
      v-if="showSelectInfo.dimCode"
      :style="showSelectInfo.style"
      class="define-async-select"
    >
      <AsynSelectDimMember
        v-if="showSelectInfo.dimCode"
        ref="selectCom"
        :pageName="pageName"
        :value="showSelectInfo.value"
        :needPermission="!!pageName"
        :dimCode="showSelectInfo.dimCode"
        :runChangeVal="false"
        :formatterLabel="formatterLabel"
        :dataInfo="
          showSelectInfo.dimCode === 'ICP' ? iCPDataInfo : entityDataInfo
        "
        :customSearch="onCustomSearch"
        @changeVal="(e, info, bool) => selectEvent(e, info, bool)"
      />
    </div>
  </div>
</template>

<script>
import Grid from "@/components/hoc/grid/index.vue";

import equityService from "@/services/equity";
import commonService from "@/services/common";

import cloneDeep from "lodash/cloneDeep";
import { getDecimal } from "@/utils/common.js";
import PAGE_EQUITY from "../constant";
import { uniqueByAtrr } from "../../../utils/equity";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import cellTypeMap from "@/constant/cellTypeMap";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import { genVDOM } from "@/views/process/control/jdom/utils";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
import { TABLE_COLUMNS } from "../constant.js";
import lang from "@/mixin/lang";
const { $t_equity, $t_common } = lang;
const FIRST_PAGE_NUM = 30;
const DIFF_CELL = [
  "directShareHolding",
  "indirectShareHolding",
  "sysCalcHOLDING",
  "minorityShareHolding",
  "parentCompanyHolding"
];
const saveTips = $t_equity("check_update");
const saveContent = $t_equity("cant_edit_if_noup");
const moduleInfo = {
  content: saveContent,
  moduleName: "equity",
  okText: $t_common("submit"),
  icon: "exclamation-circle"
};

// 单元格内容 内边距
const CELL_CONTENT_PADDING = 16;

export default {
  components: { Grid, AsynSelectDimMember },
  props: {
    equityPageDimObj: {
      type: Object,
      default: () => ({})
    },
    columnsObj: {
      type: Object,
      default: () => ({
        shareCellType: "Name", // 默认持股方的name单元格
        shareCellIsFreeze: "", // 持股方是否冻结
        stakeCellType: "Name", // 默认被持股方的那么单元格
        stakeCellIsFreeze: "" // 被持股方是否冻结
      })
    },
    precision: {
      type: [Number, String], // 小数位数
      default: 2
    },
    unlockEquity: {
      type: Function,
      default: () => {}
    },
    editing: {
      type: Boolean,
      default: false
    },
    equityAuth: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      $pageEquityStake: PAGE_EQUITY.PAGE_EQUITY_STAKE,
      edited: false, // 用于区分是否编辑了 要不要给出全局提示弹窗。
      hasMore: true,
      tableLoading: false,
      columns: [],
      tableData: [],
      mapTableData: {},
      dropDownData: {
        isHolding: [
          {
            title: "Y",
            key: "Y"
          },
          {
            title: "N",
            key: "N"
          }
        ]
      },
      filterObject: {
        isHolding: {
          name: $t_equity("parent_company_holding"),
          data: [
            {
              name: "Y",
              id: "Y"
            },
            {
              name: "N",
              id: "N"
            }
          ]
        }
      }, // 表头可以筛选的对象
      errorRowInfo: {}, // 错误行信息
      firstPageNum: FIRST_PAGE_NUM,
      pagination: {
        total: 0,
        current: 1,
        pageSize: FIRST_PAGE_NUM,
        hasMore: false,
        offset: 0
      },
      shareRowList: [], // 持股方可选列表
      stakeRowList: [], // 被持股可选方列表
      $gridIdent: "equityManagement", // 表格标识
      showTable: false,
      addRow: null, // 新增行数据
      fixedColumns: {
        left: 0
      },
      showSelectInfo: {
        // 下拉信息
        id: "",
        style: {},
        dimCode: "",
        value: "",
        cellContext: {}
      },
      iCPDataInfo: {}, // icp 第一层数据信息。
      entityDataInfo: {}, // entity 被持股方第一层信息。
      dropDownCell: null // 下拉类型，用来区分持股方、被持股方 以便调取对应接口
    };
  },
  computed: {
    // 表头可以筛选的字段
    filterOrder({ columnsObj }) {
      const { shareCellType, stakeCellType } = columnsObj;
      return [`iCP${shareCellType}`, `entity${stakeCellType}`, "isHolding"];
    },
    pageName() {
      if (this.showSelectInfo.dimCode === "ICP") {
        return this.$data.$pageEquityStake;
      } else if (this.showSelectInfo.dimCode === "Entity") {
        return PAGE_EQUITY.PAGE_EQUITY_SHARE;
      }
      return undefined;
    },
    computedPageDim() {
      return cloneDeep(this.equityPageDimObj);
    }
  },
  watch: {
    precision() {
      this.setDecimalNum();
    },
    computedPageDim: {
      deep: true,
      handler(nv, ov) {
        // 切换 持股公司不执行
        if (
          nv.holdingCompay !== ov.holdingCompay &&
          nv.holdingCompay &&
          ov.holdingCompay
        ) {
          return;
        }
        this.getData();
        this.getICPAndEntityList();
      }
    },
    columnsObj: {
      immediate: true,
      deep: true,
      handler(nv, ov) {
        this.getColumns();
        this.setReadOnly();
        this.getRowList();
        // 设置冻结列
        this.setTingfixedColumns(nv);
      }
    },
    editing: {
      handler(v) {
        if (v) {
          // 编辑态添加列（用户锁定）
          this.addActionColumn();
        } else {
          // 查看台 （用户解锁）
          this.deleteActionColumn();
        }
        this.setReadOnly();
      }
    }
  },
  created() {},
  methods: {
    handleTableData(data, offset) {
      return data.map((item, index) => {
        item["iCPCodeAndName"] = `${item.iCPCodeIndex} ${item.iCPName}`;
        item["iCPCode"] = item.iCPCodeIndex;
        item[
          "entityCodeAndName"
        ] = `${item.entityCodeIndex} ${item.entityName}`;
        item["entityCode"] = item.entityCodeIndex;
        const rowKey = item.objectId;
        const res = {};
        res.serialNumber = {
          v: index + offset + 1,
          objectId: rowKey,
          rowKey,
          level: 1
        };
        res.key = item.objectId;
        res.objectId = item.objectId;
        const readOnlyColList = [
          "serialNumber",
          "indirectShareHolding",
          "sysCalcHOLDING",
          "minorityShareHolding",
          "parentCompanyHolding"
        ];
        Object.keys(item).forEach(keyName => {
          if (keyName !== "objectId") {
            res[keyName] = {
              v: item[keyName],
              oldV: item[keyName],
              objectId: rowKey,
              customValidate: true, // 业务方，单元格校验
              rowKey,
              // 持股方、被持股方、序号、持股比例、计算列 readOnly 都为 true。
              // 持股方、被持股方、名不固定。
              readOnly:
                readOnlyColList.includes(keyName) ||
                !!(keyName.includes("iCP") || keyName.includes("entity")) ||
                !(this.editing && item.hasWriteAuth) ||
                !this.editing
            };
          }
        });
        this.handleKeyDecimal(res);
        return res;
      });
    },
    cellClass(cellContext) {
      // 给单元格添加类名
      const { colName, rowId } = cellContext;
      const dataInfo = this.mapTableData[rowId];
      if (this.editing || rowId.length <= 13) {
        const bool = rowId.length <= 13 || (dataInfo && dataInfo.hasWriteAuth); // 新增行或者有单元格权限
        if (colName.includes("iCP")) {
          return `icp-cell ${bool ? "action-cell" : ""} `;
        }
        if (colName.includes("entity")) {
          return `entity-cell ${bool ? "action-cell" : ""} `;
        }
        if (colName.includes("action")) {
          if (dataInfo && !dataInfo.hasWriteAuth) return "action-cell-disabled";
        }
      } else {
        return "simple-cell-readonly";
      }
    },
    cellNode(cellContext) {
      // rowId 即每条数据的objectId
      const { colName, value, td } = cellContext;
      if (DIFF_CELL.includes(colName) && !isNaN(Number(value))) {
        return value ? `${value}%` : "0%";
      }
      if (colName.includes("iCP") || colName.includes("entity")) {
        const cellDom = this.renderCellDom(cellContext);
        return cellDom;
      }
      if (colName === "action") {
        const tdNode = td;
        const childDiv = document.createElement("div");
        const aNode = document.createElement("a");
        aNode.textContent = this.$t_common("delete");
        aNode.addEventListener("click", e => this.deleteRecord(e, cellContext));
        childDiv.appendChild(aNode);
        tdNode.appendChild(childDiv);
        return childDiv;
      }
    },
    formatterLabel(item) {
      let res = item.label || item.dimMemberRealName;
      const titleMap = {
        Name: item.label || item.dimMemberRealName,
        Code: item.dbCodeIndex,
        CodeAndName: `${item.dbCodeIndex} ${item.dimMemberRealName}`
      };
      if (this.showSelectInfo.dimCode === "ICP") {
        res = titleMap[this.columnsObj.shareCellType];
      }
      if (this.showSelectInfo.dimCode === "Entity") {
        res = titleMap[this.columnsObj.stakeCellType];
      }
      return res;
    },
    renderCellDom(cellContext) {
      const { value: cellValue } = cellContext;
      return genVDOM({
        template: `
          <div class='dim-cell'>
            <span class='dim-cell-value'>${cellValue}</span>
          </div>
        `
      });
    },
    setReadOnly() {
      this.columns = this.columns.map(item => {
        item.originReadOnly =
          item.originReadOnly === undefined
            ? item.readOnly
            : item.originReadOnly;
        item.readOnly = this.editing ? item.originReadOnly : true;
        return item;
      });
    },
    cellMouseUp(event, cellContext) {
      if (!cellContext) return;
      const { colName, rowId } = cellContext;
      const rowInfo = this.mapTableData[rowId];
      if (rowInfo && (!this.editing || !rowInfo.hasWriteAuth)) return;
      this.hideComponents();
      if (colName.includes("iCP") || colName.includes("entity")) {
        this.handleSelect(cellContext);
        return;
      }
    },
    getTdPosStyle(td) {
      const { left, top, width } = this.$refs.grid.getPopPosByTd(td);
      const { offsetHeight } = td;
      return {
        left,
        top: top - offsetHeight,
        width
      };
    },
    handleSelect(cellContext) {
      const toBeSaveObj = this.$refs.grid.getToBeSaveObj();
      // const toBeSaveObj = {};
      const { colName, row, col, rowId } = cellContext;
      // 用来判断是否是固定列  td 取法不同
      const td = this.$refs.grid.$refs.simpleGrid.getCellMeta(row, col).td;
      const { top, left, width } = this.getTdPosStyle(td);
      const dimCode = colName.includes("entity")
        ? "Entity"
        : colName.includes("iCP")
          ? "ICP"
          : "";
      const i = `${dimCode[0].toLowerCase()}${dimCode.slice(1)}Id`;
      const type =
        dimCode === "ICP"
          ? `iCP${this.columnsObj.shareCellType}`
          : `entity${this.columnsObj.stakeCellType}`;
      const id = toBeSaveObj[rowId] && toBeSaveObj[rowId][type];
      const chooseV =
        id || (this.mapTableData[rowId] ? this.mapTableData[rowId][i] : "");
      this.showSelectInfo = {
        style: {
          left: left + CELL_CONTENT_PADDING - 6 + "px",
          top: top + 1 + "px",
          width: width - CELL_CONTENT_PADDING * 2 + 6 + "px"
        },
        dimCode,
        value: chooseV,
        cellContext
      };
    },
    hideComponents() {
      this.$set(this, "showSelectInfo", {
        id: "",
        style: {},
        dimCode: "",
        value: "",
        cellContext: {}
      });
    },
    scrollTable() {
      this.hideComponents();
    },
    selectEvent(e, info, bool = true) {
      if (!bool) return;
      const showWord = info[0].label;
      const { cellContext = {} } = this.showSelectInfo;
      const { row, col, rowId, colName } = cellContext;
      const cellProp = { ...cellContext };
      cellProp.value = showWord;
      cellProp.originVal = showWord;
      this.showSelectInfo.value = showWord;
      cellProp.checkedKeys = info[0].key;
      this.$refs.grid.updateCell(row, col, cellProp, showWord);
      this.$refs.grid.setTobeSaveObj({
        rowKey: rowId,
        colName,
        val: e
      });
      // 点击编辑的时候添加
      this.edited = true;
      this.addCallBackFnMixin(
        this.submitAndUnlock.bind(this),
        saveTips,
        "",
        moduleInfo
      );
      this.hideComponents();
    },
    modifyColWidth() {
      this.hideComponents();
    },
    scrollToL() {
      this.$nextTick(() => {
        const dom = document.getElementsByClassName("wtHolder")[0];
        if (dom) {
          dom.scrollTo(1, 0);
          dom.scrollTo(0, 0);
        }
      });
    },
    deleteRecord(e, cellContext) {
      // e => {
      //     this.dataSource.splice(row - 1, 1);
      //     this.$message.info(e.target.innerText + colName + row);
      //   }
      // 调用接口删除行。
      const { row, rowId, instance } = cellContext;
      const dataInfo = this.mapTableData[rowId];
      if (dataInfo && !dataInfo.hasWriteAuth) return;
      if (rowId.length <= 13) {
        // 不是删除库里的数据不需要调用接口
        // row, count 参数 第几行 多少个
        this.tableData[row - 1] && this.tableData.splice(row - 1, 1);
        this.$refs.grid.$refs.simpleGrid.removeRows(row, 1);
        this.$refs.grid.updateTableData(this.tableData);
        // this.$refs.grid.$refs.simpleGrid.deleteEditVMap(rowId);
        this.$refs.grid.deleteTobeSaveObjById(rowId);
        return;
      }
      const rowInfo = instance.getCellMetaAtRow(row);
      const pageDims = this.getPageDim();
      // this.savePromptMixin(!this.edited).then(() => {
      UiUtils.confirm({
        title: this.$t_equity("sure_delete"),
        content: this.$t_equity("sure_delete_equity", [
          `${rowInfo[1].value} - ${rowInfo[2].value}`
        ]),
        onOk: () => {
          this.tableLoading = true;
          equityService("deleteEquityManagers", {
            objectIds: [rowId],
            ...pageDims
          })
            .then(res => {
              UiUtils.successMessage(this.$t_common("new_delete_success"));
              // this.$refs.grid.$refs.simpleGrid.removeRows(row, 1);
              // this.getData();
              this.tableData.splice(row - 1, 1);
              this.$refs.grid.updateTableData(this.tableData);
              this.$refs.grid.deleteTobeSaveObjById(rowId);
              this.getICPAndEntityList();
            })
            .finally(() => {
              this.tableLoading = false;
            });
        }
      });
      // });
    },
    // 设置冻结列
    setTingfixedColumns(nv, ov) {
      const { shareCellIsFreeze, stakeCellIsFreeze } = nv;
      let fixNum = 0;
      if (stakeCellIsFreeze) {
        fixNum = 3;
      } else if (shareCellIsFreeze) {
        fixNum = 2;
      } else {
        fixNum = 0;
      }
      if (this.$refs.grid) {
        this.fixedColumns.left = fixNum;
        this.$refs.grid.setFixedColumns(fixNum);
      }
    },
    // 获取版本 年 期间 页面维
    getPageDim() {
      const {
        versionVal: version,
        yearVal: year,
        monthVal: period
      } = this.equityPageDimObj;
      return {
        version,
        year,
        period
      };
    },
    // 校验单元格
    validateCell(cell) {
      const { colName } = cell;
      const methodMap = {
        remark: this.validateCellRemark,
        directShareHolding: this.validateCellDirectShareHolding
      };
      if (methodMap[colName]) {
        return methodMap[colName](cell);
      } else {
        return "";
      }
    },
    // 验证备注
    validateCellRemark(cell) {
      const { originVal } = cell;
      const result = {
        res: true,
        paperwork: ""
      };
      if (originVal && originVal.length > 64) {
        result.res = false;
        result.paperwork = this.$t_process("field_length_exceeds_64");
      }
      return result;
    },
    // 直接持股比例 校验
    validateCellDirectShareHolding(cell) {
      const { originVal } = cell;
      const num = originVal.replace("%", "");
      let result = {
        res: true,
        paperwork: ""
      };
      if (!num) {
        result = {
          res: false,
          paperwork: this.$t_equity("enter_numerical")
        };
      } else if (isNaN(num)) {
        result = {
          res: false,
          paperwork: this.$t_equity("enter_correct_numerical")
        };
      } else if (num < 0 || num > 100) {
        result = {
          res: false,
          paperwork: this.$t_equity("between_0_100")
        };
      }
      return result;
    },
    changeCellVal(cell) {
      if (cell) {
        this.edited = true;
      }
      this.addCallBackFnMixin(
        this.submitAndUnlock.bind(this),
        saveTips,
        "",
        moduleInfo
      );
    },
    // 验证
    getColumns() {
      const { shareCellType, stakeCellType } = this.columnsObj;
      const columns = [
        {
          title: this.$t_common("order_number"),
          width: 80,
          dataIndex: "serialNumber",
          cellType: "text" // 新增
        },
        {
          title: this.$t_structures("investor"),
          dataIndex: `iCP${shareCellType}`,
          isFilter: true, // 新增，grid 组件监听此值，默认展示表头下拉组件
          cellType: "text",
          readOnly: true
        },
        {
          title: this.$t_structures("investee"),
          dataIndex: `entity${stakeCellType}`,
          isFilter: true,
          cellType: "text",
          readOnly: true
        },
        {
          title: this.$t_structures("direct_shareholding_ratio"),
          dataIndex: "directShareHolding",
          cellType: "text",
          align: "right",
          readOnly: false
        },
        {
          title: this.$t_structures("indirect_shareholding_ratio"),
          dataIndex: "indirectShareHolding",
          width: 150,
          align: "right",
          cellType: "text"
        },
        {
          title: this.$t_structures("calculated_comp_shareholding_ratio"),
          dataIndex: "sysCalcHOLDING",
          width: 150,
          align: "right",
          cellType: "text"
        },
        ...TABLE_COLUMNS(),
        {
          title: this.$t_structures("parent_company_holding_shares"),
          dataIndex: "isHolding",
          width: 200,
          isFilter: true,
          cellType: "dropDown",
          readOnly: false,
          paste: false
        },
        {
          title: this.$t_process("comments"),
          dataIndex: "remark",
          width: 220,
          cellType: "text",
          readOnly: false
        }
      ];
      this.columns = columns;
      this.addActionColumn();
      if (this.$refs.grid && this.$refs.grid.$refs.simpleGrid) {
        this.$refs.grid.$refs.simpleGrid.updateBody(this.tableData);
      }
    },
    addActionColumn() {
      if (!this.editing || !this.equityAuth["equity-delete"]) return;
      this.columns.push({
        title: this.$t_process("operation"),
        width: 100,
        dataIndex: "action",
        cellType: "text"
      });
    },
    deleteActionColumn() {
      // 如果没有删除权限则不需理会
      const lastColumn = this.columns.slice(-1)[0];
      if (lastColumn.dataIndex === "action") {
        this.columns = this.columns.slice(0, this.columns.length - 1);
      }
    },
    addPropsFun(data, type) {
      if (!data || !data.length) return;
      let arr = [...data];
      while (arr.length) {
        const item = arr.shift();
        const titleMap = {
          Name: item.dimMemberRealName,
          Code: item.dbCodeIndex,
          CodeAndName: `${item.dbCodeIndex} ${item.dimMemberRealName}`
        };
        item.pId = item.dimMemberParentId;
        item.key = item.objectId;
        item.title = titleMap[type];
        item.value = titleMap[type];
        item.isLeaf = item.dimMemberHasChildren === "FALSE";
        item.disabled = !(item.dimMemberHasChildren === "FALSE");
        item.isChildren = !(item.dimMemberHasChildren === "FALSE");
        if (item.children && item.children.length > 0) {
          item.disabled = true;
          arr = arr.concat(item.children);
        }
      }
      return data;
    },
    // 设置下拉
    // 获取 持股方/被持股房 列 下拉数据
    getRowList(cb) {
      const { shareCellType, stakeCellType } = this.columnsObj;
      const limit = 10;
      const offset = 0;
      const apiList = [
        // commonService("getCompanyTree"),
        // commonService("getEntityTree")
        commonService("getFirstDimMemberList", {
          limit,
          offset,
          dimCode: "ICP",
          needPermission: true,
          pageName: this.$data.$pageEquityStake
        }),
        commonService("getFirstDimMemberList", {
          limit,
          offset,
          dimCode: "Entity",
          needPermission: true,
          pageName: PAGE_EQUITY.PAGE_EQUITY_SHARE
        })
      ];
      Promise.allSettled(apiList).then(res => {
        // 持股方、被持股方
        const [shareRow, stakeRow] = res;
        if (shareRow.status === "fulfilled") {
          const {
            value: {
              data: { items }
            }
          } = shareRow;
          this.iCPDataInfo = shareRow.value.data;
          this.shareRowList = this.addPropsFun(items, shareCellType);
        }
        if (stakeRow.status === "fulfilled") {
          const {
            value: {
              data: { items }
            }
          } = stakeRow;
          this.entityDataInfo = stakeRow.value.data;
          this.stakeRowList = this.addPropsFun(items, stakeCellType);
        }
        this.dropDownData[`iCP${shareCellType}`] = this.shareRowList;
        this.dropDownData[`entity${stakeCellType}`] = this.stakeRowList;
        this.iCPCount = this.shareRowList.length;
        this.iCPIndex = 10;
        this.entityCount = this.stakeRowList.length;
        this.entityIndex = 10;
        cb && cb();
      });
    },

    // 获取头部筛选列表 0 持股方、1 被持股方
    getICPAndEntityList() {
      const pageDims = this.getPageDim();
      if (!Object.keys(pageDims).every(k => pageDims[k])) return;
      const apiList = [
        equityService("getICPAndEntityList", {
          ...pageDims,
          codeType: 0
        }),
        equityService("getICPAndEntityList", {
          ...pageDims,
          codeType: 1
        })
      ];
      const getResult = (type, list, name) => {
        const {
          value: { data }
        } = list;
        const typeMap = {
          NameList: data.map(item => ({
            id: item.objectId,
            name: item.dimMemberRealName
          })),
          CodeList: data.map(item => ({
            id: item.objectId,
            name: item.dbCodeIndex
          })),
          CodeAndNameList: data.map(item => ({
            id: item.objectId,
            name: `${item.dbCodeIndex} ${item.dimMemberRealName}`
          }))
        };
        ["Name", "Code", "CodeAndName"].forEach(key => {
          this.$set(this.filterObject, `${type}${key}`, {
            name,
            data: typeMap[`${key}List`]
          });
        });
      };
      Promise.allSettled(apiList)
        .then(res => {
          const [shareDataList, stakeDataList] = res;
          getResult("iCP", shareDataList, this.$t_structures("investor"));
          getResult("entity", stakeDataList, this.$t_structures("investee"));
        })
        .catch(() => {
          const empty = { value: { data: [] } };
          getResult("iCP", empty, this.$t_structures("investor"));
          getResult("entity", empty, this.$t_structures("investee"));
        });
    },
    setMapTableData(data) {
      const res = {};
      data.forEach(item => {
        const { objectId } = item;
        res[objectId] = item;
      });
      this.mapTableData = Object.assign({}, this.mapTableData, res);
    },
    // 重置一些数据
    resetData() {
      Object.assign(this, {
        tableLoading: true,
        // showTable: false,
        errorRowInfo: {}
      });
      this.$emit("getSaveTableMessage", []);
      // 清空筛选条件
      if (this.$refs.grid) {
        this.$refs.grid.clearAll();
        const simpleGrid = this.$refs.grid.$refs.simpleGrid;
        simpleGrid.clearEditVMap(); // 清除编辑缓存
        this.$refs.grid.clearToBeSaveObj(); // 清除带保存信息
      }
      this.edited = false;
    },
    refshTable() {
      this.tableLoading = false;
      this.getColumns();
      this.setReadOnly();
      this.resetData();
      this.getData();
      this.getRowList();
      this.getICPAndEntityList();
      if (!this.editing) {
        // 如果不是编辑态 则需要情调保存函数
        this.clearCommonSaveEventsMixin();
      }
    },
    async getData() {
      // // 判断页面维是否都设置值,防止多次请求
      const pageDims = this.getPageDim();
      if (!Object.keys(pageDims).every(k => pageDims[k])) return;
      this.resetData();
      // resetData 中 存在下面两行代码
      // const simpleGrid = this.$refs.grid.$refs.simpleGrid;
      // simpleGrid && simpleGrid.clearEditVMap(); // 清除编辑缓存
      await equityService("getEquityManagerList", {
        ...pageDims,
        offset: 0,
        limit: FIRST_PAGE_NUM
      })
        .then(res => {
          if (res.data) {
            this.showTable = true;
            const { items, totalCount, hasMore } = res.data;
            this.setMapTableData(items || []);
            this.tableData = [...this.handleTableData(items || [], 0)];
            this.$set(this, "tableData", this.tableData);
            this.$nextTick(() => {
              this.$refs.grid.updateTableData(this.tableData);
            });
            // this.$refs.grid.$refs.simpleGrid.updateBody(this.tableData);
            this.pagination.total = totalCount;
            this.pagination.hasMore = hasMore;
            this.hasMore = hasMore;
            this.pagination.offset = FIRST_PAGE_NUM;
            this.pagination.current++;
            return res;
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.scrollToL();
          this.$emit("getLastSettingV");
        });
    },
    loadMore(params) {
      const pageDims = this.getPageDim();
      const { pageSize: limit, offset } = this.pagination;
      if (!Object.keys(pageDims).every(k => pageDims[k])) return;
      const { isFirstPage, searchObj = [] } = params;
      const dataOffset = isFirstPage ? 0 : offset;
      if (isFirstPage) {
        this.pagination.offset = 0;
        this.tableLoading = true; //  如果是过滤则在此设置loading 因为simple组件内也有loading
      }
      const searchParams = this.getSearchParams(searchObj);
      return equityService("getEquityManagerList", {
        ...pageDims,
        offset: dataOffset,
        limit: isFirstPage ? FIRST_PAGE_NUM : limit,
        ...searchParams
      })
        .then(res => {
          if (res.data) {
            const { items, totalCount, hasMore } = res.data;
            const data = this.handleTableData(items || [], dataOffset);
            if (isFirstPage) {
              this.tableData = cloneDeep(data);
            } else {
              this.tableData.push(...cloneDeep(data));
            }
            this.setMapTableData(items || []);
            this.pagination.total = totalCount;
            this.pagination.hasMore = hasMore;
            this.hasMore = hasMore;
            this.pagination.offset = dataOffset + FIRST_PAGE_NUM;
            this.pagination.current++;
            this.$set(this, "pagination", this.pagination);
            return {
              data,
              hasMore,
              tableType: "equity"
            };
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    getSearchParams(arr) {
      const { shareCellType, stakeCellType } = this.columnsObj;
      const res = {
        icpList: [],
        entityList: []
      };
      const typeMap = {
        [`iCP${shareCellType}`]: "icpList",
        [`entity${stakeCellType}`]: "entityList"
      };
      arr.forEach(item => {
        const { type, searchArr } = item;
        if (type === "isHolding") {
          res["isHolding"] = searchArr.length === 1 ? searchArr[0].id : "";
        } else {
          searchArr.map(obj => {
            const { id } = obj;
            if (res[typeMap[type]].indexOf(id) === -1) {
              res[typeMap[type]].push(id);
            }
          });
        }
      });
      return res;
    },
    handleKeyDecimal(item) {
      const count =
        this.precision < 0 ? 0 : this.precision > 9 ? 9 : this.precision;
      DIFF_CELL.forEach(key => {
        item[key].v = getDecimal(item[key].oldV, count);
      });
    },
    // 设置 直接持股比例、间接持股比例、系统计算持股比例的小数位
    setDecimalNum() {
      if (this.tableData.length === 0) return;
      const currentTableData = cloneDeep(this.tableData);
      currentTableData.map(item => {
        this.handleKeyDecimal(item, this.precision);
        return item;
      });
      this.tableData = currentTableData;
      this.$refs.grid.$refs.simpleGrid.updateBody(this.tableData);
    },
    // 处理表头
    renderHeaderCell(cellContext) {},

    // generateRow
    generateRow() {
      const res = {};
      this.columns.forEach(item => {
        res[item.dataIndex] = {
          v: item.dataIndex === "isHolding" ? "Y" : "",
          cellDataTypeId: cellTypeMap[item.cellType]
        };
      });
      return res;
    },
    // 新增持股关系
    addRelation() {
      if (this.tableData[0] && this.tableData[0].flag) {
        UiUtils.warningMessage(this.$t_structures("save_previous_data_first"));
        return;
      }
      const key = AppUtils.generateUniqueId();
      this.addRow = {
        key,
        objectId: key,
        flag: true, // 上条数据未保存
        ...this.generateRow()
      };
      this.tableData.unshift(this.addRow);
      this.$refs.grid.updateTableData(this.tableData);
      // const simpleGrid = this.$refs.grid.$refs.simpleGrid;
      // simpleGrid.insertRows(1, 1);
      if (this.tableData.length === 1) {
        // 解决 grid 问题。 当只有一条数据的时候这时候横向滚动删除后，然后新增 出现表头不对齐的情况。
        this.scrollToL();
      }
      // this.edited = true;
      // this.addCallBackFnMixin(
      //   this.submitAndUnlock.bind(this),
      //   saveTips,
      //   "",
      //   moduleInfo
      // );
    },
    // 获取保存入参
    getSaveParams(toBeSaveObj) {
      const reqParams = {
        ...this.getPageDim(),
        offset: 0,
        limit: FIRST_PAGE_NUM,
        lists: []
      };
      Object.keys(toBeSaveObj).forEach(key => {
        const res = cloneDeep(this.mapTableData[key]) || {};
        const editObj = cloneDeep(toBeSaveObj[key]);
        for (const key in editObj) {
          if (key === `iCP${this.columnsObj.shareCellType}`) {
            editObj.iCPId = editObj[key];
            delete editObj[key];
          }
          if (key === `entity${this.columnsObj.stakeCellType}`) {
            editObj.entityId = editObj[key];
            delete editObj[key];
          }
        }
        if (!Object.keys(res).length) {
          res.entityId = editObj.entityId;
          res.iCPId = editObj.iCPId;
          res.objectId = key;
          res.isHolding = "Y";
          res.remark = editObj.remark;
        }
        const {
          directShareHolding,
          entityId: entity,
          iCPId: icp,
          indirectShareHolding,
          isHolding,
          objectId,
          remark
        } = Object.assign(res, editObj);
        reqParams.lists.push({
          directShareHolding,
          entity,
          icp,
          indirectShareHolding,
          isHolding,
          objectId,
          remark
        });
      });
      return reqParams;
    },
    // 提交/解锁
    async submitAndUnlock() {
      return new Promise(async (resolve, reject) => {
        const toBeSaveObj = this.$refs.grid.getToBeSaveObj();
        let res = "noneedSave";
        if (Object.keys(toBeSaveObj).length) {
          try {
            res = await this.saveRelation();
          } catch (e) {
            res = "error";
          }
        }
        // 不需要保存 或者 保存没问题则进行提交/解锁
        if (res === "success" || res === "noneedSave") {
          const r = await this.unlockEquity();
          if (r) {
            await this.getData();
            this.clearCommonSaveEventsMixin();
            resolve(this.$t_common("submit_success"));
          } else {
            reject(this.$t_common("submit_failed"));
          }
        } else {
          reject(this.$t_common("submit_failed"));
        }
      });
    },
    // 保存
    async saveRelation() {
      const toBeSaveObj = this.$refs.grid.getToBeSaveObj();
      const hasNewAdd = this.tableData.filter(
        item => item.objectId.length <= 13
      );
      if (hasNewAdd.length) {
        // 存在新增 空行不允许被保存
        const rowFir = this.tableData[0];
        if (!~Object.keys(toBeSaveObj).indexOf(rowFir.key)) {
          UiUtils.warningMessage(this.$t_structures("not_allowed_to_saved"));
          return;
        }
        const newAddIds = Object.keys(toBeSaveObj).filter(
          key => key.length <= 13
        );
        const newAddItems = newAddIds.map(id => toBeSaveObj[id]);
        if (newAddItems.length) {
          // 新增编辑
          if (newAddItems.some(item => Object.keys(item).length < 3)) {
            UiUtils.warningMessage(
              this.$t_structures("complete_information_first")
            );
            return;
          }
        }
      } else {
        // 没新增、没修改项
        if (!Object.keys(toBeSaveObj).length) {
          UiUtils.warningMessage(this.$t_structures("no_items_saved"));
          this.$emit("getSaveTableMessage", []);
          if (Object.keys(this.errorRowInfo).length) {
            this.edited = false;
            this.getData();
          }
          return;
        }
      }
      // 前端校验 最终持股是否有超过100的单元格
      if (document.querySelector(".verification-failed")) {
        // 校验失败
        UiUtils.error({
          title: this.$t_process("validation_failed"),
          content: this.$t_structures("modify_values")
        });
        this.$emit("changeLoadingStatus", false);
        return Promise.reject(this.$t_structures("value_cell_input_operation"));
      }
      this.tableLoading = true;
      const params = this.getSaveParams(toBeSaveObj);
      // 调接口
      return await equityService("operateEquityManagers", params)
        .then(res => {
          this.errorRowInfo = {};
          if (res.data) {
            const {
              equityManagerCheckVOList,
              equityManagerVOList,
              errorNum,
              equityManagerErrorList
            } = res.data.data;
            if (equityManagerVOList && equityManagerVOList.length) {
              this.tableData = [
                ...this.handleTableData(equityManagerVOList, 0)
              ];
              this.$nextTick(() => {
                this.$refs.grid.updateTableData(this.tableData);
              });
            }
            if (
              equityManagerCheckVOList &&
              equityManagerCheckVOList.length &&
              errorNum
            ) {
              const errorList = uniqueByAtrr(
                equityManagerErrorList,
                "objectId"
              );
              errorList.forEach(item => {
                const { objectId, type } = item;
                this.errorRowInfo[objectId] = { key: objectId, type };
                this.$set(this, "errorRowInfo", this.errorRowInfo);
              });

              // // 部分校验成功 需要删除 成功的 编辑缓存。
              const toBeSaveObjKeys = Object.keys(
                this.$refs.grid.getToBeSaveObj()
              );
              const errorObjectList = errorList.map(item => item.objectId);
              toBeSaveObjKeys.forEach(key => {
                if (!errorObjectList.includes(key)) {
                  this.$refs.grid.deleteTobeSaveObjById(key);
                }
              });
              // this.$refs.grid.clearToBeSaveObj(); // 清除带保存信息 点击保存的时候会重新从simpleGrid 中获取。

              this.$emit("getSaveTableMessage", equityManagerCheckVOList);

              UiUtils.warningMessage(
                `${this.$t_process("validation_failed")}！`
              );
              this.$refs.grid.clearAll(false);
              return Promise.reject(
                this.$t_structures("backend_verification_failed")
              );
            } else {
              this.errorRowInfo = {};
              const simpleGrid = this.$refs.grid.$refs.simpleGrid;
              simpleGrid.clearEditVMap(); // 清除编辑缓存
              this.$refs.grid.clearToBeSaveObj(); // 清除带保存信息
              !this.editing && this.clearCommonSaveEventsMixin(); // 非编辑态才清空，因为切换页面维也需要弹窗提示
              UiUtils.successMessage(this.$t_common("save_success"));
              this.$emit("getSaveTableMessage", []);
              this.getData();
              // 获取筛选列表
              this.getICPAndEntityList();
              return Promise.resolve("success");
            }
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    onCustomLoader(item, _this) {
      if (item.key) {
        const { shareCellType, stakeCellType } = this.columnsObj;
        return new Promise((resolve, reject) => {
          commonService("getCommonDimMemberList", {
            limit: 10,
            offset: 0,
            dimMemberId: item.key
          }).then(res => {
            const {
              data: { items, hasMore }
            } = res;
            // resolve(this.addPropsFun(items, this.dropDownCell.includes("entity") ? stakeCellType : shareCellType));
            const list = items.map(item => {
              const titleMap = {
                Name: item.dimMemberRealName,
                Code: item.dbCodeIndex,
                CodeAndName: `${item.dbCodeIndex} ${item.dimMemberRealName}`
              };
              const type = this.dropDownCell.includes("entity")
                ? stakeCellType
                : shareCellType;
              return {
                ...item,
                pId: item.dimMemberParentId,
                key: item.objectId,
                title: titleMap[type],
                value: titleMap[type],
                isLeaf: item.dimMemberHasChildren === "FALSE",
                isChildren: !(item.dimMemberHasChildren === "FALSE"),
                disabled: !(item.dimMemberHasChildren === "FALSE")
              };
            });
            resolve({
              hasMore,
              data: list
            });
          });
        });
      }
    },
    onLoadMoreData(node, _this) {
      if (node) {
        // 子项加载更多
        const { children, objectId: dimMemberId } = node;
        const childrenLen = children.length;
        const { shareCellType, stakeCellType } = this.columnsObj;
        return new Promise((resolve, reject) => {
          commonService("getCommonDimMemberList", {
            limit: 10,
            offset: childrenLen,
            dimMemberId
          }).then(res => {
            const {
              data: { items, hasMore }
            } = res;
            const type = this.dropDownCell.includes("entity")
              ? stakeCellType
              : shareCellType;
            const list = this.addPropsFun(items, type);
            resolve({
              hasMore,
              data: list
            });
          });
        });
      } else {
        // 第一页加载更多
        let count = 0;
        let index = 10;
        let list = [];
        if (this.dropDownCell.includes("entity")) {
          index = this.entityIndex;
          this.entityIndex += 10;
          count = this.entityCount;
          list = this.dropDownData[`entity${this.columnsObj.stakeCellType}`];
        } else {
          index = this.iCPIndex;
          this.iCPIndex += 10;
          count = this.iCPCount;
          list = this.dropDownData[`iCP${this.columnsObj.shareCellType}`];
        }
        return new Promise((resolve, reject) => {
          resolve({
            hasMore: index + 1 < count,
            data: list.slice(index, index + 10)
          });
        });
      }
    },
    onCustomSearch(searchVal, _this) {
      const { shareCellType, stakeCellType } = this.columnsObj;
      let searchType = 0;
      const searchMap = {
        Name: 0,
        Code: 1,
        CodeAndName: 2
      };
      if (!this.showSelectInfo.cellContext.colName) return;
      const bool = this.showSelectInfo.cellContext.colName.includes("entity");
      if (bool) {
        searchType = searchMap[stakeCellType];
      } else {
        searchType = searchMap[shareCellType];
      }
      return commonService("getQueryDimMemberList", {
        dimCode: bool ? "Entity" : "ICP",
        keyWord: searchVal,
        searchType,
        needPermission: true,
        pageName: bool
          ? PAGE_EQUITY.PAGE_EQUITY_SHARE
          : this.$data.$pageEquityStake
      });
    },
    beforeShowDropDown(cellContext, callback) {
      this.iCPIndex = 10;
      this.entityIndex = 10;
      const { colName } = cellContext;
      this.dropDownCell = colName;
    }
  }
};
</script>

<style lang="less">
.icp-cell,
.entity-cell {
  & > div {
    width: 100%;
    height: 100%;
    .dim-cell {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .dim-cell-value {
        max-width: calc(100% - @rem24);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .dim-cell-icon {
        height: 24px;
        .icon {
          width: 12px;
          height: 12px;
          color: @yn-disabled-color;
        }
      }
    }
  }
}
</style>
<style lang="less" scoped>
.content-table {
  width: 100%;
  margin-top: 1rem;
  height: calc(100% - 2.75rem);
  position: relative;
  .define-async-select {
    position: absolute;
    z-index: 999;
    /deep/.yn-select-tree {
      border: 0;
      box-shadow: none;
    }
  }
  /deep/.action-cell {
    background: @yn-body-background;
  }
  /deep/.action-cell-disabled {
    background: @yn-disabled-bg-color;
    a {
      cursor: not-allowed !important;
      color: @yn-disabled-color;
    }
  }
  /deep/.dim-cell {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
