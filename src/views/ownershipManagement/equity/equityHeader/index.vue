<template>
  <div class="equity-header">
    <!-- <div :class="['header-top']"> -->
    <yn-page-title :title="title" :allowBack="!isShowBtn" @back="goBack">
      <template v-slot:extraRender>
        <div class="titleExtraRender">
          <div>
            <yn-divider type="vertical" class="titleDivider" />
            <PageDimSelect
              ref="pageDim"
              :dimInfo="pageDimInfo.slice(0, 3)"
              class="pageDimSelect firstPageDimSelect"
              @changeDimVal="changeDimVal"
            />
            <PageDimSelect
              v-if="!isShowBtn"
              ref="pageDim"
              :dimInfo="pageDimInfo.slice(3)"
              class="pageDimSelect"
              @changeDimVal="changeDimVal"
            />
          </div>
          <div v-if="isShowBtn" class="top-graph">
            <svg-icon
              type="icon-shuaxin"
              class="icon-btn"
              :title="$t_common('refresh')"
              @onClick="refreshEvent"
            />
            <yn-button type="text" @click="() => showGrah('FinalGragh')">
              {{ $t_structures("final_fshareholding_chart") }}
            </yn-button>
            <yn-button type="text" @click="() => showGrah('StructureGragh')">
              {{ $t_structures("corporate_structure_diagram") }}
            </yn-button>
          </div>
        </div>
      </template>
    </yn-page-title>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-page-title/";
import PageDimSelect from "@/components/hoc/pageDimSelect/index.vue";
import PAGE_EQUITY from "../../constant";
import commonService from "@/services/common";
// import DimSelect from "../../../ownershipManagement/DimSelect.vue";
const dimCodeMapObj = {
  Period: "monthVal",
  Version: "versionVal",
  Year: "yearVal",
  ICP: "holdingCompay"
};
export default {
  components: { PageDimSelect },
  inject: ["defaultParams"],
  props: {
    isShowFinalGragh: {
      type: Boolean,
      default: false
    },
    isShowStructureGragh: {
      type: Boolean,
      default: false
    },
    pageLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pageDimInfo: []
    };
  },
  computed: {
    pageName() {
      if (!this.isShowStructureGragh && !this.isShowFinalGragh) {
        return undefined;
      }
      return PAGE_EQUITY.PAGE_EQUITY_STRUCT;
    },
    title() {
      const titles = [
        this.$t_structures("final_fshareholding_chart"),
        this.$t_structures("corporate_structure_diagram"),
        this.$t_equity("equity_management")
      ];
      if (this.isShowFinalGragh) {
        return titles[0];
      } else if (this.isShowStructureGragh) {
        return titles[1];
      } else {
        return titles[2];
      }
    },
    isShowBtn() {
      const { isShowFinalGragh, isShowStructureGragh } = this;
      return !isShowFinalGragh && !isShowStructureGragh;
    }
  },
  async created() {
    await this.initPageDimInfo();
    this.$emit("update:pageLoading", false);
  },
  methods: {
    async initPageDimInfo() {
      const povObj = {};
      await this.selectUserPov(data => {
        if (!data) return;
        Object.keys(data).forEach(key => {
          povObj[key] = data[key].memberId;
        });
      });
      const pageDimInfo = Object.assign({}, povObj, this.defaultParams);
      const dimOrder = ["version", "year", "period", "icp"];
      const dimCodes = ["Version", "Year", "Period", "ICP"];
      const params = dimOrder.map((key, index) => {
        return {
          dimCode: dimCodes[index],
          dimMemberId: pageDimInfo[key] || "",
          needPermission: key === "icp"
        };
      });
      await this.setFinalDimMember(params);
    },
    async setFinalDimMember(params) {
      const dimCodes = ["Version", "Year", "Period", "ICP"];
      const { data } = await commonService("transPageDim", params);
      const mapObj = {};
      data.forEach(item => {
        const {
          dimCode,
          objectId: dimMemberId,
          dimName,
          dimMemberRealName: dimMemberName
        } = item;
        mapObj[dimCode] = {
          dimMemberId,
          dimCode,
          dimName,
          dimMemberName
        };
        this.$emit("changeDimVal", dimCodeMapObj[dimCode], dimMemberId);
      });
      this.pageDimInfo = dimCodes.map(key => {
        const tempObj = {};
        if (key === "ICP") {
          tempObj.pageName = "equityManagementCorporateStructure";
        }
        return {
          attr: {
            needPermission: key === "ICP",
            ...tempObj
          },
          ...mapObj[key]
        };
      });
    },
    getParams(cover = {}) {
      return this.pageDimInfo.map(item => {
        const {
          dimCode,
          dimMemberId,
          attr: { needPermission }
        } = item;
        return {
          dimCode,
          dimMemberId:
            cover[dimCode] === undefined ? dimMemberId : cover[dimCode],
          needPermission
        };
      });
    },
    async showGrah(type) {
      this.$emit("showGrah", true, type);
      this.$emit("update:pageLoading", true);
      await this.setFinalDimMember(this.getParams({ ICP: "" }));
      this.$emit("update:pageLoading", false);
    },
    async goBack() {
      this.setFinalDimMember(this.getParams());
      this.$emit("showGrah", false, "FinalGragh");
      this.$emit("showGrah", false, "StructureGragh");
    },
    changeDimVal(values = {}, valuesArr = []) {
      const valuesMap = valuesArr.reduce((pre, next) => {
        pre[next.dimCode] = next;
        return pre;
      }, {});
      Object.keys(values).forEach(key => {
        this.pageDimInfo.forEach(item => {
          if (item.dimCode === key) {
            item.dimMemberId = values[key];
            item.dimMemberName = valuesMap[key].dimMemberName;
          }
        });
        this.$emit("changeDimVal", dimCodeMapObj[key], values[key]);
      });
    },
    async refreshEvent() {
      // this.savePromptMixin().then(async () => {
      this.$emit("update:pageLoading", true);
      await Promise.all([
        this.setFinalDimMember(this.getParams()),
        this.$refs.pageDim.refresh()
      ]);
      this.$emit("refreshEvent");
      // });
    }
  }
};
</script>

<style scoped lang="less">
.equity-header {
  width: 100%;
  padding: 0.25rem 0;
  .titleDivider {
    margin: 0 0.5rem;
    height: 1rem;
  }
  /deep/.titleExtraRender {
    display: flex;
    justify-content: space-between;
    align-items: center;
    & > div {
      display: flex;
      align-items: center;
    }
  }
  .top-graph {
    /deep/.ant-btn-link {
      padding: 0;
    }
    .icon-btn {
      cursor: pointer;
      font-size: @rem16;
      color: @yn-label-color;
    }
  }
  .pageDimSelect {
    margin-left: @yn-margin-xl;
  }
  .firstPageDimSelect {
    margin-left: 0;
  }
}
</style>
