import DIM_INFO from "@/constant/dimMapping";
import lang from "@/mixin/lang";
const { $t_process, $t_common, $t_structures } = lang;
export default {
  PAGE_EQUITY_SHARE: "equityManagementHeldShareholder", // 股权管理被持股方
  PAGE_EQUITY_STAKE: "equityManagementShareholder", // 股权管理持股方
  PAGE_EQUITY_FINALL: "equityManagementFinalShareholder", // 股权管理最终持股
  PAGE_EQUITY_STRUCT: "equityManagementCorporateStructure", // 股权管理法人架构
  PAGE_ORGANIZATION_LIST: "organizationManagementList", // 组织架构列表页面
  ORGANIZATION_MANAGEMENT_VIEW: "organizationManagementView" // 组织架构图"
};
export const PERMISSION_NAME_MAPPING = {
  // 组织架构权限名称映射 权限 新增、编辑、查看、删除、复制、导入、导出
  add: "organization-add",
  edit: "organization-edit",
  read: "organization-read",
  delete: "organization-delete",
  copy: "organization-copyMonthFramework",
  import: "organization-import",
  export: "organization-export"
};
export const ADDNODETYPE = [
  "SAMELEVELGROUP",
  "CHILDLEVELGROUP",
  "CHILDORGANIZATION"
]; // 同级合并组，子集合并组，子集组织
export const DIM_INFO_ENTITY = () => ({
  dimName: $t_process("entity"),
  dimCode: "Entity",
  objectId: DIM_INFO.Entity,
  dimId: DIM_INFO.Entity,
  selectedItem: [],
  permissionFilter: true, // 权限过滤
  requestParams: {
    _scopeCode: "sys_default_edit"
  }
});
export const DIM_INFO_SCOPE = () => ({
  dimName: $t_process("scopes"),
  dimCode: "Scope",
  objectId: DIM_INFO.Scope,
  dimId: DIM_INFO.Scope,
  selectedItem: [],
  permissionFilter: true, // 权限过滤
  requestParams: {
    _scopeCode: "sys_default_edit"
  }
});
const TREE_MENU_ITEM_SHARE = () => [
  {
    menuName: $t_common("move_up"),
    eventName: "upMove",
    isRequestMoveNode: true,
    permission: PERMISSION_NAME_MAPPING.edit
  },
  {
    menuName: $t_common("move_down"),
    eventName: "downMove",
    isRequestMoveNode: true,
    permission: PERMISSION_NAME_MAPPING.edit
  },
  {
    menuName: $t_common("top"),
    eventName: "placement",
    isRequestMoveNode: true,
    permission: PERMISSION_NAME_MAPPING.edit
  },
  {
    menuName: $t_common("to_bottom"),
    eventName: "bottomSetting",
    isRequestMoveNode: true,
    permission: PERMISSION_NAME_MAPPING.edit
  }
];
export const TREE_MENU_ITEM_DELETE_SHARE = () => [
  "splitLine",
  {
    menuName: $t_common("delete"),
    eventName: "deleteNode",
    permission: PERMISSION_NAME_MAPPING.delete
  }
];
export const TREE_MENU_ITEM_ROOT = () => [
  {
    menuName: $t_structures("add_scope"),
    eventName: "addSubsetGroup",
    permission: PERMISSION_NAME_MAPPING.add
  },
  {
    menuName: $t_structures("add_entity"),
    eventName: "addOrganization",
    permission: PERMISSION_NAME_MAPPING.add
  },
  "splitLine",
  ...TREE_MENU_ITEM_SHARE()
];
export const TREE_MENU_ITEM_PARENT = () => [
  {
    menuName: $t_structures("add_scope_same_level"),
    eventName: "addMergeGroup",
    permission: PERMISSION_NAME_MAPPING.add
  },
  {
    menuName: $t_structures("add_scope_sub_group"),
    eventName: "addSubsetGroup",
    permission: PERMISSION_NAME_MAPPING.add
  },
  "splitLine",
  {
    menuName: $t_structures("add_entity"),
    eventName: "addOrganization",
    permission: PERMISSION_NAME_MAPPING.add
  },
  "splitLine",
  ...TREE_MENU_ITEM_SHARE(),
  ...TREE_MENU_ITEM_DELETE_SHARE()
];
export const TREE_MENU_ITEM_LEAF = () => [
  ...TREE_MENU_ITEM_SHARE(),
  ...TREE_MENU_ITEM_DELETE_SHARE()
];
// 控股母公司比例、集团内少数股东比例
export const TABLE_COLUMNS = () => [
  {
    title: $t_structures("holding_ratio_of_controlling_parent"),
    dataIndex: "parentCompanyHolding",
    width: 150,
    align: "right",
    cellType: "text"
  },
  {
    title: $t_structures("calculated_minority_shareholder_ratio_in_group"),
    dataIndex: "minorityShareHolding",
    width: 250,
    align: "right",
    cellType: "text"
  }
];
