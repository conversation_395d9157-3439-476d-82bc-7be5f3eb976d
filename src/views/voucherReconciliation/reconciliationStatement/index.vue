<template>
  <reconciliation-statement
    v-bind="$attrs"
    :params="params"
    moduleName="凭证对账报告"
    :reconciliationModuleName="reconciliationModuleName"
  >
    <template #reconciliationLeft>
      <layout-left :params="params" />
    </template>
    <template #reconciliationRight>
      <layout-right :params="params" />
    </template>
    <template #configContent="{ drawerVisible, operationType, closeDrawer }">
      <reconcoilation-config
        v-bind="{ drawerVisible, operationType }"
        @closeDrawer="closeDrawer"
      />
    </template>
  </reconciliation-statement>
</template>
<script>
import ReconciliationStatement from "@/views/reconciliation/reconciliationStatement";
import { VOUCHER_RECONCILIATION } from "@/constant/reconciliation.js";
import LayoutLeft from "./left";
import LayoutRight from "./right";
import ReconcoilationConfig from "./config";
export default {
  name: "VoucherReconciliationStatement",
  components: {
    ReconciliationStatement,
    LayoutLeft,
    LayoutRight,
    ReconcoilationConfig
  },
  props: {
    params: {
      type: Object,
      require: false,
      default: () => {}
    }
  },
  created() {
    this.reconciliationModuleName = VOUCHER_RECONCILIATION;
  }
};
</script>
