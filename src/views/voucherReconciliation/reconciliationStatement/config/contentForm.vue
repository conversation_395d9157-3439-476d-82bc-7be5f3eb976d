<template>
  <div ref="config" class="config-form">
    <p class="config-title">凭证自动对账规则</p>
    <yn-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <yn-form-item label="规则">
        <yn-select
          v-decorator="[
            'ruleId',
            {
              initialValue: configObj.rules,
              rules: [
                {
                  required: true,
                  message: `请选择`
                }
              ]
            }
          ]"
          showSearch
          :filterOption="filterOption"
          :options="rules"
        />
      </yn-form-item>
    </yn-form>
    <p class="config-title">对账科目设置</p>
    <yn-table
      class="configTable"
      :data-source="tableData"
      :columns="columns"
      bordered
      :scroll="{ x: 582 }"
    >
      <template slot="footer">
        <div class="tableFooter" @click="addRow">
          <span :class="{ disabledBtn: disabledBtn, addBtn: true }">
            <span>+</span>添加
          </span>
        </div>
      </template>
      <span slot="operation" slot-scope="text, record, index" class="fullCell">
        <a
          :class="[tableData.length === 1 ? 'disabledBtn' : '']"
          @click="deleteRow(index)"
        >
          删除
        </a>
      </span>
      <span slot="ident" slot-scope="text, record, index" class="fullCell">{{
        index + 1
      }}</span>
      <div
        slot="account"
        slot-scope="text, record, index"
        class="seletTreeCont valid-prop "
      >
        <yn-select-tree
          v-model="record.account"
          forceRender
          size="large"
          class="dimSelectTree"
          :class="[
            !record.account && checkRequired ? 'valid-error-border' : ''
          ]"
          searchMode="multiple"
          placeholder="请选择"
          :nonleafselectable="true"
          :datasource="account"
          :allowClear="false"
          @change="onChange($event, 'account', index)"
        />
        <!-- errorTips -->
        <span v-if="!record.account && checkRequired" class="invalid-tip-text">
          请选择
        </span>
      </div>
      <div
        slot="otherAccount"
        slot-scope="text, record, index"
        class="seletTreeCont valid-prop "
      >
        <yn-select-tree
          v-model="record.otherAccount"
          forceRender
          size="large"
          class="dimSelectTree"
          :class="[
            !record.otherAccount && checkRequired ? 'valid-error-border' : ''
          ]"
          searchMode="multiple"
          placeholder="请选择"
          :nonleafselectable="true"
          :datasource="otherAccount"
          :allowClear="false"
          @change="onChange($event, 'otherAccount', index)"
        />

        <span
          v-if="!record.otherAccount && checkRequired"
          class="invalid-tip-text"
        >
          请选择
        </span>
      </div>
    </yn-table>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-table/";
import voucherReconciliationService from "@/services/voucherReconciliation";
export default {
  name: "ContentForm",
  props: {
    configObj: {
      type: Object,
      default: () => {}
    },
    reportId: {
      type: String,
      default: ""
    },
    visible: {
      type: Boolean,
      default: false
    },
    spinning: {
      type: Boolean,
      default: false
    },
    selectedNode: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: this.$form.createForm(this, {
        name: "config"
      }),
      labelCol: {
        span: 2,
        sm: 3
      },
      wrapperCol: {
        span: 22,
        sm: 21
      },
      rules: [],
      account: [],
      otherAccount: [],
      disabledBtn: false,
      tableData: [],
      columns: [
        {
          dataIndex: "ident",
          title: "序号",
          key: "ident",
          width: 80,
          scopedSlots: {
            customRender: "ident"
          }
        },
        {
          dataIndex: "account",
          key: "account",
          title: "我方科目",
          scopedSlots: {
            customRender: "account"
          }
        },
        {
          dataIndex: "otherAccount",
          key: "otherAccount",
          title: "对方科目",
          scopedSlots: {
            customRender: "otherAccount"
          }
        },
        {
          title: "操作",
          dataIndex: "operation",
          key: "operation",
          width: 80,
          scopedSlots: {
            customRender: "operation"
          }
        }
      ],
      checkRequired: false
    };
  },
  watch: {
    visible: {
      handler(value) {
        value && this.init();
      },
      immediate: true
    }
  },
  methods: {
    async init() {
      this.reset();
      this.$emit("update:spinning", true);
      await Promise.all([this.getTreeData(), this.getRules()]);
      this.echoData();
      this.$emit("update:spinning", false);
    },
    reset() {
      this.tableData = [];
      this.form.setFieldsValue({
        ruleId: ""
      });
      this.disabledBtn = false;
      this.checkRequired = false;
      this.objectId = "";
    },
    echoData() {
      const { id } = this.selectedNode;
      const currConfig = this.configObj[id];
      if (!currConfig || !currConfig.ruleId) {
        // 没有配置信息，则表格回显一条空数据
        this.addRow(false);
        return;
      }
      this.tableData = [...currConfig.account];
      this.checkRowNoEmptyCell();
      this.form.setFieldsValue({
        ruleId: currConfig.ruleId
      });
    },
    async getRules() {
      const {
        data: { data }
      } = await voucherReconciliationService("ruleList");
      this.rules = (data || []).map(item => {
        return {
          key: item.objectId,
          title: item.ruleName
        };
      });
    },
    async getTreeData() {
      const { data } = await voucherReconciliationService("getAccount");
      this.setTreeData(data);
    },
    setTreeData(data) {
      const count = this.formatTreeData(data);
      this.$set(this, "account", count);
      this.$set(this, "otherAccount", count);
    },
    formatTreeData(treeData) {
      const loop = data => {
        data.forEach(item => {
          const { children, id: key, name: label } = item;
          item.key = key;
          item.label = label;
          if (children && children.length) {
            loop(children);
          }
        });
      };
      loop(treeData);
      return treeData;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    deleteRow(index) {
      if (this.tableData.length === 1) return;
      this.tableData.splice(index, 1);
      this.checkRowNoEmptyCell();
    },
    addRow(check = true) {
      if (!this.checkRowNoEmptyCell()) return;
      this.tableData.push({
        account: "",
        otherAccount: ""
      });
      this.checkRowNoEmptyCell(check);
    },
    onChange(dimMemberId, propName, rowIndex) {
      const currRow = this.tableData.slice(rowIndex, rowIndex + 1)[0];
      currRow[propName] = dimMemberId;
      this.tableData = [...this.tableData];
      this.checkRowNoEmptyCell();
    },
    checkRowNoEmptyCell(check = true) {
      // 没有数据
      if (this.tableData.length === 0) return true;
      check && (this.checkRequired = true);
      this.disabledBtn = this.tableData.some(item => {
        return Object.values(item).some(value => !value);
      });
      return !this.disabledBtn;
    },
    getSaveParams() {
      // 获取保存参数
      const formVal = this.form.getFieldsValue();
      return {
        ...formVal,
        account: this.tableData,
        objectId: this.reportId
      };
    },
    // 校验必填项
    async validate() {
      return new Promise((resolve, reject) => {
        this.form.validateFields((err, value) => {
          const status = this.checkRowNoEmptyCell();
          if (err || !status) {
            reject(false);
          }
          resolve(true);
        });
      });
    }
  }
};
</script>
<style lang="less" scoped>
@import "../../../../commonLess/common.less";
.config-form {
  height: 100%;
  overflow: scroll;
  margin-right: -10px;
}
.config-title {
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.375rem;
  letter-spacing: 0px;
  color: @yn-text-color;
}
.configTable {
  /deep/ th[key="account"],
  /deep/ th[key="otherAccount"] {
    &::after {
      content: "*";
      color: #f54645;
      font-size: 0.875rem;
    }
  }
  /deep/.ant-table-row td {
    .yn-select-tree {
      border: 0;
    }
    padding: 0;
  }
  /deep/td.ant-table-row-cell-break-word {
    padding: 0.4375rem 0.75rem;
  }
  /deep/.tableFooter {
    height: @rem36;
    line-height: @rem36;
    background: @yn-background-color;
    border: 1px dashed @yn-border-color-base;
    color: @yn-primary-color;
    text-align: center;
    .addBtn {
      cursor: pointer;
    }
    .addBtn > span {
      margin-right: @yn-margin-s;
    }
  }
  .disabledBtn {
    cursor: not-allowed !important;
    color: #bcc1cc;
  }
}
</style>
