<template>
  <yn-drawer
    title="对账配置"
    placement="right"
    :width="632"
    :visible="drawerVisible"
    :maskClosable="false"
    @close="onClose"
  >
    <yn-spin :spinning="spinning">
      <content-form
        ref="form"
        :reportId="reportId"
        :selectedNode="selectedNode"
        :configObj="reconciliationConfigObj"
        :visible="drawerVisible"
        :spinning.sync="spinning"
      />
    </yn-spin>
    <div class="footer">
      <yn-button class="closeDrawer" @click="onClose"> 取消 </yn-button>
      <!-- <yn-button
        v-if="!creatorFlag"
        type="primary"
        :loading="loading"
        @click="saveByType('saveAs')"
      >
        另存为
      </yn-button> -->
      <yn-button type="primary" :loading="loading" @click="saveByType('save')">
        保存
      </yn-button>
    </div>
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-spin/";
import { EventBus } from "yn-p1/libs/utils/ComponentUtils";
import voucherReconciliationService from "@/services/voucherReconciliation";
import reconciliationCommon from "@/views/reconciliation/minix";
import { mapState } from "vuex";
import cloneDeep from "lodash/cloneDeep";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import contentForm from "./contentForm";

export default {
  name: "ReconcoilationConfig",
  components: {
    contentForm
  },
  mixins: [reconciliationCommon],
  props: {
    drawerVisible: Boolean,
    operationType: {
      type: String,
      default() {
        return "add"; // 打开的方式 edit or add
      }
    },
    isReportPage: Boolean,
    selectTreeNodeInfo: {
      type: Object,
      default: () => {}
    }
  },

  inject: ["showSaveAsModal", "reconciliationModuleName"],
  data() {
    return {
      spinning: true,
      creatorFlag: true, // true 当前用户创建的对账报告，flase 不是
      loading: false,
      isChecked: false, //  是否校验
      reconciliationConfigObj: {},
      selectTreeNode: {}
    };
  },
  computed: {
    ...mapState({
      currSelectTreeNode(state) {
        const namespace = this.getNamespace();
        return state[namespace].selectTreeNode;
      },
      configObj(state) {
        const namespace = this.getNamespace();
        return state[namespace].reconciliationConfigObj;
      },
      hasAuthority(state) {
        const namespace = this.getNamespace();
        return state[namespace].hasAuthority;
      },
      selectedNode() {
        return this.getSelectTreeNode();
      }
    }),
    reportId() {
      const { id } = this.getSelectTreeNode();
      return id;
    }
  },
  watch: {
    operationType: {
      handler(newVal) {
        if (newVal === "edit") {
          const { attr } = this.getSelectTreeNode() || {};
          this.creatorFlag = attr ? attr.creatorFlag : true;
        } else {
          this.creatorFlag = true;
        }
      },
      immediate: true
    },
    drawerVisible: {
      async handler(newVal) {
        if (!newVal) return;
        if (this.operationType === "add") {
        } else {
          this.setReconciliationConfigObj();
          const { attr } = this.getSelectTreeNode() || {};
          this.creatorFlag = attr ? attr.creatorFlag : true;
        }
      },
      immediate: true
    }
  },
  methods: {
    setReconciliationConfigObj() {
      // 新版页签并且是报告页面从session里面获取对账配置信息
      if (!this.selfTab && this.isReportPage) {
        const { reconciliationConfigObj } = this.getTabParamsMixin();
        this.$set(this, "reconciliationConfigObj", reconciliationConfigObj);
      } else {
        this.$set(this, "reconciliationConfigObj", this.configObj);
      }
    },
    getSelectTreeNode() {
      return this.isReportPage
        ? this.selectTreeNodeInfo
        : this.currSelectTreeNode;
    },
    onClose() {
      this.$emit("closeDrawer");
    },
    // 获取对账配置数据
    async saveByType(type) {
      this.loading = true;
      this.$refs.form
        .validate()
        .then(async () => {
          const params = this.$refs.form.getSaveParams();
          this[`${type}Event`] && (await this[`${type}Event`](params));
        })
        .finally(() => {
          this.loading = false;
        });
    },
    saveAsEvent(saveParams) {
      // TODO 另存为未做
      if (!this.isReportPage) {
        EventBus.trigger("openReportFormModalByType", {
          type: "saveAs",
          reconciliationConfig: saveParams
        });
      } else {
        this.showSaveAsModal && this.showSaveAsModal(cloneDeep(saveParams));
      }
    },
    async saveEvent(saveParams) {
      await voucherReconciliationService(
        "saveReconciliationConfig",
        saveParams
      ).then(res => {
        // 重新选择 树节点
        const treeNodeInfo = cloneDeep(this.getSelectTreeNode());
        this.$store.commit(
          this.getMutationOrActionType("setSelectTreeNode"),
          treeNodeInfo
        );
        const { id } = treeNodeInfo;
        this.$store.commit(this.getMutationOrActionType("setConfig"), {
          id,
          config: saveParams
        });
        if (!this.selfTab) {
          const { tabId, ...otherObj } = this.getTabParamsMixin() || {};
          tabId &&
            this.setTabParamsMixin(tabId, {
              ...otherObj,
              tabId,
              reconciliationConfigObj: this.configObj
            });
        }
        UiUtils.successMessage("保存成功！");
        if (this.isReportPage) {
          this.$emit("saveConfigCb", saveParams);
        } else {
          this.onClose();
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.config-cont {
  width: 100%;
  height: calc(100% - 2.75rem);
  overflow: scroll;
  padding-left: @yn-padding-xxl;
  padding-right: @yn-padding-xxl;
  padding-top: @rem24;
}
/deep/.ant-drawer-wrapper-body {
  overflow: hidden;
}
/deep/.ant-drawer-body {
  padding-bottom: 58px;
  height: calc(100% - 40px);
}
.footer {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid @yn-border-color-base;
  padding: 10px @yn-padding-xl;
  background: @yn-body-background;
  text-align: right;
  z-index: 1;
  .closeDrawer {
    margin-right: @yn-margin-s;
  }
}
</style>
