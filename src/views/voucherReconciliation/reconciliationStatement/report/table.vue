<template>
  <div class="reportTableCont">
    <div class="reportTable">
      <Grid
        v-if="showTable"
        ref="reportGrid"
        rowKey="objectId"
        selectTitle="序号"
        :ident="$data.$treeGridIdent"
        :fixedColumns="{ right: 0, left: 3 }"
        :columns="columns"
        :hasMore="hasMore"
        :overSystem="true"
        :sourceMap="mapTableData"
        :dataSource="showTableData"
        :paginationInfo="pagination"
        :expandedRowKeys="expandedRowKeys"
        :cellNode="renderBodyCell"
        :cellClass="bodyCellClassName"
        :cellType="cellType"
        :isAsyncLoad="true"
        :autoSelectChild="true"
        :hasSelectAllBox="false"
        :loadMore="loadMore"
        :loadData="loadData"
        :filterOrder="filterOrder"
        :cellMouseUp="selectThead"
        :requestFilterPopData="requestFilterPopData"
        :formatFilterWord="formatFilterWord"
        :showDimMemberName="showDimMemberName"
        :scrollTable="scrollTable"
        :option="option"
        v-bind="$attrs"
        v-on="$listeners"
        @reloadData="closeAll"
        @expandedRowsChange="expandedRowsChange"
      />
    </div>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-row/";
import "yn-p1/libs/components/yn-col/";
import voucherReconciliationService from "@/services/voucherReconciliation";
import cloneDeep from "lodash/cloneDeep";
import { mapState } from "vuex";
import Grid from "@/components/hoc/grid";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import { PAGE_SIZE } from "@/views/reconciliation/reconciliationStatement/reconciliationReport/constant.js";
import { genDOM } from "@/views/process/control/jdom/utils";
import drillMenuMixin from "@/views/reconciliation/reconciliationStatement/reconciliationReport/mixin.js";
// 科目的父项成员信息 固定值
const SUBJECT_PARENT_NODE = ["合计", "小计"];
const SUBJECT_PROPR_NAME = "accountName";
const FIXED_DISPLAY_COLUMN = ["entityNameTxt", "icpNameTxt", "accountName"]; // 固定显示列 组织 往来公司 科目
const AMOUNT_IDENT = "amount"; // 金额列标识
const MAPPING_DIM_NAME = {}; // 映射维度名称
const isAmount = key => {
  const reg = new RegExp(`${AMOUNT_IDENT}`, "ig");
  return reg.test(key) || key === "atr12"; // 行号
};
const IDENT = "voucherReconciliationReport";
const ROOT_ID = "-1";
const genDomItem = string => {
  if (!string) return "";
  const [key, value] = string.split(":");
  return `<span class='m-wrap' title='${string}'><span class='m-title'>${key}</span>:<span  class='m-value'>${value}</span></span>`;
};
export default {
  name: "VoucherReconciliationTable",
  components: { Grid },
  mixins: [drillMenuMixin],
  props: {
    tableInfo: {
      type: Object,
      default() {
        return {
          items: [], // 表格数据
          columnInfo: []
        };
      }
    },
    cellType: {
      type: String,
      default: "nameAndCode" // 单元格显示的类型 name or code or nameAndCode
    },
    isExpandAll: {
      type: Boolean
    },
    isSelect: {
      type: Boolean,
      default: false
    },
    frozenColName: {
      type: String,
      default: ""
    },
    frozenLeftIndex: {
      type: Number,
      default: 3
    },
    reconciliationConfig: {
      type: Object,
      default: () => {}
    },
    matchFlag: {
      type: String,
      default: ""
    },
    refreshId: {
      type: String,
      default: ""
    },
    reportId: {
      type: String,
      default: ""
    },
    isManuallyClick: {
      type: Boolean,
      default: true
    },
    titleMap: {
      type: Object,
      default: () => {}
    },
    titleInfo: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showTable: false, // 刷新操作会使用，刷新使用此属性控制grid组件卸载挂载
      $treeGridIdent: IDENT, // @modify treeGrid 唯一标识
      pagination: {
        total: 0,
        current: 1,
        pageSize: PAGE_SIZE,
        hasMore: false,
        offset: 0
      },
      option: {
        mergeCells: []
      },
      searchBody: [],
      originFormateColumns: [], // 处理后的 columns 表头
      originalTableColumns: [], // 原始接口返回 columns 数据
      columns: [],
      searchObj: [], // 查询维度显示列表
      originalTableData: [], // 原始表格数据
      showTableData: [],
      fixedCell: FIXED_DISPLAY_COLUMN,
      filterSelectObj: {}, // 过滤下拉列表对象
      filterCheckedObj: {}, // 过滤下拉列表已选对象
      expandedRowKeys: [], // 展开的key集合
      filterOrder: [], // 过滤的顺序
      frozenLeft: 3, // 左边冻结
      selectedColInfo: [], // 冻结选中表头信息
      isCtrl: false,
      selectRowDimInfo: {},
      hasMore: false,
      cacheHasMore: false,
      mapTableData: {},
      searchInfo: [], // 过滤条件
      drillMenuData: []
    };
  },
  computed: {
    ...mapState("common", {
      menuInfo: state => state.menuList
    }),
    refreshIdOrTaskId() {
      return this.refreshId || this.reportId;
    }
  },
  watch: {
    tableInfo: {
      handler(newVal) {
        this.showTable = false;
        const { items: tableData = [], columnInfo = [], hasMore } = newVal;
        this.originalTableColumns = cloneDeep(columnInfo);
        this.originalTableData = cloneDeep(tableData);
        this.hasMore = hasMore;
        this.cacheHasMore = hasMore;
        this.$set(this, "expandedRowKeys", []);
        this.$nextTick(() => {
          this.showTable = true;
          this.initData(columnInfo, tableData);
        });
      },
      immediate: true,
      deep: true
    },
    isSelect: {
      handler() {
        this.searchBody = [];
      },
      immediate: true
    },
    isExpandAll: {
      async handler(newVal, oldVal) {
        if (!this.isManuallyClick) {
          this.$emit("update:isManuallyClick", true);
          return;
        }
        this.$emit("update:spinning", true);
        this.$refs.reportGrid.clearAll();
        // 全部展开
        this.$set(this, "mapTableData", {});
        let res = {};
        if (newVal) {
          res = await this.getExpandAllDataByOffset(0);
          const { data, hasMore } = res;
          this.hasMore = hasMore;
          this.updateTableData(data);
          return;
        } else if (!newVal && oldVal) {
          // 全部 闭合
          this.closeAll();
        }
      }
    },
    cellType(newVal, oldVal) {
      if (newVal === oldVal) return;
      this.$set(this, "expandedRowKeys", []);
      this.initData(
        [...this.originalTableColumns],
        [...this.originalTableData]
      );
      this.hasMore = this.cacheHasMore;
      this.$nextTick(() => {
        this.updateTable();
        this.$refs.reportGrid.$refs.simpleGrid.loading = false;
      });
    },
    selectedColInfo(newVal) {
      const mapObj = {};
      let frozenLeft = 0;
      let frozenColName = "";
      this.columns.forEach((item, index) => {
        const { title } = item;
        mapObj[title] = mapObj[title] !== undefined ? mapObj[title] : index;
      });
      newVal.forEach(colName => {
        const currIndex = mapObj[colName];
        if (currIndex >= frozenLeft) {
          frozenLeft = currIndex;
          frozenColName = colName;
        }
      });
      this.$emit("update:frozenColName", frozenColName);
      this.frozenLeft = frozenLeft + 1;
    }
  },
  mounted() {
    document.body.addEventListener("keydown", this.keyDownEvent);
    document.body.addEventListener("keyup", this.keyUpEvent);
    document.body.addEventListener("click", this.bodyClick);
  },
  destroyed() {
    document.body.removeEventListener("keydown", this.keyDownEvent);
    document.body.removeEventListener("keyup", this.keyUpEvent);
    document.body.removeEventListener("click", this.keyUpEvent);
  },
  methods: {
    initData(tableColumns, tableData) {
      // 初始化数据时，需要清除过滤条件
      this.filterSelectObj = {};
      this.filterCheckedObj = {};
      this.setFilterObj(tableColumns);
      this.$set(this, "mapTableData", {});
      this.initColumns(tableColumns);
      this.showTableData = this.formatTableData(tableData);
    },
    // 设置过滤对象的key （除了金列跟差额列）
    setFilterObj(columns) {
      const filterSelectObj = {};
      const filterCheckedObj = {};
      const filterOrder = [];
      columns.forEach(item => {
        const { key, title } = item;
        if (!isAmount(key)) {
          filterSelectObj[key] = [];
          filterCheckedObj[key] = [];
          MAPPING_DIM_NAME[key] = title;
          filterOrder.push(key);
        }
      });
      this.$set(this, "filterSelectObj", filterSelectObj);
      this.$set(this, "filterCheckedObj", filterCheckedObj);
      this.$set(this, "filterOrder", filterOrder);
    },
    formatTableData(tableData, isSearch) {
      const list = [];
      const data = cloneDeep(tableData);
      const mapData = {};
      // 深度优先遍历非递归方式
      const nodes = [...data];
      if (nodes.length) {
        while (nodes.length) {
          const item = nodes.shift();
          const { level, children, attributes, id } = item;
          this.setPropsToObj(attributes, item);
          this.setFilterSelectedData(item);
          if (typeof level === "undefined") {
            item.level = 0;
          }
          mapData[id] = item;
          if (children) {
            item.hasChildren = true;
            for (let i = 0, LEN = children.length; i < LEN; i++) {
              children[i].level = item.level + 1;
              nodes.unshift(children[i]);
            }
          } else {
            delete item.children;
          }
          if (isSearch) {
            // item.hasChildren = false;
          }
          list.push(item);
        }
      }
      this.$set(
        this,
        "mapTableData",
        Object.assign(this.mapTableData, mapData)
      );
      return data;
    },
    setFilterSelectedData(rowData) {
      const filterColumnNames = Object.keys(this.filterSelectObj);
      Object.keys(rowData).map(item => {
        if (filterColumnNames.indexOf(item) !== -1) {
          const selectItems = this.filterSelectObj[item];
          const currVal = rowData[item].v;
          if (currVal && selectItems.indexOf(currVal) === -1) {
            this.filterSelectObj[item].push(currVal);
          }
        }
      });
    },
    setPropsToObj(attributes, obj) {
      // const val = obj.indexes;
      const pId = obj.parentId;
      const { id: rowKey } = obj;
      Object.keys(attributes || {}).forEach(propName => {
        obj[propName] = this.getShowName({ ...obj, ...attributes }, propName);
      });
      obj._select = obj.indexes; // 选择框后数据
      obj.objectId = rowKey;
      // parentId 转换  单体公司对账 attributes 里面没有parentId
      obj.parentId = pId;
    },
    // 获取显示名称
    getShowName(rowData, cellName) {
      const tempVal = rowData[cellName]; // 临时值，可能是字符串，也可能是对象
      if (typeof tempVal === "string") {
        return tempVal;
      }
      const cellData = tempVal || {};
      const { name, code } = cellData;
      const lastCode = rowData.accountName.code;
      // 科目列 合计 小计 只显示name
      if (
        cellName === SUBJECT_PROPR_NAME &&
        SUBJECT_PARENT_NODE.indexOf(lastCode) !== -1
      ) {
        return name;
      }
      if (this.cellType === "code") {
        return code || name;
      } else if (this.cellType === "nameAndCode") {
        return code ? `${code}-${name}` : name;
      }
      return name;
    },
    //  初始化 columns
    initColumns(tableColumns) {
      this.setNoExpandedColumns(tableColumns);
      this.setFirstCol();
    },
    // 设置 第一个column 数据
    setFirstCol() {
      if (!this.isSelect) {
        this.columns = [
          {
            dataIndex: "indexes",
            width: 100,
            cellType: "text",
            title: "序号"
          },
          ...this.originFormateColumns
        ];
      } else {
        this.columns = [...this.originFormateColumns];
      }
    },
    // 设置表头数据
    setNoExpandedColumns(tableColumns) {
      const columns = [];
      const data = cloneDeep(tableColumns);
      data.forEach(item => {
        const { key, title } = item;
        // 审计线索 最后一个父项列
        const tempColumn = {
          dataIndex: key,
          readOnly: true,
          cellType: "text",
          align: isAmount(key) ? "right" : "",
          title
        };
        // 添加筛选slot
        this.addFilterSlot(tempColumn);
        columns.push(tempColumn);
      });
      this.originFormateColumns = cloneDeep(columns);
      this.$set(this, "columns", columns);
    },
    // 添加刷选slot
    addFilterSlot(columnObj) {
      const { dataIndex } = columnObj;
      // 判断当前key 是否包含Amount 文案，,不包含 并且不是差额列 则认定不是金额列
      if (dataIndex && !isAmount(dataIndex)) {
        columnObj.isFilter = true;
      }
    },
    async closeAll() {
      this.$set(this, "expandedRowKeys", []);
      const res = await this.requestDataByOffset({
        parentId: ROOT_ID,
        offset: 0
      });
      const { data, hasMore } = res;
      this.hasMore = hasMore;
      this.updateTableData(data);
    },
    loadData(rowData) {
      const { objectId } = rowData;
      return this.requestDataByOffset({
        offset: 0,
        parentId: objectId
      });
    },
    loadMore(params) {
      const { searchObj, record, isFirstPage } = params;
      this.searchBody = searchObj.map(item => {
        const { type, searchArr } = item;
        return {
          dimCode: type,
          dimMemberNames: searchArr.map(obj => obj.name)
        };
      });
      // 搜索 加载更多 都会走这里，record 当子项加载更多时，有值
      // 搜索条件改变
      if (isFirstPage) {
        this.$set(this, "mapTableData", {});
        this.$emit("update:spinning", true);
        this.$set(this, "expandedRowKeys", []);
        this.hasMore = this.cacheHasMore;
      }
      const rootNodeNum = this.getRootNodeNum();
      if (record) {
        return this.loadMoreChildrenNode(record);
      } else if (searchObj.length) {
        return this.loadMoreSearchNode(isFirstPage);
      } else if (this.isExpandAll) {
        return this.loadMoreExpandAllNode(isFirstPage ? 0 : rootNodeNum);
      } else {
        return this.loadMoreRootNode(isFirstPage ? 0 : rootNodeNum);
      }
    },
    loadMoreExpandAllNode(offset) {
      return this.getExpandAllDataByOffset(offset);
    },
    // 根据offset获取全部展开数据
    async getExpandAllDataByOffset(offset) {
      return new Promise(resolve => {
        voucherReconciliationService("expandAll", {
          reportId: this.refreshIdOrTaskId,
          offset,
          limit: PAGE_SIZE
        })
          .then(res => {
            const { data, hasMore, keys } = res.data.data;
            this.$set(this, "expandedRowKeys", keys);
            const tableData = this.formatTableData(data);
            this.setMapTableData(tableData);
            resolve({
              data: tableData,
              hasMore
            });
          })
          .catch(() => {
            resolve({
              data: [],
              hasMore: false
            });
          })
          .finally(() => {
            this.$emit("update:spinning", false);
          });
      });
    },
    loadMoreSearchNode(isFirstPage) {
      return this.getSearchData(
        !isFirstPage ? Object.values(this.mapTableData).length : 0
      );
    },
    // 设置查询数据
    getSearchData(offset = 0) {
      const requestParams = {
        reportId: this.refreshIdOrTaskId,
        matchFlag: this.matchFlag,
        searchBody: this.searchBody,
        limit: PAGE_SIZE,
        offset
      };
      return new Promise(res => {
        voucherReconciliationService("searchReport", requestParams)
          .then(result => {
            const { data } = result.data;
            const { hasMore, items } = data;
            this.hasMore = hasMore;
            res({
              hasMore,
              data: this.formatTableData(items, true)
            });
          })
          .catch(() => {
            res({
              hasMore: false,
              data: []
            });
          })
          .finally(() => {
            this.$refs.reportGrid.$refs.simpleGrid.loading = false;
            this.$emit("update:spinning", false);
          });
      });
    },
    loadMoreChildrenNode(record) {
      const { children = [], objectId } = record;
      const offset = children.length - 1;
      return this.requestDataByOffset({
        parentId: objectId,
        offset
      });
    },
    loadMoreRootNode(offset) {
      if (!this.isSelect) {
        return this.requestDataByOffset({
          offset: offset,
          parentId: "-1"
        });
      } else {
        return this.nodeIdListByMatchFlag({
          offset: offset
        });
      }
    },
    getRootNodeNum() {
      return Object.values(this.mapTableData).filter(item => {
        if (typeof item.parentId === "object") {
          return item.parentId.v === "-1";
        } else {
          return item.parentId === "-1";
        }
      }).length;
    },
    async nodeIdListByMatchFlag(params) {
      const { limit = PAGE_SIZE, offset } = params;
      return new Promise(resolve => {
        voucherReconciliationService("nodeListByMatchFlag", {
          limit,
          matchFlag: this.matchFlag,
          offset,
          reportId: this.refreshId || this.reportId
        })
          .then(res => {
            const { hasMore, items } = res.data.data;
            const data = this.formatTableData(items);
            resolve({
              hasMore,
              data
            });
          })
          .catch(() => {
            resolve({
              hasMore: false,
              data: []
            });
          })
          .finally(() => {
            this.$emit("update:spinning", false);
          });
      });
    },
    async requestDataByOffset(params) {
      const { limit = PAGE_SIZE, offset, parentId } = params;
      return new Promise(res => {
        voucherReconciliationService("expandChildren", {
          reportId: this.refreshIdOrTaskId,
          searchBody: this.searchBody,
          matchFlag: this.isSelect ? this.matchFlag : "",
          limit,
          parentId,
          offset
        })
          .then(result => {
            const { hasMore, items } = result.data.data;
            const data = this.formatTableData(items);
            if (parentId === "-1") {
              this.hasMore = hasMore;
            }
            res({
              hasMore,
              data
            });
          })
          .catch(() => {
            res({
              hasMore: false,
              data: []
            });
          })
          .finally(() => {
            this.$emit("update:spinning", false);
          });
      });
    },
    expandedRowsChange(keys) {
      this.expandedRowKeys = keys;
    },
    // 获取过滤条件列表
    async requestFilterPopData(dimCode) {
      return new Promise(res => {
        voucherReconciliationService("columnSearchList", {
          reportId: this.refreshIdOrTaskId,
          matchFlag: this.isSelect ? this.matchFlag : "",
          columnName: dimCode
        })
          .then(result => {
            const { data } = result.data;
            res(data);
          })
          .catch(() => {
            res([]);
          });
      });
    },
    formatFilterWord(colName, item, cellType) {
      // const {name,id} = item;
      const WORD_MAPPING = { code: "code", name: "name" };
      const PROPS_ARR = ["code", "name"];
      const { name, code } = item;
      if (PROPS_ARR.indexOf(this.cellType) !== -1) {
        return item[WORD_MAPPING[cellType]] || item.name;
      } else {
        // 科目列，小计、合计 只显示小计、合计
        if (
          colName === SUBJECT_PROPR_NAME &&
          SUBJECT_PARENT_NODE.indexOf(name) !== -1
        ) {
          return code || name;
        } else {
          return code ? `${code}-${name}` : name;
        }
      }
    },
    keyDownEvent(e) {
      const { keyCode } = e;
      if (keyCode === 17) {
        this.isCtrl = true;
      }
    },
    keyUpEvent(e) {
      this.isCtrl = false;
    },
    selectThead(event, cell, areaByRowCol) {
      if (areaByRowCol !== "headCol") return;
      let colInfo = [];
      if (this.isCtrl) {
        colInfo = colInfo.concat(this.selectedColInfo);
      }
      const { path } = event;
      const doms = path || event.composedPath();
      for (let i = 0, LEN = doms.length; i < LEN; i++) {
        const currDom = doms[i];
        if (currDom.className && currDom.className.indexOf("htCenter") !== -1) {
          const title = currDom.getAttribute("title");
          colInfo.push(title);
          break;
        }
      }
      this.$set(this, "selectedColInfo", colInfo);
    },
    bodyCellClassName(cellContext) {
      const { hasChildren, row, col } = cellContext;
      let className = !hasChildren ? "leafNode " : "parentNode";
      className += ` cellInfo_${row}_${col} cellInfo_${col} cellInfo_${row}`;
      return className;
    },
    renderBodyCell(cellContext) {
      const { col, rowId, td, pId } = cellContext;
      const record = this.mapTableData[rowId];
      if (!record) return "";
      const mergeInfos = td.parentNode.querySelectorAll(".merge-info");
      Array.from(mergeInfos || []).forEach(dom => {
        dom && td.parentNode.removeChild(dom);
      });
      if (pId === "-1" && col === 4) {
        setTimeout(() => {
          const mergeInfos = td.parentNode.querySelectorAll(".merge-info");
          Array.from(mergeInfos || []).forEach(dom => {
            dom && td.parentNode.removeChild(dom);
          });
          const width = td.parentNode.offsetWidth - td.offsetLeft - 32;
          td.parentNode.appendChild(
            genDOM(
              `<div class='merge-info' style='left:${td.offsetLeft}px;width:${width}px'>` +
                genDomItem(record.differenceTotal) +
                genDomItem(record.entityAmountTotal) +
                genDomItem(record.icpAmountTotal) +
                "</div>"
            )
          );
        }, 0);
      }
      return "";
    },
    setMapTableData(data) {
      const mapData = this.mapTableData;
      const tempArr = [...data];
      while (tempArr.length > 0) {
        const tempNode = tempArr.shift();
        const { objectId, children } = tempNode;
        mapData[objectId] = tempNode;

        if (children && children.length > 0) {
          Array.prototype.unshift.apply(tempArr, children);
        }
      }
      this.$set(this, "mapTableData", mapData);
    },
    getRowDataById(rowId) {
      return this.mapTableData[rowId] || {};
    },
    updateTable() {
      this.$refs.reportGrid.deleteFilteItemById("all");
      this.updateTableData();
    },
    updateTableData(data = this.showTableData) {
      this.$refs.reportGrid.updateTableData(data);
    },
    // ======
    showDimMemberName(item, colName) {
      // const {name,id} = item;
      const WORD_MAPPING = { code: "code", name: "name" };
      const PROPS_ARR = ["code", "name"];
      const { name, code } = item;
      if (PROPS_ARR.indexOf(this.cellType) !== -1) {
        return item[WORD_MAPPING[this.cellType]] || item.name;
      } else {
        // 科目列，小计、合计 只显示小计、合计
        if (
          colName === SUBJECT_PROPR_NAME &&
          SUBJECT_PARENT_NODE.indexOf(name) !== -1
        ) {
          return code || name;
        } else {
          return code ? `${code}-${name}` : name;
        }
      }
    },
    // 通用逻辑
    frozenFirstCol() {
      this.$set(this, "frozenLeft", 2);
      this.$refs.reportGrid.setFixedColumns(2);
      this.$emit("update:frozenLeftIndex", 2);
      this.$emit("update:frozenColName", "");
    },
    forzenToCol() {
      // 计算列宽 是否超出窗口范围
      if (this.isOutRange()) {
        this.$refs.reportGrid.setFixedColumns(this.frozenLeft);
        this.$emit("update:frozenLeftIndex", this.frozenLeft);
        this.$emit("update:frozenColName", "");
      } else {
        UiUtils.errorMessage("冻结失败，冻结区域超出窗口");
      }
    },
    isOutRange() {
      if (this.frozenLeft > 4) return false;
      const tableContWidth = this.$refs.reportGrid.$el.offsetWidth;
      return tableContWidth > this.getFreezeColWidth();
    },
    getFreezeColWidth() {
      const { columns, frozenLeft } = this;
      const colWidthObj = this.$refs.reportGrid.currColWidthObj;
      let colWidth = 0;
      for (let i = 0, LEN = columns.length; i < LEN; i++) {
        const { dataIndex } = this.columns[i];
        colWidth += colWidthObj[dataIndex];
        if (i === frozenLeft - 1) {
          break;
        }
      }
      return colWidth;
    },
    cancelFrozen() {
      this.$emit("update:frozenColName", "");
      this.$set(this, "frozenLeft", 0);
      this.$refs.reportGrid.setFixedColumns(0);
      this.$emit("update:frozenLeftIndex", 0);
    }
  }
};
</script>
<style lang="less">
.reportCont {
  .operation-btn,
  .drill-btn {
    display: inline-block;
    height: 30px;
    line-height: 30px;
    // padding: 0 @yn-padding-l;
    border-radius: @yn-border-radius-base;
    color: @yn-primary-color;
  }
  .link-btn:hover {
    background-color: @yn-icon-bg-color;
  }
  .subtotal {
    color: @yn-error-color;
  }
  div.reportTable td.leafNode {
    background-color: @yn-disabled-bg-color;
    position: relative;
  }
  .htColHeaderBg {
    background-color: @yn-table-header-bg !important;
  }
  .header-expand-btn > span {
    display: inline-block;
    width: calc(100% - 40px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .handsontable td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  td.simple-cell-readonly {
    background-color: @yn-component-background;
  }
  .leafNodeAmount::after {
    content: url(../../../../image/c1_cr_form_enter.svg);
    position: absolute;
    top: 9px;
    cursor: pointer;
    right: -1px;
  }
}
</style>
<style lang="less" scoped>
/deep/ .ht_master .htCore tr {
  position: relative;
}
.reportTableCont {
  height: 100%;
  display: flex;
  flex-flow: column;
}
/deep/ .merge-info {
  position: absolute;
  left: 0;
  top: 0;
  height: 34px;
  line-height: 34px;
  padding: 0 1rem;
  background: #fff;
  white-space: nowrap;
  overflow: hidden;
}
/deep/ .m-wrap {
  display: inline-block;
  margin-right: 3.125rem;
  min-width: 12.5rem;
  .m-title {
    color: @yn-text-color-secondary;
  }
  .m-value {
    margin-left: 0.5rem;
    color: @yn-money-color;
  }
}
/deep/ ._select-cell.simple-cell {
  position: relative;
}
/deep/ ._select-cell.simple-cell > div:first-of-type {
  margin-left: 1.875rem;
}
/deep/ ._select .select-title {
  margin-left: 1.875rem;
}
/deep/ .dom-select-container {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translate(0, -50%);
}
.reportCont {
  overflow: hidden;
  .reportTable {
    height: calc(100% - 3rem);
    padding: @yn-margin-l @yn-margin-xl 0;
    background: @yn-component-background;
  }
  .iconBtn {
    cursor: pointer;
    margin-left: @yn-margin-s;
  }
  .rowExpandIcon {
    cursor: pointer;
    margin-right: @yn-margin-s;
  }
  .ident1 {
    padding-left: @yn-padding-l;
  }
  span.ident0 {
    height: 15px;
    display: inline-block;
  }

  .amountColumn {
    display: inline-block;
    width: 100%;
    text-align: right;
  }
  .jumpOperation {
    color: @yn-chart-1;
    cursor: pointer;
  }
  .footer {
    height: 3rem;
    line-height: 3rem;
    width: 100%;
    background: @yn-body-background;
    padding: 0 1rem;
    font-size: @yn-font-size-base;
  }
  .footer_amount {
    font-weight: 600;
    color: @yn-money-color;
  }
}
</style>
