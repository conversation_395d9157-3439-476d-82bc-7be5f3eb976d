export const dataTest = {
  data: {
    hasMore: false,
    items: [
      {
        attributes: {
          entityCode: "E0012",
          matchFlag: "已匹配",

          accountName: {
            code: "112201",
            name: "应收账款-商品销售款",
            id: "11ecf90df71c97e7b13a1b6cf19cc0a5"
          },
          debitLocalamount: "26,842.74000",
          debitAmount: "0.00000",
          localCurrencyCode: "CNY",
          source: "group2",
          atr12: "1",
          voucherNo: "**********",
          entityNameTxt: {
            code: "E0012",
            name: "中源高新技术实业有限公司",
            id: "11edc242c0ba7b25a9e393d0234d8ee8"
          },
          voucherDate: "2022-01-01 00:00:00",
          icpNameTxt: {
            code: "E0001",
            name: "源培集团股份有限公司",
            id: "11edc242e9fa7a3ba9e35b1169ab6f34"
          },
          originalCurrencyCode: "HKD",
          creditAmount: "0.00000",
          creditLocalamount: "26,000.00000",
          voucherDescription: "~",
          objectId: "63701f677a2411eeb7ae0050568725f0"
        },
        hasChildren: true,
        id: "11ee8c57317357348b6265e6410ed5c4",
        _select: 1,
        key: "11ee8c57317357348b6265e6410ed5c4",
        level: 0,
        parentId: "1"
      },
      {
        attributes: {
          entityCode: "E0012",
          matchFlag: "已匹配",
          accountName: {
            code: "112201",
            name: "应收账款-商品销售款",
            id: "11ecf90df71c97e7b13a1b6cf19cc0a5"
          },
          debitLocalamount: "52,842.74000",
          debitAmount: "0.00000",
          localCurrencyCode: "CNY",
          source: "group2",
          atr12: "2",
          voucherNo: "**********",
          entityNameTxt: {
            code: "E0012",
            name: "中源高新技术实业有限公司",
            id: "11edc242c0ba7b25a9e393d0234d8ee8"
          },
          voucherDate: "2022-02-01 00:00:00",
          icpNameTxt: {
            code: "E0001",
            name: "源培集团股份有限公司",
            id: "11edc242e9fa7a3ba9e35b1169ab6f34"
          },
          originalCurrencyCode: "HKD",
          creditAmount: "0.00000",
          creditLocalamount: "52,000.00000",
          voucherDescription: "~",
          objectId: "637024597a2411eeb7ae0050568725f0"
        },
        hasChildren: true,
        id: "11ee8c573173a5558b62cbe6ddeec703",
        _select: 2,
        key: "11ee8c573173a5558b62cbe6ddeec703",
        level: 0,
        parentId: "1"
      },
      {
        attributes: {
          entityCode: "E0012",
          matchFlag: "已匹配",
          accountName: {
            code: "222101",
            name: "应交增值税-应交增值税",
            id: "11ecf90df7206868b13a37bb1f1a778a"
          },
          debitLocalamount: "26,000.00000",
          debitAmount: "0.00000",
          localCurrencyCode: "CNY",
          source: "group2",
          atr12: "1",
          voucherNo: "**********",
          entityNameTxt: {
            code: "E0012",
            name: "中源高新技术实业有限公司",
            id: "11edc242c0ba7b25a9e393d0234d8ee8"
          },
          voucherDate: "2022-02-01 00:00:00",
          icpNameTxt: {
            code: "E0001",
            name: "源培集团股份有限公司",
            id: "11edc242e9fa7a3ba9e35b1169ab6f34"
          },
          originalCurrencyCode: "HKD",
          creditAmount: "0.00000",
          creditLocalamount: "26,000.00000",
          voucherDescription: "~",
          objectId: "637033777a2411eeb7ae0050568725f0"
        },
        hasChildren: true,
        id: "11ee8c573173cc668b6263bd13f333bc",
        _select: 3,
        key: "11ee8c573173cc668b6263bd13f333bc",
        level: 0,
        parentId: "1"
      },
      {
        attributes: {
          entityCode: "E0012",
          matchFlag: "已匹配",
          accountName: {
            code: "222101",
            name: "应交增值税-应交增值税",
            id: "11ecf90df7206868b13a37bb1f1a778a"
          },
          debitLocalamount: "13,000.00000",
          debitAmount: "0.00000",
          localCurrencyCode: "CNY",
          source: "group2",
          atr12: "2",
          voucherNo: "**********",
          entityNameTxt: {
            code: "E0012",
            name: "中源高新技术实业有限公司",
            id: "11edc242c0ba7b25a9e393d0234d8ee8"
          },
          voucherDate: "2022-01-01 00:00:00",
          icpNameTxt: {
            code: "E0001",
            name: "源培集团股份有限公司",
            id: "11edc242e9fa7a3ba9e35b1169ab6f34"
          },
          originalCurrencyCode: "HKD",
          creditAmount: "0.00000",
          creditLocalamount: "13,000.00000",
          voucherDescription: "~",
          objectId: "6370379e7a2411eeb7ae0050568725f0"
        },
        hasChildren: true,
        id: "11ee8c573173f3778b6257533d917c39",
        _select: 4,
        key: "11ee8c573173f3778b6257533d917c39",
        level: 0,
        parentId: "1"
      }
    ]
  },
  message: "",
  messageList: null,
  messageType: null,
  success: true
};
