<template>
  <div class="title">
    <div class="title-top">
      <div class="top">
        <div
          v-if="Object.keys(reconciliationInfo).length > 0"
          class="title-info"
        >
          <span class="title-word">{{ reconciliationInfo.name }}</span>
          <yn-divider type="vertical" />
          <span v-if="reconciliationInfo.desc" class="desc">{{
            reconciliationInfo.desc
          }}</span>
          <span class="creator">{{ reconciliationInfo.creator }}</span>
          <span class="time">{{ `${reconciliationInfo.createTime}` }}</span>
          <span class="text">{{ `创建` }}</span>
        </div>
        <div v-if="isShowBtn" class="jumpBtn">
          <yn-button
            v-if="authSet['edit-config']"
            type="text"
            @click="showConfig"
          >
            修改对账配置
          </yn-button>
          <yn-button type="text" @click="showQueryPage">修改查询参数</yn-button>
        </div>
      </div>
      <div class="center">
        <span
          v-for="item in titleInfo"
          :key="Object.keys(item)[0]"
          class="dimItem"
        >
          <span class="dim-name">{{ Object.keys(item)[0] }}</span>
          <span class="dim-member-name">【{{ Object.values(item)[0] }}】</span>
        </span>
      </div>
    </div>
    <div class="footer">
      <div class="selection-btn">
        <div v-if="selectionType !== 'cancel'" class="match-num">
          已选
          <span>{{ reallyNum }}</span>
          条
        </div>
        <yn-button
          v-if="selectionType !== 'revoke' && authSet['manual-match']"
          :type="selectionType !== 'revoke' ? 'primary' : ''"
          :disabled="selectionType !== 'cancel' ? reallyNum === 0 : false"
          @click="submitSelection('match')"
        >
          匹配
        </yn-button>
        <yn-button
          v-if="selectionType !== 'match' && authSet['manual-match']"
          :type="selectionType === 'revoke' ? 'primary' : ''"
          :disabled="selectionType !== 'cancel' ? reallyNum === 0 : false"
          @click="submitSelection('revoke')"
        >
          撤销匹配
        </yn-button>
        <yn-button
          v-if="selectionType !== 'cancel'"
          @click="submitSelection('cancel')"
        >
          取消
        </yn-button>
      </div>
      <div v-show="selectionType === 'cancel'" class="toolBar">
        <!-- 小数位数 -->
        <span>
          小数位数：
          <yn-input-number
            ref="decimalPlaceInput"
            v-model="decimalPlace"
            class="decimalPlace"
            size="small"
            :precision="0"
            :min="0"
            :max="9"
            @blur="handleDecimalPlace"
          />
        </span>
        <span v-if="isShowBtn" @click="handleRefresh">
          <svg-icon type="icon-shuaxin" title="刷新" />
        </span>
        <span class="expandOrCloseAllIcon" @click="expandOrCloseAll">
          <svg-icon :type="expandIconName" :title="expandAllTitle" />
        </span>
        <yn-dropdown>
          <yn-menu slot="overlay" @click="handleExport">
            <yn-menu-item key="excel">
              Excel
            </yn-menu-item>
            <yn-menu-item key="pdf">
              PDF
            </yn-menu-item>
          </yn-menu>
          <span>
            <svg-icon
              class="search-icon-export"
              type="icon-a-c1_cr_export-dropdown"
              title="导出"
            />
          </span>
        </yn-dropdown>
        <yn-dropdown v-if="false">
          <yn-menu slot="overlay" @click="handleFrozen">
            <yn-menu-item key="frozenFirstCol">
              冻结首列
            </yn-menu-item>
            <yn-menu-item key="forzenToCol" :disabled="!frozenColName">
              <template v-if="!!frozenColName">
                {{ `冻结至“${frozenColName}”列` }}
              </template>
              <template v-else>
                <yn-tooltip placement="right">
                  <template slot="title">
                    <span>请选择需要冻结的列</span>
                  </template>
                  <span class="disabled-item">冻结至</span>
                </yn-tooltip>
              </template>
            </yn-menu-item>
            <yn-divider style="margin: 0" />
            <yn-menu-item key="cancelFrozen" :disabled="!frozenLeftIndex">
              <span>取消冻结</span>
            </yn-menu-item>
          </yn-menu>
          <span>
            <svg-icon type="icon-C1_form_freeze" title="冻结列" />
          </span>
        </yn-dropdown>
        <yn-dropdown>
          <yn-menu slot="overlay" @click="handleSetting">
            <yn-menu-item
              v-for="item in $data.$setOptions"
              :key="`${item.key}_${item.name}`"
            >
              {{ item.name }}
            </yn-menu-item>
          </yn-menu>
          <span>
            <svg-icon type="icon-set-up" title="设置" />
          </span>
        </yn-dropdown>
      </div>
    </div>
    <yn-modal
      :title="`${modalInfo.title}设置`"
      :visible="modalInfo.showModal"
      :destroyOnClose="true"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <yn-form :form="form" class="reconciliation-report-matches">
        <yn-form-item
          v-show="showMemberSeting"
          label="成员设置"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <yn-radio-group
            v-decorator="[
              'cellType',
              {
                initialValue: cellType
              }
            ]"
            :options="plainOptions"
            :defaultValue="cellType"
          />
        </yn-form-item>
      </yn-form>
    </yn-modal>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu";
import "yn-p1/libs/components/yn-menu-item";
import "yn-p1/libs/components/yn-icon";
import "yn-p1/libs/components/yn-popover/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-input-number/";
import "yn-p1/libs/components/yn-list/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-page-title/";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-menu-item/";
import { downloadFile } from "@/utils/common";
import commonService from "@/services/common";
import voucherReconciliationService from "@/services/voucherReconciliation";
import { mapState } from "vuex";
import { MEMBER_SHOW_TYPE } from "./constant";
const RECONCILIATION_REPORT = "reconciliationReport";

export default {
  name: "ReconciliatioReportTtile",
  props: {
    reconciliationInfo: {
      type: Object,
      default() {
        return {};
      }
    },
    titleInfo: {
      type: Array,
      default() {
        return [];
      }
    },
    tableInfo: {
      type: Object,
      default() {
        return {
          items: [] // 表格数据
        };
      }
    },
    reallyNum: {
      type: Number,
      default: 0
    },
    isExpandAll: {
      type: Boolean
    },
    fromPage: {
      type: String,
      default: ""
    },
    reportId: {
      type: String,
      default: ""
    },
    decimalPlaces: {
      type: Number,
      default: 2
    },
    changeTableDataByParams: {
      type: Function,
      default: null
    },
    frozenColName: {
      type: String,
      default: ""
    },
    frozenLeftIndex: {
      type: Number,
      default: 3
    },
    refreshId: {
      type: String,
      default: ""
    },
    selectionType: {
      type: String,
      default: "cancel"
    }
  },
  data() {
    return {
      plainOptions: [
        {
          label: "名称",
          value: "name"
        },
        {
          label: "唯一标识",
          value: "code"
        },
        {
          label: "名称+唯一标识",
          value: "nameAndCode"
        }
      ],
      cellType: "name",
      objectId: null,
      visible: false,
      decimalPlace: 2,
      cacheDecimalPlace: 2,
      $setOptions: [{ key: "memberShow", name: "成员显示" }],
      modalInfo: {
        title: "",
        type: "",
        showModal: false
      },
      matchType: 0,
      valueType: 0,
      plainOptionsMatches: [
        {
          label: "显示",
          value: 0
        },
        {
          label: "隐藏",
          value: 1
        }
      ],
      matchesValidateStatus: "success",
      help: "",
      form: this.$form.createForm(this, {
        name: "displaySettings"
      }),
      labelCol: {
        span: 4,
        sm: 4
      },
      wrapperCol: {
        span: 20,
        sm: 20
      }
    };
  },
  computed: {
    // 来源，合并任务（merge_search）跳转过来的不显示修改对账配置、查询、刷新等按钮
    isShowBtn() {
      return this.fromPage !== "merge_search";
    },
    expandIconName() {
      return !this.isExpandAll ? "icon-zhedie" : "icon-zhankai";
    },
    expandAllTitle() {
      return !this.isExpandAll ? "全部展开" : "全部收起";
    },
    showMemberSeting() {
      return this.modalInfo.type === "memberShow";
    },
    showMatches() {
      return this.modalInfo.type === "matches";
    },
    currReportId() {
      return this.refreshId || this.reportId;
    },
    ...mapState({
      authSet: state => state.voucherReconciliation.authSet
    })
  },
  watch: {
    reconciliationInfo: {
      handler(newVal) {
        this.echoSettings(newVal);
      },
      immediate: true
    },
    decimalPlaces: {
      handler(newVal) {
        this.decimalPlace = this.decimalPlaces;
      },
      immediate: true
    }
  },
  created() {
    this.getSettingType();
  },
  mounted() {
    document.body.addEventListener("click", this.decimalPlaceBlur);
  },
  beforeDestroy() {
    document.body.removeEventListener("click", this.decimalPlaceBlur);
  },
  methods: {
    decimalPlaceBlur(e) {
      let inputDom = e.target;
      let isContinue = true;
      while (isContinue) {
        if (!inputDom) {
          return;
        }
        if (inputDom.tagName === "BODY") {
          this.$refs.decimalPlaceInput.$el.querySelector("input").blur();
          isContinue = false;
          // 防止，点击到svg 报错
        } else if (
          typeof inputDom.className === "string" &&
          inputDom.className.indexOf("decimalPlace") !== -1
        ) {
          isContinue = false;
        } else {
          inputDom = inputDom.parentNode;
        }
      }
    },
    echoSettings(obj) {
      const { matchType, toleranceNumber, tolerancePercentage } = obj;
      this.matchType = matchType || 0;
      // 当数值类型，没有值时，判断百分比类型，百分类型没有值，默认是 数值类型
      this.valueType = toleranceNumber ? 0 : tolerancePercentage ? 1 : 0;
      this.tolerance = toleranceNumber || tolerancePercentage;
      this.form.setFieldsValue({
        tolerance: this.tolerance,
        matchType,
        valueType: this.valueType
      });
    },
    // 获取 表格配置信息
    getSettingType() {
      commonService("getLastSettingV", {
        key: RECONCILIATION_REPORT, // 持股方
        tag: "userIndexName"
      }).then(res => {
        const { objectId, value } = res.data ? res.data.data : {};
        this.objectId = objectId;
        this.cellType = value;
        this.$emit("changeCellType", this.cellType);
      });
    },
    submitSelection(type) {
      if (this.selectionType === type) {
        this.$emit("submitSelect", type);
        return;
      }
      this.$emit("update:selectionType", type);
      if (type === "cancel") {
        this.$emit("submitSelect", "cancel");
      } else {
        this.$emit(
          "submitSelect",
          "select",
          type === "revoke" ? "已匹配" : "匹配失败"
        );
      }
    },
    expandOrCloseAll() {
      this.$emit("update:isExpandAll", !this.isExpandAll);
    },
    showConfig() {
      this.$emit("openDrawer", "config", true);
    },
    showQueryPage() {
      this.$emit("openDrawer", "queryParam", true);
    },
    handleRefresh() {
      this.$emit("handleRefresh", true);
    },
    async handleExport(event) {
      const { reconciliationInfo, currReportId, decimalPlace } = this;
      voucherReconciliationService("export", {
        fileName: reconciliationInfo.name,
        fileType: event.key,
        memberShowType: MEMBER_SHOW_TYPE[this.cellType],
        reportId: currReportId,
        decimalPlace: Number(decimalPlace)
      }).then(res => {
        downloadFile(res);
      });
    },
    handleFrozen(event) {
      this.$emit("setFrozen", event.key);
    },
    handleSetting({ key }) {
      const [type, title] = key.split("_");
      this.modalInfo = {
        type,
        title,
        showModal: true
      };
    },
    // 校验容差
    checkTolerance(val) {
      const toleranceType = [
        "checkToleranceNumber",
        "checkTolerancePercentage"
      ]; // 容差类型 【数值，百分比】
      if (!val.trim()) return true;
      // 百分比
      if (
        typeof Number(val) === "number" &&
        isNaN(Number(val)) &&
        this.valueType
      ) {
        return false;
      }
      return (
        this[toleranceType[this.valueType]] &&
        this[toleranceType[this.valueType]](val)
      );
    },
    // 校验数值类型
    checkToleranceNumber(val) {
      return /^[+-]?\d*\.?\d{0,9}$/.test(val);
    },
    // 校验百分比类型
    checkTolerancePercentage(val) {
      return val >= 0 && val <= 100;
    },
    changeToleranceValue(event) {
      let val = event.target.value;
      if (!this.checkTolerance(event.target.value)) {
        val = this.tolerance;
        event.target.value = val;
      }
      if (val) {
        this.clearToLeranceValidateInfo();
        this.tolerance = val;
      } else {
        // 清空值
        this.matchesValidateStatus = "error";
        this.help = "请输入容差值";
      }
    },
    handleMatchesChange($event) {
      this.matchType = $event.target.value;
      this.tolerance = "";
      this.clearToLeranceValidateInfo();
    },
    clearToLeranceValidateInfo() {
      this.matchesValidateStatus = "success";
      this.help = "";
      this.form.setFieldsValue({
        tolerance: ""
      });
    },
    async handleOk() {
      await this.form.validateFields((err, values) => {
        if (err) {
          this.help = "请输入容差值";
          this.matchesValidateStatus = "error";
          return;
        }
        this.saveOrUpdateUserSetting(values.cellType);
        this.modalInfo.showModal = false;
      });
    },
    reQueryData() {
      this.changeTableDataByParams(this.decimalPlace);
    },
    saveOrUpdateUserSetting(cellType) {
      this.cellType = cellType;
      commonService("saveOrUpdateUserSetting", {
        value: cellType,
        objectId: this.objectId,
        tag: "userIndexName",
        key: RECONCILIATION_REPORT
      });
      this.$emit("changeCellType", cellType);
    },
    handleDecimalPlace(e) {
      const decimalPlace = Number(e.target.value);
      this.decimalPlace = decimalPlace;
      if (this.decimalPlace !== this.cacheDecimalPlace) {
        this.cacheDecimalPlace = this.decimalPlace;
      } else {
        return;
      }
      this.reQueryData();
    },
    handleCancel() {
      // 还原数据，并清除校验信息
      this.modalInfo.showModal = false;
      this.help = "";
      this.matchesValidateStatus = "";
      this.echoSettings(this.reconciliationInfo);
    },
    handleChangeType(val) {
      this.valueType = val;
      // 切换以后，不在缓存上次输入正确的数值
      this.form.setFieldsValue({
        tolerance: ""
      });
      this.tolerance = "";
    }
  }
};
</script>
<style lang="less" scoped>
.title {
  // padding: @yn-margin-xl @yn-margin-xxxl 0 @yn-margin-xxxl;
  .title-info {
    span.title-word {
      font-size: @rem16;
      font-weight: 600;
      color: @yn-text-color;
    }
    height: @rem32;
    line-height: @rem32;
  }

  .title-top {
    padding-top: @yn-margin-l;
  }
  .top,
  .footer {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }
  .footer {
    margin-bottom: 0;
    background: @yn-component-background;
    border-radius: @yn-console-content-radius @yn-console-content-radius 0 0;
    padding: 0.75rem 1rem 0;
  }
  .desc,
  .time,
  .creator,
  .text {
    margin-right: @yn-margin-s;
  }
  .creator,
  .time,
  .text {
    color: @yn-label-color;
    font-size: @yn-font-size-sm;
  }
  .jumpBtn {
    margin-left: auto;
    color: @yn-chart-1;
    & > span {
      cursor: pointer;
    }
    & > span:first-child {
      margin-right: @yn-margin-xxl;
    }
  }
  .selection-btn {
    display: flex;
    align-items: center;
    & > ::v-deep(button),
    .match-num {
      margin-right: 0.5rem;
    }
  }
  .center {
    padding-bottom: @rem10;
    .dimItem {
      margin-right: @yn-margin-xl;
      .dim-name {
        color: @yn-text-color-secondary;
      }
      .dim-member-name {
        color: @yn-text-color;
      }
    }
  }

  .toolBar {
    margin-left: auto;
    & > * {
      margin-left: @yn-margin-xl;
      cursor: pointer;
      .search-icon-export {
        position: relative;
        top: 4px;
        /deep/.svg-icon {
          font-size: @rem32;
        }
      }
    }
  }
  .expandOrCloseAllIcon {
    font-size: @yn-font-size-lg;
  }
}
div.matches {
  margin-bottom: 0;
  /deep/.toleranceInput {
    margin-top: 1.5rem;
  }
}
</style>
