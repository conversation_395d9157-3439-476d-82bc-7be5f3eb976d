<template>
  <yn-spin :spinning="spinning" class="reconciliationParamsCont cs-container">
    <div v-if="!isHide" class="reportCont">
      <Title
        :fromPage="fromPage"
        :reconciliationInfo="reconciliationInfo"
        :reportId="reportId"
        :titleInfo="titleInfo"
        :tableInfo="tableInfo"
        :decimalPlaces="decimalPlaces"
        :isExpandAll.sync="isExpandAll"
        :reallyNum="reallyNum"
        :changeTableDataByParams="changeTableDataByParams"
        :frozenColName="frozenColName"
        :frozenLeftIndex="frozenLeftIndex"
        :refreshId="refreshId"
        :selectionType.sync="selectionType"
        @submitSelect="submitSelect"
        @changeCellType="changeCellType"
        @handleRefresh="handleRefresh"
        @openDrawer="setDrawerStatus"
        @setFrozen="setFrozen"
      />
      <reconciliation-table
        ref="table"
        :tableInfo="tableInfo"
        :titleInfo="titleInfo"
        :matchFlag="matchFlag"
        :cellType="cellType"
        :reportId="reportId"
        :refreshId="refreshId"
        :isExpandAll="isExpandAll"
        :spinning.sync="spinning"
        :selection="isSelect ? selection : false"
        :treeSelection="false"
        :isSelect="isSelect"
        :selectable="true"
        :frozenColName.sync="frozenColName"
        :frozenLeftIndex.sync="frozenLeftIndex"
        :reconciliationConfig="reconciliationConfig"
        :isManuallyClick.sync="isManuallyClick"
        :titleMap="titleMap"
        @selectChange="handlerSelect"
      />
    </div>
    <reconciliation-config
      v-if="reconciliationConfigVisible"
      :drawerVisible="reconciliationConfigVisible"
      operationType="edit"
      :isReportPage="true"
      :selectTreeNodeInfo="selectTreeNode"
      @closeDrawer="setDrawerStatus('config', false)"
      @saveConfigCb="saveConfigCb"
    />
    <yn-drawer
      v-if="reconciliationQueryVisible"
      :title="title"
      placement="right"
      :width="440"
      :visible="true"
      :maskClosable="false"
      wrapClassName="drawerReconciliationQuery"
      @close="setDrawerStatus('queryParam', false)"
    >
      <reconciliation-query
        :title="title"
        :isDrawer="true"
        :decimalPlace="decimalPlaces"
        :echoData="requeryParams"
        :selectTreeNodeInfo="selectTreeNode"
        class="drawerQuery"
        @generateReport="generateReport"
        @generateReportBefore="generateReportBefore"
        @generateReportError="generateReportError"
      />
    </yn-drawer>
    <process-reconciliation-reportModal
      v-if="isShowModal"
      :hasAuthority="hasAuthority"
      :currTreeNodeInfo="selectTreeNode"
      operationType="saveAs"
      :reconciliationConfig="reconciliationConfig"
      @closeModal="closeModal"
    />
  </yn-spin>
</template>
<script>
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-drawer/";
import Title from "./title.vue";
import ReconciliationQuery from "../right/ReconciliationQuery.vue";
import ReconciliationConfig from "../config";
import ReconciliationTable from "./table.vue";
import voucherReconciliationService from "@/services/voucherReconciliation";
import { mapActions, mapState } from "vuex";
import { VOUCHER_RECONCILIATION } from "@/constant/reconciliation.js";
import { PAGE_SIZE } from "@/views/voucherReconciliation/reconciliationStatement/report/constant.js";
import ProcessReconciliationReportModal from "@/views/reconciliation/reconciliationStatement/layoutLeft/ProcessReconciliationReport.vue";
const DRAWER_INFO = {
  queryParam: "reconciliationQueryVisible",
  config: "reconciliationConfigVisible"
};
export default {
  name: "VoucherReconciliationReport",
  components: {
    Title,
    ReconciliationTable,
    ReconciliationQuery,
    ReconciliationConfig,
    ProcessReconciliationReportModal
  },
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  provide() {
    return {
      showSaveAsModal: this.openSaveAsModal,
      reconciliationModuleName: VOUCHER_RECONCILIATION
    };
  },
  data() {
    return {
      reconciliationConfigVisible: false,
      reconciliationQueryVisible: false,
      tableInfo: {},
      reallyNum: 0,
      titleInfo: [],
      isSelect: false,
      selection: [],
      decimalPlaces: 2,
      matchFlag: "",
      selectionType: "cancel",
      reconciliationInfo: undefined,
      cellType: "name",
      noParentSelection: [],
      parentSelection: [],
      spinning: true,
      isHide: false,
      isExpandAll: false,
      requeryParams: {},
      selectTreeNode: {},
      title: "",
      fromPage: "", // 来源，合并任务（merge_search）跳转过来的不显示修改对账配置、查询、刷新等按钮
      isShowModal: false,
      currTreeNodeInfo: {},
      reconciliationConfig: {}, // 对账配置 信息
      frozenColName: "",
      frozenLeftIndex: 3,
      reportId: "",
      refreshId: "", // 更改 小数位数、匹配项会调用接口，接口返回此值
      isManuallyClick: true,
      titleMap: {} // 对账查询参数 版本、年、期间、转换货币
    };
  },
  computed: {
    ...mapState({
      hasAuthority: state => state.voucherReconciliation.hasAuthority,
      menuMapObj: state => state.common.menuMapObj,
      configObj: state => state.voucherReconciliation.reconciliationConfigObj,
      mainLoading: state => state.voucherReconciliation.mainLoading
    })
  },
  created() {
    this.getPublicAuth();
    this.getAuthSet();
    this.setInitData(this.params.params);
    this.title = this.params.title;
    this.fromPage = this.params.params.fromPage;
  },
  mounted() {
    if (!this.selfTab) {
      const { title, selectTreeNode, ...params } = this.getTabParamsMixin();
      this.title = title;
      this.fromPage = params.fromPage;
      this.setInitData({ ...params, selectTreeNode });
    }
  },
  methods: {
    ...mapActions("voucherReconciliation", ["getPublicAuth", "getAuthSet"]),
    // 权限控制，另存为
    openSaveAsModal(reconciliationConfig) {
      this.reconciliationConfig = reconciliationConfig;
      this.isShowModal = true;
    },
    closeModal(type, treeNodeId) {
      if (type === "saveAs_ok") {
        // 另存为 成功后，激活对账列表页签，并重新选择树节点
        this.activationTab(treeNodeId);
      }
      this.isShowModal = false;
    },
    activationTab(treeNodeId) {
      const PATH = "/voucherReconciliation/reportList";
      // 激活平台页签，并 刷新 选中传入的treeNodeId
      const menuId = this.getMenuIdByPath(PATH);
      this.newtabMixin({
        id: menuId,
        uri: PATH,
        params: {
          selectTreeId: treeNodeId,
          tabId: menuId
        },
        noKeepAlive: false, // 是否需要缓存
        router: "voucherReconciliationReport" || "systemTab" // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
      });
      // 关闭对账配置抽屉
      this.reconciliationConfigVisible = false;
    },
    getMenuIdByPath(path) {
      return Object.keys(this.menuMapObj).filter(key => {
        const { uri = "" } = this.menuMapObj[key] || {};
        return uri.indexOf(path) !== -1;
      })[0];
    },
    // 选择操作
    submitSelect(type, flag) {
      switch (type) {
        case "select":
          // 开始选择
          this.isSelect = true;
          this.selection = [];
          this.matchFlag = flag;
          this.isExpandAll = false;
          this.nodeIdListByMatchFlag();
          break;
        case "match":
          // 匹配
          this.batchDoMatch();
          break;
        case "revoke":
          // 撤销匹配
          this.batchUnDoMatch();
          break;
        case "cancel":
          // 取消
          this.spinning = true;
          this.isSelect = false;
          this.selection = [];
          this.matchFlag = "";
          this.getChildren();
          this.break;
      }
    },
    async nodeIdListByMatchFlag() {
      this.spinning = true;
      await voucherReconciliationService("nodeListByMatchFlag", {
        limit: PAGE_SIZE,
        matchFlag: this.matchFlag,
        offset: 0,
        reportId: this.refreshId || this.reportId
      })
        .then(res => {
          this.tableInfo = { ...this.tableInfo, ...res.data.data };
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    async batchDoMatch() {
      this.spinning = true;
      await voucherReconciliationService("batchDoMatch", {
        childrenIdList: this.noParentSelection.map(item => item.objectId),
        parentIdList: this.parentSelection.map(item => item.objectId),
        reportId: this.refreshId || this.reportId
      })
        .then(res => {
          this.isSelect = false;
          this.selection = [];
          this.matchFlag = "";
          this.selectionType = "cancel";
          this.getChildren();
        })
        .catch(() => {
          this.spinning = false;
        });
    },
    async batchUnDoMatch() {
      this.spinning = true;
      await voucherReconciliationService("batchUnDoMatch", {
        childrenIdList: this.noParentSelection.map(item => item.objectId),
        parentIdList: this.parentSelection.map(item => item.objectId),
        reportId: this.refreshId || this.reportId
      })
        .then(res => {
          this.isSelect = false;
          this.selection = [];
          this.selectionType = "cancel";
          this.matchFlag = "";
          this.getChildren();
        })
        .catch(() => {
          this.spinning = false;
        });
    },
    handlerSelect(selection) {
      this.selection = selection;
      const selectionMap = this.selection.reduce((pre, next) => {
        pre[next.objectId] = next;
        return pre;
      }, {});
      const noParent = [];
      const parent = [];
      this.selection.forEach(item => {
        if (selectionMap[item.objectId].hasChildren) {
          parent.push(item);
        } else if (!selectionMap[item.parentId]) {
          noParent.push(item);
        }
      });
      this.noParentSelection = noParent;
      this.parentSelection = parent;
      this.reallyNum =
        noParent.length +
        parent.reduce((pre, next) => pre + next.childrenCount, 0);
    },
    setInitData(obj = this.requeryParams) {
      const { requeryParams } = obj;
      // 当任务Id 发生变化以后，需要清空刷新Id，后续表格展开
      this.refreshId = "";
      this.decimalPlaces = requeryParams.decimalPlaces;
      this.getReconciliation(requeryParams);
      this.requeryParams = obj || {};
      this.selectTreeNode = obj.selectTreeNode;
    },
    async getReconciliation(requeryParams) {
      await voucherReconciliationService("generateReport", {
        ...requeryParams,
        limit: PAGE_SIZE,
        offset: 0
      })
        .then(async res => {
          this.isExpandAll = false;
          const {
            voucherDate,
            matchFlag,
            reportId,
            voucherReconciliationInfo,
            reportPageResult,
            columnInfo
          } = res.data.data;
          this.titleInfo = [
            {
              凭证日期: voucherDate
            },
            {
              匹配标识: matchFlag
            }
          ];
          this.titleMap = {};
          this.reconciliationInfo = {
            ...voucherReconciliationInfo,
            matchType: matchFlag
          };
          // 合并任务里面 打开对账报告，目前需要从这里取到title值
          this.title = voucherReconciliationInfo.name;
          this.tableInfo = { columnInfo, ...reportPageResult };
          this.reportId = reportId;
          this.isHide = false;
          this.frozenColName = "";
          this.frozenLeftIndex = 3;
          await this.requestConfigData(requeryParams.objectId);
        })
        .finally(() => {
          this.spinning = false;
          this.isManuallyClick = true;
        });
    },
    // 获取对账配置信息
    async requestConfigData(id) {
      await voucherReconciliationService(
        "getReconciliationConfigById",
        id
      ).then(res => {
        this.reconciliationConfig = res.data;
      });
    },
    changeCellType(cellType) {
      this.cellType = cellType;
      if (this.isExpandAll) {
        this.isManuallyClick = false;
        this.isExpandAll = false;
      }
    },
    // 刷新事件
    async handleRefresh(isManualTrigger) {
      if (isManualTrigger) {
        this.spinning = true;
      }
      this.isManuallyClick = !this.isExpandAll;
      this.setInitData();
    },
    async setDrawerStatus(drawerType, status) {
      if (this.mainLoading) return;
      this[DRAWER_INFO[drawerType]] = status;
    },
    generateReportBefore() {
      this.spinning = true;
    },
    generateReportError() {
      this.spinning = false;
    },
    generateReport(obj) {
      this.spinning = true;
      this.setInitData(obj);
      this.reconciliationQueryVisible = false;
    },
    async changeTableDataByParams(decimalPlace = this.decimalPlaces) {
      this.spinning = true;
      this.decimalPlaces = decimalPlace;
      this.isExpandAll = false;
      const {
        data: { data }
      } = await voucherReconciliationService("refresh", {
        decimalPlace,
        refreshId: this.refreshId,
        reportId: this.reportId
      });
      this.refreshId = data;
      this.getChildren();
    },
    async getChildren() {
      this.spinning = true;
      voucherReconciliationService("expandChildren", {
        reportId: this.refreshId || this.reportId,
        parentId: -1,
        limit: PAGE_SIZE,
        offset: 0
      })
        .then(res => {
          this.tableInfo = { ...this.tableInfo, ...res.data.data };
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    async saveConfigCb(reconciliationConfig) {
      this.reconciliationConfig = reconciliationConfig;
      this.generateReportBefore();
      await this.handleRefresh(true);
      this.setDrawerStatus("config", false);
    },
    setFrozen(type) {
      this.$refs.table[type] && this.$refs.table[type]();
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.ant-spin-container {
  height: 100%;
}
/deep/.ant-drawer-body {
  padding-bottom: 80px;
  padding-left: @yn-padding-xxl;
  padding-right: @yn-padding-xxl;
  overflow: auto;
}
.drawerReconciliationQuery /deep/.ant-drawer-body {
  padding: 0;
  height: calc(100% - 45px);
}
.reconciliationParamsCont {
  height: 100%;
}
.reportCont {
  height: 100%;
  display: flex;
  flex-flow: column;
  .title {
    .top {
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
    }
  }
}
.drawerQuery {
  // padding: @rem24;
  padding-bottom: 53px;
}
</style>
