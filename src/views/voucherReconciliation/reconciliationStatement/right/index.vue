<template>
  <Right>
    <template #quertContent="obj">
      <voucher-reconciliation-query
        v-bind="obj"
        :echoDatas="params"
        @generateReport="generateReport"
      />
    </template>
  </Right>
</template>
<script>
import Right from "@/views/reconciliation/reconciliationStatement/layoutRight";
import VoucherReconciliationQuery from "./ReconciliationQuery.vue";
const TAB_URI = "/voucherReconciliation/report";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import _cloneDeep from "lodash/cloneDeep";
import { mapState } from "vuex";
import { params } from "./testparams";
export default {
  name: "VoucherLayoutRight",
  components: {
    Right,
    VoucherReconciliationQuery
  },
  props: {},
  data() {
    return {
      params
    };
  },
  computed: {
    ...mapState({
      configObj(state) {
        return state["voucherReconciliation"].reconciliationConfigObj;
      },
      selectTreeNode(state) {
        return state["voucherReconciliation"].selectTreeNode;
      }
    })
  },
  methods: {
    async generateReport(obj) {
      // 打开 页签
      const { id, name } = this.selectTreeNode;
      const tabId = id + AppUtils.generateUniqueId();
      this.newtabMixin({
        id: tabId,
        title: name,
        router: "voucherReconciliationReport",
        uri: TAB_URI,
        params: _cloneDeep({
          ...obj,
          tabId,
          title: name,
          reconciliationConfigObj: this.configObj
        })
      });
    }
  }
};
</script>
