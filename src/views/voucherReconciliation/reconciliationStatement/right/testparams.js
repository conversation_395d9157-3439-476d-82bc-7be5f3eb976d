export const params = {
  requeryParams: {
    decimalPlaces: 2,
    entity: ["E0001", "E0012"],
    icp: ["E0012", "E0001"],
    beginDate: "2022-01-01",
    endDate: "2022-02-01",
    matchFlag: [0, 1, 2],
    objectId: "11ee7ecd13bc5943a98025c56f82ca40"
  },
  selectItems: {
    entity: [
      {
        dimMemberId: "11edc242c0ba5408a9e339d89b183cc1",
        dimMemberName: "源培集团股份有限公司",
        dimCode: "__vue_devtool_undefined__",
        dimMemberCode: "E0001_bianma",
        dimMemberDbCode: "E0001",
        key: "11edc242c0ba5408a9e339d89b183cc1-self",
        label: "源培集团股份有限公司",
        memberType: 1,
        memberTypeValue: "self",
        objectId: "11edc242c0ba5408a9e339d89b183cc1-self",
        shardim: false,
        title: "成员(源培集团股份有限公司)",
        type: "member",
        value: "11edc242c0ba5408a9e339d89b183cc1",
        dbCodeIndex: "E0001"
      },
      {
        dimMemberId: "11edc242c0ba7b25a9e393d0234d8ee8",
        dimMemberName: "中源高新技术实业有限公司",
        dimCode: "__vue_devtool_undefined__",
        dimMemberCode: "E0012",
        dimMemberDbCode: "E0012",
        key: "11edc242c0ba7b25a9e393d0234d8ee8-self",
        label: "中源高新技术实业有限公司",
        memberType: 1,
        memberTypeValue: "self",
        objectId: "11edc242c0ba7b25a9e393d0234d8ee8-self",
        shardim: false,
        title: "成员(中源高新技术实业有限公司)",
        type: "member",
        value: "11edc242c0ba7b25a9e393d0234d8ee8",
        dbCodeIndex: "E0012"
      }
    ],
    icp: [
      {
        dimMemberId: "11edc242e9faa158a9e33d7553ccb16e",
        dimMemberName: "中源高新技术实业有限公司",
        dimCode: "__vue_devtool_undefined__",
        dimMemberCode: "E0012",
        dimMemberDbCode: "E0012",
        key: "11edc242e9faa158a9e33d7553ccb16e-self",
        label: "中源高新技术实业有限公司",
        memberType: 1,
        memberTypeValue: "self",
        objectId: "11edc242e9faa158a9e33d7553ccb16e-self",
        shardim: false,
        title: "成员(中源高新技术实业有限公司)",
        type: "member",
        value: "11edc242e9faa158a9e33d7553ccb16e",
        dbCodeIndex: "E0012"
      },
      {
        dimMemberId: "11edc242e9fa7a3ba9e35b1169ab6f34",
        dimMemberName: "源培集团股份有限公司",
        dimCode: "__vue_devtool_undefined__",
        dimMemberCode: "E0001_bianma",
        dimMemberDbCode: "E0001",
        key: "11edc242e9fa7a3ba9e35b1169ab6f34-self",
        label: "源培集团股份有限公司",
        memberType: 1,
        memberTypeValue: "self",
        objectId: "11edc242e9fa7a3ba9e35b1169ab6f34-self",
        shardim: false,
        title: "成员(源培集团股份有限公司)",
        type: "member",
        value: "11edc242e9fa7a3ba9e35b1169ab6f34",
        dbCodeIndex: "E0001"
      }
    ]
  },
  selectTreeNode: {
    id: "11ee7ecd13bc5943a98025c56f82ca40",
    isRoot: false,
    name: "对账报表名称",
    attr: { creatorFlag: true }
  },
  tabId: "11ee7ecd13bc5943a98025c56f82ca401701312678912",
  title: "对账报表名称",
  reconciliationConfigObj: {
    "11ee8e90ea6d4afd9a7369de2704752f": {
      data: {
        account: [],
        objectId: "11ee8e90ea6d4afd9a7369de2704752f",
        ruleId: "",
        updateBy: "控制台超级管理员",
        updateDate: "2023-11-29 16:25:55"
      },
      message: "",
      messageList: null,
      messageType: null,
      success: true,
      items: []
    },
    "11ee7ecd13bc5943a98025c56f82ca40": {
      data: {
        account: [
          {
            account: "11ecf90df71c97e7b13a1b6cf19cc0a5",
            otherAccount: "11ecf90df7254a9fb13abfeacf6f3640"
          },
          {
            account: "11ecf90df71c97e7b13a1b6cf19cc0a5",
            otherAccount: "11ecf90df7254a9fb13abfeacf6f3640"
          },
          {
            account: "11ecf90df7206868b13a37bb1f1a778a",
            otherAccount: "11ecf90df7228b40b13a51da17e439b2"
          },
          {
            account: "11ecf90df7206868b13a37bb1f1a778a",
            otherAccount: "11ecf90df7228b40b13a51da17e439b2"
          },
          {
            account: "11ecf90df71c499bb13a299e5fbb6161",
            otherAccount: "11ecf90df71c97e8b13a65faef4793ea"
          },
          {
            account: "11ecf90df71c499bb13a299e5fbb6161",
            otherAccount: "11ecf90df71c97e8b13a65faef4793ea"
          },
          {
            account: "11ecf90df7234ee3b13adf4371499687",
            otherAccount: "11ecf90df71d5b96b13ae36f0bac100a"
          },
          {
            account: "11ecf90df7234ee3b13adf4371499687",
            otherAccount: "11ecf90df71d5b96b13ae36f0bac100a"
          },
          {
            account: "11ecf90df72104e8b13a25ad2289537c",
            otherAccount: "11ecf90df7241294b13ae1b68a092f7d"
          },
          {
            account: "11ecf90df72104e8b13a25ad2289537c",
            otherAccount: "11ecf90df7241294b13ae1b68a092f7d"
          },
          {
            account: "11ecf90df71d5b92b13a7b05cae0ab74",
            otherAccount: "11ecf90df71fa5b0b13a8d9caf73501d"
          },
          {
            account: "11ecf90df71d5b92b13a7b05cae0ab74",
            otherAccount: "11ecf90df71fa5b0b13a8d9caf73501d"
          },
          {
            account: "11ecf90df71fccd6b13af13fb1d05403",
            otherAccount: "11ecf90df71ce62bb13a130ffacc4e92"
          },
          {
            account: "11ecf90df71fccd6b13af13fb1d05403",
            otherAccount: "11ecf90df71ce62bb13a130ffacc4e92"
          }
        ],
        objectId: "11ee7ecd13bc5943a98025c56f82ca40",
        ruleId: "11ee8ce96e0df1579d59d7d6ddba052f",
        updateBy: "控制台超级管理员",
        updateDate: "2023-11-29 14:58:15"
      },
      message: "",
      messageList: null,
      messageType: null,
      success: true,
      items: []
    }
  }
};
