<template>
  <div :class="['queryRange', isDrawer ? 'queryRangeDrawer' : '']">
    <p class="areaTitle">查询数据范围</p>
    <yn-form :form="form">
      <yn-form-item
        v-for="item in formItem"
        :key="item.dimCode"
        :label="item.label"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <component
          :is="item.component"
          v-decorator="[
            item.name,
            {
              initialValue: item.val,
              rules: [
                {
                  required: !!item.required,
                  message: `请选择${item.label}`
                }
              ]
            }
          ]"
          :class="item.className"
          v-bind="item.props"
          v-on="item.events"
        />
      </yn-form-item>
      <p class="areaTitle">报表显示设置</p>
      <yn-form-item
        label="保留小数位数"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <yn-input-number
          v-decorator="[
            'decimalPlaces',
            {
              initialValue: decimalPlaces,
              rules: [
                {
                  required: true,
                  message: '请输入'
                }
              ]
            }
          ]"
          class="decimal"
          :precision="0"
          :max="9"
          :min="0"
          @change="setPrecision"
        />
      </yn-form-item>
      <yn-form-item
        label=" "
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        class="footer "
      >
        <yn-button
          class="generateReport"
          type="primary"
          :loading="generateReportBtnLoading"
          @click="generateReport"
        >
          生成对账报表
        </yn-button>
        <yn-button @click="reset">重置</yn-button>
      </yn-form-item>
    </yn-form>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-date-picker/";
import "yn-p1/libs/components/yn-input-number/";
import "yn-p1/libs/components/yn-select/";
import ShowDimListInput from "@/components/hoc/ShowDimListInput.vue";
import { TRANSFER_TIPS_WORD } from "@/constant/common.js";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
import { DIM_INFO_ENTITY, DIM_INFO_ICP } from "@/constant/reconciliation";
import _cloneDeep from "lodash/cloneDeep";
import moment from "moment";

import { mapState, mapActions, mapMutations } from "vuex";
const formItemOrigin = [
  {
    component: "yn-date-picker",
    className: "queryRangeItem",
    label: "凭证开始日期",
    name: "beginDate",
    props: {},
    val: ""
  },
  {
    component: "yn-date-picker",
    label: "凭证结束日期",
    className: "queryRangeItem",
    name: "endDate",
    props: {},
    required: true,
    val: ""
  },
  {
    component: "yn-select",
    label: "匹配标识",
    name: "matchFlag",
    props: {
      mode: "multiple",
      options: [
        { label: "已匹配", value: 0 },
        { label: "未匹配", value: 1 },
        { label: "匹配失败", value: 2 }
      ]
    },
    required: true,
    val: []
  }
];
export default {
  name: "VoucherReconciliationQuery",
  components: {
    // eslint-disable-next-line vue/no-unused-components
    ShowDimListInput,
    // eslint-disable-next-line vue/no-unused-components
    AsynSelectDimMember
  },
  props: {
    title: {
      type: String,
      default: () => ""
    },
    isDrawer: {
      type: Boolean,
      default: false
    },
    echoData: {
      type: Object,
      default: () => null
    },
    selectTreeNodeInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: this.$form.createForm(this, {
        name: "queryRange"
      }),
      labelCol: {
        span: 4,
        sm: 8
      },
      wrapperCol: {
        span: 18,
        sm: 16
      },
      decimalPlaces: 2,
      formItem: [],
      selected: {},
      generateReportBtnLoading: false
    };
  },
  computed: {
    ...mapState({
      currSelectTreeNode(state) {
        return state["voucherReconciliation"].selectTreeNode;
      }
    })
  },
  watch: {
    title: {
      async handler() {
        this.initFormItem();
        this.form.resetFields();
        this.initForm();
      }
    }
  },
  async created() {
    this.initFormItem();
    this.initForm();
  },
  methods: {
    ...mapActions("voucherReconciliation", ["asyncGenerateReport"]),
    ...mapMutations("voucherReconciliation", ["controlMainLoading"]),
    initForm() {
      if (!this.echoData) return;
      const { selectItems, requeryParams } = this.echoData;
      this.selected = selectItems;
      Object.keys(selectItems).forEach(key => {
        const dimInputObj = this.formItem.find(item => item.name === key);
        this.$set(dimInputObj.props.dimInfo, "selectedItem", selectItems[key]);
      });
      const {
        beginDate,
        matchFlag,
        endDate,
        icp,
        entity,
        decimalPlaces
      } = requeryParams;
      this.decimalPlaces = decimalPlaces;
      this.$nextTick(() => {
        this.form.setFieldsValue({
          entity: entity || {},
          icp: icp | {},
          beginDate: beginDate,
          matchFlag: matchFlag,
          endDate: endDate,
          decimalPlaces
        });
      });
    },
    setPrecision(value) {
      this.decimalPlaces = value;
    },
    initFormItem() {
      // TODO 初始化值
      const tempArr = [DIM_INFO_ENTITY, DIM_INFO_ICP].map(item => {
        const { dimCode, dimName } = item;
        return {
          label: dimName,
          name: dimCode.toLowerCase(),
          val: "",
          required: true,
          className: "queryRangeItem",
          props: {
            ifEmptySelectedAll: false,
            dimInfo: { ...item, selectedItem: [] },
            analyticExpName: dimCode === "icp" ? "parseICPExp" : void 0,
            tipsWord:
              dimCode === "icp"
                ? TRANSFER_TIPS_WORD.restrictedCompany()
                : void 0
          },
          events: {
            change: v => {
              this.selected[v.dimCode.toLowerCase()] = [...v.selectedItem];
              const dimInputObj = this.formItem.find(
                item => item.name === dimCode.toLowerCase()
              );
              this.$set(dimInputObj.props.dimInfo, "selectedItem", [
                ...v.selectedItem
              ]);
            }
          },
          component: "show-dim-list-input"
        };
      });
      this.$set(this, "formItem", tempArr.concat(_cloneDeep(formItemOrigin)));
    },
    async generateReport() {
      this.$emit("generateReportBefore");
      this.generateReportBtnLoading = true;
      await this.form.validateFields(async (err, value) => {
        if (err) {
          this.generateReportBtnLoading = false;
          // 对账报告页面，参数有问题，关闭loading
          this.$emit("generateReportError");
        } else {
          const params = this.formatRequestParams(value);
          this.generateReportBtnLoading = false;
          this.controlMainLoading(false);
          this.$emit("generateReport", {
            requeryParams: params,
            selectItems: this.selected,
            selectTreeNode: this.getTreeNodeInfo()
          });
        }
      });
    },
    formatRequestParams(params) {
      params["objectId"] = this.getTreeNodeInfo().id;
      params["entity"] = this.selected["entity"].map(
        item => item.dimMemberDbCode
      );
      params["icp"] = this.selected["icp"].map(item => item.dimMemberDbCode);
      params["beginDate"] = !params["beginDate"]
        ? ""
        : moment(params["beginDate"]).format("YYYY-MM-DD");
      // params["beginDate"] = moment(params["beginDate"]).format("YYYY-MM-DD");
      params["endDate"] = moment(params["endDate"]).format("YYYY-MM-DD");
      return params;
    },
    getTreeNodeInfo() {
      return this.isDrawer ? this.selectTreeNodeInfo : this.currSelectTreeNode;
    },
    reset() {
      this.form.resetFields();
      const tempArr = ["entity", "icp"];
      const { formItem } = this;
      for (let i = 0, LEN = formItem.length; i < LEN; i++) {
        const { name } = formItem[i];
        if (!tempArr.join()) break;
        const indexes = tempArr.indexOf(name);
        if (~indexes) {
          tempArr.splice(indexes, 1);
          this.$set(this.formItem[i].props.dimInfo, "selectedItem", []);
        }
      }
      this.form.setFieldsValue({
        entity: "",
        icp: "",
        reconciliationType: 0
      });
    },
    closeLoad() {
      this.controlMainLoading(false);
      this.generateReportBtnLoading = false;
    }
  }
};
</script>
<style lang="less" scoped>
div.queryRangeDrawer {
  width: 23.875rem;
}
.queryRange {
  width: 28.125rem;
  padding: @yn-padding-xl @yn-padding-xxxxl @rem8 @yn-padding-xxxxl;
  .openConfig {
    position: absolute;
    right: @yn-margin-xxxl;
    cursor: pointer;
  }
  /deep/.queryRangeItem,
  .decimal {
    width: 100%;
    background-color: @yn-body-background;
  }
  .organization-item {
    margin-bottom: @rem16;
  }
  .footer /deep/.ant-form-item-children {
    .generateReport {
      margin-right: @yn-margin-xl;
    }
  }
  .footer-btn-cont {
    // position: absolute;
    position: fixed;
    right: 0;
    bottom: 0;
    // width: 100%;
    width: 27.25rem;
    border-top: 1px solid @yn-border-color-base;
    padding: 10px @yn-padding-xl;
    background: @yn-body-background;
    text-align: right;
    margin-bottom: 0;
    z-index: 10;
    /deep/.ant-col-4 label {
      display: none;
    }
    .generateReport {
      margin-right: @yn-margin-xl;
    }
  }
}
</style>
