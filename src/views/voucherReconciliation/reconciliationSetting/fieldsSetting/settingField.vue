<template>
  <div class="field-setting">
    <header class="field-title">
      <yn-divider type="vertical" class="sign" />
      {{ title }}
    </header>
    <div class="source">
      <label class="source-label">数据源表</label>
      <yn-input v-if="edit" v-model="formValue" class="source-select" />
      <span v-else class="source-text">{{ formValue || "-" }}</span>
    </div>
    <div v-if="edit" class="field-tip">{{ CONSTANT.fieldTip }}</div>
    <yn-table
      :columns="columns"
      :dataSource="dataSource"
      :rowKey="COLUMNS_FIELD.key"
      class="field-table"
    >
      <div
        v-for="col in [COLUMNS_FIELD.source, COLUMNS_FIELD.name]"
        :slot="'table.' + col"
        :key="col"
        slot-scope="text, record"
        class="field-col"
      >
        <yn-input
          v-if="edit"
          :value="record[col]"
          @change="e => handleChange(e.target.value, record, col)"
        />
        <span v-else>{{ record[col] }}</span>
      </div>
      <template slot="table.operation" slot-scope="text, record, index">
        <yn-button
          class="yn-a-link action-btn"
          href="javascript:;"
          type="text"
          @click="deleteRow(record, index)"
        >
          删除
        </yn-button>
      </template>
    </yn-table>
    <div v-if="edit" class="new-add-btn-cont">
      <yn-button type="dashed" :disabled="isDisabled" @click="addRow">
        <span class="custom-add">+</span> 添加
      </yn-button>
    </div>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select-option/";
import "yn-p1/libs/components/yn-table/";
import CONSTANT, { columnsField as COLUMNS_FIELD } from "./constants";
export default {
  props: {
    edit: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ""
    },
    form: {
      type: String,
      default: ""
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      CONSTANT,
      COLUMNS_FIELD,
      dataColumns: [
        {
          title: "数据源表字段",
          dataIndex: COLUMNS_FIELD.source,
          scopedSlots: {
            customRender: COLUMNS_FIELD.source
          }
        },
        {
          title: "显示名称",
          dataIndex: COLUMNS_FIELD.name,
          scopedSlots: {
            customRender: COLUMNS_FIELD.name
          }
        }
      ],
      actionColumns: [
        {
          title: "操作",
          dataIndex: "operation",
          width: "5rem",
          scopedSlots: {
            customRender: "operation"
          }
        }
      ]
    };
  },
  computed: {
    columns() {
      return this.edit
        ? [...this.dataColumns, ...this.actionColumns]
        : [...this.dataColumns];
    },
    isDisabled() {
      if (this.dataSource.length === 0) return false;
      return !this.dataSource.every(
        item => item[COLUMNS_FIELD.source] && item[COLUMNS_FIELD.name]
      );
    },
    formValue: {
      get() {
        return this.form;
      },
      set(value) {
        this.$emit("update:form", value);
      }
    },

    dataSource: {
      get() {
        return [...this.data];
      },
      set(value) {
        this.$emit("update:data", [...value]);
      }
    }
  },
  watch: {
    edit: {
      handler(value) {
        if (value) {
          const empty = {
            [COLUMNS_FIELD.source]: "",
            [COLUMNS_FIELD.name]: ""
          };
          if (this.dataSource.length === 0) {
            this.$emit("update:data", [empty]);
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    handleChange(value, record, col) {
      this.$set(record, col, value);
    },
    deleteRow(record, index) {
      this.dataSource = this.dataSource.filter((data, i) => i !== index);
    },
    addRow() {
      this.dataSource = [
        ...this.dataSource,
        {
          [COLUMNS_FIELD.source]: "",
          [COLUMNS_FIELD.name]: ""
        }
      ];
    }
  }
};
</script>
<style lang="less" scoped>
.field-setting {
  margin-bottom: 2rem;
}
.field-title {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.375rem;
  letter-spacing: 0px;
  color: @yn-text-color;
  margin-bottom: 0.75rem;
  .sign {
    width: 2.5px;
    background: @yn-primary-color;
    margin-left: 0;
    margin-right: 0.375rem;
    border-radius: 0.625rem;
  }
}
.source {
  display: flex;
  align-items: center;
  width: 21.25rem;
  margin-bottom: 1.5rem;
  &-label {
    width: 3.5rem;
    margin-right: 0.5rem;
  }
  &-select {
    flex: 1;
  }
  &-text {
    height: 1.375rem;
    font-size: 0.875rem;
    font-weight: normal;
    line-height: 1.375rem;
    letter-spacing: 0px;
    color: @yn-text-color;
  }
}
.field-tip {
  height: 1.25rem;
  font-size: 0.75rem;
  font-weight: normal;
  line-height: 1.25rem;
  letter-spacing: 0px;
  margin-bottom: 0.5rem;
  color: @yn-label-color;
}
.field-table {
  margin-right: 3.5rem;
  /deep/ .ant-table-tbody > tr > td {
    height: 2.25rem;
    padding-top: 0;
    padding-bottom: 0;
  }
}
.field-col {
  display: flex;
  align-items: center;
  height: 2.25rem;
}
.action-btn {
  height: 0.875rem;
  line-height: 0.875rem;
  padding: 0.125rem;
}
.new-add-btn-cont {
  margin-right: 3.5rem;
  margin-top: 0.75rem;
  & > button {
    width: 100%;
    &[disabled] {
      background: @yn-disabled-bg-color !important;
    }
  }
}
</style>
