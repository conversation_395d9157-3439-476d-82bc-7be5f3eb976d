<template>
  <yn-spin :spinning="spinning" size="large" class="spinning">
    <div class="data-area">
      <setting-field
        :title="CONSTANT.detailField"
        :data.sync="detail"
        :edit="edit"
        :form.sync="detailForm"
      />
      <div class="footer">
        <yn-button
          v-if="!edit"
          class="menu-btn mr8"
          type="primary"
          @click="() => (edit = true)"
        >
          编辑
        </yn-button>
        <yn-button
          v-if="edit"
          class="menu-btn mr8"
          type="primary"
          @click="submit"
        >
          保存
        </yn-button>
        <yn-button v-if="edit" class="menu-btn" @click="cancel">
          取消
        </yn-button>
      </div>
    </div>
    <div v-if="edit" class="help-area">
      <div class="help-title">
        <yn-icon-svg type="view-log" class="help-icon" /> 场景说明
      </div>
      <div class="help-item">
        1、凭证明细字段设置后，显示在 “凭证对账报表” 列上，如下图
      </div>
      <img class="help-img1" src="../../../../image/rule-a.png" alt="" />
      <div class="help-item">
        2、凭证明细字段设置后，作为 “对账规则设置” 中条件，如下图
      </div>
      <img class="help-img2" src="../../../../image/rule-b.png" alt="" />
    </div>
  </yn-spin>
</template>
<script>
import settingField from "./settingField.vue";
import CONSTANT, { columnsField as COLUMNS_FIELD } from "./constants";
import voucherReconciliationService from "@/services/voucherReconciliation";
import UiUtils from "yn-p1/libs/utils/UiUtils";
export default {
  name: "FieldsSetting",
  components: { settingField },
  data() {
    return {
      CONSTANT,
      spinning: false,
      edit: false,
      detailForm: "",
      detail: []
    };
  },
  created() {
    this.getData();
  },
  methods: {
    async getDetail() {
      const {
        data: { data }
      } = await voucherReconciliationService("queryField");
      this.detailForm = data ? data.tableName : "";
      this.detail = data ? data.fieldItems : [];
      return data;
    },
    checkOk() {
      if (!this.detailForm) {
        UiUtils.errorMessage("数据源表不能为空");
        return false;
      }
      if (this.detail.length === 0) {
        return true;
      }
      const hasEmpty = !this.detail.every(
        item => item[COLUMNS_FIELD.source] && item[COLUMNS_FIELD.name]
      );
      if (hasEmpty) {
        UiUtils.errorMessage("配置字段不能为空值");
      }
      return !hasEmpty;
    },
    async submit() {
      if (!this.checkOk()) return false;
      this.spinning = true;
      voucherReconciliationService("saveField", {
        fieldItems: this.detail,
        tableName: this.detailForm
      })
        .then(res => {
          UiUtils.successMessage("保存成功");
          this.getData();
          this.edit = false;
        })
        .catch(() => {})
        .finally(() => {
          this.spinning = false;
        });
    },
    async cancel() {
      this.getData();
      this.edit = false;
    },
    async getData() {
      this.spinning = true;
      await this.getDetail();
      this.spinning = false;
    }
  }
};
</script>

<style lang="less" scoped>
.spinning {
  padding: 1rem;
  margin-top: 0.375rem;
  background: @yn-component-background;
  ::v-deep > .ant-spin-container {
    display: flex;
    flex-direction: row;
    width: calc(100% + 1.75rem);
    overflow: scroll;
  }
}
.data-area {
  flex-basis: 898px;
  overflow: auto;
  width: 0;
}
.help-area {
  width: 0;
  flex-basis: 778px;
  background: @yn-background-color;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 1.5rem 0px 1.5rem;
  gap: 1rem;
  flex-grow: 1;
  align-self: stretch;
  margin-right: 1rem;
  .help-icon {
    color: @yn-disabled-color;
  }
  .help-title {
    width: 28.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.375rem;
    letter-spacing: 0px;
    text-align: left;
    color: @yn-text-color;
  }
  .help-item {
    width: 28.75rem;
    font-size: 0.75rem;
    font-weight: normal;
    line-height: 1.25rem;
    text-align: left;
    letter-spacing: 0px;
    color: @yn-text-color;
  }
  .help-img1 {
    width: 28.75rem;
  }
  .help-img2 {
    width: 28.75rem;
  }
}
.mr8 {
  margin-right: @rem8;
}
</style>
