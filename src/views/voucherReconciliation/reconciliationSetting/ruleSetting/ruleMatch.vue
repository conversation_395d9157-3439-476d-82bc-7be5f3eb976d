<template>
  <div class="rule-match">
    <div class="rule-group">
      <div class="rule-wrap">
        <div class="rule-bar">
          <span class="line"></span>
          <span v-if="data.length > 1" class="btns" @click="handlerMatchType">
            <span
              data-type="and"
              :class="{ and: true, btn: true, active: type === 'and' }"
            >
              且
            </span>
            <span
              data-type="or"
              :class="{ or: true, btn: true, active: type === 'or' }"
            >
              或
            </span>
          </span>
        </div>
        <div class="rule-data">
          <yn-form
            class="form"
            :form="form"
            layout="horizontal"
            :labelCol="formLayout.labelCol"
            :wrapperCol="formLayout.wrapperCol"
          >
            <div
              v-for="item in data"
              :key="item[RULES_FIELD.id]"
              class="rule-fields"
            >
              <div class="rule-item rule-field">
                <yn-form-item label="" :colon="false">
                  <yn-select
                    v-decorator="[
                      item[RULES_FIELD.id] + '_' + RULES_FIELD.field,
                      {
                        rules: [{ required: true, message: '请选择' }]
                      }
                    ]"
                    :disabled="disabled"
                    :allowClear="false"
                    :options="fields"
                    @change="handlerField($event, item)"
                  />
                </yn-form-item>
              </div>
              <div
                :class="[
                  'rule-item',
                  'rule-type',
                  {
                    hidden:
                      !item[RULES_FIELD.field] ||
                      !defaultField.includes(item[RULES_FIELD.field])
                  }
                ]"
              >
                <yn-form-item label="" :colon="false">
                  <yn-select
                    v-decorator="[
                      item[RULES_FIELD.id] + '_' + RULES_FIELD.type,
                      {
                        rules: [
                          {
                            required: defaultField.includes(
                              item[RULES_FIELD.field]
                            ),
                            message: '请选择'
                          }
                        ]
                      }
                    ]"
                    :disabled="disabled"
                    :allowClear="false"
                    :options="types"
                    @change="handlerType($event, item)"
                  />
                </yn-form-item>
              </div>
              <div
                :class="[
                  'rule-item',
                  'rule-match',
                  {
                    hidden:
                      typeof item[RULES_FIELD.type] !== 'number' ||
                      !defaultField.includes(item[RULES_FIELD.field])
                  }
                ]"
              >
                <yn-form-item label="" :colon="false">
                  <yn-select
                    v-decorator="[
                      item[RULES_FIELD.id] + '_' + RULES_FIELD.match,
                      {
                        rules: [
                          {
                            required: defaultField.includes(
                              item[RULES_FIELD.field]
                            ),
                            message: '请选择'
                          }
                        ]
                      }
                    ]"
                    :allowClear="false"
                    :disabled="disabled"
                    :options="match"
                    @change="handlerMatch($event, item)"
                  />
                </yn-form-item>
              </div>
              <div
                :class="[
                  'rule-item',
                  'rule-value',
                  {
                    hidden:
                      typeof item[RULES_FIELD.match] !== 'number' ||
                      !defaultField.includes(item[RULES_FIELD.field])
                  }
                ]"
              >
                <yn-form-item
                  v-if="item[RULES_FIELD.match] === MATCH_ENUM.async"
                  label=""
                  :colon="false"
                  class="value-type"
                >
                  <yn-select
                    v-decorator="[
                      item[RULES_FIELD.id] + '_' + RULES_FIELD.valueType,
                      {
                        rules: [
                          {
                            required: defaultField.includes(
                              item[RULES_FIELD.field]
                            ),
                            message: '请选择'
                          }
                        ]
                      }
                    ]"
                    :allowClear="false"
                    :options="valueTypes"
                    :disabled="disabled"
                    @change="handlerValueType($event, item)"
                  />
                </yn-form-item>
                <yn-form-item
                  label=""
                  :colon="false"
                  :class="[
                    'value',
                    {
                      hidden:
                        (item[RULES_FIELD.match] === MATCH_ENUM.async &&
                        typeof item[RULES_FIELD.valueType] !== 'number') ||
                        !defaultField.includes(item[RULES_FIELD.field])
                    }
                  ]"
                >
                  <yn-input
                    v-decorator="[
                      item[RULES_FIELD.id] + '_showValue',
                      {
                        rules: [
                          {
                            required:
                              item[RULES_FIELD.match] !== MATCH_ENUM.async &&
                              defaultField.includes(item[RULES_FIELD.field]),
                            message:
                              item[RULES_FIELD.match] !== MATCH_ENUM.async
                                ? '请输入固定税差'
                                : '请输入容差值'
                          },
                          {
                            validator: (rule, value, callback) =>
                              validateNumber(rule, value, callback, item)
                          }
                        ]
                      }
                    ]"
                    :allowClear="false"
                    :disabled="disabled"
                    @change="handlerChange($event, item)"
                    @focus="handlerFocus(item)"
                    @blur="handlerBlur($event, item)"
                  >
                    <span
                      v-if="
                        item[RULES_FIELD.match] === MATCH_ENUM.fix ||
                          (item[RULES_FIELD.valueType] ===
                            VALUES_TYPES_ENUM.percent &&
                          item[RULES_FIELD.match] === MATCH_ENUM.async)
                      "
                      slot="suffix"
                    >
                      %
                    </span>
                  </yn-input>
                </yn-form-item>
              </div>
              <div class="icon-delete">
                <svg-icon
                  v-if="!disabled && data.length > 1"
                  type="icon-shanchuicon"
                  @click="deleteRule(item)"
                />
              </div>
            </div>
          </yn-form>
        </div>
      </div>
      <div
        v-if="!disabled"
        :class="['rule-action', { disabled: isDisableSilence() }]"
        @click="addRule"
      >
        <svg-icon class="icon-add" type="icon-cr_add" :isIconBtn="false" />
        添加条件
      </div>
    </div>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import _uniqueId from "lodash/uniqueId";
import { toFinance } from "@/utils/filters";
import voucherReconciliationService from "@/services/voucherReconciliation";
import {
  TYPES_OPTIONS,
  MATCH_OPTIONS,
  VALUES_TYPES_OPTIONS,
  VALUES_TYPES_ENUM,
  MATCH_ENUM,
  RULES_FIELD,
  TYPES_ENUM
} from "./constants";
import { columnsField } from "../fieldsSetting/constants";
export default {
  model: {
    prop: "value",
    event: "change"
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      data: [],
      fields: [{ label: 1, value: 1 }],
      RULES_FIELD,
      VALUES_TYPES_ENUM,
      MATCH_ENUM,
      TYPES_ENUM,
      inputIng: false,
      defaultField: [],
      types: TYPES_OPTIONS,
      match: MATCH_OPTIONS,
      valueTypes: VALUES_TYPES_OPTIONS,
      form: this.$form.createForm(this, "form"),
      formLayout: {
        labelCol: { span: 0 },
        wrapperCol: { span: 24 }
      }
    };
  },
  computed: {
    type: {
      get() {
        return this.value.type;
      },
      set(value) {
        this.$emit("change", {
          type: value,
          data: [...this.data]
        });
      }
    }
  },
  watch: {
    value: {
      handler() {
        if (this.value.data.length === 0) {
          this.data = [this.getEmpty()];
          this.handlerSync();
          return;
        }
        this.data = this.value.data.map(item => {
          const tmp = { ...this.getEmpty(), ...item };
          const len = this.getDecLen(item[RULES_FIELD.value]);
          const value =
            typeof item[RULES_FIELD.value] !== undefined
              ? toFinance(item[RULES_FIELD.value], len)
              : 0;
          tmp.showValue = value;
          // 设置form值, 不用初始化字段设置值是因为无法同步到form
          const formData = Object.keys(tmp).reduce((pre, next) => {
            pre[tmp[RULES_FIELD.id] + "_" + next] = tmp[next];
            return pre;
          }, {});
          this.$nextTick(() => {
            this.form.setFieldsValue(formData);
          });
          return tmp;
        });
      },
      immediate: true,
      deep: true
    }
  },
  async created() {
    await this.requestField();
  },
  methods: {
    deleteRule(rule) {
      this.data = this.data.filter(
        item => item[RULES_FIELD.id] !== rule[RULES_FIELD.id]
      );
      this.handlerSync();
    },
    async requestField() {
      const {
        data: { data }
      } = await voucherReconciliationService("fieldList");
      this.defaultField = [];
      this.fields = (data || []).map(item => {
        item.defaultField && this.defaultField.push(item[columnsField.key]);
        return {
          label: item[columnsField.name],
          value: item[columnsField.key]
        };
      });
    },
    handlerMatchType(e) {
      this.type = e.target.dataset.type;
    },
    isDisableSilence() {
      return this.data.some(item => {
        const isDefaultField = this.defaultField.includes(
          item[RULES_FIELD.field]
        );

        if (isDefaultField) {
          const { isAsync } = this.checkSign(item);
          const hasValueType = typeof item[RULES_FIELD.valueType] === "number";
          const mustValue =
            typeof item[RULES_FIELD.type] === "number" &&
            typeof item[RULES_FIELD.match] === "number";
          const status = this.validateValue(item[RULES_FIELD.value], item);
          if (isAsync) {
            return !(mustValue && status && hasValueType);
          } else {
            return !(mustValue && status);
          }
        } else {
          return !item[RULES_FIELD.field];
        }
      });
    },
    isDisabled() {
      let hasError = true;
      this.form.validateFields((err, values) => {
        if (err) {
          hasError = true;
        } else {
          hasError = false;
        }
      });
      return hasError;
    },
    addRule() {
      if (this.isDisableSilence()) return;
      this.data = [...this.data, this.getEmpty()];
      this.handlerSync();
    },
    getEmpty() {
      return {
        [RULES_FIELD.id]: _uniqueId("custom"),
        [RULES_FIELD.field]: "1",
        [RULES_FIELD.type]: 0,
        [RULES_FIELD.match]: 0,
        [RULES_FIELD.valueType]: 0,
        [RULES_FIELD.value]: ""
      };
    },
    handlerField($event, item) {
      this.$set(item, RULES_FIELD.field, $event);
      this.handlerSync();
    },
    handlerType($event, item) {
      this.$set(item, RULES_FIELD.type, $event);
      this.handlerSync();
    },
    handlerMatch($event, item) {
      this.$set(item, RULES_FIELD.match, $event);
      this.handlerReset(item);
    },
    handlerValueType($event, item) {
      this.$set(item, RULES_FIELD.valueType, $event);
      this.handlerReset(item);
    },
    handlerFocus(item) {
      this.inputIng = true;
      this.$set(item, "showValue", item[RULES_FIELD.value]);
      this.form.setFieldsValue({
        [item[RULES_FIELD.id] + "_showValue"]: item[RULES_FIELD.value]
      });
    },
    handlerSync() {
      this.$emit("change", {
        type: this.type,
        data: [...this.data]
      });
      setTimeout(() => {
        this.form.validateFields();
      }, 0);
    },
    handlerBlur(e, item) {
      this.inputIng = false;
      const status = this.validateValue(e.target.value, item);
      item[RULES_FIELD.value] = e.target.value.replace(/\.$/, "");
      if (item[RULES_FIELD.value] && status) {
        const len = this.getDecLen(e.target.value);
        const value = toFinance(item[RULES_FIELD.value], len);
        this.$set(item, "showValue", value);
      } else {
        this.$set(item, "showValue", item[RULES_FIELD.value]);
      }
      this.form.setFieldsValue({
        [item[RULES_FIELD.id] + "_showValue"]: item.showValue
      });
      this.handlerSync();
    },
    handlerReset(item) {
      item[RULES_FIELD.value] = "";
      this.$set(item, "showValue", "");
      this.$nextTick(() => {
        this.form.resetFields([item[RULES_FIELD.id] + "_showValue"]);
      });
      this.handlerSync();
    },
    getDecLen(value) {
      return value
        ? value.match(/\.(\d+)/)
          ? Math.min(value.match(/\.(\d+)/)[1].length, 9)
          : 2
        : 2;
    },
    checkSign(item) {
      // 固定差
      const isFix = item[RULES_FIELD.match] === MATCH_ENUM.fix;
      // 容差
      const isAsync = item[RULES_FIELD.match] === MATCH_ENUM.async;
      // 容差+百分比
      const isAsyncPercent =
        isAsync && item[RULES_FIELD.valueType] === VALUES_TYPES_ENUM.percent;
      // 容差+数值
      const isAsyncNum =
        isAsync && item[RULES_FIELD.valueType] === VALUES_TYPES_ENUM.num;
      return { isFix, isAsync, isAsyncPercent, isAsyncNum };
    },
    validateNumber(rule, value, callback, item) {
      const relValue = this.inputIng ? value : item[RULES_FIELD.value];
      if (relValue && !this.validateValue(relValue, item)) {
        callback("输入格式有误");
        return;
      }
      callback();
    },
    validateValue(value, item) {
      const { isFix, isAsync, isAsyncPercent, isAsyncNum } = this.checkSign(
        item
      );
      const reg = isAsyncNum
        ? /^-?([1-9]\d*|\d+\.\d{0,9}|0)$/
        : /^([1-9]\d*|^\d+\.\d{0,9}|0)$/;
      // 固定，空值
      if (!value && isFix) {
        return false;
      }
      // 容差数值或百分比，空值
      if (!value && isAsync) return true;
      // 数值类型，固定差或容差百分比都大于100
      if (
        reg.test(value) &&
        (isAsyncPercent || isFix) &&
        parseFloat(value) > 100
      ) {
        return false;
      }
      // 非数值，容差数值
      if (!reg.test(value)) {
        return false;
      }

      return true;
    },
    handlerChange(e, item) {}
  }
};
</script>

<style lang="less" scoped>
.rule-wrap {
  display: flex;
  .rule-bar {
    width: 1.25rem;
    flex-shrink: 0;
    position: relative;
    margin-bottom: 1.5rem;
    margin-right: 0.5rem;
    .line {
      position: absolute;
      display: inline-block;
      margin: auto;
      left: 50%;
      width: 1px;
      height: 100%;
      background: #ccc;
    }
    .btns {
      display: flex;
      flex-direction: column;
      width: 1.25rem;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      .btn {
        display: inline-block;
        width: 1.25rem;
        height: 1.25rem;
        background: @yn-table-header-bg;
        font-size: 0.75rem;
        font-weight: normal;
        line-height: 1.25rem;
        text-align: center;
        cursor: pointer;

        /* 中性色/次文本 */
        color: @yn-text-color-secondary;
        &.and {
          border-radius: 2px 2px 0px 0px;
        }
        &.or {
          border-radius: 0px 0px 2px 2px;
        }
        &.active {
          color: @yn-body-background;
          background: @yn-primary-color;
        }
      }
    }
  }
}
.rule-data {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.rule-fields {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  flex: 1;
  .rule-item {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    margin: 0 0.3125rem;
    flex-grow: 1;
    visibility: visible;
    &.hidden {
      visibility: hidden;
    }
  }
  .rule-value {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    flex-basis: 180px;
    .value-type {
      flex-basis: 80px;
      flex-shrink: 0;
    }
    .value {
      flex-basis: 100px;
      flex: 1;
      &.hidden {
        visibility: hidden;
      }
    }
  }
  .rule-field {
    flex-basis: 120px;
  }
  .rule-type {
    flex-basis: 100px;
  }
  .rule-match {
    flex-basis: 100px;
  }
  .icon-delete {
    width: 1.875rem;
    height: 1.875rem;
    margin-bottom: 1.5rem;
  }
}

.rule-action {
  display: inline-flex;
  align-items: center;
  font-size: 0.875rem;
  color: @yn-primary-color;
  cursor: pointer;
  margin-left: 1.75rem;
  .icon-add {
    font-size: 0.875rem;
  }
  &.disabled {
    color: @yn-disabled-color;
    cursor: not-allowed;
  }
}
.form {
  /deep/ .ant-form-item {
    width: 100%;
  }
}
</style>
