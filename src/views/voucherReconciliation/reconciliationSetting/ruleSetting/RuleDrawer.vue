<template>
  <yn-drawer
    v-if="visible"
    :title="title"
    width="45rem"
    placement="right"
    :closable="true"
    :visible="visible"
    :maskClosable="false"
    :bodyStyle="{ position: 'relative' }"
    @close="onClose"
  >
    <yn-spin size="large" :spinning="spinning" class="spinning">
      <yn-form
        class="form"
        :form="form"
        layout="horizontal"
        :labelCol="formLayout.labelCol"
        :wrapperCol="formLayout.wrapperCol"
      >
        <div class="form-block">
          <header class="field-title">
            <yn-divider type="vertical" class="sign" />
            基础信息
          </header>
          <yn-form-item label="规则名称" :colon="false">
            <yn-input
              v-decorator="[
                RULE_INFO_FIELD_NAME.ruleName,
                {
                  initialValue: '',
                  rules: [
                    { required: true, message: '请输入' },
                    {
                      validator: validateLength
                    }
                  ]
                }
              ]"
              :disabled="operationType === OPERATION_ENUM.view"
            />
          </yn-form-item>
          <yn-form-item label="规则说明" :colon="false">
            <yn-textarea
              v-decorator="[
                RULE_INFO_FIELD_NAME.des,
                {
                  initialValue: '',
                  rules: [{ required: false, message: '请输入' }]
                }
              ]"
              showCount
              :maxLength="200"
              :disabled="operationType === OPERATION_ENUM.view"
              :autoSize="{ minRows: 2, maxRows: 6 }"
            />
          </yn-form-item>
        </div>
        <div class="form-block">
          <header class="field-title">
            <yn-divider type="vertical" class="sign" />
            规则匹配
          </header>
          <div class="des">
            通过选择条件，设置条件之间的“且”和“或”的关系，设置配置规则
          </div>
          <yn-form-item
            label=""
            :colon="false"
            :labelCol="{ span: 0 }"
            :wrapperCol="{ span: 24 }"
          >
            <rule-match
              ref="ruleMatch"
              v-decorator="[
                RULE_INFO_FIELD_NAME.match,
                {
                  initialValue: rules,
                  rules: [{ required: true, message: '请输入' }]
                }
              ]"
              :disabled="operationType === OPERATION_ENUM.view"
            />
          </yn-form-item>
        </div>
      </yn-form>
      <div v-if="operationType !== OPERATION_ENUM.view" class="footerCont">
        <yn-button @click="onClose">
          取消
        </yn-button>
        <yn-button
          type="primary"
          class="okBtn"
          :loading="btnLoading"
          @click="handleOk"
        >
          确定
        </yn-button>
      </div>
    </yn-spin>
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-textarea/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-switch/";
import "yn-p1/libs/components/yn-textarea/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-button/";
import ruleMatch from "./ruleMatch.vue";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import voucherReconciliationService from "@/services/voucherReconciliation";
// import UiUtils from "yn-p1/libs/utils/UiUtils";
import {
  OPERATION_TYPE,
  OPERATION_ENUM,
  MODULE_NAME,
  RULE_INFO_FIELD_NAME,
  RULE_INFO_FIELD_NAME_LABEL
} from "./constants";
export default {
  name: "RuleDrawer",
  components: { ruleMatch },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    ruleId: {
      type: String,
      default: ""
    },
    operationType: {
      type: String,
      default: "0"
    }
  },
  data() {
    return {
      OPERATION_ENUM,
      RULE_INFO_FIELD_NAME,
      RULE_INFO_FIELD_NAME_LABEL,
      rules: {
        type: "and",
        data: []
      },
      form: this.$form.createForm(this, "form"),
      formLayout: {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 }
      },
      spinning: true,
      btnLoading: false
    };
  },
  computed: {
    title() {
      return `${OPERATION_TYPE[this.operationType]}${MODULE_NAME}`;
    }
  },
  watch: {
    visible(value) {
      value && this.init();
    }
  },
  methods: {
    async init() {
      this.spinning = true;
      const data = await this.requestRuleInfo();
      this.setDefaultVal(data);
      this.spinning = false;
    },
    async requestRuleInfo() {
      // 新增操作 不用查规则信息
      if (this.operationType === OPERATION_ENUM.add) {
        return {};
      }
      // 根据ruleId 获取规则信息
      const {
        data: { data }
      } = await voucherReconciliationService("queryRule", this.ruleId);
      return data || {};
    },
    validateLength(rule, value, callback, item) {
      if (value && value.length > 64) {
        callback("字符长度超过64位限制，请检查");
        return;
      }
      callback();
    },
    setDefaultVal(info) {
      this.form.setFieldsValue({
        [RULE_INFO_FIELD_NAME.ruleName]: info.ruleName,
        [RULE_INFO_FIELD_NAME.des]: info.ruleDesc,
        [RULE_INFO_FIELD_NAME.match]: {
          type: info.matchType || "and",
          data: info.ruleMatchItems || [{}]
        }
      });
    },
    onClose() {
      this.$emit("update:visible", false);
    },
    async handleOk() {
      this.btnLoading = true;
      const that = this;
      this.form.validateFields((err, values) => {
        that.$refs.ruleMatch.form.validateFields((ruleErr, ruleValues) => {
          if (err || ruleErr) {
            this.btnLoading = false;
            return;
          }
          voucherReconciliationService("saveRule", {
            ...values,
            [RULE_INFO_FIELD_NAME.match]:
              values[RULE_INFO_FIELD_NAME.match].type,
            [RULE_INFO_FIELD_NAME.ruleMatchItems]:
              values[RULE_INFO_FIELD_NAME.match].data,
            ruleId: this.ruleId
          })
            .then(res => {
              UiUtils.successMessage(
                `${OPERATION_TYPE[this.operationType]}${MODULE_NAME}成功`
              );
              this.$emit("ok");
              this.onClose();
            })
            .finally(() => {
              this.btnLoading = false;
            });
        });
      });
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .ant-drawer-body {
  padding: 1rem 1.5rem;
  padding-bottom: 3.125rem;
}
.spinning {
  height: auto;
}
.footerCont {
  width: 45rem;
  position: fixed;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: end;
  align-items: center;
  background: @yn-component-background;
  border-top: 1px solid @yn-border-color-base;
  padding: 0.625rem 1rem;
  .okBtn {
    margin-left: @yn-margin-s;
  }
}
.form-block {
  margin-bottom: 2.5rem;
  .des {
    font-size: 0.75rem;
    font-weight: normal;
    line-height: 1.25rem;
    letter-spacing: 0px;
    color: @yn-label-color;
    margin-bottom: 0.75rem;
  }
}
.field-title {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.375rem;
  letter-spacing: 0px;
  color: @yn-text-color;
  margin-bottom: 0.75rem;
  .sign {
    width: 2.5px;
    background: @yn-primary-color;
    margin-left: 0;
    margin-right: 0.375rem;
    border-radius: 0.625rem;
  }
}
</style>
