export const OPERATION_TYPE = Object.freeze({
  0: "查看",
  1: "编辑",
  2: "新增"
});

export const OPERATION_ENUM = Object.freeze({
  view: "0",
  edit: "1",
  add: "2"
});

export const MODULE_NAME = "对账规则";

export const RULE_INFO_FIELD_NAME = Object.freeze({
  ruleId: "objectId",
  ruleName: "ruleName",
  creator: "createBy",
  creationTime: "createDate",
  modifier: "updateBy",
  modificationTime: "updateDate",
  des: "ruleDesc",
  match: "matchType",
  ruleMatchItems: "ruleMatchItems"
});
export const RULE_INFO_FIELD_NAME_LABEL = Object.freeze({
  ruleName: "规则名称",
  creator: "创建人",
  creationTime: "创建时间",
  modifier: "修改人",
  modificationTime: "修改时间"
});

export const TYPES_OPTIONS = [
  {
    label: "一对一",
    value: 0
  },
  // {
  //   label: "一对多",
  //   value: 1
  // },
  {
    label: "多对多",
    value: 2
  }
];
export const TYPES_ENUM = {
  ONE: 0,
  OTM: 1,
  MTM: 2
};
export const MATCH_OPTIONS = [
  {
    label: "容差范围",
    value: 0
  },
  {
    label: "固定税差",
    value: 1
  }
];

export const MATCH_ENUM = {
  async: 0,
  fix: 1
};
export const VALUES_TYPES_OPTIONS = [
  {
    label: "数值",
    value: 0
  },
  {
    label: "百分比",
    value: 1
  }
];
export const VALUES_TYPES_ENUM = {
  num: 0,
  percent: 1
};

export const RULES_FIELD = Object.freeze({
  id: "objectId",
  field: "matchField",
  type: "ruleType",
  match: "differenceType",
  valueType: "dataType",
  value: "matchValue"
});
