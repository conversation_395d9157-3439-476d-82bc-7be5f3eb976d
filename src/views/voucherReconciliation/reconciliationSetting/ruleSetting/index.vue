<template>
  <yn-spin :spinning="spinning" size="large" class="spinning">
    <yn-empty v-if="empty">
      <span slot="description">当前对账规则设置为空，马上开始新增</span>
      <yn-button type="primary" @click="handlerDrawer('', OPERATION_ENUM.add)">
        新增对账规则设置
      </yn-button>
    </yn-empty>
    <yn-page-list
      v-else
      ref="pageList"
      class="rule"
      :tableConfig="tableConfig"
      :toolsConfig="toolsConfig"
      @table_rowSelectChange="onSelectChange"
      @table_change="handleSearch"
    >
      <template v-slot:[`tools.title`]>
        <template v-if="isBatchDelete">
          <span class="choose-item">
            已选 {{ selectedRowKeysAll.length }} 条
          </span>
          <yn-button
            class="delete-confirm"
            type="primary"
            :disabled="!selectedRowKeysAll.length"
            @click="batchDeleteRow"
          >
            删除
          </yn-button>
          <yn-button @click="handleCancelDel">
            取消
          </yn-button>
        </template>
        <template v-else>
          <yn-input-search
            v-model="searchWord"
            class="head-search"
            placeholder="请输入规则名称"
            @change="() => (searching = true)"
            @search="onSearch"
          />
        </template>
      </template>
      <template v-if="!isBatchDelete" v-slot:[`tools.btns`]>
        <yn-button
          type="primary"
          @click="handlerDrawer('', OPERATION_ENUM.add)"
        >
          新增
        </yn-button>
        <yn-button class="btn-delete" @click="() => (isBatchDelete = true)">
          删除
        </yn-button>
        <yn-divider class="head-divider" type="vertical" />
        <svg-icon title="刷新" type="icon-shuaxin" @click="requestTableData" />
      </template>
      <template v-if="!isBatchDelete" v-slot:[`table.operation`]="record">
        <div class="table-operation">
          <a
            href="javascript:;"
            class="yn-a-link table-operate"
            @click.stop="
              handlerDrawer(
                record[RULE_INFO_FIELD_NAME.ruleId],
                OPERATION_ENUM.edit
              )
            "
          >
            编辑
          </a>
          <yn-popconfirm
            title="你要删除当前规则吗？"
            placement="bottomRight"
            okText="删除"
            cancelText="取消"
            @confirm="confirmDelete(record)"
          >
            <a
              class="a-delete yn-a-link table-operate"
              href="javascript:;"
              @click.stop
            >
              删除
            </a>
          </yn-popconfirm>
          <svg-icon class="cell-operation-btns" type="icon-c1_cr_form_enter" />
        </div>
      </template>
    </yn-page-list>
    <RuleDrawer
      :visible.sync="visible"
      :ruleId="ruleId"
      :operationType="operationType"
      @ok="requestTableData"
    />
  </yn-spin>
</template>
<script>
import "yn-p1/libs/components/yn-page-list/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-page-list/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-input-search/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import RuleDrawer from "./RuleDrawer.vue";
import voucherReconciliationService from "@/services/voucherReconciliation";
import {
  OPERATION_ENUM,
  RULE_INFO_FIELD_NAME,
  RULE_INFO_FIELD_NAME_LABEL
} from "./constants";
export default {
  name: "RuleSetting",
  components: { RuleDrawer },
  data() {
    return {
      visible: false,
      searching: false,
      searchWord: "",
      ruleId: "",
      OPERATION_ENUM,
      operationType: "",
      RULE_INFO_FIELD_NAME,
      toolsConfig: {
        options: [
          [
            {
              slotName: "btns"
            }
          ]
        ]
      },
      tableConfig: {
        rowKey: "objectId",
        pagination: {
          total: 0,
          current: 1,
          pageSize: 10,
          currentPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: total => `总计${total}条`
        },
        columns: [
          {
            title: RULE_INFO_FIELD_NAME_LABEL.ruleName,
            dataIndex: RULE_INFO_FIELD_NAME.ruleName,
            key: RULE_INFO_FIELD_NAME.ruleName
          },
          {
            title: RULE_INFO_FIELD_NAME_LABEL.creator,
            dataIndex: RULE_INFO_FIELD_NAME.creator,
            key: RULE_INFO_FIELD_NAME.creator
          },
          {
            title: RULE_INFO_FIELD_NAME_LABEL.creationTime,
            dataIndex: RULE_INFO_FIELD_NAME.creationTime,
            key: RULE_INFO_FIELD_NAME.creationTime
          },
          {
            title: RULE_INFO_FIELD_NAME_LABEL.modifier,
            dataIndex: RULE_INFO_FIELD_NAME.modifier,
            key: RULE_INFO_FIELD_NAME.modifier
          },
          {
            title: RULE_INFO_FIELD_NAME_LABEL.modificationTime,
            dataIndex: RULE_INFO_FIELD_NAME.modificationTime,
            key: RULE_INFO_FIELD_NAME.modificationTime
          },
          {
            title: "操作",
            key: "operation",
            width: 160,
            scopedSlots: {
              customRender: "operation"
            }
          }
        ],
        dataSource: [],
        customRow: this.handlerCustomRow,
        rowSelection: false
      },
      selectedRowKeysAll: [],
      deleteRuleSetIds: [],
      tableSelectedRowKeyAll: [],
      spinning: true,
      isBatchDelete: false
    };
  },
  computed: {
    empty() {
      return (
        this.tableConfig.pagination.total === 0 &&
        !this.searchWord &&
        !this.searching &&
        !this.spinning
      );
    }
  },
  watch: {
    isBatchDelete: {
      handler(newVal) {
        this.selectedRowKeysAll = [];
        this.tableConfig.rowSelection = newVal
          ? { selectedRowKeys: this.selectedRowKeysAll }
          : false;
      }
    }
  },
  created() {
    this.requestTableData();
  },
  methods: {
    async requestTableData() {
      this.spinning = true;
      const {
        data: { data }
      } = await voucherReconciliationService("ruleListPageable", {
        pageSize: this.tableConfig.pagination.pageSize,
        pageNum: this.tableConfig.pagination.current,
        searchRuleName: this.searchWord
      });
      this.tableConfig.dataSource = data ? data.list : [];
      this.tableConfig.pagination.total = data ? data.total : 0;
      this.spinning = false;
    },
    onSelectChange(selectedRow, selectedRowKeysAll) {
      this.selectedRowKeysAll = [...selectedRowKeysAll];
    },
    async handleSearch(searchInfo) {
      this.tableConfig.pagination.current = searchInfo.pagingInfo.current;
      this.tableConfig.pagination.pageSize = searchInfo.pagingInfo.pageSize;
      await this.requestTableData();
    },
    async onSearch() {
      this.searching = true;
      this.tableConfig.pagination.current = 1;
      await this.requestTableData();
    },
    batchDeleteRow() {
      if (this.selectedRowKeysAll.length === 0) {
        UiUtils.infoMessage("请选择需要删除的项");
        return;
      }
      UiUtils.confirm({
        title: "删除已选对账规则",
        content: "确定要删除当前已选规则吗？",
        okText: "删除",
        cancelText: "取消",
        onOk: async () => {
          this.spinning = true;
          this.deleteRows(this.selectedRowKeysAll);
          this.handleCancelDel();
        }
      });
    },
    async deleteRows(ids) {
      voucherReconciliationService("deleteRule", ids).then(res => {
        this.requestTableData();
        UiUtils.successMessage("删除成功");
      }).catch(() => {
        this.spinning = false;
      });
    },
    handlerCustomRow(record) {
      return {
        on: {
          click: () => {
            if (this.isBatchDelete) return;
            this.handlerDrawer(
              record[RULE_INFO_FIELD_NAME.ruleId],
              OPERATION_ENUM.view
            );
          }
        }
      };
    },
    handleCancelDel() {
      this.isBatchDelete = false;
    },
    handlerDrawer(ruleId, operationType) {
      this.ruleId = ruleId;
      this.operationType = operationType;
      this.visible = true;
    },
    confirmDelete(record) {
      this.spinning = true;
      this.deleteRows([record.objectId]);
    }
  }
};
</script>

<style scoped lang="less">
.spinning > :deep(.ant-spin-container) {
  display: flex;
  align-items: center;
  justify-content: center;
  background: @yn-component-background;
}
.rule {
  padding: 0;
  padding-top: 0.375rem;
  /deep/ .ant-table-tbody > tr > td {
    height: 2.25rem;
    padding-top: 0;
    padding-bottom: 0;
  }
}
.a-delete {
  margin: 0 @yn-margin-l;
}
.delete-confirm {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.btn-delete {
  margin-left: 0.5rem;
}
</style>
