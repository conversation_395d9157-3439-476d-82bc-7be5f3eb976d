<template>
  <yn-tabs
    defaultActiveKey="rule"
    class="voucherReconciliationSetup cs-container"
  >
    <yn-tab-pane
      v-for="tabInfo in tabData"
      :key="tabInfo.key"
      :tab="tabInfo.title"
    >
      <component :is="tabInfo.component" />
    </yn-tab-pane>
  </yn-tabs>
</template>
<script>
import "yn-p1/libs/components/yn-tabs/";
import "yn-p1/libs/components/yn-tab-pane/";
import FieldsSetting from "./fieldsSetting";
import RuleSetting from "./ruleSetting";
export default {
  name: "VoucherReconciliationSetings",
  components: {
    // eslint-disable-next-line vue/no-unused-components
    FieldsSetting,
    // eslint-disable-next-line vue/no-unused-components
    RuleSetting
  },
  data() {
    return {
      tabData: [
        {
          key: "rule",
          title: "对账规则设置",
          component: "RuleSetting"
        },
        {
          key: "fields",
          title: "对账字段设置",
          component: "FieldsSetting"
        }
      ]
    };
  }
};
</script>
<style lang="less" scoped>
.voucherReconciliationSetup {
  height: 100%;
  /deep/ .ant-tabs-top-bar {
    border: none;
  }
  /deep/ .ant-tabs-nav-wrap {
    display: flex;
    align-items: center;
    height: 2.75rem;
    background: transparent;
    box-shadow: none;
    font-weight: 600;
  }
  /deep/.ant-tabs-content {
    height: calc(100% - 2.75rem);
  }
  /deep/.ant-tabs-tabpane-active {
    height: 100%;
  }
  /deep/.ant-spin-blur {
    width: 100%;
  }
}
</style>
