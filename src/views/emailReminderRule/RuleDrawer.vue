<template>
  <yn-drawer
    :title="title"
    width="27.5rem"
    placement="right"
    :closable="true"
    :visible="true"
    :maskClosable="false"
    :bodyStyle="{ position: 'relative' }"
    @close="onClose"
  >
    <yn-spin size="large" :spinning="spinning">
      <yn-form :form="form" layout="horizontal">
        <template v-for="item in formItem">
          <yn-form-item
            v-if="item.componentName !== 'p'"
            :key="item.key"
            :label="item.label"
            :colon="false"
            :labelCol="formLayout.labelCol"
            :wrapperCol="formLayout.wrapperCol"
          >
            <component
              :is="item.componentName"
              v-decorator="[
                `${item.key}`,
                {
                  initialValue: item.value,
                  ...(item.decoratorOptions || {}),
                  rules: [
                    { required: item.require, message: '请输入' },
                    ...(item.rules || [])
                  ]
                }
              ]"
              :disabled="item.disabled || operationType === 3"
              v-bind="item.attrs || {}"
              v-on="item.events"
            />
          </yn-form-item>
          <component
            :is="item.componentName"
            v-else
            :key="item.key"
            :disabled="item.disabled || operationType === 3"
            class="area"
          >
            {{ item.label }}
          </component>
        </template>
      </yn-form>
    </yn-spin>
    <div v-if="operationType !== 3" class="footerCont">
      <yn-button @click="onClose">
        取消
      </yn-button>
      <yn-button
        type="primary"
        class="okBtn"
        :loading="btnLoading"
        @click="handleOk"
      >
        确定
      </yn-button>
    </div>
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-switch/";
import "yn-p1/libs/components/yn-textarea/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-button/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import ShowUserListInput from "@/components/hoc/showUserListInput";
import emailService from "@/services/email";
import { TYPE_MAPPING, MODULE_NAME, RULE_INFO_FIELD_NAME } from "./constant.js";
export default {
  name: "RuleDrawer",
  components: {
    ShowUserListInput
  },
  props: {
    ruleId: {
      type: String,
      default: ""
    },
    operationType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      form: this.$form.createForm(this, "form"),
      formLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 }
      },
      formItem: [
        {
          label: "基础信息",
          componentName: "p"
        },
        {
          label: "规则名称",
          componentName: "yn-input",
          key: RULE_INFO_FIELD_NAME.ruleName,
          value: "",
          attrs: {},
          require: true,
          rules: [{ max: 64, message: "字段长度超过64位限制, 请检查" }]
        },
        {
          label: "类型",
          componentName: "yn-select",
          key: RULE_INFO_FIELD_NAME.type,
          disabled: true,
          value: "0",
          attrs: {
            options: [
              {
                value: "0",
                label: "对账邮件"
              },
              {
                value: "1",
                label: "流程邮件"
              }
            ]
          },
          require: true,
          rules: []
        },
        {
          label: "启用",
          componentName: "yn-switch",
          key: RULE_INFO_FIELD_NAME.status,
          value: false,
          require: true,
          attrs: {},
          decoratorOptions: {
            valuePropName: "checked"
          }
        },
        {
          label: "规则说明",
          key: RULE_INFO_FIELD_NAME.explain,
          componentName: "yn-textarea",
          value: "",
          rules: [
            { require: false, max: 50, message: "字段长度超过50位限制, 请检查" }
          ]
        },
        {
          label: "规则说明",
          componentName: "p"
        },
        {
          label: "邮件模板",
          componentName: "yn-select",
          key: RULE_INFO_FIELD_NAME.templateName,
          value: "",
          attrs: {
            options: []
          },
          require: true,
          rules: []
        },
        {
          label: "收件人",
          componentName: "ShowUserListInput",
          key: RULE_INFO_FIELD_NAME.recipients,
          value: [],
          attrs: {
            enableUnlimited: false
          },
          events: {
            change: this.changeUser
          },
          require: true,
          rules: []
        }
      ],
      spinning: true,
      btnLoading: false
    };
  },
  computed: {
    title() {
      return `${TYPE_MAPPING[this.operationType]}${MODULE_NAME}`;
    }
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      const tempalteData = await this.requestTemplate();
      const ruleInfo = await this.requestRuleInfo();
      this.setDefaultVal(tempalteData, ruleInfo);
      this.spinning = false;
    },
    async requestTemplate() {
      const {
        data: { data }
      } = await emailService("emailTemplates");
      return data.map(item => ({
        label: item.templateName,
        value: item.templateId
      }));
    },
    async requestRuleInfo() {
      if (!this.operationType) {
        // 新增操作 不用查规则信息
        return {};
      }
      // 根据ruleId 获取规则信息
      const {
        data: { data }
      } = await emailService("queryEmail", this.ruleId);
      this.formatterForm(data);
      return data;
    },
    setDefaultVal(tempalteData, ruleInfo) {
      this.formItem.forEach((item, index) => {
        const { key } = item;
        const value = ruleInfo[key];
        if (value) {
          this.$set(this.formItem[index], "value", value);
        }
        if (key === RULE_INFO_FIELD_NAME.templateName) {
          this.$set(this.formItem[index].attrs, "options", tempalteData);
          return true;
        }
      });
    },
    onClose() {
      this.$emit("opearDrawer", {}, false);
    },
    changeUser(userScope) {
      this.form.setFieldsValue({
        [RULE_INFO_FIELD_NAME.recipients]: userScope
      });
    },
    // 特殊回显处理：
    formatterForm(data) {
      // 启动状态，
      data[RULE_INFO_FIELD_NAME.status] =
        data[RULE_INFO_FIELD_NAME.status] === "TRUE";
      // 名称复制
      if (this.operationType === 1) {
        data[RULE_INFO_FIELD_NAME.ruleName] =
          data[RULE_INFO_FIELD_NAME.ruleName] + "_副本";
      }
      // 收件人
      const type =
        data[RULE_INFO_FIELD_NAME.recipients][0].operationType === "and"
          ? "join"
          : "union";
      data[RULE_INFO_FIELD_NAME.recipients] = data[
        RULE_INFO_FIELD_NAME.recipients
      ].map(item => {
        return {
          apvlUserIds: item.userItems.map(sItem => sItem.userId),
          apvlUserNames: item.userItems.map(sItem => sItem.userName),
          list: item.userItems.map(sItem => {
            return {
              apvlUserId: sItem.userId,
              orgName: sItem.userName,
              scopeType: sItem.scopeType,
              label: sItem.userName
            };
          }),
          val: item.userType
        };
      });
      data[RULE_INFO_FIELD_NAME.recipients].unshift({
        setType: type
      });
    },
    // 特殊处理表单：
    formatterData(values) {
      if (this.operationType === 2) {
        // 新增操作 不用查规则信息
        values.ruleId = this.ruleId;
      }
      // 人员
      const userIdMap = {
        user: item =>
          item.apvlUserIds.map((subItem, index) => {
            return {
              userId: subItem,
              userName: item.apvlUserNames[index]
            };
          }),
        role: item =>
          item.apvlUserIds.map((subItem, index) => {
            return {
              userId: subItem,
              userName: item.apvlUserNames[index]
            };
          }),
        group: item =>
          item.apvlUserIds.map((subItem, index) => {
            return {
              userId: subItem,
              userName: item.apvlUserNames[index]
            };
          }),
        post: item =>
          item.apvlUserIds.map((subItem, index) => {
            return {
              userId: subItem,
              userName: item.apvlUserNames[index]
            };
          }),
        org: item =>
          item.list.map(subItem => {
            return {
              userId: subItem.apvlUserId,
              userName: subItem.orgName,
              scopeType: subItem.scopeType
            };
          })
      };
      const { setType } = values[RULE_INFO_FIELD_NAME.recipients][0];
      values[RULE_INFO_FIELD_NAME.recipients] = values[
        RULE_INFO_FIELD_NAME.recipients
      ]
        .slice(1)
        .map(item => {
          return {
            operationType: setType === "join" ? "and" : "or", // 交并集 类型。或：or，且：and
            userType: item.val, // 用户类型。用户：user， 角色：role，群组：group，岗位：post，组织机构：org
            userItems: userIdMap[item.val](item)
          };
        });

      // 状态
      values[RULE_INFO_FIELD_NAME.status] = values[RULE_INFO_FIELD_NAME.status]
        ? "TRUE"
        : "FALSE";
    },
    async handleOk() {
      this.btnLoading = true;
      this.form.validateFields((err, values) => {
        if (err) {
          this.btnLoading = false;
          return;
        }
        // 保存接口
        this.formatterData(values);
        const api = this.operationType === 2 ? "editEmail" : "saveEmail";
        emailService(api, values)
          .then(res => {
            const {
              data: { success }
            } = res;
            if (success) {
              UiUtils.successMessage(
                `${TYPE_MAPPING[this.operationType]}${MODULE_NAME}成功`
              );
              this.$emit("refreshTableData");
              this.onClose();
            }
          })
          .finally(() => {
            this.btnLoading = false;
            // 调用刷新表格数据接口
          });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.area {
  font-weight: 600;
}
.footerCont {
  width: 27.5rem;
  position: fixed;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: end;
  align-items: center;
  border-top: 1px solid @yn-border-color-base;
  padding: 0.625rem 1rem;
  .okBtn {
    margin-left: @yn-margin-s;
  }
}
</style>
