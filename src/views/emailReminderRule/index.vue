<template>
  <div class="emailReminderRule">
    <yn-spin :spinning="spinning" size="large">
      <div v-if="empty" class="emptyCont">
        <yn-empty type="null-data" description="请新增邮件提醒规则">
          <yn-button type="primary" @click="handleAddRules">新增规则</yn-button>
        </yn-empty>
      </div>
      <yn-page-list
        v-else
        ref="pageList"
        :pageTitle="pageTitle"
        :tableConfig="tableConfig"
        :toolsConfig="toolsConfig"
        @table_rowSelectChange="handleSelectChange"
        @table_change="changeTable"
      >
        <template v-slot:[`tools.title`]>
          <template v-if="isBatchesDelete">
            <span class="choose-item">
              已选 {{ selectedRowKeysAll.length }} 条
            </span>
            <yn-button
              class="delete-confirm toolsBtn"
              type="primary"
              :disabled="!selectedRowKeysAll.length"
              @click="handleBatchesDelete"
            >
              删除
            </yn-button>
            <yn-button
              class="toolsBtn cancelBatchDel"
              @click="handleCancelbatchDel"
            >
              取消
            </yn-button>
          </template>
          <template v-else>
            <yn-input-search
              v-model="searchWord"
              class="head-search"
              placeholder="请输入规则名称"
              @change="() => (searching = true)"
              @search="handleSearch"
            />
          </template>
        </template>
        <template v-if="!isBatchesDelete" v-slot:[`tools.btns`]>
          <yn-button type="primary" class="toolsBtn" @click="handleAddRules">
            新增
          </yn-button>
          <yn-button
            class="toolsBtn btn-delete"
            @click="() => (isBatchesDelete = true)"
          >
            删除
          </yn-button>
          <yn-divider class="head-divider" type="vertical" />
          <svg-icon
            title="刷新"
            type="icon-shuaxin"
            @click="refreshTableData"
          />
        </template>
        <template v-slot:[`table.status`]="status">
          {{ status | ruleStatus }}
        </template>
        <template v-slot:[`table.type`]="type">
          {{ type | ruleTypes }}
        </template>
        <template v-slot:[`table.operation`]="record">
          <div class="table-operation">
            <a
              href="javascript:;"
              class="yn-a-link opearBtn edit"
              @click.stop="handleEditRule(record)"
            >
              编辑
            </a>
            <a
              href="javascript:;"
              type="text"
              class="yn-a-link opearBtn copy"
              @click.stop="handleCopyRule(record)"
            >
              复制
            </a>
            <yn-popconfirm
              title="确定要删除当前已选规则吗？"
              placement="bottomRight"
              okText="删除"
              cancelText="取消"
              @confirm="confirmDelete(record)"
            >
              <a class="yn-a-link opearBtn delete" @click.stop>
                删除
              </a>
            </yn-popconfirm>
            <svg-icon
              class="cell-operation-btns"
              type="icon-c1_cr_form_enter"
            />
          </div>
        </template>
      </yn-page-list>
    </yn-spin>
    <RuleDrawer
      v-if="visible"
      :ruleId="ruleId"
      :operationType="operationType"
      @opearDrawer="opearDrawer"
      @refreshTableData="refreshTableData"
    />
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-page-list/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-input-search/";
import RuleDrawer from "./RuleDrawer.vue";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import emailService from "@/services/email";
import {
  FIELD_NAME,
  STATUS_MAPPING,
  MODULE_NAME,
  TYPES_MAPPING
} from "./constant.js";
export default {
  name: "EmailReminderRule",
  components: {
    RuleDrawer
  },
  filters: {
    ruleStatus(val) {
      return STATUS_MAPPING[val] || STATUS_MAPPING["TRUE"];
    },
    ruleTypes(val) {
      return TYPES_MAPPING[val] || TYPES_MAPPING[0];
    }
  },
  data() {
    return {
      isBatchesDelete: false,
      searchWord: "",
      searching: false,
      pageTitle: {
        title: MODULE_NAME
      },
      toolsConfig: {
        options: [
          [
            {
              slotName: "btns"
            }
          ]
        ]
      },
      tableConfig: {
        rowKey: "objectId",
        pagination: {
          total: 0,
          current: 1,
          pageSize: 10,
          currentPageSize: 10,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          showTotal: total => `总计${total}条`
        },
        columns: [
          {
            title: "规则名称",
            dataIndex: FIELD_NAME.ruleName,
            key: FIELD_NAME.ruleName
          },
          {
            title: "类型",
            dataIndex: FIELD_NAME.type,
            key: FIELD_NAME.type,
            scopedSlots: {
              customRender: "type"
            }
          },
          {
            title: "状态",
            key: FIELD_NAME.status,
            dataIndex: FIELD_NAME.status,
            scopedSlots: {
              customRender: "status"
            }
          },
          {
            title: "最近修改人",
            dataIndex: FIELD_NAME.updateUserName,
            key: FIELD_NAME.updateUserName
          },
          {
            title: "最近修改时间",
            dataIndex: FIELD_NAME.updateTime,
            key: FIELD_NAME.updateTime
          },
          {
            title: "创建人",
            dataIndex: FIELD_NAME.creator,
            key: FIELD_NAME.creator
          },
          {
            title: "创建时间",
            dataIndex: FIELD_NAME.createTime,
            key: FIELD_NAME.createTime
          },
          {
            title: "操作",
            key: "operation",
            width: 190,
            scopedSlots: {
              customRender: "operation"
            }
          }
        ],
        dataSource: [],
        customRow: this.handlerCustomRow,
        rowSelection: false
      },
      selectedRowKeysAll: [],
      deletedRowKeys: [],
      spinning: false,
      ruleId: "",
      operationType: -1,
      visible: false
    };
  },
  computed: {
    empty() {
      return (
        this.tableConfig.dataSource.length === 0 &&
        !this.searchWord &&
        !this.searching &&
        !this.spinning
      );
    }
  },
  watch: {
    isBatchesDelete: {
      handler(newVal) {
        this.tableConfig.rowSelection = newVal
          ? { selectedRowKeys: this.selectedRowKeysAll }
          : false;
      }
    }
  },
  created() {
    this.requestTableData();
  },
  methods: {
    async requestTableData() {
      this.spinning = true;
      const {
        data: {
          data: { list, total }
        }
      } = await emailService("searchEmail", {
        searchRuleName: this.searchWord,
        pageNum: this.tableConfig.pagination.current,
        pageSize: this.tableConfig.pagination.pageSize
      });
      this.tableConfig.pagination.total = total;
      this.$set(this.tableConfig, "dataSource", list);
      this.spinning = false;
      this.searching = false;
    },
    async handleSearch() {
      this.searching = true;
      this.tableConfig.pagination.current = 1;
      await this.requestTableData();
    },
    async changeTable(searchInfo) {
      this.tableConfig.pagination.current = searchInfo.pagingInfo.current;
      this.tableConfig.pagination.pageSize = searchInfo.pagingInfo.pageSize;
      await this.requestTableData();
    },
    handleRefresh() {
      this.searchWord = "";
      this.tableConfig.pagination.current = 1;
    },
    handleAddRules() {
      this.opearDrawer({}, true, 0);
    },
    confirmDelete(record) {
      this.deleteRows([record.objectId]);
    },
    handleCopyRule(record) {
      this.opearDrawer(record, true, 1);
    },
    handleEditRule(record) {
      this.opearDrawer(record, true, 2);
    },
    handleViewRule(record) {
      this.opearDrawer(record, true, 3);
    },
    opearDrawer(record, visible, type = -1) {
      const { objectId: ruleId } = record;
      this.visible = visible;
      this.ruleId = ruleId;
      this.operationType = type;
    },
    getRuleInfoById() {},
    handlerCustomRow(record) {
      return {
        on: {
          click: () => {
            if (this.isBatchesDelete) return;
            this.handleViewRule(record);
          }
        }
      };
    },
    handleSelectChange(selectedRow, selectedRowKeysAll) {
      const res = selectedRowKeysAll.filter(item => {
        return this.deletedRowKeys.indexOf(item) === -1;
      });
      this.$set(this, "selectedRowKeysAll", res);
    },
    handleBatchesDelete() {
      if (this.selectedRowKeysAll.length === 0) {
        UiUtils.infoMessage("请选择需要删除的项");
        return;
      }
      UiUtils.confirm({
        title: "删除已选邮件提醒规则",
        content: "确定要删除当前已选规则吗？",
        okText: "删除",
        cancelText: "取消",
        onOk: async () => {
          this.spinning = true;
          await this.deleteRows(this.selectedRowKeysAll);
          this.handleCancelbatchDel();
          this.spinning = false;
        }
      });
    },
    // 删除数据
    async deleteRows(keys) {
      this.spinning = true;
      await emailService("deleteEmail", keys).then(res => {
        const {
          data: { success }
        } = res;
        if (success) {
          UiUtils.successMessage("删除成功");
          this.requestTableData();
        } else {
          UiUtils.errorMessage("删除失败");
        }
      });
      // 重置数据
    },
    handleCancelbatchDel() {
      this.isBatchesDelete = false;
      this.selectedRowKeysAll.splice(0);
    },
    refreshTableData() {
      this.searchWord = "";
      this.requestTableData();
    }
  }
};
</script>
<style lang="less" scoped>
.screenful() {
  width: 100%;
  height: 100%;
}
.emailReminderRule {
  .screenful;
  /deep/.ant-table-row-cell-break-word {
    padding: 1px @rem16;
  }
  .emptyCont {
    .screenful;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .delete-confirm {
    margin-left: @yn-margin-xl;
  }
  .btn-delete,
  .cancelBatchDel {
    margin-left: @yn-margin-s;
  }
  .toolsBtn {
    min-width: 3.75rem;
  }
  .edit,
  .copy {
    margin-right: @yn-margin-l;
  }
}
</style>
