<template>
  <yn-tabs defaultActiveKey="rule" class="reconciliationSetup cs-container">
    <yn-tab-pane
      v-for="tabInfo in tabData"
      :key="tabInfo.key"
      :tab="tabInfo.title"
    >
      <component :is="tabInfo.component" />
    </yn-tab-pane>
  </yn-tabs>
</template>
<script>
import "yn-p1/libs/components/yn-tabs/";
import "yn-p1/libs/components/yn-tab-pane/";
import MatchSetting from "./matchSetting";
import DimSetting from "./dimSetting";
import reconciliationService from "@/services/reconciliation";
export default {
  name: "ReconciliationSetings",
  components: {
    // eslint-disable-next-line vue/no-unused-components
    MatchSetting,
    // eslint-disable-next-line vue/no-unused-components
    DimSetting
  },
  data() {
    return {
      tabData: [
        {
          key: "rule",
          title: "对账维度设置",
          component: "DimSetting"
        },
        {
          key: "fields",
          title: "对账协同设置",
          component: "MatchSetting"
        }
      ]
    };
  },
  async mounted() {
    await this.getPermission();
  },
  methods: {
    async getPermission() {
      const {
        data: { data }
      } = await reconciliationService("hasReconciliationCoordinationAuth");
      const hasAuth = data;
      // 当没有 对账协同 权限时，不要展示 对账协同设置 tab页签
      if (!hasAuth) {
        this.tabData = this.tabData.filter(f => f.key !== "fields");
      }
    }
  }
};
</script>
<style lang="less" scoped>
.reconciliationSetup {
  height: 100%;
  /deep/ .ant-tabs-top-bar {
    border: none;
  }
  /deep/ .ant-tabs-nav-wrap {
    display: flex;
    align-items: center;
    height: 2.75rem;
    background: transparent;
    box-shadow: none;
    font-weight: 600;
  }
  /deep/.ant-tabs-content {
    height: calc(100% - 2.75rem);
  }
  /deep/.ant-tabs-tabpane-active {
    height: 100%;
  }
  /deep/.ant-spin-blur {
    width: 100%;
  }
}
</style>
