<template>
  <div class="reconcilliation_setting">
    <div class="header">
      <div class="toolBar">
        <div class="title">
          <span class="title-word">对账设置</span>
          <yn-button
            v-if="!isEditstatus"
            type="primary"
            class="eitBtn"
            @click="handleEdit"
          >
            编辑
          </yn-button>
        </div>
      </div>
      <span class="updateDate">更新时间：{{ updateTime }}</span>
      <yn-divider class="divider" />
    </div>
    <yn-spin :spinning="spinning" :class="['content']">
      <div class="cubes">
        <span class="title">对账数据源</span>
        <yn-form :form="fromCube">
          <yn-form-item
            class="cube-item"
            label="对账Cube"
            :colon="false"
            v-bind="formItemLayout"
          >
            <span v-show="!isEditstatus" class="dim-value">
              {{ currCubeName }}
            </span>
            <yn-select-tree
              v-show="isEditstatus"
              ref="cubetree"
              v-decorator="['cubeCode']"
              :disabled="true"
              placeholder="请选择对账Cube"
              :height="180"
              searchMode="custom"
              :datasource="cubes"
              nonleafselectable
              forceRender
              @change="handlderCube"
              @customSearch="onCustomeSearch"
              @dropdownVisibleChange="close"
            />
          </yn-form-item>
        </yn-form>
      </div>
      <div class="selectedDim">
        <span class="title">
          已选对账维度
          <!-- <span class="tips">
            可“<span class="highlight">拖拽</span>”调整顺序
          </span> -->
        </span>
        <div class="dimListCont">
          <vuedraggable
            v-model="customerDropDim"
            draggable=".item"
            dragClass="sortable-drag"
            :group="{ name: 'dimList', pull: false, put: true }"
            :class="['drop-area', isOnDraging ? 'draging' : '']"
            @start="handlerDropStart"
            @end="handlerDropEnd"
          >
            <dim-block
              v-for="item in systemDim"
              slot="header"
              :key="item.key"
              :dimInfo="item"
              :isShowDel="false"
              :disabled="true"
            />
            <dim-block
              v-for="item in customerDropDim"
              :key="item.dimId"
              :dimInfo="item"
              :class="[
                isEditstatus && !item.disabled && !item.used
                  ? 'disable-item'
                  : ''
              ]"
              :isShowDel="isEditstatus && !item.disabled && !item.used"
              :disabled="!isEditstatus || item.disabled || item.used"
              @clickEvent="handleDimClick"
            />
          </vuedraggable>
        </div>
      </div>
      <div class="toBeSelect">
        <span class="title">
          可选对账维度
          <span class="tips">
            可“<span class="highlight">拖拽</span>”成员至已选对账维度
          </span>
        </span>
        <div class="dimListCont">
          <yn-form layout="inline" :form="fromSelect" class="custome-dragform">
            <vuedraggable
              v-model="customerDragDim"
              :group="{ name: 'dimList', pull: true, put: true }"
              :class="['drag-area', isOnDroping ? 'draging' : '']"
              draggable=".edit"
              dragClass="sortable-drag"
              handle=".edit label"
              :delay="20"
              :touchStartThreshold="5"
              :forceFallback="true"
              @change="addSaveEvent"
              @start="handlerDragStart"
              @end="handlerDragEnd"
            >
              <yn-form-item
                v-for="(item, index) in customerDragDim"
                :key="item.dimId"
                :colon="false"
                :class="[
                  'drag-area-item',
                  isEditstatus ? 'edit' : '',
                  isOnDraging && index === dragIndex ? 'draging' : ''
                ]"
                :label="item.dimName"
                v-bind="formItemLayout"
              >
                <span v-show="!isEditstatus" class="dim-value">
                  {{ item.memberName }}
                </span>
                <div v-show="isEditstatus">
                  <yn-select-tree
                    v-if="item.dimCode === 'Category'"
                    :ref="item.dimId"
                    v-decorator="[
                      `${item.dimId}`,
                      { rules: [{ required: true, message: '请选择维度值!' }] }
                    ]"
                    placeholder="请选择维度值!"
                    :height="180"
                    searchMode="single"
                    :datasource="categoryData"
                    nonleafselectable
                    forceRender
                    @click.native="e => e.target.focus()"
                    @change="handlerChange(item, arguments)"
                  />
                  <asyn-select-dimMember
                    v-else
                    :ref="`${item.dimId}`"
                    v-decorator="[
                      `${item.dimId}`,
                      {
                        rules: [{ required: true, message: '请选择维度值!' }],
                        initialValue: undefined
                      }
                    ]"
                    placeholder="请选择维度值!"
                    :height="180"
                    :dimCode="item.dimCode"
                    nonleafselectable
                    forceRender
                    :loadComplete="loadComplete"
                    @click.native="e => e.target.focus()"
                    @change="handlerChange(item, arguments)"
                  />
                </div>
              </yn-form-item>
            </vuedraggable>
          </yn-form>
        </div>
      </div>
      <div v-if="isEditstatus" class="footer">
        <yn-button class="cancelBtn" @click="handleEditState(false)">
          取消
        </yn-button>
        <yn-button
          type="primary"
          class="okBtn"
          :disabled="onSubmit"
          :loading="onSubmit"
          @click="handleEditState(true)"
        >
          保存
        </yn-button>
      </div>
    </yn-spin>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-page-title/";
import cloneDeep from "lodash/cloneDeep";
import vuedraggable from "vuedraggable";
import DimBlock from "./dimBlock.vue";
import reconciliationService from "@/services/reconciliation";
import { setDataKey2key } from "@/utils/common";
import savePromptMixin from "@/mixin/savePrompt.js";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
export default {
  name: "ReconciliationSetting",
  components: {
    vuedraggable,
    DimBlock,
    AsynSelectDimMember
  },
  mixins: [savePromptMixin],
  data() {
    return {
      formItemLayout: {
        labelCol: {
          span: 6
        },
        wrapperCol: {
          span: 18
        }
      },
      onSubmit: false,
      spinning: false,
      cubeCode: "sys10主附表",
      updateTime: "",
      fromCube: this.$form.createForm(this, {
        name: "fromCube"
      }),
      fromSelect: this.$form.createForm(this, {
        name: "fromSelect"
      }),
      isEditstatus: false,
      cubes: [],
      isOnDraging: false,
      isOnDroping: false,
      dragIndex: "",
      dropIndex: "",
      systemDim: [],
      customerDropDim: [],
      customerDragDim: [],
      loadNum: 0,
      categoryData: [] // 类别维度下拉列表
    };
  },
  computed: {
    currCubeName() {
      const cubes = this.cubes;
      let currCubeName = "";
      for (let i = 0, LEN = cubes.length; i < LEN; i++) {
        const { cubeCode, cubeName } = cubes[i];
        if (this.cubeCode === cubeCode) {
          currCubeName = cubeName;
          break;
        }
      }
      return currCubeName;
    }
  },
  created() {
    this.init();
  },
  methods: {
    loadComplete() {
      this.loadNum += 1;
    },
    async getCubes() {
      const { data } = await reconciliationService("getCubes");
      const cubes = data.map(item => {
        return {
          ...item,
          label: item.cubeName,
          key: item.cubeCode
        };
      });
      this.cubes = cubes;
      this.$nextTick(() => {
        this._cubesCache = cloneDeep(cubes);
      });
    },
    async setMembersTree(dragData = this.customerDragDim) {
      await Promise.all(
        dragData.map(item => {
          return reconciliationService("getMembersTree", item.dimId);
        })
      ).then(resarr => {
        resarr.forEach((res, index) => {
          const {
            data: { errmsg },
            data = []
          } = res;
          const dragitem = dragData[index];
          if (errmsg) return;
          const { name: memberName = "" } = dragitem.memberId
            ? this.getTreeNodeByMemberId(data, dragitem.memberId)
            : {};
          this.$set(dragitem, "memberName", memberName);
          this.$nextTick(() => {
            if (dragitem.datasource) {
              this.fromSelect.setFieldsValue({
                [dragitem.dimId]: dragitem.memberId
              });
              return;
            }
            const datasource = setDataKey2key(
              data,
              ["id", "name"],
              ["key", "label"]
            );
            dragitem.datasource = datasource;
            this.fromSelect.setFieldsValue({
              [dragitem.dimId]: dragitem.memberId
            });
            dragitem._datasourceCache = cloneDeep(datasource);
          });
        });
      });
    },
    getTreeNodeByMemberId(data, memberId) {
      let isBreak = false;
      let index = 0;
      let tempArr = [...data];
      while (!isBreak) {
        const { id, children } = tempArr[index];
        if (id === memberId) {
          isBreak = true;
        } else {
          index++;
          tempArr = tempArr.concat(children || []);
        }
      }
      return tempArr[index];
    },
    async setDimData(cubeCode) {
      const {
        data: {
          optionalDims = [],
          selectedDims = [],
          updateTime,
          cubeCode: selCubeCode
        }
      } = !cubeCode
        ? await reconciliationService("getData")
        : await reconciliationService("getDataByCube", cubeCode);

      this.systemDim = selectedDims
        .filter(item => item.disabled)
        .map(item => ({ ...item, dimId: item.key, dimName: item.title }));

      this.customerDropDim = selectedDims
        .filter(item => !item.disabled)
        .map(item => ({ ...item, dimId: item.key, dimName: item.title }));

      this.customerDragDim = optionalDims;
      this.updateTime = updateTime;
      this.cubeCode = selCubeCode;
      this.fromCube.setFieldsValue({ cubeCode: selCubeCode });
      // 获取类别下拉
      this.categoryData = await this.getCateGoryDimMembersTree();
      // 下拉树加载完成
      await new Promise(res => {
        const ident = setInterval(() => {
          if (this.loadNum === this.customerDragDim.length) {
            clearInterval(ident);
            res();
          }
        }, 100);
      });
      // await this.setMembersTree();
    },
    // 获取类别列表
    getCateGoryDimMembersTree() {
      // 如果 已选、可选对账维度都没有 类别维度则不需要请求
      if (
        ![...this.customerDropDim, ...this.customerDragDim].find(
          item => item.dimCode === "Category"
        )
      ) {
        return;
      }
      return new Promise(resolve => {
        reconciliationService("cateGoryDimMembersTree")
          .then(res => {
            const data = res.data && res.data.length ? res.data : [];
            resolve(setDataKey2key(data, ["id", "name"], ["key", "label"]));
          })
          .catch(() => {
            resolve([]);
          })
          .finally(() => {
            this.spinning = false;
          });
      });
    },
    async resetDimData() {
      this.fromSelect.resetFields();
      const { systemDim, customerDropDim, customerDragDim } = cloneDeep(
        this._lastdimDataCache || this._dimDataCache
      );
      this.systemDim = systemDim;
      this.customerDropDim = customerDropDim;
      this.customerDragDim = customerDragDim;
      this.updateTime = this._lastupdateTime || this.updateTime;
      this.cubeCode = this._lastcubeCode || this.cubeCode;
      this.setFormData();
    },
    async init() {
      this.spinning = true;
      Promise.all([this.getCubes(), this.setDimData()])
        .then(() => {
          this.setDimDataCache();
          this.setSuccessRecord();
          this.spinning = false;
        })
        .catch(res => {
          this.spinning = false;
        });
    },
    setFormData() {
      const temp = {};
      this.customerDragDim.forEach(item => {
        temp[item.dimId] = item.memberId;
      });
      this.$nextTick(() => {
        this.fromSelect.setFieldsValue(temp);
        this.fromCube.setFieldsValue({ cubeCode: this.cubeCode });
      });
    },
    setDimDataCache() {
      this.$nextTick(() => {
        this._updateTime = this.updateTime;
        this._cubeCode = this.cubeCode;
        this._dimDataCache = cloneDeep({
          systemDim: this.systemDim,
          customerDropDim: this.customerDropDim,
          customerDragDim: this.customerDragDim
        });
      });
    },
    setSuccessRecord() {
      // 保存最近一次成功操作记录
      this._lastupdateTime = this.updateTime;
      this._lastcubeCode = this.cubeCode;
      this._lastdimDataCache = cloneDeep({
        systemDim: this.systemDim,
        customerDropDim: this.customerDropDim,
        customerDragDim: this.customerDragDim
      });
    },
    handlerDragEnd() {
      this.isOnDraging = false;
    },
    handlerDragStart(event) {
      this.isOnDraging = true;
      this.dragIndex = event.oldIndex;
    },
    handlerDropEnd() {
      this.isOnDroping = false;
    },
    handlerDropStart(event) {
      this.isOnDroping = false; // true 目前组件存在拖拽问题
      this.dropIndex = event.oldIndex;
    },
    handlderCube(cubeCode) {
      this.loadNum = 0;
      this.$nextTick(() => {
        this.fromCube.setFieldsValue({ cubeCode: this.cubeCode });
      });
      this.savePromptMixin().then(async () => {
        this.isEditstatus = true;
        this.spinning = true;
        this.fromCube.setFieldsValue({ cubeCode: cubeCode });
        await this.setDimData(cubeCode);
        this.spinning = false;
      });
    },
    handlerChange(...info) {
      const [selectItem, [, idatasource]] = info;
      selectItem.memberId = idatasource ? idatasource.key : "";
      selectItem.memberName = idatasource ? idatasource.label : "";
      this.addSaveEvent();
    },
    onCustomeSearch({ searchValue } = {}) {
      const refs = this.$refs.cubetree;
      refs.setMenuLoading(true);
      this.cubes = this.cubes.filter(
        item => item.cubeName.indexOf(searchValue) > -1
      );
      refs.setMenuLoading(false);
    },
    onCustomeSearchDrag(e, item, index) {
      const refs = this.$refs[item.dimId][0];
      refs.setMenuLoading(true);
      if (e.searchValue) {
        item.datasource = item._datasourceCache.filter(
          item => item.name.indexOf(e.searchValue) > -1
        );
      } else {
        item.datasource = item._datasourceCache;
      }
      this.customerDragDim.splice(index, 1, item);
      refs.setMenuLoading(false);
    },
    close(open) {
      if (!open) {
        this.cubes = this._cubesCache;
      }
    },
    closeDrag(e, item) {
      if (!e.open) {
        item.datasource = item._datasourceCache;
      }
    },
    handleEdit() {
      this.isEditstatus = true;
    },
    handleDimClick(dimInfo) {
      const { dimId } = dimInfo;
      this.customerDropDim = this.customerDropDim.filter(
        item => item.dimId !== dimId
      );
      this.customerDragDim.push(dimInfo);
      // this.setMembersTree([dimInfo]);
      this.addSaveEvent();
    },
    handleEditState(type) {
      if (type) {
        this.saveEvent();
      } else {
        this.isEditstatus = false;
        this.resetDimData();
        this.clearCommonSaveEventsMixin();
      }
    },
    addSaveEvent() {
      this.addCallBackFnMixin(
        this.saveEvent,
        "您要保存在当前 Cube 下的对账设置吗？？"
      );
    },
    saveEvent() {
      return new Promise((resolve, reject) => {
        this.fromSelect.validateFields((error, values) => {
          if (error) {
            UiUtils.errorMessage("可选对账维度选项不能为空");
            return;
          }
          this.spinning = true;
          const params = this.formatParam(values);
          this.onSubmit = true;
          resolve();
          reconciliationService("saveData", params)
            .then(async res => {
              UiUtils.successMessage("对账维度配置成功");
              this.updateTime = res.data.data;
              this.isEditstatus = false;
              this.setSuccessRecord();
              this.clearCommonSaveEventsMixin();
              resolve();
            })
            .finally(() => {
              this.onSubmit = false;
              this.spinning = false;
            });
        });
      });
    },
    formatParam(values) {
      return {
        cubeCode: this.cubeCode,
        optionalDims: Object.entries(values).map(item => {
          return {
            dimId: item[0],
            memberId: item[1]
          };
        }),
        selectedDims: this.systemDim
          .map(item => ({ dimId: item.dimId, systemFlag: 1 }))
          .concat(
            this.customerDropDim.map(item => ({
              dimId: item.dimId,
              systemFlag: 0
            }))
          )
      };
    }
  }
};
</script>

<style lang="less" scoped>
.reconcilliation_setting {
  height: 100%;
  width: 100%;
  position: relative;
  display: flex;
  flex-flow: column;
  .title {
    display: flex;
    flex-flow: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    height: @rem32;
    margin: 0.625rem 0 0.25rem 0;
    .title-word {
      font-size: @rem16;
      color: @yn-text-color;
      font-weight: 600;
    }
  }
  .divider {
    margin: 0 1rem;
    margin-top: 0.625rem;
    width: calc(100% - 2rem);
    min-width: calc(100% - 2rem);
  }
  .header {
    position: relative;
    margin-top: 0.375rem;
    background: #fff;
    .eitBtn {
      width: 5rem;
    }
    .updateDate {
      font-size: @yn-font-size-base;
      color: @yn-label-color;
      margin-top: @rem8;
      padding-left: 1rem;
    }
  }
  .content {
    background: @yn-component-background;
    border-top-left-radius: @yn-console-content-radius !important;
    border-top-right-radius: @yn-console-content-radius !important;
    display: flex;
    flex-direction: column;
    .cubes {
      padding: @yn-padding-l 1rem 0 0;
    }
    .selectedDim,
    .toBeSelect {
      padding: 0 1rem 0 0;
    }
    .cube-item {
      width: 364px;
      margin-left: @yn-margin-s;
      & /deep/ .ant-form-item-label label {
        color: @yn-text-color-secondary;
      }
      & /deep/ .yn-select-tree-clear-btn {
        display: none;
      }
    }
    .drop-area {
      width: 100%;
      display: flex;
      flex-flow: row wrap;
      padding: @yn-padding-s 1rem;
      margin-right: @yn-margin-xxl;
      &.draging {
        border: 1px dashed @yn-chart-1;
      }
    }
    .title {
      font-size: 14px;
      color: @yn-text-color;
      font-weight: 500;
      .tips {
        color: @yn-label-color;
        margin-left: 16px;
      }
      .highlight {
        color: @yn-warning-color;
      }
    }
    .dimListCont {
      display: flex;
      flex-flow: row wrap;
    }
    /deep/.ant-spin-container {
      overflow: auto;
    }
    .toBeSelect {
      margin-top: @rem16;
      position: relative;
      .title {
        margin-bottom: 16px;
      }
      .dimListCont {
        height: calc(100% - @rem32);
        // position: absolute;
        width: 100%;
        bottom: 0;
        left: 0;
        top: 0;
        right: 0;
        overflow: auto;
        margin-top: @rem16;
        margin-bottom: @rem16;
        .custome-dragform {
          margin-bottom: @rem8;
          padding-right: @rem24;

          .drag-area {
            width: 100%;
            display: flex;
            flex-flow: row wrap;
            &.draging {
              border: 1px dashed @yn-primary-color;
            }
            .drag-area-item {
              width: 364px;
              margin-bottom: 10px;
              user-select: none;
              &.edit:hover {
                color: @yn-text-color-secondary;
                font-weight: 400;
                background: @yn-background-color-light;
              }
              & /deep/ .ant-form-item-label label {
                color: @yn-text-color-secondary;
              }
              &.draging {
                display: flex;
                align-items: center;
                border: 1px dashed @yn-primary-color;
              }
              &.edit /deep/ .ant-form-item-label label {
                cursor: move;
              }
              /deep/ .ant-form-item-label {
                overflow: hidden;
                text-overflow: ellipsis;
                height: @rem32;
              }
              .dim-value {
                height: 22px;
                font-family: PingFangSC-Regular;
                font-size: @yn-font-size-base;
                color: @yn-text-color;
                text-align: left;
                line-height: @rem22;
                font-weight: 400;
              }
            }
          }
        }
      }
    }
  }
  .footer {
    position: absolute;
    height: 3rem;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 1rem;
    bottom: 0;
    line-height: 3rem;
    background: @yn-body-background;
    border-top: 1px solid @yn-border-color-base;
    & > button {
      width: 5rem;
    }
    .cancelBtn {
      margin-right: 1rem;
    }
  }
}
.sortable-drag {
  height: 48px !important;
  display: flex !important;
  padding: 8px;
  align-items: center;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: @yn-text-color-secondary;
  font-weight: 400;
  background: @yn-background-color-light;
  opacity: 1 !important;
  .ant-form-explain {
    display: none;
  }
}
</style>
