<template>
  <div
    :class="[
      'dimBlock',
      isShowDel ? 'showDelBtn' : '',
      isShowAdd ? 'showAddBtn' : '',
      disabled ? 'disabled' : ''
    ]"
  >
    <span>{{ dimInfo.dimName || dimInfo.title }}</span>
    <yn-icon
      v-if="isShowDel"
      type="close"
      class="closeBtn"
      @click.stop="handleClick('deleteDim')"
    />
    <div v-if="isShowAdd" class="mark" @click="handleClick('addDim')">
      <div class="addMark">
        <yn-icon type="plus" />
        <span>添加</span>
      </div>
    </div>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-icon/";
export default {
  name: "DimBlock",
  props: {
    dimInfo: {
      type: Object,
      default() {
        return {
          title: "123",
          id: "123"
        };
      }
    },
    isShowDel: <PERSON><PERSON><PERSON>,
    isShowAdd: <PERSON><PERSON><PERSON>,
    disabled: <PERSON>olean
  },
  methods: {
    handleClick(type) {
      if (this.disabled) return;
      this.$emit("clickEvent", this.dimInfo, type);
    }
  }
};
</script>
<style lang="less" scoped>
.dimBlock {
  height: 32px;
  line-height: 31px;
  text-align: center;
  position: relative;
  cursor: pointer;
  padding: 0 15px;
  border-radius: 4px;
  border: 1px solid @yn-border-color-base;
  margin: 8px 16px 8px 0;
  & > span {
    min-width: 50px;
    display: block;
  }
  cursor: move;
}
.closeBtn,
.mark {
  display: none;
}
.showDelBtn {
  padding-right: 30px;
  position: relative;
  background: @yn-background-color-light;
  color: @yn-primary-color;
  &:hover {
    & > .closeBtn {
      display: inline-block;
      position: absolute;
      bottom: 8px;
      right: 10px;
    }
  }
}

.showAddBtn {
  background: @yn-component-background;
  &:hover {
    & > .mark {
      display: block;
      position: absolute;
      z-index: 1;
      width: calc(100% + 2px);
      height: calc(100% + 2px);
      top: -1px;
      left: 0;
      border-radius: 4px;
      background: rgba(0, 0, 0, 0.75);
      line-height: 32px;
      .addMark {
        color: @yn-component-background;

        & > span {
          margin-left: 3px;
        }
      }
    }
  }
}
.disabled {
  cursor: not-allowed;
  height: 32px;
  background: @yn-disabled-bg-color;
  border: 1px solid @yn-border-color-base;
  border-radius: 4px;
  color: @yn-text-color-secondary;
  &:hover {
    .mark,
    .closeBtn {
      display: none;
      color: @yn-text-color-secondary;
    }
  }
}
</style>
