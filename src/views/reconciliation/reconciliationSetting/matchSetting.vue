<template>
  <yn-spin :spinning="spinning" :class="['match-setting']">
    <div class="field-setting">
      <!-- <div class="source">
          <label class="source-label">启用对账协同</label>
          <yn-switch
            v-model="formValue"
            trueValue="开"
            falseValue="关"
            @change="onChangeMatch"
          />
        </div>
        <yn-divider class="divider" /> -->
      <div class="field-tip">{{ CONSTANT.fieldTip }}</div>
      <yn-table
        :columns="columns"
        :dataSource="dataSource"
        :rowKey="COLUMNS_FIELD.key"
        class="field-table"
      >
        <span slot="table.customTitle">
          对账协同状态 <span class="required">*</span>
        </span>
        <div
          v-for="col in [COLUMNS_FIELD.status]"
          :slot="'table.' + col"
          :key="col"
          slot-scope="text, record"
          class="field-col"
        >
          <yn-input
            v-if="record.edit"
            :class="{
              'status-input': true,
              error: record.error === true
            }"
            :value="record[col]"
            @mouseover="showValidateInfo($event, record)"
            @mouseout="hiddenValidateInfo"
            @change="e => handleChange(e.target.value, record, col)"
          />
          <span v-if="!record.edit">{{ record[col] }}</span>
        </div>
        <template slot="table.operation" slot-scope="text, record, index">
          <yn-button
            v-if="!record.edit"
            class="yn-a-link action-btn"
            href="javascript:;"
            type="text"
            @click="editRow(record)"
          >
            编辑
          </yn-button>
          <yn-button
            v-if="record.edit"
            class="yn-a-link action-btn"
            href="javascript:;"
            type="text"
            @click="saveRow(record)"
          >
            保存
          </yn-button>
          <yn-button
            v-if="record.edit"
            class="yn-a-link action-btn"
            href="javascript:;"
            type="text"
            @click="cancelEdit(record)"
          >
            取消
          </yn-button>
          <yn-popconfirm
            v-if="!record.builtIn && !record.created && !record.edit"
            title="确定要删除当前已选状态吗？"
            placement="bottomRight"
            okText="删除"
            cancelText="取消"
            :disabled="dataSource.length === 1"
            @confirm="deleteRow(record, index)"
          >
            <yn-button
              class="yn-a-link action-btn"
              href="javascript:;"
              type="text"
              :disabled="dataSource.length === 1"
              @click.stop
            >
              删除
            </yn-button>
          </yn-popconfirm>
          <yn-button
            v-if="record.created && !record.edit"
            class="yn-a-link action-btn"
            href="javascript:;"
            type="text"
            :disabled="dataSource.length === 1"
            @click.stop="deleteRow(record, index)"
          >
            删除
          </yn-button>
        </template>
      </yn-table>
      <div
        v-show="showVerificationInfo"
        ref="errorTipsCont"
        class="error-tips-cont"
        :style="errorTipsStyle"
      >
        <span class="tips">请输入</span>
      </div>
      <div class="new-add-btn-cont">
        <yn-button type="dashed" :disabled="isDisabled" @click="addRow">
          <span class="custom-add">+</span> 添加
        </yn-button>
      </div>
    </div>
  </yn-spin>
</template>

<script>
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select-option/";
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-switch/";
import "yn-p1/libs/components/yn-popconfirm/";
import reconciliationService from "@/services/reconciliation";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import CONSTANT, { COLUMNS_FIELD } from "./constants";
export default {
  props: {},
  data() {
    return {
      CONSTANT,
      COLUMNS_FIELD,
      spinning: false,
      currentRecord: null,
      dataColumns: [
        {
          dataIndex: COLUMNS_FIELD.status,
          slots: {
            title: "customTitle"
          },
          scopedSlots: {
            customRender: COLUMNS_FIELD.status
          }
        }
      ],
      showVerificationInfo: false,
      errorTipsStyle: {},
      actionColumns: [
        {
          title: "操作",
          dataIndex: "operation",
          width: "7.5rem",
          scopedSlots: {
            customRender: "operation"
          }
        }
      ],
      dataSource: [],
      formValue: ""
    };
  },
  computed: {
    columns() {
      return [...this.dataColumns, ...this.actionColumns];
    },
    isDisabled() {
      if (this.dataSource.length === 0) return false;
      return this.dataSource.some(
        item => !item[COLUMNS_FIELD.status] || item.created || item.edit
      );
    }
  },
  async created() {
    this.spinning = true;
    this.getDataSource();
    this.spinning = false;
  },
  methods: {
    getEmpty() {
      // created: 新建的， // edit: 正在编辑
      return [{ [COLUMNS_FIELD.status]: "", edit: true, created: true }];
    },
    async getDataSource() {
      const {
        data: { data }
      } = await reconciliationService("getReconciliationCoordinationSetting");
      this.dataSource = data && data.length > 0 ? data : this.getEmpty();
    },
    handleChange(value, record, col) {
      this.$set(record, col, value);
      if (value === "") {
        this.$set(record, "error", true);
      } else {
        this.$set(record, "error", false);
        this.showVerificationInfo = false;
      }
      this.currentRecord = record;
      this.addCallBackFnMixin(
        async () => this.saveRow(record),
        `您要保存所做的更改吗`,
        async () => {
          this.$set(record, [COLUMNS_FIELD.status], record.originValue);
          this.dataSource = this.dataSource.filter(
            item => item[COLUMNS_FIELD.status]
          );
        }
      );
    },
    onChangeMatch() {},
    cancelEdit(record) {
      this.$set(record, COLUMNS_FIELD.status, record.originValue);
      this.$set(record, "edit", false);
      this.clearCommonSaveEventsMixin();
    },
    async deleteRow(record, index) {
      this.spinning = true;
      if (record.created) {
        this.dataSource = this.dataSource.filter((data, i) => i !== index);
        this.spinning = false;
      } else {
        if (this.currentRecord === record) {
          reconciliationService(
            "deleteStatementCoordination",
            record[COLUMNS_FIELD.id]
          )
            .then(res => {
              this.getDataSource();
              UiUtils.successMessage("删除成功");
              this.clearCommonSaveEventsMixin();
            })
            .finally(() => {
              this.spinning = false;
            });
        } else {
          this.savePromptMixin()
            .then(() => {
              reconciliationService(
                "deleteStatementCoordination",
                record[COLUMNS_FIELD.id]
              )
                .then(res => {
                  this.getDataSource();
                  UiUtils.successMessage("删除成功");
                })
                .finally(() => {
                  this.spinning = false;
                });
            })
            .catch(() => {
              this.spinning = false;
            });
        }
      }
    },
    hiddenValidateInfo() {
      this.showVerificationInfo = false;
    },
    showValidateInfo(e, record) {
      const target = e.target.parentNode;
      const className = target.className || "";
      if (
        record.error === true &&
        typeof className === "string" &&
        className.indexOf("status-input") !== -1
      ) {
        // 设置错误提示弹窗位置信息
        const {
          left: tLeft,
          top: tTop,
          height,
          width
        } = target.getBoundingClientRect();
        const { left: sLeft, top: sTop } = this.$el.getBoundingClientRect();
        this.errorTipsStyle = {
          width: width + "px",
          left: tLeft - sLeft + "px",
          top: tTop - sTop + height + 1 + "px"
        };
        this.showVerificationInfo = true;
      } else {
        this.showVerificationInfo = false;
      }
    },
    async saveRow(record) {
      if (!record[COLUMNS_FIELD.status]) {
        this.$set(record, "error", true);
        UiUtils.errorMessage("可标记状态不能为空");
        return Promise.reject(false);
      }
      this.spinning = true;
      this.$set(record, "error", false);
      reconciliationService("saveReconciliationCoordinationSetting", {
        id: record[COLUMNS_FIELD.id],
        name: record[COLUMNS_FIELD.status]
      })
        .then(res => {
          this.clearCommonSaveEventsMixin();
          UiUtils.successMessage("保存成功");
          this.getDataSource();
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    editRow(record) {
      record.originValue = record[COLUMNS_FIELD.status];
      this.savePromptMixin().then(() => {
        this.dataSource.forEach(item => {
          this.$set(item, "edit", false);
        });
        this.$set(record, "edit", true);
      });
    },
    addRow() {
      this.dataSource = [...this.dataSource, ...this.getEmpty()];
    }
  }
};
</script>
<style lang="less" scoped>
.match-setting {
  height: 100%;
}
.field-setting {
  height: 100%;
  padding: 1rem;
  margin-top: 0.375rem;
  position: relative;
  border-radius: 4px 4px 0 0;
  background: @yn-component-background;
}
.field-title {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.375rem;
  letter-spacing: 0px;
  color: @yn-text-color;
  margin-bottom: 0.75rem;
  .sign {
    width: 2.5px;
    background: @yn-primary-color;
    margin-left: 0;
    margin-right: 0.375rem;
    border-radius: 0.625rem;
  }
}
.divider {
  margin: 0;
}
.empty {
  height: 100%;
}
.error-tips-cont {
  position: absolute;
  display: flex;
  justify-content: end;
  min-width: 200px; // 与ERROR_TIPS_MIN_WIDTH对应
  & > .tips {
    display: inline-block;
    position: absolute;
    height: 24px;
    line-height: 26px;
    padding: 0 12px;
    color: @yn-error-color;
    background: @yn-error-bg-color;
    z-index: 100;
    box-shadow: inset 0 0 5px 0 @yn-border-color-base;
    border-bottom-left-radius: @yn-border-radius-base;
    border-bottom-right-radius: @yn-border-radius-base;
  }
}
.status-input {
  &.error {
    border: 1px solid #f54645 !important;
    /deep/ &:hover .ant-input:not(.ant-input-disabled) {
      border-color: transparent;
    }
    /deep/ .ant-input:focus {
      border: none;
      box-shadow: none;
    }
  }
}
.required {
  color: #f54645;
}
.source {
  display: flex;
  align-items: center;
  height: 4.0625rem;
  width: 21.25rem;
  &-label {
    margin-right: 0.5rem;
  }
  &-select {
    flex: 1;
  }
  &-text {
    height: 1.375rem;
    font-size: 0.875rem;
    font-weight: normal;
    line-height: 1.375rem;
    letter-spacing: 0px;
    color: @yn-text-color;
  }
}
.field-tip {
  height: 1.25rem;
  font-size: 0.75rem;
  font-weight: normal;
  line-height: 1.25rem;
  letter-spacing: 0px;
  margin-bottom: 0.5rem;
  color: @yn-label-color;
}
.field-table {
  max-width: 50%;
  /deep/ .ant-table-tbody > tr > td {
    height: 2.25rem;
    padding-top: 0;
    padding-bottom: 0;
  }
}
.field-col {
  display: flex;
  align-items: center;
  height: 2.25rem;
}
.action-btn {
  height: 1.125rem;
  line-height: 1.125rem;
  margin-right: 0.5rem;
}
.new-add-btn-cont {
  margin-right: 3.5rem;
  margin-top: 0.75rem;
  max-width: 50%;
  & > button {
    width: 100%;
    &[disabled] {
      background: @yn-disabled-bg-color !important;
    }
  }
}
</style>
