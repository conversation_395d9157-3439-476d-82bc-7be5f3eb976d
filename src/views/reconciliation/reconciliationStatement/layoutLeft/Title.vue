<template>
  <div class="toolBar">
    <yn-button
      v-show="!isShowSearchInput"
      v-if="authSet['add']"
      type="primary"
      size="small"
      @click="addReportForm"
    >
      新增报表
    </yn-button>
    <svg-icon
      v-show="!isShowSearchInput"
      type="search"
      title="搜索"
      size="small"
      class="iconBtn"
      @click="showSearchInput(true)"
    />
    <svg-icon
      v-show="!isShowSearchInput"
      title="刷新"
      size="small"
      type="icon-shuaxin"
      class="iconBtn"
      @onClick="handleRefresh"
    />
    <yn-input-search
      v-show="isShowSearchInput"
      ref="searchInput"
      class="title_search"
      placeholder="请输入搜索关键字"
      size="small"
      @blur="showSearchInput(false)"
      @search="handleSearch"
    />
  </div>
  <!-- <div v-else class="toolBar noAuthority">
    <div>
      <yn-input-search
        ref="searchInput"
        class="title_search"
        placeholder="请输入搜索关键字"
        @search="handleSearch"
      />
    </div>
    <svg-icon
      type="icon-shuaxin"
      class="iconBtn"
      title="刷新"
      size="small"
      @onClick="handleRefresh"
    />
  </div> -->
</template>
<script>
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-input-search/";
import { mapState } from "vuex";
export default {
  name: "ReconciliationTitle",
  props: {
    isNewAddAuthority: {
      type: Boolean
    }
  },
  data() {
    return {
      searchWord: "",
      isShowModal: false,
      isShowSearchInput: false
    };
  },
  inject: ["namespace"],
  computed: {
    ...mapState({
      authSet(state) {
        return state[this.namespace].authSet;
      }
    })
  },
  methods: {
    addReportForm() {
      this.$emit("addReportForm");
    },
    handleSearch(word) {
      if (!word) {
        this.isShowSearchInput = false;
      }
      this.searchWord = word;
      this.$emit("handleSearch", word);
    },
    handleRefresh() {
      this.$refs.searchInput.$el.querySelector("input").value = "";
      this.$emit("handleRefresh", true);
    },
    showSearchInput(status) {
      if (status) {
        setTimeout(() => {
          this.$refs.searchInput.$el.querySelector("input").focus();
        });
      }
      if (!status && this.$refs.searchInput.$el.querySelector("input").value) {
        return;
      }
      this.isShowSearchInput = status;
    }
  }
};
</script>
<style lang="less" scoped>
.toolBar {
  .btns {
    margin-bottom: 7px;
  }
  .iconBtn {
    color: @yn-label-color;
    float: right;
    position: relative;
    cursor: pointer;
  }
  .title_search {
    position: absolute;
    width: calc(100% - @rem32);
    left: @rem16;
    top: @rem8;
    /deep/.ant-input {
      height: 26px;
      line-height: 26px;
    }
  }
}
.noAuthority {
  .title_search {
    width: calc(100% - 36px);
  }
  .iconBtn {
    position: absolute;
    top: 7px;
    right: 12px;
  }
}
</style>
