<template>
  <div class="reconciliation-tree">
    <div class="tree-cont">
      <yn-spin :spinning="spinning">
        <yn-tree-menu
          :searchConfig="searchConfig"
          width="100%"
          :operateData="buttonOperate"
          :treePanelSkeleton="treePanelSkeleton"
          :treeConfig="treeConfig"
          @expand="onExpand"
          @select="onSelect"
          @drop="onDrop"
        >
          <div slot="operateBar" class="operateBar">
            <reconciliation-title
              :isNewAddAuthority="hasAuthority"
              @handleSearch="handleSearch"
              @handleRefresh="handleRefresh"
              @addReportForm="addReportForm"
            />
          </div>
          <template slot="tree.custom" slot-scope="item">
            <span class="tree-node-cont">
              <span :title="item.name" class="tree-name">{{ item.name }}</span>
              <yn-dropdown v-if="!item.isRoot" style="text-align: center">
                <yn-icon-button
                  v-auth="{
                    createBySelf: getIconAuth(item)
                  }"
                  type="more"
                  class="more-btn"
                  size="small"
                  @click.stop
                />
                <yn-menu slot="overlay" @click="handleMenuClick($event, item)">
                  <template v-for="menuItem in menuData">
                    <yn-menu-item
                      :key="`${menuItem.eventName}`"
                      :menuItem="menuItem"
                      class="menu-item"
                      style="font-size: 12px; padding: 5px 30px 5px 15px"
                    >
                      {{ menuItem.menuName }}
                    </yn-menu-item>
                  </template>
                </yn-menu>
              </yn-dropdown>
            </span>
          </template>
        </yn-tree-menu>
      </yn-spin>
    </div>
    <process-reconciliation-report-modal
      v-if="isShowModal"
      :hasAuthority="hasAuthority"
      :currTreeNodeInfo="currTreeNodeInfo"
      :operationType="operationType"
      :reconciliationConfig="reconciliationConfig"
      @closeModal="closeModal"
      @selectTreeNode="selectTreeNodeById"
    />
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-tree/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-spin/";
import ProcessReconciliationReportModal from "./ProcessReconciliationReport.vue";
import { EventBus } from "yn-p1/libs/utils/ComponentUtils";
import ReconciliationTitle from "./Title.vue";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import Logger from "yn-p1/libs/modules/log/logger";
import cloneDeep from "lodash/cloneDeep";
import reconciliationCommon from "@/views/reconciliation/minix";
import apiService from "@/views/reconciliation/minix/service";
import "yn-p1/libs/components/yn-tree-menu/";
import { mapState } from "vuex";
const MENU_DATA = [
  { menuName: "编辑", eventName: "editTreeNode" },
  { menuName: "删除", eventName: "deleteTreeNode" }
];
const PUBLIC_FOLDER_RID = "11ec744a1f09cb5e9b76a1dee584c1a5"; // 余额公有
const PRIVATE_FOLDER_RID = "11ec744a1f122fcf9b763ffd18fd81d3"; // 余额私有
const PUBLIC_FOLDER_VID = "11ee76d50f7f81a487ce4db17244035e"; // 凭证公有
const PRIVATE_FOLDER_VID = "11ee76d50fc8236587ce61e87cee268f"; // 凭证私有

const CLOSE_MODAL_TYPE = {
  ok: "ok",
  saveOk: "save_ok",
  add: "add"
};
export default {
  components: {
    ProcessReconciliationReportModal,
    ReconciliationTitle
  },
  mixins: [reconciliationCommon, apiService],
  props: {
    params: {
      type: Object,
      require: false,
      default: () => {}
    }
  },
  data() {
    return {
      searchConfig: {
        props: {
          placeholder: "请输入"
        },
        events: {
          search: value => {
            this.onSearch(value);
          }
        }
      },
      buttonOperate: [
        {
          key: "addType",
          label: "新增报表",
          overflow: false,
          type: "primary",
          group: "common"
        }
      ],
      treeConfig: {
        selectedKeys: [],
        expandedKeys: [],
        treeData: [],
        draggable: true
      },
      treePanelSkeleton: {
        loading: false
      },
      treeData: [],
      treeDataList: [],
      spinning: true,
      searchWord: "",
      operationType: "add", // add or edit saveAs
      isShowModal: false,
      currTreeNodeInfo: {},
      hasLeaf: false, //  是否有叶子节点
      reconciliationConfig: {}, // 对账配置信息
      selectNodeInfo: {} // 选中树节点信息
    };
  },
  inject: [
    "openDrawerPro",
    "closeDrawerPro",
    "defaultParams",
    "reconciliationModuleName",
    "namespace"
  ],
  computed: {
    ...mapState({
      tabActiveId: state => state.common.tabActiveId,
      languages: state => {
        return state.common.languages;
      },
      hasAuthority(state) {
        return state[this.namespace].hasAuthority;
      },
      authSet(state) {
        return state[this.namespace].authSet;
      }
    }),
    menuData() {
      const auth_delete_key = this.authSet.delete ? "deleteTreeNode" : "";
      const auth_edit_key = this.authSet.edit ? "editTreeNode" : "";
      const keyList = [auth_delete_key, auth_edit_key];
      const menuData = MENU_DATA.filter(
        f => keyList.indexOf(f.eventName) !== -1
      );
      return menuData;
    }
  },
  watch: {
    hasLeaf(newVal) {
      this.$store.commit(
        this.getMutationOrActionType("changeHasLeafStatus"),
        newVal
      );
    },
    treeData: {
      handler(newVal) {
        this.$store.commit(
          this.getMutationOrActionType("setTreeData"),
          cloneDeep(newVal)
        );
      },
      immediate: true,
      deep: true
    },
    tabActiveId: {
      async handler(newVal) {
        const { id, params } = this.params;
        const { selectTreeId } = params;
        // 激活当前页签，并且传入选中节点id与当前树选中节点id不一致
        if (newVal === id && selectTreeId !== this.treeConfig.selectedKeys[0]) {
          await this.handleRefresh(false, selectTreeId);
          // 清除params 里面的值
          this.$store.commit("common/updateTab", {
            id: id,
            params: {}
          });
        }
      },
      immediate: true
    },
    languages: {
      handler(newVal) {
        if (!newVal.length) {
          this.$store.dispatch("common/getEnableLanguages");
        }
      },
      immediate: true
    }
  },
  async created() {
    await this.reuqestTreeData();
    // 维度成员引用 跳转
    this.memberReferenceJumpEvent();
    EventBus.on("openReportFormModalByType", this.openModalByType);
    this.activeTabCbMixin(this.activeTabCb);
  },
  destroyed() {
    this.$store.commit(this.getMutationOrActionType("setSelectTreeNode"), {});
    EventBus.off("openReportFormModalByType", this.openModalByType);
  },
  methods: {
    getIconAuth(item) {
      if (this.namespace === "reconciliation") {
        // 余额对账中，编辑等数据权限通过creatorFlag表达
        return item.attr.creatorFlag && this.menuData.length;
      } else if (this.namespace === "voucherReconciliation") {
        // 凭证对账中，编辑等数据权限只通过 公有报表权限表达，只要拥有 公有报表 权限，即可只通过权限集控制菜单权限
        return this.menuData.length;
      }
    },
    memberReferenceJumpEvent() {
      const { id: treeNodeId } = this.defaultParams.MRJ || {};
      if (!treeNodeId) return;
      const treeNode = this.findTreeNodeById(treeNodeId);
      if (Object.keys(treeNode).length) {
        const { id, parentId } = treeNode;
        this.$store.commit(
          this.getMutationOrActionType("setSelectTreeNode"),
          treeNode
        );
        this.selectTreeNodeById(id);
        this.expandeNodeById(parentId);
      } else {
        UiUtils.errorMessage("对账不存在");
      }
    },
    activeTabCb() {
      // 设置选中树节点信息
      const { selectTreeId, tabId } = this.getTabParamsMixin();
      if (selectTreeId && this.treeConfig.selectedKeys[0] !== selectTreeId) {
        this.handleRefresh(false, selectTreeId);
        this.setTabParamsMixin(tabId, { selectTreeId: "" });
      }
    },
    async reuqestTreeData() {
      await this.apiService("getListReconciliation").then(res => {
        const orgTreeData = this.handleOrgTreeData([
          ...(Array.isArray(res.data) ? res.data : res.data.data)
        ]);
        this.treeData = [...orgTreeData];
        this.treeConfig.treeData = [...orgTreeData];
        this.treePanelSkeleton.loading = false;
        this.spinning = false;
      });
    },
    handleOrgTreeData(data) {
      const treeDataList = [];
      const loop = data => {
        data.map(item => {
          const { children, id } = item;

          item.key = id;
          item.isLeaf =
            [
              PUBLIC_FOLDER_RID,
              PRIVATE_FOLDER_RID,
              PUBLIC_FOLDER_VID,
              PRIVATE_FOLDER_VID
            ].indexOf(id) === -1;
          item.isRoot =
            [
              PUBLIC_FOLDER_RID,
              PRIVATE_FOLDER_RID,
              PUBLIC_FOLDER_VID,
              PRIVATE_FOLDER_VID
            ].indexOf(id) !== -1;
          const attr = item.scopedSlots;
          item.attr = attr;
          item.scopedSlots = {
            title: "custom",
            icon: "folderIcon"
          };
          if (!this.hasLeaf && item.isLeaf) {
            this.hasLeaf = true;
          }
          treeDataList.push({
            ...item,
            isLeaf: true
          });
          if (children && children.length > 0) {
            loop(children);
          }
        });
      };
      loop(data);
      this.treeDataList = treeDataList;
      return data;
    },
    onExpand(expandedKeys) {
      this.treeConfig.expandedKeys = [...expandedKeys];
    },
    onSelect(nodeKey, event) {
      const treeNodeInfo = event.node.dataRef;
      const { id, isRoot, name, attr } = treeNodeInfo;
      // 选中跟节点，控制根节点展开闭合装填
      const [currKey] = nodeKey;
      if (isRoot) {
        this.setExpandStatusByRootNodeId(currKey);
        return;
      }
      // 不做取消选中处理
      if (!nodeKey.length) {
        return;
      }
      // 选中叶子节点
      this.treeConfig.selectedKeys = [...nodeKey];
      this.selectNodeInfo = treeNodeInfo;
      this.$store.commit(this.getMutationOrActionType("setSelectTreeNode"), {
        id,
        isRoot,
        name,
        attr
      });
    },
    // 设置展开闭合状态根据根节点id
    setExpandStatusByRootNodeId(id) {
      const index = this.treeConfig.expandedKeys.indexOf(id);
      if (index === -1) {
        this.treeConfig.expandedKeys.push(id);
      } else {
        this.treeConfig.expandedKeys.splice(index, 1);
      }
    },
    handleMenuClick(event, treeNode) {
      const { key } = event;
      this[key] && this[key](treeNode);
    },
    deleteTreeNode(treeNode) {
      const self = this;
      UiUtils.confirm({
        title: "该操作会删除当前对账报表，确定删除吗?",
        onOk() {
          self.spinning = true;
          const { id } = treeNode;
          self.apiService("deleteReconciliation", id).then(res => {
            if (res.status === 200) {
              self.removeTreeNode(id);
              self.spinning = false;
              UiUtils._getMessage().success("删除成功", 10);
            }
          });
        },
        onCancel() {
          Logger.log("Cancel");
        },
        okText: "删除",
        class: "test"
      });
    },
    removeTreeNode(id) {
      const loop = (data, parentNode) => {
        const tempArr = data.filter(item => {
          const { id: treeNodeId, children } = item;
          if (children && children.length > 0) {
            loop(children, item);
          }
          return treeNodeId !== id;
        });
        if (parentNode) {
          parentNode.children = [...tempArr];
        } else {
          return tempArr;
        }
      };
      const treeData = loop(this.treeData);
      const showTreeData = loop(this.treeConfig.treeData);
      this.treeData = [...treeData];
      this.treeConfig.treeData = [...showTreeData];
      this.$store.commit(this.getMutationOrActionType("setSelectTreeNode"), {});
      this.treeConfig.selectedKeys = [];
    },
    editTreeNode(treeNodeData) {
      this.isShowModal = true;
      this.operationType = "edit";
      const { id, key, parentId, name } = treeNodeData;
      this.currTreeNodeInfo = { id, key, parentId, name };
    },
    addReportForm() {
      this.openModalByType("add");
    },
    openModalByType(type) {
      const DEFAULT_TYPE = "add";
      let operationType = type || DEFAULT_TYPE;
      let reconciliationConfig = {};
      if (typeof operationType !== "string") {
        const { type: openType, reconciliationConfig: config } = type;
        operationType = openType || DEFAULT_TYPE;
        reconciliationConfig = config;
      }
      this.isShowModal = true;
      this.operationType = operationType;
      this.reconciliationConfig = reconciliationConfig;
    },
    handleSearch(word) {
      this.treeConfig.expandedKeys = [];
      if (word) {
        const treeData = this.treeDataList.filter(item => {
          return item.name.indexOf(word) !== -1 && !item.isRoot;
        });
        this.treeConfig.treeData = [...treeData];
      } else {
        this.treeConfig.treeData = this.treeData;
      }
    },
    async handleRefresh(isCloseAllNode, newTreeNodeId) {
      if (isCloseAllNode) {
        this.treeConfig.expandedKeys = [];
      }
      await this.reuqestTreeData();
      if (newTreeNodeId && this.treeConfig.selectedKeys[0] !== newTreeNodeId) {
        const treeNode = this.findTreeNodeById(newTreeNodeId);
        const { id, parentId } = treeNode;
        this.$store.commit(
          this.getMutationOrActionType("setSelectTreeNode"),
          treeNode
        );
        this.selectTreeNodeById(id);
        this.expandeNodeById(parentId);
      }
    },
    selectTreeNodeById(id) {
      id && this.treeConfig.selectedKeys.splice(0, 1, id);
    },
    expandeNodeById(id) {
      const isExpanded = this.treeConfig.expandedKeys.some(
        item => item.id === id
      );
      if (!isExpanded) {
        this.treeConfig.expandedKeys.push(id);
      }
    },
    findTreeNodeById(id) {
      const data = this.treeData;
      const childs = (data[0].children || []).concat(data[1].children || []);
      let treeNode = {};
      childs.some(item => {
        if (item.id === id) {
          treeNode = item;
          return true;
        }
        return false;
      });
      return treeNode;
    },
    closeModal(type, newTreeNodeId, treeNodeInfo) {
      // 关闭 操作对账报告弹窗以后 打开抽屉
      if (type.indexOf(CLOSE_MODAL_TYPE.ok) !== -1) {
        // 另存为以后关闭 对账配置抽屉，并且在激活页签时，去刷新树信息
        if (type === "saveAs_ok") {
          this.closeDrawerPro();
        }
        this.handleRefresh(false, newTreeNodeId);
      }
      if (type === CLOSE_MODAL_TYPE.saveOk) {
        // 新增保存
        this.openDrawerPro(CLOSE_MODAL_TYPE.add);
      }
      if (typeof treeNodeInfo === "object") {
        const {
          reconciliationName: name,
          objectId: editReconciliationId
        } = treeNodeInfo;
        const { attr, id, isRoot } = this.selectNodeInfo;
        // 编辑当前选中的节点，需要更新store上的数据
        if (editReconciliationId === id) {
          this.$store.commit(
            this.getMutationOrActionType("setSelectTreeNode"),
            {
              id,
              isRoot,
              name,
              attr
            }
          );
        }
      }
      this.currTreeNodeInfo = {};
      this.isShowModal = false;
    },
    onDrop(info) {
      this.spinning = true;
      // 拖入节点内不做处理
      if (!info.dropToGap) {
        UiUtils._getMessage().error("不能跨层级移动", 10);
        this.spinning = false;
        return;
      }
      const node = info.node;
      const nodeData = node.dataRef;
      const dragNode = info.dragNode;
      const dragNodeData = dragNode.dataRef;
      const endPos = node.pos.split("-").slice(-1)[0];
      const nodePos = info.dropPosition - endPos; // 放置节点位置，在目标节点上（1） 还是下（-1）
      const DRAGPOSTYPE = ["up", "down"];
      const params = {
        moveType: DRAGPOSTYPE[0], // UP or DOWWN
        targetId: nodeData.id,
        sourceId: dragNodeData.id
      };
      // 将源节点拖动到父节点下
      if (dragNodeData.isRoot || nodeData.isRoot) {
        UiUtils._getMessage().error("不能跨层级移动", 10);
        this.spinning = false;
        return;
      }
      // 同级移动
      if (nodeData.pId === dragNodeData.pId) {
        if (nodePos === 1) {
          params.moveType = DRAGPOSTYPE[1];
        }
      } else if (dragNodeData.pId === nodeData.id && nodePos === 1) {
        // 移动到父节点上，默认放置子节点第一个
        params.targetId = nodeData.children[0].id;
        params.moveType = DRAGPOSTYPE[0];
      } else {
        // 移动到其他父节点下面了
        UiUtils._getMessage().error("不能跨层级移动", 10);
        this.spinning = false;
        return;
      }
      this.apiService("dragMove", params)
        .then(res => {
          if (res.status === 200) {
            this.moveNode(params, nodeData);
            this.spinning = false;
          }
        })
        .catch(() => {
          this.spinning = false;
        });
    },
    moveNode(params, targetNode) {
      const { parentId } = targetNode;
      const { moveType, sourceId, targetId } = params;
      this.treeData.some(item => {
        if (item.id === parentId) {
          let sourceNode = {};
          let targetNodeIndex = 0;
          item.children = item.children.filter((item, index) => {
            const { id } = item;
            if (id === targetId) {
              targetNodeIndex = index;
            }
            if (id !== sourceId) {
              return true;
            } else {
              sourceNode = item;
              return false;
            }
          });
          if (moveType === "down") {
            targetNodeIndex += 1;
          }
          item.children.splice(targetNodeIndex, 0, sourceNode);
          return true;
        }
      });
      this.$set(this, "treeData", this.treeData);
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.operateBar {
  width: 100%;
  height: @rem26;
}
.reconciliation-tree {
  height: calc(100% - 1px);
  box-shadow: inset -1px 0px 0px 0px @yn-border-color-base;
  position: relative;
  .tree-cont {
    height: 100%;
  }
  .tree-node-cont {
    display: inline-block;
    width: 100%;
    height: 2rem;
    line-height: 2rem;
    margin-top: -1px;
    position: relative;
    span.tree-name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
      width: calc(100% - 28px);
    }
  }
  .more-btn {
    position: absolute;
    top: 50%;
    vertical-align: text-bottom;
    transform: translateY(-50%);
  }
  /deep/.yn-draggable-tree .ant-tree {
    // 标题 hover 时 after 伪类背景色
    li span.ant-tree-node-content-wrapper-open:hover:after {
      border-color: transparent;
      background: none !important;
    }
    li span.ant-tree-node-content-wrapper-close:hover:after {
      border-color: transparent;
      background: none !important;
    }
  }
}
.menu-item {
  font-size: @yn-font-size-sm;
  padding: @yn-padding-xs @yn-padding-xxxl @yn-padding-xs @yn-padding-xl;
}
</style>
