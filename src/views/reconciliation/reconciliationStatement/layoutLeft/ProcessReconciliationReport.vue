<template>
  <yn-modal
    :title="title"
    :visible="true"
    :width="440"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <yn-form :form="form" class="reportForm">
      <yn-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="名称">
        <yn-input
          v-decorator="[
            'reconciliationName',
            {
              rules: [
                { required: true, message: '请输入' },
                { max: 64, message: '字段长度超过64位限制，请检查' }
              ]
            }
          ]"
          class="report-form-name"
          placeholder="请输入"
        >
          <yn-language-popover
            slot="suffix"
            :languageData="languageData"
            @ok="handleLanguages"
          />
        </yn-input>
      </yn-form-item>
      <yn-form-item :labelCol="labelCol" :wrapperCol="wrapperCol">
        <template slot="label">
          <span class="item-label">类型</span>
          <yn-tooltip placement="bottom">
            <template slot="title">
              <p v-for="(item, index) in tips" :key="index" class="tip-item">
                {{ `${item}` }}
              </p>
            </template>
            <svg-icon type="icon-guanyu" :isIconBtn="false" class="tips" />
          </yn-tooltip>
        </template>
        <yn-radio-group
          v-decorator="['reconciliationType', { initialValue: 0 }]"
        >
          <yn-radio :value="0">
            私有
          </yn-radio>
          <yn-radio :disabled="!hasAuthority" :value="1">
            公有
          </yn-radio>
        </yn-radio-group>
      </yn-form-item>
      <yn-form-item
        label="说明"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        class="reconciliationDesc"
      >
        <yn-textarea
          v-decorator="['reconciliationDesc']"
          placeholder="请输入"
          :maxLength="50"
          :showCount="true"
          :autoSize="{ minRows: 3, maxRows: 10 }"
        />
      </yn-form-item>
    </yn-form>
  </yn-modal>
</template>
<script>
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-radio/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-textarea/";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-language-popover/";
import apiService from "@/views/reconciliation/minix/service";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import * as getPropByPath from "lodash/get";
import { mapState } from "vuex";
const OPERATION_TYPE_TIPS = {
  add: "新增对账报表",
  edit: "编辑对账报表",
  saveAs: "另存为"
};
const TIPS = [
  "私有：仅自己可修改编辑查看。",
  "公有：创建人可修改编辑，所有用户可查看。"
];
const SAVE_FN_MAPPING = {
  saveAs: "saveAs",
  add: "saveAdd",
  edit: "saveEdit"
};
export default {
  name: "ProcessReconciliationReportModal",
  mixins: [apiService],
  inject: ["reconciliationModuleName"],
  props: {
    operationType: {
      type: String,
      require: true,
      default() {
        return "add"; //  add 新增 or edit 编辑 saveAs 另存为
      }
    },
    currTreeNodeInfo: {
      // 根据 树节点 信息 请求对账报表信息
      type: Object,
      default() {
        return {};
      }
    },
    // 是否拥有创建公有报表的权限
    hasAuthority: {
      type: Boolean,
      default: true
    },
    // 对账配置信息，用于另存为时，将修改后的对账配置进行另存操作
    reconciliationConfig: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      title: "",
      plainOptions: [
        { label: "私有", value: 0 },
        { label: "公有", value: 1 }
      ],
      labelCol: {
        span: 4,
        offset: 2
      },
      wrapperCol: { span: 15 },
      form: this.$form.createForm(this, {
        name: "reportModal"
      }),
      confirmLoading: false,
      languageList: [],
      tips: TIPS
    };
  },
  computed: {
    ...mapState({
      selectTreeNode: state => state.reconciliation.selectTreeNode,
      languages: state => state.common.languages,
      languagesMap: state => {
        const mapObj = {};
        state.common.languages.forEach(item => {
          const { code } = item;
          mapObj[code] = item;
        });
        return mapObj;
      }
    }),
    languageData() {
      return this.languageList.map(item => {
        const { text: value, languageCode: key } = item;
        return {
          label: this.languagesMap[key].name,
          value,
          key
        };
      });
    }
  },
  watch: {
    operationType: {
      handler(newVal) {
        this.title = OPERATION_TYPE_TIPS[newVal];
        if (newVal === "edit") {
          this.requestFromData();
        } else if (newVal === "saveAs") {
          this.requestFromData(true);
        } else {
          this.setLanguageList();
        }
      },
      immediate: true
    }
  },
  methods: {
    setLanguageList(data = []) {
      const mapData = {};
      data.forEach(item => {
        const { languageCode } = item;
        mapData[languageCode] = item;
      });
      this.languageList = this.languages.map(item => {
        const { code: languageCode } = item;
        return {
          languageCode,
          text: mapData[languageCode] ? mapData[languageCode].text : ""
        };
      });
      this.$set(this, "languageList", this.languageList);
    },
    requestFromData(isSaveAs) {
      // 当编辑时，触发弹框，需要从props 上传入currTreeNode
      let { id } = this.currTreeNodeInfo;
      if (!id) {
        id = this.selectTreeNode.id;
      }
      this.apiService("getReconciliationById", id).then(res => {
        const {
          // 余额对账
          reconciliationName,
          reconciliationType,
          reconciliationDesc,
          // 凭证对账
          data,
          multiLanguages
        } = res.data;
        const relName = data ? data.name : reconciliationName;
        const relType = data ? data.type : reconciliationType;
        const relDes = data ? data.desc : reconciliationDesc;
        const mLang = data ? data.multiLanguages : multiLanguages;
        this.setLanguageList(mLang);
        this.form.setFieldsValue({
          reconciliationName: isSaveAs ? relName + "副本" : relName,
          reconciliationType: isSaveAs ? 0 : relType, // 另存为 默认是0
          reconciliationDesc: relDes
        });
      });
    },
    handleOk() {
      this.form.validateFields(err => {
        if (!err) {
          this.confirmLoading = true;
          this.handleSave(this.form.getFieldsValue());
        }
      });
    },
    async handleSave(params) {
      const {
        reconciliationDesc = "",
        reconciliationName,
        reconciliationType
      } = params;

      const requestParams = {
        reconciliationDesc,
        reconciliationName,
        reconciliationType,
        name: reconciliationName,
        type: reconciliationType,
        desc: reconciliationDesc,
        multiLanguages: this.getLanguageVal()
      };
      try {
        const fn = getPropByPath(
          this,
          SAVE_FN_MAPPING[this.operationType],
          () => {}
        );
        await fn(requestParams);
      } catch (e) {
        this.confirmLoading = false;
      }
    },
    getLanguageVal() {
      return this.languageList.filter(item => {
        return item.text;
      });
    },
    async saveAdd(params) {
      await this.apiService("saveReconciliation", params).then(res => {
        if (res.status === 200) {
          this.$emit("closeModal", "save_ok", res.data.data);
        }
      });
    },
    async saveEdit(params) {
      params.objectId = this.currTreeNodeInfo.id;
      await this.apiService("editReconciliation", params).then(res => {
        if (res.status === 200) {
          this.confirmLoading = false;
          UiUtils._getMessage().success("编辑成功", 10);
          this.$emit("closeModal", "edit_ok", "", params);
        }
      });
    },
    async saveAs(params) {
      await this.apiService("saveAsReconciliationConfig", {
        ...params,
        ...this.reconciliationConfig
      }).then(res => {
        if (res.status === 200) {
          UiUtils._getMessage().success("另存为成功", 10);
          this.$emit("closeModal", "saveAs_ok", res.data.data);
        }
      });
    },
    handleCancel() {
      this.visible = false;
      this.$emit("closeModal", "cancel");
    },
    handleLanguages(value) {
      this.languageList = value.map(item => {
        const { key: languageCode, value: text } = item;
        return {
          languageCode,
          text
        };
      });
      this.$set(this, "languageList", this.languageList);
    }
  }
};
</script>
<style lang="less" scoped>
.reportForm {
  /deep/.desc {
    margin-bottom: 0;
  }
}
.tips {
  position: relative;
  top: 1px;
  font-size: @yn-font-size-lg;
}
.tip-item {
  margin-bottom: @yn-margin-xs;
}
.item-label {
  margin-right: 4px;
}
</style>
