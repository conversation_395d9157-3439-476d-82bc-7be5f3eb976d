<template>
  <div :class="['displaySettings', isDrawer ? 'DrawerDisplaySettings' : '']">
    <p class="region-title areaTitle">
      报表显示设置
    </p>
    <yn-form :form="form">
      <yn-form-item
        class="showSuject"
        label="科目展示"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <yn-radio-group
          v-decorator="[
            'subject',
            { initialValue: displaySettingData.subject || 0 }
          ]"
          :options="plainOptions_subject"
        />
      </yn-form-item>
      <yn-form-item
        label="匹配项"
        required
        :class="['matches', isDrawer ? 'drawerMatches' : '']"
        :validateStatus="matchesValidateStatus"
        :help="help"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <yn-radio-group
          v-decorator="[
            'matches',
            {
              initialValue: displaySettingData.matches || 0
            }
          ]"
          :options="plainOptions"
          @change="handleMatchesChange"
        />
        <br v-if="isDrawer" />
        <yn-input
          v-if="matchesVal"
          v-decorator="[
            'tolerance',
            {
              initialValue: displaySettingData.tolerance || '',
              rules: [
                {
                  required: true,
                  message: '请输入容差值'
                }
              ]
            }
          ]"
          :class="['toleranceInput', isDrawer ? '' : 'inline']"
          @change="changeToleranceValue"
        >
          <yn-select
            slot="addonBefore"
            v-decorator="[
              'valueType',
              { initialValue: displaySettingData.valueType || 0 }
            ]"
            style="width: 5.625rem"
            :allowClear="false"
            @change="handleChangeType"
          >
            <yn-select-option :value="0">数值</yn-select-option>
            <yn-select-option :value="1">百分比</yn-select-option>
          </yn-select>
          <span v-if="valueType" slot="addonAfter">%</span>
        </yn-input>
      </yn-form-item>
      <yn-form-item
        label="保留小数位数"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <yn-input-number
          v-decorator="[
            'decimalPlace',
            {
              initialValue: decimalPlace,
              rules: [{ required: true, message: '请输入' }]
            }
          ]"
          :precision="0"
          class="decimalPlace"
          :min="0"
          :max="9"
        />
      </yn-form-item>
      <yn-form-item
        :class="[isDrawer ? 'footer-btn-cont' : 'footer']"
        label=" "
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <yn-button
          class="generateReport"
          type="primary"
          :loading="generateReportBtnLoading"
          @click="generateReport"
        >
          生成对账报表
        </yn-button>
        <yn-button @click="reset">重置</yn-button>
      </yn-form-item>
    </yn-form>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-option/";
import "yn-p1/libs/components/yn-input-number/";

export default {
  name: "DisplaySettings",
  components: {},
  props: {
    displaySettingData: {
      type: Object,
      default() {
        return {};
      }
    },
    generateReportBtnLoading: {
      type: Boolean,
      default: false
    },
    isDrawer: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      matchesValidateStatus: "success",
      help: "",
      form: this.$form.createForm(this, {
        name: "displaySettings"
      }),
      labelCol: {
        span: 4,
        sm: 6
      },
      wrapperCol: {
        span: 18,
        sm: 16
      },
      valueType: 0,
      matchesVal: 0,
      plainOptions_subject: [
        {
          label: "汇总",
          value: 0
        },
        {
          label: "分组",
          value: 1
        }
      ],
      plainOptions: [
        {
          label: "显示",
          value: 0
        },
        {
          label: "隐藏",
          value: 1
        }
      ],
      tolerance: ""
    };
  },
  computed: {
    decimalPlace() {
      const { decimalPlace } = this.displaySettingData;
      if (typeof decimalPlace === "number") {
        return decimalPlace;
      } else {
        // 默认值
        return 2;
      }
    }
  },
  watch: {
    displaySettingData: {
      handler(newVal) {
        this.valueType = newVal.valueType || 0;
        this.matchesVal = newVal.matches;
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleMatchesChange($event) {
      this.matchesVal = $event.target.value;
      this.tolerance = "";

      this.clearToLeranceValidateInfo();
    },
    handleChangeType(val) {
      this.valueType = val;
      // 切换以后，不在缓存上次输入正确的数值
      this.form.setFieldsValue({
        tolerance: ""
      });
      this.tolerance = "";
    },
    // 校验容差
    checkTolerance(val) {
      const toleranceType = [
        "checkToleranceNumber",
        "checkTolerancePercentage"
      ]; // 容差类型 【数值，百分比】
      if (!val.trim()) return true;
      // 百分比
      if (
        typeof Number(val) === "number" &&
        isNaN(Number(val)) &&
        this.valueType
      ) {
        return false;
      }
      return (
        this[toleranceType[this.valueType]] &&
        this[toleranceType[this.valueType]](val)
      );
    },
    // 校验数值类型
    checkToleranceNumber(val) {
      return /^[+-]?\d*\.?\d{0,9}$/.test(val);
    },
    // 校验百分比类型
    checkTolerancePercentage(val) {
      return val >= 0 && val <= 100;
    },
    changeToleranceValue(event) {
      let val = event.target.value;
      if (!this.checkTolerance(event.target.value)) {
        val = this.tolerance;
        event.target.value = val;
      }
      if (val) {
        this.clearToLeranceValidateInfo();
        this.tolerance = val;
      } else {
        // 清空值
        this.matchesValidateStatus = "error";
        this.help = "请输入容差值";
      }
    },
    clearToLeranceValidateInfo() {
      this.matchesValidateStatus = "success";
      this.help = "";

      this.form.setFieldsValue({
        tolerance: ""
      });
    },
    generateReport() {
      this.$emit("generateReportRequest", this.form.getFieldsValue());
    },
    reset() {
      this.form.resetFields();
      this.matchesVal = 0;
      this.help = "";
      this.$emit("reset");
    }
  }
};
</script>
<style lang="less" scoped>
div.showSuject {
  margin-bottom: 1rem;
}
div.DrawerDisplaySettings {
  width: 23.875rem;
}
.displaySettings {
  width: 28.125rem;
  padding-left: 0.25rem;
  .region-title {
    font-size: @rem14;
  }
  .toleranceInput {
    display: inline-block;
    width: calc(12.5rem - 0.1875rem);
  }
  .inline {
    position: absolute;
    top: -8px;
  }
  .footer /deep/.ant-form-item-children {
    .generateReport {
      margin-right: @yn-margin-xl;
    }
  }
  .footer-btn-cont {
    // position: absolute;
    position: fixed;
    right: 0;
    bottom: 0;
    // width: 100%;
    width: 440px;
    min-width: 27.5rem;
    border-top: 1px solid @yn-border-color-base;
    padding: 10px @yn-padding-xl;
    background: @yn-body-background;
    text-align: right;
    margin-bottom: 0;
    z-index: 10;
    /deep/.ant-col-4 label {
      display: none;
    }
    .generateReport {
      margin-right: @yn-margin-xl;
    }
  }
}
.decimalPlace {
  width: 100%;
}
.matches /deep/.ant-form-explain {
  margin-left: 135px;
}
.drawerMatches/deep/.ant-form-explain {
  margin-left: 0px;
}
</style>
