<template>
  <div class="reconciliationParams">
    <div v-if="!isDrawer" class="query-head">
      <div>
        <span class="title">{{ title }}</span>
        <yn-divider type="vertical" />
        <span class="head-info">{{ creator }}</span>
        <span class="head-info head-info-time">{{ updateTime }}</span>
        <span class="head-info">修改</span>
      </div>
      <yn-button
        v-if="authSet['edit-config']"
        type="text"
        class="head-btn"
        @click="openConfig"
      >
        修改对账配置
      </yn-button>
    </div>
    <slot :title="title" :isDrawer="isDrawer" name="quertContent">
      <query-range
        ref="queryRange"
        :title="title"
        :queryRangeData="queryRangeData"
        :isDrawer="isDrawer"
        @closeLoad="closeLoad"
        v-on="$listeners"
      />
      <display-settings
        ref="displaySettings"
        :displaySettingData="displaySettingData"
        :generateReportBtnLoading="generateReportBtnLoading"
        :isDrawer="isDrawer"
        @reset="reset"
        @generateReportRequest="generateReport"
      />
    </slot>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-divider/";
import { mapState, mapActions, mapMutations } from "vuex";
import QueryRange from "./QueryRange.vue";
import DisplaySettings from "./DisplaySettings.vue";
import { formatReconciliationParams } from "../utils";
import cloneDeep from "lodash/cloneDeep";
import { pollTaskStatus } from "@/utils/common";
import UiUtils from "yn-p1/libs/utils/UiUtils";
export default {
  name: "ReconciliationQuery",
  components: {
    QueryRange,
    DisplaySettings
  },
  props: {
    title: {
      type: String,
      default: ""
    },
    creator: {
      type: String,
      default: ""
    },
    updateTime: {
      type: String,
      default: ""
    },
    isDrawer: {
      type: Boolean,
      default: false
    },
    echoData: {
      type: Object,
      default: () => {}
    },
    selectTreeNodeInfo: {
      type: Object,
      default: () => {}
    }
  },
  inject: ["namespace"],
  data() {
    return {
      isSelectLeaf: false,
      isShowEmpty: true,
      hasConfig: false, // 是有有对账配置信息
      reportQueryParams: {}, // 对账的查询参数
      displaySettingData: {}, // 对账显示设置 数据
      queryRangeData: {}, // 对账查询数据
      cacheTreeNodeId: "",
      generateReportBtnLoading: false,
      selectTreeNode: {} // 选中树节点信息，如果isDrawer 是传入的selectTreeNodeInfo 否则是store上的
    };
  },
  computed: {
    ...mapState("reconciliation", {
      currSelectTreeNode: state => state.selectTreeNode, // 当前对账树上选中的节点信息
      hasLeaf: state => state.hasLeaf
    }),
    ...mapState({
      authSet(state) {
        return state[this.namespace].authSet;
      }
    })
  },
  watch: {
    title: {
      handler() {
        this.$refs.displaySettings && this.$refs.displaySettings.reset();
        this.displaySettingData = {};
      }
    },
    echoData: {
      handler(newVal) {
        const { displaySettingData, queryRangeData } = newVal || {};
        this.displaySettingData = cloneDeep(displaySettingData);
        this.queryRangeData = cloneDeep(queryRangeData);
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    ...mapActions("reconciliation", ["asyncGenerateReport"]),
    ...mapMutations("reconciliation", ["controlMainLoading"]),
    openConfig() {
      this.$emit("openDrawer", "edit");
    },
    reset() {
      this.$refs.queryRange.reset();
      this.queryRangeData = {};
      this.displaySettingData = {};
    },
    getTreeNodeInfo() {
      return this.isDrawer ? this.selectTreeNodeInfo : this.currSelectTreeNode;
    },
    async verificationParams() {
      let result = false;
      await Promise.all([
        this.$refs.displaySettings.form.validateFields(),
        this.$refs.queryRange.form.validateFields()
      ]).then(
        res => {
          result = true;
          this.$refs.displaySettings.matchesValidateStatus = "success";
          if (!this.verificationIcpAndEntity(res[1])) {
            result = false;
          }
        },
        rej => {
          // 如果容差校验失败
          if (Object.keys(rej.errors).indexOf("tolerance") !== -1) {
            this.$refs.displaySettings.matchesValidateStatus = "error";
            this.$refs.displaySettings.help = "请输入容差值";
          }
          this.verificationIcpAndEntity(rej.values);
        }
      );
      return result;
    },
    verificationIcpAndEntity(values) {
      //  子集团对账，不校验组织、往来公司
      const { reconciliationType, icp, entity } = values;
      //  如果对账类型1（为子集团对账）或者没有查询范围内容reconciliationType===undefined
      if (
        reconciliationType ||
        (typeof reconciliationType === "undefined" && !reconciliationType)
      ) {
        return true;
      }
      let res = true;
      if (!icp || !icp.selectedItem.length) {
        res = false;
        this.$refs.queryRange.icpValidateStatus = "error";
      }
      if (!entity || !entity.selectedItem.length) {
        res = false;
        this.$refs.queryRange.entityValidateStatus = "error";
      }
      return res;
    },
    async generateReport(data) {
      this.$emit("generateReportBefore");
      this.displaySettingData = data;
      this.queryRangeData = this.$refs.queryRange.form.getFieldsValue();
      const res = await this.verificationParams();
      if (res) {
        this.generateReportBtnLoading = true;
        this.controlMainLoading(true);
        const params = formatReconciliationParams(
          data,
          this.$refs.queryRange.form.getFieldsValue(),
          this.getTreeNodeInfo()
        );
        this.reportQueryParams = params;
        let taskId;
        try {
          taskId = await this.asyncGenerateReport(params);
        } catch (e) {
          this.closeLoad();
          this.$emit("generateReportError");
          return;
        }
        pollTaskStatus(
          taskId,
          () => {
            this.generateReportBtnLoading = false;
            this.controlMainLoading(false);
            this.$emit("generateReport", {
              taskId,
              requeryParams: {
                displaySettingData: this.displaySettingData,
                queryRangeData: this.queryRangeData
              },
              selectTreeNode: this.getTreeNodeInfo()
            });
          },
          () => {
            this.closeLoad();
            this.$emit("generateReportError");
            UiUtils.errorMessage("查询对账报告失败");
          }
        );
      } else {
        // 对账报告页面，参数有问题，关闭loading
        this.$emit("generateReportError");
      }
    },
    closeLoad() {
      this.controlMainLoading(false);
      this.generateReportBtnLoading = false;
    }
  }
};
</script>
<style lang="less">
.reconciliationParams {
  .areaTitle {
    font-weight: 600;
    font-size: 0.875rem;
    &::before {
      content: "";
      width: @rem4;
      height: @rem14;
      background: @yn-primary-color;
      display: inline-block;
      border-radius: 10px;
      margin-right: 0.375rem;
      vertical-align: text-top;
    }
  }
}
</style>
<style lang="less" scoped>
.reconciliationParams {
  padding: 0 @yn-padding-l 0 @yn-padding-xl;
  overflow: auto;
  height: 100%;
  position: relative;
  .query-head {
    width: 100%;
    height: 3.375rem;
    padding: 0 0 0 @rem4;
    background: @yn-body-background;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid @yn-border-color-base;
    position: sticky;
    top: 0;
    z-index: 1;
    /deep/.ant-divider-vertical {
      margin: 0 @rem16;
    }
    .head-info {
      font-size: @rem14;
      color: @yn-label-color;
      &.head-info-time {
        margin: 0 @rem8;
      }
    }
    .head-btn {
      margin-right: -@rem4;
    }
    .title {
      font-size: @yn-font-size-lg;
      color: @yn-text-color;
      text-align: left;
      line-height: @yn-margin-xxl;
      font-weight: 600;
    }
  }
}
</style>
