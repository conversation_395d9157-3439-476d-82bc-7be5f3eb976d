<template>
  <div :class="['queryRange', isDrawer ? 'queryRangeDrawer' : '']">
    <p class="areaTitle">
      查询数据范围
    </p>
    <yn-form :form="form">
      <yn-form-item
        v-for="item in formItem"
        :key="item.dimCode"
        :label="item.name"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <asyn-select-dimMember
          v-decorator="[
            item.dimCode,
            {
              initialValue: item.val,
              rules: [
                {
                  required: true,
                  message: `请选择${item.name}`
                }
              ]
            }
          ]"
          :dimCode="item.dimCode"
          forceRender
          class="dimSelectTree"
          :nonleafselectable="false"
          :allowClear="false"
          placeholder="请选择"
          :loadComplete="loadComplete"
          @dropdownVisibleChange="dropdownVisibleChange($event, item.dimCode)"
        />
      </yn-form-item>
      <yn-form-item
        label="对账类型"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <yn-radio-group
          v-decorator="[
            'reconciliationType',
            { initialValue: reconciliationType }
          ]"
          :options="plainOptions"
          @change="handleChange"
        />
      </yn-form-item>
      <template v-if="hideMergeGrup">
        <yn-form-item
          class="organization-item"
          label="组织"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          :validateStatus="entityValidateStatus"
          :help="entityHelp"
        >
          <show-dim-list-input
            v-decorator="['entity']"
            class="queryRangeItem"
            :dimInfo="dimInfoEntiy"
            :ifEmptySelectedAll="false"
            @change="setData"
          />
        </yn-form-item>
        <yn-form-item
          label="往来公司"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          :validateStatus="icpValidateStatus"
          :help="icpHelp"
        >
          <show-dim-list-input
            v-decorator="['icp']"
            class="queryRangeItem"
            :dimInfo="dimInfoIcp"
            :ifEmptySelectedAll="false"
            analyticExpName="parseICPExp"
            :tipsWord="tipsWord"
            @change="setData"
          />
        </yn-form-item>
      </template>
      <template v-else>
        <yn-form-item
          label="合并组"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <asyn-select-dimMember
            v-decorator="[
              'mergeGroup',
              {
                initialValue: mergeGroupVal,
                rules: [
                  {
                    required: true,
                    message: '请选择合并组'
                  }
                ]
              }
            ]"
            forceRender
            dimCode="Scope"
            class="dimSelectTree"
            :nonleafselectable="false"
            :needPermission="true"
            :allowClear="false"
            :loadComplete="loadComplete"
            @changeVal="e => (mergeGroupVal = e)"
            @dropdownVisibleChange="memoryMergingGroup"
          >
            />
          </asyn-select-dimMember>
        </yn-form-item>
      </template>
    </yn-form>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-radio/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-button/";
import cloneDeep from "lodash/cloneDeep";
import ShowDimListInput from "@/components/hoc/ShowDimListInput.vue";
import DIM_INFO from "@/constant/dimMapping";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
import reconciliationService from "@/services/reconciliation";
import { TRANSFER_TIPS_WORD } from "@/constant/common.js";
const DIM_INFO_ENTITY = {
  permissionFilter: true,
  dimCode: "Entity",
  dimId: DIM_INFO.Entity,
  dimName: "组织",
  selectedItem: []
};
const DIM_INFO_ICP = {
  dimCode: "ICP",
  dimId: DIM_INFO.ICP,
  dimName: "往来公司",
  selectedItem: []
};
export default {
  name: "QueryRange",
  components: {
    ShowDimListInput,
    AsynSelectDimMember
  },
  props: {
    queryRangeData: {
      type: Object,
      default() {
        return {};
      }
    },
    title: {
      type: String,
      default: ""
    },
    isDrawer: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dimInfo: [],
      visible: false,
      reconciliationType: 0, // 对账类型 默认0单体公司对账
      mergeGroupVal: "", // 合并组默认值
      form: this.$form.createForm(this, {
        name: "queryRange"
      }),
      labelCol: {
        span: 4,
        sm: 6
      },
      wrapperCol: {
        span: 18,
        sm: 16
      },
      pov: {},
      formItem: [
        {
          name: "版本",
          dimCode: "Version",
          treeData: [],
          val: ""
        },
        {
          name: "期间",
          dimCode: "Period",
          treeData: [],
          val: ""
        },
        {
          name: "年",
          dimCode: "Year",
          treeData: [],
          val: ""
        },
        {
          name: "报告币种",
          dimCode: "ReportingCurrency",
          treeData: [],
          val: ""
        }
      ],
      plainOptions: [
        {
          label: "单体公司对账",
          value: 0
        },
        {
          label: "子集团对账",
          value: 1
        }
      ],
      hideMergeGrup: true,
      scopeData: [],
      dimInfoEntiy: cloneDeep(DIM_INFO_ENTITY),
      dimInfoIcp: cloneDeep(DIM_INFO_ICP),
      cacheTreeDataObj: {},
      loadNum: 0,
      icpValidateStatus: "success",
      entityValidateStatus: "success",
      tipsWord: TRANSFER_TIPS_WORD.restrictedCompany()
    };
  },
  computed: {
    entityHelp() {
      const obj = {
        success: "",
        error: "请选择组织"
      };
      return obj[this.entityValidateStatus];
    },
    icpHelp() {
      const obj = {
        success: "",
        error: "请选择往来公司"
      };
      return obj[this.icpValidateStatus];
    }
  },
  watch: {
    title: {
      async handler(newVal) {
        this.loadNum = 0;
        this.icpValidateStatus = "success";
        this.entityValidateStatus = "success";
        await this.getUserMemory();
        if (!Object.keys(this.queryRangeData).length) {
          await this.getPOV();
        } else {
          this.echoDefaultVal();
        }
        this.$emit("closeLoading");
      }
    },
    loadNum(newVal) {
      if (newVal === 4) {
        this.$emit("closeLoading");
      }
    }
  },
  async created() {
    await this.getUserMemory();
    if (!Object.keys(this.queryRangeData).length) {
      await this.getPOV();
    } else {
      this.echoDefaultVal();
    }
    this.$emit("closeLoad");
  },
  methods: {
    loadComplete() {
      this.loadNum += 1;
    },
    memoryMergingGroup(isShow) {
      if (!isShow) {
        const value = this.form.getFieldsValue();
        this.saveUserMemory({
          scope: value.mergeGroup
        });
      }
    },
    // 保存记忆
    saveUserMemory(params) {
      reconciliationService("userMemory", { type: "save", ...params });
    },
    // 获取记忆数据
    async getUserMemory() {
      if (this.isDrawer) return;
      await reconciliationService("userMemory", { type: "request" }).then(
        res => {
          const { data } = res.data || {};
          const { reportCurrency, icp, entity, reconciliationType, scope } =
            data || {};
          const reconciliationTypeVal = reconciliationType || 0;
          this.reconciliationType = reconciliationTypeVal;
          this.hideMergeGrup = !reconciliationType;
          // 回显 报告币种、对账类型、组织、往来公司、合并组 数据
          this.formItem[3].val = reportCurrency;
          const dimInfoEntiy = this.formatEchoData(entity);
          const dimInfoIcp = this.formatEchoData(icp);
          this.$set(this.dimInfoEntiy, "selectedItem", dimInfoEntiy);
          this.$set(this.dimInfoIcp, "selectedItem", dimInfoIcp);
          this.mergeGroupVal = scope;
          const setFieldsValue = {
            reconciliationType: reconciliationTypeVal,
            ReportingCurrency: reportCurrency
          };
          if (!reconciliationType) {
            Object.assign(setFieldsValue, {
              entity: this.dimInfoEntiy,
              icp: this.dimInfoIcp
            });
          }
          this.form.setFieldsValue(setFieldsValue);
        }
      );
    },
    formatEchoData(data = []) {
      return data.map(item => {
        const {
          dimMemberName,
          objectId,
          dimCode,
          dimMemberCode,
          dimMemberDbCode,
          dimMemberShared
        } = item;
        return {
          type: "member",
          memberType: 1,
          memberTypeValue: "self",
          value: objectId,
          shardim: dimMemberShared,
          dbCodeIndex: "",
          dimCode,
          dimMemberCode,
          dimMemberDbCode,
          dimMemberName,
          key: `${objectId}_self`,
          label: dimMemberName,
          objectId: `${objectId}_self`,
          title: `成员(${dimMemberName})`
        };
      });
    },
    // 回显上次填写的值
    echoDefaultVal() {
      const {
        reconciliationType,
        icp,
        entity,
        mergeGroup
      } = this.queryRangeData;
      this.reconciliationType = reconciliationType;
      this.hideMergeGrup = !reconciliationType;
      this.dimInfoEntiy = this.hideMergeGrup
        ? entity
        : cloneDeep(DIM_INFO_ENTITY);
      this.dimInfoIcp = this.hideMergeGrup ? icp : cloneDeep(DIM_INFO_ICP);
      this.$set(this, "dimInfoEntiy", this.dimInfoEntiy);
      this.$set(this, "dimInfoIcp", this.dimInfoIcp);
      if (this.hideMergeGrup) {
        this.$nextTick(() => {
          this.form.setFieldsValue({
            entity: this.dimInfoEntiy,
            icp: this.dimInfoIcp
          });
        });
      }
      this.mergeGroupVal = mergeGroup;
      this.setDefaultVal(this.queryRangeData);
    },
    convertData(data, keyMappingName) {
      const loop = data => {
        data.map(item => {
          const { children, id, name } = item;
          if (keyMappingName) {
            item.key = item[keyMappingName];
          } else {
            item.key = id;
          }
          item.label = name;
          if (children && children.length) {
            loop(children);
          }
        });
      };
      loop(data);
      return data;
    },
    toUpperCaseWord(word) {
      if (!word) return word;
      return word.slice(0, 1).toUpperCase() + word.slice(1).toLowerCase();
    },
    async getPOV() {
      this.pov = {
        Version: "",
        Year: "",
        Period: "",
        ReportingCurrency: ""
      };
      await this.selectUserPov(data => {
        for (const key in data) {
          this.pov[this.toUpperCaseWord(key)] = data[key]
            ? data[key].memberId
            : null;
        }
        this.setDefaultVal(this.pov);
      });
    },
    setDefaultVal(data) {
      this.formItem.map((item, index) => {
        const { dimCode } = item;
        if (data[dimCode]) {
          this.$set(this.formItem[index], "val", data[dimCode]);
        }
      });
    },
    openConfig() {
      this.$emit("openDrawer", "edit");
    },
    handleChange(e) {
      const { target } = e;
      this.hideMergeGrup = !target.value;
      this.icpValidateStatus = "success";
      this.entityValidateStatus = "success";
      if (this.hideMergeGrup) {
        this.$nextTick(() => {
          this.form.setFieldsValue({
            entity: this.dimInfoEntiy,
            icp: this.dimInfoIcp
          });
        });
      }
      // 保存记忆
      this.saveUserMemory({
        reconciliationType: target.value
      });
    },
    setData(data) {
      const { dimCode, selectedItem } = data;
      let propsName = "dimInfoEntiy"; // dimInfoIcp
      if (dimCode === "ICP") {
        propsName = "dimInfoIcp";
      }
      const ids = selectedItem.map(item => item.value);
      this.saveUserMemory({
        [dimCode.toLocaleLowerCase()]: ids.toString()
      });
      this[`${dimCode.toLocaleLowerCase()}ValidateStatus`] = "success";
      this.$set(this[propsName], "selectedItem", selectedItem);
    },
    reset() {
      this.icpValidateStatus = "success";
      this.entityValidateStatus = "success";
      this.hideMergeGrup = true;
      this.$set(this.dimInfoEntiy, "selectedItem", []);
      this.$set(this.dimInfoIcp, "selectedItem", []);
      this.mergeGroupVal = "";
      this.formItem.forEach(item => {
        item.val = "";
      });
      this.$set(this, "formItem", this.formItem);
      this.form.resetFields();
      this.form.setFieldsValue({
        reconciliationType: 0
      });
    },
    dropdownVisibleChange(isShow, propName, index) {
      if (!isShow) {
        // 报告币种不走pov 接口 后续会走其他接口做记忆操作
        const formFields = this.form.getFieldsValue();
        if (propName === "ReportingCurrency") {
          this.saveUserMemory({
            reportCurrency: formFields.ReportingCurrency
          });
          return;
        }
        const params = {};
        this.formItem.map((item, index) => {
          params[item.dimCode.toLowerCase()] = formFields[item.dimCode];
        });
        this.saveOrUpdateUserPov(params);
      }
    }
  }
};
</script>
<style lang="less" scoped>
div.queryRangeDrawer {
  width: 23.875rem;
}
.queryRange {
  width: 30.5rem;
  padding: @yn-padding-xl 0 @rem8 0.25rem;
  .openConfig {
    position: absolute;
    right: @yn-margin-xxxl;
    cursor: pointer;
  }
  /deep/.queryRangeItem {
    width: 100%;
    background-color: @yn-body-background;
  }
  .organization-item {
    margin-bottom: @rem16;
  }
}
</style>
