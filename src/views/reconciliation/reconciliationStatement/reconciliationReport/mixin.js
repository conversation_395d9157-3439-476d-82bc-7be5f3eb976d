import reconciliationService from "@/services/reconciliation";
import {
  AMOUNT_IDENT,
  CODE_NAME_MAPPING,
  RECONCILIATION_CUBE_CODE
} from "./constant.js";
import DIM_INFO from "@/constant/dimMapping";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import { FRONTEND } from "@/config/SETUP";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
export default {
  computed: {
    drillMenuMap() {
      return this.drillMenuData.reduce((obj, cur) => {
        obj[cur.objectId] = cur;
        return obj;
      }, {});
    },
    drillMenu() {
      const groupMap = {};
      this.drillMenuData.forEach(item => {
        const {
          relName,
          relGroupName,
          pageOpenMode,
          relGroupId,
          objectId: key
        } = item;
        const menuItem = {
          groupName: relGroupName,
          pageOpenMode,
          key
        };
        if (groupMap[relGroupId]) {
          groupMap[relGroupId].children.push({
            title: relName,
            ...menuItem
          });
        } else {
          groupMap[relGroupId] = {
            ...menuItem,
            title: relGroupName,
            children: [{ title: relName, ...menuItem }]
          };
        }
      });
      return Object.values(groupMap);
    }
  },
  data() {
    return {
      modalTitle: "",
      modalUrl: "",
      showDrillPage: false
    };
  },
  mounted() {
    this.attachResize();
  },
  destroyed() {
    this.observer.disconnect();
  },
  methods: {
    attachResize() {
      this.observer = new ResizeObserver(() => {
        setTimeout(
          self => {
            try {
              const { height } = self.getTableContStyle();
              document.querySelector(
                ".reportTableCont"
              ).style.height = `${height}px`;
              self.$refs.reportGrid.$refs.simpleGrid.updateSettings();
            } catch (e) {}
          },
          500,
          this
        );
      });
      this.observer.observe(document.querySelector(".reportCont"));
    },
    getTableContStyle() {
      const { offsetHeight: contH } = document.querySelector(".reportCont");
      const { offsetHeight: titleH } = document.querySelector(
        ".reportCont .title "
      );
      return { height: contH - titleH };

      // reportTable
    },
    bodyClick(event) {
      const isClickDrillBtn = this.isClickDrillBtn(event);
      const className = event.target.className;
      if (
        !isClickDrillBtn &&
        typeof className === "string" &&
        className.indexOf("drill-btn") === -1 &&
        className.indexOf("ant-menu") === -1
      ) {
        this.showDrillMenu = false;
      } else {
        if (typeof className === "object") return;
        const tempArr = className
          .split(" ")
          .filter(item => item.indexOf("cellInfo") !== -1);
        if (tempArr.length) {
          const [row, col] = tempArr[0].split("_").slice(1);
          const cellContext = this.$refs.reportGrid.$refs.simpleGrid.getCellMeta(
            row,
            col
          );
          this.certificateDrill(
            event,
            cellContext,
            className.indexOf("drill-btn") !== -1 ? "subtotal" : ""
          );
        }
      }
    },
    isClickDrillBtn(event) {
      let res = false;
      const { className } = event.target;
      if (!className || typeof className !== "string") return;
      if (
        !this.$refs.reportGrid ||
        !this.$refs.reportGrid.$refs ||
        !this.$refs.reportGrid.$refs.gridCont
      ) {
        return;
      }
      if (!this.gridX) {
        this.gridX = this.$refs.reportGrid.$refs.gridCont
          .querySelector(".simple-grid-wrapper")
          .getBoundingClientRect().x;
      }
      // 金额单元格 凭证钻取
      if (className.indexOf("leafNodeAmount") !== -1) {
        const { clientX } = event;
        const { left, width } = this.$refs.reportGrid.getPopPosByTd(
          event.target
        );
        const hotspotArea = width - (clientX - this.gridX - left);
        // 热区范围
        if (hotspotArea > 0 && hotspotArea < 16) {
          res = true;
        }
      }
      return res;
    },
    async certificateDrill(event, cellContext, type) {
      const dimObj = await this.getDrillParams(cellContext, type);
      let params = [[]];
      this.clickCell = cellContext;
      Object.values(dimObj).forEach(item => {
        const { memberId } = item;
        const memberIdArr = memberId.split(",");
        if (memberIdArr.length === 1) {
          params.forEach(arr => {
            arr.push(memberIdArr[0]);
          });
        } else {
          const res = [];
          params.forEach(arr => {
            memberIdArr.forEach(item => {
              const tempArr = [...arr, item];
              res.push(tempArr);
            });
          });
          params = res;
        }
      });
      // 获取钻取路径信息
      reconciliationService("getFormRelationCell", {
        memberIds: params
      }).then(res => {
        const { data } = res;
        if (data.length) {
          this.showDrillMenu = true;
          this.drillMenuData = res.data;
          this.$nextTick(() => {
            this.setDrillMenuStyle(event, cellContext);
          });
        } else {
          UiUtils.warningMessage("无可钻取的路径，请配置关联跳转规则");
        }
      });
      this.drillParams = dimObj;
    },
    async getDrillParams(cellContext, type) {
      const dimInfo = await this.getDimInfoByCube();
      const params = this.getDrillCommonParams();
      await this.setFixedCellIdsToParams(params, cellContext, type);
      if (type !== "subtotal") {
        const { colName } = cellContext;
        const columnInfoMap = this.getColumnInfoMap();
        // 当前单元格对应的审计线索成员信息录入params
        const { id, title } = columnInfoMap[colName];
        if (id) {
          const memberName = this.getAmountMemberName(title);
          params["Audittrail"] = {
            dimCode: "Audittrail",
            dimId: "",
            memberName: [memberName],
            memberId: [id]
          };
        }
      }
      return dimInfo.map(item => {
        const { dimId, dimCode, dimName } = item;
        const tempObj =
          dimCode === "Account" ? params["AccountGroup"] : params[dimCode];
        return {
          dimId,
          dimName,
          memberId: [...new Set(tempObj.memberId)].join(","),
          memberName: [...new Set(tempObj.memberName)].join("、")
        };
      });
    },
    getAmountMemberName(name = "") {
      return name.replace(/^(组织|往来公司)/, "").replace(/金额$/, "");
    },
    // 钻取的普通参数（1.对账配置筛选区域维度信息2.对账报告页面维信息）
    getDrillCommonParams() {
      const titleMap = this.titleMap;
      const titleInfo = this.titleInfo;
      const titleInfoMap = titleInfo.reduce((res, cur) => {
        const [item] = Object.entries(cur);
        const [key, val] = item;
        res[key] = val;
        return res;
      }, {});
      const params = Object.keys(titleMap).reduce((res, key) => {
        res[key] = {
          dimCode: key,
          dimId: "",
          dimName: CODE_NAME_MAPPING[key],
          memberId: [titleMap[key]],
          memberName: [titleInfoMap[CODE_NAME_MAPPING[key]]]
        };
        return res;
      }, {});
      const filterMembers = this.reconciliationConfig.filterMembers;
      filterMembers.forEach(item => {
        const { dimCode, dimMemberId, dimId, dimName, memberName } = item;
        params[dimCode] = {
          dimCode,
          dimId,
          dimName,
          memberId: [dimMemberId],
          memberName: [memberName]
        };
      });
      // 合并组 默认是G_NONE
      params["Scope"] = {
        dimId: "",
        dimCode: "Scope",
        dimName: "合并组",
        memberId: [DIM_INFO.SCOPE_G_NONE],
        memberName: ["不区分合并组"]
      };
      return params;
    },
    // 获取对账cube的维度信息
    async getDimInfoByCube() {
      const {
        data: { data: dimInfo }
      } = await reconciliationService("reconciliationDrillDim");
      return dimInfo;
    },
    getColumnInfoMap() {
      const { columnInfo } = this.tableInfo;
      const columnInfoMap = {};
      columnInfo.forEach(item => {
        const { key } = item;
        columnInfoMap[key] = item;
      });
      return columnInfoMap;
    },
    // 获取固定列（金额列之前的列除序号列之外）
    getFixedCellInfo() {
      const fixedCol = [];
      const { columns } = this;
      for (let i = 0, LEN = columns.length; i < LEN; i++) {
        const { dataIndex } = columns[i];
        if (dataIndex.indexOf(AMOUNT_IDENT) !== -1) break;
        if (dataIndex !== "indexes") {
          fixedCol.push(dataIndex);
        }
      }
      return fixedCol;
    },
    async setFixedCellIdsToParams(params, cellContext, type) {
      const { rowId: parentId } = cellContext;
      const fixedCol = this.getFixedCellInfo();
      if (type === "subtotal") {
        const { data } = await this.requestDataByOffset({
          parentId,
          limit: 10000,
          offset: 0
        });
        fixedCol.forEach(key => {
          params[key] = {
            dimCode: key,
            dimId: "",
            memberId: [],
            memberName: []
          };
        });
        data.forEach(item => {
          const { attributes } = item;
          fixedCol.forEach(fixedCellKey => {
            const { id, name } = attributes[fixedCellKey];
            const { memberName, memberId } = params[fixedCellKey];
            memberId.push(id);
            memberName.push(name);
          });
        });
      } else {
        const rowData = this.getRowDataById(parentId);
        fixedCol.forEach(dimCode => {
          const { id, name } = rowData.attributes[dimCode];
          params[dimCode] = {
            dimCode: dimCode,
            dimId: "",
            memberId: [id],
            memberName: [name]
          };
        });
      }
    },
    setDrillMenuStyle(event, cellContext) {
      const { clientX } = event;
      const { top } = this.$refs.reportGrid.getPopPosByTd(cellContext.td);
      const {
        left: gridLeft,
        top: gridTop,
        width: gridWidth
      } = this.$refs.reportGrid.$refs.gridCont
        .querySelector(".simple-grid-wrapper")
        .getBoundingClientRect();
      let menuLeft = clientX;
      const menuWidth = this.$refs.drillMenu.offsetWidth;
      if (menuLeft + menuWidth + gridLeft > gridWidth + gridLeft) {
        menuLeft -= menuWidth;
      }
      this.drillMenuStyle = {
        left: menuLeft + "px",
        top: gridTop + top + "px"
      };
    },
    scrollTable() {
      this.showDrillMenu = false;
    },
    handleMenuClick({ key }) {
      const { pageOpenMode, objectId: id, relName: title } = this.drillMenuMap[
        key
      ];
      const url = this.getDrillPageUrl(id, pageOpenMode);
      pageOpenMode === "_self"
        ? this.openDrillModal(title, url)
        : this.openDrillTab(title, url, id);
      this.showDrillMenu = false;
    },
    // 获取钻取页面路径
    getDrillPageUrl(objectId, pageOpenMode) {
      const urlParms = encodeURIComponent(
        JSON.stringify({
          relationId: objectId,
          dimsInfo: this.drillParams
        })
      );
      const serviceName = UrlUtils.getQuery("serviceName");
      const menuId = UrlUtils.getQuery("menuId");
      const appId = UrlUtils.getQuery("appId");
      const token = UrlUtils.getQuery("TOKEN");
      const url = `${
        FRONTEND.MDD_FRONT_VIEW_URL
      }dataDrillDownDetails?dmDrillParam=${urlParms}&cubeCode=${encodeURIComponent(
        RECONCILIATION_CUBE_CODE
      )}`;
      if (pageOpenMode === "_self" || this.selfTab) {
        return `${url}&serviceName=${serviceName}&menuId=${menuId}&appId=${appId}&TOKEN=${token}`;
      } else {
        return url;
      }
    },
    //  打开钻取结果模态框
    openDrillModal(title, url) {
      this.modalTitle = title;
      this.modalUrl = url;
      this.showDrillPage = true;
    },
    // 打开钻取结果页签
    openDrillTab(title, url, id) {
      const { row, col } = this.clickCell;
      this.newtabMixin({
        id: id + row + col,
        uri: url,
        params: {
          isAbsolutePath: true // 根据此参数 判断是否需要添加baseUrl
        },
        title,
        noKeepAlive: false, // 是否需要缓存
        router: "systemTab" // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
      });
    },
    closeDrillPageModal() {
      this.showDrillPage = false;
    }
  }
};
