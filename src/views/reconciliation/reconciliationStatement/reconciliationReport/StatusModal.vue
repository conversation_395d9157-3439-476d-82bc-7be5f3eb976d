<template>
  <yn-modal
    :title="title"
    :visible="visible"
    wrapClassName="status-modal"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template slot="footer">
      <yn-button key="back" @click="handleCancel">取消</yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="confirmLoading"
        @click="handleOk"
      >
        确定
      </yn-button>
    </template>
    <div>
      <p class="status-item">
        <span class="label">组织方: </span>
        <span class="value">{{ data.Entity }} </span>
      </p>
      <p class="status-item">
        <span class="label">往来方: </span>
        <span class="value">{{ data.ICP }} </span>
      </p>
    </div>
    <yn-divider />
    <yn-form :form="form">
      <yn-form-item
        label="对账协同状态"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
      >
        <yn-select
          v-decorator="[
            'status',
            {
              rules: [{ required: true, message: '请输入' }]
            }
          ]"
          placeholder="请选择"
          :options="options"
        />
      </yn-form-item>
    </yn-form>
  </yn-modal>
</template>
<script>
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-form/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import reconciliationService from "@/services/reconciliation";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ""
    },
    coordinationStatus: {
      type: Array,
      default: () => []
    },
    taskId: {
      // 对账任务id
      type: String,
      default: ""
    },
    reconciliationId: {
      // 对账报表的id
      type: String,
      default: ""
    },
    statusType: {
      type: String, // 组织方对账状态:entityReconciliationStatus, 往来方对账状态:icpReconciliationStatus;
      default: ""
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      labelCol: {
        span: 6
      },
      wrapperCol: {
        span: 18
      },
      form: this.$form.createForm(this, {
        name: "form"
      }),
      options: [],
      confirmLoading: false
    };
  },
  watch: {
    visible(value) {
      value && this.getReconciliationCoordinationSetting();
    }
  },
  methods: {
    async handleOk(e) {
      this.form.validateFields(async (err, value) => {
        if (err) return;
        this.confirmLoading = true;
        // TODO save
        await this.updateCoordinationStatus(value);
        this.$emit("ok");
        this.handleCancel();
        this.confirmLoading = false;
      });
    },
    async getReconciliationCoordinationSetting() {
      this.options = this.coordinationStatus.map(item => ({
        label: item.name,
        value: item.id
      }));
      this.$nextTick(() => {
        this.form.setFieldsValue({
          status:
            this.options && this.options.length > 0 ? this.options[0].value : ""
        });
      });
    },
    async updateCoordinationStatus(value) {
      const params = {
        taskId: this.taskId,
        reconciliationId: this.reconciliationId,
        statusType: this.statusType,
        statusId: value.status, // 选择的对账状态字段的id
        nodeIds: [
          this.data.id // 选择的小计行id 集合
        ]
      };
      await reconciliationService("updateCoordinationStatus", params);
      UiUtils.successMessage("状态调整成功");
    },
    handleCancel(e) {
      this.$emit("update:visible", false);
    }
  }
};
</script>
<style lang="less">
.status-modal {
  .status-item {
    .label {
      color: @yn-text-color-secondary;
    }
    .value {
      margin-left: 0.625rem;
      font-size: 0.875rem;
      font-weight: normal;
      line-height: 1.375rem;
      letter-spacing: 0px;
      color: @yn-text-color;
    }
  }
  .ant-modal-body {
    padding-bottom: 0;
  }
}
</style>
