<template>
  <yn-spin :spinning="spinning" class="reconciliationParamsCont cs-container">
    <div v-if="!isHide" class="reportCont">
      <Title
        :fromPage="fromPage"
        :spinning.sync="spinning"
        :reconciliationInfo="reconciliationInfo"
        :reportId="tableInfo.reportId"
        :titleInfo="titleInfo"
        :tableInfo="tableInfo"
        :isExpandAll.sync="isExpandAll"
        :taskId="taskId"
        :coordinationStatus="coordinationStatus"
        :changeTableDataByParams="changeTableDataByParams"
        :frozenColName="frozenColName"
        :frozenLeftIndex="frozenLeftIndex"
        :refreshId="refreshId"
        @changeCellType="changeCellType"
        @handleRefresh="handleRefresh"
        @openDrawer="setDrawerStatus"
        @setFrozen="setFrozen"
        @columnZeroStateChange="handleColumnZeroStateChange"
      />
      <reconciliatio-table
        ref="table"
        :tableInfo="tableInfo"
        :titleInfo="titleInfo"
        :cellType="cellType"
        :taskId="taskId"
        :refreshId="refreshId"
        :isExpandAll="isExpandAll"
        :spinning.sync="spinning"
        :coordinationStatus="coordinationStatus"
        :reconciliationInfo="reconciliationInfo"
        :frozenColName.sync="frozenColName"
        :frozenLeftIndex.sync="frozenLeftIndex"
        :reconciliationConfig="reconciliationConfig"
        :isManuallyClick.sync="isManuallyClick"
        :titleMap="titleMap"
        :isColumnZeroActive="isColumnZeroActive"
        @refresh="refreshTable"
      />
    </div>
    <reconcoilation-config
      v-if="reconciliationConfigVisible"
      :drawerVisible="reconciliationConfigVisible"
      operationType="edit"
      :isReportPage="true"
      :selectTreeNodeInfo="selectTreeNode"
      @closeDrawer="setDrawerStatus('config', false)"
      @saveConfigCb="saveConfigCb"
    />
    <yn-drawer
      v-if="reconciliationQueryVisible"
      :title="title"
      placement="right"
      :width="440"
      :visible="true"
      :maskClosable="false"
      wrapClassName="drawerReconciliationQuery"
      @close="setDrawerStatus('queryParam', false)"
    >
      <reconciliation-query
        :title="title"
        :isDrawer="true"
        :echoData="requeryParams"
        :selectTreeNodeInfo="selectTreeNode"
        class="drawerQuery"
        @generateReport="generateReport"
        @generateReportBefore="generateReportBefore"
        @generateReportError="generateReportError"
      />
    </yn-drawer>
    <process-reconciliation-reportModal
      v-if="isShowModal"
      :hasAuthority="hasAuthority"
      :currTreeNodeInfo="selectTreeNode"
      operationType="saveAs"
      :reconciliationConfig="reconciliationConfig"
      @closeModal="closeModal"
    />
  </yn-spin>
</template>
<script>
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-drawer/";
import Title from "@/views/reconciliation/reconciliationStatement/reconciliationReport/Title.vue";
import ReconciliationQuery from "../reconciliationQuery";
import ReconcoilationConfig from "../reconciliationConfig";
import ReconciliatioTable from "@/views/reconciliation/reconciliationStatement/reconciliationReport/ReconciliationGrid.vue";
import { formatReconciliationParams } from "../utils";
import reconciliationService from "@/services/reconciliation";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import { mapActions, mapState } from "vuex";
import { pollTaskStatus } from "@/utils/common";
import { PAGE_SIZE } from "./constant.js";
import ProcessReconciliationReportModal from "../layoutLeft/ProcessReconciliationReport.vue";
const DRAWER_INFO = {
  queryParam: "reconciliationQueryVisible",
  config: "reconciliationConfigVisible"
};
export default {
  name: "ReconciliatioReport",
  components: {
    Title,
    ReconciliatioTable,
    ReconciliationQuery,
    ReconcoilationConfig,
    ProcessReconciliationReportModal
  },
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  provide() {
    return {
      showSaveAsModal: this.openSaveAsModal
    };
  },
  data() {
    return {
      reconciliationConfigVisible: false,
      reconciliationQueryVisible: false,
      tableInfo: {},
      titleInfo: [],
      coordinationStatus: [],
      reconciliationInfo: undefined,
      cellType: "name",
      spinning: true,
      isHide: false,
      isExpandAll: false,
      requeryParams: {},
      selectTreeNode: {},
      title: "",
      taskId: "", // 异步任务id
      fromPage: "", // 来源，合并任务（merge_search）跳转过来的不显示修改对账配置、查询、刷新等按钮
      isShowModal: false,
      currTreeNodeInfo: {},
      reconciliationConfig: {}, // 对账配置 信息
      frozenColName: "",
      frozenLeftIndex: 3,
      refreshId: "", // 更改 小数位数、匹配项会调用接口，接口返回此值
      isManuallyClick: true,
      titleMap: {}, // 对账查询参数 版本、年、期间、转换货币
      isColumnZeroActive: false // 列消零状态
    };
  },
  computed: {
    ...mapState({
      batchType: state => state.reconciliation.batchType,
      hasAuthority: state => state.reconciliation.hasAuthority,
      menuMapObj: state => state.common.menuMapObj,
      configObj: state => state.reconciliation.reconciliationConfigObj,
      mainLoading: state => state.reconciliation.mainLoading
    })
  },
  watch: {
    //  tab 组件 会传入params
    "params.params": {
      handler(newVal) {
        if (this.selfTab && newVal.taskId !== this.taskId) {
          this.setInitData(newVal);
          const selectTreeNode = newVal.selectTreeNode;
          this.coordinationStatus =
            newVal.reconciliationConfigObj[
              selectTreeNode.id
            ].coordinationStatus;
          this.title = this.params.title;
          this.fromPage = newVal.fromPage;
        }
      },
      immediate: true,
      deep: true
    },
    batchType: {
      handler() {
        this.getStatusMatch();
      }
    }
  },
  created() {
    this.getPublicAuth();
    this.getAuthSet();
  },
  mounted() {
    if (!this.selfTab) {
      const { title, selectTreeNode, ...params } = this.getTabParamsMixin();
      this.title = title;
      this.fromPage = params.fromPage;
      this.setInitData({ ...params, selectTreeNode });
    }
  },
  methods: {
    ...mapActions("reconciliation", [
      "asyncGenerateReport",
      "getPublicAuth",
      "getAuthSet"
    ]),
    openSaveAsModal(reconciliationConfig) {
      this.reconciliationConfig = reconciliationConfig;
      this.isShowModal = true;
    },
    closeModal(type, treeNodeId) {
      if (type === "saveAs_ok") {
        // 另存为 成功后，激活对账列表页签，并重新选择树节点
        this.activationTab(treeNodeId);
      }
      this.isShowModal = false;
    },
    activationTab(treeNodeId) {
      const PATH = "/reconciliation/reportList";
      // 激活平台页签，并 刷新 选中传入的treeNodeId
      const menuId = this.getMenuIdByPath(PATH);
      this.newtabMixin({
        id: menuId,
        uri: PATH,
        params: {
          selectTreeId: treeNodeId,
          tabId: menuId
        },
        noKeepAlive: false, // 是否需要缓存
        router: "reconciliationReport" || "systemTab" // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
      });
      // 关闭对账配置抽屉
      this.reconciliationConfigVisible = false;
    },
    getMenuIdByPath(path) {
      return Object.keys(this.menuMapObj).filter(key => {
        const { uri = "" } = this.menuMapObj[key] || {};
        return uri.indexOf(path) !== -1;
      })[0];
    },
    setInitData(obj) {
      const { requeryParams, taskId } = obj;
      this.taskId = taskId;
      // 当任务Id 发生变化以后，需要清空刷新Id，后续表格展开
      this.refreshId = "";
      this.isExpandAll = false;
      this.getReconciliationByTaskId(taskId);
      this.requeryParams = requeryParams || {};
      this.selectTreeNode = obj.selectTreeNode;
    },
    async getStatusMatch() {
      this.spinning = true;
      await reconciliationService("getReconciliationByTaskId", {
        taskId: this.refreshId || this.taskId,
        limit: PAGE_SIZE,
        offset: 0
      })
        .then(async res => {
          const { reportPageResult, ...tableInfo } = res.data.data;
          // 合并任务里面 打开对账报告，目前需要从这里取到title值
          Object.assign(tableInfo, reportPageResult);
          this.tableInfo = tableInfo;
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    async refreshTable() {
      reconciliationService("expandChildren", {
        taskId: this.refreshId || this.taskId,
        limit: PAGE_SIZE,
        parentId: -1,
        offset: 0
      }).then(result => {
        this.tableInfo = { ...this.tableInfo, ...result.data.data };
        this.isExpandAll = false;
      });
    },
    async getReconciliationByTaskId(taskId) {
      await reconciliationService("getReconciliationByTaskId", {
        taskId,
        limit: PAGE_SIZE,
        offset: 0
      })
        .then(async res => {
          const {
            titleInfo,
            reconciliationInfo,
            reportPageResult,
            titleMap,
            ...tableInfo
          } = res.data.data;
          this.titleInfo = titleInfo;
          this.titleMap = titleMap;
          this.reconciliationInfo = reconciliationInfo;
          // 合并任务里面 打开对账报告，目前需要从这里取到title值
          this.title = reconciliationInfo.name;
          Object.assign(tableInfo, reportPageResult);
          this.tableInfo = tableInfo;
          this.isHide = false;
          this.frozenColName = "";
          this.frozenLeftIndex = 3;
          await this.requestConfigData(reconciliationInfo.reconciliationId);
          this.coordinationStatus = this.reconciliationConfig.coordinationStatus;
        })
        .finally(() => {
          this.spinning = false;
          this.isManuallyClick = true;
        });
    },
    // 获取对账配置信息
    async requestConfigData(id) {
      await reconciliationService("getReconciliationConfigById", id).then(
        res => {
          this.reconciliationConfig = res.data;
        }
      );
    },
    async getReportInfo(displaySettingsData, queryRangeData) {
      try {
        const requeryParams = formatReconciliationParams(
          displaySettingsData,
          queryRangeData,
          this.selectTreeNode
        );
        // 发起异步任务
        const taskId = await this.asyncGenerateReport(requeryParams);
        this.taskId = taskId;
        this.refreshId = "";
        // 轮询获取状态
        pollTaskStatus(
          taskId,
          () => {
            // 异步任务完成，查询结果
            this.getReconciliationByTaskId(taskId);
          },
          () => {
            this.spinning = false;
            UiUtils.errorMessage("查询对账报告失败");
          }
        );
      } catch (e) {
        this.spinning = false;
      }
    },
    changeCellType(cellType) {
      this.cellType = cellType;
      if (this.isExpandAll) {
        this.isManuallyClick = false;
        this.isExpandAll = false;
      }
    },
    // 刷新事件
    async handleRefresh(isManualTrigger, setting) {
      if (isManualTrigger) {
        this.spinning = true;
      }
      this.isManuallyClick = !this.isExpandAll;
      this.isExpandAll = false;
      const { displaySettingData, queryRangeData } = this.requeryParams;
      await this.getReportInfo(
        Object.assign({}, displaySettingData, setting || {}),
        queryRangeData
      );
    },
    async setDrawerStatus(drawerType, status) {
      if (this.mainLoading) return;
      this[DRAWER_INFO[drawerType]] = status;
    },
    generateReportBefore() {
      this.spinning = true;
    },
    generateReportError() {
      this.spinning = false;
    },
    generateReport(obj) {
      this.spinning = true;
      this.setInitData(obj);
      this.reconciliationQueryVisible = false;
    },
    changeTableDataByParams(params) {
      this.spinning = true;
      params.limit = PAGE_SIZE;
      params.offset = 0;
      reconciliationService("reconciliationRefresh", params)
        .then(res => {
          const {
            titleInfo,
            reconciliationInfo,
            refreshId,
            reportPageResult,
            ...tableInfo
          } = res.data.data;
          this.titleInfo = titleInfo;
          this.reconciliationInfo = reconciliationInfo;
          // 合并任务里面 打开对账报告，目前需要从这里取到title值
          this.title = reconciliationInfo.name;
          Object.assign(tableInfo, reportPageResult);
          this.tableInfo = tableInfo;

          this.isHide = false;
          this.refreshId = refreshId;
          this.isExpandAll = false;
          this.frozenLeftIndex = 3;
          this.isManuallyClick = !this.isExpandAll;
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    async saveConfigCb(reconciliationConfig) {
      this.reconciliationConfig = reconciliationConfig;
      this.generateReportBefore();
      await this.handleRefresh(true);
      this.setDrawerStatus("config", false);
    },
    setFrozen(type) {
      this.$refs.table[type] && this.$refs.table[type]();
    },
    // 处理列消零状态变化
    handleColumnZeroStateChange(isActive) {
      this.isColumnZeroActive = isActive;
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.ant-spin-container {
  height: 100%;
}
/deep/.ant-drawer-body {
  padding-bottom: 20px;
  overflow: auto;
}
.drawerReconciliationQuery /deep/.ant-drawer-body {
  padding: 0;
  height: calc(100% - 45px);
}
.reconciliationParamsCont {
  height: 100%;
}
.reportCont {
  height: 100%;
  display: flex;
  flex-flow: column;
  .title {
    .top {
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
    }
  }
}
.drawerQuery {
  // padding: @rem24;
  padding-bottom: 53px;
}
</style>
