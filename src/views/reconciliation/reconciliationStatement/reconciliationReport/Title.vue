<template>
  <div class="title">
    <div class="title-top">
      <div class="top">
        <div
          v-if="Object.keys(reconciliationInfo).length > 0"
          class="title-info"
        >
          <span class="title-word">{{ reconciliationInfo.name }}</span>
          <yn-divider type="vertical" />
          <span v-if="reconciliationInfo.desc" class="desc">{{
            reconciliationInfo.desc
          }}</span>
          <span class="creator">{{ reconciliationInfo.creator }}</span>
          <span class="time">{{ `${reconciliationInfo.createTime}` }}</span>
          <span class="text">{{ `创建` }}</span>
        </div>
        <div v-if="isShowBtn" class="jumpBtn">
          <yn-button
            v-if="authSet['edit-config']"
            type="text"
            @click="showConfig"
          >
            修改对账配置
          </yn-button>
          <yn-button type="text" @click="showQueryPage">修改查询参数</yn-button>
        </div>
      </div>
      <div class="center">
        <span
          v-for="item in titleInfo"
          :key="Object.keys(item)[0]"
          class="dimItem"
        >
          <span class="dim-name">{{ Object.keys(item)[0] }}</span>
          <span class="dim-member-name">【{{ Object.values(item)[0] }}】</span>
        </span>
      </div>
    </div>
    <div class="footer">
      <div>
        <div v-if="batchType">
          <span>已选状态: {{ batchType.value }}</span>
          <yn-divider type="vertical" />
          <span class="selection">已选 {{ selection.length }} 条</span>
          <yn-dropdown-button
            placement="bottomRight"
            type="primary"
            @click="handleButtonClick('entityReconciliationStatus')"
          >
            组织方调整
            <yn-icon slot="icon" type="down" />
            <yn-menu slot="overlay">
              <yn-menu-item
                key="Dropdown"
                @click="handleButtonClick('icpReconciliationStatus')"
              >
                <span class="btn-like">往来方调整</span>
              </yn-menu-item>
            </yn-menu>
          </yn-dropdown-button>
          <yn-button class="batch-cancel" @click="handleStatusMenuCancel">
            取消
          </yn-button>
        </div>
        <yn-dropdown v-if="!batchType && authSet['reconciliation-synergy']">
          <yn-menu slot="overlay">
            <yn-menu-item
              v-for="item in statusMenu"
              :key="item.id"
              @click="handleStatusMenuClick(item)"
            >
              {{ item.value }}
            </yn-menu-item>
          </yn-menu>
          <yn-button class="batch-status" type="primary">
            批量状态调整
            <yn-icon type="down" />
          </yn-button>
        </yn-dropdown>
        <yn-dropdown
          v-if="
            !batchType &&
              tableInfo.items &&
              tableInfo.items.length > 0 &&
              authSet['email-caution']
          "
        >
          <yn-menu slot="overlay" @click="handleMenuClick">
            <yn-menu-item key="1">
              {{ EMAIL_TYPE_MAP["1"] }}
            </yn-menu-item>
            <yn-menu-item key="2">
              {{ EMAIL_TYPE_MAP["2"] }}
            </yn-menu-item>
          </yn-menu>
          <yn-button class="emailReminder">
            邮件提醒
            <yn-icon type="down" />
          </yn-button>
        </yn-dropdown>
      </div>
      <div class="toolBar">
        <!-- 小数位数 -->
        <span>
          小数位数：
          <yn-input-number
            ref="decimalPlaceInput"
            v-model="decimalPlace"
            class="decimalPlace"
            size="small"
            :precision="0"
            :min="0"
            :max="9"
            @blur="handleDecimalPlace"
          />
        </span>
        <span v-if="isShowBtn" class="ynicon-button ynicon-button-undefined" :title="columnZeroTitle" @click="handleColumnZero">
          <svg
            v-if="isColumnZeroActive"
            t="1605064250805"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="175933"
            width="16"
            height="16"
          >
            <path
              d="M837.851429 46.518857c51.346286 0 93.037714 41.691429 93.037714 93.110857v210.212572a417.792 417.792 0 0 0-74.386286-39.643429V139.702857a18.651429 18.651429 0 0 0-18.651428-18.578286H651.629714v160.768c-25.6 2.852571-50.468571 8.045714-74.459428 15.213715V121.051429H353.718857v338.651428a416.914286 416.914286 0 0 0-74.459428 238.445714c0 66.925714 15.725714 130.194286 43.593142 186.221715H93.110857C41.691429 884.297143 0 842.605714 0 791.259429V139.702857C0 88.210286 41.691429 46.518857 93.110857 46.518857h744.740572zM279.259429 121.051429H93.110857a18.651429 18.651429 0 0 0-18.651428 18.578285v651.702857c0 10.24 8.338286 18.578286 18.651428 18.578286h186.148572V121.051429z"
              fill="#1E88E5"
              p-id="175934"
            />
            <path
              d="M749.933714 120.832v0.146286H651.629714v160.914285c-25.6 2.779429-50.468571 7.899429-74.459428 15.140572V121.051429H353.718857v338.651428a416.914286 416.914286 0 0 0-74.459428 238.445714v-577.097142L219.428571 120.978286v-0.146286h530.505143z"
              fill="#1E88E5"
              p-id="175935"
            />
            <path
              d="M694.857143 365.714286l15.798857 0.365714a329.142857 329.142857 0 0 1 0 657.554286L694.857143 1024a329.142857 329.142857 0 0 1 0-658.285714z m190.390857 138.752a269.312 269.312 0 1 0-11.044571 391.314285l11.044571-10.532571 10.532571-11.044571a269.312 269.312 0 0 0-10.532571-369.737143z m-118.345143 80.164571a29.988571 29.988571 0 0 1 46.08 37.595429l-3.803428 4.681143-71.972572 72.045714 58.733714 58.806857a29.988571 29.988571 0 0 1-42.276571 42.276571l-58.806857-58.733714-58.806857 58.733714a29.988571 29.988571 0 0 1-37.595429 3.876572l-4.681143-3.876572a29.988571 29.988571 0 0 1-3.876571-37.595428l3.876571-4.681143 58.733715-58.806857-71.972572-72.045714a29.988571 29.988571 0 0 1 42.276572-42.276572l72.045714 71.972572 72.045714-71.972572z"
              fill="#1E88E5"
              p-id="175936"
            />
          </svg>
          <svg
            v-else
            t="1653381894888"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="20683"
            width="16"
            height="16"
            style="width:16px;height:16px;"
          >
            <path
              d="M832 85.333333a106.666667 106.666667 0 0 1 106.453333 99.669334L938.666667 192l0.042666 235.690667a364.544 364.544 0 0 0-85.333333-74.069334L853.333333 192a21.333333 21.333333 0 0 0-17.493333-20.992L832 170.666667h-149.504v128.597333a368.384 368.384 0 0 0-85.333333 5.077333V170.666667H426.666667v214.186666a364.202667 364.202667 0 0 0-85.333334 105.642667V170.666667H192a21.333333 21.333333 0 0 0-20.992 17.493333L170.666667 192v640a21.333333 21.333333 0 0 0 17.493333 20.992L192 853.333333l161.621333 0.042667a364.544 364.544 0 0 0 74.069334 85.333333L192 938.666667a106.666667 106.666667 0 0 1-106.453333-99.669334L85.333333 832v-640a106.666667 106.666667 0 0 1 99.669334-106.453333L192 85.333333h640z m-170.666667 298.666667a277.333333 277.333333 0 1 1 0 554.666667 277.333333 277.333333 0 0 1 0-554.666667z m0 85.333333a192 192 0 1 0 0 384 192 192 0 0 0 0-384z m85.333334 149.333334a42.666667 42.666667 0 0 1 4.992 85.034666l-4.992 0.298667h-170.666667a42.666667 42.666667 0 0 1-4.992-85.034667l4.992-0.298666h170.666667z"
              p-id="20684"
            />
          </svg>
        </span>
        <span v-if="isShowBtn" @click="handleRefresh">
          <svg-icon type="icon-shuaxin" title="刷新" />
        </span>
        <span class="expandOrCloseAllIcon" @click="expandOrCloseAll">
          <svg-icon :type="expandIconName" :title="expandAllTitle" />
        </span>
        <yn-dropdown>
          <yn-menu slot="overlay" @click="handleExport">
            <yn-menu-item key="excel">
              Excel
            </yn-menu-item>
            <yn-menu-item key="pdf">
              PDF
            </yn-menu-item>
          </yn-menu>
          <span>
            <svg-icon
              class="search-icon-export"
              type="icon-a-c1_cr_export-dropdown"
              title="导出"
            />
          </span>
        </yn-dropdown>
        <yn-dropdown>
          <yn-menu slot="overlay" @click="handleFrozen">
            <yn-menu-item key="frozenFirstCol">
              冻结首列
            </yn-menu-item>
            <yn-menu-item key="forzenToCol" :disabled="!frozenColName">
              <template v-if="!!frozenColName">
                {{ `冻结至“${frozenColName}”列` }}
              </template>
              <template v-else>
                <yn-tooltip placement="right">
                  <template slot="title">
                    <span>请选择需要冻结的列</span>
                  </template>
                  <span class="disabled-item">冻结至</span>
                </yn-tooltip>
              </template>
            </yn-menu-item>
            <yn-divider style="margin: 0" />
            <yn-menu-item key="cancelFrozen" :disabled="!frozenLeftIndex">
              <span>取消冻结</span>
            </yn-menu-item>
          </yn-menu>
          <span>
            <svg-icon type="icon-C1_form_freeze" title="冻结列" />
          </span>
        </yn-dropdown>
        <yn-dropdown>
          <yn-menu slot="overlay" @click="handleSetting">
            <yn-menu-item
              v-for="item in $data.$setOptions"
              :key="`${item.key}_${item.name}`"
            >
              {{ item.name }}
            </yn-menu-item>
          </yn-menu>
          <span>
            <svg-icon type="icon-set-up" title="设置" />
          </span>
        </yn-dropdown>
      </div>
    </div>
    <yn-modal
      :title="`${modalInfo.title}设置`"
      :visible="modalInfo.showModal"
      :destroyOnClose="true"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <yn-form :form="form" class="reconciliation-report-matches">
        <yn-form-item
          v-show="showMemberSeting"
          label="成员设置"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <yn-radio-group
            v-decorator="[
              'cellType',
              {
                initialValue: cellType
              }
            ]"
            :options="plainOptions"
            :defaultValue="cellType"
          />
        </yn-form-item>
        <yn-form-item
          v-show="showMatches"
          label="匹配项"
          required
          :class="['matches']"
          :validateStatus="matchesValidateStatus"
          :help="help"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
        >
          <yn-radio-group
            v-decorator="[
              'matchType',
              {
                initialValue: matchType || 0
              }
            ]"
            :options="plainOptionsMatches"
            @change="handleMatchesChange"
          />
          <yn-input
            v-if="matchType"
            v-decorator="[
              'tolerance',
              {
                initialValue: tolerance || '',
                rules: [
                  {
                    required: true,
                    message: '请输入容差值'
                  }
                ]
              }
            ]"
            :class="['toleranceInput']"
            @change="changeToleranceValue"
          >
            <yn-select
              slot="addonBefore"
              v-decorator="['valueType', { initialValue: valueType || 0 }]"
              style="width: 5.625rem"
              :allowClear="false"
              @change="handleChangeType"
            >
              <yn-select-option :value="0">数值</yn-select-option>
              <yn-select-option :value="1">百分比</yn-select-option>
            </yn-select>
            <span v-if="valueType" slot="suffix">%</span>
          </yn-input>
        </yn-form-item>
      </yn-form>
    </yn-modal>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-dropdown-button/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu";
import "yn-p1/libs/components/yn-menu-item";
import "yn-p1/libs/components/yn-icon";
import "yn-p1/libs/components/yn-popover/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-input-number/";
import "yn-p1/libs/components/yn-list/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-page-title/";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-menu-item/";
import { downloadFile } from "@/utils/common";
import commonService from "@/services/common";
import reconciliationService from "@/services/reconciliation";
import emailService from "@/services/email";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import { mapState, mapMutations } from "vuex";
const RECONCILIATION_REPORT = "reconciliationReport";
import { MEMBER_SHOW_TYPE } from "./constant";
const EMAIL_TYPE_MAP = Object.freeze({
  2: "双方邮件",
  1: "对方邮件"
});
const authMap = {
  icpReconciliationStatus: "icpAuth",
  entityReconciliationStatus: "entityAuth"
};
const authTypeMap = {
  icpReconciliationStatus: "往来方调整",
  entityReconciliationStatus: "组织方调整"
};
export default {
  name: "ReconciliatioReportTtile",
  props: {
    reconciliationInfo: {
      type: Object,
      default() {
        return {};
      }
    },
    titleInfo: {
      type: Array,
      default() {
        return [];
      }
    },
    tableInfo: {
      type: Object,
      default() {
        return {
          auditTrailColumns: [], // 审计线索列集合， 用于列收起，确认收起范围
          items: [], // 表格数据
          columnInfo: [],
          totalAmount: {
            differenceAmount: 100, // 差额合计
            entityAmount: 100, // 组织抵销金额
            icpAmount: 100 // 往来公司抵销金额
          }
        };
      }
    },
    isExpandAll: {
      type: Boolean
    },
    fromPage: {
      type: String,
      default: ""
    },
    taskId: {
      type: String,
      default: ""
    },
    reportId: {
      type: String,
      default: ""
    },
    changeTableDataByParams: {
      type: Function,
      default: null
    },
    coordinationStatus: {
      type: Array,
      default: () => []
    },
    frozenColName: {
      type: String,
      default: ""
    },
    frozenLeftIndex: {
      type: Number,
      default: 3
    },
    refreshId: {
      type: String,
      default: ""
    },
    spinning: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      EMAIL_TYPE_MAP,
      statusMenu: [],
      plainOptions: [
        {
          label: "名称",
          value: "name"
        },
        {
          label: "唯一标识",
          value: "code"
        },
        {
          label: "名称+唯一标识",
          value: "nameAndCode"
        }
      ],
      cellType: "name",
      objectId: null,
      visible: false,
      decimalPlace: 2,
      cacheDecimalPlace: 2,
      $setOptions: [
        { key: "memberShow", name: "成员显示" }
        // { key: "matches", name: "匹配项" }
      ],
      modalInfo: {
        title: "",
        type: "",
        showModal: false
      },
      matchType: 0,
      valueType: 0,
      plainOptionsMatches: [
        {
          label: "显示",
          value: 0
        },
        {
          label: "隐藏",
          value: 1
        }
      ],
      matchesValidateStatus: "success",
      help: "",
      form: this.$form.createForm(this, {
        name: "displaySettings"
      }),
      labelCol: {
        span: 4,
        sm: 4
      },
      wrapperCol: {
        span: 20,
        sm: 20
      },
      isColumnZeroActive: false
    };
  },
  computed: {
    ...mapState({
      selection: state => state.reconciliation.selection,
      batchType: state => state.reconciliation.batchType
    }),
    // 来源，合并任务（merge_search）跳转过来的不显示修改对账配置、查询、刷新等按钮
    isShowBtn() {
      return this.fromPage !== "merge_search";
    },
    expandIconName() {
      return !this.isExpandAll ? "icon-zhedie" : "icon-zhankai";
    },
    expandAllTitle() {
      return !this.isExpandAll ? "全部展开" : "全部收起";
    },
    showMemberSeting() {
      return this.modalInfo.type === "memberShow";
    },
    showMatches() {
      return this.modalInfo.type === "matches";
    },
    currReportId() {
      return this.refreshId || this.taskId;
    },
    ...mapState({
      authSet: state => state.reconciliation.authSet
    }),
    columnZeroTitle() {
      return this.isColumnZeroActive ? "取消列消零" : "列消零";
    }
  },
  watch: {
    reconciliationInfo: {
      handler(newVal) {
        this.echoSettings(newVal);
      },
      immediate: true
    },
    coordinationStatus: {
      handler() {
        this.statusMenu = this.coordinationStatus.map(item => ({
          value: item.name,
          id: item.id
        }));
      },
      immediate: true
    }
  },
  created() {
    this.getSettingType();
  },
  mounted() {
    document.body.addEventListener("click", this.decimalPlaceBlur);
  },
  beforeDestroy() {
    document.body.removeEventListener("click", this.decimalPlaceBlur);
  },
  methods: {
    ...mapMutations("reconciliation", ["setBatchType", "setSelection"]),
    decimalPlaceBlur(e) {
      let inputDom = e.target;
      let isContinue = true;
      while (isContinue) {
        if (!inputDom) {
          return;
        }
        if (inputDom.tagName === "BODY") {
          this.$refs.decimalPlaceInput.$el.querySelector("input").blur();
          isContinue = false;
          // 防止，点击到svg 报错
        } else if (
          typeof inputDom.className === "string" &&
          inputDom.className.indexOf("decimalPlace") !== -1
        ) {
          isContinue = false;
        } else {
          inputDom = inputDom.parentNode;
        }
      }
    },
    echoSettings(obj) {
      const {
        decimalPlace,
        matchType,
        toleranceNumber,
        tolerancePercentage
      } = obj;
      this.decimalPlace = decimalPlace;
      this.cacheDecimalPlace = decimalPlace;
      this.matchType = matchType || 0;
      // 当数值类型，没有值时，判断百分比类型，百分类型没有值，默认是 数值类型
      this.valueType = toleranceNumber ? 0 : tolerancePercentage ? 1 : 0;
      this.tolerance = toleranceNumber || tolerancePercentage;
      this.form.setFieldsValue({
        tolerance: this.tolerance,
        matchType,
        valueType: this.valueType
      });
    },
    // 获取 表格配置信息
    getSettingType() {
      commonService("getLastSettingV", {
        key: RECONCILIATION_REPORT, // 持股方
        tag: "userIndexName"
      }).then(res => {
        const { objectId, value } = res.data ? res.data.data : {};
        this.objectId = objectId;
        this.cellType = value;
        this.$emit("changeCellType", this.cellType);
      });
    },
    expandOrCloseAll() {
      this.$emit("update:isExpandAll", !this.isExpandAll);
    },
    async handleMenuClick(e) {
      const {
        data: { data }
      } = await emailService("taskExpire", this.currReportId);
      if (!data) {
        emailService("sendReconciliationMail", {
          taskId: this.currReportId,
          emailType: e.key
        }).then(res => {
          UiUtils.successMessage(EMAIL_TYPE_MAP[e.key] + "提醒成功");
        });
      } else {
        UiUtils.error({
          title: EMAIL_TYPE_MAP[e.key] + "提醒",
          content: "当前对账报表已过期，请重新刷新生成数据。"
        });
      }
    },
    showConfig() {
      this.$emit("openDrawer", "config", true);
    },
    showQueryPage() {
      this.$emit("openDrawer", "queryParam", true);
    },
    handleRefresh() {
      const { matchType, decimalPlace, valueType, tolerance } = this;
      const params = {
        decimalPlace: Number(decimalPlace),
        matches: matchType,
        tolerance: tolerance + "",
        valueType
      };
      if (!matchType) {
        delete params.tolerance;
      }
      this.$emit("handleRefresh", true, params);
    },
    async handleExport(event) {
      const { reconciliationInfo, taskId, refreshId, decimalPlace } = this;
      const {
        data: { data }
      } = await emailService("taskExpire", this.currReportId);
      if (!data) {
        reconciliationService("reconciliationExportFile", {
          fileName: reconciliationInfo.name,
          fileType: event.key,
          memberShowType: MEMBER_SHOW_TYPE[this.cellType],
          taskId: refreshId || taskId,
          decimalPlace: Number(decimalPlace)
        }).then(res => {
          downloadFile(res);
        });
      } else {
        UiUtils.error({
          title: "导出",
          content: "当前对账报表已过期，请重新刷新生成数据。"
        });
      }
    },
    handleFrozen(event) {
      this.$emit("setFrozen", event.key);
    },
    handleSetting({ key }) {
      const [type, title] = key.split("_");
      this.modalInfo = {
        type,
        title,
        showModal: true
      };
    },
    handleStatusMenuClick(item) {
      this.setBatchType(item);
      this.isExpandAll = false;
      this.$emit("update:isExpandAll", false);
    },
    handleStatusMenuCancel() {
      this.setBatchType("");
      this.setSelection([]);
    },
    async handleButtonClick(statusType) {
      if (this.selection.length === 0) {
        UiUtils.errorMessage(`请先勾选有效记录`);
        return;
      }
      if (this.selection.some(item => !item[authMap[statusType]])) {
        UiUtils.errorMessage(
          `选择的数据中存在没有${authTypeMap[statusType]}的权限`
        );
        return;
      }
      this.$emit("update:spinning", true);
      const params = {
        taskId: this.refreshId || this.taskId,
        reconciliationId: this.reconciliationInfo.reconciliationId,
        statusType: statusType,
        statusId: this.batchType.id, // 选择的对账状态字段的id
        nodeIds: this.selection.map(item => item.id)
      };
      await reconciliationService("updateCoordinationStatus", params).then(
        () => {
          UiUtils.successMessage("状态调整成功");
          this.handleStatusMenuCancel();
        }
      );
    },
    // 校验容差
    checkTolerance(val) {
      const toleranceType = [
        "checkToleranceNumber",
        "checkTolerancePercentage"
      ]; // 容差类型 【数值，百分比】
      if (!val.trim()) return true;
      // 百分比
      if (
        typeof Number(val) === "number" &&
        isNaN(Number(val)) &&
        this.valueType
      ) {
        return false;
      }
      return (
        this[toleranceType[this.valueType]] &&
        this[toleranceType[this.valueType]](val)
      );
    },
    // 校验数值类型
    checkToleranceNumber(val) {
      return /^[+-]?\d*\.?\d{0,9}$/.test(val);
    },
    // 校验百分比类型
    checkTolerancePercentage(val) {
      return val >= 0 && val <= 100;
    },
    changeToleranceValue(event) {
      let val = event.target.value;
      if (!this.checkTolerance(event.target.value)) {
        val = this.tolerance;
        event.target.value = val;
      }
      if (val) {
        this.clearToLeranceValidateInfo();
        this.tolerance = val;
      } else {
        // 清空值
        this.matchesValidateStatus = "error";
        this.help = "请输入容差值";
      }
    },
    handleMatchesChange($event) {
      this.matchType = $event.target.value;
      this.tolerance = "";
      this.clearToLeranceValidateInfo();
    },
    clearToLeranceValidateInfo() {
      this.matchesValidateStatus = "success";
      this.help = "";
      this.form.setFieldsValue({
        tolerance: ""
      });
    },
    async handleOk() {
      await this.form.validateFields((err, values) => {
        if (err) {
          this.help = "请输入容差值";
          this.matchesValidateStatus = "error";
          return;
        }
        if (this.showMemberSeting) {
          this.saveOrUpdateUserSetting(values.cellType);
        } else {
          this.reQueryData(values);
        }
        this.modalInfo.showModal = false;
      });
    },
    reQueryData(values) {
      const { matchType, tolerance, valueType } = values;
      this.matchType = matchType;
      this.tolerance = tolerance;
      this.valueType = valueType;
      const {
        taskId,
        reconciliationInfo,
        decimalPlace,
        reportId,
        refreshId
      } = this;
      const {
        reconciliationId,
        year,
        period,
        reportingCurrency,
        version,
        reconciliationType
      } = reconciliationInfo;
      const requeryParams = {
        taskId, // 任务id
        reconciliationId, // 对账报表的id
        version,
        year,
        reportingCurrency,
        period,
        reconciliationType, // 对账类型
        matchType, // 匹配项
        reportId,
        decimalPlace: decimalPlace, // 保留小数位数
        toleranceNumber: tolerance + "", // 数值
        tolerancePercentage: tolerance + "" // 百分比
      };
      if (valueType) {
        //  百分比类型
        delete requeryParams.toleranceNumber;
      } else {
        delete requeryParams.tolerancePercentage;
      }
      if (!matchType) {
        delete requeryParams.toleranceNumber;
        delete requeryParams.tolerancePercentage;
      }
      if (refreshId) {
        requeryParams.refreshId = refreshId;
      }
      this.changeTableDataByParams(requeryParams);
    },
    saveOrUpdateUserSetting(cellType) {
      this.cellType = cellType;
      commonService("saveOrUpdateUserSetting", {
        value: cellType,
        objectId: this.objectId,
        tag: "userIndexName",
        key: RECONCILIATION_REPORT
      });
      this.$emit("changeCellType", cellType);
    },
    handleDecimalPlace(e) {
      const decimalPlace = Number(e.target.value);
      this.decimalPlace = decimalPlace;
      if (this.decimalPlace !== this.cacheDecimalPlace) {
        this.cacheDecimalPlace = this.decimalPlace;
      } else {
        return;
      }
      const { matchType, tolerance, valueType } = this;
      this.reQueryData({
        matchType,
        tolerance,
        valueType
      });
    },
    handleCancel() {
      // 还原数据，并清除校验信息
      this.modalInfo.showModal = false;
      this.help = "";
      this.matchesValidateStatus = "";
      this.echoSettings(this.reconciliationInfo);
    },
    handleChangeType(val) {
      this.valueType = val;
      // 切换以后，不在缓存上次输入正确的数值
      this.form.setFieldsValue({
        tolerance: ""
      });
      this.tolerance = "";
    },
    handleColumnZero() {
      // 切换列消零状态
      this.isColumnZeroActive = !this.isColumnZeroActive;

      // 给用户提示
      if (this.isColumnZeroActive) {
        UiUtils.successMessage("已启用列消零，将先展开所有列后检测全零列");
      } else {
        UiUtils.successMessage("已取消列消零，恢复所有列显示");
      }

      // 通知父组件状态变化，让表格组件响应列消零状态
      this.$emit("columnZeroStateChange", this.isColumnZeroActive);
    }
  }
};
</script>
<style lang="less" scoped>
.title {
  // padding: @yn-margin-xl @yn-margin-xxxl 0 @yn-margin-xxxl;
  .title-info {
    span.title-word {
      font-size: @rem16;
      font-weight: 600;
      color: @yn-text-color;
    }
    height: @rem32;
    line-height: @rem32;
  }

  .title-top {
    padding-top: @yn-margin-l;
  }
  .top,
  .footer {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }
  .footer {
    margin-bottom: 0;
    background: @yn-component-background;
    border-radius: @yn-console-content-radius @yn-console-content-radius 0 0;
    padding: 0.75rem 1rem 0;
  }
  .desc,
  .time,
  .creator,
  .text {
    margin-right: @yn-margin-s;
  }
  .creator,
  .time,
  .text {
    color: @yn-label-color;
    font-size: @yn-font-size-sm;
  }
  .jumpBtn {
    color: @yn-chart-1;
    & > span {
      cursor: pointer;
    }
    & > span:first-child {
      margin-right: @yn-margin-xxl;
    }
  }
  .center {
    padding-bottom: @rem10;
    .dimItem {
      margin-right: @yn-margin-xl;
      .dim-name {
        color: @yn-text-color-secondary;
      }
      .dim-member-name {
        color: @yn-text-color;
      }
    }
  }

  .toolBar > * {
    margin-left: @yn-margin-xl;
    cursor: pointer;
    .search-icon-export {
      position: relative;
      top: 4px;
      /deep/.svg-icon {
        font-size: @rem32;
      }
    }
  }
  .expandOrCloseAllIcon {
    font-size: @yn-font-size-lg;
  }
}
div.matches {
  margin-bottom: 0;
  /deep/.toleranceInput {
    margin-top: 1.5rem;
  }
}
.batch-status {
  margin-right: 0.5rem;
}
.batch-cancel {
  margin: 0 0.5rem;
}
.selection {
  margin-right: 0.5rem;
}
</style>

<style lang="less">
.btn-like {
  padding: 0.25rem 0.5rem;
}
</style>
