<template>
  <div class="reportTableCont">
    <div class="reportTable">
      <Grid
        v-if="showTable"
        ref="reportGrid"
        rowKey="objectId"
        :ident="$data.$treeGridIdent"
        :fixedColumns="{ right: 1, left: frozenLeftIndex }"
        :columns="columns"
        :hasMore="hasMore"
        :dataSource="showTableData"
        :paginationInfo="pagination"
        :expandedRowKeys="expandedRowKeys"
        :renderHeaderCell="renderHeaderCell"
        :cellNode="renderBodyCell"
        :cellClass="bodyCellClassName"
        :cellType="cellType"
        :loadMore="loadMore"
        :loadData="loadData"
        :filterOrder="filterOrder"
        :cellMouseUp="selectThead"
        :requestFilterPopData="requestFilterPopData"
        :formatFilterWord="formatFilterWord"
        :showDimMemberName="showDimMemberName"
        :scrollTable="scrollTable"
        :selection="batchType ? localSelection : false"
        :treeSelection="false"
        :selectable="true"
        :isAsyncLoad="true"
        :overSystem="true"
        :hasSelectAllBox="false"
        :hasSelectBox="hasSelectBox"
        selectTitle="序号"
        @selectChange="handlerSelect"
        @reloadData="closeAll"
        @expandedRowsChange="expandedRowsChange"
      />
    </div>
    <div class="footerCont">
      <yn-row class="footer">
        <yn-col :span="8">
          差额合计：<span class="footer_amount">{{
            totalAmount.differenceAmount
          }}</span>
        </yn-col>
        <yn-col :span="8">
          组织自动抵销前金额合计：<span class="footer_amount">{{
            totalAmount.entityAmount
          }}</span>
        </yn-col>
        <yn-col :span="8">
          往来公司自动抵销前金额合计：<span class="footer_amount">{{
            totalAmount.icpAmount
          }}</span>
        </yn-col>
      </yn-row>
    </div>
    <add-journal-modal
      :visible.sync="visible"
      :addBefore="addBefore"
      :templeteData="templeteData"
      @ok="openDetail"
    />
    <drill-page-modal
      v-if="showDrillPage"
      :title="modalTitle"
      :url="modalUrl"
      @closeModal="closeDrillPageModal"
    />
    <StatusModal
      :visible.sync="statusModalVisible"
      v-bind="statusModalInfo"
      :coordinationStatus="coordinationStatus"
      @ok="handlerStatusOk"
    />
    <div
      v-if="showDrillMenu"
      ref="drillMenu"
      class="drillMenu"
      :style="drillMenuStyle"
    >
      <yn-menu slot="overlay" @click="handleMenuClick">
        <template v-for="menuItem in drillMenu">
          <yn-menu-item
            v-if="menuItem.children.length === 1"
            :key="menuItem.key"
          >
            {{ menuItem.groupName }}
          </yn-menu-item>
          <yn-sub-menu v-else :key="menuItem.key" :title="menuItem.groupName">
            <yn-menu-item v-for="item in menuItem.children" :key="item.key">
              {{ item.title }}
            </yn-menu-item>
          </yn-sub-menu>
        </template>
      </yn-menu>
    </div>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-row/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-col/";
import reconciliationService from "@/services/reconciliation";
import commonService from "@/services/common";
import cloneDeep from "lodash/cloneDeep";
import omit from "lodash/omit";
import { mapState, mapMutations } from "vuex";
import Grid from "@/components/hoc/grid";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import AddJournalModal from "@/views/journal/journalList/list/AddJournalModal.vue";
import DrillPageModal from "./DrillPage.vue";
import DIM_INFO from "@/constant/dimMapping";
import { PAGE_SIZE } from "./constant.js";
import { genVDOM, genDOM } from "@/views/process/control/jdom/utils";
import StatusModal from "@/views/reconciliation/reconciliationStatement/reconciliationReport/StatusModal";
import drillMenuMixin from "./mixin.js";
// 科目的父项成员信息 固定值
const SUBJECT_PARENT_NODE = ["合计", "小计"];
const SUBJECT_PROPR_NAME = "AccountGroup";
const FIXED_DISPLAY_COLUMN = ["Entity", "ICP", "AccountGroup"]; // 固定显示列 组织 往来公司 科目
const AMOUNT_IDENT = "Amount"; // 金额列标识
const MAPPING_DIM_NAME = {}; // 映射维度名称
const AMOUNT_CELL_WIDTH = 200; // 金额列宽
const DIFFERENCE_COLUMN_KEY = "Difference"; // 差额列key值
const EXPAND_STATUS = ["icon-xiangyouzhankai", "icon-xiangzuoshouqi"];
const COLUMN_HEADER_INDEX = {
  true: "showCollapseBtnColumnKey",
  false: "showExpandBtnColumnKey"
};
const TABNAME = "journalDetail";
const TAB_URI = "/journal/detail";
const IDENT = "reconciliationReport";
const NAME_SEPARATOR = "@"; // 名称分隔符 "成员名称@成员唯一标识"
const ROOT_ID = "-1";
const STATUS_ICON_MAP = {
  全部核对: "check-circle",
  部分核对: "information",
  未核对: "delete-fill"
};
const STATUS_ICON_CLASS_MAP = {
  green: "check-circle",
  yellow: "information",
  grey: "delete-fill"
};
export default {
  name: "ReconciliatioTable",
  components: { Grid, AddJournalModal, DrillPageModal, StatusModal },
  mixins: [drillMenuMixin],
  props: {
    tableInfo: {
      type: Object,
      default() {
        return {
          auditTrailColumns: [], // 审计线索列集合， 用于列收起，确认收起范围
          items: [], // 表格数据
          columnInfo: [],
          totalAmount: {
            differenceAmount: 100, // 差额合计
            entityAmount: 100, // 组织抵销金额
            icpAmount: 100 // 往来公司抵销金额
          }
        };
      }
    },
    cellType: {
      type: String,
      default: "nameAndCode" // 单元格显示的类型 name or code or nameAndCode
    },
    isExpandAll: {
      type: Boolean
    },
    frozenColName: {
      type: String,
      default: ""
    },
    frozenLeftIndex: {
      type: Number,
      default: 3
    },
    coordinationStatus: {
      type: Array,
      default: () => []
    },
    reconciliationConfig: {
      type: Object,
      default: () => {}
    },
    reconciliationInfo: {
      type: Object,
      default() {
        return {};
      }
    },
    taskId: {
      type: String,
      default: ""
    },
    refreshId: {
      type: String,
      default: ""
    },
    isManuallyClick: {
      type: Boolean,
      default: true
    },
    titleMap: {
      type: Object,
      default: () => {}
    },
    titleInfo: {
      type: Array,
      default: () => []
    },
    // 新增：列消零状态
    isColumnZeroActive: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showTable: false, // 刷新操作会使用，刷新使用此属性控制grid组件卸载挂载
      $treeGridIdent: IDENT, // @modify treeGrid 唯一标识
      pagination: {
        total: 0,
        current: 1,
        pageSize: PAGE_SIZE,
        hasMore: false,
        offset: 0
      },
      localSelection: [],
      columns: [],
      searchObj: [], // 查询维度显示列表
      originFormateColumns: [], // 处理后的 columns 表头
      originalTableColumns: [], // 原始接口返回 columns 数据
      originalTableData: [], // 原始表格数据
      showTableData: [],
      columnExpanStatus: false, // 列展开状态 true 展开 false 闭合 默认false
      fixedCell: FIXED_DISPLAY_COLUMN,
      filterSelectObj: {}, // 过滤下拉列表对象
      filterCheckedObj: {}, // 过滤下拉列表已选对象
      expandedRowKeys: [], // 展开的key集合
      showExpandBtnColumnKey: "", // 显示展开按钮 列头key
      showCollapseBtnColumnKey: "", // 展示 收起按钮 列头key
      filterOrder: [], // 过滤的顺序
      frozenLeft: 3, // 左边冻结
      seletedColInfo: [], // 冻结选中表头信息
      isCtrl: false,
      visible: false,
      selectRowDimInfo: {},
      hasMore: false,
      cacheHasMore: false,
      statusModalVisible: false,
      statusModalInfo: {},
      mapTableData: {},
      searchInfo: [], // 过滤条件
      drillMenuData: [],
      drillMenuStyle: {},
      showDrillMenu: false,
      // 新增：隐藏的零值列
      hiddenZeroColumns: []
    };
  },
  computed: {
    ...mapState({
      menuInfo: state => state.common.menuList,
      batchType: state => state.reconciliation.batchType,
      selection: state => state.reconciliation.selection,
      authSet: state => state.reconciliation.authSet
    }),
    filterObject() {
      const res = {};
      Object.keys(this.filterSelectObj).forEach(keyName => {
        res[keyName] = {
          data: this.filterSelectObj[keyName],
          name: MAPPING_DIM_NAME[keyName]
        };
      });
      return res;
    },
    templeteData() {
      const templateData = this.reconciliationConfig.journalTemplate;
      if (!templateData) return [];
      return templateData.map(item => {
        const { templateId, templateName } = item;
        return {
          key: templateId,
          label: templateName
        };
      });
    },
    refreshIdOrTaskId() {
      return this.refreshId || this.taskId;
    }
  },
  watch: {
    authSet: {
      handler(newVal) {
        this.setTableColumns(newVal);
      },
      immediate: true,
      deep: true
    },
    tableInfo: {
      handler(newVal) {
        this.showTable = false;
        const {
          auditTrailColumns = [],
          items: tableData = [],
          columnInfo = [],
          totalAmount = {},
          hasMore
        } = newVal;
        this.auditTrailColumns = cloneDeep(auditTrailColumns);
        this.originalTableColumns = cloneDeep(columnInfo);
        this.originalTableData = cloneDeep(tableData);
        this.totalAmount = cloneDeep(totalAmount);
        this.columnExpanStatus = false;
        this.hasMore = hasMore;
        this.cacheHasMore = hasMore;
        this.$set(this, "expandedRowKeys", []);

        // 数据变化时，清空之前缓存的零列信息
        this.hiddenZeroColumns = [];

        this.$nextTick(() => {
          this.showTable = true;
          this.initData(this.originalTableColumns, this.originalTableData);
        });
      },
      immediate: true,
      deep: true
    },
    columnExpanStatus() {
      this.initColums(this.originalTableColumns);
    },
    isExpandAll: {
      async handler(newVal, oldVal) {
        if (!this.isManuallyClick) {
          this.$emit("update:isManuallyClick", true);
          return;
        }
        this.$refs.reportGrid.clearAll();
        // 全部展开
        this.$set(this, "mapTableData", {});
        this.$emit("update:spinning", true);
        let res = {};
        if (newVal) {
          res = await this.getExpandAllDataByOffset(0);
          const { data, hasMore } = res;
          this.hasMore = hasMore;
          this.$nextTick(() => {
            this.$refs.reportGrid.updateTableData(data);
          });
          return;
        } else if (!newVal && oldVal) {
          // 全部 闭合
          this.closeAll();
        }
      }
    },
    selection(value) {
      this.localSelection = value;
    },
    cellType(newVal, oldVal) {
      if (newVal === oldVal) return;
      this.$set(this, "expandedRowKeys", []);
      this.initData(this.originalTableColumns, this.originalTableData);
      this.hasMore = this.cacheHasMore;
      this.$nextTick(() => {
        this.updateTable();
        this.$refs.reportGrid.$refs.simpleGrid.loading = false;
      });
    },
    seletedColInfo(newVal) {
      const mapObj = {};
      let frozenLeft = 0;
      let frozenColName = "";
      this.columns.forEach((item, index) => {
        const { title } = item;
        mapObj[title] = index;
      });
      newVal.forEach(colName => {
        const currIndex = mapObj[colName];
        if (currIndex >= frozenLeft) {
          frozenLeft = currIndex;
          frozenColName = colName;
        }
      });
      this.$emit("update:frozenColName", frozenColName);
      this.frozenLeft = frozenLeft + 1;
    },
    // 新增：监听列消零状态变化
    isColumnZeroActive(newVal) {
      if (newVal) {
        // 激活列消零，只有在首次激活时检测全零列
        if (this.hiddenZeroColumns.length === 0) {
          this.detectAndHideZeroColumns();
        } else {
          // 如果已经有缓存的零列，直接应用
          this.updateColumnsVisibility();
        }
      } else {
        // 取消列消零，恢复所有列
        this.restoreAllColumns();
      }
    }
  },
  mounted() {
    document.body.addEventListener("keydown", this.keyDownEvent);
    document.body.addEventListener("keyup", this.keyUpEvent);
    document.body.addEventListener("click", this.bodyClick);
  },
  destroyed() {
    document.body.removeEventListener("keydown", this.keyDownEvent);
    document.body.removeEventListener("keyup", this.keyUpEvent);
    document.body.removeEventListener("click", this.keyUpEvent);
  },
  methods: {
    ...mapMutations("reconciliation", ["setSelection"]),
    handlerSelect(selection) {
      this.localSelection = selection;
      this.setSelection(selection);
    },
    async closeAll() {
      this.$set(this, "expandedRowKeys", []);
      const res = await this.requestDataByOffset({
        parentId: ROOT_ID,
        offset: 0
      });
      const { data, hasMore } = res;
      this.hasMore = hasMore;
      this.$refs.reportGrid.updateTableData(data);
    },
    expandedRowsChange(keys) {
      this.expandedRowKeys = keys;
    },
    // 根据offset获取全部展开数据
    async getExpandAllDataByOffset(offset) {
      return new Promise(resolve => {
        reconciliationService("expandAll", {
          taskId: this.refreshIdOrTaskId,
          offset,
          limit: PAGE_SIZE
        })
          .then(res => {
            const { data, hasMore, keys } = res.data.data;
            this.$set(this, "expandedRowKeys", keys);
            const tableData = this.formatTableData(data);
            this.setMapTableData(tableData);
            resolve({
              data: tableData,
              hasMore
            });
          })
          .catch(() => {
            resolve({
              data: [],
              hasMore: false
            });
          })
          .finally(() => {
            this.$emit("update:spinning", false);
          });
      });
    },
    // 根据 offset 获取表格数据
    async getTableDataByOffset(offset) {
      return new Promise(res => {
        reconciliationService("getReconciliationByTaskId", {
          taskId: this.refreshIdOrTaskId,
          limit: PAGE_SIZE,
          offset
        }).then(result => {
          const { reportPageResult } = result.data.data;
          const { hasMore, items } = reportPageResult;
          this.hasMore = hasMore;
          this.$emit("update:spinning", false);
          res({
            hasMore,
            data: this.formatTableData(items)
          });
        });
      });
    },
    async requestFilterPopData(dimCode) {
      return new Promise(res => {
        reconciliationService("columnSearchList", {
          taskId: this.refreshIdOrTaskId,
          columnName: dimCode
        })
          .then(result => {
            const { data } = result.data;
            res(data);
          })
          .catch(() => {
            res([]);
          });
      });
    },
    formatFilterWord(colName, item, cellType) {
      // const {name,id} = item;
      const WORD_MAPPING = { code: "code", name: "name" };
      const PROPS_ARR = ["code", "name"];
      const { name, code } = item;
      if (PROPS_ARR.indexOf(this.cellType) !== -1) {
        return item[WORD_MAPPING[cellType]] || item.name;
      } else {
        // 科目列，小计、合计 只显示小计、合计
        if (
          colName === SUBJECT_PROPR_NAME &&
          SUBJECT_PARENT_NODE.indexOf(name) !== -1
        ) {
          return code || name;
        } else {
          return code ? `${code}-${name}` : name;
        }
      }
    },
    keyDownEvent(e) {
      const { keyCode } = e;
      if (keyCode === 17) {
        this.isCtrl = true;
      }
    },
    keyUpEvent(e) {
      this.isCtrl = false;
    },
    selectThead(event, cell, areaByRowCol) {
      if (areaByRowCol !== "headCol") return;
      let colInfo = [];
      if (this.isCtrl) {
        colInfo = colInfo.concat(this.seletedColInfo);
      }
      const { path } = event;
      const doms = path || event.composedPath();
      for (let i = 0, LEN = doms.length; i < LEN; i++) {
        const currDom = doms[i];
        if (currDom.className && currDom.className.indexOf("htCenter") !== -1) {
          const title = currDom.getAttribute("title");
          colInfo.push(title);
          break;
        }
      }
      this.$set(this, "seletedColInfo", colInfo);
    },
    loadMore(params) {
      const { searchObj, record, isFirstPage } = params;
      // 搜索 加载更多 都会走这里，record 当子项加载更多时，有值
      // 搜索条件改变
      if (isFirstPage) {
        this.$set(this, "searchObj", cloneDeep(searchObj));
        this.$set(this, "mapTableData", {});
        this.$emit("update:spinning", true);
        this.$set(this, "expandedRowKeys", []);
        this.hasMore = this.cacheHasMore;
      }
      const rootNodeNum = this.getRootNodeNum();
      if (record) {
        return this.loadMoreChildrenNode(record);
      } else if (searchObj.length) {
        return this.loadMoreSearchNode(searchObj, isFirstPage);
      } else if (this.isExpandAll) {
        return this.getExpandAllDataByOffset(isFirstPage ? 0 : rootNodeNum);
      } else {
        return this.loadMoreRootNode(isFirstPage ? 0 : rootNodeNum);
      }
    },
    loadMoreRootNode(offset) {
      return this.requestDataByOffset({
        offset: offset,
        parentId: "-1"
      });
    },
    loadMoreChildrenNode(record) {
      const { children = [], objectId } = record;
      const offset = children.length - 1;
      return this.requestDataByOffset({
        parentId: objectId,
        offset
      });
    },
    loadMoreSearchNode(searchObj, isFirstPage) {
      return this.getSearchData(
        searchObj,
        !isFirstPage ? Object.values(this.mapTableData).length : 0
      );
    },
    getRootNodeNum() {
      return Object.values(this.mapTableData).filter(item => {
        if (typeof item.parentId === "object") {
          return item.parentId.v === "-1";
        } else {
          return item.parentId === "-1";
        }
      }).length;
    },
    async requestDataByOffset(params) {
      const { limit = PAGE_SIZE, offset, parentId } = params;
      return new Promise(res => {
        reconciliationService("expandChildren", {
          taskId: this.refreshIdOrTaskId,
          limit,
          parentId,
          offset
        })
          .then(result => {
            const { hasMore, items } = result.data.data;
            const data = this.formatTableData(items);
            if (parentId === "-1") {
              this.hasMore = hasMore;
            }
            res({
              hasMore,
              data
            });
          })
          .catch(() => {
            res({
              hasMore: false,
              data: []
            });
          })
          .finally(() => {
            this.$emit("update:spinning", false);
          });
      });
    },
    expand(expandedKeys) {
      this.expandedRowKeys = expandedKeys;
    },
    bodyCellClassName(cellContext) {
      const { colName, hasChildren, row, col } = cellContext;
      let className = !hasChildren ? "leafNode " : "";
      // 当科目为合计、小计时，并且差额列不为0 ，则 金额列、差额列文本 变色
      if (
        colName.indexOf(AMOUNT_IDENT) !== -1 ||
        colName === DIFFERENCE_COLUMN_KEY
      ) {
        className += this.getAmountClassNames(cellContext);
      }
      className += ` cellInfo_${row}_${col}`;
      return className;
    },
    getAmountClassNames(cellContext) {
      const { rowId, colName } = cellContext;
      const { AccountGroup = {}, Difference = {} } = this.getRowDataById(rowId);
      const accountGroupVal = AccountGroup;
      const differenceVal =
        (typeof Difference === "object" ? Difference.v : Difference) || "";
      const tempNum = Number(differenceVal.replace(/,/g, ""));
      if (
        SUBJECT_PARENT_NODE.indexOf(accountGroupVal) !== -1 &&
        !isNaN(tempNum) &&
        !!tempNum
      ) {
        return " subtotal";
      } else if (
        SUBJECT_PARENT_NODE.indexOf(accountGroupVal) === -1 &&
        colName !== DIFFERENCE_COLUMN_KEY &&
        this.authSet["voucher-drilling"]
      ) {
        return " leafNodeAmount";
      }
    },
    renderBodyCell(cellContext) {
      const { colName } = cellContext;
      if (colName === "operationCol") {
        return this.renderOperationColumn(cellContext);
      } else if (colName === "entityReconciliationStatus") {
        return this.renderEntityStatus(cellContext);
      } else if (colName === "icpReconciliationStatus") {
        return this.renderCpiStatus(cellContext);
      } else {
        return "";
      }
    },
    renderEntityStatus(cellContext) {
      const { rowId } = cellContext;
      const rowData = this.getRowDataById(rowId);
      const value = rowData.entityReconciliationStatus;
      if (rowData.AccountGroup === "小计") {
        const childDiv = document.createElement("div");
        const self = this;
        const vClass = rowData.entityIcon;
        const disabled = !rowData.entityAuth || this.batchType;
        const spanNode = genVDOM({
          render(h) {
            return (
              <div>
                <yn-button
                  type="text"
                  class="operation-btn"
                  disabled={!!disabled}
                  onClick={() =>
                    self.showStatusModal(
                      {
                        title: "组织方对账数据确认",
                        data: rowData
                      },
                      rowData.entityAuth,
                      "entityReconciliationStatus"
                    )
                  }
                >
                  <yn-icon-svg
                    type={STATUS_ICON_MAP[value]}
                    class={STATUS_ICON_CLASS_MAP[vClass]}
                  />
                  {value}
                </yn-button>
              </div>
            );
          }
        });
        spanNode.onmousedown = e => {
          e.stopPropagation();
          return false;
        };
        childDiv.appendChild(spanNode);
        return childDiv;
      } else if (rowData.AccountGroup !== "合计") {
        const dom = genDOM(`<div class='status-text'>${value}</div>`);
        return dom;
      }
      return "";
    },
    renderCpiStatus(cellContext) {
      const { rowId } = cellContext;
      const rowData = this.getRowDataById(rowId);
      const value = rowData.icpReconciliationStatus;
      const vClass = rowData.icpIcon;
      if (rowData.AccountGroup === "小计") {
        const childDiv = document.createElement("div");
        const self = this;
        const disabled = !rowData.icpAuth || this.batchType;
        const spanNode = genVDOM({
          render(h) {
            return (
              <div>
                <yn-button
                  type="text"
                  class="operation-btn"
                  disabled={!!disabled}
                  onClick={() =>
                    self.showStatusModal(
                      {
                        title: "往来方对账数据确认",
                        data: rowData
                      },
                      rowData.icpAuth,
                      "icpReconciliationStatus"
                    )
                  }
                >
                  <yn-icon-svg
                    type={STATUS_ICON_MAP[value]}
                    class={STATUS_ICON_CLASS_MAP[vClass]}
                  />
                  {value}
                </yn-button>
              </div>
            );
          }
        });
        spanNode.onmousedown = e => {
          e.stopPropagation();
          return false;
        };
        childDiv.appendChild(spanNode);
        return childDiv;
      } else if (rowData.AccountGroup !== "合计") {
        const dom = genDOM(`<div class='status-text'>${value}</div>`);
        return dom;
      }
      return "";
    },
    renderOperationColumn(cellContext) {
      const { rowId, td } = cellContext;
      const rowData = this.getRowDataById(rowId);
      const {
        AccountGroup = {},
        ICP_ORIGVAL = {},
        Entity_ORIGVAL = {}
      } = rowData;
      const cellVal = AccountGroup;
      const IcpVal = ICP_ORIGVAL.id;
      const EntityVal = Entity_ORIGVAL.id;
      if (cellVal === SUBJECT_PARENT_NODE[1]) {
        // 操作列 只有科目为小计 才显示生成凭证
        const childDiv = document.createElement("div");
        const self = this;
        const spanNode = genVDOM({
          render(h) {
            return h(
              "yn-button",
              {
                class: "operation-btn",
                attrs: {
                  type: "text"
                },
                on: {
                  click: self.handleJump
                }
              },
              "生成调整凭证"
            );
          }
        });
        const drillNode = genVDOM({
          render(h) {
            return h(
              "yn-button",
              {
                class: "drill-btn",
                attrs: {
                  type: "text"
                },
                on: {
                  click: event => {
                    self.certificateDrill(event, cellContext, "subtotal");
                  }
                }
              },
              "凭证钻取"
            );
          }
        });
        spanNode.setAttribute("icp_val", IcpVal);
        spanNode.setAttribute("entity_val", EntityVal);
        if (this.authSet["adjustmentVoucher"]) {
          childDiv.appendChild(spanNode);
        }
        // 根据权限控制显示与否
        if (this.authSet["voucher-drilling"]) {
          childDiv.appendChild(drillNode);
        }
        td.appendChild(childDiv);
        return childDiv;
      }
    },
    renderHeaderCell(cellContext) {
      const { colName } = cellContext;
      const showBtnColName =
        this[COLUMN_HEADER_INDEX[this.columnExpanStatus.toString()]] ||
        this.showExpandBtnColumnKey;
      if (showBtnColName === colName) {
        return this.renderColHeader(cellContext);
      }
      return false;
    },
    renderColHeader(cellContext) {
      const { value, td } = cellContext;
      const childDiv = document.createElement("div");
      const wordTag = document.createElement("span");
      wordTag.textContent = value;
      childDiv.className = "header-col header-expand-btn";
      childDiv.appendChild(wordTag);
      if (this.isShowColumsExpandIcon()) {
        const iconButton = document.createElement("i");
        const iconClass = this.columnExpanStatus
          ? EXPAND_STATUS[1]
          : EXPAND_STATUS[0];
        iconButton.className = `iconfont icon-button icon-hover ${iconClass} row-header-icon-btn`;
        childDiv.appendChild(iconButton);
        iconButton.addEventListener(
          "mousedown",
          e => {
            // 切换
            this.clickExpandCollapseBtn(e, cellContext);
            e.stopPropagation();
          },
          true
        );
      }
      td.className += " grid-header";
      td.appendChild(childDiv);
      return childDiv;
    },
    hasSelectBox(row) {
      return row.AccountGroup === "小计";
    },
    handlerStatusOk() {
      this.$emit("refresh");
    },
    showStatusModal(data, auth, type) {
      if (!auth) {
        return;
      }
      this.statusModalInfo = {
        ...data,
        taskId: this.refreshIdOrTaskId,
        statusType: type,
        reconciliationId: this.reconciliationInfo.reconciliationId
      };
      this.statusModalVisible = true;
    },
    clickExpandCollapseBtn() {
      this.columnExpanStatus = !this.columnExpanStatus;
    },
    setShowExpandBtnColumnKey(columnInfo) {
      const firstChildAmountKey = this.auditTrailColumns[0];
      let expandColumnKey = "";
      columnInfo.some((item, index) => {
        const { key } = item;
        if (key === firstChildAmountKey) {
          expandColumnKey = columnInfo[index - 1].key;
          return true;
        }
      });
      this.showExpandBtnColumnKey = expandColumnKey;
    },
    initData(tableColumns, tableData) {
      // 初始化数据时，需要清除过滤条件
      this.filterSelectObj = {};
      this.filterCheckedObj = {};
      this.setFilterObj(tableColumns);
      this.$set(this, "mapTableData", {});
      this.showTableData = this.formatTableData(tableData);
      this.setShowExpandBtnColumnKey(tableColumns);
      this.initColums(tableColumns);
    },
    formatTableData(tableData, isSearch) {
      const list = [];
      const parentKeys = [];
      const data = cloneDeep(tableData);
      const mapData = {};
      // 深度优先遍历非递归方式
      const nodes = [...data];
      if (nodes.length) {
        while (nodes.length) {
          const item = nodes.shift();
          const { level, children, attributes, id } = item;
          this.setPropsToObj(attributes, item);
          this.setFilterSlectedData(item);
          item["ICP_ORIGVAL"] = attributes ? attributes.ICP : item.ICP;
          item["Entity_ORIGVAL"] = attributes ? attributes.Entity : item.Entity;
          if (typeof level === "undefined") {
            item.level = 0;
          }
          mapData[id] = item;
          if (children) {
            item.hasChildren = true;
            for (let i = 0, LEN = children.length; i < LEN; i++) {
              children[i].level = item.level + 1;
              nodes.unshift(children[i]);
            }
            parentKeys.push(id);
            // delete item.children;
          } else {
            delete item.children;
          }
          if (isSearch) {
            item.hasChildren = false;
          }
          list.push(item);
        }
      }
      this.$set(
        this,
        "mapTableData",
        Object.assign(this.mapTableData, mapData)
      );
      return data;
    },
    loadData(rowData) {
      const { objectId } = rowData;
      return this.requestDataByOffset({
        offset: 0,
        parentId: objectId
      });
    },
    setMapTableData(data) {
      const mapData = this.mapTableData;
      const tempArr = [...data];
      while (tempArr.length > 0) {
        const tempNode = tempArr.shift();
        const { objectId, children } = tempNode;
        mapData[objectId] = tempNode;

        if (children && children.length > 0) {
          Array.prototype.unshift.apply(tempArr, children);
        }
      }
      this.$set(this, "mapTableData", mapData);
    },
    getRowDataById(rowId) {
      return this.mapTableData[rowId] || {};
    },
    updateTable() {
      this.$refs.reportGrid.deleteFilteItemById("all");
      this.$refs.reportGrid.updateTableData(this.showTableData);
    },
    /**
     *@desc 是否是空值或者0
     *@param{String||Number}
     *@returns{Boolean}
     */
    isEmptyVal(numberVal) {
      const tempVal = numberVal;
      if (typeof tempVal === "string") {
        return !Number(tempVal.replace(/,/g, ""));
      }
      return !tempVal;
    },
    setPropsToObj(attributes, obj) {
      // const val = obj.indexes;
      const pId = obj.parentId;
      const { id: rowKey } = obj;
      Object.keys(attributes || {}).forEach(propName => {
        obj[propName] = this.getShowName({ ...obj, ...attributes }, propName);
      });
      obj.objectId = rowKey;
      obj._select = obj.indexes; // 选择框后数据
      // parentId 转换  单体公司对账 attributes 里面没有parentId
      obj.parentId = pId;
    },
    //  初始化 columns
    initColums(tableColumns) {
      if (this.columnExpanStatus) {
        this.setExpandedColums(tableColumns, []);
      } else {
        this.setNoExpandedColums(tableColumns);
      }
      this.setFirstAndLastCol();
    },
    // 设置过滤对象的key （除了金列跟差额列）
    setFilterObj(columns) {
      const filterSelectObj = {};
      const filterCheckedObj = {};
      const filterOrder = [];
      columns.forEach(item => {
        const { key, title } = item;
        if (key.indexOf(AMOUNT_IDENT) === -1 && key !== "Difference") {
          filterSelectObj[key] = [];
          filterCheckedObj[key] = [];
          MAPPING_DIM_NAME[key] = title;
          filterOrder.push(key);
        }
      });
      this.$set(this, "filterSelectObj", filterSelectObj);
      this.$set(this, "filterCheckedObj", filterCheckedObj);
      this.$set(this, "filterOrder", filterOrder);
    },
    setFilterSlectedData(rowData) {
      const filterColumnNames = Object.keys(this.filterSelectObj);
      Object.keys(rowData).map(item => {
        if (filterColumnNames.indexOf(item) !== -1) {
          const selectItems = this.filterSelectObj[item];
          const currVal = rowData[item].v;
          if (currVal && selectItems.indexOf(currVal) === -1) {
            this.filterSelectObj[item].push(currVal);
          }
        }
      });
    },
    // 设置 没有展开状态下的 表头数据
    setNoExpandedColums(tableColumns) {
      const columns = [];
      const data = cloneDeep(tableColumns);
      data.forEach(item => {
        const { key, title } = item;

        // 如果启用了列消零且该列是隐藏的零列，则跳过
        if (this.isColumnZeroActive && this.hiddenZeroColumns.includes(key)) {
          return;
        }

        // 审计线索 最后一个父项列
        const tempColumn = {
          dataIndex: key,
          readOnly: true,
          cellType: "text",
          align:
            key.indexOf(AMOUNT_IDENT) !== -1 || key === DIFFERENCE_COLUMN_KEY
              ? "right"
              : "",
          title
        };
        // 添加筛选solt
        this.addFilterSolt(tempColumn);
        if (key === this.showExpandBtnColumnKey) {
          // 金额子项列 收起按钮
          tempColumn.width = AMOUNT_CELL_WIDTH + 50;
          columns.push(tempColumn);
        } else if (this.auditTrailColumns.indexOf(key) === -1) {
          // 除金额子项列外
          // 设置固定列的solt
          columns.push(tempColumn);
        }
      });
      this.originFormateColumns = cloneDeep(columns);
      this.$set(this, "columns", columns);
    },
    // 设置 展开状态下的 表头数据
    setExpandedColums(tableColumns) {
      const columns = [];
      tableColumns.forEach((item, index) => {
        const { key, title } = item;

        // 如果启用了列消零且该列是隐藏的零列，则跳过
        if (this.isColumnZeroActive && this.hiddenZeroColumns.includes(key)) {
          return;
        }

        const columnItem = {
          dataIndex: key,
          readOnly: true,
          cellType: "text",
          title
        };
        // 添加筛选solt
        this.addFilterSolt(columnItem);
        // 设置金额、差额列
        if (key.indexOf(AMOUNT_IDENT) !== -1 || key === DIFFERENCE_COLUMN_KEY) {
          columnItem.align = "right";
          columnItem.width = AMOUNT_CELL_WIDTH;
        }
        // 修改差额列，差额列前一项 是需要展示列头收起按钮的
        if (key === DIFFERENCE_COLUMN_KEY) {
          this.showCollapseBtnColumnKey = columns[columns.length - 1].dataIndex;
          columns[columns.length - 1].width = AMOUNT_CELL_WIDTH + 50;
        }
        columns.push(columnItem);
      });
      this.originFormateColumns = cloneDeep(columns);
      this.$set(this, "columns", columns);
    },
    // 添加刷选solt
    addFilterSolt(columnObj) {
      const { dataIndex } = columnObj;
      // 判断当前key 是否包含Amount 文案，,不包含 并且不是差额列 则认定不是金额列
      if (
        dataIndex &&
        dataIndex.indexOf(AMOUNT_IDENT) === -1 &&
        dataIndex !== "Difference"
      ) {
        columnObj.isFilter = true;
      }
    },
    // 设置 第一个和最后一个column 数据
    setFirstAndLastCol() {
      if (!this.batchType) {
        this.columns = [
          {
            dataIndex: "indexes",
            width: 100,
            cellType: "text",
            title: "序号"
          },
          ...this.originFormateColumns
        ];
      } else {
        this.columns = [...this.originFormateColumns];
      }
      // 批量操作不展示操作列
      // 存在操作列中的按钮权限时，才展示操作列；没有任何按钮权限则不展示操作列
      let drillAuth = true;
      // 该判断用来解决初始化拿不到authSet的问题
      if (this.authSet && JSON.stringify(this.authSet) !== "{}") {
        // 存在 authSet 且不为空，那此时说明拥有权限集参数
        if (
          this.authSet["voucher-drilling"] ||
          this.authSet["adjustmentVoucher"]
        ) {
          drillAuth = true;
        } else {
          drillAuth = false;
        }
      }
      if (!this.batchType && drillAuth) {
        this.columns.push({
          dataIndex: "operationCol",
          title: "操作",
          cellType: "text",
          width: 200,
          fixed: "right",
          scopedSlots: {
            customRender: "operationColumn"
          }
        });
      }
    },
    /**
     * 根据权限集，删除操作列
     */
    setTableColumns(authSet) {
      // 存在操作列中的按钮权限时，才展示操作列；没有任何按钮权限则不展示操作列
      if (!(authSet["voucher-drilling"] || authSet["adjustmentVoucher"])) {
        this.columns = this.columns.filter(f => f.dataIndex !== "operationCol");
      }
    },
    // 是否展示列上的 展开收起按钮
    isShowColumsExpandIcon() {
      // 没有审计线索的 不显示列的展开收缩按钮
      if (!this.auditTrailColumns.length) return false;
      return true;
    },
    // 处理 列头的展开收起
    handleColumnExpand() {
      this.columnExpanStatus = !this.columnExpanStatus;
    },
    // 获取显示名称
    getShowName(rowData, cellName) {
      let tempVal = rowData[cellName]; // 临时值，可能是字符串，也可能是对象, boolean
      if (typeof tempVal === "boolean") {
        return tempVal;
      }
      if (typeof tempVal === "string") {
        if (tempVal.indexOf(NAME_SEPARATOR) === -1) {
          return tempVal;
        } else {
          const [name, code] = tempVal.split(NAME_SEPARATOR);
          tempVal = {
            code,
            name
          };
        }
      }
      const cellData = tempVal || {};
      const { name, code } = cellData;
      const lastCode = rowData.AccountGroup.code;
      // 科目列 合计 小计 只显示name
      if (
        cellName === SUBJECT_PROPR_NAME &&
        SUBJECT_PARENT_NODE.indexOf(lastCode) !== -1
      ) {
        return name;
      }
      if (this.cellType === "code") {
        return code || name;
      } else if (this.cellType === "nameAndCode") {
        return code ? `${code}-${name}` : name;
      }
      return name;
    },
    handleChecked(selectedKeys, cellName) {
      this.$set(this.filterCheckedObj, cellName, selectedKeys);
    },
    // 设置过滤信息
    setFilterInfo() {
      const res = [];
      Object.keys(this.filterCheckedObj).forEach(item => {
        const checkedList = this.filterCheckedObj[item];
        if (checkedList.length > 0) {
          res.push({
            title: MAPPING_DIM_NAME[item],
            searchArr: checkedList.map(checkedItem => {
              return {
                id: `${item}_${checkedItem}`,
                name: checkedItem
              };
            })
          });
        }
      });
      this.$set(this, "searchObj", res);
    },
    handleSearch(selectedKeys, confirm) {
      confirm();
      this.setFilterInfo();
      this.setSearchData();
    },
    // 设置查询数据
    getSearchData(searchObj, offset = 0) {
      const requestParams = {
        taskId: this.refreshIdOrTaskId,
        searchBody: [],
        limit: PAGE_SIZE,
        offset
      };
      requestParams.searchBody = searchObj.map(item => {
        const { type, searchArr } = item;
        return {
          dimCode: type,
          dimMemberNames: searchArr.map(obj => obj.name)
        };
      });
      return new Promise(res => {
        reconciliationService("searchReport", requestParams)
          .then(result => {
            const { data } = result.data;
            const { hasMore, items } = data;
            this.hasMore = hasMore;
            res({
              hasMore,
              data: this.formatTableData(items, true)
            });
          })
          .catch(() => {
            res({
              hasMore: false,
              data: []
            });
          })
          .finally(() => {
            this.$refs.reportGrid.$refs.simpleGrid.loading = false;
            this.$emit("update:spinning", false);
          });
      });
    },
    // 获取有效的过滤项key(指维度下有选择成员)
    getEffectiveFilterItemKey(searchInfo) {
      return searchInfo.map(item => {
        return item.type;
      });
    },
    // 是否有查询信息
    hasSearchInfo() {
      return Object.keys(this.filterCheckedObj).some(propName => {
        return this.filterCheckedObj[propName].length > 0;
      });
    },
    // 操作列 跳转
    handleJump(e) {
      // ICP Entity 成员名称 传入接口
      const spanDom = e.target;
      this.selectRowDimInfo = {
        ICP: spanDom.getAttribute("icp_val"),
        Entity: spanDom.getAttribute("entity_val")
      };
      this.visible = true;
    },
    frozenFirstCol() {
      this.$set(this, "frozenLeft", 2);
      this.$refs.reportGrid.setFixedColumns(2);
      this.$emit("update:frozenLeftIndex", 2);
      this.$emit("update:frozenColName", "");
    },
    forzenToCol() {
      // 计算列宽 是否超出窗口范围
      if (this.isOutRange()) {
        this.$refs.reportGrid.setFixedColumns(this.frozenLeft);
        this.$emit("update:frozenLeftIndex", this.frozenLeft);
        this.$emit("update:frozenColName", "");
      } else {
        UiUtils.errorMessage("冻结失败，冻结区域超出窗口");
      }
    },
    isOutRange() {
      const tableContWidth = this.$refs.reportGrid.$el.offsetWidth;
      const actionColWidth = this.$refs.reportGrid.cw.operationCol;
      return tableContWidth - actionColWidth > this.getFreezeColWidth();
    },
    getFreezeColWidth() {
      const { columns, frozenLeft } = this;
      const colWidthObj = this.$refs.reportGrid.currColWidthObj;
      let colWidth = 0;
      for (let i = 0, LEN = columns.length; i < LEN; i++) {
        const { dataIndex } = this.columns[i];
        colWidth += colWidthObj[dataIndex];
        if (i === frozenLeft - 1) {
          break;
        }
      }
      return colWidth;
    },
    cancelFrozen() {
      this.$emit("update:frozenColName", "");
      this.$set(this, "frozenLeft", 0);
      this.$refs.reportGrid.setFixedColumns(0);
      this.$emit("update:frozenLeftIndex", 0);
    },
    async addBefore(params) {
      const { templateId, templateName } = params;
      const dimMemberInfo = this.getDims();
      const requestParams = {
        templateId,
        templateName,
        paramMap: {
          ...dimMemberInfo
        }
      };
      let result = false;
      await reconciliationService("journalCheckParam", requestParams).then(
        res => {
          result = res.data.success;
        },
        rej => {
          result = false;
        }
      );
      return result;
    },
    async openDetail(type, record) {
      let dimMemberInfo = this.getDims();
      const { data } = await commonService("filterParamMap", dimMemberInfo);
      dimMemberInfo = data;
      const {
        data: { items }
      } = await commonService("getQueryDimMemberList", {
        dimCode: "Entity",
        needPermission: true,
        needFilterAudittrail: false,
        needFilterShared: false,
        limit: 10,
        offset: 0,
        memberExp: null,
        hasAllMembers: false,
        dimMemberId: dimMemberInfo.Entity
      });
      if (!items || !items.length) {
        delete dimMemberInfo.Entity;
        delete dimMemberInfo.ICP;
      }
      this.newtabMixin({
        id: record.journalId,
        title: `${record.journalName}`,
        uri: TAB_URI,
        router: TABNAME, // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
        params: {
          isAdd: true,
          ...record,
          pov: dimMemberInfo //  TODO POV 对账配置的指定维度、筛选维度 、及当前行选择的成员
        },
        newTabParams: {}
      });
    },
    getDims() {
      const { specifyMembers, filterMembers } = this.reconciliationConfig;
      const dimObj = {};
      // 合并组 默认G_NONE
      dimObj["Scope"] = DIM_INFO.SCOPE_G_NONE;
      filterMembers.forEach(item => {
        const { dimCode, dimMemberId } = item;
        dimObj[dimCode] = dimMemberId;
      });
      const specifyDimObj = {
        version: "Version",
        year: "Year",
        period: "Period",
        change: "Change",
        reportingCurrency: "ReportingCurrency"
      };
      Object.keys(specifyDimObj).forEach(key => {
        dimObj[specifyDimObj[key]] = specifyMembers[key];
      });
      Object.assign(dimObj, omit(this.titleMap, ["Audittrail"]));
      Object.assign(dimObj, this.selectRowDimInfo);
      return dimObj;
    },
    showDimMemberName(item, colName) {
      // const {name,id} = item;
      const WORD_MAPPING = { code: "code", name: "name" };
      const PROPS_ARR = ["code", "name"];
      const { name, code } = item;
      if (PROPS_ARR.indexOf(this.cellType) !== -1) {
        return item[WORD_MAPPING[this.cellType]] || item.name;
      } else {
        // 科目列，小计、合计 只显示小计、合计
        if (
          colName === SUBJECT_PROPR_NAME &&
          SUBJECT_PARENT_NODE.indexOf(name) !== -1
        ) {
          return code || name;
        } else {
          return code ? `${code}-${name}` : name;
        }
      }
    },
    // 新增：检测并隐藏全零列
    detectAndHideZeroColumns() {
      // 如果当前列未展开，先展开所有列以确保检测准确性
      const needExpand = !this.columnExpanStatus && this.auditTrailColumns.length > 0;
      if (needExpand) {
        console.log("列消零检测：先展开所有列以确保检测准确性");
        this.columnExpanStatus = true;
        // 等待列展开完成后再进行检测
        this.$nextTick(() => {
          this.performZeroColumnDetection();
        });
      } else {
        this.performZeroColumnDetection();
      }
    },
    // 新增：执行实际的零列检测
    performZeroColumnDetection() {
      // 数据结构调试
      const allData = this.getAllTableData();
      if (allData.length > 0) {
        console.log("第一行数据结构:", allData[0]);
        console.log("第一行所有属性:", Object.keys(allData[0]));
      }
      // 使用当前已加载的所有数据进行检测
      const zeroColumns = this.getZeroColumns();
      this.hiddenZeroColumns = zeroColumns;
      this.updateColumnsVisibility();
      // 控制台输出检测结果，便于调试
      if (zeroColumns.length > 0) {
        console.log("检测到全零列:", zeroColumns);
      } else {
        console.log("未检测到全零列");
      }
    },
    // 新增：恢复所有列的显示
    restoreAllColumns() {
      this.hiddenZeroColumns = [];
      this.updateColumnsVisibility();
    },
    // 新增：获取全零的金额列
    getZeroColumns() {
      const zeroColumns = [];
      const allData = this.getAllTableData();
      console.log("=== 列消零检测开始 ===");
      console.log("所有列信息:", this.originalTableColumns.map(col => ({ key: col.key, title: col.title })));
      console.log("数据行数:", allData.length);
      // 遍历所有列，找出金额列（包括隐藏的审计线索列）
      this.originalTableColumns.forEach(column => {
        const { key, title } = column;
        // 只检查金额列（包含Amount标识但不是差额列）
        if (key.indexOf(AMOUNT_IDENT) !== -1 && key !== DIFFERENCE_COLUMN_KEY) {
          console.log(`检测金额列: ${key} (${title})`);
          // 检查该列是否所有数据都为0
          const isZeroColumn = this.isColumnAllZero(key, allData);
          if (isZeroColumn) {
            zeroColumns.push(key);
            console.log(`列 ${key} (${title}) 被标记为全零列`);
          } else {
            console.log(`列 ${key} (${title}) 包含非零值，保持显示`);
          }
        }
      });

      console.log(`总共检测了 ${this.originalTableColumns.filter(col => col.key.indexOf(AMOUNT_IDENT) !== -1 && col.key !== DIFFERENCE_COLUMN_KEY).length} 个金额列`);
      console.log("=== 列消零检测结束 ===");
      return zeroColumns;
    },
    // 新增：获取所有表格数据（包括展开的数据）
    getAllTableData() {
      // 优先使用mapTableData（包含所有已加载的数据），其次使用显示数据
      let allData = [];
      if (Object.keys(this.mapTableData).length > 0) {
        allData = Object.values(this.mapTableData);
      } else if (this.showTableData && this.showTableData.length > 0) {
        allData = this.showTableData;
      } else if (this.originalTableData && this.originalTableData.length > 0) {
        // 如果前两者都没有，使用原始数据
        allData = this.originalTableData;
      }
      return allData;
    },
    // 新增：检查指定列是否所有值都为0
    isColumnAllZero(columnKey, tableData) {
      // 如果没有数据，认为不是零列
      if (!tableData || tableData.length === 0) {
        console.log(`列 ${columnKey}: 没有数据，不是零列`);
        return false;
      }

      let nonZeroCount = 0;
      let totalDataRows = 0;
      let undefinedCount = 0;
      const sampleValues = [];

      // 遍历所有数据行，检查该列的值
      for (let i = 0; i < tableData.length; i++) {
        const row = tableData[i];

        // 安全检查：确保行数据存在
        if (!row) {
          continue;
        }

        // 尝试多种方式获取列值
        let cellValue = row[columnKey];

        // 如果直接访问失败，尝试通过attributes访问
        if (cellValue === undefined && row.attributes && row.attributes[columnKey]) {
          cellValue = row.attributes[columnKey];
        }

        // 记录前几个样本值用于调试
        sampleValues.push({
          accountGroup: row.AccountGroup,
          cellValue: cellValue,
          hasAttributes: !!row.attributes,
          isEmpty: cellValue === undefined ? "undefined" : this.isEmptyVal(cellValue)
        });

        // 统计undefined的数量
        if (cellValue === undefined) {
          undefinedCount++;
          continue;
        }

        // 只检查非合计行和非小计行（只检查实际的数据行）
        // if (row.AccountGroup !== "合计" && row.AccountGroup !== "小计") {
        totalDataRows++;
        // 如果该列有非零值，则不是零列
        if (!this.isEmptyVal(cellValue)) {
          nonZeroCount++;
          console.log(`列 ${columnKey}: 在行 ${row.AccountGroup || "未知"} 中发现非零值:`, cellValue);
          console.log(`  具体值:`, cellValue);
          console.log(`  值类型:`, typeof cellValue);
          console.log(`  isEmptyVal结果:`, this.isEmptyVal(cellValue));
          return false;
        }
        // }
      }

      console.log(`列 ${columnKey}: 检查了 ${tableData.length} 行数据`);
      console.log(`  - 非合计数据行: ${totalDataRows}`);
      console.log(`  - undefined值: ${undefinedCount}`);
      console.log(`  - 非零值: ${nonZeroCount}`);
      console.log(`  - 样本数据:`, sampleValues);

      return totalDataRows > 0 && nonZeroCount === 0; // 只有当确实有数据行且都为零时才返回true
    },
    // 新增：更新列的显示状态
    updateColumnsVisibility() {
      // 重新初始化列配置
      this.initColums(this.originalTableColumns);
    },
    // 新增：手动重新检测零列（供外部调用）
    reDetectZeroColumns() {
      if (this.isColumnZeroActive) {
        this.hiddenZeroColumns = []; // 清空缓存
        this.detectAndHideZeroColumns();
      }
    },

    // 临时调试方法：手动检查特定列
    debugSpecificColumn(columnKey) {
      const allData = this.getAllTableData();
      console.log(`=== 调试列 ${columnKey} ===`);

      if (allData.length > 0) {
        // 显示前几行数据中该列的值
        for (let i = 0; i < Math.min(5, allData.length); i++) {
          const row = allData[i];
          let cellValue = row[columnKey];

          // 尝试从attributes获取
          if (cellValue === undefined && row.attributes) {
            cellValue = row.attributes[columnKey];
          }

          console.log(`行 ${i}: AccountGroup=${row.AccountGroup}, 值=`, cellValue, `isEmpty=${this.isEmptyVal(cellValue)}`);
        }
      }
      console.log(`=== 调试结束 ===`);
    }
  }
};
</script>
<style lang="less">
.reportCont {
  .operation-btn,
  .drill-btn {
    display: inline-block;
    height: 30px;
    line-height: 30px;
    // padding: 0 @yn-padding-l;
    border-radius: @yn-border-radius-base;
    color: @yn-primary-color;
  }
  .status-text {
    padding-left: 2.0625rem;
    color: @yn-text-color-secondary;
  }
  .link-btn:hover {
    background-color: @yn-icon-bg-color;
  }
  .subtotal {
    color: @yn-error-color;
  }
  div.reportTable td.leafNode {
    background-color: @yn-disabled-bg-color;
    position: relative;
  }
  .htColHeaderBg {
    background-color: @yn-table-header-bg !important;
  }
  .header-expand-btn > span {
    display: inline-block;
    width: calc(100% - 40px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .handsontable td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  td.simple-cell-readonly {
    background-color: @yn-component-background;
  }
  .leafNodeAmount::after {
    content: url(../../../../image/c1_cr_form_enter.svg);
    position: absolute;
    top: 9px;
    cursor: pointer;
    right: -1px;
  }
  .handsontable td.Difference,
  .handsontable td.Difference-cell {
    padding: 0 10px 0 16px;
  }
  .drillMenu > ul {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: background 0.3s, width 0.3s cubic-bezier(0.2, 0, 0, 1) 0s;
    border-radius: 0.25rem;
  }
}
</style>
<style lang="less" scoped>
.reportTableCont {
  height: 100%;
  display: flex;
  flex-flow: column;
}
.reportCont {
  overflow: hidden;
  .reportTable {
    height: calc(100% - 3rem);
    padding: @yn-margin-l @yn-margin-xl 0;
    background: @yn-component-background;
  }
  .iconBtn {
    cursor: pointer;
    margin-left: @yn-margin-s;
  }
  .rowExpandIcon {
    cursor: pointer;
    margin-right: @yn-margin-s;
  }
  .ident1 {
    padding-left: @yn-padding-l;
  }
  span.ident0 {
    height: 15px;
    display: inline-block;
  }

  .amountColumn {
    display: inline-block;
    width: 100%;
    text-align: right;
  }
  .jumpOperation {
    color: @yn-chart-1;
    cursor: pointer;
  }
  .footer {
    height: 3rem;
    line-height: 3rem;
    width: 100%;
    background: @yn-body-background;
    padding: 0 1rem;
    font-size: @yn-font-size-base;
  }
  .footer_amount {
    font-weight: 600;
    color: @yn-money-color;
  }
  .drillMenu {
    position: absolute;
    min-width: 12.5rem;
    z-index: 10000;
    // padding: 50px 50px 0 0;
    /deep/& > ul {
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      transition: background 0.3s, width 0.3s cubic-bezier(0.2, 0, 0, 1) 0s;
      border-radius: 0.25rem;
    }
  }
}
.footerCont {
  border-top: 1px solid @yn-border-color-base;
}
/deep/ .check-circle {
  color: @yn-success-color;
}
/deep/ .information {
  color: @yn-warning-color;
}
/deep/ .delete-fill {
  color: @yn-disabled-color;
}

/deep/ ._select-cell.simple-cell {
  position: relative;
}
/deep/ ._select-cell.simple-cell > div:first-of-type {
  margin-left: 1.875rem;
}
/deep/ .dom-select-container {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translate(0, -50%);
}
/deep/ ._select .select-title {
  margin-left: 1.875rem;
}
</style>
