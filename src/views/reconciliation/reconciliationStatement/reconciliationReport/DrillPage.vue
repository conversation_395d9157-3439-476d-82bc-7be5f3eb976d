<template>
  <div class="openRelateModal">
    <yn-modal
      width="80%"
      :title="title"
      wrapClassName="drillDownModalWrapper"
      dialogClass="drillDownModal"
      :visible="true"
      :destroyOnClose="true"
      :closable="true"
      :maskClosable="false"
      @cancel="handleCancel"
    >
      <div class="iframeCT">
        <iframe :src="url"></iframe>
      </div>
    </yn-modal>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-checkbox/";
import "yn-p1/libs/components/yn-tree-select";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-tooltip/";
export default {
  name: "DrillPageModal",
  components: {},
  props: {
    url: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    }
  },
  methods: {
    handleCancel(item, index) {
      this.$emit("closeModal");
    }
  }
};
</script>
<style>
.ant-modal-wrap.drillDownModalWrapper .drillDownModal {
  height: 80%;
}
.drillDownModal .ant-modal-content {
  height: 100%;
}
.drillDownModal .ant-modal-footer {
  display: none;
}
.drillDownModal .ant-modal-body {
  display: flex;
  padding: 0;
  min-height: unset;
  height: calc(100% - 45px);
}
.drillDownModal .ant-modal-body .iframeCT {
  display: flex;
  flex: 1;
  width: 100%;
  height: 100%;
}
.drillDownModal .ant-modal-body .iframeCT > iframe {
  flex: 1;
  border: none;
}
</style>
