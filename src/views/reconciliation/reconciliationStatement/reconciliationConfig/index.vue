<template>
  <yn-drawer
    title="对账配置"
    placement="right"
    :width="632"
    :visible="drawerVisible"
    :maskClosable="false"
    @close="onClose"
  >
    <yn-spin :spinning="spinning">
      <div class="config-cont">
        <specify-dimension
          ref="specifyDimension"
          :isVerification="isChecked"
          :dimInfo="specifyMembers"
          @changeLoadStatus="changeLoadStatus"
        />
        <custom-dim-member
          ref="customDimMember"
          :filterMembers="filterMembers"
          :isVerification="isChecked"
          :listAreaMember="listMembers"
          @changeLoadStatus="changeLoadStatus"
        />
        <subject-setting
          ref="subjectSetting"
          :isVerification="isChecked"
          :subject="accountType"
          :data="accountData"
          :coordinationStatus="coordinationStatus"
          :coordinationToleranceValue="coordinationToleranceValue"
          :coordinationToleranceType="coordinationToleranceType"
          :journalTemplate="journalTemplate"
          @changeLoadStatus="changeLoadStatus"
        />
      </div>
    </yn-spin>
    <div class="footer">
      <yn-button class="closeDrawer" @click="onClose"> 取消 </yn-button>
      <yn-button
        v-if="!creatorFlag"
        type="primary"
        :loading="loading"
        @click="saveByType('saveAs')"
      >
        另存为
      </yn-button>
      <yn-button
        v-else
        type="primary"
        :loading="loading"
        @click="saveByType('save')"
      >
        保存
      </yn-button>
    </div>
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-spin/";
import SpecifyDimension from "./SpecifyDimension";
import CustomDimMember from "./customDimMember";
import SubjectSetting from "./SubjectSetting";
import { EventBus } from "yn-p1/libs/utils/ComponentUtils";
import reconciliationService from "@/services/reconciliation";
import reconciliationCommon from "@/views/reconciliation/minix";
import { mapState } from "vuex";
import cloneDeep from "lodash/cloneDeep";
import Logger from "yn-p1/libs/modules/log/logger";
import UiUtils from "yn-p1/libs/utils/UiUtils";

const ACCOUN_TTYPES = [1, 0]; // 插式账户1（默认值） 指定科目0
export default {
  name: "ReconcoilationConfig",
  components: {
    SpecifyDimension,
    CustomDimMember,
    SubjectSetting
  },
  mixins: [reconciliationCommon],
  props: {
    drawerVisible: Boolean,
    operationType: {
      type: String,
      default() {
        return "add"; // 打开的方式 edit or add
      }
    },
    isReportPage: Boolean,
    selectTreeNodeInfo: {
      type: Object,
      default: () => {}
    }
  },
  provide() {
    return {
      changeLoadStatusPro: this.changeLoadStatus,
      isEmptyConfig: this.hasConfig
    };
  },
  inject: ["showSaveAsModal"],
  data() {
    return {
      spinning: true,
      creatorFlag: true, // true 当前用户创建的对账报告，flase 不是
      specifyMembers: {}, // 指定维度成员
      filterMembers: [], // 筛选区域成员
      listMembers: [], // 列表区域成员
      accountType: ACCOUN_TTYPES[0], // 插式账户1（默认值） 指定科目0
      accountData: [],
      coordinationStatus: [],
      loading: false,
      isChecked: false, //  是否校验
      loadingInfo: {
        filterMembers: false,
        specifyMembers: false,
        subjectSetting: false
      },
      reconciliationConfigObj: {},
      selectTreeNode: {},
      journalTemplate: [], // 差异调整模板信息
      slotRef: ""
    };
  },
  computed: {
    ...mapState({
      currSelectTreeNode(state) {
        const namespace = this.getNamespace();
        return state[namespace].selectTreeNode;
      },
      configObj(state) {
        const namespace = this.getNamespace();
        return state[namespace].reconciliationConfigObj;
      },
      hasAuthority(state) {
        const namespace = this.getNamespace();
        return state[namespace].hasAuthority;
      }
    })
  },
  watch: {
    operationType: {
      handler(newVal) {
        if (newVal === "edit") {
          const { attr } = this.getSelectTreeNode() || {};
          this.creatorFlag = attr ? attr.creatorFlag : true;
          this.spinning = true;
        } else {
          this.creatorFlag = true;
        }
      },
      immediate: true
    },
    drawerVisible: {
      async handler(newVal) {
        if (!newVal) return;
        if (this.operationType === "add") {
          await this.getReconciliationSettingData();
        } else {
          this.setReconciliationConfigObj();
          await this.getReconciliationConfigData();
          const { attr } = this.getSelectTreeNode() || {};
          this.creatorFlag = attr ? attr.creatorFlag : true;
          this.spinning = false;
        }
      },
      immediate: true
    },
    loadingInfo: {
      handler(newVal) {
        const noHasLoadStatus = Object.values(newVal).every(item => item);
        // 新增 对账 配置需要等到指定维度成员 区域、筛选区域维度成员区域全部加载完 在关闭loading
        if (noHasLoadStatus) {
          this.spinning = false;
        } else {
          this.spinning = true;
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    setReconciliationConfigObj() {
      const { id } = this.getSelectTreeNode();
      // 新版页签并且是报告页面从session里面获取对账配置信息
      if (!this.selfTab && this.isReportPage) {
        const { reconciliationConfigObj } = this.getTabParamsMixin();
        this.$set(this, "reconciliationConfigObj", reconciliationConfigObj[id]);
      } else {
        this.$set(this, "reconciliationConfigObj", this.configObj[id]);
      }
    },
    getSelectTreeNode() {
      return this.isReportPage
        ? this.selectTreeNodeInfo
        : this.currSelectTreeNode;
    },
    changeLoadStatus(type, status) {
      if (type) {
        this.loadingInfo[type] = status;
      }
    },
    onClose() {
      this.$emit("closeDrawer");
    },
    async saveByType(type) {
      this.loading = true;
      const saveParams = await this.getSaveParams();
      let isVerificationSuccess = this.verificationRequired(saveParams);
      if (isVerificationSuccess) {
        await reconciliationService(
          "checkJournalTemplateExist",
          saveParams.journalTemplate
        ).then(res => {
          const { data } = res.data;
          if (data) {
            isVerificationSuccess = false;
            UiUtils.errorMessage(data);
          }
        });
      }
      if (isVerificationSuccess) {
        try {
          this[`${type}Event`] && (await this[`${type}Event`](saveParams));
        } finally {
          this.loading = false;
        }
      } else {
        // 校验失败
        this.isChecked = true;
        this.$nextTick(() => {
          this.loading = false;
          // valid-prop
          const errorTipsDom = document.querySelector(".errorTips");
          const validPropDom = document.querySelector(".valid-prop");
          if (errorTipsDom) {
            errorTipsDom.scrollIntoView();
          } else {
            validPropDom && validPropDom.scrollIntoView();
          }
        });
      }
    },
    saveAsEvent(saveParams) {
      if (!this.isReportPage) {
        EventBus.trigger("openReportFormModalByType", {
          type: "saveAs",
          reconciliationConfig: saveParams
        });
      } else {
        this.showSaveAsModal && this.showSaveAsModal(cloneDeep(saveParams));
      }
    },
    async saveEvent(saveParams) {
      await reconciliationService("saveReconciliationConfig", saveParams).then(
        res => {
          // 重新选择 树节点
          const treeNodeInfo = cloneDeep(this.getSelectTreeNode());
          this.$store.commit(
            this.getMutationOrActionType("setSelectTreeNode"),
            treeNodeInfo
          );
          const { id } = treeNodeInfo;
          this.$store.commit(this.getMutationOrActionType("setConfig"), {
            id,
            config: saveParams
          });
          if (!this.selfTab) {
            const { reconciliationConfigObj, tabId, ...otherObj } =
              this.getTabParamsMixin() || {};
            Logger.log(reconciliationConfigObj);
            tabId &&
              this.setTabParamsMixin(tabId, {
                ...otherObj,
                tabId,
                reconciliationConfigObj: this.configObj
              });
          }
          UiUtils.successMessage("保存成功！");
          if (this.isReportPage) {
            this.$emit("saveConfigCb", saveParams);
          } else {
            this.onClose();
          }
        }
      );
    },
    async getSaveParams() {
      const specifyMembers = this.$refs.specifyDimension.getSaveParams();
      const {
        filterMembers,
        listMembers
      } = this.$refs.customDimMember.getSaveParams();
      const {
        accountType,
        accountData,
        journalTemplate,
        coordinationStatus,
        coordinationToleranceType,
        coordinationToleranceValue,
        validate
      } = this.$refs.subjectSetting.getSaveParams();
      const { id } = this.getSelectTreeNode();
      return {
        reconciliationId: id,
        specifyMembers,
        filterMembers,
        listMembers,
        accountType,
        accountData,
        journalTemplate,
        coordinationStatus,
        coordinationToleranceType,
        coordinationToleranceValue,
        validate
      };
    },
    verificationRequired(params) {
      const {
        specifyMembers,
        filterMembers,
        listMembers,
        accountData,
        accountType,
        validate
      } = params;
      // 对账差异
      if (!validate) {
        return false;
      }
      // 指定维度成员校验
      const hasNoSelDimMember = Object.values(specifyMembers).some(
        item => !item
      );
      if (hasNoSelDimMember) {
        return false;
      }
      // 筛选区域成员校验
      const hasEmptyVal = filterMembers.some(item => !item.dimMemberId);
      if (hasEmptyVal) {
        return false;
      }
      // 列表区域成员 校验
      const hasDimNoSelMember = listMembers.some(
        item => !item.selectedItem.length
      );
      if (hasDimNoSelMember) {
        return false;
      }
      const lastRowData = accountData.slice(-1)[0];
      let hasEmptyCell = false;
      //  插式账户
      if (accountType) {
        hasEmptyCell = !lastRowData.account;
      } else {
        // 指定科目
        hasEmptyCell = !lastRowData.account || !lastRowData.otherAccount;
      }
      if (hasEmptyCell) {
        return false;
      }
      return true;
    },
    // 获取对账配置数据
    async getReconciliationConfigData() {
      if (this.hasConfig()) {
        await this.getReconciliationSettingData();
        return;
      }
      this.setEchoData(this.reconciliationConfigObj);
      this.spinning = false; // 关闭loading
    },
    hasConfig() {
      return !Object.values(this.reconciliationConfigObj).some(item => {
        if (Array.isArray(item)) {
          return item.length;
        } else {
          return !!item;
        }
      });
    },
    //  获取对账设置信息
    async getReconciliationSettingData() {
      // 调用 接口获取对账 设置信息
      await reconciliationService("getCustomDim")
        .then(res => {
          const { data, status } = res;
          const resultList = [];
          if (status === 200) {
            data &&
              data.map(item => {
                const { dimId, dimName, dimCode } = item;
                resultList.push({
                  dimId,
                  dimName,
                  dimCode,
                  dimMemberId: ""
                });
              });
            this.setEchoData({
              filterMembers: resultList
            });
            this.spinning = false;
          }
        })
        .catch(e => {
          this.spinning = false;
        });
    },
    // 设置回显数据 将请求的数据赋值给data 对应的属性
    setEchoData(data) {
      Object.keys(data).map(propName => {
        this[propName] = data[propName];
      });
    },
    // 设置loading信息
    setLoaddingInfo(type, status) {}
  }
};
</script>
<style lang="less" scoped>
.config-cont {
  width: 100%;
  height: calc(100% - 2.75rem);
  overflow: scroll;
  padding-left: @yn-padding-xxl;
  padding-right: @yn-padding-xxl;
  padding-top: @rem24;
}
/deep/.ant-drawer-wrapper-body {
  overflow: hidden;
}
/deep/.ant-drawer-body {
  padding-bottom: 88px;
  padding: 0;
  height: calc(100% - 40px);
}
.footer {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid @yn-border-color-base;
  padding: 10px @yn-padding-xl;
  background: @yn-body-background;
  text-align: right;
  z-index: 1;
  .closeDrawer {
    margin-right: @yn-margin-s;
  }
}
/deep/.errorTips {
  position: absolute;
  color: @yn-error-color;
  line-height: 1.5;
  z-index: 1;
}
</style>
