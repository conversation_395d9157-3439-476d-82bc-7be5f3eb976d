<template>
  <div class="specifyDimensionMembers">
    <span class="title"><yn-divider type="vertical" class="sign" />指定维度成员</span>
    <yn-row v-for="(item, index) in specifyDimInfo" :key="index" class="dimRow">
      <yn-col
        v-for="dimObj in item"
        :key="dimObj.id"
        :span="12"
        class="dimItemCont"
      >
        <span class="require">{{ dimObj.name }}</span>
        <div class="dimSelectCont">
          <asyn-select-dimMember
            :ref="dimObj.id"
            class="dimSelectTree"
            forceRender
            nonleafselectable
            :dimCode="dimObj.dimCode"
            :value="selectTreeNodeIds[dimObj.id]"
            :isSetDefaultVal="isSetDefaultVal(dimObj.id)"
            :allowClear="false"
            :loadComplete="$event => loadComplete($event, dimObj.id)"
            @changeVal="value => onChange(value, dimObj.id)"
          />
          <span
            v-if="!selectTreeNodeIds[dimObj.id] && isVerification"
            class="errorTips"
          >
            {{ `请选择‘${dimObj.name}’的成员` }}
          </span>
        </div>
      </yn-col>
    </yn-row>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-row/";
import "yn-p1/libs/components/yn-col/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-divider/";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
export default {
  name: "SpecifyDimension",
  components: { AsynSelectDimMember },
  props: {
    dimInfo: {
      type: Object,
      default() {
        return {};
      }
    },
    isVerification: {
      type: Boolean
    }
  },
  data() {
    return {
      isChecked: false,
      specifyDimInfo: [
        [
          { name: "版本", id: "version", dimCode: "Version" },
          { name: "年", id: "year", dimCode: "Year" }
        ],
        [
          { name: "期间", id: "period", dimCode: "Period" },
          {
            name: "报告币",
            id: "reportingCurrency",
            dimCode: "ReportingCurrency"
          }
        ],
        [
          { name: "审计线索", id: "audittrail", dimCode: "Audittrail" },
          { name: "增减变动", id: "change", dimCode: "Change" }
        ]
      ],
      selectTreeNodeIds: {
        version: "",
        year: "",
        period: "",
        reportingCurrency: "",
        audittrail: "",
        change: ""
      },
      loadNum: 0 // 下拉树 加载 次数
    };
  },
  watch: {
    dimInfo: {
      handler(newVal) {
        Object.keys(newVal).map(item => {
          this.selectTreeNodeIds[item] = newVal[item];
        });
      },
      immediate: true
    }
  },
  methods: {
    isSetDefaultVal(id) {
      return ["audittrail", "change"].indexOf(id) !== -1;
    },
    loadComplete() {
      this.loadNum++;
      if (this.loadNum === 6) {
        this.$emit("changeLoadStatus", "specifyMembers", true);
      }
    },
    onChange(val, type) {
      this.selectTreeNodeIds[type] = val;
    },
    getSaveParams() {
      return this.selectTreeNodeIds;
    }
  }
};
</script>
<style lang="less" scoped>
.require::before {
  display: inline-block;
  margin-right: 4px;
  color: @yn-error-color;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: "*";
}
.specifyDimensionMembers {
  margin-bottom: @yn-margin-l;
  .title {
    font-size: @yn-font-size-base;
    margin-bottom: @yn-margin-xl;
    display: inline-block;
    font-weight: 600;
  }
}
.dimRow {
  padding-bottom: @yn-padding-xxl;
}
.dimItemCont {
  display: flex;
  justify-content: flex-end;
  & > span {
    line-height: @rem32;
    margin-right: @yn-margin-s;
    color: @yn-label-color;
  }
  .dimSelectCont {
    position: relative;
    width: 65%;
  }
  .dimSelectTree {
    width: 100%;
  }
}
.sign {
  width: 2.5px;
  background: @yn-primary-color;
  margin-left: 0;
  margin-right: 0.375rem;
  border-radius: 0.625rem;
}
</style>
