<template functional>
  <div class="emptyDimCont">
    <span class="tips">
      当前{{ type == "filterArea" ? "筛选" : "列表" }}区域成员为空，可
      <font class="heightlight">“拖拽”</font>
      {{ type == "filterArea" ? "列表" : "筛选" }}区域成员添加
    </span>
  </div>
</template>
<script>
export default {
  name: "EmptyDimMember",
  props: {
    // eslint-disable-next-line vue/require-default-prop
    tips: String
  }
};
</script>
<style lang="less" scoped>
.tips {
  color: @yn-label-color;
  & > .keyWord {
    color: @yn-warning-color;
  }
}
.emptyDimCont {
  height: 88px;
  line-height: 88px;
  text-align: center;
  background: @yn-background-color;
  border: 1px dashed @yn-border-color-base;
  margin: 12px 0;
}
.heightlight {
  color: @yn-warning-color;
}
</style>
