<template>
  <div class="customDimMember">
    <div class="title">
      <span><yn-divider type="vertical" class="sign" />自定义维度成员</span>
      <span class="tips">
        可
        <font class="keyword heightlight">“拖拽”</font>
        筛选区域和列表区域成员，调整成员区域
      </span>
    </div>
    <div class="memberCont">
      <filter-area-member
        ref="filterAreaMember"
        :isVerification="isVerification"
        :dims="filterAreaDimCodeArr"
        @dragStart="dragStart"
        @dragEnd="dragEnd"
        @setDimInfo="setDimInfo"
      />
      <list-area-member
        ref="listAreaMember"
        :isVerification="isVerification"
        :dims="listAreaDimCodeArr"
        @dragStart="dragStart"
        @dragEnd="dragEnd"
        @setDimInfo="setDimInfo"
      />
    </div>
  </div>
</template>
<script>
import FilterAreaMember from "./filterAreaMember.vue";
import ListAreaMember from "./listAreaMember.vue";
import cloneDeep from "lodash/cloneDeep";
import "yn-p1/libs/components/yn-divider/";
export default {
  name: "CustomDimMember",
  components: {
    FilterAreaMember,
    ListAreaMember
  },
  props: {
    filterMembers: {
      type: Array,
      default() {
        return [];
      }
    },
    listAreaMember: {
      type: Array,
      default() {
        return [];
      }
    },
    isVerification: {
      type: Boolean
    }
  },
  data() {
    return {
      filterAreaDimCodeArr: [],
      listAreaDimCodeArr: [],
      dragInfo: {} // 拖动的信息
    };
  },
  watch: {
    filterMembers: {
      handler(newVal) {
        this.filterAreaDimCodeArr = [...newVal];
      },
      deep: true,
      immediate: true
    },
    listAreaMember: {
      handler(newVal) {
        if (newVal.length) {
          this.listAreaDimCodeArr = this.formatListAreaMember(newVal);
        } else {
          this.listAreaDimCodeArr = [];
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    formatListAreaMember(data) {
      const resData = cloneDeep(data);
      resData.map(item => {
        item.selectedItem = this.formatSelectedItem(item.selectedItem);
      });
      return resData;
    },
    formatSelectedItem(selectedItem) {
      return selectedItem.map(item => {
        const { dimMemberId, dimMemberName, dimMemberShared, dimCode } = item;
        return {
          dimCode,
          dimMemberId,
          dimMemberName,
          dimMemberShared,
          key: `${dimMemberId}-self`,
          label: dimMemberName,
          memberType: 1,
          memberTypeValue: "self",
          objectId: `${dimMemberId}-self`,
          shardim: dimMemberShared,
          title: `成员(${dimMemberName})`,
          type: "member",
          value: dimMemberId
        };
      });
    },
    dragStart(index, type) {
      this.dragInfo = {
        index,
        type
      };
    },
    dragEnd(index, type) {
      const endPropName = `${type}AreaDimCodeArr`;
      const { type: startType, index: sIndex } = this.dragInfo;
      const startPropName = `${startType}AreaDimCodeArr`;
      const { dimId, dimCode, dimName, selectedItem } = this[
        startPropName
      ].splice(sIndex, 1)[0];
      const res = {
        dimName,
        dimCode,
        dimId,
        selectedItem: startPropName === endPropName ? selectedItem : []
      };
      if (type === "filter") {
        res["dimMemberId"] = "";
      }
      this[endPropName].splice(index, 0, res);
    },
    getSaveParams() {
      const { listMembers } = this.$refs.listAreaMember.getSaveParams();
      const { filterMembers } = this.$refs.filterAreaMember.getSaveParams();
      return {
        listMembers,
        filterMembers
      };
    },
    setDimInfo(index, dimInfo, type) {
      this[`${type}DimCodeArr`].splice(index, 1, dimInfo);
    }
  }
};
</script>
<style lang="less" scoped>
.customDimMember {
  .title {
    & > span {
      font-size: @yn-font-size-base;
      margin-bottom: @yn-margin-xxl;
      display: inline-block;
    }
    span:first-child {
      font-weight: 600;
    }
    & > span.tips {
      color: @yn-label-color;
      padding-left: 24px;
    }
  }
}
.sign {
  width: 2.5px;
  background: @yn-primary-color;
  margin-left: 0;
  margin-right: 0.375rem;
  border-radius: 0.625rem;
}
.heightlight {
  color: @yn-warning-color;
}
</style>
