<template>
  <div class="filterAreaCont">
    <span>筛选区域成员</span>
    <vuedraggable
      v-model="data"
      class="filterArea"
      group="site"
      ghostClass="ghost"
      handle=".dimSelectItem>span"
      :move="onMove"
      @start="onStart"
      @end="onEnd"
      @unchoose="unchoose"
    >
      <template v-if="data.length > 0">
        <div
          v-for="item in data"
          :key="item.dimCode"
          :class="item.isEmpty ? 'emptyCont' : 'dimSelectItem'"
        >
          <template v-if="!item.isEmpty">
            <span>{{ item.dimName }}</span>
            <div>
              <yn-select-tree
                v-if="item.dimCode === 'Category'"
                v-model="item.dimMemberId"
                forceRender
                class="dimSelectTree"
                searchMode="single"
                :nonleafselectable="true"
                :datasource="showTreeData[item.dimCode]"
                :allowClear="false"
                @change="onChange($event, item.dimCode)"
              />

              <asyn-select-dimMember
                v-else
                :ref="item.dimCode"
                v-model="item.dimMemberId"
                forceRender
                class="dimSelectTree"
                searchMode="custom"
                :dimCode="item.dimCode"
                :nonleafselectable="true"
                :allowClear="false"
                :isSetDefaultVal="true"
                :loadComplete="$event => loadComplete($event, item.dimCode)"
                @changeVal="dimMemberId => onChange(dimMemberId, item.dimCode)"
              />
              <span
                v-if="isVerification && !item.dimMemberId"
                class="errorTips"
              >
                {{ `请选择‘${item.dimName}’维度的成员` }}
              </span>
            </div>
          </template>
        </div>
      </template>
      <div v-else :class="['filterAreaEmptyCont', isMove ? 'moveing' : '']">
        <empty-dim-member type="listArea" />
      </div>
    </vuedraggable>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-row/";
import "yn-p1/libs/components/yn-col/";
import "yn-p1/libs/components/yn-select-tree";
import vuedraggable from "vuedraggable";
import EmptyDimMember from "./emptyDimMember.vue";
import reconciliationService from "@/services/reconciliation";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import cloneDeep from "lodash/cloneDeep";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
import { mapState } from "vuex";
export default {
  name: "FilterAreaMember",
  components: {
    vuedraggable,
    EmptyDimMember,
    AsynSelectDimMember
  },
  props: {
    dims: {
      type: Array,
      default() {
        return [];
      }
    },
    isVerification: {
      type: Boolean
    }
  },
  inject: ["changeLoadStatusPro", "isEmptyConfig"],
  data() {
    return {
      emptyData: [1],
      data: [],
      allTreeData: {},
      showTreeData: {},
      isMove: false,
      notMoveToListArea: false,
      loadNum: 0
    };
  },
  computed: {
    ...mapState("reconciliation", {
      reconciliationConfigObj: state => state.reconciliationConfigObj
    })
  },
  watch: {
    dims: {
      async handler(newVal) {
        this.loadNum = 0;
        if (newVal && newVal.length > 0) {
          this.data = cloneDeep(newVal);
          try {
            await this.getSelectTreeData();
          } catch (e) {}
          this.changeLoadStatusPro("filterMembers", true);
        } else {
          this.data = [];
          this.changeLoadStatusPro("filterMembers", true);
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    onStart(event) {
      const { oldIndex } = event;
      if (this.$parent.$refs.listAreaMember.data.length === 2) {
        this.notMoveToListArea = true;
      }
      this.$emit("dragStart", oldIndex, "filter");
    },
    onEnd(event) {
      const { newIndex, to } = event;
      if (this.notMoveToListArea) {
        this.notMoveToListArea = false;
        this.$parent.$refs.listAreaMember.isMove = false;
        return;
      }
      let targetType = "list";
      if (to.parentElement.getAttribute("class").indexOf("list") === -1) {
        if (!this.isMoveEmptyCont) {
          targetType = "filter";
        }
      }
      this.isMoveEmptyCont = false;
      this.$emit("dragEnd", newIndex, targetType);
      this.$parent.$refs.listAreaMember.isMove = false;
    },
    onMove(e) {
      this.$parent.$refs.listAreaMember.isMove = true;
      if (
        !this.$parent.$refs.listAreaMember.data.length &&
        e.from.className !== e.to.className
      ) {
        this.isMoveEmptyCont = true;
        return false;
      }
      if (this.$parent.$refs.listAreaMember.data.length === 2) {
        this.notMoveToListArea = true;
        return false;
      }
      return true;
    },
    unchoose() {
      if (this.notMoveToListArea) {
        UiUtils.warningMessage("列表区域不允许配置超过2个维度");
      }
    },
    async getSelectTreeData() {
      const promiseArr = [];
      const promiseIdents = [];
      const dimCodes = [];
      this.data.forEach((item, index) => {
        // 判断 是否请求过此数据
        const { dimCode } = item;
        const currTreeData = this.allTreeData[dimCode];
        if (currTreeData || dimCode !== "Category") {
          this.setDefaultValByIndex(index, currTreeData);
          return;
        }
        dimCodes.push(dimCode);
        promiseIdents.push(index);
        // 只对类别维度
        promiseArr.push(reconciliationService("cateGoryDimMembersTree"));
      });
      await Promise.all(promiseArr).then(res => {
        res.forEach((item, index) => {
          const { data } = item;
          const currDimCode = dimCodes[index];
          const treeData = this.convertData(data);
          this.setDefaultValByIndex(promiseIdents[index], treeData);
          this.$set(this.allTreeData, currDimCode, treeData);
        });
        this.showTreeData = cloneDeep(this.allTreeData);
        this.changeLoadStatusPro("filterMembers", true);
      });
    },
    // 如果 没有默认值，则将下拉树种第一个节点 作为选中节点
    setDefaultValByIndex(index, treeData = []) {
      const currDimInfo = this.data[index] || {};
      if (!currDimInfo.dimMemberId) {
        const firstNode = treeData[0] || {};
        this.data[index].dimMemberId = firstNode.id;
      }
    },
    convertData(data) {
      const loop = data => {
        data.map(item => {
          const { name, id, children } = item;
          item.key = id;
          item.label = name;
          if (children && children.length) {
            loop(children);
          }
        });
      };
      loop(data);
      return data;
    },
    loadComplete() {
      this.loadNum++;
      if (this.loadNum === this.data.length) {
        setTimeout(() => {
          this.data.forEach(item => {
            if (!item.dimMemberId) {
              item.dimMemberId = this.$refs[item.dimCode][0].currValue;
            }
          });
        });
      }
    },
    onChange(dimMemberId, dimCode) {
      this.data.some((item, index) => {
        if (item.dimCode === dimCode) {
          item.dimMemberId = dimMemberId;
          this.$set(this.data, index, item);
          this.$emit("setDimInfo", index, item, "filterArea");
          return true;
        }
      });
    },
    getSaveParams() {
      return {
        filterMembers: this.data
      };
    },
    onCustomeSearch(event, dimCode) {
      const currTreeData = this.allTreeData[dimCode];
      let { searchValue } = event;
      searchValue = searchValue.trim();
      let searchRes = [];
      if (!searchValue) {
        searchRes = currTreeData;
        this.$set(this.showTreeData, dimCode, searchRes);
        return;
      }
      const loop = data => {
        data &&
          data.forEach(item => {
            const { children, name } = item;
            if (name.indexOf(searchValue) !== -1) {
              searchRes.push(item);
              if (children && children.length > 0) {
                loop(children);
              }
            }
          });
      };
      loop(currTreeData);
      this.$set(this.showTreeData, dimCode, searchRes);
    },
    dropdownVisibleChange(isOpen, dimCode) {
      if (!isOpen) {
        this.$set(this.showTreeData, dimCode, this.allTreeData[dimCode]);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.filterAreaCont {
  & > span {
    font-size: @yn-font-size-base;
    margin-bottom: @yn-margin-l;
    display: inline-block;
    font-weight: 400;
  }
  .filterAreaEmptyCont {
    width: 100%;
    margin-bottom: @yn-margin-xxl;
  }
  .moveing {
    /deep/.emptyDimCont {
      border: 2px dashed @yn-primary-color;
    }
  }
  .filterArea {
    display: flex;
    width: 100%;
    flex-flow: row wrap;
    margin-bottom: @yn-margin-l;
    /deep/.emptyDimCont {
      width: 100%;
    }
    .dimSelectItem:hover {
      background-color: @yn-background-color-light;
      & > span {
        cursor: move;
        color: @yn-text-color-secondary;
      }
    }
    .dimSelectItem {
      & > div {
        position: relative;
      }
      & > span {
        margin-right: 20px;
        color: @yn-text-color-secondary;
      }
      & .dimSelectTree {
        width: 176px;
      }
      flex: 0 1 50%;
      justify-content: flex-end;
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      position: relative;
    }
  }
  .emptyCont {
    width: 100%;
  }
}
/deep/.ghost {
  width: 50% !important;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 24px;
  /deep/& > span {
    margin-right: 20px;
  }
  /deep/& > div {
    width: 200px;
  }
}
</style>
