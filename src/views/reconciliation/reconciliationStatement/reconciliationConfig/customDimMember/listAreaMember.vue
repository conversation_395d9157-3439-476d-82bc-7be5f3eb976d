<template>
  <div class="listAreaCont">
    <span>列表区域成员</span>
    <vuedraggable
      v-model="data"
      class="listArea"
      group="site"
      ghostClass="ghost"
      handle=".dimSelectItem>span"
      :move="onMove"
      @start="onStart"
      @end="onEnd"
    >
      <template v-if="data.length > 0">
        <div v-for="item in data" :key="item.dimCode" class="dimSelectItem">
          <template>
            <span class="require">{{ item.dimName }}</span>
            <div class="dim-list-cont">
              <show-dim-list-input
                :dimInfo="item"
                :analyticExpName="
                  item.dimCode === 'Category'
                    ? 'parseCateGoryExp'
                    : 'getExpMemberWithCode'
                "
                @change="setData"
              />
              <span
                v-if="checkStatus[item.dimCode] && !item.selectedItem.length"
                class="errorTips"
              >
                {{ `请选择‘${item.dimName}’维度的成员` }}
              </span>
            </div>
          </template>
        </div>
      </template>
      <div v-else :class="['listAreaEmptyCont', isMove ? 'moveing' : '']">
        <empty-dim-member type="listArea" />
      </div>
    </vuedraggable>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-input/";
import vuedraggable from "vuedraggable";
import EmptyDimMember from "./emptyDimMember.vue";
import ShowDimListInput from "@/components/hoc/ShowDimListInput.vue";
import cloneDeep from "lodash/cloneDeep";
export default {
  name: "ListAreaMember",
  components: {
    vuedraggable,
    EmptyDimMember,
    ShowDimListInput
  },
  props: {
    dims: {
      type: Array,
      default() {
        return [];
      }
    },
    isVerification: {
      type: Boolean
    }
  },
  data() {
    return {
      visible: false,
      data: [],
      dimInfo: {
        dimId: "",
        dimName: ""
      },
      isMove: false,
      checkStatus: {} // 校验状态
    };
  },
  watch: {
    dims: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.data = cloneDeep(newVal);
        } else {
          this.data = [];
        }
        this.setDefaultCheckStatus(newVal);
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    setDefaultCheckStatus(dimArr) {
      const res = {};
      dimArr.forEach(dimInfo => {
        const { dimCode } = dimInfo;
        const currCheckStatus = this.checkStatus[dimCode];
        if (typeof currCheckStatus === "boolean") {
          res[dimCode] = currCheckStatus;
        } else {
          res[dimCode] = false;
        }
      });
      this.$set(this, "checkStatus", res);
    },
    onStart(event) {
      const { oldIndex } = event;
      this.$emit("dragStart", oldIndex, "list");
    },
    onEnd(event) {
      const { newIndex, to } = event;
      let targetType = "filter";
      if (to.parentElement.getAttribute("class").indexOf("filter") === -1) {
        if (!this.isMoveEmptyCont) {
          targetType = "list";
        }
      }
      this.$emit("dragEnd", newIndex, targetType);
      this.$parent.$refs.filterAreaMember.isMove = false;
    },
    onMove(e) {
      this.$parent.$refs.filterAreaMember.isMove = true;
      if (
        !this.$parent.$refs.filterAreaMember.data.length &&
        e.from.className !== e.to.className
      ) {
        this.isMoveEmptyCont = true;
        return false;
      }
      return true;
    },
    setData(data) {
      const { dimCode, selectedItem } = data;
      const { dimInfo: currDimInfo, index } = this.findItem(dimCode);
      currDimInfo.selectedItem = selectedItem;
      this.$set(this.data, index, currDimInfo);
      this.$emit("setDimInfo", index, currDimInfo, "listArea");
    },
    findItem(code) {
      const res = {
        index: 0,
        dimInfo: {}
      };
      this.data.some((item, index) => {
        const { dimCode } = item;
        if (dimCode === code) {
          res.dimInfo = item;
          res.index = index;
          return true;
        }
        return false;
      });
      return res;
    },
    getSaveParams() {
      Object.keys(this.checkStatus).forEach(item => {
        this.checkStatus[item] = true;
      });
      this.$set(this, "checkStatus", this.checkStatus);
      return {
        listMembers: this.data
      };
    }
  }
};
</script>
<style lang="less" scoped>
.listAreaCont {
  & > span {
    font-size: 14px;
    margin-bottom: 12px;
    display: inline-block;
    font-weight: 400;
  }
  position: relative;
  .listAreaEmptyCont {
    margin-bottom: @yn-margin-xxxl;
    width: 100%;
    /deep/.emptyDimCont {
      margin: 0;
      width: 100%;
    }
    // z-index: 0;
  }
  .moveing {
    /deep/.emptyDimCont {
      border: 2px dashed @yn-primary-color;
    }
  }
}
.listArea {
  display: flex;
  width: 100%;
  flex-flow: row wrap;
  position: relative;
  z-index: 1;
  margin-bottom: @yn-margin-l;
  .dimSelectItem:hover {
    background-color: @yn-background-color-light;
    & > span {
      cursor: move;
      color: @yn-text-color-secondary;
    }
  }
  .dimSelectItem {
    & > span {
      margin-right: 16px;
      color: @yn-text-color-secondary;
    }
    & > .dimSelectTree {
      width: 176px;
    }
    flex: 0 1 50%;
    justify-content: flex-end;
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    position: relative;
  }
}
.emptyCont {
  width: 100%;
  height: 18px;
  position: relative;
  z-index: 1;
}
/deep/.ghost {
  width: 50% !important;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 24px;
  /deep/& > span {
    margin-right: 16px;
  }
  /deep/& > div {
    width: 200px;
  }
}
.require::before {
  display: inline-block;
  margin-right: 4px;
  color: @yn-error-color;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: "*";
}
.dim-list-cont {
  position: relative;
}
</style>
