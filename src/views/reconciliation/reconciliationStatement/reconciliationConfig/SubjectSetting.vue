<template>
  <div class="subjectSetting">
    <span class="title">
      <yn-divider type="vertical" class="sign" />对账科目设置
    </span>
    <yn-radio-group
      v-if="isEmptyTable"
      v-model="radioVal"
      class="radioCont"
      :options="options"
      @change="changeRadio"
    />
    <yn-popconfirm
      v-else
      title="切换对账科目，已选科目数据会被清空"
      okText="切换"
      cancelText="取消"
      @confirm="confirm"
      @cancel="cancel"
    >
      <yn-radio-group v-model="radioVal" class="radioCont" :options="options" />
    </yn-popconfirm>

    <yn-table
      :data-source="tableData"
      :columns="columns"
      bordered
      :scroll="{ x: 582 }"
    >
      <template slot="footer">
        <div class="tableFooter">
          <span :class="getAddRowBtnClass" @click="addRow">
            <span>+</span>添加
          </span>
        </div>
      </template>
      <span slot="accountTitle" slot-scope>
        <span class="requireCell">{{
          !radioVal ? "我方科目" : "插式科目"
        }}</span>
      </span>
      <span slot="otherAccountTitle" class="requireCell"> 对方科目 </span>
      <span slot="operation" slot-scope="text, record, index" class="fullCell">
        <a
          :class="[tableData.length === 1 ? 'disabledBtn' : '']"
          @click="deleteRow(index)"
        >
          删除
        </a>
      </span>
      <span slot="ident" slot-scope="text, record, index" class="fullCell">{{
        index + 1
      }}</span>
      <div
        slot="account"
        slot-scope="text, record, index"
        class="seletTreeCont valid-prop "
      >
        <yn-select-tree
          v-model="record.account"
          forceRender
          size="large"
          class="dimSelectTree"
          :class="[
            !record.account && checkRequired ? 'valid-error-border' : ''
          ]"
          searchMode="multiple"
          placeholder="请选择"
          :nonleafselectable="true"
          :datasource="treeData"
          :allowClear="false"
          @change="onChange($event, 'account', index)"
        />
        <!-- errorTips -->
        <span
          v-if="!record.account && checkRequired"
          class="invalid-tip-text"
        >{{ radioVal ? "请选择插式科目" : "请选择资产/费用科目" }}</span>
      </div>
      <div
        slot="otherAccount"
        slot-scope="text, record, index"
        class="seletTreeCont valid-prop "
      >
        <yn-select-tree
          v-model="record.otherAccount"
          forceRender
          size="large"
          class="dimSelectTree"
          :class="[
            !record.otherAccount && checkRequired ? 'valid-error-border' : ''
          ]"
          searchMode="multiple"
          placeholder="请选择"
          :nonleafselectable="true"
          :datasource="treeOtherData"
          :allowClear="false"
          @change="onChange($event, 'otherAccount', index)"
        />
        <span
          v-if="!record.otherAccount && checkRequired"
          class="invalid-tip-text"
        >{{ radioVal ? "请选择插式科目" : "请选择负债/收入科目" }}</span>
      </div>
    </yn-table>
    <span class="title">
      <yn-divider type="vertical" class="sign" />对账差异
    </span>
    <div class="reconciliation-diff">
      <yn-form
        autocomplete="off"
        :form="form"
        layout="horizontal"
        v-bind="{
          labelCol: { span: 6 },
          wrapperCol: { span: 18 }
        }"
      >
        <yn-form-item label="差异调整模板">
          <yn-select-tree
            ref="selector"
            v-decorator="['journalTemplateId']"
            :datasource="journalTemplateData"
            searchMode="multiple"
            multiple
            forceRender
            nonleafselectable
            @change="handleChangeTemplate"
          />
        </yn-form-item>
        <yn-form-item v-if="hasAuth" label="对账协同状态">
          <yn-select
            v-decorator="[
              'status',
              {
                rules: [{ required: true, message: '请选择' }]
              }
            ]"
            :options="collaborativeStatus"
            mode="multiple"
            showSearch
            :filterOption="filterOption"
          />
        </yn-form-item>
        <div v-if="hasAuth" class="value-wrap">
          <yn-form-item
            class="value-type"
            :colon="false"
            :labelCol="{ span: 12 }"
            :wrapperCol="{ span: 12 }"
          >
            <span slot="label">
              协同容差
              <yn-tooltip
                title="对账双方确认金额后，根据容差 判断是否已对平"
                placement="topLeft"
                arrowPointAtCenter
              >
                <yn-icon-svg type="hint" />
              </yn-tooltip>
            </span>
            <yn-select
              v-decorator="[
                'valueType',
                {
                  rules: [
                    {
                      required: true,
                      message: '请选择'
                    }
                  ]
                }
              ]"
              class="value-type-select"
              :allowClear="false"
              :options="valueTypes"
              @change="handlerValueType"
            />
          </yn-form-item>
          <yn-form-item
            label=""
            :colon="false"
            :class="['value']"
            :wrapperCol="{ span: 24 }"
          >
            <yn-input
              v-decorator="[
                'value',
                {
                  rules: [
                    {
                      validator: validateNumber
                    }
                  ]
                }
              ]"
              class="value-type-input"
              autocomplete="off"
              :allowClear="false"
              @focus="handlerFocus"
              @blur="handlerBlur"
            >
              <span
                v-if="valueType === VALUES_TYPES_ENUM.percent"
                slot="suffix"
              >
                %
              </span>
            </yn-input>
          </yn-form-item>
        </div>
      </yn-form>
    </div>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-radio/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-icon/";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import reconciliationService from "@/services/reconciliation";
import journalService from "@/services/journal";
import { setDataKey2key } from "@/utils/common";
import { toFinance } from "@/utils/filters";
import {
  VALUES_TYPES_OPTIONS,
  VALUES_TYPES_ENUM
} from "@/views/voucherReconciliation/reconciliationSetting/ruleSetting/constants.js";
const PLUG_IN_ACCOUNT = 1; // 插式账户
const SPECIFY_ACCOUNT = 0; // 指定科目
export default {
  name: "SubjectSetting",
  props: {
    data: {
      type: Array,
      default() {
        return [];
      }
    },
    coordinationStatus: {
      type: Array,
      default() {
        return [];
      }
    },
    coordinationToleranceType: {
      type: Number,
      default() {
        return 0;
      }
    },
    coordinationToleranceValue: {
      type: [Number, String],
      default() {
        return "";
      }
    },
    subject: {
      type: Number,
      default() {
        return 1;
      }
    },
    isVerification: {
      type: Boolean
    },
    journalTemplate: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableData: [],
      VALUES_TYPES_ENUM,
      valueTypes: VALUES_TYPES_OPTIONS,
      radioVal: 1,
      options: [
        {
          label: "插式账户",
          value: PLUG_IN_ACCOUNT
        },
        {
          label: "指定科目",
          value: SPECIFY_ACCOUNT
        }
      ],
      columns: [],
      treeData: [],
      showTreeData: [],
      treeOtherData: [],
      showOtherTreeData: [],
      checkRequired: false,
      isDisabledBtn: true,
      journalTemplateData: [],
      collaborativeStatus: [],
      valueType: "",
      value: "",
      showValue: "",
      hasAuth: false,
      inputIng: false,
      selectTreeNodes: [] // 差异对账选中节点信息
    };
  },
  computed: {
    getAddRowBtnClass() {
      const classNames = ["addBtn"];
      if (this.isDisabledBtn) {
        classNames.push("disabledBtn");
      }
      return classNames.join(" ");
    },
    isEmptyTable() {
      if (this.tableData.length === 1) {
        const { account, otherAccount } = this.tableData[0];
        return !(account || otherAccount);
      }
      return false;
    },
    templateId() {
      return this.selectTreeNodes.map(item => item.templateId);
    }
  },
  watch: {
    subject: {
      handler(newVal) {
        const radioVal = typeof newVal !== "number" ? 1 : newVal;
        this.radioVal = radioVal;
        this.columns = [...this.getColums(radioVal)];
        this.setEmptyDataByType(newVal);
      },
      immediate: true
    },
    data: {
      handler(newVal) {
        if (newVal.length) {
          this.tableData = newVal.map((item, index) => {
            return { key: String(index), ...item };
          });
          this.isDisabledBtn = false;
        } else {
          this.setEmptyDataByType(PLUG_IN_ACCOUNT);
        }
      },
      immediate: true
    },
    isVerification: {
      handler(newVal) {
        this.checkRequired = newVal;
      }
    },
    journalTemplate: {
      handler(newVal) {
        if (newVal.length > 0) {
          this.selectTreeNodes = newVal;
        }
      },
      immediate: true
    }
  },
  beforeCreate() {
    this.form = this.$form.createForm(this, {
      name: "reconciliationVariance"
    });
  },
  async mounted() {
    await this.getPermission();
    await this.getTreeData();
    await this.getJournalTemplate();
    await this.getCollaborativeStatus();
    // TODO 初始化
    this.form.setFieldsValue({
      journalTemplateId: this.templateId,
      status: (this.coordinationStatus || []).map(item => item.id),
      value: this.coordinationToleranceValue,
      valueType: this.coordinationToleranceType || 0
    });
    this.value = this.coordinationToleranceValue;
    const len = this.getDecLen(this.value);
    const value = toFinance(this.value, len);
    this.showValue = value;
    this.valueType = this.coordinationToleranceType || 0;
    this.$emit("changeLoadStatus", "subjectSetting", true);
  },
  methods: {
    async getPermission() {
      const {
        data: { data }
      } = await reconciliationService("hasReconciliationCoordinationAuth");
      this.hasAuth = data;
    },
    cancel() {},
    confirm() {
      this.radioVal = Number(!this.radioVal);
      this.columns = this.getColums(this.radioVal);
      this.isDisabledBtn = true;
      this.getTreeData();
      this.setEmptyDataByType(this.radioVal);
    },
    changeRadio(e) {
      this.radioVal = Number(e.target.value);
      this.columns = this.getColums(this.radioVal);
      this.isDisabledBtn = true;
      this.getTreeData();
      this.setEmptyDataByType(this.radioVal);
    },
    handlerValueType(value) {
      this.valueType = value;
      this.handlerReset();
    },
    handlerReset(item) {
      this.value = "";
      this.showValue = "";
      this.$nextTick(() => {
        this.form.resetFields(["value"]);
      });
      setTimeout(() => {
        this.form.validateFields(["value"]);
      }, 0);
    },
    handlerFocus() {
      this.inputIng = true;
      this.showValue = this.value;
      this.form.setFieldsValue({
        value: this.value
      });
    },
    handlerBlur(e) {
      this.inputIng = false;
      const status = this.validateValue(e.target.value);
      this.value = e.target.value.replace(/\.$/, "");
      if (this.value && status) {
        const len = this.getDecLen(e.target.value);
        const value = toFinance(this.value, len);
        this.showValue = value;
      } else {
        this.showValue = this.value;
      }
      this.form.setFieldsValue({
        value: this.showValue
      });
      setTimeout(() => {
        this.form.validateFields(["value"]);
      }, 0);
    },
    getDecLen(value) {
      return value
        ? value.match(/\.(\d+)/)
          ? Math.min(value.match(/\.(\d+)/)[1].length, 9)
          : 2
        : 2;
    },
    validateNumber(rule, value, callback, item) {
      const relValue = this.inputIng ? value : this.value;
      if (relValue && !this.validateValue(relValue)) {
        callback("输入格式有误");
        return;
      }
      callback();
    },
    validateValue(value) {
      const isAsyncPercent = this.valueType === VALUES_TYPES_ENUM.percent;
      const reg = !isAsyncPercent
        ? /^-?([1-9]\d*|\d+\.\d{0,9}|0)$/
        : /^([1-9]\d*|^\d+\.\d{0,9}|0)$/;
      // 容差数值或百分比，空值
      if (!value) return true;
      // 数值类型，固定差或容差百分比都大于100
      if (reg.test(value) && isAsyncPercent && parseFloat(value) > 100) {
        return false;
      }
      // 非数值，容差数值
      if (!reg.test(value) && !isAsyncPercent) {
        return false;
      }

      return true;
    },
    setEmptyDataByType(type) {
      const emptyTableData = {
        account: "",
        key: 1
      };
      if (type === SPECIFY_ACCOUNT) {
        // 指定科目
        emptyTableData.otherAccount = "";
      }
      this.$set(this, "tableData", [emptyTableData]);
    },
    async getTreeData() {
      const requestMethodName = this.radioVal ? "getRA" : "getAccount";
      reconciliationService(requestMethodName).then(res => {
        this.setTreeData(res.data);
      });
    },
    async getJournalTemplate() {
      await journalService("queryAllTemplate").then(res => {
        const data = setDataKey2key(
          res.data,
          ["templateId", "name", "subNodes"],
          ["key", "label", "children"]
        );
        const tempArr = [...data];
        while (tempArr.length) {
          const tempObj = tempArr.shift();
          const { children, isLeaf } = tempObj;
          tempObj.disabled = !isLeaf;
          if (children) {
            if (children.length) {
              Array.prototype.unshift.apply(tempArr, children);
            }
            if (isLeaf) {
              delete tempObj.children;
            }
          }
        }
        this.journalTemplateData = data;
      });
    },
    async getCollaborativeStatus() {
      const {
        data: { data }
      } = await reconciliationService("getReconciliationCoordinationSetting");
      this.collaborativeStatus =
        data && data.length > 0
          ? data.map(item => ({
            label: item.name,
            value: item.id
          }))
          : [];
    },
    setTreeData(data) {
      if (!this.radioVal) {
        const { data: newData } = this.recursion(data.account);
        const { data: newOtherData } = this.recursion(data.otherAccount);
        this.$set(this, "treeData", newData);
        this.$set(this, "treeOtherData", newOtherData);
        return;
      }
      const { data: newData } = this.recursion(data);
      this.$set(this, "treeData", newData);
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    recursion(data) {
      const loop = data => {
        data.map(item => {
          const { children, name, id } = item;
          item.label = name;
          item.key = id;
          if (children && children.length > 0) {
            loop(children);
          }
        });
      };
      loop(data);
      return {
        data
      };
    },
    getColums(type) {
      const columns = [
        {
          dataIndex: "ident",
          title: "序号",
          key: "ident",
          width: 80,
          scopedSlots: {
            customRender: "ident"
          }
        },
        {
          dataIndex: "account",
          key: "account",
          scopedSlots: {
            customRender: "account",
            title: "accountTitle"
          }
        },
        {
          dataIndex: "otherAccount",
          key: "otherAccount",
          scopedSlots: {
            customRender: "otherAccount",
            title: "otherAccountTitle"
          }
        },
        {
          title: "操作",
          dataIndex: "operation",
          key: "operation",
          width: 80,
          scopedSlots: {
            customRender: "operation"
          }
        }
      ];
      if (type === SPECIFY_ACCOUNT) {
        // 指定科目
        return columns;
      } else {
        return columns.slice(0, 2).concat(columns.slice(3));
      }
    },
    deleteRow(rowIndex) {
      if (this.tableData.length > 1) {
        this.tableData = [
          ...this.tableData
            .slice(0, rowIndex)
            .concat(this.tableData.slice(rowIndex + 1))
        ];
        this.isDisabledBtn = !this.checkLastRowNoEmptyCell();
      }
    },
    addRow() {
      const noEmptyCell = this.checkLastRowNoEmptyCell();
      if (noEmptyCell) {
        const rowData = {
          account: ""
        };
        if (this.radioVal === SPECIFY_ACCOUNT) {
          rowData["otherAccount"] = "";
        }
        this.tableData.push({ key: AppUtils.generateUniqueId(), ...rowData });
        this.isDisabledBtn = true;
      }
    },
    onChange(dimMemberId, propName, rowIndex) {
      const currRow = this.tableData.slice(rowIndex, rowIndex + 1)[0];
      currRow[propName] = dimMemberId;
      this.tableData = [...this.tableData];
      const noEmptyCell = this.checkLastRowNoEmptyCell();
      this.isDisabledBtn = !noEmptyCell;
      if (noEmptyCell) {
        this.checkRequired = false;
      }
    },
    checkLastRowNoEmptyCell() {
      // 没有数据
      if (!this.tableData.length) return true;
      const lastRow = this.tableData.slice(-1)[0];
      if (this.radioVal === SPECIFY_ACCOUNT) {
        // 指定科目
        return Object.values(lastRow).every(item => !!item);
      } else {
        return !!lastRow.account;
      }
    },
    // 供父组件获取 当前组件数据
    getSaveParams() {
      let validate = true;
      let formValue = {};
      this.form.validateFields((err, value) => {
        formValue = value;
        if (err) {
          validate = false;
        }
      });
      const { radioVal, tableData, selectTreeNodes } = this;
      this.checkRequired = true;
      return {
        accountType: radioVal,
        accountData: tableData,
        journalTemplate: selectTreeNodes,
        coordinationStatus: (formValue.status || []).map(id => ({ id: id })),
        coordinationToleranceType: this.valueType,
        coordinationToleranceValue: this.value,
        validate
      };
    },
    handleChangeTemplate(keys, selectItems) {
      this.selectTreeNodes = selectItems.map(item => {
        const { label: templateName, key: templateId } = item;
        return {
          templateName,
          templateId
        };
      });
    }
  }
};
</script>
<style lang="less" scoped>
@import "../../../../commonLess/common.less";

.radioCont {
  padding-bottom: @yn-padding-l;
}
.requireCell::after {
  content: "*";
  color: @yn-error-color;
  font-size: 21px;
  height: 15px;
  float: right;
  margin-left: @yn-margin-xs;
}
.subjectSetting {
  width: 100%;
  .reconciliation-diff {
    width: 100%;
  }
  & > span,
  .title {
    font-size: @yn-font-size-base;
    font-weight: 500;
    display: block;
    margin-bottom: @yn-margin-xl;
  }
  .title {
    font-weight: 600;
    margin-top: 2rem;
  }
  .value-type-select {
    /deep/ .ant-select-selection {
      border-radius: 0.25rem 0 0 0.25rem;
    }
  }
  .value-type-input {
    border-radius: 0 0.25rem 0.25rem 0;
    border-left: none;
  }
  /deep/.ant-table-tbody > tr > td {
    padding: 0;
    overflow-wrap: break-word;
  }
  /deep/.tableFooter {
    height: @rem36;
    line-height: @rem36;
    background: @yn-background-color;
    border: 1px dashed @yn-border-color-base;
    color: @yn-primary-color;
    text-align: center;
    .addBtn {
      cursor: pointer;
    }
    .addBtn > span {
      margin-right: @yn-margin-s;
    }
  }
  /deep/.fullCell {
    display: inline-block;
    width: 100%;
    height: 100%;
    padding-left: 17px;
  }
}
.disabledBtn {
  cursor: not-allowed !important;
  color: @yn-disabled-color;
}
.disabledBtn:disabled {
  cursor: not-allowed;
  color: @yn-disabled-color;
}
.seletTreeCont {
  position: relative;
  height: 100%;
  .dimSelectTree {
    width: 100%;
    height: 36px;
    /deep/.yn-select-tree {
      height: 36px;
      line-height: 36px;
      border: 0;
    }
  }
  .valid-error-border {
    /deep/.yn-select-tree {
      height: 34px;
      line-height: 34px;
    }
  }
}
.value-wrap {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  .value-type {
    flex-grow: 1;
    flex-basis: 200px;
    flex-wrap: nowrap;
  }
  .value {
    flex-wrap: nowrap;
    flex-basis: 200px;
    flex-grow: 1;
  }
}
.sign {
  width: 2.5px;
  background: @yn-primary-color;
  margin-left: 0;
  margin-right: 0.375rem;
  border-radius: 0.625rem;
}
</style>
