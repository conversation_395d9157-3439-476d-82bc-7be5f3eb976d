/**
 * @description 转换获取对账报表请求参数
 * @param{Object} queryRangeData 查询数据范围表单数据
 * @param{Object} displaySettingsData 报表显示设置
 * @returns{Object} 转换后的数据
 * */
export const formatReconciliationParams = (
  displaySettingsData,
  queryRangeData,
  selectTreeNode
) => {
  const {
    subject: accountShowType,
    matches: matchType,
    decimalPlace,
    valueType,
    tolerance
  } = displaySettingsData;
  const {
    Version: version,
    Year: year,
    ReportingCurrency: reportingCurrency,
    Period: period,
    entity,
    icp,
    mergeGroup: scope,
    reconciliationType
  } = queryRangeData;
  const { name: reconciliationName, id: reconciliationId } = selectTreeNode;
  const params = {
    reconciliationName,
    reconciliationId,
    version,
    year,
    reportingCurrency,
    period,
    scope,
    reconciliationType,
    accountShowType,
    matchType,
    decimalPlace,
    icp: icp && icp.selectedItem && getDimNamesByDims(icp.selectedItem),
    entity:
      entity && entity.selectedItem && getDimNamesByDims(entity.selectedItem)
  };
  // 对账类型
  if (reconciliationType) {
    // 子集团对账
    delete params.icp;
    delete params.entity;
  } else {
    delete params.scope;
  }
  // 0数值 1 百分比
  if (typeof valueType === "number" && !valueType) {
    params.toleranceNumber = tolerance;
  } else {
    params.tolerancePercentage = tolerance;
  }
  return params;
};
const getDimNamesByDims = dims => {
  return dims && dims.map(item => item.dimMemberName);
};
