<template>
  <yn-spin :spinning="spinning">
    <Empty v-show="!authSet['read']" />
    <div v-show="authSet['read']" class="reconciliationStatement">
      <yn-page-title :title="moduleName" />
      <div class="content cs-body-headerComponent">
        <yn-lc-layout iconPosition="center">
          <template slot="left">
            <slot name="reconciliationLeft">
              <reconciliation-left
                :hasAuthority="hasAuthority"
                :params="params"
              />
            </slot>
          </template>
          <template slot="center">
            <slot name="reconciliationRight">
              <reconciliation-right :hasAuthority="hasAuthority" />
            </slot>
          </template>
        </yn-lc-layout>
      </div>
      <slot
        name="configContent"
        v-bind="{ drawerVisible, operationType, closeDrawer }"
      >
        <reconcoilation-config
          v-if="drawerVisible"
          :drawerVisible="drawerVisible"
          :operationType="operationType"
          @closeDrawer="closeDrawer"
        />
      </slot>
    </div>
  </yn-spin>
</template>
<script>
import "yn-p1/libs/components/yn-lc-layout/";
import "yn-p1/libs/components/yn-page-title/";
import ReconciliationLeft from "@/views/reconciliation/reconciliationStatement/layoutLeft";
import reconciliationRight from "@/views/reconciliation/reconciliationStatement/layoutRight";
import ReconcoilationConfig from "./reconciliationConfig";
import "yn-p1/libs/components/yn-breadcrumb/";
import defaultParams from "@/mixin/defaultParams";
import reconciliationCommon from "@/views/reconciliation/minix";
import { mapState } from "vuex";
import { VOUCHER_RECONCILIATION } from "@/constant/reconciliation.js";
import Empty from "./empty.vue";
export default {
  name: "ReconciliationStatement",
  components: {
    ReconciliationLeft,
    reconciliationRight,
    ReconcoilationConfig,
    Empty
  },
  mixins: [defaultParams, reconciliationCommon],
  props: {
    params: {
      type: Object,
      require: false,
      default: () => {}
    },
    reconciliationModuleName: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      drawerVisible: false,
      operationType: "add" // 操作类型 add or edit
    };
  },
  provide() {
    return {
      openDrawerPro: this.openDrawer,
      closeDrawerPro: this.closeDrawer,
      showSaveAsModal: "", // 防止报错，此方法，在对账报告页面进行注入，在对账配置页面引用
      reconciliationModuleName: this.reconciliationModuleName,
      namespace: this.namespace
    };
  },
  computed: {
    ...mapState({
      hasAuthority(state) {
        // const namespace = this.getNamespace();
        return state[this.namespace].hasAuthority;
      },
      authSet(state) {
        // const namespace = this.getNamespace(); // 这种实现方式太蠢了...哪怕把它实现成一个计算属性呢？
        return state[this.namespace].authSet;
      },
      spinning(state) {
        // const namespace = this.getNamespace();
        return state[this.namespace].mainLoading;
      }
    }),
    moduleName() {
      return this.reconciliationModuleName === VOUCHER_RECONCILIATION
        ? "凭证对账报告"
        : "余额对账报告";
    },
    namespace() {
      return this.getNamespace();
    }
  },
  async created() {
    this.$store.dispatch(`${this.namespace}/getPublicAuth`);
    this.$store.dispatch(`${this.namespace}/getAuthSet`);
  },
  methods: {
    openDrawer(type) {
      this.drawerVisible = true;
      this.operationType = type;
    },
    closeDrawer() {
      this.drawerVisible = false;
    }
  }
};
</script>
<style lang="less" scoped>
.reconciliationStatement {
  width: 100%;
  height: 100%;

  border-radius: 4px;
  .content {
    height: calc(100% - 2.75rem);
    background: @yn-body-background;
    border-radius: @yn-console-content-radius @yn-console-content-radius 0 0;
  }
}
</style>
