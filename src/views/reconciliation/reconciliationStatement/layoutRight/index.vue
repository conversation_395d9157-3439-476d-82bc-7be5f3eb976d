<template>
  <yn-spin :spinning="spinning" class="reconciliationParamsCont">
    <reconciliation-query
      v-if="!isShowEmpty"
      :title="title"
      :creator="creator"
      :updateTime="updateTime"
      @closeLoading="closeLoading"
      @generateReport="generateReport"
      @openDrawer="openDrawerPro"
    >
      <template #quertContent="obj">
        <slot name="quertContent" v-bind="obj"></slot>
      </template>
    </reconciliation-query>
    <empty
      v-else
      :hasAuthority="hasAuthority"
      :hasConfig="hasConfig"
      :isSelectLeaf="isSelectLeaf"
    />
  </yn-spin>
</template>
<script>
import "yn-p1/libs/components/yn-breadcrumb/";
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-button/";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import { mapState } from "vuex";
import apiService from "@/views/reconciliation/minix/service";
import ReconciliationQuery from "../reconciliationQuery";
import cloneDeep from "lodash/cloneDeep";
import reconciliationCommon from "@/views/reconciliation/minix";
import { VOUCHER_RECONCILIATION } from "@/constant/reconciliation.js";
import Empty from "./Empty.vue";
const TAB_URI = "/reconciliation/report";
export default {
  name: "ReconciliationRight",
  components: {
    ReconciliationQuery,
    Empty
  },
  mixins: [reconciliationCommon, apiService],
  data() {
    return {
      title: "",
      creator: "", // 创建人
      updateTime: "", // 最新修改时间
      spinning: true,
      isSelectLeaf: false,
      isShowEmpty: true,
      hasConfig: false, // 是有有对账配置信息
      reportQueryParams: {}, // 对账的查询参数
      loadStateObj: {
        queryRange: false,
        displaySetting: false
      },
      cacheTreeNodeId: "",
      generateReportBtnLoading: false
    };
  },
  inject: ["openDrawerPro", "reconciliationModuleName"],
  computed: {
    ...mapState({
      selectTreeNode(state) {
        const namespace = this.getNamespace();
        return state[namespace].selectTreeNode;
      },
      hasLeaf(state) {
        const namespace = this.getNamespace();
        return state[namespace].hasLeaf;
      },
      configObj(state) {
        const namespace = this.getNamespace();
        return state[namespace].reconciliationConfigObj;
      },
      hasAuthority(state) {
        const namespace = this.getNamespace();
        return state[namespace].hasAuthority;
      }
    })
  },
  watch: {
    selectTreeNode: {
      async handler(newVal) {
        const { isRoot } = newVal;
        const isSelNode = !!Object.keys(newVal).length;
        this.spinning = isSelNode;
        // 没有选中树节点
        if (!isSelNode) {
          this.setDefaultData();
          this.cacheTreeNodeId = "";
          return;
        }
        this.cacheTreeNodeId = newVal.id;
        if (typeof isRoot === "boolean" && !isRoot) {
          try {
            await this.requestConfigData();
          } catch (e) {
            this.spinning = false;
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    closeLoading() {
      this.spinning = false;
    },
    setDefaultData() {
      this.isShowEmpty = true;
      this.spinning = false;
      this.isSelectLeaf = false;
      this.hasConfig = false;
    },
    async requestConfigData() {
      const { name, id } = this.selectTreeNode;
      await this.apiService("getReconciliationConfigById", id).then(res => {
        let reconciliationConfigObj;
        if (this.reconciliationModuleName === VOUCHER_RECONCILIATION) {
          reconciliationConfigObj = this.configV(res, name);
        } else {
          reconciliationConfigObj = this.configR(res, name);
        }
        this.spinning = false;
        this.$store.commit(this.getMutationOrActionType("setConfig"), {
          id,
          config: reconciliationConfigObj
        });
      });
    },
    configV(res, name) {
      if (typeof res.data === "string") {
        this.isShowEmpty = true;
        this.isSelectLeaf = true;
        this.hasConfig = false;
        return {};
      } else {
        const {
          data: { data }
        } = res;
        const hasConfig = !!data.ruleId;
        this.title = name;
        this.creator = data.updateBy;
        this.updateTime = data.updateDate;
        this.hasConfig = hasConfig;
        this.isSelectLeaf = true;
        this.isShowEmpty = !hasConfig;
        return res.data.data;
      }
    },
    configR(res, name) {
      if (typeof res.data === "string") {
        this.isShowEmpty = true;
        this.isSelectLeaf = true;
        this.hasConfig = false;
        return {};
      } else {
        const hasConfig = !this.isEmptyConfig(res.data);
        this.title = name;
        this.creator = res.data.creator;
        this.updateTime = res.data.updateTime;
        this.hasConfig = hasConfig;
        this.isSelectLeaf = true;
        this.isShowEmpty = !hasConfig;
        return res.data;
      }
    },
    isEmptyConfig(reconciliationConfigObj) {
      return !Object.values(reconciliationConfigObj).some(item => {
        if (Array.isArray(item)) {
          return item.length;
        } else {
          return !!item;
        }
      });
    },
    async generateReport(obj) {
      // 打开 页签
      const { id, name } = this.selectTreeNode;
      const tabId = id + AppUtils.generateUniqueId();
      this.newtabMixin({
        id: tabId,
        title: name,
        router: "reconciliationReport",
        uri: TAB_URI,
        params: cloneDeep(obj),
        newTabParams: {
          tabId,
          title: name,
          reconciliationConfigObj: this.configObj
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.reconciliationParamsCont {
  height: 100%;
  /deep/.ant-spin-container {
    height: 100%;
  }
}
.generateReport {
  margin-right: @yn-margin-xl;
}
.emptyCont {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
  .emptyTips {
    color: @yn-label-color;
  }
}
/deep/.ant-form-item-label > label::after {
  content: "";
}
</style>
