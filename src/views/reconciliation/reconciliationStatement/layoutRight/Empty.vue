<template>
  <div class="emptyCont">
    <yn-empty>
      <span slot="description" class="emptyTips">
        {{ promptText }}
      </span>
      <yn-button
        v-if="!isSelectLeaf && authSet['add'] && !hasLeaf"
        type="primary"
        @click="addReport"
      >
        {{ "新增报表" }}
      </yn-button>
      <yn-button
        v-if="isSelectLeaf && authSet['edit-config']"
        type="primary"
        @click="addConfig"
      >
        {{ "对账配置" }}
      </yn-button>
    </yn-empty>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-breadcrumb/";
import "yn-p1/libs/components/yn-empty/";
import reconciliationCommon from "@/views/reconciliation/minix";
import { EventBus } from "yn-p1/libs/utils/ComponentUtils";
import { mapState } from "vuex";
export default {
  name: "Empty",
  mixins: [reconciliationCommon],
  props: {
    hasAuthority: <PERSON><PERSON><PERSON>,
    hasConfig: <PERSON><PERSON>an,
    isSelectLeaf: <PERSON>olean
  },
  data() {
    return {};
  },
  inject: ["openDrawerPro", "namespace"],
  computed: {
    ...mapState({
      hasLeaf(state) {
        const namespace = this.getNamespace();
        return state[namespace].hasLeaf;
      },
      selectTreeNode(state) {
        const namespace = this.getNamespace();
        return state[namespace].selectTreeNode;
      },
      authSet(state) {
        return state[this.namespace].authSet;
      }
    }),
    promptText() {
      return this.getAuthorityTipMessage();
    }
    // isShowBtn() {
    //   if (!this.hasAuthority) {
    //     // 没有权限
    //     if (this.hasLeaf) {
    //       // 需要判断选中的是否是私有报表
    //       return (
    //         this.selectTreeNode.attr &&
    //         this.selectTreeNode.attr.reconciliationType === 0
    //       );
    //     } else {
    //       return true;
    //     }
    //   }
    //   // 有叶子节点，没有选中
    //   if (!this.isSelectLeaf && this.hasLeaf) return false;
    //   return true;
    // }
  },
  methods: {
    handleClick() {
      if (this.isSelectLeaf) {
        this.openDrawerPro("add");
      } else {
        EventBus.trigger("openReportFormModalByType", "add");
      }
    },
    // 新增报表
    addReport() {
      EventBus.trigger("openReportFormModalByType", "add");
    },
    // 对账配置
    addConfig() {
      this.openDrawerPro("add");
    },
    getAuthorityTipMessage() {
      let message = this.checkNode(); //  是否有叶子节点
      if (message) {
        return message;
      }
      message = this.checkSelectedNode(); // 是否选中叶子节点
      if (message) {
        return message;
      }
      message = this.checkConfig(); // 是否有配置信息
      if (message) {
        return message;
      }
    },
    getNoAuthorityTipMessage() {
      return "当前对账报表为空，请联系管理员新增对账报表！";
    },
    checkSelectedNode() {
      if (!this.isSelectLeaf) {
        return "请选择需要查看的对账报表";
      }
    },
    checkConfig() {
      if (!this.hasConfig) {
        const editConfigAuth = this.isSelectLeaf && this.authSet["edit-config"];
        // 新增权限
        const addAuth = !this.isSelectLeaf && this.authSet["add"];
        if (editConfigAuth || addAuth) {
          // 有权限
          return "当前报表对账配置为空，马上开始配置吧!";
        } else {
          return this.selectTreeNode.attr.reconciliationType === 1
            ? "当前报表对账配置为空，请联系管理员配置吧!"
            : "当前报表对账配置为空，马上开始配置吧!";
        }
      }
    },
    checkNode() {
      if (!this.hasLeaf) {
        return "当前对账报表为空，马上开始新增吧！";
      }
    }
  }
};
</script>
