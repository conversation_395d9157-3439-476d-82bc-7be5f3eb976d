import { VOUCHER_RECONCILIATION } from "@/constant/reconciliation.js";
export default {
  methods: {
    getNamespace() {
      const reconciliationModuleName =
        this.reconciliationModuleName ||
        this._provided.reconciliationModuleName;
      return reconciliationModuleName === VOUCHER_RECONCILIATION
        ? "voucherReconciliation"
        : "reconciliation";
    },
    getMutationOrActionType(name) {
      const namespace = this.getNamespace();
      return `${namespace}/${name}`;
    }
  }
};
