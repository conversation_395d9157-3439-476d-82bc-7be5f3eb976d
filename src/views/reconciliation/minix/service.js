import reconciliationService from "@/services/reconciliation";
import voucherReconciliationService from "@/services/voucherReconciliation";
import { VOUCHER_RECONCILIATION } from "@/constant/reconciliation.js";

export default {
  computed: {
    apiService() {
      return this.reconciliationModuleName === VOUCHER_RECONCILIATION
        ? voucherReconciliationService
        : reconciliationService;
    }
  }
};
