<template>
  <yn-modal
    class="version-modal"
    width="58.5rem"
    title="关于C1-V20"
    :visible="visible"
    :footer="null"
    @cancel="handleClose"
  >
    <div class="app-info">
      <yn-spin v-if="loading" />
      <div v-show="!loading" class="app-version">
        <div class="name">
          <div class="blue-icon"></div>
          <!-- 固定 -->
          {{ productVersion.productName }}
        </div>
        <div class="version-data">
          <yn-row>
            <yn-col :span="12">
              <div class="label">
                版本号：
              </div>
              {{ productVersion.productVersion }}
            </yn-col>
            <yn-col :span="12" />
          </yn-row>
        </div>
      </div>
      <div
        v-for="item in appVersionList"
        :key="item.appName"
        class="app-version"
      >
        <div class="name">
          <div class="blue-icon"></div>
          {{ item.appName }}
        </div>
        <div class="version-data">
          <yn-row v-show="item.version">
            <yn-col :span="12">
              <div class="label">
                后端版本号：
              </div>
              {{ item.version }}
            </yn-col>
            <yn-col :span="12">
              <div class="label">后端分支：</div>
              {{ item.branchName }}
            </yn-col>
            <yn-col :span="12">
              <div class="label">后端发布时间：</div>
              {{ item.buildDate }}
            </yn-col>
            <yn-col :span="12">
              <div class="label">依赖平台版本：</div>
              {{ item.ecsVersion }}
            </yn-col>
          </yn-row>
          <yn-row v-show="item.appWebVersion.buildDate">
            <yn-col :span="12">
              <div class="label">前端版本号：</div>
              {{ item.appWebVersion.version || "未输入" }}
            </yn-col>
            <yn-col :span="12">
              <div class="label">前端分支：</div>
              {{ item.appWebVersion.branchName || "未指定" }}
            </yn-col>

            <yn-col :span="12">
              <div class="label">前端发布时间：</div>
              {{
                moment(item.appWebVersion.buildDate).format(
                  "YYYY-MM-DD HH:mm:ss"
                )
              }}
            </yn-col>
          </yn-row>
        </div>
      </div>
      <div class="app-version">
        <div class="name">
          <div class="blue-icon"></div>
          多维库
        </div>
        <div class="version-data">
          <yn-row>
            <yn-col :span="12">
              <div class="label">
                版本号：
              </div>
              {{ productVersion.multiDimensionalDatabaseVersion }}
            </yn-col>
            <yn-col :span="12" />
          </yn-row>
        </div>
      </div>
      <div
        v-for="item in otherAppVersionList"
        :key="item.appName"
        class="app-version"
      >
        <div class="name">
          <div class="blue-icon"></div>
          {{ item.appName }}
        </div>
        <div class="version-data">
          <yn-row v-show="item.appWebVersion.buildDate">
            <yn-col :span="12">
              <div class="label">前端版本号：</div>
              {{ item.appWebVersion.version || "未输入" }}
            </yn-col>
            <yn-col :span="12">
              <div class="label">前端分支：</div>
              {{ item.appWebVersion.branchName || "未指定" }}
            </yn-col>

            <yn-col :span="12">
              <div class="label">前端发布时间：</div>
              {{
                moment(item.appWebVersion.buildDate).format(
                  "YYYY-MM-DD HH:mm:ss"
                )
              }}
            </yn-col>
          </yn-row>
        </div>
      </div>
    </div>

    <div class="footer">
      {{ `北京元年科技股份有限公司 ©${new Date().getFullYear()}` }}
    </div>
  </yn-modal>
</template>
<script>
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-row/";
import "yn-p1/libs/components/yn-col/";
import YnUiUtils from "yn-p1/libs/utils/UiUtils";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { MRKey } from "@/config/SETUP";
import api from "@/services/api";
import moment from "moment";

export default {
  name: "YnAbout",
  data() {
    return {
      visible: false,
      version: window.APPVER.ver,
      branch: window.APPVER.branch,
      hasDahsboard: true,
      appVersionList: [],
      otherAppVersionList: [],
      loading: false,
      defaultAppNames: ["MDD", "MR", "Dashboard"],
      productVersion: {}
    };
  },
  mounted() {
    document.onkeydown = event => {
      // eslint-disable-next-line no-caller
      const e = event || window.event || arguments.callee.caller.arguments[0];
      if (
        e &&
        e.ctrlKey &&
        e.shiftKey &&
        (e.keyCode === 87 || e.keyCode === 86)
      ) {
        e.preventDefault();
        this.openVersionModal();
      }
      if (e && e.keyCode === 27) {
        this.visible = false;
      }
    };
  },
  methods: {
    moment,
    openVersionModal() {
      this.visible = true;
      if (this.appVersionList.length === 0) {
        this.hasDahsboard = DsUtils.getLocalStorageItem("_dashboard", {
          storagePrefix: MRKey
        });
        this.loading = true;
        this.getServerVers();
        this.getOtherServerVers();
      }
    },
    getServerVers() {
      if (!YnUiUtils.isTouchScreen()) {
        Promise.all([
          DsUtils.get(api.getMddServerVer),
          DsUtils.get(api.getMrServerVer),
          DsUtils.get(api.getDashboardServerVer)
        ])
          .then(res => {
            res.forEach((item, index) => {
              this.setAppVersionList(
                this.defaultAppNames[index],
                item && item.data ? item.data : {}
              );
            });
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    getOtherServerVers() {
      const APPVERLIST_WEB = JSON.parse(
        sessionStorage.getItem("APPVERLIST_WEB") || ""
      );
      for (const key in APPVERLIST_WEB) {
        if (this.defaultAppNames.indexOf(key) === -1) {
          const appWebVersion = APPVERLIST_WEB[key];
          this.otherAppVersionList.push({
            key: "con",
            appName: appWebVersion.appName,
            appWebVersion
          });
        }
      }
    },
    handleClose() {
      this.visible = false;
      this.appVersionList = [];
      this.otherAppVersionList = [];
    },
    setAppVersionList(name, versionData) {
      const APPVERLIST_WEB = JSON.parse(
        sessionStorage.getItem("APPVERLIST_WEB") || ""
      );
      const appWebVersion =
        APPVERLIST_WEB && APPVERLIST_WEB[name] ? APPVERLIST_WEB[name] : {};

      const {
        branchName = "未指定",
        version = "未输入",
        buildDate = "",
        ecsVersion = "",
        multiDimensionalDatabaseVersion,
        productName,
        productVersion
      } = versionData;
      if (name === "MDD") {
        this.productVersion = {
          multiDimensionalDatabaseVersion,
          productName,
          productVersion
        };
      }
      const data = {
        key: name,
        appWebVersion,
        appName: name,
        branchName,
        version,
        buildDate,
        ecsVersion
      };
      this.appVersionList.push(data);
    }
  }
};
</script>
<style lang="less" scoped>
.version-modal {
  .app-info {
    min-height: 20rem;
  }
  .app-version {
    display: flex;
    align-items: flex-start;
    font-size: @rem14;
    line-height: @rem22;
    padding-bottom: @rem8;
    margin-bottom: @rem16;
    border-bottom: 1px dashed @yn-border-color-base;
  }
  .version-data {
    /* 中性色/@yn-text-color标题、重要文本 */
    color: @yn-text-color;
    width: 90%;
  }
  .label {
    /* 中性色/@yn-text-color-secondary次文本色 */
    color: @yn-text-color-secondary;
    width: 7rem;
    display: inline-block;
    flex-shrink: inherit;
  }

  .blue-icon {
    width: @rem4;
    height: @rem16;
    /* 主题色/@yn-primary-color主题 */
    background: @yn-primary-color;
    border-radius: 10px;
    margin-right: @rem6;
    flex: 0 0 @rem4;
  }
  .name {
    font-weight: 600;
    /* 中性色/标题、重要文本 */
    color: @yn-text-color;
    width: 8rem;
    display: flex;
    align-items: center;
  }
  :deep {
    .ant-modal-body {
      padding: @rem16 @rem24;
    }
    .ant-col {
      margin-bottom: @rem4;
      display: flex;
    }
  }
  .footer {
    font-size: @rem12;
    font-weight: normal;
    line-height: @rem20;
    /* 中性色/@yn-label-color标签文本-按钮 */
    color: @yn-label-color;
  }
}
</style>
