<template>
  <div class="group-modal">
    <yn-modal
      :visible="visible"
      :bodyStyle="{ minHeight: '60px' }"
      :title="groupObj.title ? $t_common('edit_group') : $t_common('add_group')"
      :okText="$t_common('save')"
      @cancel="cancelEvent"
    >
      <template slot="footer">
        <yn-button key="cancel" @click="cancelEvent">
          {{ $t_common("cancel") }}
        </yn-button>
        <yn-button
          key="submit"
          type="primary"
          :loading="btnLoading"
          @click="okEvent"
        >
          {{ $t_common("save") }}
        </yn-button>
      </template>
      <yn-form
        :form="formGroup"
        :colon="false"
        v-bind="{
          labelCol: { span: 6 },
          wrapperCol: { span: 16 }
        }"
      >
        <yn-form-item :label="$t_process('group_name')">
          <yn-input
            v-decorator="[
              'groupName',
              {
                initialValue: groupObj.title ? groupObj.title : '',
                rules: [
                  { required: true, message: $t_common('input_message') },
                  { max: 64, message: $t_process('field_length_exceeds_64') }
                ]
              }
            ]"
            :placeholder="$t_common('input_message')"
            @change="syncLang"
          >
            <!-- <yn-icon
              slot="suffix"
              class="cursor-p"
              type="global"
              @click="handleLanguages"
            /> -->
            <svg-icon
              slot="suffix"
              class="cursor-p"
              type="icon-multilingual"
              @click="handleLanguages"
            />
          </yn-input>
          <div v-show="renameTips" class="rename-tips">
            {{ renameTipInfo }}
          </div>
        </yn-form-item>
      </yn-form>
    </yn-modal>
    <language-modal
      :languageVisible="languageVisible"
      :languageList.sync="languageList"
      :curText="curText"
      :addCurLang="true"
      @cancelMultilanguage="cancelMultilanguage"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-option/";

import LanguageModal from "@/components/multilanguage";

import UiUtils from "yn-p1/libs/utils/UiUtils.js";
import { mapState, mapActions } from "vuex";
import taskFlowServeice from "@/services/taskFlowTemp";
import renameTip from "../../../../../mixin/renameTip";

export default {
  components: { LanguageModal },
  mixins: [renameTip],
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    groupObj: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formGroup: this.$form.createForm(this, "formGroup"),
      btnLoading: false,
      curText: "",
      languageVisible: false,
      languageList: []
    };
  },
  computed: {
    ...mapState({
      // 当前语言环境
      lang: state => state.common.lang
    })
  },
  watch: {
    visible: {
      immediate: true,
      handler(v) {
        if (v) {
          this.setSameNameF();
          this.formGroup.resetFields();
          this.setLang();
          if (this.groupObj.title) {
            this.curText = this.groupObj.title;
            this.getGroupLanguage(this.groupObj.objectId);
          }
        }
      }
    }
  },
  methods: {
    ...mapActions({
      setLang: "common/getEnableLanguages"
    }),
    // 获取分组多语言
    getGroupLanguage(key) {
      taskFlowServeice("getTemplateBase", key).then(res => {
        const { multiLanguages } = res.data.data;
        multiLanguages &&
          (this.languageList = multiLanguages.filter(
            item => item.languageCode !== this.lang
          ));
      });
    },
    handleLanguages() {
      this.languageVisible = true;
    },
    cancelMultilanguage(languageInfo) {
      this.languageVisible = false;
      this.languageList = languageInfo;
    },
    cancelEvent() {
      this.formGroup.resetFields();
      this.$emit("update:visible", false);
    },
    okEvent() {
      this.formGroup.validateFields((err, value) => {
        if (!err) {
          this.handleOk(value);
        }
      });
    },
    syncLang(e) {
      this.curText = e.target.lang;
      this.setSameNameF();
    },
    handleOk(values) {
      const { groupName: name } = values;
      const addParams = {
        name,
        multiLanguages: [
          ...this.languageList,
          {
            languageCode: this.lang,
            text: name
          }
        ]
      };
      const editParams = {
        objectId: this.groupObj.objectId,
        name,
        multiLanguages: [
          ...this.languageList,
          {
            languageCode: this.lang,
            text: name
          }
        ]
      };
      const apiName = this.groupObj.title ? "updateGroupTemp" : "saveGroup";
      const reqParams = this.groupObj.title ? editParams : addParams;
      this.btnLoading = true;
      taskFlowServeice(apiName, reqParams)
        .then(res => {
          if (res.data.data) {
            this.setSameNameT();
          } else {
            const tip = this.groupObj.title
              ? this.$t_common("edit_success")
              : this.$t_common("add_success");
            UiUtils.successMessage(tip);
            this.cancelEvent();
            this.$emit("onSave", "group");
          }
        })
        .finally(() => {
          this.btnLoading = false;
        });
    }
  }
};
</script>

<style lang="less" scoped>
@import "../../../../../commonLess/nameTips.less";
.cursor-p {
  cursor: pointer;
  line-height: 2rem !important;
}
</style>
