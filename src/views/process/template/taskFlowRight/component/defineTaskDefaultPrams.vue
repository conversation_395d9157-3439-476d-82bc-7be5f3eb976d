<template>
  <yn-drawer
    wrapClassName="task-params-drawer"
    :width="440"
    :title="$t_process('default_parameters2')"
    :visible="visible"
    @close="onCloseDrawer"
  >
    <div class="drawer-content" :style="{ height: `${contentHeight}px` }">
      <div
        v-for="(item, index) in paramsList"
        :key="item.memberId"
        class="content-item"
      >
        <yn-select
          v-model="item.dimCode"
          :placeholder="$t_process('select_dimension')"
          class="item-select"
          showSearch
          :filterOption="filterOption"
          @change="handleChange(index, item.dimCode)"
        >
          <yn-select-option
            v-for="dim in allDimList"
            :key="dim.key"
            :value="dim.value"
            :disabled="dim.disabled"
          >
            {{ dim.label }}
          </yn-select-option>
        </yn-select>
        <AsynSelectDimMember
          :value="item.memberId"
          class="item-select-tree"
          :dimCode="item.dimCode"
          :disabled="!item.dimCode"
          searchMode="single"
          :nonleafselectable="true"
          :placeholder="$t_process('select_members')"
          @changeVal="(e, item) => changeVal(e, item, index)"
        />
        <svg-icon
          :isIconButton="true"
          type="icon-shanchuicon"
          :title="$t_common('delete')"
          @onClick="deleteParams(index)"
        />
      </div>
      <div class="content-btns">
        <yn-button :disabled="disabledAddBtn" type="text" @click="addParams">
          {{ $t_process("add_parameters") }}
        </yn-button>
      </div>
    </div>
    <div class="drawer-btns">
      <yn-button class="btn" @click="onCloseDrawer">
        {{ $t_common("cancel") }}
      </yn-button>
      <yn-button class="btn" type="primary" @click="saveEvent">
        {{ $t_common("save") }}
      </yn-button>
    </div>
  </yn-drawer>
</template>

<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-option/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-icon-button/";

import { handleDim } from "@/utils/taskFlowTemp.js";
// import { pavingTree } from "@/utils/taskFlowTemp.js";
import taskFlowTempService from "@/services/taskFlowTemp";
import AsynSelectDimMember from "../../../../../components/hoc/asynSelectDimMember";

export default {
  components: { AsynSelectDimMember },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    clickParamsInfo: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      contentHeight: 0,
      paramsList: [],
      dataSource: [],
      allDimList: [],
      // dimCode: dimMembers
      dimCodeMap: {},
      // dimCode: dimName
      dimCodeNameMap: {},
      // dimMemberId: dimMemberInfo
      dimMemberMap: {},
      searchDimMemberKey: "",
      searchDimMemberList: []
    };
  },
  computed: {
    disabledAddBtn() {
      return !!(
        this.paramsList.length &&
        !this.paramsList[this.paramsList.length - 1].memberId
      );
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(v) {
        if (v) {
          this.getPageHeight();
          this.getAllDims();
          this.enchoData(this.clickParamsInfo);
        }
      }
    }
  },
  methods: {
    async enchoData(v) {
      if (v.length) {
        const dimCodes = v.map(item => item.dimCode);
        for (let i = 0; i < dimCodes.length; i++) {
          await this.getDimMembers(dimCodes[i]);
        }
        this.paramsList = v.map(item => ({
          dimCode: item.dimCode,
          memberId: item.dimMemberId,
          memberItemInfo: {
            dimMemberRealName: item.dimMemberName,
            ...item
          }
        }));
      } else {
        this.paramsList = [];
      }
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 获取所有维度
    getAllDims() {
      taskFlowTempService("getAllDims").then(res => {
        handleDim(res.data.data);
        this.allDimList = res.data.data;
        this.allDimList.forEach(dim => {
          this.dimCodeNameMap[dim.dimCode] = dim;
          // 禁用已选维度
          dim.disabled = !!this.clickParamsInfo.filter(
            item => dim.dimCode === item.dimCode
          ).length;
        });
      });
    },
    getPageHeight() {
      const pageH = window.innerHeight - 92;
      this.contentHeight = pageH;
    },
    handleDimMembers(members) {
      const loop = data => {
        data.forEach(item => {
          item.key = item.id;
          item.label = item.name;
          if (item.children && item.children.length) {
            loop(item.children);
          }
        });
      };
      loop(members);
    },
    // 根据 dimCode 获取维度成员
    async getDimMembers(dimCode) {
      if (this.dimCodeMap[dimCode]) return;
      await taskFlowTempService("getDimMembersTree", dimCode).then(res => {
        this.handleDimMembers(res.data);
        this.$set(this.dimCodeMap, dimCode, res.data);
      });
    },
    // 下拉框 选择值该改变
    handleChange(index, dimCode) {
      const dimCodeList = this.paramsList.map(item => item.dimCode);
      this.allDimList.forEach(dim => {
        dim.disabled = dimCodeList.includes(dim.value);
      });
      this.getDimMembers(dimCode);
      // 当选择维度、维度成员后 然后 对改行维度进行切换 将维度成员置空
      this.paramsList[index].memberId = undefined;
    },
    changeVal(e, item, index) {
      this.paramsList[index].memberItemInfo = item[0];
      this.paramsList[index].memberId = e;
    },
    addParams() {
      this.paramsList.push({
        dimCode: undefined,
        memberId: ""
      });
    },
    deleteParams(index) {
      this.paramsList.splice(index, 1);
    },
    onCloseDrawer() {
      this.$emit("update:visible", false);
    },
    saveEvent() {
      const chooseParams = this.paramsList
        .filter(item => item.dimCode && item.memberId)
        .map(item => {
          const dimObj = this.dimCodeNameMap[item.dimCode];
          // 这段代码可注释。 changeVal 回调中返了 选中的 item 信息。
          // const memberObj = pavingTree(this.dimCodeMap[item.dimCode]).find(
          //   el => el.key === item.memberId
          // );
          return {
            dimCode: item.dimCode,
            dimName: dimObj.dimName,
            dimMemberId: item.memberId,
            dimMemberName: item.memberItemInfo.dimMemberRealName
            // dimMemberName: memberObj.name
          };
        });
      this.$emit("saveDefaultParams", chooseParams);
      this.onCloseDrawer();
    }
  }
};
</script>

<style lang="less">
.task-params-drawer {
  .ant-drawer-body {
    padding: 0;
    border-bottom: 1px solid @yn-border-color-base;
  }
}
</style>
<style lang="less" scoped>
.drawer-content {
  overflow-y: scroll;
  padding: @yn-padding-xxxl @yn-padding-xl @yn-padding-xxxl @yn-padding-xxxl;
  .content-item {
    display: flex;
    .item-select {
      width: 10.1875rem;
      margin-right: @yn-margin-l;
    }
    .item-select-tree {
      flex: 1;
      margin: 0 @yn-margin-s 0 @yn-margin-l;
    }
    .icon-shanchuicon {
      margin-left: @rem8;
    }
    margin-bottom: @yn-margin-xxxl;
  }
}
.drawer-btns {
  position: absolute;
  bottom: 0;
  height: 2.875rem;
  line-height: 2.875rem;
  width: 100%;
  text-align: right;
  padding-right: @yn-padding-xl;
  background: @yn-component-background;
  .btn {
    &:first-child {
      margin-right: @yn-padding-xl;
    }
    width: 5rem;
  }
}
</style>
