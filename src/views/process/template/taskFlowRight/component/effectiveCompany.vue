<template>
  <div class="effective-company">
    <div class="effective-company-header">
      <span class="header-title">
        {{ $t_process("valid_entities") }}
        <yn-divider v-show="!editing" type="vertical" />
        <span v-show="!editing" class="header-count">
          {{ $t_process("total") }}：{{ totalCompany }}
        </span>
      </span>
      <br />
      <span
        v-if="editing"
        :class="[
          'header-desc',
          showEffectiveTips && totalCompany === 0
            ? 'desc-error'
            : 'desc-default'
        ]"
      >
        {{ $t_process("please_add_entity") }}
      </span>
    </div>
    <div
      class="effective-company-list"
      :class="{ 'edit-effective-company': !editing }"
    >
      <div v-if="!entityCompany.length" class="list-content list-left-empty">
        <div class="company-name">
          <span class="name-style">{{ $t_process("single_entities") }}</span>
          <span v-show="!editing">{{ $t_process("subtotal") }}：0</span>
        </div>
        <span v-if="editing" class="empty-desc">
          {{
            $t_process("no_single_entities_simple") + "," + $t_common("please")
          }}
          <yn-button type="link" @click="showTransfer('entity')">
            {{ $t_common("add_to") }}
          </yn-button>
        </span>
        <span v-else class="empty-desc">
          {{ $t_common("not_have") + $t_process("single_entities") }}
        </span>
      </div>
      <div v-else class="list-content list-left-empty">
        <div class="list-content-head">
          <span class="name-style">{{ $t_process("single_entities") }}</span>
          <yn-button v-if="editing" type="link" @click="showTransfer('entity')">
            {{ $t_common("add_to") }}
          </yn-button>
          <span v-else>
            {{ $t_process("subtotal") }}：{{ entityCompany.length }}
          </span>
        </div>
        <yn-input-search
          v-show="!editing"
          v-model="searchEntity"
          class="list-search"
          :placeholder="$t_process('please_search_here')"
          @search="onSearch('entity')"
        />
        <div class="list-content-com">
          <p
            v-for="item in searchEntitying ? searchEntityList : entityCompany"
            :key="item.key"
            :title="item.title"
            class="com-item"
          >
            {{ item.title }}
          </p>
        </div>
      </div>
      <div v-if="!scopeCompany.length" class="list-content list-right-empty">
        <div class="company-name">
          <span class="name-style">{{
            $t_process("consolidated_entities")
          }}</span>
          <span v-show="!editing">{{ $t_process("subtotal") }}：0</span>
        </div>
        <span v-if="editing" class="empty-desc">
          {{
            $t_process("no_consolidated_entities_simple") +
              "," +
              $t_common("please")
          }}
          <yn-button type="link" @click="showTransfer('scope')">
            {{ $t_common("add_to") }}
          </yn-button>
        </span>
        <span v-else class="empty-desc">
          {{ $t_common("not_have") + $t_process("consolidated_entities") }}
        </span>
      </div>
      <div v-else class="list-content list-right-empty">
        <div class="list-content-head">
          <span class="name-style">{{
            $t_process("consolidated_entities")
          }}</span>
          <yn-button v-if="editing" type="link" @click="showTransfer('scope')">
            {{ $t_common("add_to") }}
          </yn-button>
          <span v-else>
            {{ $t_process("subtotal") }}：{{ scopeCompany.length }}
          </span>
        </div>
        <yn-input-search
          v-show="!editing"
          v-model="searchScope"
          class="list-search"
          :placeholder="$t_process('please_search_here')"
          @search="onSearch('scope')"
        />
        <div class="list-content-com">
          <p
            v-for="item in searchScopeing ? searchScopeList : scopeCompany"
            :key="item.key"
            :title="item.title"
            class="com-item"
          >
            {{ item.title }}
          </p>
        </div>
      </div>
    </div>
    <add-member
      v-if="visible"
      :closeAnalysis="true"
      :dimInfo="transferData"
      :closeTransfer="closeTransfer"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-button/";
import commonService from "@/services/common";
import { formatRequestParams } from "@/utils/common";
import { enchoTransferData } from "@/utils/taskFlowTemp.js";
import AddMember from "../../../../../components/hoc/newDimensionTransfer/index.vue";

import "yn-p1/libs/components/yn-input-search/";
import { formatRequestParams as formatEnchoExp } from "@/utils/journal.js";
import DIM_INFO from "@/constant/dimMapping";
import lang from "@/mixin/lang";
const { $t_process } = lang;
export default {
  components: { AddMember },
  props: {
    editing: {
      type: Boolean,
      default: false
    },
    effectiveCompany: {
      type: Object,
      default: () => ({})
    },
    showEffectiveTips: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      entityCompany: [],
      scopeCompany: [],
      memberExp: {
        expDtoList: [] // 表达式
      }, // 表达式入参
      visible: false,
      transferData: {},
      scopeDimInfo: {
        dimName: $t_process("scopes"),
        objectId: DIM_INFO.Scope,
        dimId: DIM_INFO.Scope,
        dimCode: "Scope"
      }, // 合并体公司(合并组)维度信息
      entityDimInfo: {
        dimName: $t_process("entity"),
        dimId: DIM_INFO.Entity,
        objectId: DIM_INFO.Entity,
        dimCode: "Entity"
      }, // 单体公司(组织)维度信息
      searchEntity: "",
      searchEntitying: false,
      searchEntityList: [],
      searchScope: "",
      searchScopeing: false,
      searchScopeList: []
    };
  },
  computed: {
    totalCompany() {
      return this.entityCompany.length + this.scopeCompany.length;
    }
  },
  watch: {
    effectiveCompany: {
      deep: true,
      handler(v) {
        this.resetData();
        this.initData(v);
      }
    }
  },
  methods: {
    initData(v) {
      const { entity, scope, memberExp } = v;
      this.entityCompany = enchoTransferData(entity);
      this.scopeCompany = enchoTransferData(scope);
      // 生效公司 表达式需要保留之前静态的（即改动态之前需要保留静态的情况下的成员）
      if (memberExp) {
        this.memberExp = JSON.parse(memberExp);
      } else {
        // 保留之前静态成员
        const obj = {
          entity,
          scope
        };
        Object.keys(obj).forEach(item => {
          // 如果上次有静态成员
          if (obj[item] && obj[item].length) {
            this.memberExp.expDtoList.push({
              dimId: this[`${item}DimInfo`].dimId,
              dimMemberExps: JSON.stringify({
                allMember: false,
                attrUnion: true,
                member: obj[item].map(item => ({
                  dimMemberShared: item.dimMemberShared,
                  memberId: item.dimMemberId,
                  memberName: item.dimMemberName,
                  memberTypeValue: "self"
                }))
              })
            });
          }
        });
      }
    },
    showTransfer(type) {
      this.visible = true;
      const item = this.memberExp.expDtoList.find(
        item => item.dimId === this[`${type}DimInfo`].dimId
      );
      this.transferData = {
        ...this[`${type}DimInfo`],
        dynamicOrStatic: "dynamic",
        members:
          item && item.dimMemberExps
            ? formatEnchoExp(JSON.parse(item.dimMemberExps), true)
            : ""
        // selectedItem: cloneDeep(this[`${type}Company`])
      };
    },
    closeTransfer(exprObj) {
      if (!exprObj) {
        this.visible = false;
        return;
      }
      const { dimId, dimCode } = this.transferData;
      this.$emit("addSaveEventCb");
      this.$emit("closeTips", false);
      const dataList = [
        "memberType",
        "member",
        "level",
        "attr",
        "subset",
        "variable"
      ];
      const hasData = dataList.some(key => {
        return exprObj[key] && exprObj[key].length > 0;
      });
      const dimMemberExps = formatRequestParams(exprObj);
      // this[`${dimCode.toLowerCase()}CompanyExp`] = dimMemberExps;
      const curExp = this.memberExp.expDtoList.find(
        item => item.dimId === dimId
      );
      if (curExp) {
        // 修改
        curExp.dimMemberExps = dimMemberExps;
      } else {
        // 添加
        this.memberExp.expDtoList.push({
          dimId,
          dimMemberExps
        });
      }
      // 可能选择全部成员
      if (!hasData && !exprObj.allMember) {
        this.visible = false;
        this[`${dimCode.toLowerCase()}Company`] = [];
        this.memberExp.expDtoList = this.memberExp.expDtoList.filter(
          item => item.dimId !== dimId
        );
        return;
      }
      commonService("getExpMember", {
        expDtoList: [
          {
            dimId,
            dimMemberExps
          }
        ]
      }).then(res => {
        if (res.data && res.data[0] && res.data[0].members) {
          this[`${dimCode.toLowerCase()}Company`] = res.data[0].members.map(
            item => {
              return {
                dimMemberId: item.objectId,
                dimMemberName: item.dimMemberName,
                dimCode,
                key: `${item.objectId}-self`,
                label: item.dimMemberName,
                memberType: 1,
                memberTypeValue: "self",
                objectId: `${item.objectId}-self`,
                shardim: item.dimMemberShared,
                title: item.dimMemberName,
                type: "member",
                value: item.objectId
              };
            }
          );
        } else {
          this[`${dimCode.toLowerCase()}Company`] = [];
        }
        this.$nextTick(() => {
          this.visible = false;
        });
      });
    },
    onSearch(type) {
      const handleType = `${type.slice(0, 1).toUpperCase()}${type.slice(1)}`;
      this[`search${handleType}ing`] = !!this[`search${handleType}`];
      this[`search${handleType}List`] = this[`${type}Company`].filter(item =>
        item.title.includes(this[`search${handleType}`])
      );
    },
    resetData() {
      this.searchEntity = "";
      this.searchScope = "";
      this.memberExp = {
        expDtoList: []
      };
      this.searchEntitying = false;
      this.searchScopeing = false;
    },
    getDataInfo() {
      return {
        entityCompany: this.entityCompany,
        scopeCompany: this.scopeCompany,
        memberExp: this.memberExp
      };
    }
  }
};
</script>

<style lang="less" scoped>
.effective-company {
  margin-top: @yn-margin-xxxl;
  .effective-company-header {
    color: @yn-heading-color;
    font-size: @rem14;
    .header-title {
      display: inline-block;
      height: @rem22;
      line-height: @rem22;
      .header-count {
        color: @yn-label-color;
      }
    }
    .header-desc {
      font-size: @rem12;
      display: inline-block;
      height: @rem20;
      line-height: @rem20;
      margin: @yn-margin-s 0;
    }
    .desc-default {
      color: @yn-label-color;
    }
    .desc-error {
      color: @yn-error-color;
    }
  }
  .edit-effective-company {
    margin-top: @yn-margin-xl;
  }
  .effective-company-list {
    min-height: 6.375rem;
    display: flex;
    .name-style {
      color: @yn-text-color-secondary;
      font-weight: 500;
    }
    .list-content {
      width: 50%;
      box-sizing: border-box;
      background: @yn-background-color;
      padding: @yn-padding-l @yn-padding-xl;
      padding-bottom: 0;
      .company-name {
        font-size: @rem14;
        color: @yn-text-color-secondary;
        display: flex;
        justify-content: space-between;
      }
      .empty-desc {
        display: inline-block;
        margin-top: @yn-margin-xl;
        width: 100%;
        text-align: center;
        color: @yn-label-color;
      }
      .list-content-head {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .list-search {
        margin-top: @yn-margin-s;
      }
      .list-content-com {
        margin-top: @rem10;
        max-height: 32.375rem;
        overflow-y: scroll;
        .com-item {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: @yn-text-color;
        }
      }
    }
    .list-left-empty {
      margin-right: @yn-margin-s;
    }
    .list-right-empty {
      margin-left: @yn-margin-s;
    }
  }
}
</style>
