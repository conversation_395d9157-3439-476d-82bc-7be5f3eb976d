<template>
  <div class="template-modal">
    <yn-modal
      :visible="visible"
      :title="
        templateObj.title
          ? $t_process('edit_template')
          : $t_process('new_template')
      "
      :okText="$t_common('save')"
      @cancel="cancelEvent"
    >
      <template slot="footer">
        <yn-button key="cancel" @click="cancelEvent">
          {{ $t_common("cancel") }}
        </yn-button>
        <yn-button
          key="submit"
          type="primary"
          :loading="btnLoading"
          @click="okEvent"
        >
          {{ $t_common("save") }}
        </yn-button>
      </template>
      <yn-spin :spinning="spinning">
        <yn-form
          :form="formTemplate"
          :colon="false"
          v-bind="{
            labelCol: { span: 6 },
            wrapperCol: { span: 16 }
          }"
        >
          <yn-form-item :label="$t_process('template_name')">
            <yn-input
              v-decorator="[
                'templateName',
                {
                  initialValue: templateObj.title,
                  rules: [
                    { required: true, message: $t_common('input_message') },
                    { max: 64, message: $t_process('field_length_exceeds_64') }
                  ]
                }
              ]"
              :placeholder="$t_common('input_message')"
              @change="syncLang"
            >
              <svg-icon
                slot="suffix"
                class="cursor-p"
                type="icon-multilingual"
                @click="handleLanguages"
              />
            </yn-input>
            <div v-show="renameTips" class="rename-tips">
              {{ renameTipInfo }}
            </div>
          </yn-form-item>
          <yn-form-item :label="$t_process('template_group')">
            <yn-select
              v-decorator="[
                'groupId',
                {
                  initialValue: templateObj.pId,
                  rules: [
                    { required: true, message: $t_common('input_select') }
                  ]
                }
              ]"
              :placeholder="$t_common('input_select')"
              showSearch
              :filterOption="filterOption"
            >
              <yn-select-option v-for="item in groupList" :key="item.objectId">
                {{ item.name }}
              </yn-select-option>
            </yn-select>
          </yn-form-item>
          <yn-form-item :label="$t_common('explain')" class="form-remark">
            <yn-textarea
              v-decorator="[
                'remark',
                {
                  initialValue: templateDesc,
                  rules: [
                    {
                      required: false,
                      max: 50,
                      message: $t_process('field_length_exceeds_50')
                    }
                  ]
                }
              ]"
              :allowClear="false"
              type="textarea"
              :placeholder="$t_common('input_message')"
              :autoSize="{ minRows: 3, maxRows: 8 }"
              @change="onChange($event)"
            />
            <div class="text-count">{{ descriptionLen }}/50</div>
          </yn-form-item>
        </yn-form>
      </yn-spin>
    </yn-modal>

    <language-modal
      :languageVisible="languageVisible"
      :languageList.sync="languageList"
      :curText="curText"
      :addCurLang="true"
      @cancelMultilanguage="cancelMultilanguage"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-select-option/";
import "yn-p1/libs/components/yn-textarea/";

import LanguageModal from "@/components/multilanguage";

import taskFlowServeice from "@/services/taskFlowTemp";
import { mapState, mapActions } from "vuex";
import UiUtils from "yn-p1/libs/utils/UiUtils.js";

import renameTip from "../../../../../mixin/renameTip";

export default {
  components: { LanguageModal },
  mixins: [renameTip],
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    templateObj: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      spinning: false,
      formTemplate: this.$form.createForm(this, "formTemplate"),
      btnLoading: false,
      groupList: [],
      curText: "",
      descriptionLen: 0,
      templateDesc: "",
      languageVisible: false,
      languageList: []
    };
  },
  computed: {
    ...mapState({
      // 当前语言环境
      lang: state => state.common.lang
    })
  },
  watch: {
    visible: {
      immediate: true,
      handler(v) {
        if (v) {
          this.setSameNameF();
          this.setLang();
          this.getGroups();
          this.getTemplateBase(this.templateObj.key);
          this.curText = this.templateObj.title;
        }
      }
    }
  },
  created() {
    this.getGroups();
  },
  methods: {
    ...mapActions({
      setLang: "common/getEnableLanguages"
    }),
    syncLang(e) {
      this.curText = e.target.value;
      this.setSameNameF();
    },
    handleLanguages() {
      this.languageVisible = true;
    },
    cancelMultilanguage(languageInfo) {
      this.languageVisible = false;
      this.languageList = languageInfo;
    },
    // 获取模板基础信息 说明 等
    getTemplateBase(key) {
      if (!key) {
        this.templateDesc = "";
        this.descriptionLen = 0;
        return;
      }
      this.spinning = true;
      taskFlowServeice("getTemplateBase", key)
        .then(res => {
          const { desc, multiLanguages } = res.data.data;
          this.templateDesc = desc;
          this.descriptionLen = this.templateDesc.length;
          multiLanguages &&
            (this.languageList = multiLanguages.filter(
              item => item.languageCode !== this.lang
            ));
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    // 获取模板分组
    getGroups() {
      this.spinning = true;
      taskFlowServeice("getGroups")
        .then(res => {
          if (res.data.data && res.data.data.length) {
            res.data.data.forEach(item => {
              item.label = item.name;
              item.key = item.objectId;
            });
            this.groupList = res.data.data;
          }
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    cancelEvent() {
      this.formTemplate.resetFields();
      this.$emit("update:visible", false);
    },
    okEvent() {
      this.formTemplate.validateFields((err, value) => {
        if (!err) {
          this.handleOk(value);
        }
      });
    },
    handleOk(values) {
      const { templateName: name, groupId, remark } = values;
      const params = {
        pId: groupId,
        name,
        desc: remark,
        multiLanguages: [...this.languageList]
      };
      const editParams = {
        pId: groupId,
        objectId: this.templateObj.key,
        name,
        desc: remark,
        multiLanguages: [...this.languageList]
      };
      const apiName = this.templateObj.title
        ? "updateGroupTemp"
        : "saveTemplate";
      const reqParams = this.templateObj.title ? editParams : params;
      this.btnLoading = true;
      taskFlowServeice(apiName, reqParams)
        .then(res => {
          const createPersonInfo = res.data && res.data.data;
          if (
            Object.prototype.toString.call(createPersonInfo) ===
            "[object String]"
          ) {
            this.setSameNameT();
            return;
          }
          let tip = this.$t_common("add_success");
          let responeseInfo = { ...createPersonInfo, pId: groupId };
          if (this.templateObj.title) {
            tip = this.$t_common("edit_success");
            responeseInfo = {
              templateId: this.templateObj.key,
              pId: groupId,
              name,
              remark,
              createBy: "",
              createDate: ""
            };
          }
          UiUtils.successMessage(tip);
          this.cancelEvent();
          this.$emit("onSave", "template", responeseInfo);
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    onChange(e) {
      this.descriptionLen = e.target.value.length;
    }
  }
};
</script>

<style lang="less" scoped>
@import "../../../../../commonLess/nameTips.less";

.same-name {
  color: @yn-error-color;
  height: @rem22;
  line-height: @rem22;
}
.cursor-p {
  cursor: pointer;
  line-height: 2rem !important;
}
.text-count {
  height: @rem22;
  line-height: @rem22;
  position: absolute;
  top: -@rem10;
  right: @rem10;
  color: @yn-disabled-color;
}
.form-remark {
  /deep/.ant-form-item-control {
    .ant-input {
      height: 6rem;
    }
  }
}
</style>
