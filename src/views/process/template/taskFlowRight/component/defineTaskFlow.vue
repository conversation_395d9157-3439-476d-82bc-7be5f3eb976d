<template>
  <div class="define-task-flow">
    <div class="task-flow-title">{{ $t_process("define_task_flow") }}</div>
    <yn-table
      class="task-flow-table"
      :columns="columns"
      :data-source="tableData"
      :scroll="{ x: true }"
    >
      <div
        slot="table.operationName"
        slot-scope="text, record, index"
        class="task-flow-name valid-prop"
      >
        <template v-if="editing">
          <yn-input
            v-model="record.operationName"
            :placeholder="$t_common('input_message')"
            :class="{
              'valid-error-border':
                (fieldEmpty && !record.operationName) ||
                (operationNameLen && record.operationName.length > 64)
            }"
            @change="e => inputValueChange(e, index, 'operationName', record)"
            @blur="inputBlur(record.operationName, 'operationName')"
          >
            <yn-icon
              slot="suffix"
              type="global"
              @click="
                handleLanguages(
                  record,
                  'operationNameMultiLanguages',
                  record.operationName
                )
              "
            />
          </yn-input>
          <span :class="getErrorStyle(record)">
            <span
              v-show="fieldEmpty && !record.operationName"
              class="invalid-tip-text-task"
            >
              {{ $t_common("input_message") + $t_process("activity_name") }}
            </span>
            <span
              v-show="operationNameLen && record.operationName.length > 64"
              class="invalid-tip-text-task"
            >
              {{ $t_process("field_length_exceeds_64") }}
            </span>
          </span>
        </template>
        <span
          v-else
          v-tooltip="{
            visibleOnOverflow: true,
            title: text
          }"
          class="operate-name"
        >
          {{ text }}
        </span>
      </div>
      <div slot="table.operationNameTitle" class="task-flow-star">
        {{ $t_process("activity_name") }}
      </div>
      <div
        slot="table.operationType"
        slot-scope="text, record, index"
        class="task-flow-type valid-prop"
      >
        <template v-if="editing">
          <yn-select
            v-model="record.operationType"
            :allowClear="false"
            :placeholder="
              $t_common('input_select') + $t_process('activity_type')
            "
            :class="{
              'valid-error-border': fieldEmpty && !record.operationType
            }"
            @change="val => changeValue(val, record, 'operationType', index)"
          >
            <yn-select-option
              v-for="item in typeList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </yn-select-option>
          </yn-select>
          <span :class="getErrorStyle(record)">
            <span
              v-show="fieldEmpty && !record.operationType"
              class="invalid-tip-text-task"
            >
              {{ $t_common("input_select") + $t_process("activity_type") }}
            </span>
          </span>
        </template>
        <span v-else class="ml16">{{
          text ? getValueBykey("typeList", text) : ""
        }}</span>
      </div>
      <div slot="table.operationTypeTitle" class="task-flow-star">
        {{ $t_process("activity_type") }}
      </div>
      <div
        slot="table.operationLink"
        slot-scope="text, record, index"
        class="task-flow-url valid-prop"
      >
        <template v-if="editing">
          <!-- <span v-show="record.operationType === 'operate'" class="ml16">
            -
          </span> -->
          <yn-input
            v-show="!record.operationType"
            :placeholder="$t_common('input_select')"
            disabled
            value="-"
          />
          <yn-select
            v-if="record.operationType === 'operate'"
            :value="record.operationLink[record.operationType] || undefined"
            :placeholder="$t_common('input_select')"
            showArrow
            :class="{
              'valid-error-border':
                fieldEmpty &&
                (!record.operationLink[record.operationType] ||
                record.operationLink[record.operationType].length === 0)
            }"
            @change="val => changeValue(val, record, 'operationLink', index)"
          >
            <yn-select-option
              v-for="item in buttonNameList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </yn-select-option>
          </yn-select>
          <yn-select
            v-if="record.operationType === 'script'"
            :value="record.operationLink[record.operationType] || undefined"
            :placeholder="$t_common('input_select')"
            showArrow
            :class="{
              'valid-error-border':
                fieldEmpty && !record.operationLink[record.operationType]
            }"
            @change="
              val => changeScriptValue(val, record, 'operationLink', index)
            "
          >
            <yn-select-option
              v-for="item in scriptList"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </yn-select-option>
          </yn-select>
          <yn-input
            v-show="['outURL', 'URL'].includes(record.operationType)"
            v-model="record.operationLink[record.operationType]"
            :placeholder="$t_common('input_message')"
            :class="{
              'valid-error-border':
                fieldEmpty && !record.operationLink[record.operationType]
            }"
            @change="e => inputValueChange(e, index)"
          />
          <yn-select-tree
            v-show="record.operationType === 'page'"
            v-model="record.operationLink[record.operationType]"
            :allowClear="false"
            :placeholder="$t_common('input_select')"
            :datasource="$data[`${record.operationType}List`]"
            searchMode="single"
            :class="{
              'valid-error-border':
                fieldEmpty && !record.operationLink[record.operationType]
            }"
          />
          <div
            v-show="record.operationType === 'form'"
            class="url-form"
            :class="{
              'valid-error-border': fieldEmpty && !text['form']
            }"
            @click="clickFormCell(index, text.form)"
          >
            <span :class="{ 'placeholder-color': !text['form'] }">
              {{
                text["form"]
                  ? echoFormName(text["form"])
                  : $t_common("input_select")
              }}
            </span>
          </div>
          <span :class="getErrorStyle(record)">
            <span
              v-show="fieldEmpty && !record.operationLink[record.operationType]"
              class="invalid-tip-text-task"
            >
              {{
                ["form", "page", "operate", "script"].includes(
                  record.operationType
                )
                  ? $t_common("input_select")
                  : $t_common("input_message")
              }}
            </span>
          </span>
        </template>
        <!-- <span class="ml16">{{ text }}</span> -->
        <template v-else>
          <span
            v-if="['outURL', 'URL'].includes(record.operationType)"
            v-tooltip="{
              visibleOnOverflow: true,
              title: text[record.operationType]
            }"
            class="url-form-text"
          >
            {{ text[record.operationType] }}
          </span>
          <span v-if="record.operationType === 'operate'" class="ml16">
            {{
              text
                ? getValueBykey("buttonNameList", [text[record.operationType]])
                : ""
            }}
          </span>
          <span v-if="record.operationType === 'script'" class="ml16">
            {{
              text
                ? getValueBykey("scriptList", [text[record.operationType]])
                : ""
            }}
          </span>
          <span v-if="record.operationType === 'page'" class="ml16">
            {{
              menuMapObj[text[record.operationType]] &&
                menuMapObj[text[record.operationType]].menuName
            }}
          </span>
          <span
            v-if="record.operationType === 'form'"
            v-tooltip="{
              visibleOnOverflow: true,
              title: text['form'] ? echoFormName(text['form']) : ''
            }"
            class="url-form-text"
          >
            {{ text["form"] ? echoFormName(text["form"]) : "" }}
          </span>
        </template>
      </div>
      <div slot="table.operationLinkTitle" class="task-flow-star">
        {{ $t_process("hyperlinks") }}
      </div>
      <div slot="table.buttonNameTitle" class="task-flow-star">
        {{ $t_process("button_name") }}
      </div>
      <div
        slot="table.buttonName"
        slot-scope="text, record, index"
        class="task-flow-button-name valid-prop"
      >
        <template v-if="editing">
          <yn-input
            v-model="record.buttonName[record.operationType]"
            :placeholder="$t_common('input_message')"
            :class="{
              'valid-error-border':
                fieldEmpty &&
                (!record.buttonName[record.operationType] ||
                record.buttonName[record.operationType].length === 0)
            }"
            @change="e => inputValueChange(e, index, 'buttonName', record)"
          >
            <yn-icon
              slot="suffix"
              type="global"
              @click="
                handleLanguages(
                  record,
                  'buttonNameMultiLanguages',
                  record.buttonName[record.operationType]
                )
              "
            />
          </yn-input>
          <span :class="getErrorStyle(record)">
            <span
              v-show="
                fieldEmpty &&
                  (!record.buttonName[record.operationType] ||
                  record.buttonName[record.operationType].length === 0)
              "
              class="invalid-tip-text-task"
            >
              {{ $t_common("input_message") + $t_process("button_name") }}
            </span>
            <span
              v-show="
                record.operationType &&
                  record.operationType !== 'operate' &&
                  record.buttonName &&
                  record.buttonName[record.operationType] &&
                  record.buttonName[record.operationType].length > 64
              "
              class="error-tips"
            >
              {{ $t_process("field_length_exceeds_64") }}
            </span>
          </span>
        </template>
        <span
          v-else
          v-tooltip="{
            visibleOnOverflow: true,
            title: text
          }"
          class="button-name-text"
        >
          {{ text[record.operationType] }}
        </span>
      </div>
      <div
        slot="table.defaultParam"
        slot-scope="text, record, index"
        class="task-flow-params valid-prop"
      >
        <template v-if="editing">
          <div
            :class="{
              'default-params': true,
              'placeholder-color': !record.defaultParam.length
            }"
            @click="clickParamsCell(index, record.defaultParam || [])"
          >
            {{
              record.defaultParam.length
                ? echoParamsName(record.defaultParam)
                : $t_common("input_select")
            }}
          </div>
          <span :class="getErrorStyle(record)"></span>
        </template>
        <span
          v-if="!editing"
          v-tooltip="{
            visibleOnOverflow: true,
            title: text.length ? echoParamsName(record.defaultParam) : ''
          }"
          class="default-params-text"
        >
          {{ text.length ? echoParamsName(record.defaultParam) : "-" }}
        </span>
      </div>
      <div
        slot="table.remark"
        slot-scope="text, record"
        class="task-flow-remark valid-prop"
      >
        <template v-if="editing">
          <yn-input
            v-model="record.remark"
            :placeholder="$t_common('input_message')"
            :class="{
              'valid-error-border': remarkLen && record.remark.length > 50
            }"
            @change="e => inputValueChange(e, index, 'remark', record)"
            @blur="inputBlur(record.remark, 'remark')"
          >
            <yn-icon
              slot="suffix"
              type="global"
              @click="
                handleLanguages(record, 'remarkMultiLanguages', record.remark)
              "
            />
          </yn-input>
          <span :class="getErrorStyle(record)">
            <span
              v-show="remarkLen && record.remark.length > 50"
              class="invalid-tip-text-task"
            >
              {{ $t_process("field_length_exceeds_50") }}
            </span>
          </span>
        </template>
        <span
          v-else
          v-tooltip="{
            visibleOnOverflow: true,
            title: text
          }"
          class="flow-remark-text"
        >
          {{ text }}
        </span>
      </div>
      <template slot="table.operation" slot-scope="text, record, index">
        <a
          class="delete-text ml16 yn-a-link"
          href="javascript:;"
          @click="deleteRecord(index)"
        >
          {{ $t_common("delete") }}
        </a>
        <yn-dropdown
          overlayClassName="operate-menu"
          class="yn-a-link"
          :trigger="['click']"
        >
          <svg-icon :isIconBtn="false" type="icon-more" />
          <yn-menu slot="overlay" @click="e => actionClick(e, index)">
            <yn-menu-item
              v-for="item in actionList"
              :key="item.actionMethod"
              :disabled="disabledMenu(item.actionMethod, index)"
            >
              {{ item.actionName }}
            </yn-menu-item>
          </yn-menu>
        </yn-dropdown>
      </template>
    </yn-table>
    <div v-show="editing" class="footer-btn" @click="handleAdd">
      <svg-icon type="icon-cr_add" class="add-icon" />{{ $t_common("add_to") }}
    </div>

    <define-task-default-prams
      :visible.sync="paramsVisible"
      :clickParamsInfo="clickParamsInfo"
      @saveDefaultParams="saveDefaultParams"
    />
    <transfer-like
      :visible.sync="formVisible"
      :clickFormInfo="clickFormInfo"
      @getForm="getForm"
    />
    <language-modal
      :languageVisible="languageVisible"
      :curText="curText"
      :languageList="multiLanguages"
      @cancelMultilanguage="cancelMultilanguage"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-select-option/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import LanguageModal from "@/components/multilanguage";
import { mapActions, mapState } from "vuex";
import { addKeyToTree, pavingTree } from "@/utils/taskFlowTemp.js";
import cloneDeep from "lodash/cloneDeep";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import _debounce from "lodash/debounce";
import { genLanguages } from "../../../../../utils/common";
import DefineTaskDefaultPrams from "./defineTaskDefaultPrams.vue";
import TransferLike from "../../../../../components/hoc/transferLike/index.vue";
import taskFlowService from "@/services/taskFlowTemp";

export default {
  components: { DefineTaskDefaultPrams, TransferLike, LanguageModal },
  props: {
    editing: {
      type: Boolean,
      default: false
    },
    flowTableInfo: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      columns: [
        {
          width: 180,
          dataIndex: "operationName",
          scopedSlots: {
            customRender: "operationName",
            title: "operationNameTitle"
          }
        },
        {
          dataIndex: "operationType",
          width: 180,
          scopedSlots: {
            customRender: "operationType",
            title: "operationTypeTitle"
          }
        },
        {
          width: 300,
          dataIndex: "operationLink",
          scopedSlots: {
            customRender: "operationLink",
            title: "operationLinkTitle"
          }
        },
        {
          width: 180,
          dataIndex: "buttonName",
          scopedSlots: {
            customRender: "buttonName",
            title: "buttonNameTitle"
          }
        },
        {
          width: 300,
          title: this.$t_process("default_parameters"),
          dataIndex: "defaultParam",
          scopedSlots: {
            customRender: "defaultParam"
          }
        },
        {
          width: 180,
          title: this.$t_process("comments"),
          dataIndex: "remark",
          scopedSlots: {
            customRender: "remark"
          }
        }
      ],
      tableData: [],
      typeList: [
        {
          label: this.$t_process("external_url"),
          value: "outURL"
        },
        {
          label: this.$t_process("url"),
          value: "URL"
        },
        {
          label: this.$t_process("pages"),
          value: "page"
        },
        {
          label: this.$t_process("forms"),
          value: "form"
        },
        {
          label: this.$t_process("operations"),
          value: "operate"
        },
        {
          label: this.$t_process("script"),
          value: "script"
        }
      ],
      scriptList: [],
      // 启动、计算、合并、折算、提交、审核、驳回，
      buttonNameList: [
        {
          label: this.$t_process("start_up"),
          value: "start"
        },
        {
          label: this.$t_process("calculation"),
          value: "caculate"
        },
        {
          label: this.$t_process("consolidation"),
          value: "merge"
        },
        {
          label: this.$t_process("fxtrans"),
          value: "converted"
        },
        {
          label: this.$t_process("submit"),
          value: "submit"
        },
        {
          label: this.$t_process("approve"),
          value: "audit"
        },
        {
          label: this.$t_process("reject"),
          value: "turndown"
        }
      ],
      pageList: [],
      pageListMap: "",
      languageVisible: false,
      curText: "",
      currentRecord: {},
      currentKey: "",
      multiLanguages: [],
      paramsVisible: false,
      formVisible: false,
      pavingMenuList: [],
      clickFormIndex: 0,
      clickFormInfo: "",
      clickParamsIndex: 0,
      clickParamsInfo: [],
      fieldEmpty: false,
      operationNameLen: false,
      buttonNameLen: false,
      remarkLen: false,
      variableW: "",
      actionList: [
        {
          actionName: this.$t_common("top"),
          actionMethod: "setTop"
        },
        {
          actionName: this.$t_common("move_up"),
          actionMethod: "moveUp"
        },
        {
          actionName: this.$t_common("move_down"),
          actionMethod: "moveDown"
        },
        {
          actionName: this.$t_common("to_bottom"),
          actionMethod: "setEnd"
        }
      ]
    };
  },
  computed: {
    ...mapState({
      menuList: state => state.common.menuList,
      menuMapObj: state => state.common.menuMapObj,
      pavingData: state => state.processStore.pavingFormList
    })
  },
  watch: {
    menuList: {
      immediate: true,
      handler(nv) {
        const tree = cloneDeep(nv);
        addKeyToTree(tree);
        this.pavingMenuList = pavingTree(tree);
        this.pageList = [...tree];
      }
    },
    editing: {
      immediate: true,
      handler(v) {
        if (v) {
          this.columns.push({
            title: this.$t_common("operation"),
            key: "operation",
            width: 96,
            fixed: "right",
            scopedSlots: {
              customRender: "operation"
            }
          });
          delete this.columns[4].width;
        } else {
          this.columns[4].width = 300;
          !this.columns[this.columns.length - 1].dataIndex &&
            this.columns.pop();
        }
      }
    },
    flowTableInfo: {
      handler(v) {
        this.initData(v);
      }
    }
  },
  created() {
    this.getFormList();
    this.tiRecords();
  },
  mounted() {
    const tableW = document.getElementsByClassName("define-task-flow")[0]
      .clientWidth;
    const width = this.columns.reduce((w, c) => {
      return w + c.width;
    }, 0);
    this.variableW = tableW - width;
  },
  methods: {
    ...mapActions({
      getFormList: "processStore/getFormList"
    }),
    handleLanguages(record, key, value) {
      this.languageVisible = true;
      this.multiLanguages = record[key];
      this.currentRecord = record;
      this.currentKey = key;
      this.curText = value;
    },
    cancelMultilanguage(languageInfo) {
      this.languageVisible = false;
      this.multiLanguages = [];
      this.curText = "";
      languageInfo && (this.currentRecord[this.currentKey] = languageInfo);
      this.currentKey = "";
      this.currentRecord = {};
    },
    tiRecords() {
      taskFlowService("tiRecords").then(res => {
        this.scriptList = res.data.items.map(item => {
          return {
            label: item.scriptRecordName,
            value: item.objectId
          };
        });
      });
    },
    getErrorStyle(record) {
      const bool =
        // 操作名称
        record.operationName &&
        record.operationName.length <= 64 &&
        // 类型
        record.operationType &&
        // 链接
        record.operationLink[record.operationType] &&
        // 按钮名称
        record.buttonName[record.operationType] &&
        record.buttonName[record.operationType].length !== 0 &&
        record.remark.length <= 50;
      if (this.fieldEmpty && !bool) {
        return "invalid-tip-text-task";
      }
      return "";
    },
    inputValueChange(e, index, type, record) {
      if (e.type === "click") {
        this.fieldEmpty = true;
      }
      if (type) {
        const langs = this.setItemLangs(
          type,
          e.target.value,
          record[type + "MultiLanguages"] || []
        );
        langs && Object.assign(record, langs);
      }
      this.addGlobalCb();
      this.setOptColoumnH(index);
    },
    initData(v) {
      const data = cloneDeep(v);
      data.forEach(item => {
        item.key = AppUtils.generateUniqueId();
        const link = item.operationLink;
        item.operationLink = {};
        item.operationLink[item.operationType] = link;
        const btnName = {};
        // if (item.operationType === "operate") {
        //   btnName.operate = item.buttonName.split(",");
        // } else {
        //   btnName[item.operationType] = item.buttonName;
        // }
        btnName[item.operationType] = item.buttonName;
        item.buttonName = btnName;
      });
      this.tableData = data;
    },
    // 输入框失去焦点
    inputBlur(text, type) {
      if (type === "remark") {
        this.remarkLen = text.length > 50;
      } else {
        this[`${type}Len`] = text.length > 64;
      }
    },
    // 根据 value 找 label 类型、按钮 名称
    getValueBykey(type, v) {
      if (!v) return "";
      if (type === "buttonNameList") {
        return this.buttonNameList
          .filter(btn => v.includes(btn.value))
          .map(item => item.label)
          .join("；");
      }
      if (type === "scriptList") {
        return this.scriptList
          .filter(btn => v.includes(btn.value))
          .map(item => item.label)
          .join("；");
      }
      const targetItem = this[type].find(item => item.value === v);
      return targetItem ? targetItem.label : "";
    },
    setItemLangs(type, value = "", lang = []) {
      const langMap = {
        buttonName: {
          buttonNameMultiLanguages: genLanguages(value, lang)
        },
        operationName: {
          operationNameMultiLanguages: genLanguages(value, lang)
        },
        remark: {
          remarkMultiLanguages: genLanguages(value, lang)
        }
      };
      return langMap[type];
    },
    handleAdd() {
      const emptyTable = this.tableData.filter((item, index) => {
        const {
          operationType,
          operationName,
          buttonName,
          operationLink
        } = item;
        if (
          !operationType ||
          !operationName ||
          !buttonName[operationType] ||
          !buttonName[operationType].length ||
          !operationLink[operationType]
        ) {
          this.setOptColoumnH(index);
          return true;
        }
      });
      if (emptyTable.length) {
        this.fieldEmpty = true;
        return;
      }
      this.fieldEmpty = false;
      this.tableData.push({
        key: AppUtils.generateUniqueId(),
        operationName: "",
        ...this.setItemLangs("buttonName"),
        ...this.setItemLangs("operationName"),
        ...this.setItemLangs("remark"),
        operationType: undefined,
        operationLink: {
          outURL: "",
          URL: "",
          page: "",
          form: "",
          operate: "",
          script: ""
        },
        buttonName: {
          outURL: "",
          URL: "",
          page: "",
          form: "",
          operate: "",
          script: ""
        },
        defaultParam: "",
        remark: ""
      });
      this.$emit("addSaveEventCb");
    },
    deleteRecord(index) {
      this.addGlobalCb();
      this.tableData.splice(index, 1);
    },
    // 当 类型 为操作的时候，多选 按钮名称 撑高 行高度 但是 操作列是 固定列，不会随之撑高 导致错行。
    setOptColoumnH(index) {
      // 获取 点击行的 操作 单元格。curOpt
      const fixDom = Array.from(
        document.getElementsByClassName("ant-table-body-inner")
      )[0];
      const fixC =
        fixDom && fixDom.getElementsByClassName("ant-table-fixed")[0];
      const curOpt =
        fixC &&
        fixC.getElementsByClassName("ant-table-tbody")[0].children[index];
      // 设置单元格 高度 等于 行高
      // 获取点击的行 dom 以便 获取行高
      const dom = Array.from(
        document.getElementsByClassName("ant-table-row-level-0")
      )[index];
      this.$nextTick(() => {
        curOpt.style && (curOpt.style.height = dom.clientHeight + "px");
      });
      // 当高度减少的时候 获取到的高度是上一次。所以...
      setTimeout(() => {
        curOpt.style && (curOpt.style.height = dom.clientHeight + "px");
      });
    },
    changeScriptValue(val, record, type, index) {
      this.$set(record[type], record.operationType, val);
      record.buttonName[record.operationType] = this.getValueBykey(
        "scriptList",
        [val]
      );
      this.setOptColoumnH(index);
      this.$emit("addSaveEventCb");
    },
    changeValue(val, record, type, index) {
      if (type === "operationLink") {
        // record[type][record.operationType] = val;
        this.$set(record[type], record.operationType, val);
        record.buttonName[
          record.operationType
        ] = this.getValueBykey("buttonNameList", [val]);
        this.setOptColoumnH(index);
      } else {
        record[type] = val;
      }
      this.$emit("addSaveEventCb");
    },
    clickUrl(url, record) {
      const id = "sdfdf";
      const newAddTabInfo = {
        id,
        router: "outUrl",
        title: record.operationName,
        uri: "",
        routerName: "outUrl",
        params: {
          ruleInfo: {
            key: id
          }
        }
      };
      this.newTabMixin(newAddTabInfo);
    },
    newTabMixin(tabInfo) {
      const { id, router, title, uri, routerName, params } = tabInfo;
      this.newtabMixin({ id, router, title, uri, routerName, params });
    },
    saveDefaultParams(val) {
      const clickParams = this.tableData[this.clickParamsIndex];
      if (
        clickParams.defaultParam &&
        val.join("") !== clickParams.defaultParam.join("")
      ) {
        this.$emit("addSaveEventCb");
      }
      this.tableData[this.clickParamsIndex].defaultParam = val;
    },
    getForm(forms) {
      if (
        this.tableData[this.clickFormIndex].operationLink["form"] !==
        forms.join(";")
      ) {
        this.$emit("addSaveEventCb");
      }
      this.tableData[this.clickFormIndex].operationLink["form"] = forms.join(
        ";"
      );
    },
    // 返显 选择的 form 表单名称
    echoFormName(list) {
      // const res = this.pavingData.filter(item => list.includes(item.key));
      const res = list
        .split(";")
        .map(key => this.pavingData.find(item => item.key === key));
      return res.every(item => !!item)
        ? res.map(item => item.title).join("；")
        : "";
    },
    // 返显 选择的 默认传参
    echoParamsName(list) {
      const res = list
        .map(item => `${item.dimName}：${item.dimMemberName}`)
        .join("；");
      return res;
    },
    clickFormCell(index, info) {
      this.formVisible = true;
      this.clickFormIndex = index;
      this.clickFormInfo = info;
    },
    clickParamsCell(index, info) {
      this.paramsVisible = true;
      this.clickParamsIndex = index;
      this.clickParamsInfo = info;
    },
    addGlobalCb: _debounce(function() {
      this.$emit("addSaveEventCb");
    }, 200),
    disabledMenu(key, index) {
      const firKey = ["moveUp", "setTop"];
      const lastKey = ["moveDown", "setEnd"];
      return (
        (index === 0 && firKey.includes(key)) ||
        (index === this.tableData.length - 1 && lastKey.includes(key))
      );
    },
    actionClick(e, index) {
      const { key } = e;
      this.addGlobalCb();
      switch (key) {
        case "moveUp":
          const upItem = this.tableData.splice(index, 1);
          this.tableData.splice(index - 1, 0, ...upItem);
          break;
        case "moveDown":
          const downItem = this.tableData.splice(index, 1);
          this.tableData.splice(index + 1, 0, ...downItem);
          break;
        case "setTop":
          this.tableData.unshift(...this.tableData.splice(index, 1));
          break;
        case "setEnd":
          this.tableData.push(...this.tableData.splice(index, 1));
          break;
        default:
          break;
      }
    }
  }
};
</script>
<style lang="less">
.operate-menu {
  min-width: 112px;
}
</style>
<style lang="less" scoped>
@import "../../../../../commonLess/common.less";
.placeholder-color {
  color: @yn-auxiliary-color;
}
.task-flow-star::after {
  // content: url("../../../../../image/necessarily.svg");
  content: "*";
  color: @yn-error-color;
  display: inline-block;
  margin-right: @yn-margin-xs;
  font-size: @rem14;
  font-family: SimSun, sans-serif;
  line-height: 1;
}
.define-task-flow {
  .task-flow-title {
    height: @rem22;
    font-size: @rem14;
    color: @yn-heading-color;
  }
  .task-flow-table {
    margin-top: @yn-margin-xl;
    .operate-name {
      display: inline-block;
      width: 11.25rem;
      height: @rem32;
      line-height: @rem32;
      top: -@rem16;
      padding: 0 @rem16;
      position: absolute;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .task-flow-name,
    .task-flow-remark,
    .task-flow-url,
    .task-flow-button-name {
      /deep/.ant-input {
        // border: none;
      }
    }
    /deep/.ant-table-tbody > tr > td {
      padding: 0.25rem;
    }
    .task-flow-type,
    .task-flow-url,
    .task-flow-button-name {
      /deep/.ant-select {
        width: 100%;
        .ant-select-selection {
          // border: none;
        }
      }
      /deep/.yn-select-tree {
        // border: none;
      }
    }
    .ml16 {
      margin-left: @yn-margin-xl;
    }
    .task-flow-name,
    .task-flow-button-name,
    .task-flow-remark,
    .task-flow-type {
      position: relative;
      .error-tips {
        position: absolute;
        display: inline-block;
        width: 100%;
        left: 0;
        padding-left: @rem7;
        top: -@rem22;
        line-height: @rem22;
        height: @rem22;
        background: @yn-error-bg-color;
        color: @yn-error-color;
      }
    }
    .task-flow-url {
      position: relative;
    }
    .task-flow-params {
      // height: @rem32;
      // line-height: @rem32;
      position: relative;
      .default-params,
      .default-params-text {
        width: 100%;
        max-width: 31.25rem;
        min-width: 18.75rem;
        height: 100%;
        cursor: pointer;
        padding-left: @rem7;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .default-params {
        height: 2rem;
        line-height: 2rem;
      }
      .default-params-text {
        display: inline-block;
        height: 2rem;
        line-height: 2rem;
        padding: 0 @rem16;
      }
    }
    .url-form,
    .button-name-text,
    .url-form-text,
    .flow-remark-text {
      min-width: 18.75rem;
      max-width: 31.25rem;
      height: @rem32;
      line-height: @rem32;
      padding-left: @rem7;
      cursor: pointer;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .button-name-text,
    .flow-remark-text {
      width: 11.25rem;
      display: inline-block;
      padding: 0 @rem16;
    }
    .url-form,
    .default-params {
      border: 1px solid @yn-border-color-base;
      background: @yn-body-background;
      border-radius: 0.25rem;
    }
    .default-params:hover,
    .url-form:hover {
      border: 1px solid @yn-primary-color;
      box-shadow: 0 0 0 2px @yn-icon-bg-color;
    }
    .url-form-text {
      display: inline-block;
      padding: 0 @rem16;
    }
    .link-btn {
      width: 100%;
      text-align: left;
    }
  }
  .footer-btn {
    width: 100%;
    height: @rem36;
    background: @yn-component-background;
    border: 1px dashed rgba(225, 229, 235, 1);
    margin-top: @yn-margin-l;
    line-height: @rem36;
    text-align: center;
    cursor: pointer;
    .add-icon {
      // margin-right: @yn-margin-s;
    }
  }
}
.invalid-tip-text-task {
  display: inline-block;
  top: @rem36;
  left: 0;
  height: @rem24;
  line-height: @rem26;
  width: 100%;
  color: @yn-error-color;
  z-index: 100;
}
</style>
