<template>
  <div class="effective-period">
    <div class="period-title">{{ $t_process("valid_periods") }}</div>
    <div v-if="!editing" class="effective-period-detail">
      <span>{{
        `“${$t_common("version")}”、“${$t_common("year")}”、“${$t_common(
          "period"
        )}”，`
      }}</span>
      <yn-button type="text" @click="drawerVisible = !drawerVisible">
        {{ $t_process("click_to_view") }}
      </yn-button>
    </div>
    <yn-form
      v-else
      :form="periodForm"
      :colon="false"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 18 }
      }"
      class="effective-period-tranfer"
    >
      <yn-form-item
        v-for="(item, index) in periodDimList"
        :key="item.objectId"
        class="period-item"
        :label="item.dimName"
      >
        <ShowDimListInput
          v-decorator="[
            item.dimCode,
            {
              rules: [
                {
                  required: true,
                  message: `${$t_common('input_select')}${item.dimName}`
                }
              ],
              initialValue: item.selectedItem
            }
          ]"
          analyticExpName="getExpMemberWithCode"
          :dimInfo="item"
          @change="v => getExparseMembers(v, index)"
        />
      </yn-form-item>
    </yn-form>

    <!-- 查看生效期间 -->
    <yn-drawer
      wrapClassName="period-drawer"
      :title="$t_process('valid_periods_scope')"
      :visible="drawerVisible"
      @close="() => (drawerVisible = !drawerVisible)"
    >
      <div
        class="period-drawer-content"
        :style="{ maxHeight: `${contentHeight}px` }"
      >
        <div
          v-for="(item, index) in periodDimList"
          :key="item.dimId"
          class="period-type"
        >
          <p class="period-type-title">{{ item.dimName }}</p>
          <p
            v-for="member in item.selectedItem"
            :key="member.objectId"
            class="period-type-name"
          >
            {{ member.dimMemberName }}
          </p>
          <yn-divider v-show="index !== periodDimList.length - 1" />
        </div>
      </div>
    </yn-drawer>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-button/";

import ShowDimListInput from "../../../../../components/hoc/ShowDimListInput.vue";
import cloneDeep from "lodash/cloneDeep";
import taskFlowTempService from "@/services/taskFlowTemp";
import DIM_INFO from "@/constant/dimMapping";
import {
  pavingTree,
  addKeyToData,
  composeFuns,
  enchoTransferData
} from "@/utils/taskFlowTemp.js";

export default {
  components: { ShowDimListInput },
  props: {
    editing: {
      type: Boolean,
      default: false
    },
    effectivePeriod: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      periodForm: this.$form.createForm(this, "periodForm"),
      periodDimList: [
        {
          dimName: this.$t_common("version"),
          dimCode: "Version",
          objectId: DIM_INFO.Version,
          dimId: DIM_INFO.Version,
          selectedItem: []
        },
        {
          dimName: this.$t_common("year"),
          dimCode: "Year",
          objectId: DIM_INFO.Year,
          dimId: DIM_INFO.Year,
          selectedItem: []
        },
        {
          dimName: this.$t_common("period"),
          dimCode: "Period",
          objectId: DIM_INFO.Period,
          dimId: DIM_INFO.Period,
          selectedItem: []
        }
      ],
      drawerVisible: false,
      contentHeight: 0,
      dimMemberMap: {}
    };
  },
  watch: {
    effectivePeriod: {
      deep: true,
      handler(v) {
        this.initData(v);
      }
    },
    drawerVisible: {
      handler(v) {
        if (v) {
          this.getPageHeight();
        }
      }
    }
  },
  methods: {
    async initData(v) {
      await this.getAllMembers(["Version", "Year", "Period"]);
      this.periodDimList.forEach(async dim => {
        const type = dim.dimCode.toLowerCase();
        if (v[type].length) {
          dim.selectedItem = enchoTransferData(v[type]);
        } else {
          dim.selectedItem = this.dimMemberMap[dim.dimCode];
        }
      });
    },
    getPageHeight() {
      const pageH = window.innerHeight - 93;
      this.contentHeight = pageH;
    },
    // 获取版本/年/期间所有成员
    async getAllMembers(dimCodeList) {
      if (Object.keys(this.dimMemberMap).length) return;
      const pList = dimCodeList.map(item =>
        taskFlowTempService("getDimMembersTree", item)
      );
      await Promise.allSettled(pList).then(res => {
        for (let i = 0; i < res.length; i++) {
          const data = res[i].value ? res[i].value.data : [];
          const result = composeFuns(pavingTree, addKeyToData)(data);
          this.dimMemberMap[this.periodDimList[i].dimCode] = [...result];
        }
      });
    },
    getExparseMembers(info, i) {
      const { selectedItem } = info;
      const copyDimList = cloneDeep(this.periodDimList);
      copyDimList[i].selectedItem = selectedItem;
      this.$set(this, "periodDimList", copyDimList);
      this.$emit("addSaveEventCb");
    }
  }
};
</script>

<style lang="less">
.period-drawer {
  .ant-drawer-body {
    padding: @yn-padding-xxxl 0 @yn-padding-xxxl @yn-padding-xxxl;
  }
}
</style>
<style lang="less" scoped>
.effective-period {
  .period-title,
  .effective-period-detail {
    height: @rem22;
    line-height: @rem22;
    font-weight: 400;
    font-size: @rem14;
  }
  .period-title {
    color: @yn-heading-color;
  }
  .effective-period-detail {
    margin-top: @yn-margin-xl;
    color: @yn-text-color-secondary;
    margin-bottom: @yn-margin-xxxl;
  }
  .effective-period-tranfer {
    display: flex;
    flex-wrap: wrap;
    margin-top: @yn-margin-xl;
    .period-item {
      width: 50%;
      /deep/.ant-form-item-control {
        line-height: normal;
      }
      /deep/.ant-form-item-no-colon {
        color: @yn-text-color-secondary;
      }
      .dim-list-input {
        width: 100%;
      }
    }
  }
}

.period-drawer-content {
  overflow: scroll;
}
.period-type {
  .period-type-title {
    font-size: @rem14;
    color: @yn-label-color;
  }
  .period-type-name {
    font-size: @rem14;
    color: @yn-text-color;
  }
}
</style>
