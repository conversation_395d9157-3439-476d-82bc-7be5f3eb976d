<template>
  <div class="task-flow-right">
    <empty v-if="!selectTreeNode">
      <span slot="desc">{{ $t_process("start_new_task_flow") }}</span>
      <yn-button slot="btn" type="primary" @click="addTemplate">
        {{ $t_common("new_template") }}
      </yn-button>
    </empty>
    <yn-spin v-else :spinning="spinning">
      <div class="right-content">
        <div class="content-header">
          <div v-if="!editingName" class="header-detail">
            <span class="header-title">{{ templateName }}</span>
            <yn-tooltip
              v-show="editingTemplate"
              :title="$t_common('edit_name')"
            >
              <yn-icon-button
                type="edit"
                class="edit-pan"
                @click="editHeader('name')"
              />
            </yn-tooltip>
          </div>
          <yn-input
            v-else
            id="nameInput"
            v-model="templateName"
            class="edit-name"
            :allowClear="false"
            @change="handlerChangeName"
            @blur="closeInput('name')"
          />
          <div class="header-desc">
            <div v-if="!editingDesc" class="desc-left">
              <span class="title-label label-desc">{{
                templateDesc || $t_common("no_description")
              }}</span>
              <yn-tooltip
                v-show="editingTemplate"
                :title="$t_common('edit_description')"
              >
                <yn-icon-button
                  type="edit"
                  class="edit-pan"
                  @click="editHeader('desc')"
                />
              </yn-tooltip>
            </div>
            <yn-input
              v-else
              id="descInput"
              v-model="templateDesc"
              :placeholder="$t_common('input_message')"
              class="edit-desc"
              :allowClear="false"
              @blur="closeInput('desc')"
              @change="addSaveEventCb"
            />
            <yn-divider v-show="!editingDesc && updateBy" type="vertical" />
            <span
              v-show="updateBy"
              :class="[
                'title-label',
                'update-time',
                editingDesc ? 'text-right' : ''
              ]"
            >
              {{ updateBy }} {{ updateTime }} {{ $t_common("modify") }}
            </span>
          </div>
          <yn-button
            v-show="!editingTemplate"
            type="primary"
            class="header-btn-edit"
            @click="() => (editingTemplate = true)"
          >
            {{ $t_common("edit") }}
          </yn-button>
        </div>
        <yn-divider class="header-divider" />
        <div
          :class="[
            'content-main',
            editingTemplate ? 'edit-template' : 'view-template'
          ]"
        >
          <!-- 生效期间 -->
          <effective-period
            ref="periodRef"
            :editing="editingTemplate"
            :effectivePeriod="effectivePeriod"
            @addSaveEventCb="addSaveEventCb"
          />
          <!-- 定义任务流 -->
          <define-task-flow
            ref="taskFlowRef"
            :editing="editingTemplate"
            :flowTableInfo="flowTableInfo"
            @addSaveEventCb="addSaveEventCb"
          />
          <!-- 生效公司 -->
          <effective-company
            ref="companyRef"
            :editing="editingTemplate"
            :effectiveCompany="effectiveCompany"
            :showEffectiveTips="showEffectiveTips"
            @addSaveEventCb="addSaveEventCb"
            @closeTips="showEffectiveTips = false"
          />
        </div>
        <div v-if="editingTemplate" class="content-footer">
          <yn-button class="footer-btn" @click="onUpdate">
            {{ $t_common("cancel") }}
          </yn-button>
          <yn-button
            class="footer-btn"
            type="primary"
            :loading="btnLoading"
            @mousedown="onSave"
          >
            {{ $t_common("save") }}
          </yn-button>
        </div>
      </div>
    </yn-spin>
  </div>
</template>

<script>
import Empty from "../../../ruleConfiguration/empty.vue";
import EffectivePeriod from "./component/effectivePeriod.vue";
import DefineTaskFlow from "./component/defineTaskFlow.vue";
import EffectiveCompany from "./component/effectiveCompany.vue";

import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-divider/";
import { confirm } from "../../../journal/journalList/exconfirm";
import { syncLanguages, copyLanguages } from "@/utils/common";
import "yn-p1/libs/components/yn-icon-button/";
import moment from "moment";
import { mapState } from "vuex";

import taskFlowServeice from "@/services/taskFlowTemp";

import cloneDeep from "lodash/cloneDeep";
import UiUtils from "yn-p1/libs/utils/UiUtils";

export default {
  name: "FlowRight",
  components: {
    Empty,
    EffectivePeriod,
    DefineTaskFlow,
    EffectiveCompany
  },
  props: {
    selectTreeNode: {
      type: Object,
      default: null
    },
    updateMessage: {
      type: Object,
      default: () => ({})
    },
    refreshTreeData: {
      type: Function,
      default: () => () => {}
    }
  },
  data() {
    return {
      spinning: false,
      editingTemplate: false,
      multiLanguages: [],
      btnLoading: false,
      editingName: false,
      saveTips: this.$t_process("do_you_save_tmp"),
      templateName: this.$t_common("template_name"),
      cloneTempName: "",
      templateDesc: this.$t_process("tmp_des"),
      cloneTempDesc: "",
      editingDesc: false,
      updateBy: "",
      updateTime: "",
      flowTableInfo: [],
      effectiveCompany: {},
      showEffectiveTips: false,
      effectivePeriod: {},
      pId: "",
      tempInfo: {},
      directSave: false // 是否直接保存。
    };
  },
  computed: {
    ...mapState("common", {
      currentUserInfo: state => state.currentUserInfo
    })
  },
  watch: {
    selectTreeNode: {
      immediate: true,
      deep: true,
      async handler(newNode, oldNode) {
        if (newNode) {
          const { key, flag, copyKey } = newNode;
          if (oldNode && newNode.key === oldNode.key) return;
          const k = flag ? copyKey : key;
          if (flag) {
            this.editingTemplate = true;
            this.addSaveEventCb();
          }
          await this.getTemplateBase(k);
          this.pId && this.getTemplateInfo(k);
        }
      }
    },
    updateMessage: {
      deep: true,
      immediate: true,
      handler(nv) {
        if (nv && Object.keys(nv).length) {
          const { name, remark, isAdd } = nv;
          this.updateBy = this.currentUserInfo.userName;
          this.updateTime = moment().format("YYYY-MM-DD HH:mm:ss");
          this.templateDesc = remark;
          this.templateName = name;
          if (isAdd) {
            this.editingTemplate = true;
          }
        }
      }
    }
  },
  methods: {
    // 获取模板基础信息
    async getTemplateBase(key) {
      const { flag, name: copyName } = this.selectTreeNode;
      await taskFlowServeice("getTemplateBase", key).then(res => {
        const { data, success, message } = res.data;
        const { desc, name, pId, multiLanguages } = data;
        if (success) {
          this.templateName = flag ? copyName : name;
          const index = this.templateName.match(/(\d+)&/)
            ? this.templateName.match(/(\d+)&/)[1]
            : 1;
          this.multiLanguages = flag
            ? copyLanguages(multiLanguages, index)
            : multiLanguages;
          this.pId = pId;
          this.cloneTempName = cloneDeep(name);
          this.templateDesc = desc;
          this.cloneTempDesc = cloneDeep(desc);
        } else {
          UiUtils.errorMessage(message);
        }
      });
    },
    // 获取模板信息
    getTemplateInfo(key) {
      this.spinning = true;
      taskFlowServeice("getTemplateInfo", key)
        .then(res => {
          let tempInfo;
          if (res.data.data) {
            this.updateBy = res.data.data.updateBy;
            this.updateTime = res.data.data.updateDate;
            tempInfo = res.data.data;
            const { flag, name: copyName } = this.selectTreeNode;
            if (flag) {
              tempInfo.templateName = copyName;
            }
          } else {
            tempInfo = {
              effectivePeriod: {
                version: [],
                year: [],
                period: []
              },
              taskFlowTemplateDefineResponse: [],
              effectiveCompany: {
                scope: [],
                entity: []
              }
            };
          }
          this.tempInfo = tempInfo;
          this.initTempltaInfo(tempInfo);
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    initTempltaInfo(data) {
      const {
        effectiveCompany,
        effectivePeriod,
        taskFlowTemplateDefineResponse,
        templateName,
        templateDesc
      } = data;
      this.templateName = templateName;
      this.templateDesc = templateDesc;
      this.flowTableInfo = taskFlowTemplateDefineResponse;
      this.effectiveCompany = effectiveCompany;
      this.effectivePeriod = effectivePeriod;
    },
    addTemplate() {
      this.$emit("addTemplate");
    },
    onUpdate() {
      this.savePromptMixin().then(() => {
        this.editingTemplate = false;
      });
    },
    onSave() {
      if (document.activeElement.tagName === "INPUT") {
        // 没失去焦点直接点保存
        this.directSave = true;
      }
      if (this.templateName.length > 64 || this.templateDesc.length > 50) {
        this.directSave = false;
        return;
      }
      const [
        version,
        year,
        period
      ] = this.$refs.periodRef.periodDimList.map(selectedList =>
        selectedList.selectedItem.map(item => item.dimMemberId)
      );
      const {
        entityCompany,
        scopeCompany,
        memberExp
      } = this.$refs.companyRef.getDataInfo();
      const taskFlowTemplateDefine = cloneDeep(
        this.$refs.taskFlowRef.tableData
      ).map(item => {
        item.operationLink = item.operationLink
          ? item.operationLink[item.operationType]
          : "";
        const defaultP =
          item.defaultParam &&
          item.defaultParam.map(p => ({
            [p.dimCode]: p.dimMemberId
          }));
        item.defaultParam = defaultP;
        item.buttonName = Array.isArray(item.buttonName[item.operationType])
          ? item.buttonName[item.operationType].join(",")
          : item.buttonName[item.operationType];
        return item;
      });
      const list = [
        "operationName",
        "operationType",
        "buttonName",
        "operationLink"
      ];
      const emptyItem = taskFlowTemplateDefine.filter(temp =>
        list.some(item => !temp[item])
      );
      if (emptyItem.length) {
        this.$refs.taskFlowRef.handleAdd();
        return;
      }
      if (entityCompany.length + scopeCompany.length === 0) {
        this.showEffectiveTips = true;
        const templateDom = document.getElementsByClassName("edit-template")[0];
        const y =
          templateDom && templateDom.scrollHeight - templateDom.clientHeight;
        templateDom.scrollTop = y;
        return;
      }
      const params = {
        objectId: this.selectTreeNode.key,
        name: this.templateName,
        multiLanguages: syncLanguages(this.templateName, this.multiLanguages),
        desc: this.templateDesc,
        version,
        year,
        period,
        memberExp,
        taskFlowTemplateDefine
      };
      if (this.selectTreeNode.flag) {
        params.pId = this.selectTreeNode.parentId;
        params.sourceId = this.selectTreeNode.sourceId;
        delete params.objectId;
        params.taskFlowTemplateDefine.forEach(item => {
          delete item.objectId;
        });
      }
      this.btnLoading = true;
      return taskFlowServeice("saveTemplateDetail", params)
        .then(async res => {
          if (res.data.success && res.data.data) {
            if (typeof res.data.data !== "string") {
              this.saveFailed(res.data.data);
              return Promise.reject(this.$t_common("save_failed"));
            }

            UiUtils.successMessage(this.$t_common("save_success"));
            // 保存成功之后没有重新获取基础信息接口 所以 需要 重新copy name、desc
            this.cloneTempName = cloneDeep(params.name);
            this.cloneTempDesc = cloneDeep(params.desc);
            this.editingTemplate = false;
            // 刷新树 数据
            await this.refreshTreeData(res.data.data);
            this.clearCommonSaveEventsMixin();
            this.getTemplateBase(res.data.data);
            this.getTemplateInfo(res.data.data);
            return res.data.data;
          }
        })
        .finally(() => {
          this.btnLoading = false;
          this.directSave = false;
        });
    },
    saveFailed(info) {
      this._confirmModal = confirm({
        title: this.$t_common("save_failed"),
        type: "error",
        content: h => this.renderDetail(info),
        button: btn => (
          <yn-button
            type="primary"
            onClick={() => {
              this._confirmModal.destroy();
            }}
          >
            {this.$t_common("maintenance_oktext")}
          </yn-button>
        )
      });
      return this._confirmModal;
    },
    renderDetail(info) {
      const { tips, list } = info;
      return (
        <div class="info-content">
          <span class="info-content-title">{tips}</span>
          <ul>
            {list.map(item => (
              <li class="info-content-detail">{item}</li>
            ))}
          </ul>
        </div>
      );
    },
    handlerChangeName() {
      this.addSaveEventCb();
    },
    closeInput(type) {
      const w = type.charAt(0).toUpperCase() + type.slice(1);
      this[`editing${w}`] = false;
      this.addSaveEventCb();
      // 名称重名校验
      if (type === "name") {
        if (!this.templateName) {
          this.templateName = this.cloneTempName;
          UiUtils.errorMessage(this.$t_process("process_template_name_empty"));
          return;
        }
        if (this.templateName.length > 64) {
          UiUtils.errorMessage(this.$t_process("field_length_exceeds_64"));
          this.templateName = this.cloneTempName;
          return;
        }
        if (this.directSave) return;
        taskFlowServeice("checkDuplicateName", {
          objectId: this.selectTreeNode.key,
          name: this.templateName,
          multiLanguages: syncLanguages(this.templateName, this.multiLanguages)
        }).then(res => {
          if (res.data.data) {
            UiUtils.errorMessage(res.data.data);
            this.templateName = this.cloneTempName;
          }
        });
      }
      if (type === "desc" && this.templateDesc.length > 50) {
        UiUtils.errorMessage(
          this.$t_process$t_process("field_length_exceeds_50")
        );
        this.templateDesc = this.cloneTempDesc;
        return;
      }
    },
    editHeader(type) {
      const w = type.charAt(0).toUpperCase() + type.slice(1);
      this[`editing${w}`] = true;
      this.$nextTick(() => {
        const editInput = document.getElementById(`${type}Input`);
        editInput.focus();
      });
    },
    notSaveEvent() {
      if (this.selectTreeNode.flag) {
        this.$emit("notSave", { dataRef: this.selectTreeNode });
        return;
      } // 复制不需要
      this.editingTemplate = false;
      this.initTempltaInfo(cloneDeep(this.tempInfo));
    },
    addSaveEventCb() {
      this.addCallBackFnMixin(this.onSave, this.notSaveEvent, this.saveTips);
    }
  }
};
</script>

<style lang="less" scoped>
.info-content {
  margin-bottom: 3.125rem;
  .info-content-title {
    color: @yn-text-color;
  }
  .info-content-detail {
    color: @yn-text-color-secondary;
  }
}
.task-flow-right {
  width: 100%;
  height: 100%;
  .right-content {
    width: 100%;
    height: 100%;
    background: @yn-component-background;
    .content-header {
      width: 100%;
      padding: 1rem 1rem;
      height: 5.5rem;
      position: relative;
      .header-detail,
      .edit-name {
        margin-top: -@yn-margin-xs;
      }
      .edit-name,
      .edit-desc {
        border: none;
        caret-color: @yn-primary-color;
        font-weight: 500;
        padding: 0;
        &:focus {
          box-shadow: none;
        }
      }
      .edit-desc {
        font-weight: 400;
        color: @yn-label-color;
      }
      .header-detail {
        width: calc(100% - 5rem);
        display: flex;
        align-items: center;
        .header-title {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: inline-block;
        }
      }
      .header-title {
        display: inline-block;
        height: @rem32;
        line-height: @rem32;
        font-size: @rem14;
        color: @yn-text-color;
        font-weight: 500;
      }
      .header-desc {
        display: flex;
        align-items: center;
        width: 100%;
        .desc-left {
          display: flex;
          align-items: center;
          max-width: calc(100% - 17.5rem);
        }
        .title-label {
          color: @yn-label-color;
          font-size: @rem14;
          font-family: PingFangSC-Regular;
        }
        .label-desc {
          display: inline-block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          height: @rem32;
          line-height: @rem32;
        }
        .update-time {
          min-width: 23.75rem;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: inline-block;
        }
        .text-right {
          text-align: right;
        }
      }
      .header-btn-edit {
        position: absolute;
        right: 1rem;
        top: @rem12;
        min-width: 5rem;
      }
    }
    .header-divider {
      width: calc(100% - 2rem);
      min-width: calc(100% - 2rem);
      margin: -0.25rem 1rem 0;
    }
    .content-main {
      width: 100%;
      overflow-y: scroll;
      background: @yn-component-background;
      padding: 0.5rem 1rem 0;
      &::-webkit-scrollbar {
        width: 0.3125rem;
      }
    }
    .view-template {
      height: calc(100% - 6.25rem);
    }
    .edit-template {
      height: calc(100% - 10rem);
    }
    .content-footer {
      height: 3.25rem;
      margin-top: @yn-margin-s;
      background: @yn-component-background;
      text-align: right;
      line-height: 3.25rem;
      .footer-btn {
        min-width: 5rem;
        &:first-child {
          margin-right: @yn-margin-s;
        }
        &:last-child {
          margin-right: @yn-margin-xxxxl;
        }
      }
    }
  }
}
</style>
