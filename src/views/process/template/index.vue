<template>
  <div class="task-flow cs-container">
    <div class="task-flow-title cs-header-single">
      {{ $t_process("task_flow_template") }}
    </div>
    <yn-lc-layout iconPosition="center" class="container-layout">
      <div slot="left" class="container-left">
        <div v-if="searching" class="left-header-menu">
          <yn-input-search
            v-model="searchValue"
            class="menu-simple-search"
            :placeholder="$t_common('input_message')"
            @search="onSearch"
          />
        </div>
        <div v-else class="left-header-menu">
          <yn-dropdown>
            <yn-menu slot="overlay" @click="handleNodeOperate">
              <yn-menu-item key="addTemplate">
                <span>{{ $t_common("new_template") }}</span>
              </yn-menu-item>
              <yn-menu-item key="addGroup">
                <span>{{ $t_common("new_group") }}</span>
              </yn-menu-item>
            </yn-menu>
            <yn-button type="primary" size="small" class="header-menu-btn">
              {{ $t_common("add") }}
              <yn-icon type="down" />
            </yn-button>
          </yn-dropdown>
          <div class="header-icon">
            <svg-icon
              type="icon-shuaxin"
              :title="$t_common('refresh')"
              @onClick="refreshTree"
            />
            <svg-icon
              type="icon-search1"
              :title="$t_common('search_key')"
              @onClick="showSearch"
            />
          </div>
        </div>
        <yn-spin :spinning="spinning" class="left-header-tree">
          <yn-tree-menu
            class="left-tree"
            :treePanelSkeleton="treePanelSkeleton"
            :treeConfig="treeConfig"
            :initConfig="initConfig"
            @expand="onExpand"
            @select="onSelect"
            @dragenter="onDragEnter"
            @drop="onDrop"
          >
            <span slot="tree.custom" slot-scope="obj">
              <svg-icon
                v-show="!obj.isLeaf"
                type="icon-wenjianjia"
                class="folder-icon"
                :isIconBtn="false"
              />
              <span
                v-tooltip="{ visibleOnOverflow: true, title: obj.title }"
                :class="[
                  'node-title',
                  obj.isLeaf ? 'node-title-leaf' : 'ml22 node-title-non-leaf'
                ]"
              >
                <!-- {{ obj.title }}
              <template v-if="!obj.isLeaf">
                ({{ obj.children.length }})
              </template> -->
                <!-- <template v-if="obj.title.indexOf(searchValue) > -1">
                  {{ obj.title.substr(0, obj.title.indexOf(searchValue)) }}
                  <strong class="search-value">
                    {{ searchValue }}
                  </strong>
                  {{
                    obj.title.substr(
                      obj.title.indexOf(searchValue) + searchValue.length
                    )
                  }}
                </template> -->
                <template>
                  {{ obj.title }}
                </template>
                <template v-if="!obj.isLeaf">
                  ({{ obj.children ? obj.children.length : 0 }})
                </template>
              </span>
              <span
                class="node-operate-icon"
                style="width:1.625rem;height:1.625rem"
                @click.stop
              >
                <yn-dropdown
                  placement="bottomRight"
                  overlayClassName="tree-menu-dropdown"
                  :trigger="['click']"
                >
                  <!-- <svg-icon
                    class="anticon-more small-icon"
                    type="icon-more"
                    :isIconBtn="true"
                  /> -->
                  <yn-icon-button
                    type="more"
                    class="anticon-more more-btn"
                    size="small"
                    @click.stop
                  />
                  <yn-menu slot="overlay">
                    <yn-menu-item
                      v-for="item in obj.isLeaf
                        ? leafNodeOperate
                        : nonLeafNodeOperate"
                      :key="item.key"
                      :disabled="
                        item.key === 'deleteGroup' && obj.scopedSlots.systemFlag
                      "
                      class="nodeOperate"
                      @click="handleNodeOperate($event, obj)"
                    >
                      {{ item.label }}
                    </yn-menu-item>
                  </yn-menu>
                </yn-dropdown>
              </span>
            </span>
          </yn-tree-menu>
        </yn-spin>
      </div>
      <flow-right
        slot="center"
        :selectTreeNode="selectTreeNode"
        :updateMessage="updateMessage"
        :refreshTreeData="refreshTreeData"
        @addTemplate="addTemplate"
        @notSave="deleteTemplate"
      />
    </yn-lc-layout>
    <group-modal
      v-if="groupVisible"
      :groupObj="groupObj"
      :visible.sync="groupVisible"
      @onSave="onSaveEvent"
    />
    <template-modal
      v-if="templateVisible"
      :templateObj="templateObj"
      :visible.sync="templateVisible"
      @onSave="onSaveEvent"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-lc-layout/";
import "yn-p1/libs/components/yn-tree-menu/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-spin/";

import FlowRight from "./taskFlowRight";
import groupModal from "./taskFlowRight/component/groupModal.vue";
import templateModal from "./taskFlowRight/component/templateModal.vue";

import taskFlowServeice from "@/services/taskFlowTemp";
import { handleLefTree } from "@/utils/taskFlowTemp.js";

import UiUtils from "yn-p1/libs/utils/UiUtils";
import cloneDeep from "lodash/cloneDeep";
export default {
  name: "TaskFlow",
  components: { FlowRight, groupModal, templateModal },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      spinning: false,
      groupVisible: false,
      templateVisible: false,
      groupObj: {},
      templateObj: {},
      searching: false,
      searchValue: "",
      selectTreeNode: null,
      treePanelSkeleton: {
        loading: false
      },
      initConfig: {
        show: false,
        tipText: this.$t_process("no_template")
      },
      treeConfig: {
        selectedKeys: [],
        expandedKeys: [],
        draggable: true,
        treeData: []
      },
      nodeNameList: [], // 用于复制模板取名
      nonLeafNodeOperate: [
        {
          key: "addGroup",
          label: this.$t_common("new_group")
        },
        {
          key: "editGroup",
          label: this.$t_common("edit_group")
        },
        {
          key: "deleteGroup",
          label: this.$t_common("delete_group")
        },
        {
          key: "addTemplate",
          label: this.$t_common("new_template")
        }
      ],
      leafNodeOperate: [
        {
          key: "editTemplate",
          label: this.$t_process("edit_template")
        },
        {
          key: "copyTemplate",
          label: this.$t_process("copy_template")
        },
        {
          key: "deleteTemplate",
          label: this.$t_process("delete_template")
        }
      ],
      originData: [],
      updateMessage: {} // 新增模板人/时间
    };
  },
  created() {
    this.getTemplateTree();
  },
  methods: {
    memberReferenceJumpEvent() {
      const defaultParams = this.selfTab
        ? this.params.params
        : this.getTabParamsMixin() || {};
      const { id: selectedId } = defaultParams.MRJ || {};
      const treeNode = this.findTreeNodeById(selectedId);
      if (selectedId) {
        if (treeNode) {
          this.treeConfig.selectedKeys = [selectedId];
          this.treeConfig.expandedKeys.push(treeNode.parentId);
          this.selectTreeNode = treeNode;
        } else {
          this.chooseFirst();
          UiUtils.errorMessage(this.$t_process("no_task_template"));
        }
      } else {
        this.chooseFirst();
      }
    },
    findTreeNodeById(id) {
      const allTemplate = [];
      const loop = list => {
        list.forEach(item => {
          if (item.isLeaf) {
            allTemplate.push(item);
          }
          if (item.children && item.children.length) {
            loop(item.children);
          }
        });
      };
      loop(this.originData);
      const [treeNode] = allTemplate.filter(item => item.id === id);
      return treeNode;
    },
    // 获取模板（左侧树）
    getTemplateTree(cb) {
      this.spinning = true;
      taskFlowServeice("getTemplateTree").then(res => {
        this.spinning = false;
        if (res.data.data && res.data.data.length) {
          this.initConfig.show = false;
          handleLefTree(res.data.data);
          this.treeConfig.treeData = res.data.data || [];
          this.originData = cloneDeep(this.treeConfig.treeData);
          this.nodeNameList = this.getNodeNameList([...res.data.data], "name");
          if (cb) {
            cb();
          } else {
            this.memberReferenceJumpEvent();
          }
        } else {
          this.initConfig.show = true;
        }
      });
    },
    refreshTree() {
      this.savePromptMixin().then(() => {
        this.getTemplateTree();
      });
    },
    closeModal() {},
    onSaveEvent(type, info) {
      this.getTemplateTree(() => {
        if (type === "template") {
          // 新增完模板之后选中
          const { templateId, pId, createBy, createDate, name, remark } = info;
          // node._props.dataRef
          const nodeInfo = {
            pId,
            node: {
              dataRef: {
                isLeaf: true,
                key: templateId,
                parentId: pId
              }
            }
          };
          this.updateMessage = {
            createBy,
            name, // 名称  右侧同步修改
            remark, // 备注
            isAdd: !this.templateObj.title, // 新增、右侧直接进入编辑态
            createDate
          };
          this.onSelect([templateId], nodeInfo);
        }
      });
    },
    showSearch() {
      this.searching = !this.searching;
      this.$nextTick(() => {
        const leftSimpleSearch = document.getElementsByClassName(
          "menu-simple-search"
        )[0];
        const searchDom =
          leftSimpleSearch &&
          leftSimpleSearch.getElementsByClassName("ant-input")[0];
        if (searchDom) {
          searchDom.focus();
          searchDom.onblur = () => {
            if (!this.searchValue) {
              this.searching = false;
            }
          };
        }
      });
    },
    onSearch(value) {
      if (!value) {
        this.searching = false;
        this.treeConfig.treeData = this.originData;
        return;
      }
      const allTemplate = [];
      const loop = list => {
        list.forEach(item => {
          if (item.isLeaf) {
            allTemplate.push(item);
          }
          if (item.children && item.children.length) {
            loop(item.children);
          }
        });
      };
      loop(this.originData);
      this.treeConfig.treeData = allTemplate.filter(
        item => item.name.indexOf(value) > -1
      );
    },
    // 默认选中第一个节点
    chooseFirst() {
      const { treeData, expandedKeys } = this.treeConfig;
      for (let i = 0; i < treeData.length; i++) {
        const curItem = treeData[i];
        if (i === treeData.length - 1 && !curItem.children) {
          this.selectTreeNode = null;
        }
        if (curItem.children && curItem.children.length) {
          this.selectTreeNode = curItem.children[0];
          this.treeConfig.expandedKeys = [...expandedKeys, curItem.key];
          this.treeConfig.selectedKeys = [curItem.children[0].key];
          break;
        }
      }
    },
    deleteConfirmModal(info) {
      const { title, cb, content } = info;
      UiUtils.confirm({
        icon: () =>
          this.$createElement("yn-icon", {
            props: {
              type: "exclamation-circle",
              theme: "filled"
            }
          }),
        title,
        content: () => <span class="modal-content">{content}</span>,
        onOk: cb
      });
    },
    // 结点操作
    handleNodeOperate(event, item) {
      const { key } = event;
      this[key] && this[key](item);
    },
    addGroup() {
      this.groupVisible = true;
      this.groupObj = {};
    },
    editGroup(item) {
      this.groupVisible = true;
      const {
        dataRef: { title, key }
      } = item;
      this.groupObj.title = title;
      this.groupObj.objectId = key;
    },
    deleteGroup(item) {
      const {
        dataRef: { key, title }
      } = item;
      this.deleteConfirmModal({
        title: this.$t_process("sure_delete_file", [title]),
        content: this.$t_process("folder_deletion"),
        cb: () =>
          taskFlowServeice("deleteGroup", key).then(() => {
            UiUtils.successMessage(this.$t_common("delete_success"));
            this.getTemplateTree();
          })
      });
    },
    addTemplate(item) {
      this.templateVisible = true;
      this.templateObj = {};
      if (item) {
        // 树上新增
        const {
          dataRef: { key }
        } = item;
        this.templateObj.pId = key;
      } else {
        this.templateObj.pId = this.treeConfig.treeData.filter(
          item => item.scopedSlots.systemFlag
        )[0].key;
      }
    },
    editTemplate(item) {
      this.savePromptMixin().then(res => {
        if (~item.key.indexOf("copy")) {
          UiUtils.warningMessage(this.$t_process("save_before_edit"));
          return;
        }
        this.templateVisible = true;
        const {
          dataRef: { title, parentId, key }
        } = item;
        this.templateObj.title = title;
        this.templateObj.pId = parentId;
        this.templateObj.key = key;
      });
    },
    // 获取 tree 中 filed 值。
    getNodeNameList(tree, field) {
      if (!Array.isArray(tree)) return [];
      const ans = [];
      const stack = [...tree];
      while (stack.length > 0) {
        const cur = stack.shift();
        ans.push(cur[field]);
        if (cur.children && cur.children.length) {
          stack.push(...cur.children);
        }
      }
      return ans;
    },
    copyTemplate(item) {
      this.savePromptMixin().then(res => {
        const {
          dataRef,
          dataRef: { key, parentId, name, flag, copyKey }
        } = item;
        let count = 1;
        while (
          ~this.nodeNameList.indexOf(
            `${this.$t_process("duplicate_copy", [name])}${count}`
          )
        ) {
          count++;
        }
        if (
          `${this.$t_process("duplicate_copy", [name])}${count}`.length > 64
        ) {
          UiUtils.errorMessage(this.$t_process("field_length_exceeds_64"));
          return;
        }
        const copyInfo = {
          ...dataRef,
          key: `${key}-copy`,
          copyKey: flag ? copyKey : key,
          sourceId: item.flag ? item.sourceId : key,
          name: `${this.$t_process("duplicate_copy", [name])}${count}`,
          title: `${this.$t_process("duplicate_copy", [name])}${count}`,
          flag: true // 代表复制出来的节点
        };
        let index = 0;
        copyInfo.position = index;
        // originData
        const parent = this.originData.find(item => item.key === parentId);
        if (!parent || parent.children.length === 0) return;
        for (let i = 0; i < parent.children.length; i++) {
          if (parent.children[i].key === key) {
            index = i + 1;
            break;
          }
        }
        parent.children.splice(index, 0, copyInfo);
        if (this.searching) {
          this.onSearch(this.searchValue);
        } else {
          this.treeConfig.treeData = this.originData;
        }
        this.treeConfig.selectedKeys = [copyInfo.key];
        this.selectTreeNode = copyInfo;
      });
    },
    deleteTemplate(item) {
      const {
        dataRef: { key, title, parentId, flag }
      } = item;
      // 删除复制模板
      if (flag) {
        let index = -1;
        const parent = this.originData.find(item => item.key === parentId);
        for (let i = 0; i < parent.children.length; i++) {
          if (parent.children[i].key === key) {
            index = i;
            break;
          }
        }
        parent.children.splice(index, 1);
        if (this.searching) {
          this.onSearch(this.searchValue);
        } else {
          this.treeConfig.treeData = this.originData;
        }
        this.chooseFirst();
        this.clearCommonSaveEventsMixin();
        return;
      }
      // 删除已经入库的模板
      this.savePromptMixin().then(() => {
        this.deleteConfirmModal({
          title: this.$t_process("sure_delete_tmp", [title]),
          content: this.$t_process("template_deletion"),
          cb: () =>
            taskFlowServeice("deleteTemplate", key).then(res => {
              UiUtils.successMessage(this.$t_common("delete_success"));
              this.searching = false;
              this.searchValue = "";
              const callBack =
                this.treeConfig.selectedKeys[0] === key ? "" : () => {};
              this.getTemplateTree(callBack);
            })
        });
      });
    },
    // 树内容区
    // 选中
    onSelect(selectedKeys, info) {
      this.savePromptMixin().then(() => {
        if (selectedKeys.length === 0) return;
        const { node } = info;
        if (node.dataRef.isLeaf) {
          // 当前节点为最末子节点
          this.treeConfig.selectedKeys = selectedKeys;
          this.selectTreeNode = node.dataRef;
          if (info.pId) {
            this.treeConfig.expandedKeys = [
              ...this.treeConfig.expandedKeys,
              info.pId
            ];
          }
        } else {
          // 当前节点为父节点
          let tempKeys = [...this.treeConfig.expandedKeys];
          // 当前节点未展开
          if (tempKeys.indexOf(node._props.dataRef.key) < 0) {
            tempKeys.push(node._props.dataRef.key);
          } else {
            tempKeys = tempKeys.filter(item => {
              return item !== node._props.dataRef.key;
            });
          }
          this.treeConfig.expandedKeys = tempKeys;
        }
      });
    },
    // 展开
    onExpand(expandedKeys) {
      this.treeConfig.expandedKeys = expandedKeys;
      this.treeConfig.autoExpandParent = false;
    },
    // 拖拽进入
    onDragEnter(info) {
      this.expandedKeys = info.expandedKeys;
    },
    // 拖拽放下
    onDrop(info) {
      this.savePromptMixin().then(res => {
        // 目标节点的信息
        const dropInfo = info.node;
        const {
          eventKey: dropKey,
          dataRef: { isLeaf: dropIsLeaf }
        } = dropInfo;
        // 拖动节点的信息
        const dragInfo = info.dragNode;
        // 拖动节点的key
        const {
          eventKey: dragKey,
          dataRef: { isLeaf: dragIsLeaf }
        } = dragInfo;
        const dropPos = info.node.pos.split("-");
        // 拖动到目标节点位置 0: 中、1:下 -1:上
        const dropPosition =
          info.dropPosition - Number(dropPos[dropPos.length - 1]);
        if (dragIsLeaf && !dropIsLeaf && dropPosition !== 0) {
          UiUtils.errorMessage(this.$t_process("not_drag_outside"));
          return;
        }
        if (!dragIsLeaf) {
          if (dropPosition === 0 || dropIsLeaf) {
            UiUtils.errorMessage(this.$t_process("not_drag_outside_file"));
            return;
          }
        }
        const positionMap = {
          "0": "in",
          "-1": "up",
          "1": "down"
        };
        const params = {
          sourceId: res || dragKey,
          targetId: dropKey,
          moveType: positionMap[String(dropPosition)]
        };
        taskFlowServeice("dragMoveNode", params).then(res => {
          this.getTemplateTree(() => {});
        });
      });
    },
    async refreshTreeData(selectKey) {
      await taskFlowServeice("getTemplateTree").then(res => {
        if (res.data.data && res.data.data.length) {
          this.initConfig.show = false;
          const { data } = res.data;
          handleLefTree(data);
          this.originData = cloneDeep(data);
          this.nodeNameList = this.getNodeNameList([...data], "name");
          this.treeConfig.treeData = data;
          if (this.searchValue) {
            this.searchValue = "";
            this.searching = false;
          }
          this.selectTreeNode = this.findNodeById(selectKey);
          this.treeConfig.selectedKeys = [selectKey];
        }
      });
    },
    findNodeById(id) {
      let isBreak = false;
      const tempArr = [...this.treeConfig.treeData];
      let treeNode = "";
      while (!isBreak) {
        const tempNode = tempArr.shift();
        const { key, children } = tempNode;
        if (children && children.length) {
          tempArr.unshift(...children);
        }
        isBreak = !tempArr.length;
        if (key === id) {
          treeNode = tempNode;
          isBreak = true;
        }
      }
      return treeNode;
    }
  }
};
</script>

<style lang="less">
.ant-modal-confirm {
  .ant-modal-confirm-btns {
    margin-top: @rem24 !important;
  }
}
</style>
<style lang="less" scoped>
.modal-content {
  font-size: @rem14;
  color: @yn-text-color-secondary;
}
.yn-tree-menu-wrapper .ant-tree-title > span:first-child .anticon-more {
  display: none;
  color: @yn-primary-color;
  line-height: @rem26;
  height: @rem26;
  width: 1.625rem;
  margin-top: 0.1875rem;
}

.yn-tree-menu-wrapper
  .ant-tree
  li
  .ant-tree-node-content-wrapper:hover
  .anticon-more {
  display: block;
}

.yn-tree-menu-wrapper
  .ant-tree
  li.ant-tree-treenode-selected
  span.ant-tree-node-selected
  .anticon-more {
  display: block;
}

.yn-tree-menu-wrapper span.node-operate-icon {
  float: right;
  position: relative;
  .anticon-more {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
}

.yn-tree-menu-wrapper
  li
  .ant-tree-node-content-wrapper
  span:not(.ant-tree-icon_loading) {
  display: inline-block;
  width: 100%;
  height: 100%;
}

.yn-tree-menu-wrapper li .ant-tree-node-content-wrapper span.node-title {
  /* width: calc(100% - 50px); */
  overflow: hidden;
  text-overflow: ellipsis;
}
.search-value {
  color: red;
  font-weight: 400;
}
.task-flow {
  width: 100%;
  height: 100%;
  /deep/.layout-left {
    overflow: hidden;
  }
  .task-flow-title {
    height: @rem40;
    line-height: @rem40;
    background: @yn-component-background;
    padding-left: @rem16;
    font-size: @rem16;
    font-weight: 600;
    color: @yn-heading-color;
    box-shadow: inset 0px -1px 0px 0px rgba(225, 229, 235, 1);
  }
  .container-layout {
    width: 100%;
    height: calc(100% - 2.75rem);
    .container-left {
      height: 100%;
      background: @yn-component-background;
      .left-header-menu {
        display: flex;
        width: 100%;
        height: @rem40;
        align-items: center;
        padding: 0 @yn-padding-xl;
        justify-content: space-between;
        border-right: 1px solid @yn-border-color-base;
        border-bottom: 1px solid @yn-border-color-base;
        .header-menu-btn {
        }
        .menu-simple-search {
          height: @rem26;
          width: 100%;
          /deep/ .ant-input {
            height: @rem26;
          }
        }
      }
      .left-header-tree {
        height: calc(100% - @rem36);
      }
      .left-tree {
        width: 100% !important;
        height: 100%;
        .folder-icon {
          // margin-left: -@yn-margin-l;
          position: absolute;
          width: @rem32;
          height: @rem32;
          // top: -@rem16;
          color: @yn-label-color;
        }
        .ml22 {
          margin-left: @rem22;
        }
        /deep/.ant-tree {
          padding-bottom: 3rem;
        }
      }
    }
  }

  .node-title-non-leaf {
    width: calc(100% - 4rem) !important;
  }
  .node-title-leaf {
    width: calc(100% - 2rem) !important;
  }

  .small-icon {
    height: 1.5rem;
    line-height: 1.5rem;
  }
}
</style>
