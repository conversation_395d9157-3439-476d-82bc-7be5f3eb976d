import Logger from "yn-p1/libs/modules/log/logger";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { APPS, FRONTEND } from "@/config/SETUP";
const DASHBOARD_REPORT_URL =
  "#/dashboard_rt?isDependent=true&loginName=admin&projectCode=mddengine&hideHeader=true&_self=true&source=109";

const TAB_URI = "/journal/detail";
const TABNAME = "journalDetail";
// 转发 表单向父层发送的消息到平台
function forwordMessage(openRelateCb, newtabMixin) {
  window.addEventListener("message", e => {
    Logger.info("params from form: ", e);
    const {
      data: { methods }
    } = e;
    if (!methods) return;
    const v = Object.values(methods[0].args);
    const key = Object.keys(methods[0].args)[0];
    switch (key) {
      case "openTab":
        openTab(v[0].jumpParam);
        break;
      case "openRelate":
        const jumpParam = v[0].jumpParam;
        if (jumpParam.openJournal) {
          // 打开合并日记账
          const { objectId: journalId, journalName } = jumpParam;
          newtabMixin({
            id: jumpParam.objectId,
            title: `${jumpParam.journalName}`,
            uri: TAB_URI,
            router: TABNAME, // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
            params: {
              journalId,
              title: journalName,
              isAdd: false
            },
            newTabParams: {}
          });
        } else {
          // 打开关联表单调用处自己实现，主要是传参数以及处理不同
          openRelateCb(v);
        }
        break;
    }
  });
}

function openTab(params) {
  window.parent.postMessage(
    JSON.stringify({
      type: "iframe_message_open",
      params
    }),
    "*"
  );
}

function setIframeP(nodeInfo, params) {
  const {
    key: formId,
    title: formName,
    data: { elementType }
  } = nodeInfo;
  const customObjForFormData = params.nodeId;
  const inTab = params.id;
  const customObj = params.customObj;
  const fieldsMap = {
    lang: "",
    TOKEN: "",
    appId: "",
    MenuId: "",
    securityFlag: "",
    ServiceName
  };
  Object.keys(fieldsMap).forEach(key => {
    fieldsMap[key] = DsUtils.getSessionStorageItem(key, {
      storagePrefix: APPS.NAME,
      isJson: true
    });
  });
  const {
    lang,
    TOKEN,
    appId,
    MenuId: menuId,
    securityFlag = true,
    ServiceName = "consolidation"
  } = fieldsMap;
  const { origin } = location;

  let logoutTargetUrl = null;
  let baseSrc = null;
  if (elementType === "report") {
    const logoutTargetUrl = location.href.split("logoutTargetUrl=")[1];
    baseSrc = `${FRONTEND.DASHBOARD_FRONT_VIEW_URL}/${DASHBOARD_REPORT_URL}&dashboardName=${formName}&token=${TOKEN}&appId=${appId}&lang=${lang}&referenceId=${formId}&logoutTargetUrl=${logoutTargetUrl}`;
  } else {
    logoutTargetUrl = `${origin}/ecs_console/index.html#/login&sandboxId=default&origin=${origin}/ecs_console/index.html&inTab=${inTab}`;
    const p1 = customObj ? `&customObj=${customObj}` : "";
    const p2 = customObjForFormData
      ? `&customObjForFormData=${customObjForFormData}`
      : "";
    baseSrc = `${FRONTEND.MR_FRONT_VIEW_URL}/#/openForm?bubbleStep=true&platType=1&menuHide=true&formId=${formId}&formName=${formName}&withAuthOpen=true&appId=${appId}&serviceName=${ServiceName}&menuId=${menuId}&lang=${lang}&TOKEN=${TOKEN}&securityFlag=${securityFlag}&timeDelta=-110&logoutTargetUrl=${logoutTargetUrl}${p1}${p2}`;
  }
  Logger.info("iframe src: ", baseSrc);
  return baseSrc;
}

export { setIframeP, forwordMessage };
