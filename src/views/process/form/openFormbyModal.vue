<template>
  <yn-modal
    :visible="modalInfo.visible"
    :title="modalInfo.title"
    width="80%"
    wrapClassName="drillDownModalWrapper"
    dialogClass="drillDownModal"
    :destroyOnClose="true"
    :closable="true"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <div class="iframeCT">
      <iframe :src="modalInfo.src"></iframe>
    </div>
  </yn-modal>
</template>

<script>

import "yn-p1/libs/components/yn-modal/";

export default {
  props: {
    modalInfo: {
      type: Object,
      default() {
        return {
          visible: false,
          title: "",
          src: ""
        };
      }
    }
  },
  data() {
    return {

    };
  },
  methods: {
    handleCancel() {
      this.$emit("handleCancel");
    }
  }
};
</script>

<style>
.ant-modal-wrap.drillDownModalWrapper .drillDownModal {
  height: 80%;
}
.drillDownModal .ant-modal-content {
  height: 100%;
}
.drillDownModal .ant-modal-footer {
  display: none;
}
.drillDownModal .ant-modal-body {
  display: flex;
  padding: 0;
  min-height: unset;
  height: calc(100% - 45px);
}
.drillDownModal .ant-modal-body .iframeCT {
  display: flex;
  flex: 1;
  width: 100%;
  height: 100%;
}
.drillDownModal .ant-modal-body .iframeCT > iframe {
  flex: 1;
  border: none;
}
</style>
