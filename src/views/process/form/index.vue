<template>
  <div class="form-tab">
    <iframe
      v-if="notForm && !customErrorPage"
      ref="notFormSrcRef"
      frameborder="0"
      width="100%"
      height="100%"
      scroll="auto"
      :src="notFormSrc"
    ></iframe>
    <!-- 自定义错误页面 -->
    <div v-if="notForm && customErrorPage" class="error-page">
      <yn-empty :image="require('../../../image/error.png')">
        <span slot="description" class="error-page-desc">
          <span class="span1">
            {{ $t_common("no_access_website") }}
          </span>
          <span>
            {{ $t_process("unable_access", [notFormSrc]) }}
          </span>
          <span>
            {{ $t_common("contact_administrator") }}
          </span>
        </span>
      </yn-empty>
    </div>

    <yn-lc-layout v-if="!notForm" iconPosition="center">
      <div slot="left" class="tab-left">
        <yn-tree
          :selectedKeys="selectedKeys"
          :expandedKeys.sync="expandedKeys"
          :treeData="treeData"
          @expand="onExpand"
          @select="onSelect"
        />
      </div>
      <div slot="center" class="tab-center">
        <empty v-if="!iframeSrc">
          <span slot="desc">{{ $t_process("select_form_view") }}</span>
        </empty>
        <iframe
          v-if="iframeSrc"
          frameborder="0"
          width="100%"
          height="100%"
          scroll="auto"
          :src="iframeSrc"
          class="childIframe"
        ></iframe>
      </div>
    </yn-lc-layout>

    <ModalForm
      v-for="item in modalFormList"
      :key="item.key"
      :modalInfo="item"
      @handleCancel="handleCancel"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-lc-layout/";
import "yn-p1/libs/components/yn-tree/";
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-modal/";
import EmptyImage from "@/image/reportEmpty.png";

import Logger from "yn-p1/libs/modules/log/logger";

import formMixin from "../../../mixin/formMixin";
import cloneDeep from "lodash/cloneDeep";
import { buildTree } from "../../../utils/taskFlowTemp";

import { APPS } from "@/config/SETUP";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { forwordMessage } from "./forwordMessage";

import Empty from "../../ruleConfiguration/empty.vue";
import ModalForm from "./openFormbyModal.vue";

import { FRONTEND } from "@/config/SETUP";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
const DASHBOARD_REPORT_URL =
  "#/dashboard_rt?isDependent=true&loginName=admin&projectCode=mddengine&hideHeader=true&_self=true&source=109";

export default {
  components: { Empty, ModalForm },
  mixins: [formMixin],
  props: {
    params: {
      type: Object,
      default: () => ({
        params: {
          formIds: "",
          id: "",
          nodeId: "",
          // 流程控制组织 id（table数据的 objectId） 用于 表单透传 区分是从 流程进入表单 还是菜单上进入
          customObj: "", // 表单 页面维 pov
          notForm: false
        }
      })
    }
  },
  data() {
    return {
      image: EmptyImage,
      notForm: false,
      customErrorPage: false,
      notFormSrc: "",
      paramsFromLastTab: {},
      treeData: [],
      treeKeyList: [],
      expandedKeys: [],
      selectedKeys: [],
      iframeSrc: "",
      pageDim: "", // 接受传过来的 页面维 pov
      nodeId: "", // 接收的 nodeId
      modalFormList: []
    };
  },
  watch: {},
  async created() {
    const params = {
      appId: UrlUtils.getQuery("appId"),
      menuId: UrlUtils.getQuery("menuId"),
      TOKEN: UrlUtils.getQuery("TOKEN"),
      lang: UrlUtils.getQuery("lang"),
      roleId: UrlUtils.getQuery("roleId"),
      serviceName: UrlUtils.getQuery("serviceName"),
      securityFlag: UrlUtils.getQuery("securityFlag"),
      timeDelta: UrlUtils.getQuery("timeDelta"),
      origin: UrlUtils.getQuery("origin"),
      logoutTargetUrl: UrlUtils.getQuery("logoutTargetUrl")
    };
    // 过滤掉空值并拼接成参数字符串
    const queryString = Object.entries(params)
      .filter(
        ([key, value]) => value !== undefined && value !== null && value !== ""
      )
      .map(
        ([key, value]) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
      )
      .join("&");
    let platformUrl = "";
    const { notForm, notFormSrc, customObj, nodeId } = this.params.params;
    platformUrl = notFormSrc + "?" + queryString;
    Object.assign(this, {
      notForm: notForm,
      notFormSrc: platformUrl,
      pageDim: customObj,
      nodeId
    });
    if (!this.selfTab) {
      const {
        notForm,
        notFormSrc,
        customObj,
        nodeId
      } = this.getTabParamsMixin();
      this.paramsFromLastTab = this.getTabParamsMixin();
      platformUrl = notFormSrc + "?" + queryString;
      Object.assign(this, {
        notForm: notForm,
        notFormSrc: platformUrl,
        pageDim: customObj,
        nodeId
      });
    } else {
      this.paramsFromLastTab = this.params.params;
    }
    const jugeUrl = url => {
      var Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;
      var objExp = new RegExp(Expression);
      return objExp.test(url);
    };
    if (!jugeUrl(this.notFormSrc)) {
      this.customErrorPage = true;
    }
    if (this.notForm) return;

    // 表单才调下面的接口
    await this.getFormList();
    this.buildFormTree();

    // 打开新页签 表单
    this.openNewTabForm();

    // 转发 表单向父层发送的信息到 平台。
    forwordMessage(this.openRelate, this.newtabMixin);
  },
  mounted() {
    window.addEventListener("message", this.messageCallback, false);
  },
  methods: {
    messageCallback(e) {
      switch (e.data.type) {
        case "iframe_message_mdvgrid_inited":
          this.$el.querySelector(".childIframe").contentWindow.postMessage(
            {
              type: "iframe_message_location_form_by_dim",
              locationDim: JSON.parse(decodeURIComponent(this.pageDim))
            },
            "*"
          );
          break;
      }
    },
    openNewTabForm() {
      const { dataRef } = this.paramsFromLastTab;
      if (!dataRef || dataRef.length === 0) return;
      this.selectedKeys = [dataRef.key];
      const parentKeys = this.findParentKeys(this.selectedKeys);
      this.expandedKeys = [...parentKeys];
      this.iframeSrc = this.setIframeP(dataRef);
    },

    openRelate(v) {
      const type = v[0].openType;
      // 要么是 页签 要么是 弹窗 tab || modal
      const key = v[0].jumpParam.key;
      const title = v[0].jumpParam.title || v[0].jumpParam.name;
      if (type === "tab") {
        const bool = this.treeKeyList.find(item => item === key);
        const { formIds, nodeId, customObj } = this.paramsFromLastTab;
        if (bool) {
          // 存在左侧树上
          this.newtabMixin({
            id: key,
            title,
            uri: "/process/form",
            router: "processForm",
            params: {
              from: "process",
              formIds,
              id: key,
              // 流程控制组织 id（table数据的 objectId）
              nodeId,
              customObj,
              dataRef: {
                key,
                title,
                data: {
                  elementType: v[0].jumpParam.type
                }
              }
            }
          });
        } else {
          // 不在左侧树上
          const elementType = v[0].jumpParam.type;
          const dataRef = {
            key,
            title,
            data: {
              elementType
            }
          };
          this.newtabMixin({
            id: key,
            title,
            uri: elementType === "report" ? "/openReport" : "/openForm",
            router: elementType === "report" ? "openReport" : "openForm",
            params: {
              from: "process",
              formIds,
              id: key,
              // 流程控制组织 id（table数据的 objectId）
              nodeId,
              customObj,
              dataRef,
              src: this.setIframeP(dataRef),
              treeKeyList: this.treeKeyList
            }
          });
        }
      } else {
        // 弹窗方式打开
        const modalInfo = {
          visible: true,
          key,
          src: this.setIframeP({
            key,
            title,
            data: {
              elementType: v[0].jumpParam.type
            }
          }),
          title
        };
        this.modalFormList.push(modalInfo);
      }
    },

    handleCancel() {
      // this.$set(this.modalInfo, "visible", false);
      this.$set(
        this.modalFormList[this.modalFormList.length - 1],
        "visible",
        false
      );
      this.modalFormList.pop();
    },

    buildFormTree() {
      let formIdList =
        this.params.params.formIds && this.params.params.formIds.split(";");
      if (!this.selfTab) {
        formIdList = this.getTabParamsMixin().formIds.split(";");
      }
      const formList = formIdList
        .map(id => {
          const f = this.pavingData.filter(item => item.key === id);
          if (f.length) {
            return f[0];
          }
          return "";
        })
        .filter(item => item);
      const getKeys = arr => {
        const allKey = [];
        let copyArr = cloneDeep(arr);
        while (copyArr.length) {
          const cur = copyArr.shift();
          if (cur) {
            allKey.push(cur.key);
          }
          if (cur.children && cur.children.length) {
            copyArr = [...cur.children, ...copyArr];
          }
        }
        return allKey;
      };
      const formKeys = getKeys(formList);
      const parentKeys = this.findParentKeys(formKeys);
      const treeKeys = [...new Set([...formKeys, ...parentKeys])];
      // const formData = this.pavingData.filter(item =>
      //   treeKeys.includes(item.key)
      // );
      const formData = treeKeys
        .map(key => this.pavingData.find(item => item.key === key))
        .filter(item => item);
      this.treeData = buildTree(formData);
      this.treeKeyList = treeKeys;
      // 默认展开 全部，选择第一个表单
      const selectNode = this.getFirstChild(formList[0]);
      this.selectedKeys = [selectNode.key];
      this.expandedKeys = [...parentKeys];
      this.iframeSrc = this.setIframeP(selectNode);
    },
    getFirstChild(parent) {
      let next = parent;
      while (next.children.length > 0) {
        next = next.children[0];
      }
      return next;
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
    },
    onSelect(selectedKeys, info) {
      if (selectedKeys.length === 0) return;
      const { node } = info;
      if (this.iframeSrc) {
        // 如果第一次选中了表单，则再次选择的时候打开新页签
        const {
          dataRef,
          dataRef: { key, title }
        } = node;
        const { formIds, nodeId, customObj } = this.paramsFromLastTab;
        const firstFormTabId = DsUtils.getSessionStorageItem("firstFormTabId", {
          storagePrefix: APPS.NAME,
          isJson: true
        });
        const firstFormId = DsUtils.getSessionStorageItem("firstFormId", {
          storagePrefix: APPS.NAME,
          isJson: true
        });
        // 打开新页签
        this.newtabMixin({
          id: key === firstFormId ? firstFormTabId : key,
          title,
          uri: "/process/form",
          router: "processForm",
          params: {
            from: "process",
            dataRef,
            formIds,
            id: key === firstFormId ? firstFormTabId : key,
            // 流程控制组织 id（table数据的 objectId）
            nodeId,
            customObj
          }
        });
      } else {
        if (!node.dataRef.children || !node.dataRef.children.length) {
          this.selectedKeys = [node.dataRef.key];
          this.iframeSrc = this.setIframeP(node.dataRef);
          DsUtils.setSessionStorageItem(
            "firstFormTabId",
            this.paramsFromLastTab.id || "",
            {
              storagePrefix: APPS.NAME,
              isJson: true
            }
          );
          DsUtils.setSessionStorageItem("firstFormId", node.dataRef.key || "", {
            storagePrefix: APPS.NAME,
            isJson: true
          });
        }
      }
    },
    setIframeP(nodeInfo) {
      // const formId = nodeInfo.key;
      // const formName = encodeURIComponent(nodeInfo.title);
      const {
        key: formId,
        title: formName,
        data: { elementType }
      } = nodeInfo;
      // const customObjForFormData = encodeURIComponent(
      //   JSON.stringify({ nodeId: this.nodeId })
      // );
      const customObjForFormData = this.nodeId;
      const fieldsMap = {
        lang: "",
        TOKEN: "",
        appId: "",
        MenuId: "",
        securityFlag: "",
        ServiceName
      };
      Object.keys(fieldsMap).forEach(key => {
        fieldsMap[key] = DsUtils.getSessionStorageItem(key, {
          storagePrefix: APPS.NAME,
          isJson: true
        });
      });
      const {
        lang,
        TOKEN,
        appId,
        MenuId: menuId,
        securityFlag = true,
        ServiceName = "consolidation"
      } = fieldsMap;
      let baseSrc = null;
      if (elementType === "report") {
        const logoutTargetUrl = location.href.split("logoutTargetUrl=")[1];
        baseSrc = `${FRONTEND.DASHBOARD_FRONT_VIEW_URL}/${DASHBOARD_REPORT_URL}&dashboardName=${formName}&token=${TOKEN}&appId=${appId}&lang=${lang}&referenceId=${formId}&logoutTargetUrl=${logoutTargetUrl}`;
      } else {
        baseSrc = `${FRONTEND.MR_FRONT_VIEW_URL}/#/openForm?bubbleStep=false&customObj=${this.pageDim}&customObjForFormData=${customObjForFormData}&platType=1&menuHide=true&formId=${formId}&formName=${formName}&withAuthOpen=true&appId=${appId}&serviceName=${ServiceName}&menuId=${menuId}&lang=${lang}&TOKEN=${TOKEN}&securityFlag=${securityFlag}&timeDelta=-110`;
      }
      Logger.info("iframe src: ", baseSrc);
      return baseSrc;
    }
  }
};
</script>

<style scoped lang="less">
.form-tab {
  background: @yn-component-background;
  width: 100%;
  height: 100%;
  .tab-left {
    height: 100%;
  }
  .tab-center {
    width: 100%;
    height: 100%;
  }
  .error-page {
    width: 100%;
    height: 100%;
    position: relative;
    /deep/.ant-empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      .ant-empty-image {
        height: 8.75rem;
      }
    }
    .error-page-desc {
      & > span {
        display: inline-block;
        width: 100%;
        font-size: @rem14;
        color: @yn-label-color;
        height: @rem22;
        line-height: @rem22;
      }
      .span1 {
        height: @rem24;
        line-height: @rem24;
        color: @yn-primary-10;
      }
      .span2 {
        margin: @rem10 0;
      }
    }
  }
}
</style>
