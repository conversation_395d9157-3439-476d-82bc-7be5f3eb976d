<template>
  <BatchTab
    :tab="tab"
    :root="root"
    :element="element"
    @add="handlerAdd"
    @delete="handlerDelete"
    @update="updateElement"
  />
</template>

<script>
import BatchTab from "./batchtab.vue";
export default {
  components: { BatchTab },
  props: {
    element: {
      type: Object,
      default: () => {
        return {};
      }
    },
    root: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      tab: []
    };
  },
  watch: {
    "element.hash": {
      handler() {
        this.setDefault();
        this.tab = this.element.batchAttributes || [this.genEmptyBatch()];
      },
      immediate: true
    }
  },
  methods: {
    genEmptyBatch() {
      return {
        batchName: this.$t_process("batch_submit1"),
        batchItem: "DIMMEMBER",
        batchDimMemberExp: [],
        batchFormId: [],
        nodeType: "batch",
        timeoutCtrl: 0,
        processNodeMessages: []
      };
    },
    handlerAdd() {
      this.tab.push(this.genEmptyBatch());
      this.updateElement();
    },
    handlerDelete(i) {
      this.tab.splice(i, 1);
      if (this.tab.length === 0) {
        this.tab.push(this.genEmptyBatch());
      }
      this.updateElement();
    },
    updateElement() {
      this.element.batchAttributes = this.tab;
      this.$emit("node-update", this.element);
    },
    setDefault() {
      this.element.nodeAttribute ||
        this.$set(this.element, "nodeAttribute", {});
      this.$set(
        this.element.nodeAttribute,
        "nodeShowName",
        this.$t_process("phase_submit")
      );
      this.$set(this.element.nodeAttribute, "nodeType", "batch");
    }
  }
};
</script>

<style lang="less" scoped></style>
