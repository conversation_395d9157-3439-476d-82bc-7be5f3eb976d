<template>
  <div class="collapselist">
    <yn-collapse v-model="activeKey">
      <yn-collapse-panel v-for="(value, key) in form" :key="key">
        <template slot="header">
          <span class="collapse-header-wrap">
            <div class="collapse-msg-wrap">
              <div>{{ groupName[key] }}</div>
            </div>
          </span>
        </template>
        <yn-list
          rowKey="key"
          itemLayout="horizontal"
          :dataSource="value"
          :drag="true"
        >
          <yn-list-item slot="renderItem" slot-scope="item">
            <yn-icon-svg class="list-drag-icon" type="drag-and-drop" />
            <span slot="extra">
              <yn-tooltip :title="$t_common('delete')">
                <yn-icon-button
                  type="icon-shanchu"
                  class="delete-item"
                  @click="handlerDelete(item, value)"
                />
              </yn-tooltip>
            </span>
            <yn-list-item-meta>
              <span slot="title" :title="item.name" class="list-title">
                {{ item.name }}
                <yn-icon-svg
                  v-if="$attrs.ranger"
                  :class="['select-role', { has: hasType(item) }]"
                  type="relation"
                  @click="handlerRole(item)"
                />
              </span>
            </yn-list-item-meta>
          </yn-list-item>
        </yn-list>
      </yn-collapse-panel>
    </yn-collapse>
    <role-model
      :title="name + $t_process('affiliated_organizations')"
      v-bind="$attrs"
      :visible.sync="roleVisible"
      :dimInfos="dimInfo"
      @ok="handlerOk"
    />
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-list/";
import "yn-p1/libs/components/yn-list-item/";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-collapse-panel/";
import "yn-p1/libs/components/yn-list-item-meta/";
import "yn-p1/libs/components/yn-icon-button/";
import "yn-p1/libs/components/yn-collapse/";
import "yn-p1/libs/components/yn-icon/";
import roleModel from "./roleModel";
import DIM_INFO from "@/constant/dimMapping";
import lang from "@/mixin/lang";
const { $t_process } = lang;
const Entity = {
  dimName: $t_process("entity"),
  dimCode: "Entity",
  objectId: DIM_INFO.Entity,
  dimId: DIM_INFO.Entity,
  selectedItem: [],
  dynamicOrStatic: "dynamic",
  member: ""
};
const Scope = {
  dimName: $t_process("scopes"),
  dimCode: "Scope",
  objectId: DIM_INFO.Scope,
  dimId: DIM_INFO.Scope,
  dynamicOrStatic: "dynamic",
  selectedItem: [],
  member: ""
};
export default {
  components: { roleModel },
  props: {
    data: {
      type: Object,
      default: () => {
        return {
          group: [],
          role: [],
          work: [],
          person: []
        };
      }
    },
    dimInfos: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      text: "",
      activeKey: ["1"],
      roleVisible: false,
      name: "",
      type: "",
      id: "",
      groupName: {
        person: this.$t_process("select_users"),
        role: this.$t_process("select_roles"),
        group: this.$t_process("select_groups"),
        work: this.$t_process("select_positions")
      }
    };
  },

  computed: {
    dimInfo() {
      if (this.type) {
        const dimInfo = this.dimInfos[this.type].find(
          item => item.id === this.id
        );
        return dimInfo ? dimInfo.member : [{ ...Entity }, { ...Scope }];
      } else {
        return [{ ...Entity }, { ...Scope }];
      }
    },
    form: {
      get() {
        return {
          person: this.data.person,
          role: this.data.role,
          group: this.data.group,
          work: this.data.work
        };
      },
      set() {}
    }
  },
  watch: {
    activeKey(key) {}
  },
  methods: {
    handleClick(event) {
      // If you don't want click extra trigger collapse, you can prevent this:
      event.stopPropagation();
    },
    handlerDelete(item, value) {
      const index = value.indexOf(item);
      value.splice(index, 1);
      this.$emit("change");
    },
    hasType(item) {
      const dimInfo = (this.dimInfos[item.type] || []).find(
        dim => item.id === dim.id
      );
      if (dimInfo) {
        return dimInfo.member.some(item => {
          if (!item.members) return false;
          const hasMemberType = this.hasValue(
            item.members.memberType,
            item.dimId
          );
          const hasAttr = item.members.attr && item.members.attr.length > 0;
          const hasLevel = item.members.level && item.members.level.length > 0;
          const hasVariable =
            item.members.variable && item.members.variable.length > 0;
          const hasSubset =
            item.members.subset && item.members.subset.length > 0;

          return (
            item.members.allMember ||
            hasMemberType ||
            hasAttr ||
            hasLevel ||
            hasVariable ||
            hasSubset
          );
        });
      }
      return false;
    },
    hasValue(arr, id) {
      if (!arr || !this.$attrs.ranger || !this.$attrs.ranger[id]) return false;
      return (
        arr.filter(item => {
          return this.$attrs.ranger[id].some(
            sItem => sItem.objectId === item.memberId
          );
        }).length > 0
      );
    },
    handlerOk(data) {
      data.id = this.id;
      this.$emit("changeDim", data, this.type);
    },
    handlerRole(item) {
      this.type = item.type;
      this.name = item.name;
      this.id = item.id;
      this.roleVisible = true;
    }
  }
};
</script>
<style scoped lang="less">
.collapselist {
  height: calc(100% - 2.5rem);
  padding: 0.5rem;
  overflow-y: auto;

  .delete-item {
    display: none;
  }
  .select-role {
    margin-left: 0.5rem;
    color: @yn-label-color;
    cursor: pointer;
    &.has {
      color: @yn-primary-color;
    }
  }
  /deep/ .ant-list-item-meta {
    margin-left: 0.625rem;
  }
  /deep/ .ant-collapse-content {
    border: none;
  }
  /deep/ .ant-collapse {
    border: none;
  }
  /deep/ .ant-list-item-meta-title {
    margin-bottom: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  /deep/ .ant-collapse-content-box {
    padding: 0;
  }
  /deep/ .ant-list-item {
    padding-top: 0;
    padding-bottom: 0;
    border: none;
  }
  /deep/ .ant-list-item {
    height: 2.0625rem;
  }
  /deep/ .yn-list-row:hover {
    height: 2.0625rem;
    background: @yn-background-color-light;
    .delete-item {
      display: inline-block;
    }
  }
  /deep/ .ant-collapse-item {
    border: none;
  }
  /deep/ .ant-collapse-header {
    background: @yn-primary-1;
    box-shadow: inset 0px -1px 0px 0px rgba(225, 229, 235, 1);
  }
}
</style>
