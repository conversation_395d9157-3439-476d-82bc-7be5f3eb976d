<template>
  <div ref="dimListInput" :class="contClass" @click="showTransfer">
    <div class="input-content">
      <div
        :class="[
          'input-text',
          forms && forms.split(';').length ? 'input-text-choose' : ''
        ]"
      >
        {{
          forms && forms.split(";").length
            ? $t_common("selected_view_members")
            : $t_common("input_select")
        }}
      </div>
      <svg-icon
        class="icon-cont"
        type="icon-chuansuokuang"
        :isIconBtn="false"
        @click="showTransfer"
      />
      <TransferLike
        :visible.sync="visible"
        :clickFormInfo="forms"
        @getForm="getForm"
      />
    </div>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-icon/";
import TransferLike from "@/components/hoc/transferLike/index.vue";
import { mapActions } from "vuex";
import lang from "@/mixin/lang";
const { $t_common } = lang;
export default {
  name: "BatchForm",
  components: { TransferLike },
  model: {
    prop: "value",
    event: "change"
  },
  props: {
    placeholder: {
      type: String,
      default: $t_common("input_select")
    },
    value: Array
  },
  data() {
    return {
      exprObj: null,
      showDimList: [],
      hideDimNum: 0,
      dimList: [],
      visible: false,
      contWidth: 0,
      transferData: {}
    };
  },
  computed: {
    contClass() {
      const arr = ["dim-list-input", "expase-text"];
      return arr;
    },
    forms: {
      get() {
        return this.value ? this.value.join(";") : "";
      },
      set(value) {
        this.$emit("change", value.split(";"));
      }
    }
  },
  created() {
    this.getFormList();
  },
  methods: {
    ...mapActions({
      getFormList: "processStore/getFormList"
    }),
    getForm(forms) {
      this.forms = forms.join(";");
    },
    showTransfer() {
      this.visible = true;
    }
  }
};
</script>
<style lang="less" scoped>
.expase-text {
  display: flex;
  align-items: center;
}
.dim-list-input {
  width: 200px;
  height: @rem32;
  overflow: hidden;
  line-height: @rem32;
  border: 1px solid @yn-border-color-base;
  border-radius: @yn-border-radius-base;
  padding: 0 @rem32 0 @rem10;
  position: relative;
  .input-content {
    width: 100%;
    height: @rem30;
    padding: 3px 0;
    display: flex;
    align-items: center;
    .placeholder {
      color: @yn-auxiliary-color;
    }
    .input-text {
      height: @rem24;
      line-height: @rem24;
      color: @yn-auxiliary-color;
    }
    .input-text-choose {
      color: @yn-text-color;
    }
  }
  .icon-cont {
    position: absolute;
    top: 0;
    display: inline-block;
    height: @rem30;
    line-height: @rem30;
    right: @rem8;
    cursor: pointer;
    color: @yn-disabled-color;
  }
  &:hover {
    border-color: @yn-primary-5;
    cursor: pointer;
  }
}
</style>
