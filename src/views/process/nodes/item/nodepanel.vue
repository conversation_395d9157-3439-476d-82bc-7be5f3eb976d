<template>
  <section class="node-panel">
    <yn-tabs
      :activeKey="activeKey"
      class="node-panel-tab"
      @change="handlerChangeTab"
    >
      <yn-tab-pane key="base" :tab="$t_process('basic_settings')" />
      <yn-tab-pane key="person" :tab="$t_process('executor')" />
      <yn-tab-pane key="access" :tab="$t_process('authorization')" />
      <yn-tab-pane
        key="email"
        :tab="$t_process('message_template.message_notification')"
      />
    </yn-tabs>
    <section class="node-panel-content">
      <section class="block base" data-tab="base">
        <header class="tab-title">
          <yn-divider type="vertical" class="sign" />{{
            $t_process("basic_settings")
          }}
        </header>
        <yn-form
          :form="nodeForm"
          class="nodeForm"
          labelAlign="left"
          v-bind="{
            labelCol: { span: 7 },
            wrapperCol: { span: 17 }
          }"
        >
          <yn-form-item :label="$t_process('process_step_name')" :colon="false">
            <yn-input
              v-decorator="[
                'nodeShowName',
                {
                  rules: [
                    {
                      required: true,
                      message:
                        $t_common('input_message') +
                        $t_process('process_step_name')
                    },
                    {
                      message: $t_process('field_length_exceeds_64'),
                      max: 64
                    }
                  ]
                }
              ]"
              :placeholder="$t_common('input_message')"
              @change="handlerChange"
            >
              <yn-icon slot="suffix" type="global" @click="handleLanguages" />
            </yn-input>
          </yn-form-item>
          <yn-form-item :label="$t_process('process_step_type')" :colon="false">
            <yn-select
              v-decorator="[
                'nodeType',
                {
                  rules: [
                    {
                      required: true,
                      message:
                        $t_common('input_select') +
                        $t_process('process_step_type')
                    }
                  ]
                }
              ]"
              :placeholder="$t_common('input_select')"
              :options="optionsType"
              :disabled="true"
              @change="handlerChange"
            />
          </yn-form-item>
          <yn-form-item
            :label="$t_process('process_step_status')"
            :colon="false"
          >
            <yn-select
              v-decorator="[
                'nodeStatus',
                {
                  rules: [
                    {
                      required: true,
                      message:
                        $t_common('input_select') +
                        $t_process('process_step_status')
                    }
                  ]
                }
              ]"
              :placeholder="$t_common('input_select')"
              :allowClear="false"
              :options="optionsStatus"
              @change="handlerChange"
            />
          </yn-form-item>
          <yn-form-item
            :label="$t_process('process_step_buttons')"
            :colon="false"
          >
            <yn-select
              v-decorator="[
                'nodeButton',
                {
                  rules: [
                    {
                      required: true,
                      message:
                        $t_common('input_select') +
                        $t_process('process_step_buttons')
                    }
                  ]
                }
              ]"
              :placeholder="$t_common('input_select')"
              mode="multiple"
              showSearch
              :allowClear="false"
              showArrow
              @change="handlerChange"
            >
              <yn-select-option
                v-for="item in optionsButton"
                :key="item.value"
                :disabled="item.disabled"
              >
                {{ item.label }}
              </yn-select-option>
            </yn-select>
          </yn-form-item>
          <yn-form-item :label="$t_process('due_time')" :colon="false">
            {{ $t_process("duration_not_exceed") }}
            <yn-input-number
              v-decorator="['timeoutCtrl']"
              :min="0"
              :max="9999"
              :precision="0"
              :placeholder="$t_common('input_message')"
              @change="handlerChange"
            />
            {{ $t_common("maintenance_hour") }}
          </yn-form-item>
          <!-- <yn-form-item label=" " :colon="false">
            <yn-checkbox
              :checked="showTaskCountdown"
              class="task-countdown"
              @change="checkBoxChange"
            >
              {{ $t_process("task_countdown") }}
            </yn-checkbox>
          </yn-form-item> -->
        </yn-form>
      </section>
      <section class="block person" data-tab="person">
        <header class="tab-title">
          <yn-divider type="vertical" class="sign" />{{
            $t_process("executor")
          }}
        </header>
        <yn-form
          :form="nodeForm"
          class="nodeForm"
          labelAlign="left"
          v-bind="{
            labelCol: { span: 7 },
            wrapperCol: { span: 17 }
          }"
        >
          <NumberCard
            v-for="(item, index) in executor"
            :key="index"
            :num="index"
            class="person-card"
            :disabled="executor.length === 1"
            @delete="handlerDelPerson"
          >
            <yn-form-item :label="$t_process('executor')" :colon="false">
              <PersonInput
                v-decorator="[
                  'card-person-' + index,
                  {
                    rules: [
                      { required: true, message: $t_common('input_select') }
                    ]
                  }
                ]"
                :ranger="ranger"
                @change="handlerChange"
              />
            </yn-form-item>
            <div class="import-user">
              {{ $t_common("local_data") }}, {{ $t_common("batch") }}
              <span @click="handlerImport(index)">
                {{ $t_common("import") }}
              </span>
            </div>
            <yn-form-item :label="$t_process('execute_type')">
              <yn-radio-group
                v-decorator="['card-person-type-' + index]"
                @change="handlerChange"
              >
                <yn-radio value="seize">
                  {{ $t_process("monopolize") }}
                </yn-radio>
              </yn-radio-group>
            </yn-form-item>
          </NumberCard>
          <yn-button type="text" class="addOne" @click="handlerPerson">
            <SvgIcon :isIconBtn="false" type="icon-cr_add" class="prefix" />
            {{ $t_process("add_executor") }}
          </yn-button>
          <yn-form-item :label="$t_process('cc_to')" :colon="false">
            <PersonInput v-decorator="['copy']" @change="handlerChange" />
          </yn-form-item>
        </yn-form>
      </section>
      <section class="block access" data-tab="access">
        <header class="tab-title">
          <yn-divider type="vertical" class="sign" />{{
            $t_process("authorization")
          }}
        </header>
        <yn-form
          :form="nodeForm"
          class="nodeForm"
          labelAlign="left"
          v-bind="{
            labelCol: { span: 7 },
            wrapperCol: { span: 17 }
          }"
        >
          <div class="access-title">{{ $t_process("task_authorization") }}</div>
          <yn-form-item
            :label="$t_process('allowed_to_fill_form')"
            class="radiosgroup"
          >
            <yn-radio-group v-decorator="['writeForm']" @change="handlerChange">
              <yn-radio :value="1">
                {{ $t_common("allow") }}
              </yn-radio>
              <yn-radio :value="0">
                {{ $t_common("not") + $t_common("allow") }}
              </yn-radio>
            </yn-radio-group>
          </yn-form-item>
          <yn-form-item
            :label="$t_process('allowed_to_run_validations')"
            class="radiosgroup"
          >
            <yn-radio-group
              v-decorator="['validateForm']"
              @change="handlerChange"
            >
              <yn-radio :value="1">
                {{ $t_common("check") }}
              </yn-radio>
              <yn-radio :value="0">
                {{ $t_common("not") + $t_common("check") }}
              </yn-radio>
            </yn-radio-group>
          </yn-form-item>
          <div v-if="false" class="access-title">
            {{ $t_process("email_notifications") }}
          </div>
          <yn-form-item
            v-if="false"
            :label="$t_process('process_steps_start')"
            class="radiosgroup"
          >
            <yn-radio-group v-decorator="['nodeStart']" @change="handlerChange">
              <yn-radio :value="1">
                {{ $t_common("yes") }}
              </yn-radio>
              <yn-radio :value="0">
                {{ $t_common("no") }}
              </yn-radio>
            </yn-radio-group>
          </yn-form-item>
          <yn-form-item
            v-if="false"
            :label="$t_process('process_steps_rejected')"
            class="radiosgroup"
          >
            <yn-radio-group
              v-decorator="['nodeReturn']"
              @change="handlerChange"
            >
              <yn-radio :value="1">
                {{ $t_common("yes") }}
              </yn-radio>
              <yn-radio :value="0">
                {{ $t_common("no") }}
              </yn-radio>
            </yn-radio-group>
          </yn-form-item>
          <yn-form-item
            v-if="false"
            :label="$t_process('process_steps_overdue')"
            class="radiosgroup"
          >
            <yn-radio-group
              v-decorator="['nodeTimeout']"
              @change="handlerChange"
            >
              <yn-radio :value="1">
                {{ $t_common("yes") }}
              </yn-radio>
              <yn-radio :value="0">
                {{ $t_common("no") }}
              </yn-radio>
            </yn-radio-group>
          </yn-form-item>
          <yn-form-item
            v-if="false"
            :label="$t_process('process_steps_overdue_warning')"
            class="radiosgroup"
          >
            <yn-radio-group
              v-decorator="['nodeTimeoutWarning']"
              @change="handlerChange"
            >
              <yn-radio :value="1">
                {{ $t_common("yes") }}
              </yn-radio>
              <yn-radio :value="0">
                {{ $t_common("no") }}
              </yn-radio>
            </yn-radio-group>
          </yn-form-item>
        </yn-form>
      </section>
      <section class="block email" data-tab="email">
        <header class="tab-title">
          <yn-divider type="vertical" class="sign" />{{
            $t_process("message_template.message_notification")
          }}
        </header>
        <yn-form
          :form="nodeForm"
          class="nodeForm"
          labelAlign="left"
          v-bind="{
            labelCol: { span: 0 },
            wrapperCol: { span: 24 }
          }"
        >
          <NumberCard
            v-for="(item, index) in emailCard"
            :key="index"
            :num="index"
            class="email-card"
            @delete="handlerDelEmail"
          >
            <yn-form-item label="" :colon="false">
              <EmailContent
                ref="card-email"
                v-decorator="['card-email-' + index]"
                :ranger="ranger"
                :nodeId="element.nodeId"
                @change="handlerChange"
              />
            </yn-form-item>
          </NumberCard>
        </yn-form>
        <yn-button type="text" class="addOne" @click="handlerEmail">
          <SvgIcon :isIconBtn="false" type="icon-cr_add" class="prefix" />
          {{ $t_process("message_template.add_set_of_message_notifications") }}
        </yn-button>
      </section>
    </section>
    <language-modal
      :key="element.nodeId"
      :languageVisible="languageVisible"
      :languageList.sync="element.nodeAttribute.multiLanguages"
      :addCurLang="true"
      :curText="element.nodeShowName"
      @change.native="handlerChange"
      @cancelMultilanguage="cancelMultilanguage"
    />
    <user-import-model
      :visible.sync="userVisible"
      :root="root"
      :params="executor[executorIndex]"
      @ok="handlerOk"
    />
  </section>
</template>

<script>
import "yn-p1/libs/components/yn-tabs/";
import "yn-p1/libs/components/yn-tab-pane/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-radio/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-input-number/";
import "yn-p1/libs/components/yn-divider/";
import NumberCard from "./numberCard.vue";
import EmailContent from "./emailContent.vue";
import commonService from "@/services/common";
import UserImportModel from "./userImportModel.vue";
import SvgIcon from "@/components/ui/SvgIcon.vue";
import PersonInput from "./personinput.vue";
import LanguageModal from "@/components/multilanguage";
import precessService from "@/services/process";
import { mapActions } from "vuex";
import { isType } from "@/utils/common";
const autoSelect = {
  not_started: ["start"],
  processing: ["submit", "turndown"],
  pending_review: ["audit", "turndown"],
  submitted: ["turndown"]
};
const disableSelect = {
  not_started: [
    "start",
    "submit",
    "audit",
    "turndown",
    "caculate",
    "converted",
    "merge"
  ],
  processing: ["start", "submit", "audit", "turndown"],
  pending_review: ["start", "submit", "audit", "turndown"],
  submitted: ["start", "submit", "audit", "turndown"]
};
export default {
  components: {
    SvgIcon,
    PersonInput,
    LanguageModal,
    UserImportModel,
    NumberCard,
    EmailContent
  },
  props: {
    element: {
      type: Object,
      default: () => {
        return {};
      }
    },
    root: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      executor: [""],
      emailCard: [],
      copyer: "",
      ranger: {},
      activeKey: "base",
      visible: false,
      userVisible: false,
      showTaskCountdown: false,
      formData: {},
      languageVisible: false,
      executorIndex: -1,
      languageList: [],
      optionsType: [
        {
          label: this.$t_process("start"),
          value: "start"
        },
        {
          label: this.$t_process("approve"),
          value: "approval"
        },
        {
          label: this.$t_process("activities"),
          value: "active"
        },
        {
          label: this.$t_process("end"),
          value: "end"
        },
        {
          label: this.$t_process("batch"),
          value: "batch"
        }
      ],
      optionsStatus: [
        {
          label: this.$t_process("not_started"),
          value: "not_started"
        },
        {
          label: this.$t_common("in_process"),
          value: "processing"
        },
        {
          label: this.$t_common("wait_audit"),
          value: "pending_review"
        },
        {
          label: this.$t_common("submitted"),
          value: "submitted"
        }
      ],
      optionsButton: [
        {
          label: this.$t_process("start_up"),
          value: "start"
        },
        {
          label: this.$t_process("submit"),
          value: "submit"
        },
        {
          label: this.$t_process("reject"),
          value: "turndown"
        },
        {
          label: this.$t_process("approve"),
          value: "audit"
        },
        {
          label: this.$t_process("calculation"),
          value: "caculate"
        },
        {
          label: this.$t_process("fxtrans"),
          value: "converted"
        },
        {
          label: this.$t_process("consolidation"),
          value: "merge"
        }
      ],

      nodeForm: this.$form.createForm(this, "nodeForm")
    };
  },
  watch: {
    "element.hash": {
      handler() {
        this.setDefault();
        this.getCard();
        this.getCopy();
        this.getEmailCard();
        this.getFormData();
        this.setFormValue();
        this.setButtonOptions();
      },
      immediate: true
    }
  },
  async created() {
    this.ranger = await this.getRanger();
    this.$nextTick(() => {
      this.bindScroll();
    });
  },
  mounted() {
    this.setLang();
  },
  methods: {
    ...mapActions({
      setLang: "common/getEnableLanguages"
    }),
    async getRanger() {
      const entity = this.root.entity ? JSON.parse(this.root.entity) : "";
      const scope = this.root.scope ? JSON.parse(this.root.scope) : "";
      const params = { expDtoList: [] };
      entity && params.expDtoList.push(entity.expDtoList[0]);
      scope && params.expDtoList.push(scope.expDtoList[0]);
      return await commonService("getExpMemberWithCode", params).then(res => {
        return res.data.reduce((pre, next) => {
          pre[next.dimId] = next.members;
          return pre;
        }, {});
      });
    },
    async handleLanguages() {
      await this.getTextMapByDataId(this.element.nodeId);
      this.languageVisible = true;
    },
    async getTextMapByDataId(id) {
      const {
        data: { data }
      } = await precessService("getTextMapByDataId", { id });
      const list = [];
      for (const key in data) {
        list.push({
          languageCode: key,
          text: data[key]
        });
      }
      this.$set(
        this.element.nodeAttribute,
        "multiLanguages",
        this.element.nodeAttribute.multiLanguages &&
          this.element.nodeAttribute.multiLanguages.length > 0
          ? this.element.nodeAttribute.multiLanguages
          : list
      );
    },
    handlerImport(index) {
      this.executorIndex = index;
      this.userVisible = true;
    },
    handlerOk(data) {
      const executor = {
        nodeUserAuth: {},
        ...this.executor[this.executorIndex]
      };
      const userAuth = data.nodeUserAuth || {};
      for (const key in userAuth) {
        const tmp = {};
        if (isType(userAuth[key], "Array")) {
          (executor.nodeUserAuth[key] || [])
            .concat(userAuth[key])
            .forEach(item => {
              if (item) {
                tmp[item.id] = item;
              }
            });
          executor.nodeUserAuth[key] = Object.values(tmp);
        }
      }
      for (const key in data) {
        const tmp = {};
        if (isType(data[key], "Array")) {
          data[key].concat(executor[key] || []).forEach(item => {
            if (item) {
              tmp[item.id] = item;
            }
          });
          executor[key] = Object.values(tmp);
        }
      }
      this.executor[this.executorIndex] =
        Object.keys(executor).length === 0 ||
        (executor.nodeUserAuth &&
          Object.keys(executor.nodeUserAuth).length === 0)
          ? ""
          : executor;
      this.getFormData();
      this.setFormValue();
      this.$nextTick(() => this.updateFormData());
    },
    cancelMultilanguage(languageInfo) {
      this.languageVisible = false;
      languageInfo &&
        (this.element.nodeAttribute.multiLanguages = languageInfo);
    },
    checkBoxChange(checked, e) {
      this.showTaskCountdown = e.target.checked;
      this.handlerChange();
    },
    handlerChange() {
      this.$nextTick(() => {
        this.setButton();
        this.updateFormData();
        this.getCard();
        this.getCopy();
        this.getEmailCard();
        this.$nextTick(() => {
          // this.nodeForm.validateFields();
          // this.$refs["card-email"].map(item => item.hasError());
        });
      });
    },
    handlerChangeTab(activeKey) {
      const domp = this.$el.querySelector(".node-panel-content");
      const dom = this.$el.querySelector("." + activeKey);
      domp.scrollTop = dom.offsetTop;
    },
    handlerPerson() {
      this.getCard(); // 历史的
      this.getCopy();
      this.executor.push(""); // 新增的
      this.getFormData();
      this.setFormValue();
    },
    handlerEmail() {
      this.getEmailCard(); // 历史的
      this.emailCard.push({
        objectId: "",
        nodeId: this.element.nodeId,
        messageType: "node_arrived",
        sameExecutor: true,
        executor: "",
        messageTemplateId: ""
      }); // 新增的
      this.getFormData();
      this.setFormValue();
    },
    handlerDelPerson(index) {
      this.executor.splice(index, 1);
      this.getFormData();
      this.setFormValue();
    },
    handlerDelEmail(index) {
      this.emailCard.splice(index, 1);
      this.getFormData();
      this.setFormValue();
    },
    setButtonOptions(status) {
      status =
        status ||
        (this.element.nodeAttribute
          ? this.element.nodeAttribute.nodeStatus
          : "");
      const select = disableSelect[status] || [];
      this.optionsButton = this.optionsButton.map(item => {
        const temp = { ...item };
        temp.disabled = select.includes(temp.value);
        return temp;
      });
    },
    setButton() {
      const formData = this.nodeForm.getFieldsValue();
      const status = formData["nodeStatus"];
      if (status) {
        this.setButtonOptions(status);
        let nodeButton = formData.nodeButton || [];
        nodeButton = nodeButton.filter(item => {
          return !disableSelect[status].includes(item);
        });
        this.nodeForm.setFieldsValue({
          nodeButton: [...new Set(autoSelect[status].concat(nodeButton))]
        });
      }
    },
    getCard() {
      if (
        this.element.nodeExecutor.executor &&
        this.element.nodeExecutor.executor.length > 0
      ) {
        this.executor = this.element.nodeExecutor.executor;
      } else {
        this.executor = [""];
      }
    },
    getEmailCard() {
      this.emailCard = this.element.processNodeMessages;
    },
    getCopy() {
      if (this.element.nodeExecutor.copy) {
        this.copyer = this.element.nodeExecutor.copy;
      } else {
        this.copyer = "";
      }
    },
    getFormData() {
      this.$nextTick(() => {
        const field = this.nodeForm.getFieldsValue();
        const data = {
          ...this.element.nodeAttribute,
          ...this.element.nodeEmailNotice,
          ...this.element.nodeFunctionAuth,
          ...this.element
        };
        this.showTaskCountdown = !!this.element.nodeAttribute.showTaskCountdown;
        for (const index in this.executor) {
          data["card-person-type-" + index] = this.executor[index]
            ? this.getValue(this.executor[index].executeType, "seize")
            : "seize";
          data["card-person-" + index] = this.executor[index];
        }
        for (const index in this.emailCard) {
          data["card-email-" + index] = this.emailCard[index];
        }
        data.copy = this.copyer;
        for (const key in field) {
          field[key] = data[key];
        }
        this.formData = field;
      });
    },
    setDefault() {
      this.element.nodeAttribute || (this.element.nodeAttribute = {});
      this.element.nodeExecutor ||
        (this.element.nodeExecutor = { executor: [], copy: "" });
      this.element.processNodeMessages ||
        (this.element.processNodeMessages = []);
      this.element.nodeEmailNotice || (this.element.nodeEmailNotice = {});
      this.element.nodeFunctionAuth || (this.element.nodeFunctionAuth = {});
      this.element.nodeAttribute.multiLanguages ||
        (this.element.nodeAttribute.multiLanguages = []);
      const attrEmail = this.element.nodeEmailNotice;
      const attrFunc = this.element.nodeFunctionAuth;
      this.element.nodeAttribute.nodeStatus = this.getValue(
        this.element.nodeAttribute.nodeStatus,
        "not_started"
      );
      this.element.nodeAttribute.nodeButton = this.getValue(
        this.element.nodeAttribute.nodeButton,
        ["start"]
      );
      attrFunc.writeForm = this.getValue(attrFunc.writeForm, 0);
      attrFunc.validateForm = this.getValue(attrFunc.validateForm, 0);
      attrEmail.nodeStart = this.getValue(attrEmail.nodeStart, 1);
      attrEmail.nodeReturn = this.getValue(attrEmail.nodeReturn, 1);
      attrEmail.nodeTimeout = this.getValue(attrEmail.nodeTimeout, 1);
      attrEmail.nodeTimeoutWarning = this.getValue(
        attrEmail.nodeTimeoutWarning,
        1
      );
    },
    setFormValue() {
      this.$nextTick(() => {
        this.nodeForm.setFieldsValue(this.formData);
        // this.nodeForm.validateFields();
      });
    },
    updateFormData() {
      const formData = this.nodeForm.getFieldsValue();
      const temp = {
        nodeShowName: formData.nodeShowName,
        nodeType: formData.nodeType,
        nodeAttribute: {
          activityType: formData.activityType,
          executeActivity: formData.executeActivity,
          multiLanguages: this.element.nodeAttribute.multiLanguages,
          nodeShowName: formData.nodeShowName,
          nodeButton: formData.nodeButton,
          nodeStatus: formData.nodeStatus,
          nodeType: formData.nodeType,
          showTaskCountdown: !!this.showTaskCountdown,
          timeoutCtrl: formData.timeoutCtrl
        },
        nodeEmailNotice: {
          nodeReturn: formData.nodeReturn,
          nodeStart: formData.nodeStart,
          nodeTimeout: formData.nodeTimeout,
          nodeTimeoutWarning: formData.nodeTimeoutWarning
        },
        nodeExecutor: { executor: null, copy: null },
        processNodeMessages: [],
        nodeFunctionAuth: {
          validateForm: formData.validateForm,
          writeForm: formData.writeForm
        }
      };
      temp.nodeExecutor.copy = formData.copy;
      for (const key in formData) {
        if (/card\-person\-(\d+)/.test(key)) {
          const num = key.match(/\d+/)[0];
          const empty = this.checkEmpty(formData[key]);
          if (empty) {
            continue;
          }
          formData[key].executeType = formData["card-person-type-" + num];
          temp.nodeExecutor.executor || (temp.nodeExecutor.executor = []);
          temp.nodeExecutor.executor.push(formData[key]);
        }
        if (/card\-email\-(\d+)/.test(key)) {
          temp.processNodeMessages.push(formData[key]);
        }
      }
      Object.assign(this.element, temp);
      this.$emit("node-update", this.element);
    },
    // ========== tools ==========
    bindScroll() {
      const domp = this.$el.querySelector(".node-panel-content");
      const doms = Array.from(this.$el.querySelectorAll(".block"));
      domp.addEventListener("scroll", () => {
        const scrollTop = domp.scrollTop;
        for (const dom of doms) {
          if (dom.offsetTop >= scrollTop) {
            this.activeKey = dom.dataset.tab;
            break;
          }
        }
      });
    },
    getValue(value, defaultValue) {
      return value === undefined || value === "" ? defaultValue : value;
    },
    checkEmpty(value) {
      if (!value || Object.keys(value).length === 0) return true;
      let empty = true;
      for (const key in value) {
        if (value[key] && Object.keys(value[key]).length > 0) {
          empty = false;
          break;
        }
      }
      return empty;
    }
  }
};
</script>

<style lang="less" scoped>
.node-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: PingFangSC-Medium;
  padding: 0.625rem 0;
  .node-panel-tab {
    padding: 0;
    /deep/ .ant-tabs-bar {
      padding: 0 1rem;
    }
  }
  .node-panel-content {
    position: relative;
    height: 0;
    flex: 1;
    overflow-y: scroll;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      width: 0;
    }
  }
  .block {
    padding: 0 1rem 0.5rem;
    .tab-title {
      display: flex;
      align-items: center;
      padding-top: 1rem;
      font-size: 0.875rem;
      color: @yn-text-color;
      text-align: left;
      line-height: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      .sign {
        width: 2.5px;
        background: @yn-primary-color;
        margin-left: 0;
        margin-right: 0.375rem;
        border-radius: 0.625rem;
      }
    }
    &.person {
      padding: 0 0.5rem;
      .nodeForm {
        padding: 0 0.5rem 0.5rem;
      }
      .tab-title {
        margin-left: 0.5rem;
      }
    }
    &.access {
    }
    &.email {
      min-height: 100%;
    }
    .task-countdown {
      color: @yn-text-color-secondary;
      font-size: 0.75rem;
    }
  }
}

.nodeForm {
  .access-title {
    height: 1.375rem;
    font-size: 0.75rem;
    padding-left: 0.6875rem;
    color: @yn-text-color-secondary;
    text-align: left;
    line-height: 1.375rem;
    font-weight: 500;
    margin-top: 0.75rem;
    margin-bottom: 0.5rem;
  }
  .person-card {
    .import-user {
      font-size: 0.75rem;
      line-height: 1.25rem;
      color: @yn-label-color;
      margin-bottom: 0.25rem;
      margin-top: -0.25rem;
      padding-left: calc(7 / 24 * 100%);
      span {
        color: @yn-primary-color;
        cursor: pointer;
      }
    }
  }

  /deep/ .ant-form-item-label label::before {
    margin-right: 0.25rem;
    display: inline-block;
    width: 0.5rem;
    content: "";
  }
  /deep/ .ant-form-item-label label.ant-form-item-required::before {
    content: "*";
  }
  /deep/ .ant-form-item {
    margin-bottom: 0.5rem;
    &.radiosgroup {
      margin-bottom: 0px;
      .ant-form-item-control-wrapper {
        padding-left: 0.5rem;
      }
    }
  }
  /deep/ .ant-tabs-tabpane {
    padding-top: 1rem;
  }
}
.addOne {
  color: @yn-label-color;
  margin-left: 0;
  padding: 0;
  margin-bottom: 0.5rem;
  .prefix {
    margin-right: 0.5rem;
  }
}
</style>
