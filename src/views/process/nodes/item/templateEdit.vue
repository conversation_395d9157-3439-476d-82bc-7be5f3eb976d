<template>
  <yn-modal
    title=""
    dialogClass="msgTemplate"
    :visible="visible"
    :footer="null"
    width="900px"
    height="608px"
    :closable="false"
  >
    <iframe
      id="email"
      :style="{ height: '100%', width: '100%' }"
      :src="
        `${pageUrl}&title=${
          option === 'create'
            ? $t_process('message_template.create_message_template')
            : $t_process('edit_emessage_template')
        }&templateId=${msgTemplateId}`
      "
      :frameBorder="0"
      scrolling="auto"
    ></iframe>
  </yn-modal>
</template>

<script>
import DsUtils from "yn-p1/libs/utils/DsUtils";
import "yn-p1/libs/components/yn-modal/";
import { APPS } from "@/config/SETUP";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    option: {
      type: String,
      default: "create"
    },
    messageList: {
      type: Array,
      default: () => []
    },
    msgTemplateId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      pageUrl: ""
    };
  },
  watch: {
    visible: {
      handler() {
        this.getConsoleFrontEnd();
      },
      immediate: true
    }
  },
  beforeDestroy() {
    window.removeEventListener("message", this.onMessage);
  },
  created() {
    window.addEventListener("message", this.onMessage, false);
  },
  methods: {
    onMessage(message) {
      if (message.data && typeof message.data === "string") {
        const data = JSON.parse(message.data);
        if (data.actionType === "cancel") {
          this.handleCancel();
        } else if (data.actionType === "ok") {
          this.handleOk(data);
        }
      }
    },
    handleOk(data) {
      const { objectId } = data.data;
      this.$emit("ok", objectId);
      this.handleCancel();
    },
    handleCancel(e) {
      this.$emit("update:visible", false);
    },
    generateParams() {
      const fieldsMap = {
        lang: "",
        TOKEN: "",
        appId: "",
        MenuId: "",
        securityFlag: "",
        ServiceName: ""
      };
      Object.keys(fieldsMap).forEach(key => {
        fieldsMap[key] = DsUtils.getSessionStorageItem(key, {
          storagePrefix: APPS.NAME,
          isJson: true
        });
      });
      const {
        lang,
        TOKEN,
        appId,
        MenuId: menuId,
        securityFlag = true
      } = fieldsMap;
      return `?appId=${appId}&serviceName=console&menuId=${menuId}&lang=${lang}&TOKEN=${TOKEN}&securityFlag=${securityFlag}&timeDelta=-110`;
    },
    getConsoleFrontEnd() {
      const frontEnd = `${window.location.origin}`;
      const serviceName = window.top.location.pathname.match(/\/(.+)\//)
        ? window.top.location.pathname.match(/\/(.+)\//)[1]
        : "ecs_console";
      // const frontEnd = `http://**************:91`;
      const params = this.generateParams();
      const url = `${frontEnd}/${serviceName}/#message/messageTemplatePage${params}`;
      this.pageUrl = url;
    }
  }
};
</script>

<style lang="less" scoped></style>

<style lang="less">
.msgTemplate {
  .ant-modal-content {
    height: 100%;
  }
  .ant-modal-body {
    height: 100%;
    padding: 0;
    overflow: hidden;
  }
}
</style>
