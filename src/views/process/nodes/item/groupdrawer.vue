<template>
  <yn-drawer
    :title="$t_process('entity') + $t_common('and_he') + $t_process('scopes')"
    placement="right"
    :closable="true"
    width="27.5rem"
    :visible="visible"
    @close="onClose"
  >
    <yn-spin :spinning="spinning">
      <div class="drawer-content">
        <p class="member-title">
          {{ $t_process("current_template_selection") }}“
          <span class="member-dim">
            {{ $t_process("scopes") }}
          </span>
          ”{{ $t_process("detail_like_follows") }}：
        </p>
        <p v-for="item in group" :key="item.objectId" class="member-name">
          {{ item.dimMemberName }}
        </p>

        <p class="member-title">
          {{ $t_process("current_template_selection") }}“
          <span class="member-dim">
            {{ $t_process("entity") }}
          </span>
          ”{{ $t_process("detail_like_follows") }}：
        </p>
        <p v-for="item in option" :key="item.objectId" class="member-name">
          {{ item.dimMemberName }}
        </p>
      </div>
    </yn-spin>
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-spin/";
export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      spinning: false,
      group: [
        {
          objectId: "1",
          dimMemberName: "sdsd"
        },
        {
          objectId: "2",
          dimMemberName: "sdsd"
        },
        {
          objectId: "3",
          dimMemberName: "sdsd"
        },
        {
          objectId: "4",
          dimMemberName: "sdsd"
        },
        {
          objectId: "5",
          dimMemberName: "sdsd"
        },
        {
          objectId: "6",
          dimMemberName: "sdsd"
        },
        {
          objectId: "7",
          dimMemberName: "sdsd"
        },
        {
          objectId: "8",
          dimMemberName: "sdsd"
        }
      ],
      option: [
        {
          objectId: "1",
          dimMemberName: "sdsd"
        },
        {
          objectId: "2",
          dimMemberName: "sdsd"
        },
        {
          objectId: "3",
          dimMemberName: "sdsd"
        },
        {
          objectId: "4",
          dimMemberName: "sdsd"
        },
        {
          objectId: "5",
          dimMemberName: "sdsd"
        },
        {
          objectId: "6",
          dimMemberName: "sdsd"
        },
        {
          objectId: "7",
          dimMemberName: "sdsd"
        },
        {
          objectId: "8",
          dimMemberName: "sdsd"
        },
        {
          objectId: "9",
          dimMemberName: "sdsd"
        },
        {
          objectId: "10",
          dimMemberName: "sdsd"
        },
        {
          objectId: "11",
          dimMemberName: "sdsd"
        },
        {
          objectId: "81",
          dimMemberName: "sdsd"
        },
        {
          objectId: "29",
          dimMemberName: "sdsd"
        },
        {
          objectId: "130",
          dimMemberName: "sdsd"
        },
        {
          objectId: "114",
          dimMemberName: "sdsd"
        }
      ]
    };
  },
  methods: {
    onClose() {
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style lang="less" scoped>
.drawer-content {
  height: 100%;
}
.member-title {
  color: @yn-text-color-secondary;
  .member-dim {
    color: @yn-text-color;
  }
}
/deep/ .ant-drawer-body {
  height: calc(100% - 2.8125rem);
  overflow: scroll;
}
</style>
