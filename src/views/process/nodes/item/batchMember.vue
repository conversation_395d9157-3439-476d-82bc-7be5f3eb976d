<template>
  <div ref="dimListInput" :class="contClass" @click="showTransfer">
    <div class="input-content">
      <div :class="['input-text', hasMember ? 'input-text-choose' : '']">
        {{
          hasMember
            ? $t_process("selected_view_members")
            : $t_common("input_select")
        }}
      </div>
      <svg-icon
        class="icon-cont"
        type="icon-chuansuokuang"
        :isIconBtn="false"
        @click="showTransfer"
      />
    </div>
    <dimSelect
      :title="$t_process('batch_member')"
      :data="allDimOptions"
      :visible.sync="dimSelectVisible"
      :selected.sync="checked"
      @ok="handlerDimOk"
    />
    <dyTabTransfer
      :title="$t_process('batch_member')"
      :visible.sync="memberSelectVisible"
      :closeAnalysis="true"
      :closeTransfer="closeTransfer"
      :dimInfos="dimList"
      @add="addDim"
      @remove="removeDim"
    />
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-icon/";
import dyTabTransfer from "@/components/hoc/newDimensionTransfer/dyTabTransfer";
import dimSelect from "@/components/hoc/dimSelect";
import taskFlowTempService from "@/services/taskFlowTemp";
import { formatRequestParams } from "@/utils/journal.js";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import lang from "@/mixin/lang";
const { $t_common } = lang;
const dataList = {
  memberType: $t_common("member"),
  member: $t_common("member"),
  level: $t_common("level"),
  attr: $t_common("attribute"),
  subset: $t_common("subset"),
  variable: $t_common("variable")
};
export default {
  name: "BatchMember",
  components: { dyTabTransfer, dimSelect },
  model: {
    prop: "value",
    event: "change"
  },
  props: {
    value: Array
  },
  data() {
    return {
      checked: [],
      dimList: [],
      allDimOptions: [],
      allDimListMap: {},
      dimSelectVisible: false,
      memberSelectVisible: false
    };
  },
  computed: {
    dimInfo: {
      get() {
        return this.value;
      },
      set(value) {}
    },
    contClass() {
      const arr = ["dim-list-input", "expase-text"];
      return arr;
    },
    hasMember() {
      return (this.dimList || []).some(
        item => item.members && Object.values(item.members).length > 0
      );
    }
  },
  watch: {
    dimInfo: {
      async handler(newVal) {
        this.checked = (this.dimInfo || []).map(item => item.dimCode);
        await this.getAllDims();
        this.dimList = this.getDimInfos();
      },
      immediate: true
    }
  },
  methods: {
    async getAllDims() {
      await taskFlowTempService("getAllDims").then(res => {
        this.allDimListMap = {};
        this.allDimOptions = [];
        res.data.data.forEach(item => {
          const temp = {
            dimName: item.dimName,
            dimCode: item.dimCode,
            objectId: item.objectId,
            dimId: item.objectId,
            dynamicOrStatic: "dynamic",
            selectedItem: [],
            member: ""
          };
          this.allDimOptions.push({
            value: item.dimCode,
            labelSlot: item.dimName
          });
          this.allDimListMap[item.dimCode] = temp;
        });
      });
    },
    getDimInfos() {
      return (this.dimInfo || []).map(item => {
        const expJson = item.dimMemberExp
          ? AppUtils.jsonParse(item.dimMemberExp)
          : {};
        let scope =
          expJson.expDtoList && expJson.expDtoList[0]
            ? expJson.expDtoList[0].dimMemberExps || ""
            : "";

        scope = scope ? AppUtils.jsonParse(scope) : {};
        const temp = { ...this.allDimListMap[item.dimCode] };
        temp.members = formatRequestParams(scope, true);
        return temp;
      });
    },
    addDim() {
      this.dimSelectVisible = true;
    },
    removeDim(item) {
      this.checked = this.checked.filter(check => check !== item.dimCode);
      this.handlerDimOk();
      if (this.checked.length === 0) {
        this.showTransfer();
      }
    },
    showTransfer() {
      if (this.checked.length) {
        this.memberSelectVisible = true;
        this.dimSelectVisible = false;
      } else {
        this.memberSelectVisible = false;
        this.dimSelectVisible = true;
      }
    },
    handlerDimOk() {
      const history = this.dimList.reduce((pre, next) => {
        pre[next.dimCode] = next;
        return pre;
      }, {});
      const now = this.checked.map(item =>
        history[item] ? history[item] : { ...this.allDimListMap[item] }
      );
      this.dimList = now;
      this.memberSelectVisible = true;
    },
    closeTransfer(exprObjs) {
      if (!exprObjs) {
        this.memberSelectVisible = false;
        return;
      }
      const expDtoList = {};
      for (let i = 0; i < this.dimList.length; i++) {
        const { dimId } = this.dimList[i];
        let exprObj = exprObjs[i];
        const hasData = Object.keys(dataList).some(key => {
          return exprObj[key] && exprObj[key].length > 0;
        });
        if (!hasData && !exprObj.allMember) {
          exprObj = "";
        }
        expDtoList[dimId] = exprObj
          ? JSON.stringify({
            expDtoList: [
              {
                dimId: dimId,
                dimMemberExps: formatRequestParams(exprObj)
              }
            ]
          })
          : "";
      }
      this.memberSelectVisible = false;
      this.$emit(
        "change",
        this.dimList.map(item => {
          return {
            dimCode: item.dimCode,
            dimName: item.dimName,
            dimMemberExp: expDtoList[item.dimId]
          };
        })
      );
    }
  }
};
</script>
<style lang="less" scoped>
.expase-text {
  display: flex;
  align-items: center;
}
.dim-list-input {
  width: 200px;
  height: @rem32;
  overflow: hidden;
  line-height: @rem32;
  border: 1px solid @yn-border-color-base;
  border-radius: @yn-border-radius-base;
  padding: 0 @rem32 0 @rem10;
  position: relative;
  .input-content {
    width: 100%;
    height: @rem30;
    padding: 3px 0;
    display: flex;
    align-items: center;
    .placeholder {
      color: @yn-auxiliary-color;
    }
    .input-text {
      height: @rem24;
      line-height: @rem24;
      color: @yn-auxiliary-color;
    }
    .input-text-choose {
      color: @yn-text-color;
    }
  }
  .icon-cont {
    position: absolute;
    top: 0;
    display: inline-block;
    height: @rem30;
    line-height: @rem30;
    right: @rem8;
    cursor: pointer;
    color: @yn-disabled-color;
  }
  .dim-cont {
    max-width: 84px;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 0.125rem;
    padding: 0px 2px 0px 2px;
    overflow: hidden;
    color: @yn-text-color-secondary;
    background-color: @yn-background-color;
    border: 1px solid @yn-border-color-base;
    border-radius: @yn-border-radius-base;
    display: inline-block;
    height: @rem24;
    line-height: @rem24;
    cursor: default;
  }
  .hideNum {
    margin-right: 0;
  }
  &:hover {
    border-color: @yn-primary-5;
    cursor: pointer;
  }
}
</style>
