<template>
  <div class="node-panel">
    <section class="node-panel-content">
      <section class="block base">
        <header class="tab-title">
          <yn-divider type="vertical" class="sign" />{{
            $t_process("basic_settings")
          }}
        </header>
        <yn-form
          :form="nodeForm"
          class="nodeForm"
          labelAlign="left"
          v-bind="{
            labelCol: { span: 7 },
            wrapperCol: { span: 17 }
          }"
        >
          <yn-form-item :label="$t_process('batch_name')" :colon="false">
            <yn-input
              v-decorator="[
                'batchName',
                {
                  rules: [
                    {
                      required: true,
                      message: $t_common('input_message')
                    },
                    {
                      message: $t_process('field_length_exceeds_64'),
                      max: 64
                    }
                  ]
                }
              ]"
              :placeholder="$t_common('input_message')"
              @change="handlerChange"
            >
              <yn-icon slot="suffix" type="global" @click="handleLanguages" />
            </yn-input>
          </yn-form-item>
          <yn-form-item :label="$t_process('process_step_type')" :colon="false">
            <yn-select
              v-decorator="[
                'nodeType',
                {
                  rules: [
                    {
                      required: true,
                      message: $t_common('input_select')
                    }
                  ]
                }
              ]"
              :placeholder="$t_common('input_select')"
              :options="optionsType"
              :disabled="true"
              @change="handlerChange"
            />
          </yn-form-item>
          <yn-form-item :label="$t_process('batch_item')" :colon="false">
            <yn-select
              v-decorator="[
                'batchItem',
                {
                  rules: [
                    {
                      required: true,
                      message: $t_common('input_select')
                    }
                  ]
                }
              ]"
              :placeholder="$t_common('input_select')"
              :allowClear="false"
              :options="optionsStatus"
              @change="handlerChange"
            />
          </yn-form-item>
          <yn-form-item
            v-show="item.batchItem === 'DIMMEMBER'"
            :label="$t_process('batch_members')"
            :colon="false"
          >
            <batchMember
              v-decorator="[
                'batchDimMemberExp',
                {
                  rules: [
                    {
                      required: true,
                      message: $t_common('input_select')
                    }
                  ]
                }
              ]"
              @change="handlerChange"
            />
          </yn-form-item>
          <yn-form-item
            v-show="item.batchItem === 'FORM'"
            :label="$t_process('batch_form')"
            :colon="false"
          >
            <batchForm
              v-decorator="[
                'batchFormId',
                {
                  rules: [
                    {
                      required: true,
                      message: $t_common('input_select')
                    }
                  ]
                }
              ]"
              @change="handlerChange"
            />
          </yn-form-item>
          <yn-form-item :label="$t_process('batch_validation')" :colon="false">
            <ShowDimListInput
              class="diminput"
              :dimInfo="verification"
              dynamicOrStatic="dynamic"
              @getExp="val => getExp(val, 'verification')"
              @click.native="handlerChange"
            />
          </yn-form-item>
          <yn-form-item :label="$t_process('due_time')" :colon="false">
            {{ $t_process("duration_not_exceed") }}
            <yn-input-number
              v-decorator="['timeoutCtrl']"
              :min="0"
              :max="9999"
              :precision="0"
              @change="handlerChange"
            />
            {{ $t_common("maintenance_hour") }}
          </yn-form-item>
        </yn-form>
      </section>
      <section class="block email">
        <header class="tab-title">
          <yn-divider type="vertical" class="sign" />{{
            $t_process("message_notification")
          }}
        </header>
        <yn-form
          :form="nodeForm"
          class="nodeForm"
          labelAlign="left"
          v-bind="{
            labelCol: { span: 0 },
            wrapperCol: { span: 24 }
          }"
        >
          <NumberCard
            v-for="(item, index) in emailCard"
            :key="index"
            :num="index"
            class="email-card"
            @delete="handlerDelEmail"
          >
            <yn-form-item label="" :colon="false">
              <EmailContent
                ref="card-email"
                v-decorator="['card-email-' + index]"
                :ranger="ranger"
                :nodeId="element.nodeId"
                @change="handlerChange"
              />
            </yn-form-item>
          </NumberCard>
        </yn-form>
        <yn-button type="text" class="addOne" @click="handlerEmail">
          <svg-icon :isIconBtn="false" type="icon-cr_add" class="prefix" />
          {{ $t_process("add_a_set_of_message_notifications") }}
        </yn-button>
      </section>
    </section>
    <language-modal
      :languageVisible="languageVisible"
      :languageList.sync="item.multiLanguages"
      :curText="item.batchName"
      :addCurLang="true"
      @cancelMultilanguage="cancelMultilanguage"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-tabs/";
import "yn-p1/libs/components/yn-tab-pane/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-radio/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-input-number/";
import "yn-p1/libs/components/yn-divider/";
import { formatRequestParams } from "@/utils/journal.js";
import NumberCard from "./numberCard.vue";
import LanguageModal from "@/components/multilanguage";
import EmailContent from "./emailContent.vue";
import batchForm from "./batchForm.vue";
import batchMember from "./batchMember.vue";
import commonService from "@/services/common";
import { mapActions } from "vuex";
import DIM_INFO from "@/constant/dimMapping";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import ShowDimListInput from "@/components/hoc/ShowDimListInput.vue";
import precessService from "@/services/process";
import lang from "@/mixin/lang";
const { $t_process, $t_common } = lang;
const Verification = {
  dimName: $t_process("validation_accounts"),
  dimCode: "Verification",
  objectId: DIM_INFO.Verification,
  dimId: DIM_INFO.Verification,
  selectedItem: [],
  dynamicOrStatic: "dynamic"
};
export default {
  components: {
    LanguageModal,
    batchMember,
    ShowDimListInput,
    batchForm,
    EmailContent,
    NumberCard
  },
  props: {
    element: {
      type: Object,
      default: () => {
        return {};
      }
    },
    root: {
      type: Object,
      default: () => {
        return {};
      }
    },
    item: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      verification: {
        ...Verification
      },
      ranger: {},
      optionsStatus: [
        // {
        //   label: "按表单分批提交",
        //   value: "FORM"
        // }
        {
          label: $t_process("submit_by_member_batch"),
          value: "DIMMEMBER"
        }
      ],
      optionsType: [
        {
          label: $t_process("start"),
          value: "start"
        },
        {
          label: $t_process("approve"),
          value: "approval"
        },
        {
          label: $t_process("activities"),
          value: "active"
        },
        {
          label: $t_process("end"),
          value: "end"
        },
        {
          label: $t_process("batch"),
          value: "batch"
        }
      ],
      emailCard: [],
      languageVisible: false,
      nodeForm: this.$form.createForm(this, "nodeForm")
    };
  },
  watch: {
    item: {
      handler() {
        this.setDefault();
        this.getEmailCard();
        this.getFormData();
        this.setFormValue();
      },
      immediate: true
    }
  },
  async created() {
    this.ranger = await this.getRanger();
  },
  methods: {
    ...mapActions({
      setLang: "common/getEnableLanguages"
    }),
    async getRanger() {
      const entity = this.root.entity ? JSON.parse(this.root.entity) : "";
      const scope = this.root.scope ? JSON.parse(this.root.scope) : "";
      const params = { expDtoList: [] };
      entity && params.expDtoList.push(entity.expDtoList[0]);
      scope && params.expDtoList.push(scope.expDtoList[0]);
      return await commonService("getExpMemberWithCode", params).then(res => {
        return res.data.reduce((pre, next) => {
          pre[next.dimId] = next.members;
          return pre;
        }, {});
      });
    },
    async handleLanguages() {
      await this.setLang();
      this.getTextMapByDataId();
      this.languageVisible = true;
    },
    async getTextMapByDataId() {
      const {
        data: { data }
      } = await precessService("getTextMapByDataId", { id: this.item.batchId });
      const list = [];
      for (const key in data) {
        list.push({
          languageCode: key,
          text: data[key]
        });
      }
      this.$set(
        this.item,
        "multiLanguages",
        this.item.multiLanguages && this.item.multiLanguages.length > 0
          ? this.item.multiLanguages
          : list
      );
    },
    cancelMultilanguage(languageInfo) {
      this.languageVisible = false;
      languageInfo && this.$set(this.item, "multiLanguages", languageInfo);
      this.$emit("update");
    },
    getExp(val, dimCode) {
      const dataList = {
        memberType: $t_common("member"),
        member: $t_common("member"),
        level: $t_common("level"),
        attr: $t_common("attribute"),
        subset: $t_common("subset"),
        variable: $t_common("variable")
      };
      const hasData = Object.keys(dataList).some(key => {
        return val[key] && val[key].length > 0;
      });
      if (!hasData && !val.allMember) {
        val = "";
      }
      this.$set(this[dimCode], "members", val);
      this.$set(
        this.item,
        "batchVerifyExp",
        val
          ? JSON.stringify({
            expDtoList: [
              {
                dimId: Verification.dimId,
                dimMemberExps: formatRequestParams(val)
              }
            ]
          })
          : ""
      );
    },
    getFormData() {
      this.$nextTick(() => {
        this.setAccountMember();
        const field = this.nodeForm.getFieldsValue();
        const data = {
          ...this.item
        };
        for (const index in this.emailCard) {
          data["card-email-" + index] = this.emailCard[index];
        }
        for (const key in field) {
          field[key] = data[key];
        }
        this.formData = field;
      });
    },
    setFormValue() {
      this.$nextTick(() => {
        this.nodeForm.setFieldsValue(this.formData);
      });
    },
    getEmailCard() {
      this.emailCard = this.item.processNodeMessages;
    },
    handlerEmail() {
      this.getEmailCard(); // 历史的
      this.emailCard.push({
        objectId: "",
        nodeId: this.element.nodeId,
        messageType: "node_arrived",
        sameExecutor: true,
        executor: "",
        messageTemplateId: ""
      }); // 新增的
      this.getFormData();
      this.setFormValue();
    },
    handlerDelEmail(index) {
      this.emailCard.splice(index, 1);
      this.getFormData();
      this.setFormValue();
    },
    setAccountMember() {
      const scopelist = this.item.batchVerifyExp
        ? AppUtils.jsonParse(this.item.batchVerifyExp)
        : {};

      let scope =
        scopelist.expDtoList && scopelist.expDtoList[0]
          ? scopelist.expDtoList[0].dimMemberExps || ""
          : "";

      scope = scope ? AppUtils.jsonParse(scope) : {};
      this.$set(this.verification, "members", formatRequestParams(scope, true));
    },
    setDefault() {
      this.item.processNodeMessages || (this.item.processNodeMessages = []);
      this.item.batchDimMemberExp || (this.item.batchDimMemberExp = []);
      this.item.batchFormId || (this.item.batchFormId = []);
      this.item.batchVerifyExp || (this.item.batchVerifyExp = "");
      this.item.nodeType || (this.item.nodeType = "batch");
      this.item.batchItem || (this.item.batchItem = "DIMMEMBER");
      this.item.timeoutCtrl || (this.item.timeoutCtrl = 0);
    },
    updateFormData() {
      const formData = this.nodeForm.getFieldsValue();
      const temp = {
        batchName: formData.batchName,
        batchItem: formData.batchItem,
        batchFormId: formData.batchFormId,
        batchVerifyExp: this.item.batchVerifyExp,
        batchDimMemberExp: formData.batchDimMemberExp,
        nodeType: "batch",
        timeoutCtrl: formData.timeoutCtrl,
        processNodeMessages: []
      };
      for (const key in formData) {
        if (/card\-email\-(\d+)/.test(key)) {
          temp.processNodeMessages.push(formData[key]);
        }
      }
      Object.assign(this.item, temp);
      this.$emit("update");
    },
    handlerChange() {
      this.$nextTick(() => {
        this.updateFormData();
        this.getEmailCard();
      });
    }
  }
};
</script>

<style lang="less" scoped>
.node-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0.625rem 0;

  .node-panel-content {
    position: relative;
    height: 0;
    flex: 1;
    overflow-y: scroll;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      width: 0;
    }
  }
  .block {
    padding: 0 1rem 0.5rem;
    .tab-title {
      display: flex;
      align-items: center;
      padding-top: 1rem;
      font-size: 0.875rem;
      color: @yn-text-color;
      text-align: left;
      line-height: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      .sign {
        width: 2.5px;
        background: @yn-primary-color;
        margin-left: 0;
        margin-right: 0.375rem;
        border-radius: 0.625rem;
      }
    }
    &.email {
      min-height: 100%;
    }
  }
}
.nodeForm {
  .access-title {
    height: 1.375rem;
    font-size: 0.75rem;
    padding-left: 0.6875rem;
    color: @yn-text-color-secondary;
    text-align: left;
    line-height: 1.375rem;
    font-weight: 500;
    margin-top: 0.75rem;
    margin-bottom: 0.5rem;
  }
  .person-card {
    .import-user {
      font-size: 0.75rem;
      line-height: 1.25rem;
      color: @yn-label-color;
      margin-bottom: 0.25rem;
      margin-top: -0.25rem;
      padding-left: calc(7 / 24 * 100%);
      span {
        color: @yn-primary-color;
        cursor: pointer;
      }
    }
  }

  /deep/ .ant-form-item-label label::before {
    margin-right: 0.25rem;
    display: inline-block;
    width: 0.5rem;
    content: "";
  }
  /deep/ .ant-form-item-label label.ant-form-item-required::before {
    content: "*";
  }
  /deep/ .ant-form-item {
    margin-bottom: 0.5rem;
    &.radiosgroup {
      margin-bottom: 0px;
      .ant-form-item-control-wrapper {
        padding-left: 0.5rem;
      }
    }
  }
  /deep/ .ant-tabs-tabpane {
    padding-top: 1rem;
  }
}
</style>
