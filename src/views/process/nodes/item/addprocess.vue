<template>
  <yn-modal
    :visible="visible"
    :title="$t_process('add_process')"
    width="25rem"
    :bodyStyle="{ display: 'flex', 'align-items': 'center' }"
    @cancel="cancelEvent"
  >
    <yn-form
      :form="addForm"
      class="exportform"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 18 }
      }"
    >
      <yn-form-item :label="$t_process('process_name')" :colon="false">
        <yn-input
          v-decorator="[
            'name',
            {
              rules: [
                { required: true, message: $t_common('input_message') },
                { max: 64, message: $t_process('field_length_exceeds_64') }
              ]
            }
          ]"
          :placeholder="$t_common('input_message')"
          @change="syncLang"
        >
          <yn-icon slot="suffix" type="global" @click="handleLanguages" />
        </yn-input>
      </yn-form-item>
      <yn-form-item
        v-if="data.isGroup"
        :label="$t_process('process_group')"
        :colon="false"
      >
        <yn-select
          v-decorator="[
            'processGroup',
            {
              initialValue: defaultGroup,
              rules: [{ required: true, message: $t_common('input_select') }]
            }
          ]"
          :allowClear="false"
          :placeholder="$t_common('input_select')"
          :options="options"
        />
      </yn-form-item>
    </yn-form>
    <language-modal
      :languageVisible="languageVisible"
      :languageList.sync="languageList"
      :curText="curText"
      :addCurLang="true"
      @cancelMultilanguage="cancelMultilanguage"
    />
    <template slot="footer">
      <yn-button key="back" @click="cancelEvent">
        {{ $t_common("cancel") }}
      </yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="loading"
        :disabled="loading"
        @click="okEvent"
      >
        {{ $t_common("save") }}
      </yn-button>
    </template>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-option/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import "yn-p1/libs/components/yn-icon-button/";
import "yn-p1/libs/components/yn-icon/";
import precessService from "@/services/process";
import LanguageModal from "@/components/multilanguage";
import { mapActions } from "vuex";
export default {
  components: { LanguageModal },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      languageVisible: false,
      languageList: [],
      curText: "",
      defaultGroup: "",
      loading: false,
      options: [],
      addForm: this.$form.createForm(this, "addForm")
    };
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.$nextTick(() => {
            this.addForm.resetFields();
            this.getallGroup();
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    async getallGroup() {
      const {
        data: { data }
      } = await precessService("getallGroup");
      this.options = data.map(item => {
        if (item.defaultGroup === true) {
          this.defaultGroup = item.groupId;
        }
        return {
          label: item.groupName,
          value: item.groupId
        };
      });
    },
    ...mapActions({
      setLang: "common/getEnableLanguages"
    }),
    syncLang(e) {
      this.curText = e.target.value;
    },
    handleLanguages() {
      this.languageVisible = true;
      this.setLang();
      // this.languageList = init()
    },
    cancelMultilanguage(languageInfo) {
      this.languageVisible = false;
      this.languageList = languageInfo;
    },
    cancelEvent() {
      this.$emit("update:visible", false);
    },
    okEvent() {
      this.addForm.validateFields((err, value) => {
        if (!err) {
          this.loading = true;
          precessService("addprocess", {
            processGroup: this.defaultGroup,
            ...value,
            multiLanguages: this.languageList
          })
            .then(res => {
              UiUtils.successMessage(
                this.$t_process("process_template") +
                  this.$t_common("add_success")
              );
              this.cancelEvent();
              this.$emit("ok", "addprocess", res.data.data);
            })

            .finally(e => {
              this.loading = false;
            });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.exportform {
  width: 100%;
}
.item {
  margin-right: 3.125rem;
  width: 4.0625rem;
  height: @rem22;
  font-family: PingFangSC-Regular;
  font-size: @rem14;
  color: @yn-text-color-secondary;
  text-align: left;
  line-height: @rem22;
  font-weight: 400;
}
/deep/ .ant-modal-body {
  min-height: auto;
}
/deep/ .ant-form-item:last-of-type {
  margin-bottom: 0;
}
</style>
