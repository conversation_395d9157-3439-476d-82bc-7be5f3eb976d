<template>
  <yn-tree
    :key="key"
    :checkedKeys="checkedKeys"
    checkable
    :replaceFields="replaceFields"
    :autoExpandParent="autoExpandParent"
    :selectable="false"
    :treeData="data"
    :loadData="onLoadData"
    @check="onCheck"
  />
</template>
<script>
import "yn-p1/libs/components/yn-tree/";
export default {
  props: {
    replaceFields: {
      type: Object,
      default: () => {
        return { children: "children", title: "title", key: "key" };
      }
    },
    checked: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: ""
    },
    data: {
      type: Array,
      default: () => []
    },
    // eslint-disable-next-line vue/require-default-prop
    loader: {
      type: Function
    }
  },
  data() {
    return {
      autoExpandParent: true,
      checkedItems: [...this.checked],
      checkMap: {},
      key: "",
      cacheSelected: {}
    };
  },
  computed: {
    checkedKeys: {
      get() {
        return this.checked.map(item => item.id);
      },
      set(value) {
        this.$emit(
          "update:checked",
          this.checkedItems.map(item => {
            item.type = this.type;
            return item;
          })
        );
      }
    }
  },
  watch: {
    checked() {
      this.checkMap = {};
      this.checkedItems = [...this.checked];
      this.checkedItems.forEach(node => {
        this.checkMap[node.id] = node;
      });
    },
    data(newval, oldval) {
      if (newval !== oldval) {
        this.key = Date.now();
      }
    }
  },
  methods: {
    onLoadData(treeNode) {
      return new Promise(resolve => {
        if (!this.loader) {
          resolve();
          return;
        }
        this.loader(treeNode.dataRef).then(res => {
          treeNode.dataRef.children = res;
          this.data.splice(0, this.data.length, ...this.data);
          resolve();
        });
      });
    },
    onCheck(checkedKeys, e) {
      if (e.checked) {
        this.checkedItems.forEach(node => {
          this.checkMap[node.id] = node;
        });
        e.checkedNodes.forEach(node => {
          this.checkMap[node.key] = {
            id: node.key,
            name: node.data.props.title
          };
        });
      } else {
        this.checkedItems.forEach(node => {
          if (node.id !== e.node.$vnode.data.key) {
            this.checkMap[node.id] = node;
          } else {
            delete this.checkMap[node.id];
          }
        });
      }
      this.checkedItems = Object.values(this.checkMap);
      this.checkedKeys = Object.keys(this.checkMap);
    }
  }
};
</script>
