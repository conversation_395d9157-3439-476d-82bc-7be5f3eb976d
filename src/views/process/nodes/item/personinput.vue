<template>
  <section>
    <yn-input
      v-model="valuePro"
      :placeholder="$t_common('input_select')"
      :allowClear="false"
      @click="handlerPeople"
    >
      <SvgIcon
        slot="suffix"
        :isIconBtn="false"
        type="icon-chuansuokuang"
        @click.native.stop="handlerPeople"
      />
    </yn-input>
    <yn-modal
      v-if="innerVisible"
      :visible="innerVisible"
      :class="['person-input', className]"
      :title="myTitle"
      width="45.5rem"
      :bodyStyle="{ display: 'flex', 'align-items': 'center' }"
      @cancel="cancelEvent"
    >
      <section class="person-model">
        <section class="person-menu">
          <yn-tree-menu
            width="100%"
            :treeConfig="treeConfig"
            :treePanelSkeleton="treePanelSkeleton"
            @select="onSelect"
          />
        </section>
        <section class="person-select">
          <section class="select-area">
            <section v-show="current === 'person'" class="area-person">
              <yn-tabs :defaultActiveKey="activeKey" @change="handlerChange">
                <yn-tab-pane key="person" :tab="$t_process('users')">
                  <div class="tabs-content">
                    <yn-input
                      v-model="searchPerson"
                      class="person-search"
                      :placeholder="$t_common('please_enter_search_content')"
                      @keydown.enter="
                        loadPerson({ hasNextPage: true, pageNum: 0 })
                      "
                    >
                      <yn-icon
                        slot="suffix"
                        type="search"
                        @click="loadPerson({ hasNextPage: true, pageNum: 0 })"
                      />
                    </yn-input>
                    <PersonTree
                      type="person"
                      :checked.sync="form.person"
                      :data="personData"
                      :replaceFields="{ title: 'userName', key: 'userId' }"
                    />
                  </div>
                </yn-tab-pane>
                <yn-tab-pane
                  key="group"
                  :tab="$t_process('users_by_organization')"
                >
                  <div class="tabs-content">
                    <yn-input
                      v-model="searchOption"
                      class="person-search"
                      :placeholder="$t_common('please_enter_search_content')"
                      @keydown.enter="
                        loadOrgUser({ hasNextPage: true, pageNum: 0 })
                      "
                    >
                      <yn-icon
                        slot="suffix"
                        type="search"
                        @click="loadOrgUser({ hasNextPage: true, pageNum: 0 })"
                      />
                    </yn-input>
                    <PersonTree
                      type="option"
                      :checked.sync="form.option"
                      :data="optionData"
                      :loader="getOrgUserManageAccessTree"
                    />
                  </div>
                </yn-tab-pane>
              </yn-tabs>
            </section>
            <section v-show="current === 'role'" class="area-role">
              <yn-input
                v-model="searchRole"
                class="person-search"
                :placeholder="$t_common('please_enter_search_content')"
                @keydown.enter="loadRole({ hasNextPage: true, pageNum: 0 })"
              >
                <yn-icon
                  slot="suffix"
                  type="search"
                  @click="loadRole({ hasNextPage: true, pageNum: 0 })"
                />
              </yn-input>
              <PersonTree
                type="role"
                :data="roleData"
                :checked.sync="form.role"
                :replaceFields="{
                  title: 'roleName',
                  key: 'roleId',
                  children: 'children'
                }"
              />
            </section>
            <section v-show="current === 'group'" class="area-group">
              <yn-input
                v-model="searchGroup"
                class="person-search"
                :placeholder="$t_common('please_enter_search_content')"
                @keydown.enter="loadGroup({ hasNextPage: true, pageNum: 0 })"
              >
                <yn-icon
                  slot="suffix"
                  type="search"
                  @click="loadGroup({ hasNextPage: true, pageNum: 0 })"
                />
              </yn-input>
              <PersonTree
                type="group"
                :checked.sync="form.group"
                :data="groupData"
                :replaceFields="{ title: 'groupName', key: 'groupId' }"
              />
            </section>
            <section v-show="current === 'work'" class="area-work">
              <yn-input
                v-show="false"
                v-model="searchWork"
                class="person-search"
                :placeholder="$t_common('please_enter_search_content')"
                @keydown.enter="loadManage"
              >
                <yn-icon slot="suffix" type="search" @click="loadManage" />
              </yn-input>
              <PersonTree
                type="work"
                :checked.sync="form.work"
                :data="workData"
                :loader="getPostManageAccessTree"
              />
            </section>
          </section>
          <section class="selected-area">
            <div class="summary">
              {{ $t_common("text_has_chosen") }} : {{ getDataNum() }}
              <yn-button type="text" class="deleteall" @click="deleteAll">
                {{ $t_common("clear") }}
              </yn-button>
            </div>
            <CollapseList
              :data="formView"
              :dimInfos="dimInfos"
              v-bind="$attrs"
              @change="handerChange"
              @changeDim="handerChangeDim"
            />
          </section>
        </section>
      </section>
      <template slot="footer">
        <yn-button key="back" @click="cancelEvent">
          {{ $t_common("cancel") }}
        </yn-button>
        <yn-button key="submit" type="primary" @click="okEvent">
          {{ $t_common("save") }}
        </yn-button>
      </template>
    </yn-modal>
  </section>
</template>

<script>
// import "yn-p1/libs/components/yn-tree/";
import "yn-p1/libs/components/yn-tree-menu/";
import PersonTree from "./persontree";
import SvgIcon from "@/components/ui/SvgIcon.vue";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-tab-pane/";
import "yn-p1/libs/components/yn-tabs/";
import CollapseList from "./collapselist";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import processService from "@/services/process";
import DIM_INFO from "@/constant/dimMapping";
import { formatRequestParams } from "@/utils/journal.js";
import _cloneDeep from "lodash/cloneDeep";
import lang from "@/mixin/lang";
const { $t_process } = lang;
const Entity = {
  dimName: $t_process("entity"),
  dimCode: "Entity",
  objectId: DIM_INFO.Entity,
  dimId: DIM_INFO.Entity,
  selectedItem: [],
  dynamicOrStatic: "dynamic"
};
const Scope = {
  dimName: $t_process("scopes"),
  dimCode: "Scope",
  objectId: DIM_INFO.Scope,
  dimId: DIM_INFO.Scope,
  dynamicOrStatic: "dynamic",
  selectedItem: []
};
const FieldMap = {
  group: "designatedGroup",
  role: "processRole",
  work: "designatedPost",
  person: "user",
  option: "orgUser"
};
export default {
  components: { SvgIcon, PersonTree, CollapseList },
  model: {
    prop: "value",
    event: "change"
  },
  props: {
    visible: {
      type: Boolean
    },
    className: {
      type: String,
      default: ""
    },
    // eslint-disable-next-line vue/require-default-prop
    value: {
      type: [Object, String]
    },
    title: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      valuePro: "",
      form: {
        group: [],
        role: [],
        work: [],
        person: [],
        option: []
      },
      dimInfos: [],
      searchkey: "",
      innerVisible: false,
      treePanelSkeleton: {
        loading: false
      },
      memberExps: {},
      searchPerson: "",
      personData: [],
      searchOption: "",
      optionData: [],
      searchRole: "",
      roleData: [],
      searchGroup: "",
      groupData: [],
      searchWork: "",
      workData: [],
      treeConfig: {
        selectedKeys: ["person"],
        treeData: [
          {
            title: this.$t_process("select_users"),
            key: "person",
            isLeaf: true
          },
          {
            title: this.$t_process("select_roles"),
            key: "role",
            isLeaf: true
          },
          {
            title: this.$t_process("select_groups"),
            key: "group",
            isLeaf: true
          },
          {
            title: this.$t_process("select_positions"),
            key: "work",
            isLeaf: true
          }
        ]
      },
      activeKey: "person",
      selected: ["person"],
      $groupTreeDataCache: {
        list: [],
        pageNum: 0,
        pageSize: 100,
        hasNextPage: true
      },
      $roleTreeDataCache: {
        list: [],
        pageNum: 0,
        pageSize: 100,
        hasNextPage: true
      },
      $personTreeDataCache: {
        list: [],
        pageNum: 0,
        pageSize: 100,
        hasNextPage: true
      }
    };
  },
  computed: {
    myTitle() {
      return this.title || this.$t_process("select_executors");
    },
    formView: {
      get() {
        return {
          group: this.form.group,
          role: this.form.role,
          work: this.form.work,
          person: this.form.person.concat(this.form.option)
        };
      },
      set() {}
    },
    current() {
      return this.selected[0];
    }
  },
  watch: {
    visible: {
      handler(value) {
        this.innerVisible = value;
      }
    },
    innerVisible(value) {
      if (value) {
        this.searchPerson = "";
        this.searchRole = "";
        this.searchOption = "";
        this.searchGroup = "";
        this.searchWork = "";
        this.$data.$groupTreeDataCache = {
          list: [],
          pageNum: 0,
          pageSize: 100,
          hasNextPage: true
        };
        this.$data.$roleTreeDataCache = {
          list: [],
          pageNum: 0,
          pageSize: 100,
          hasNextPage: true
        };
        this.$data.$personTreeDataCache = {
          list: [],
          pageNum: 0,
          pageSize: 100,
          hasNextPage: true
        };
        this.loadPerson();
        this.loadOrgUser();
        this.loadGroup();
        this.loadManage();
        this.loadRole();
      }
      this.$emit("update:visible", value);
      this.form = this.getForm();
      this.dimInfos = this.getDimInfos();
      this.getMemberExps();
    },
    value: {
      deep: true,
      immediate: true,
      handler: function(newValue) {
        this.form = this.getForm();
        this.dimInfos = this.getDimInfos();
        this.getMemberExps();
        const isEmpty = this.checkEmpty(this.form);
        this.valuePro = !isEmpty
          ? this.$t_process("selected_view_members")
          : "";
      }
    }
  },
  methods: {
    async loadPerson(last = this.$data.$personTreeDataCache) {
      if (!last.hasNextPage) return;
      const params = {
        condition: this.searchPerson,
        pageNum: last.pageNum + 1,
        pageSize: 100
      };
      const data = await this.loadUserByCondition(params);
      this.personData = last.pageNum ? this.personData.concat(data) : data;
    },
    async loadRole(last = this.$data.$roleTreeDataCache) {
      if (!last.hasNextPage) return;
      const params = {
        pageNum: last.pageNum + 1,
        pageSize: 100,
        queryValue: this.searchRole
      };
      const data = await this.getRoleListForPage(params);
      this.roleData = last.pageNum ? this.roleData.concat(data) : data;
    },
    async loadGroup(last = this.$data.$groupTreeDataCache) {
      if (!last.hasNextPage) return;
      const params = {
        pageNum: last.pageNum + 1,
        pageSize: 100,
        queryValue: this.searchGroup
      };
      const data = await this.getGroupListForPage(params);
      this.groupData = last.pageNum ? this.groupData.concat(data) : data;
    },
    async loadOrgUser() {
      const data = await this.getOrgUserManageAccessTree();
      this.optionData = data;
    },
    async loadManage() {
      const data = await this.getPostManageAccessTree();
      this.workData = data;
    },
    async loadUserByCondition(params = {}) {
      const {
        data: {
          data,
          data: { list }
        }
      } = await processService("loadUserByCondition", params);
      this.$data.$personTreeDataCache = data;
      return list.map(person => {
        person.isLeaf = true;
        return person;
      });
    },
    async getOrgUserManageAccessTree(params = { key: "" }) {
      const {
        data: { data }
      } = await processService("getOrgUserManageAccessTree", {
        dimObjectId: params.key,
        isFastShow: true,
        isParentId: !!params.key,
        isShowCode: true,
        isShowDisable: false,
        searchInfo: this.searchOption
      });
      return data.map(item => {
        item.selectable = false;
        item.key = item.data.objectId;
        item.title = item.data.name;
        item.children = null;
        if (!item.isLeaf || item.data.typeCode !== "SYS_USER") {
          item.disableCheckbox = true;
        }
        return item;
      });
    },
    async getGroupListForPage(params = {}) {
      const {
        data: {
          data,
          data: { list }
        }
      } = await processService("getGroupListForPage", params);
      this.$data.$groupTreeDataCache = data;
      return list.map(item => {
        item.isLeaf = true;
        return item;
      });
    },
    async getPostManageAccessTree(params = { key: "" }) {
      const {
        data: { data }
      } = await processService("getPostManageAccessTree", {
        dimObjectId: params.key,
        isFastShow: true,
        isParentId: this.searchWork ? false : !!params.key,
        isShowCode: true,
        isShowDisable: false,
        searchInfo: this.searchWork
      });
      return data.map(item => {
        item.selectable = false;
        if (!item.isLeaf) {
          item.disableCheckbox = true;
        }
        return item;
      });
    },
    async getRoleListForPage(params = {}) {
      const {
        data: {
          data,
          data: { list }
        }
      } = await processService("getRoleListForPage", params);
      this.$data.$roleTreeDataCache = data;
      return list.map(item => {
        item.disableCheckbox = true;
        item.children.map(child => {
          child.isLeaf = true;
          return child;
        });
        return item;
      });
    },
    getForm() {
      return {
        group: this.genForm(this.value, "designatedGroup", "group"),
        role: this.genForm(this.value, "processRole", "role"),
        work: this.genForm(this.value, "designatedPost", "work"),
        person: this.genForm(this.value, "user", "person"),
        option: this.genForm(this.value, "orgUser", "option")
      };
    },
    genForm(value, field, type) {
      if (!value || !value[field]) return [];
      return value[field].map(item => {
        item.type = type;
        return item;
      });
    },
    getMemberExps() {
      this.memberExps =
        this.value && this.value.nodeUserAuth
          ? _cloneDeep(this.value.nodeUserAuth)
          : {};
      for (const key in this.dimInfos) {
        const field = FieldMap[key];
        this.memberExps[field] || (this.memberExps[field] = []);
      }
    },
    getDimInfos() {
      const { nodeUserAuth } = this.value || {};
      return {
        group: this.genDimInfo(nodeUserAuth, "designatedGroup"),
        role: this.genDimInfo(nodeUserAuth, "processRole"),
        work: this.genDimInfo(nodeUserAuth, "designatedPost"),
        person: this.genDimInfo(nodeUserAuth, "user"),
        option: this.genDimInfo(nodeUserAuth, "orgUser")
      };
    },
    genDimInfo(data, field) {
      if (field && (!data || !data[field])) {
        return [];
      }
      const arr = field ? data[field] : data;
      return arr.map(item => {
        const entity = item.entity ? AppUtils.jsonParse(item.entity) : {};
        const scope = item.scope ? AppUtils.jsonParse(item.scope) : {};
        const entityExp =
          entity.expDtoList && entity.expDtoList[0]
            ? entity.expDtoList[0].dimMemberExps || ""
            : "";
        const scopeExp =
          scope.expDtoList && scope.expDtoList[0]
            ? scope.expDtoList[0].dimMemberExps || ""
            : "";
        const scopeMember = scopeExp ? AppUtils.jsonParse(scopeExp) : {};
        const entityMember = entityExp ? AppUtils.jsonParse(entityExp) : {};
        return {
          id: item.id,
          member: [
            {
              ...Entity,
              members: formatRequestParams(entityMember, true)
            },
            {
              ...Scope,
              members: formatRequestParams(scopeMember, true)
            }
          ]
        };
      });
    },
    cancelEvent() {
      this.innerVisible = false;
    },
    handlerChange() {},
    okEvent() {
      const isEmpty = this.checkEmpty(this.form);
      this.processRefer();
      this.$emit(
        "change",
        isEmpty
          ? ""
          : {
            designatedGroup: this.form.group,
            processRole: this.form.role,
            designatedPost: this.form.work,
            user: this.form.person,
            orgUser: this.form.option,
            nodeUserAuth: { ...this.memberExps }
          }
      );
      this.innerVisible = false;
    },
    handlerPeople() {
      this.innerVisible = true;
    },
    handerChangeDim(data, type) {
      const field = FieldMap[type];
      const index = this.dimInfos[type].findIndex(item => item.id === data.id);
      const expIndex = this.memberExps[field].findIndex(
        item => item.id === data.id
      );
      if (index > -1) {
        if (expIndex > -1) {
          this.memberExps[field][expIndex] = data;
        } else {
          this.memberExps[field].push(data);
        }
        this.$set(this.dimInfos[type], index, this.genDimInfo([data])[0]);
      } else {
        this.memberExps[field].push(data);
        this.dimInfos[type].push(...this.genDimInfo([data]));
      }
    },
    handerChange() {
      const person = this.form.person.filter(item => {
        return this.formView.person.includes(item);
      });
      const option = this.form.option.filter(item => {
        return this.formView.person.includes(item);
      });
      const group = [...this.formView.group];
      const role = [...this.formView.role];
      const work = [...this.formView.work];
      this.form = {
        group: group,
        role: role,
        work: work,
        person: person,
        option: option
      };
    },
    processRefer() {
      for (const group in this.form) {
        const temp = [];
        const authMap = this.memberExps[FieldMap[group]].reduce((pre, next) => {
          pre[next.id] = next;
          return pre;
        }, {});
        for (const sItem of this.form[group]) {
          if (authMap[sItem.id]) {
            temp.push(authMap[sItem.id]);
          } else {
            temp.push({
              id: sItem.id,
              entity: "",
              scope: ""
            });
          }
        }
        this.memberExps[FieldMap[group]] = temp;
      }
    },
    deleteAll() {
      const temp = {};
      for (const group in this.form) {
        temp[group] = [];
        this.memberExps[FieldMap[group]] = [];
      }
      this.form = temp;
    },
    checkEmpty(value) {
      if (!value || Object.keys(value).length === 0) return true;
      let empty = true;
      for (const key in value) {
        if (value[key] && Object.keys(value[key]).length > 0) {
          empty = false;
          break;
        }
      }
      return empty;
    },
    onSelect(selectedKeys, info) {
      this.treeConfig.selectedKeys = selectedKeys;
      this.selected = selectedKeys;
    },
    getDataNum() {
      return (
        this.form.group.length +
        this.form.work.length +
        this.form.person.length +
        this.form.role.length +
        this.form.option.length
      );
    }
  }
};
</script>

<style lang="less" scoped>
.person-model {
  display: flex;
  height: 27.25rem;
  width: 100%;
  border: 1px solid rgba(225, 229, 235, 1);
  .tabs-content {
    padding: 0.5rem;
    height: 100%;
    overflow: auto;
  }
  /deep/ .ant-tabs {
    height: 100%;
  }
  /deep/ .ant-tabs-content {
    height: calc(100% - 2.5rem);
  }
  /deep/ .ant-tabs-tabpane-active {
    height: 100%;
  }
  /deep/ .ant-menu-item {
    margin-top: 0;
  }
  /deep/ .ant-tabs-bar {
    margin: 0;
  }
  /deep/ .ant-tabs-tab {
    height: 2.5rem;
  }
  /deep/ .ant-tabs-nav-wrap {
    margin-bottom: 0;
  }
  /deep/ .yn-tree-menu {
    min-width: 100%;
  }
}
.person-menu {
  width: 8.5rem;
  flex-shrink: 0;
  border-right: 1px solid rgba(225, 229, 235, 1);
  /deep/ .ant-menu {
    border: none;
  }
}
.person-menu {
  /deep/ .ant-tree li {
    height: 2.75rem;
    line-height: 2.75rem;
    * {
      height: 2.75rem;
    }
  }
  /deep/ .ant-tree-node-content-wrapper {
    line-height: 2.75rem;
  }
  /deep/ .ant-tree-node-content-wrapper::before {
    height: 2.75rem !important;
  }

  /deep/ .ant-tree-node-content-wrapper::after {
    height: 2.75rem !important;
  }
}
.person-select {
  display: flex;
  flex: 1;
  width: 0;
  .selected-area,
  .select-area {
    flex-shrink: 0;
    width: 50%;
    height: 100%;
  }
  .selected-area {
    border-left: 1px solid rgba(225, 229, 235, 1);
  }
  .area-person {
    height: 100%;
    overflow: auto;
    padding: 0.5rem;
  }
  .area-group {
    height: 100%;
    overflow: auto;
    padding: 0.5rem;
  }
  .area-work {
    height: 100%;
    overflow: auto;
    padding: 0.5rem;
  }
  .area-role {
    height: 100%;
    overflow: auto;
    padding: 0.5rem;
  }
  .summary {
    display: flex;
    align-items: center;
    height: 2.5rem;
    padding-left: 1.25rem;
    border-bottom: 1px solid rgba(225, 229, 235, 1);
    .deleteall {
      margin-left: auto;
    }
  }
}
</style>
