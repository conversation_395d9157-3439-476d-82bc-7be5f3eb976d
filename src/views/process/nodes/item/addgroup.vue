<template>
  <yn-modal
    :visible="visible"
    :title="data.title"
    width="25rem"
    :bodyStyle="{ display: 'flex', 'align-items': 'center' }"
    @cancel="cancelEvent"
  >
    <yn-form
      :form="addForm"
      class="exportform"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 18 }
      }"
    >
      <yn-form-item :label="$t_process('group_name')" :colon="false">
        <yn-input
          v-decorator="[
            'name',
            {
              rules: [
                { required: true, message: $t_common('input_message') },
                { max: 64, message: $t_process('field_length_exceeds_64') }
              ]
            }
          ]"
          :placeholder="$t_common('input_message')"
          @change="handlerName"
        >
          <yn-icon slot="suffix" type="global" @click="handleLanguages" />
        </yn-input>
      </yn-form-item>
    </yn-form>
    <template slot="footer">
      <yn-button key="back" @click="cancelEvent">
        {{ $t_common("cancel") }}
      </yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="loading"
        :disabled="loading"
        @click="okEvent"
      >
        {{ $t_common("save") }}
      </yn-button>
    </template>
    <language-modal
      :languageVisible="languageVisible"
      :languageList.sync="languageList"
      :curText="curText"
      :addCurLang="true"
      @cancelMultilanguage="cancelMultilanguage"
    />
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-option/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import "yn-p1/libs/components/yn-icon-button/";
import "yn-p1/libs/components/yn-icon/";
import LanguageModal from "@/components/multilanguage";
import precessService from "@/services/process";
import { mapActions } from "vuex";
export default {
  components: { LanguageModal },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      languageVisible: false,
      languageList: [],
      curText: "",
      loading: false,
      addForm: this.$form.createForm(this, "addForm")
    };
  },
  computed: {
    isEdit() {
      return !!this.data.record;
    },
    objectId() {
      return this.data.record ? this.data.record.id : "";
    }
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.$nextTick(() => {
            this.addForm.setFieldsValue({
              name: this.data.record ? this.data.record.name : ""
            });
          });
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.setLang();
  },
  methods: {
    ...mapActions({
      setLang: "common/getEnableLanguages"
    }),
    async handleLanguages() {
      await this.getTextMapByDataId(this.objectId);
      this.languageVisible = true;
      // this.languageList = init()
    },
    async getTextMapByDataId(id) {
      const {
        data: { data }
      } = await precessService("getTextMapByDataId", { id });
      const list = [];
      for (const key in data) {
        list.push({
          languageCode: key,
          text: data[key]
        });
      }
      this.languageList = list;
    },
    handlerName(e) {
      this.curText = e.target.value;
    },
    cancelMultilanguage(languageInfo) {
      this.languageVisible = false;
      this.languageList = languageInfo;
    },
    cancelEvent() {
      this.$emit("update:visible", false);
    },

    addgroup(value) {
      precessService("addgroup", {
        ...value,
        multiLanguages: this.languageList
      })
        .then(res => {
          UiUtils.successMessage(
            this.$t_process("process_group") + this.$t_common("save_success")
          );
          this.cancelEvent();
          this.$emit("ok", "addgroup");
        })

        .finally(e => {
          this.loading = false;
        });
    },
    editgroup(value) {
      precessService("editgroup", {
        ...value,
        objectId: this.objectId,
        multiLanguages: this.languageList
      })
        .then(res => {
          if (res.data) {
            UiUtils.successMessage(this.$t_common("save_success"));
            this.cancelEvent();
            this.$emit("ok", "editgroup");
          }
        })

        .finally(e => {
          this.loading = false;
        });
    },
    okEvent() {
      this.addForm.validateFields((err, value) => {
        if (!err) {
          this.loading = true;
          this.isEdit ? this.editgroup(value) : this.addgroup(value);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.exportform {
  width: 100%;
}
.item {
  margin-right: 3.125rem;
  width: 4.0625rem;
  height: @rem22;
  font-family: PingFangSC-Regular;
  font-size: @rem14;
  color: @yn-text-color-secondary;
  text-align: left;
  line-height: @rem22;
  font-weight: 400;
}
/deep/ .ant-modal-body {
  min-height: auto;
}
/deep/ .ant-form-item:last-of-type {
  margin-bottom: 0;
}
</style>
