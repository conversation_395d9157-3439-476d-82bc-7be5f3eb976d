<template>
  <yn-modal
    :title="$t_process('select_message_template')"
    dialogClass="msgTemplateSelect"
    :visible="visible"
    width="45rem"
    height="38rem"
    @ok="handlerOk"
    @cancel="handlerCancel"
  >
    <section class="msg-wrap">
      <section class="msg-left">
        <div class="msg-top">
          <span class="top-title">{{ $t_process("to_be_selected") }}:</span>
          <yn-checkbox
            class="top-all"
            :checked="selectAll"
            :indeterminate="
              selection.length > 0 && selection.length !== messageList.length
            "
            @change="handlerSelectAll"
          >
            {{ this.$t_common("select_all") }}
          </yn-checkbox>
        </div>
        <yn-input-search
          v-model="searchValue"
          class="search"
          :placeholder="$t_common('please_enter_search_content')"
          @change="handlerSearch"
        />
        <div class="msg-list">
          <div v-for="item in showList" :key="item.objectId" class="list-item">
            <yn-checkbox
              :checked="!!selectionMap[item.objectId]"
              @change="handlerSelect(item)"
            >
              {{ item.name }}
            </yn-checkbox>
          </div>
        </div>
      </section>
      <section class="msg-right">
        <div class="msg-top">
          <span class="top-title">
            {{ $t_common("has_selected") }}: ({{ selection.length }})
          </span>
          <span class="top-delete" @click="handlerClear">{{
            $t_common("clear_all")
          }}</span>
        </div>
        <div class="selection">
          <div
            v-for="item in selection"
            :key="item.objectId"
            class="selectitem"
          >
            {{ item.name }}
            <svg-icon
              type="icon-shanchu"
              class="del-icon"
              :isIconBtn="false"
              @click.native="handlerDel(item)"
            />
          </div>
        </div>
      </section>
    </section>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-checkbox/";
import "yn-p1/libs/components/yn-modal/";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    messageList: {
      type: Array,
      default: () => []
    },
    selected: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectAll: false,
      searchValue: "",
      showList: "",
      selectionMap: {},
      selection: []
    };
  },
  watch: {
    selection: {
      handler() {
        this.getSelectionMap();
      },
      immediate: true
    },
    visible: {
      handler() {
        this.getSelection();
      },
      immediate: true
    }
  },
  methods: {
    handlerCancel(e) {
      this.$emit("update:visible", false);
    },
    handlerOk() {
      this.$emit("ok", this.selection);
      this.handlerCancel();
    },
    handlerSearch() {
      if (this.searchValue) {
        this.showList = this.messageList.filter(item =>
          item.name.match(this.searchValue)
        );
      } else {
        this.showList = [...this.messageList];
      }
    },
    handlerSelect(item) {
      const index = this.selection.findIndex(
        tmp => tmp.objectId === item.objectId
      );
      if (index < 0) {
        this.selection.push(item);
      } else {
        this.selection.splice(index, 1);
      }
    },
    handlerClear() {
      this.selection = [];
      this.selectAll = false;
    },
    handlerDel(item) {
      const index = this.selection.findIndex(
        tmp => tmp.objectId === item.objectId
      );
      this.selection.splice(index, 1);
    },
    handlerSelectAll() {
      if (this.selectAll) {
        this.selection = [];
      } else {
        this.selection = [...this.messageList];
      }
      this.selectAll = !this.selectAll;
    },
    getSelection() {
      this.selection = [...this.selected];
      this.showList = [...this.messageList];
      if (this.selection.length === this.messageList) {
        this.selectAll = true;
      }
    },
    getSelectionMap() {
      this.selectionMap = this.selection.reduce((pre, next) => {
        pre[next.objectId] = next;
        return pre;
      }, {});
    }
  }
};
</script>

<style lang="less">
.msgTemplateSelect {
  .ant-modal-body {
    height: 100%;
    padding: 0;
    overflow: hidden;
  }
}
</style>
<style lang="less" scoped>
.msg-wrap {
  height: 38rem;
  display: flex;
  padding: 0.5rem;
  .msg-left {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 50%;
    .search {
      width: 100%;
      margin: 0 0 1rem 0;
    }
  }
  .selection,
  .msg-list {
    padding: 0 0.5rem;
    height: 0;
    flex: 1;
    width: 100%;
    overflow: auto;
  }
  .msg-list {
    .list-item {
      padding: 0 0.5rem;
      margin-bottom: 0.5rem;
    }
  }
  .selection {
    .selectitem {
      font-weight: 500;
      border-radius: 0.125rem;
      padding: 0 0.5rem;
      color: @yn-text-color-secondary;
      background: rgba(16, 88, 131, 0.1);
      display: inline-block;
      font-size: 0.875rem;
      line-height: 1.875rem;
      margin-bottom: 0.5rem;
      width: 100%;
    }
    .del-icon {
      float: right;
      cursor: pointer;
    }
  }
  .msg-right {
    display: flex;
    flex-direction: column;
    width: 50%;
    height: 100%;
  }
  .msg-top {
    display: flex;
    align-items: center;
    width: 100%;
    height: 2.25rem;
    padding: 0.25rem 0.5rem;
    .top-title {
      font-size: 0.875rem;
      color: @yn-label-color;
    }
    .top-delete,
    .top-all {
      margin-left: auto;
      cursor: pointer;
    }
  }
}
</style>
