<template>
  <yn-modal
    :visible="visible"
    :title="$t_common('modify') + $t_process('process_group')"
    width="25rem"
    :bodyStyle="{ display: 'flex', 'align-items': 'center' }"
    @cancel="cancelEvent"
  >
    <yn-form
      :form="addForm"
      class="exportform"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 18 }
      }"
    >
      <yn-form-item :label="$t_process('process_group')" :colon="false">
        <yn-select
          v-decorator="[
            'name',
            { rules: [{ required: true, message: $t_common('input_select') }] }
          ]"
          :allowClear="false"
          :placeholder="$t_common('input_select')"
          :options="options"
        />
      </yn-form-item>
    </yn-form>
    <template slot="footer">
      <yn-button key="back" @click="cancelEvent">
        {{ $t_common("cancel") }}
      </yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="loading"
        :disabled="loading"
        @click="okEvent"
      >
        {{ $t_common("save") }}
      </yn-button>
    </template>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-option/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import "yn-p1/libs/components/yn-icon-button/";
import "yn-p1/libs/components/yn-icon/";

import precessService from "@/services/process";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      loading: false,
      options: [],
      addForm: this.$form.createForm(this, "addForm")
    };
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.getallGroup();
          this.$nextTick(() => {
            this.addForm.setFieldsValue({
              name: this.data.record ? this.data.record.parentId : ""
            });
          });
        }
      },
      immediate: true
    }
  },
  created() {
    this.getallGroup();
  },
  methods: {
    async getallGroup() {
      const {
        data: { data }
      } = await precessService("getallGroup");
      this.options = data.map(item => {
        return {
          label: item.groupName,
          value: item.groupId
        };
      });
    },
    cancelEvent() {
      this.$emit("update:visible", false);
    },
    okEvent() {
      this.addForm.validateFields((err, value) => {
        if (!err) {
          this.loading = true;
          precessService("editTemplateGroup", {
            processGroup: value.name, // 分组id
            objectId: this.data.record.id // 模板id
          })
            .then(res => {
              UiUtils.successMessage(
                this.$t_common("modify") +
                  this.$t_process("process_group") +
                  this.$t_common("success")
              );
              this.cancelEvent();
              this.$emit("ok", "editgroup");
            })
            .finally(e => {
              this.loading = false;
            });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.exportform {
  width: 100%;
}
.item {
  margin-right: 3.125rem;
  width: 4.0625rem;
  height: @rem22;
  font-family: PingFangSC-Regular;
  font-size: @rem14;
  color: @yn-text-color-secondary;
  text-align: left;
  line-height: @rem22;
  font-weight: 400;
}
/deep/ .ant-modal-body {
  min-height: auto;
}
/deep/ .ant-form-item:last-of-type {
  margin-bottom: 0;
}
</style>
