<template>
  <yn-modal
    :visible="visible"
    :title="$t_common('import')"
    :okText="$t_common('ok')"
    :cancelText="$t_common('cancel')"
    width="31.625rem"
    class="import-user-modal"
    @cancel="cancelEvent"
  >
    <div class="download" @click="downTmp">
      {{ $t_process("download_template") }}
    </div>
    <yn-upload-dragger
      :class="{ uploaded: hasFile }"
      :fileList="fileList"
      :disabled="hasFile"
      name="userFile"
      :multiple="false"
      :remove="handlerDelete"
      :beforeUpload="beforeUpload"
    >
      <p :class="['ant-upload-drag-icon upload-icon', { disabled: hasFile }]">
        <yn-icon type="inbox" />
      </p>
      <p :class="['upload-text', { disabled: hasFile }]">
        {{ $data.$uploadMessage[status] }}
      </p>
      <svg-icon
        type="icon-shanchuicon"
        class="deleteIcons"
        @click.native="handlerDelete"
      />
    </yn-upload-dragger>
    <template slot="footer">
      <yn-button key="back" @click="cancelEvent">
        {{ $t_common("cancel") }}
      </yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="loading"
        :disabled="loading"
        @click="okEvent"
      >
        {{ $t_common("ok") }}
      </yn-button>
    </template>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-upload-dragger/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import processService from "@/services/process";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import { confirm } from "@/views/journal/journalList/exconfirm";
import { isFileType, downloadFile } from "@/utils/common";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    root: {
      type: Object,
      default: () => ({})
    },
    params: {
      type: [Object, String],
      default: () => ({})
    }
  },
  data() {
    return {
      tempVisible: false,
      fileList: [],
      $uploadMessage: {
        uploaded: this.$t_common("delete_then_drag_or_select"),
        unuploaded: this.$t_process("drop_excel_upload")
      },
      loading: false,
      action: ""
    };
  },
  computed: {
    status() {
      return this.hasFile ? "uploaded" : "unuploaded";
    },
    hasFile: {
      get() {
        return this.fileList.length > 0;
      },
      set(value) {
        return value;
      }
    }
  },
  methods: {
    async downTmp() {
      processService("exportTemplate", this.root.templateName).then(res => {
        if (res.data) {
          downloadFile(res);
          UiUtils.successMessage(this.$t_process("template_download_sucess"));
        }
      });
    },
    cancelEvent() {
      this.$emit("update:visible", false);
      this.fileList = [];
    },
    beforeUpload(file, fileList) {
      if (!isFileType(file.name, /(xlsx|xls)$/)) {
        UiUtils.errorMessage(this.$t_process("upload_correct_format"));
        return false;
      }
      const hasFile = this.hasFile;
      if (!hasFile) {
        this.fileList = fileList;
      }
      return false;
    },
    okEvent() {
      if (this.fileList.length === 0) {
        UiUtils.errorMessage(this.$t_process("please_upload_file"));
        return;
      }
      this.loading = true;
      const formData = new FormData();
      formData.append("file", this.fileList[0]);
      formData.append("entity", this.root.entity);
      formData.append("scope", this.root.scope);
      formData.append(
        "nodeUserAuth",
        this.params
          ? JSON.stringify(this.params.nodeUserAuth)
          : JSON.stringify({})
      );
      processService("importTemplate", formData)
        .then(res => {
          this.handlerConfirm(res.data.data);
          this.$emit("ok", res.data.data.nodeUserVO || {});
          this.cancelEvent();
          this.loading = false;
        })
        .catch(() => {
          UiUtils.errorMessage(this.$t_process("reupdate_file"));
          this.loading = false;
        });
    },
    handlerDelete() {
      this.fileList = [];
    },
    handlerDownload({ failedExcelFileName, failedExcelFilePath }) {
      processService("exportFailedList", {
        fileName: failedExcelFileName,
        filePath: failedExcelFilePath
      }).then(res => {
        if (res.data) {
          downloadFile(res);
          UiUtils.successMessage(
            this.$t_process("failed_list_downloaded_successfully")
          );
        }
      });
    },
    // ----------子方法拆分------------
    handlerConfirm(res) {
      const successNum = res.successCount;
      const errorNum = res.failedCount;
      const total = res.totalCount;
      this._importmodel = confirm({
        title: this.$t_process("operation_successful"),
        class: "jsx-message",
        content: h => (
          <div class="jsx-message-body">
            {this.$t_process("total_import_executors")} {total}{" "}
            {this.$t_common("strip")}，{this.$t_common("success")} {successNum}{" "}
            {this.$t_common("strip")}，{this.$t_common("failed_n")}
            {errorNum ? (
              <span class="errorNum"> {errorNum}</span>
            ) : (
              " " + errorNum
            )}{" "}
            {this.$t_common("strip")}。
          </div>
        ),
        button: h => this.vnodeButtonRender(res),
        type: "success"
      });
    },
    vnodeButtonRender(res) {
      return (
        <div>
          <yn-button
            class="exportbtn"
            onClick={() => {
              this._importmodel.destroy();
            }}
          >
            {this.$t_common("close")}
          </yn-button>
          {!res.allSuccess && (
            <yn-button
              class="exportbtn"
              type="primary"
              onClick={() => this.handlerDownload(res)}
            >
              {this.$t_process("download_ailure_checklist")}
            </yn-button>
          )}
        </div>
      );
    }
  }
};
</script>

<style lang="less" scoped>
.import-user-modal {
  font-family: PingFangSC-Regular;
  font-size: @rem14;
}
.upload-text {
  text-align: center;
  height: @rem24;
  font-weight: 400;
  line-height: @rem24;
  color: @yn-text-color;
}
.upload-icon.disabled {
  /deep/ .anticon {
    color: @yn-disabled-color !important;
  }
}
.upload-text.disabled {
  color: @yn-disabled-color;
}
.download {
  height: @rem22;
  margin-bottom: @rem8;
  color: @yn-chart-1;
  text-align: right;
  line-height: @rem22;
  font-weight: 400;
  cursor: pointer;
}
.exportbtn {
  margin-left: @rem8;
}

.deleteIcon,
.deleteIcons {
  display: none;
}
.uploaded .uploadIcon {
  position: absolute;
  display: inline-block;
  width: @rem20;
  height: @rem20;
  left: @rem8;
  bottom: -@rem36;
  z-index: 10;
}
.uploaded .deleteIcons {
  position: absolute;
  display: inline-block;
  bottom: -@rem42;
  z-index: 10;
  right: @rem8;
  cursor: pointer;
}
/deep/ .ant-upload-list-item-card-actions {
  display: none;
}

/deep/ .ant-upload-list-item-name {
  position: relative;
  margin-left: @rem12;
  padding-right: @rem32;
}
/deep/ .ant-form-item:last-of-type {
  margin-bottom: 0;
}
/deep/ .anticon-paper-clip {
  width: 0;
  height: 0;
  top: @rem8;
  svg {
    display: none;
  }
}
/deep/ .ant-modal-body {
  padding-top: 0.75rem;
}
</style>
<style lang="less">
.jsx-message .errorNum {
  color: @yn-error-color;
}
.jsx-message .jsx-message-body {
  height: 6.125rem;
}
</style>
