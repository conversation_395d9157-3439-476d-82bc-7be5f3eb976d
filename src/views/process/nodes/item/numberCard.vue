<template>
  <div class="num-card">
    <div class="card-top">
      <span class="card-num">{{ `# ${num + 1}` }}</span>
      <svg-icon
        class="card-icon"
        type="icon-shanchuicon"
        :class="{ disabled: disabled }"
        :isIconBtn="false"
        @click.native="deleteCard"
      />
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    num: {
      type: Number,
      default: 1
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    deleteCard() {
      if (this.disabled) return;
      this.$emit("delete", this.num);
    }
  }
};
</script>

<style lang="less" scoped>
.num-card {
  position: relative;
  background: @yn-background-color;
  border-radius: 0.25rem;
  padding: 0.625rem 0.5rem;
  margin: 0 0 0.5rem;
}
.card-top {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}
.card-num {
  font-size: 0.875rem;
  line-height: 1.375rem;
  display: flex;
  align-items: center;
  letter-spacing: 0px;
  color: @yn-text-color-secondary;
}
.card-icon {
  margin-left: auto;
  cursor: pointer;
  color: @yn-label-color;
  &:hover {
    color: @yn-primary-color;
  }
  &.disabled {
    color: @yn-disabled-color !important;
  }
}
.card-content {
}
</style>
