<template>
  <div class="email-content">
    <yn-form :form="nodeForm" class="emailForm" labelAlign="left">
      <yn-form-item
        :label="$t_process('message_template.sending_timing')"
        :colon="false"
        :labelCol="{ span: 7 }"
        :wrapperCol="{ span: 17 }"
      >
        <yn-select
          v-decorator="[
            'messageType',
            {
              rules: [
                {
                  required: true,
                  message: $t_common('input_select')
                }
              ]
            }
          ]"
          :placeholder="$t_common('input_select')"
          :options="optionsType"
          @change="handlerChange"
        />
      </yn-form-item>
      <yn-form-item
        :label="$t_process('message_template.notifier')"
        :colon="false"
        :labelCol="{ span: 7 }"
        :wrapperCol="{ span: 17 }"
      >
        <yn-radio-group
          v-decorator="[
            'sameExecutor',
            {
              rules: [
                {
                  required: true,
                  message: $t_common('input_select')
                }
              ]
            }
          ]"
          @change="handlerChange"
        >
          <yn-radio :value="true">
            {{ $t_process("message_template.same_as_executor") }}
          </yn-radio>
          <yn-radio :value="false">
            {{ $t_process("message_template.define_notifier") }}
          </yn-radio>
        </yn-radio-group>
      </yn-form-item>
      <yn-form-item
        v-if="!sameExecutor"
        label=""
        :colon="false"
        :labelCol="{ span: 7 }"
        :wrapperCol="{ span: 17, offset: 7 }"
      >
        <PersonInput
          v-decorator="[
            'messageUser',
            {
              rules: [
                {
                  required: !sameExecutor ? true : false,
                  message: $t_common('input_select')
                }
              ]
            }
          ]"
          :title="$t_process('message_template.notifier')"
          v-bind="$attrs"
          @change="handlerChange"
        />
      </yn-form-item>
    </yn-form>
    <yn-table :columns="columns" :dataSource="data" :scroll="{ x: '100%' }">
      <span slot="table.name" slot-scope="text" :title="text">
        {{ text }}
      </span>
      <span slot="table.action" slot-scope="text, record">
        <span
          class="yn-a-link action-margin"
          href="javascript:;"
          @click="handlerEdit(record.objectId)"
        >
          {{ $t_common("edit") }}
        </span>
        <span
          href="javascript:;"
          class="yn-a-link action-margin"
          @click="handlerDelete(record.objectId)"
        >
          {{ $t_process("message_template.unbind") }}
        </span>
      </span>
    </yn-table>
    <div
      class="add-template"
      :style="{ position: data.length === 0 ? 'absolute' : '' }"
    >
      <yn-button type="dashed" @click="handlerSelect">
        {{ $t_process("message_template.bind_message_template") }}
      </yn-button>
      <yn-button type="dashed" @click="handlerAdd">
        {{ $t_process("message_template.create_message_template") }}
      </yn-button>
    </div>
    <TemplateEdit
      v-if="visibleEdit"
      :messageList="messageList"
      :visible.sync="visibleEdit"
      :option="operate"
      :msgTemplateId="msgTemplateId"
      @ok="handlerOk"
    />

    <TemplateSelect
      v-if="visibleSelect"
      :messageList="messageList"
      :visible.sync="visibleSelect"
      :selected="data"
      @ok="handlerSelectOk"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-radio/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-radio-group/";
import PersonInput from "./personinput.vue";
import TemplateEdit from "./templateEdit.vue";
import TemplateSelect from "./templateSelect.vue";
import commonService from "@/services/common";
export default {
  components: { PersonInput, TemplateEdit, TemplateSelect },
  model: {
    prop: "value",
    event: "change"
  },
  props: {
    // eslint-disable-next-line vue/require-default-prop
    value: {
      type: [Object, String]
    },
    nodeId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      msgTemplateId: "",
      messageList: [],
      operate: "create",
      visibleSelect: false,
      sameExecutor: false,
      visibleEdit: false,
      columns: [
        {
          title: this.$t_process("message_template.message_template"),
          dataIndex: "name",
          key: "name",
          scopedSlots: {
            customRender: "name"
          }
        },
        {
          title: this.$t_process("message_template.channel"),
          width: "60px",
          dataIndex: "channelId",
          key: "channelId"
        },
        {
          title: this.$t_common("operation"),
          dataIndex: "action",
          width: "90px",
          key: "action",
          scopedSlots: {
            customRender: "action"
          }
        }
      ],
      data: [],
      nodeForm: this.$form.createForm(this, "nodeForm"),
      optionsType: [
        {
          label: this.$t_process("message_template.process_arrival"),
          value: "node_arrived"
        },
        {
          label: this.$t_process("message_template.process_rejected"),
          value: "node_return"
        },
        {
          label: this.$t_process("message_template.process_overdue"),
          value: "node_timeout"
        },
        {
          label: this.$t_process("message_template.process_finished"),
          value: "node_finish"
        },
        {
          label: this.$t_process("message_template.urging"),
          value: "node_remind"
        }
      ]
    };
  },
  watch: {
    value: {
      async handler(value) {
        this.setFormData();
        await this.msgTemplates();
      }
    }
  },
  methods: {
    async msgTemplates() {
      const {
        data: { items }
      } = await commonService("msgTemplates");
      this.messageList = items;
      this.setTemplateData();
    },
    handlerChange() {
      this.$nextTick(() => {
        const data = {
          ...this.nodeForm.getFieldsValue(),
          messageTemplateId: this.data.map(item => item.objectId).join(","),
          objectId: this.value.objectId || "",
          nodeId: this.nodeId
        };
        this.$emit("change", data);
        this.sameExecutor = data.sameExecutor;
      });
      setTimeout(() => {
        this.hasError();
      }, 0);
    },
    handlerEdit(msgTemplateId) {
      this.msgTemplateId = msgTemplateId;
      this.visibleEdit = true;
      this.operate = "edit";
    },
    handlerDelete(msgTemplateId) {
      this.data = this.data.filter(item => item.objectId !== msgTemplateId);
      this.handlerChange();
    },
    handlerAdd() {
      this.msgTemplateId = "";
      this.visibleEdit = true;
      this.operate = "create";
    },
    handlerSelect() {
      this.visibleSelect = true;
    },
    hasError() {
      let hasError = true;
      this.nodeForm.validateFields((err, values) => {
        if (err) {
          hasError = true;
        } else {
          hasError = false;
        }
      });
      return hasError;
    },
    async handlerOk(objectId) {
      await this.msgTemplates();
      const message = this.messageList.filter(
        item => item.objectId === objectId
      );
      if (message[0]) {
        const index = this.data.findIndex(
          item => item.objectId === message[0].objectId
        );
        if (index > -1) {
          this.data.splice(index, 1, message[0]);
        } else {
          this.data.push(message[0]);
        }
      }
      this.handlerChange();
    },
    handlerSelectOk(selection) {
      this.data = selection;
      this.handlerChange();
    },
    setFormData() {
      const { messageType, sameExecutor, messageUser } = this.value || {
        messageType: "node_arrived",
        sameExecutor: true,
        messageUser: ""
      };
      this.sameExecutor = sameExecutor;
      this.$nextTick(() => {
        this.nodeForm.setFieldsValue({
          messageType,
          sameExecutor,
          messageUser
        });
      });
    },
    setTemplateData() {
      const { messageTemplateId } = this.value || {
        messageTemplateId: ""
      };
      const list = messageTemplateId.split(",");
      this.data = this.messageList.filter(item => {
        return list.includes(item.objectId);
      });
    }
  }
};
</script>

<style lang="less" scoped>
.email-content {
  position: relative;
}
.emailForm {
  /deep/ .ant-form-item-label label::before {
    margin-right: 0.25rem;
    display: inline-block;
    width: 0.5rem;
    content: "";
  }
  /deep/ .ant-form-item-label label.ant-form-item-required::before {
    content: "*";
  }
  /deep/ .ant-form-item {
    margin-bottom: 0.5rem;
    &.radiosgroup {
      margin-bottom: 0px;
      .ant-form-item-control-wrapper {
        padding-left: 0.5rem;
      }
    }
  }
}
.add-template {
  width: 100%;
  display: flex;
  justify-content: space-between;
  bottom: 0rem;
  z-index: 2;
  button {
    width: 45%;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
/deep/ .ant-table-placeholder {
  height: 2.75rem;
  background: @yn-background-color;
  .ant-empty {
    display: none;
  }
}
/deep/ .ant-radio-group {
  white-space: nowrap;
}
/deep/ .ant-table-tbody > tr > td {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
