<template>
  <tab-transfer
    v-if="visible"
    v-bind="$attrs"
    :required="false"
    :closeAnalysis="true"
    :customLoaderMember="ranger ? customLoaderMember : null"
    :customSearchMember="ranger ? customSearchMember : null"
    :closeTransfer="closeTransfer"
    :dimInfos="transferData"
  />
</template>

<script>
import tabTransfer from "@/components/hoc/newDimensionTransfer/tabTransfer.vue";
import { formatRequestParams } from "@/utils/common";
import _cloneDeep from "lodash/cloneDeep";
import { getBooleanValue } from "@/utils/common";
const dataList = [
  "memberType",
  "member",
  "level",
  "attr",
  "subset",
  "variable"
];
export default {
  components: { tabTransfer },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    ranger: {
      type: Object,
      default: null
    },
    dimInfos: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      filterMembers: {},
      transferData: []
    };
  },
  computed: {
    rangerMap() {
      const temp = {};
      for (const key in this.ranger) {
        temp[key] = this.ranger[key].reduce((pre, next) => {
          pre[next.objectId] = next;
          return pre;
        }, {});
      }
      return temp;
    }
  },
  watch: {
    visible: {
      handler(nv) {
        if (nv && this.dimInfos && this.dimInfos.length) {
          this.transferData = this.filterDim();
        }
      }
    }
  },
  methods: {
    async customSearchMember({ dimId, searchValue }) {
      const arr = (this.ranger && this.ranger[dimId]) || [];
      const selfChildren = [];
      for (const item of arr) {
        const obj = {
          type: "member",
          isLeaf: true,
          title: item.dimMemberName,
          key: item.objectId,
          dimMemberCode: item.dimMemberCode,
          dimMemberShowName: item.dimMemberName,
          fromSearch: true,
          shardim: getBooleanValue(item.dimMemberShared),
          scopedSlots: { title: "custom" },
          disabled: typeof item.readOnly === "undefined" ? false : item.readOnly
        };
        if (item.dimMemberName.indexOf(searchValue) > -1) {
          selfChildren.push(obj);
        }
      }
      return selfChildren;
    },
    async customLoaderMember({ dimId, parentId }) {
      const arr = (this.ranger && this.ranger[dimId]) || [];
      const selfChildren = [];
      for (const item of arr) {
        const obj = {
          dimId,
          title: item.dimMemberName,
          dimMemberCode: item.dimMemberCode,
          dimMemberShowName: item.dimMemberShowName,
          key: item.objectId,
          type: "member",
          dimMemberDbCode: item.dimMemberDbCode,
          isLeaf: !getBooleanValue(item.hasChildren),
          value: item.dimMemberName,
          shardim: getBooleanValue(item.dimMemberShared),
          scopedSlots: { title: "custom" },
          disabled: typeof item.readOnly === "undefined" ? false : item.readOnly
        };
        if (item.dimMemberParentId === parentId) {
          selfChildren.push(obj);
        } else if (
          !this.rangerMap[dimId][item.dimMemberParentId] &&
          dimId === parentId
        ) {
          selfChildren.push(obj);
        }
      }
      return selfChildren;
    },
    filterDim() {
      if (!this.ranger) return _cloneDeep(this.dimInfos);
      const dimInfos = [];
      for (const item of this.dimInfos) {
        const ranger = this.rangerMap[item.dimId];
        const memberType = item.members ? item.members.memberType || [] : [];
        dimInfos.push({
          ...item,
          members: {
            ...item.members,
            memberType: memberType.filter(sItem => {
              return ranger ? ranger[sItem.memberId] : false;
            })
          }
        });
        this.filterMembers[item.dimId] = ranger
          ? memberType.filter(sItem => {
            return !ranger[sItem.memberId];
          })
          : memberType;
      }
      return dimInfos;
    },
    cancelEvent() {
      this.$emit("update:visible", false);
    },
    filter(lists) {
      return lists;
    },
    addItems(obj, field, items) {
      if (items && items.length > 0) {
        obj[field] = Object.values(
          items.concat(obj[field] || []).reduce((pre, next) => {
            pre[next.id] = next;
            return pre;
          }, {})
        );
      }
    },
    closeTransfer(exprObjs) {
      if (!exprObjs) {
        this.cancelEvent();
        return;
      }
      const expDtoList = {};
      for (let i = 0; i < this.transferData.length; i++) {
        const { dimId, dimCode } = this.transferData[i];
        let exprObj = exprObjs[i];
        this.addItems(exprObj, "memberType", this.filterMembers[dimId]);
        const hasData = dataList.some(key => {
          return exprObj[key] && exprObj[key].length > 0;
        });
        if (!hasData && !exprObj.allMember) {
          exprObj = "";
        }
        expDtoList[dimCode.toLowerCase()] = exprObj
          ? JSON.stringify({
            expDtoList: [
              {
                dimId: dimId,
                dimMemberExps: formatRequestParams(exprObj)
              }
            ]
          })
          : "";
      }
      this.$emit("ok", expDtoList);
      this.cancelEvent();
    }
  }
};
</script>

<style lang="less" scoped></style>
