<template>
  <div class="batch-tab">
    <div class="batch-tav-header">
      <yn-dropdown v-if="left.length > 0" placement="bottomRight">
        <svg-icon type="ellipsis" class="tab-more" />
        <yn-menu slot="overlay">
          <yn-menu-item v-for="i in left" :key="i" @click="handlerCurrent(i)">
            {{ $t_process("phase_xx", [i]) }}
          </yn-menu-item>
        </yn-menu>
      </yn-dropdown>
      <div class="tab-group">
        <span
          v-for="i in center"
          :key="i"
          :class="['tab-item', { active: current === i }]"
          @click="handlerCurrent(i)"
        >
          {{ $t_process("phase_xx", [i]) }}
        </span>
      </div>
      <yn-dropdown v-if="right.length > 0" placement="bottomRight">
        <svg-icon type="ellipsis" class="tab-more" />
        <yn-menu slot="overlay">
          <yn-menu-item v-for="i in right" :key="i" @click="handlerCurrent(i)">
            {{ $t_process("phase_xx", [i]) }}
          </yn-menu-item>
        </yn-menu>
      </yn-dropdown>
      <yn-divider type="vertical" />
      <svg-icon class="add-more" type="icon-zhankai" @click="handlerAdd" />
    </div>
    <div class="batch-tab-content">
      <div
        v-for="(item, index) in tab"
        v-show="index === current - 1"
        :key="index"
        class="content-wrap"
      >
        <div class="content-body">
          <BatchItem
            :key="index"
            :item="item"
            :element="element"
            :root="root"
            @update="handlerUpdate"
          />
        </div>
        <div class="batch-tab-footer">
          <yn-button class="delete-btn" @click="handlerDelete(index)">
            {{ $t_process("delete_phase") }}
          </yn-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BatchItem from "./batchitem.vue";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-button/";
export default {
  components: { BatchItem },
  props: {
    element: {
      type: Object,
      default: () => {
        return {};
      }
    },
    root: {
      type: Object,
      default: () => {
        return {};
      }
    },
    tab: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      current: 1
    };
  },
  computed: {
    arrayTab() {
      return Array.from({ length: this.tab.length }, (_, index) => index + 1);
    },
    start() {
      if (this.current <= 4) {
        return 1;
      } else if (this.current + 4 > this.tab.length) {
        return this.tab.length - 3;
      } else {
        return this.current;
      }
    },
    left() {
      return this.arrayTab.slice(0, this.start - 1);
    },
    center() {
      return this.arrayTab.slice(this.start - 1, this.start + 3);
    },
    right() {
      return this.arrayTab.slice(this.start + 3);
    }
  },
  methods: {
    handlerDelete(index) {
      this.$emit("delete", index);
      this.current = 1;
    },
    handlerAdd() {
      this.$emit("add");
      this.$nextTick(() => {
        this.current = this.tab.length;
      });
    },
    handlerCurrent(i) {
      this.current = i;
    },
    handlerUpdate() {
      this.$emit("update");
    }
  }
};
</script>
<style lang="less" scoped>
.batch-tab {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.batch-tav-header {
  padding: 0.5625rem 0.625rem;
  display: flex;
  align-items: center;
  height: 2.625rem;
  border-bottom: 1px solid #e1e5eb;
  .add-more {
    margin-left: auto;
  }
  .tab-group {
    display: flex;
    white-space: nowrap;
    flex: 1;
  }
  .tab-item {
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.375rem;
    text-align: center;
    letter-spacing: 0px;
    margin: 0 0.625rem;
    white-space: nowrap;
    color: @yn-text-color-secondary;
    position: relative;
    cursor: pointer;
    &.active {
      color: @yn-primary-color;
    }
    &.active::after {
      content: " ";
      display: block;
      position: absolute;
      width: 120%;
      height: 2px;
      left: -10%;
      top: 1.9rem;
      background: @yn-primary-color;
    }
  }
  .tab-more {
  }
}
.batch-tab-content {
  height: 0;
  flex: 1;
}
.content-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.content-body {
  height: 0;
  flex: 1;
}
.batch-tab-footer {
  /* 自动布局 */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.3125rem 0.75rem;
  .delete-btn {
    width: 323px;
    height: 32px;
    border-radius: 4px;
    opacity: 1;

    align-self: stretch;

    /* 中性色/反白文字-FFFFFF */
    background: #ffffff;

    box-sizing: border-box;
    /* 功能色/@yn-error-color危险色 */
    border: 1px solid @yn-error-color;
    color: @yn-error-color;
  }
}
</style>
