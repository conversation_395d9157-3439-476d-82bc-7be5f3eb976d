<template>
  <yn-tabs defaultActiveKey="root" class="root-panel">
    <yn-tab-pane key="root" :tab="$t_process('basic_settings')">
      <yn-form
        :form="rootForm"
        class="rootform"
        labelAlign="left"
        v-bind="{
          labelCol: { span: 5 },
          wrapperCol: { span: 19 }
        }"
      >
        <yn-form-item :label="$t_process('description')" :colon="false">
          <yn-input
            v-decorator="[
              'templateName',
              {
                rules: [
                  {
                    required: true,
                    message:
                      $t_common('input_message') + $t_process('description')
                  },
                  {
                    message: $t_process('field_length_exceeds_64'),
                    max: 64
                  }
                ]
              }
            ]"
            :placeholder="$t_common('input_message')"
            @change="handlerChange"
          >
            <yn-icon slot="suffix" type="global" @click="handleLanguages" />
          </yn-input>
        </yn-form-item>
        <yn-form-item :label="$t_process('entity')" :colon="false">
          <ShowDimListInput
            class="diminput"
            :dimInfo="entity"
            dynamicOrStatic="dynamic"
            @getExp="val => getExp(val, 'entity')"
            @click.native="handlerChange"
          />
        </yn-form-item>
        <yn-form-item :label="$t_process('scopes')" :colon="false">
          <ShowDimListInput
            class="diminput"
            :dimInfo="scope"
            dynamicOrStatic="dynamic"
            @click.native="handlerChange"
            @getExp="val => getExp(val, 'scope')"
          />
        </yn-form-item>
      </yn-form>
      <language-modal
        :languageVisible="languageVisible"
        :languageList.sync="element.multiLanguages"
        :curText="element.templateName"
        :addCurLang="true"
        @cancelMultilanguage="cancelMultilanguage"
      />
    </yn-tab-pane>
  </yn-tabs>
</template>

<script>
import "yn-p1/libs/components/yn-tabs/";
import "yn-p1/libs/components/yn-tab-pane/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-icon/";
import AppUtils from "yn-p1/libs/utils/AppUtils";
// import SvgIcon from "../../../components/ui/SvgIcon.vue";
import ShowDimListInput from "@/components/hoc/ShowDimListInput.vue";
import { formatRequestParams } from "@/utils/journal.js";
import DIM_INFO from "@/constant/dimMapping";
import LanguageModal from "@/components/multilanguage";
import precessService from "@/services/process";
import { mapActions } from "vuex";
import lang from "@/mixin/lang";
const { $t_process } = lang;
const Entity = {
  dimName: $t_process("entity"),
  dimCode: "Entity",
  objectId: DIM_INFO.Entity,
  dimId: DIM_INFO.Entity,
  selectedItem: [],
  dynamicOrStatic: "dynamic"
};
const Scope = {
  dimName: $t_process("scopes"),
  dimCode: "Scope",
  objectId: DIM_INFO.Scope,
  dimId: DIM_INFO.Scope,
  dynamicOrStatic: "dynamic",
  selectedItem: []
};
export default {
  components: { ShowDimListInput, LanguageModal },
  props: {
    id: {
      type: String,
      default: ""
    },
    element: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      scope: {
        ...Scope
      },
      entity: {
        ...Entity
      },
      languageVisible: false,
      rootForm: this.$form.createForm(this, "rootForm")
    };
  },
  watch: {
    "element.hash": {
      handler() {
        const scopelist = this.element.scope
          ? AppUtils.jsonParse(this.element.scope)
          : {};
        const entitylist = this.element.entity
          ? AppUtils.jsonParse(this.element.entity)
          : {};
        let scope =
          scopelist.expDtoList && scopelist.expDtoList[0]
            ? scopelist.expDtoList[0].dimMemberExps || ""
            : "";
        let entity =
          entitylist.expDtoList && entitylist.expDtoList[0]
            ? entitylist.expDtoList[0].dimMemberExps || ""
            : "";
        scope = scope ? AppUtils.jsonParse(scope) : {};
        entity = entity ? AppUtils.jsonParse(entity) : {};
        this.$nextTick(() => {
          this.rootForm.setFieldsValue({
            templateName: this.element.templateName
          });
          this.rootForm.validateFields();
        });
        this.$set(this.scope, "members", formatRequestParams(scope, true));
        this.$set(this.entity, "members", formatRequestParams(entity, true));
      },
      immediate: true
    }
  },
  mounted() {
    this.setLang();
  },
  methods: {
    ...mapActions({
      setLang: "common/getEnableLanguages"
    }),
    async handleLanguages() {
      await this.getTextMapByDataId(this.id);
      this.languageVisible = true;
    },
    async getTextMapByDataId(id) {
      const {
        data: { data }
      } = await precessService("getTextMapByDataId", { id });
      const list = [];
      for (const key in data) {
        list.push({
          languageCode: key,
          text: data[key]
        });
      }
      this.$set(
        this.element,
        "multiLanguages",
        this.element.multiLanguages && this.element.multiLanguages.length > 0
          ? this.element.multiLanguages
          : list
      );
    },
    cancelMultilanguage(languageInfo) {
      this.languageVisible = false;
      languageInfo && (this.element.multiLanguages = languageInfo);
    },
    getExp(val, dimCode) {
      const dataList = [
        "memberType",
        "member",
        "level",
        "attr",
        "subset",
        "variable"
      ];
      const hasData = dataList.some(key => {
        return val[key] && val[key].length > 0;
      });
      if (!hasData && !val.allMember) {
        val = "";
      }
      this.$set(this[dimCode], "members", val);
      this.element[dimCode] = val
        ? JSON.stringify({
          expDtoList: [
            {
              dimId: dimCode === "entity" ? Entity.dimId : Scope.dimId,
              dimMemberExps: formatRequestParams(val)
            }
          ]
        })
        : "";
    },
    handlerChange() {
      this.$nextTick(() => {
        Object.assign(this.element, this.rootForm.getFieldsValue());
        this.$emit("node-update", this.element);
      });
    }
  }
};
</script>
<style lang="less" scoped>
.root-panel {
  padding: 0.625rem 0.9375rem;
  .diminput {
    width: 100%;
  }
}
.rootform {
  /deep/ .ant-form-item-label label::before {
    margin-right: 0.25rem;
    display: inline-block;
    width: 0.4375rem;
    content: "";
  }
  /deep/ .ant-form-item-label label.ant-form-item-required::before {
    content: "*";
  }
  /deep/ .ant-form-item {
    margin-bottom: 0.5rem;
    label {
      font-size: 0.75rem;
    }
  }
}

/deep/ .ant-tabs-tabpane {
  padding-top: 1rem;
}
</style>
