<template>
  <yn-tabs defaultActiveKey="root" class="root-panel">
    <yn-tab-pane key="root" :tab="$t_process('basic_settings')">
      <div>
        <yn-form
          :form="activeForm"
          class="activeForm"
          labelAlign="left"
          v-bind="{
            labelCol: { span: 5 },
            wrapperCol: { span: 19 }
          }"
        >
          <yn-form-item :label="$t_process('process_step_name')" :colon="false">
            <yn-input
              v-decorator="[
                'nodeShowName',
                {
                  rules: [
                    { required: true, message: $t_common('input_message') },
                    { max: 64, message: $t_process('field_length_exceeds_64') }
                  ]
                }
              ]"
              :placeholder="$t_common('input_message')"
              @change="handlerChange"
            >
              <yn-icon slot="suffix" type="global" @click="handleLanguages" />
            </yn-input>
          </yn-form-item>
          <yn-form-item :label="$t_process('process_step_type')" :colon="false">
            <yn-select
              v-decorator="[
                'nodeType',
                {
                  rules: [
                    {
                      required: true,
                      message: $t_common('input_select')
                    }
                  ]
                }
              ]"
              :placeholder="$t_common('input_select')"
              :options="optionsType"
              :disabled="true"
              @change="handlerChange"
            />
          </yn-form-item>
          <yn-form-item :label="$t_process('activities_type')" :colon="false">
            <yn-radio-group
              v-decorator="['activityType']"
              @change="handlerChange"
            >
              <yn-radio :value="0">
                {{ $t_process("cc_notification") }}
              </yn-radio>
              <yn-radio :value="1">
                {{ $t_process("waiting_for_results") }}
              </yn-radio>
            </yn-radio-group>
          </yn-form-item>
          <yn-form-item :label="$t_process('execute_activity')" :colon="false">
            <yn-select
              v-decorator="[
                'executeActivity',
                {
                  rules: [
                    { required: true, message: $t_common('input_select') }
                  ]
                }
              ]"
              :allowClear="false"
              :placeholder="$t_common('input_select')"
              :options="optionsStatus"
              @change="handlerChange"
            />
          </yn-form-item>
        </yn-form>
      </div>
      <language-modal
        :key="element.nodeId"
        :languageVisible="languageVisible"
        :languageList.sync="element.nodeAttribute.multiLanguages"
        :curText="element.nodeShowName"
        :addCurLang="true"
        @change.native="handlerChange"
        @cancelMultilanguage="cancelMultilanguage"
      />
    </yn-tab-pane>
  </yn-tabs>
</template>

<script>
import "yn-p1/libs/components/yn-tabs/";
import "yn-p1/libs/components/yn-tab-pane/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-select/";
import LanguageModal from "@/components/multilanguage";
import { mapActions } from "vuex";
import precessService from "@/services/process";
export default {
  components: {
    LanguageModal
  },
  props: {
    element: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      optionsType: [
        {
          label: this.$t_process("start"),
          value: "start"
        },
        {
          label: this.$t_process("approve"),
          value: "approval"
        },
        {
          label: this.$t_process("activities"),
          value: "activity"
        },
        {
          label: this.$t_process("end"),
          value: "end"
        }
      ],
      languageVisible: false,
      optionsStatus: [
        {
          label: this.$t_process("activity_test_failed"),
          value: 0
        },
        {
          label: this.$t_process("activity_test_successful"),
          value: 1
        }
      ],
      activeForm: this.$form.createForm(this, "activeForm")
    };
  },
  watch: {
    "element.hash": {
      handler() {
        this.setDefault();
        this.setFormData();
      },
      immediate: true
    }
  },
  mounted() {
    this.setLang();
  },
  methods: {
    ...mapActions({
      setLang: "common/getEnableLanguages"
    }),
    async handleLanguages() {
      await this.getTextMapByDataId(this.element.nodeId);
      this.languageVisible = true;
    },
    cancelMultilanguage(languageInfo) {
      this.languageVisible = false;
      languageInfo &&
        (this.element.nodeAttribute.multiLanguages = languageInfo);
    },
    async getTextMapByDataId(id) {
      const {
        data: { data }
      } = await precessService("getTextMapByDataId", { id });
      const list = [];
      for (const key in data) {
        list.push({
          languageCode: key,
          text: data[key]
        });
      }
      this.$set(
        this.element.nodeAttribute,
        "multiLanguages",
        this.element.nodeAttribute.multiLanguages &&
          this.element.nodeAttribute.multiLanguages.length > 0
          ? this.element.nodeAttribute.multiLanguages
          : list
      );
    },
    handlerChange() {
      this.$nextTick(() => {
        const formData = this.activeForm.getFieldsValue();
        this.element.nodeShowName = formData.nodeShowName;
        this.element.nodeType = formData.nodeType;
        Object.assign(this.element.nodeAttribute, formData);
        this.$emit("node-update", this.element);
        this.activeForm.validateFields();
      });
    },
    setFormData() {
      const data = { ...this.element.nodeAttribute, ...this.element };
      this.$nextTick(() => {
        const field = this.activeForm.getFieldsValue();
        for (const key in field) {
          field[key] = data[key];
        }
        this.activeForm.setFieldsValue(field);
        this.activeForm.validateFields();
      });
    },
    setDefault() {
      this.element.nodeAttribute || (this.element.nodeAttribute = {});
      const attr = this.element.nodeAttribute;
      attr.activityType = this.getValue(attr.activityType, 0);
    },
    getValue(value, defaultValue) {
      return value === undefined || value === "" ? defaultValue : value;
    }
  }
};
</script>
<style lang="less" scoped>
.root-panel {
  padding: 0.625rem 0.9375rem;
  .diminput {
    width: 100%;
  }
}
.activeForm {
  /deep/ .ant-form-item-label label::before {
    margin-right: 0.25rem;
    display: inline-block;
    width: 0.4375rem;
    content: "";
  }
  /deep/ .ant-form-item-label label.ant-form-item-required::before {
    content: "*";
  }
  /deep/ .ant-form-item {
    margin-bottom: 0.5rem;
    label {
      font-size: 0.75rem;
    }
  }
}
/deep/ .ant-tabs-tabpane {
  padding-top: 1rem;
}
</style>
