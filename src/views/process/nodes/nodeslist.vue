<template>
  <section v-show="!visible" class="process-nodes">
    <header class="process-title cs-header-single">
      {{ $t_process("process_template") }}
    </header>
    <yn-spin :spinning="spinning" class="process-spinning">
      <section v-if="!empty" class="process-content cs-body-table">
        <section class="process-tools cs-body-table-action">
          <yn-input
            v-model="searchkey"
            :placeholder="$t_process('enter_template_name')"
            class="process-search"
            :allowClear="true"
            @keydown.enter="search"
            @change="search"
          >
            <!-- <yn-icon-svg slot="suffix" type="search" /> -->
            <svg-icon slot="suffix" type="icon-search1" :isIconBtn="false" />
          </yn-input>
          <div class="button-group">
            <yn-button
              type="primary"
              @click="handlerModel('AddProcess', { isGroup })"
            >
              {{ $t_process("add_process") }}
            </yn-button>
            <yn-button
              @click="
                handlerModel('AddGroup', { title: $t_process('new_group') })
              "
            >
              {{ $t_process("new_group") }}
            </yn-button>
          </div>
        </section>
        <yn-table
          class="process-table"
          :columns="columns"
          :dataSource="data"
          rowKey="id"
          :scroll="{ x: '100%', y: scrollHeight }"
          :customRow="customRow"
          :expandedRowKeys="expandedRowKeys"
          @expandedRowsChange="expandedRowsChange"
        >
          <span slot="number" slot-scope="text, record, index">
            <span class="numbercum">{{ index + 1 }}</span>
          </span>
          <span slot="numberTitle" class="numbercum">
            {{ isGroup ? "" : $t_common("order_number") }}
          </span>
          <span slot="name" slot-scope="text, record, index">
            <span v-if="!record.templateFlag" :class="['group-name']">
              <span class="groupname" @click="expandedRowsChangeLick(record)">{{
                record.name
              }}</span>
              <yn-tooltip :title="$t_common('edit')">
                <yn-icon-button
                  type="icon-edit"
                  class="group-button hover-button"
                  @click.native.stop="
                    handlerModel(
                      'AddGroup',
                      {
                        title: $t_common('modify') + $t_process('group_name'),
                        record
                      },
                      $event
                    )
                  "
                />
              </yn-tooltip>
              <yn-tooltip
                v-if="record.defaultGroup !== true"
                :title="$t_common('delete')"
              >
                <yn-icon-button
                  type="icon-shanchuicon"
                  class="group-button hover-button"
                  @click.native.stop="handlerDelGroup(record, $event)"
                />
              </yn-tooltip>
              <yn-tooltip v-if="!record._last" :title="$t_common('move_down')">
                <yn-icon-button
                  type="icon-c"
                  class="group-button"
                  @click="move(record, 'down')"
                />
              </yn-tooltip>
              <yn-tooltip v-if="!record._first" :title="$t_common('move_up')">
                <yn-icon-button
                  type="icon-shangyi"
                  class="group-button"
                  @click="move(record, 'up')"
                />
              </yn-tooltip>
            </span>
            <span v-else class="numbercum">
              <span v-if="isGroup" class="numberIndex">{{ index + 1 }}</span>
              {{ record.children ? "" : text }}
            </span>
          </span>
          <span slot="nameTitle">
            {{ $t_process("process_template_name") }}
          </span>
          <span slot="status" slot-scope="status, record">
            <yn-switch
              v-if="!record.children"
              :checked="!!record.status"
              :trueValue="1"
              :falseValue="0"
              size="small"
              @change="setStatus(record, arguments)"
            />
          </span>
          <div slot="table.action" slot-scope="text, record">
            <div v-if="!record.children">
              <yn-button
                type="text"
                class="action-button no-padding"
                @click="handlerEdit(record)"
              >
                {{ $t_common("edit") }}
              </yn-button>
              <yn-button
                type="text"
                class="action-button no-padding"
                @click="copy(record)"
              >
                {{ $t_common("copy") }}
              </yn-button>
              <yn-dropdown
                placement="bottomRight"
                overlayClassName="tree-menu-dropdown"
                :trigger="['click']"
              >
                <yn-icon-button type="more" class="action-button" @click.stop />
                <yn-menu slot="overlay">
                  <yn-menu-item
                    key="delete"
                    @click="deleteTemplate(record, $event)"
                  >
                    <span type="text" class="txtmodel">
                      {{ $t_common("delete") }}
                    </span>
                  </yn-menu-item>
                  <yn-menu-item
                    v-if="!record._first"
                    key="up"
                    @click="move(record, 'up')"
                  >
                    <span type="text" class="txtmodel">
                      {{ $t_common("move_up") }}
                    </span>
                  </yn-menu-item>
                  <yn-menu-item
                    v-show="!record._last"
                    key="down"
                    @click="move(record, 'down')"
                  >
                    <span type="text" class="txtmodel">
                      {{ $t_common("move_down") }}
                    </span>
                  </yn-menu-item>
                  <yn-menu-item
                    key="update"
                    @click="handlerModel('EditGroup', { record })"
                  >
                    <span type="text" class="txtmodel">
                      {{ $t_common("change_group") }}
                    </span>
                  </yn-menu-item>
                </yn-menu>
              </yn-dropdown>
            </div>
          </div>
        </yn-table>
      </section>
      <yn-empty v-else :image="require('@/image/reportEmpty.png')">
        <span slot="description" class="description">
          {{ $t_process("process_empty") }}
        </span>
        <yn-button
          type="primary"
          @click="handlerModel('AddProcess', { isGroup })"
        >
          {{ $t_process("add_process") }}
        </yn-button>
      </yn-empty>
    </yn-spin>
    <keep-alive>
      <component
        :is="componentId"
        :key="componentId"
        :visible.sync="cmpvisible"
        v-bind="componentProps"
        @ok="handlerOk"
      />
    </keep-alive>
  </section>
</template>

<script>
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-switch/";
import "yn-p1/libs/components/yn-icon-button/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import AddGroup from "./item/addgroup";
import AddProcess from "./item/addprocess";
import EditGroup from "./item/editgroup";
import precessService from "@/services/process";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import _debounce from "lodash.debounce";
// import { isType } from "@/utils/common";
// 1-开启 ,0-关闭
export default {
  components: { AddGroup, AddProcess, EditGroup },
  props: {
    visible: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      statusMap: [this.$t_common("close"), this.$t_common("open")],
      spinning: false,
      scrollHeight: false,
      searchkey: "",
      componentId: "",
      componentProps: {},
      expandedRowKeys: [],
      defaultExpandAllRows: false,
      cmpvisible: false,
      data: [],
      isGroup: true,
      numberCol: {
        dataIndex: "number",
        key: "number",
        width: 50,
        scopedSlots: {
          customRender: "number",
          title: "numberTitle"
        }
      },
      columns: [
        {
          dataIndex: "name",
          key: "name",
          scopedSlots: {
            customRender: "name",
            title: "nameTitle"
          }
        },
        {
          title: this.$t_process("status"),
          dataIndex: "status",
          key: "status",
          width: "6.125rem",
          scopedSlots: {
            customRender: "status"
          }
        },
        {
          title: this.$t_common("operation"),
          dataIndex: "action",
          width: "11.375rem",
          key: "action",
          scopedSlots: {
            customRender: "action"
          }
        }
      ]
    };
  },
  computed: {
    empty() {
      return (
        !this.spinning && this.data.length === 0 && this.searchkey.length === 0
      );
    }
  },
  watch: {
    visible(value) {
      if (!value) {
        this.search();
      }
    },
    cmpvisible(value) {
      if (!value) {
        this.handlerHideBtn();
      }
    }
  },
  created() {
    this.search();
  },
  methods: {
    async deleteTemplate(record) {
      UiUtils.confirm({
        title: `${this.$t_common("delete")} ${record.name}？`,
        content: this.$t_process("process_template_deletion"),
        onOk: () => {
          this.spinning = true;
          precessService("deleteTemplate", { id: record.id })
            .then(res => {
              UiUtils.successMessage(
                this.$t_process("process_template") +
                  this.$t_common("delete_success")
              );
              this.search();
            })
            .catch(e => {
              this.spinning = false;
            });
        }
      });
    },
    async addprocess() {
      precessService("addprocess", {});
    },
    async getlist(params) {
      let {
        data: { data }
      } = await precessService("getlist", params);
      this.formatlist(data);
      const isDefault =
        data.length === 1 && data[0].scopedSlots.defaultGroup === true;
      data = isDefault ? data[0].children : data;
      if (this.isGroup && isDefault) {
        this.columns.unshift(this.numberCol);
      } else if (!isDefault && this.isGroup === false) {
        this.columns.shift();
      }
      this.isGroup = !isDefault;
      return data;
    },
    async getTextMapByDataId() {
      precessService("getTextMapByDataId", {});
    },
    search: _debounce(async function() {
      this.spinning = true;
      this.data = await this.getlist({ value: this.searchkey });
      this.setExpand();
      this.coverStyle();
      this.$nextTick(() => {
        this.setScrollHeight();
      });
      this.spinning = false;
    }, 200),
    async handlerDeleteGroup(record, e) {
      this.spinning = true;
      precessService("deleteGroup", { id: record.id })
        .then(res => {
          UiUtils.successMessage(
            this.$t_process("process_group") + this.$t_common("delete_success")
          );
          this.handlerHideBtn(e);
          this.search();
        })
        .catch(e => {
          this.spinning = false;
          this.handlerHideBtn(e);
        });
    },
    async move(record, placement) {
      this.spinning = true;
      const text = {
        up: this.$t_common("move_up"),
        down: this.$t_common("move_down")
      };
      precessService("move", { id: record.id, moveType: placement }).then(
        res => {
          UiUtils.successMessage(text[placement] + this.$t_common("success"));
          this.search();
        }
      );
    },
    async copy(record) {
      this.spinning = true;
      precessService("copy", { id: record.id })
        .then(res => {
          UiUtils.successMessage(this.$t_common("copy_success"));
          this.search();
        })
        .catch(e => {
          this.spinning = false;
        });
    },
    async setStatus(record, arg) {
      this.spinning = true;
      precessService("setStatus", { id: record.id, flag: arg[0] })
        .then(res => {
          const data = res.data.data;
          if (data) {
            UiUtils.info({
              title: this.$t_common("tips"),
              class: "jsx-message",
              content: h => (
                <div>
                  <div class="message-title">{data.tips}</div>
                  <ul class="message-lists">
                    {data.list.map(li => {
                      return (
                        <li class="message-list">
                          {li.split(",").join("， ")}
                        </li>
                      );
                    })}
                  </ul>
                </div>
              )
            });
          } else {
            UiUtils.successMessage(
              this.statusMap[arg[0]] + this.$t_common("success")
            );
          }
        })
        .finally(e => {
          this.search();
        });
    },
    handlerDelGroup(record, e) {
      this.handlerShowBtn(e);
      UiUtils.confirm({
        title: this.$t_process("delete_x_group", [record.name]),
        content: this.$t_process("group_and_template_deletion"),
        onOk: () => {
          this.handlerDeleteGroup(record, e);
        },
        onCancel: () => {
          this.handlerHideBtn(e);
        }
      });
    },
    handlerEdit(record) {
      this.$emit("update:visible", true);
      this.$emit("update:params", record);
    },

    handlerOk(type, data) {
      switch (type) {
        case "addprocess":
          this.handlerEdit({ id: data });
          break;
      }
      this.search();
    },
    customRow(record) {
      return {
        class: {
          "group-row": record.children
        }
      };
    },
    expandedRowsChange(rows) {
      this.expandedRowKeys = rows;
    },
    expandedRowsChangeLick(record) {
      if (this.expandedRowKeys.includes(record.id)) {
        this.expandedRowKeys = this.expandedRowKeys.filter(
          id => id !== record.id
        );
      } else {
        this.expandedRowKeys.push(record.id);
      }
    },
    handlerModel(type, data, e) {
      this.cmpvisible = true;
      this.componentId = type;
      this.componentProps = {
        data: data
      };
      this.handlerShowBtn(e);
    },
    setExpand() {
      this.$nextTick(() => {
        if (this.searchkey) {
          this.expandedRowKeys = this.data.map(item => item.id);
        }
      });
    },
    // ====== tools 格式化方法 =======
    formatlist(list) {
      for (const index in list) {
        list[index] = {
          ...list[index].scopedSlots,
          ...list[index],
          _first: index === "0",
          _last: index === list.length - 1 + ""
        };
        if (list[index].children) {
          this.formatlist(list[index].children);
        }
        if (list[index].templateFlag) {
          delete list[index].children;
        } else if (!list[index].children) {
          list[index].children = [];
        }
      }
    },
    // 处理表格表头合并
    coverStyle() {
      this.$nextTick(() => {
        Array.from(this.$el.querySelectorAll(".group-row")).forEach(row => {
          Array.from(row.children).forEach((item, index) => {
            if (index === 0) {
              item.setAttribute("colspan", this.columns.length);
            } else {
              item.style.display = "none";
            }
          });
        });
      });
    },
    handlerHideBtn() {
      this._currentHover && this._currentHover.classList.remove("hovered");
    },
    handlerShowBtn(e) {
      if (!e) return;
      this.handlerHideBtn();
      this._currentHover = e.currentTarget.parentNode;
      e.currentTarget.parentNode.classList.add("hovered");
    },
    setScrollHeight() {
      const dom_dataarea = this.$el.querySelector(".process-content");
      const dom_ob = this.$el.querySelector(".process-table");
      const dom_tools = this.$el.querySelector(".process-tools");
      const padding = 12;
      const header = 37;
      const tools = dom_tools.offsetHeight;
      if (this.sizeObserve) return;
      this.sizeObserve = new ResizeObserver(() => {
        const redux = header + padding + tools;
        const height = dom_dataarea.offsetHeight - redux;
        setTimeout(() => {
          const current = dom_ob.offsetHeight;
          if (current < height) {
            this.scrollHeight = true;
          } else {
            this.scrollHeight = dom_dataarea.offsetHeight - redux;
          }
        }, 0);
      });
      this.sizeObserve.observe(dom_ob);
    }
  }
};
</script>
<style lang="less" scoped>
.process-nodes {
  height: 100%;
  display: flex;
  flex-direction: column;
  font-size: 0.875rem;
  overflow: hidden;
}
.process-title {
  flex-shrink: 0;
  height: 2.75rem;
  background: @yn-body-background;
  box-shadow: 0px 1px 0.25rem 0px rgba(22, 24, 35, 0.06);
  font-size: 1rem;
  color: @yn-text-color;
  text-align: left;
  font-weight: 600;
  padding: 0.625rem 1.875rem;
}
.description {
  width: 14.875rem;
  height: 1.375rem;
  color: @yn-label-color;
  text-align: center;
  line-height: 1.375rem;
  font-weight: 400;
}
.process-spinning {
  height: 0 !important;
  flex-grow: 1;
  /deep/ & > .ant-spin-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}
.process-content {
  display: flex;
  height: 100%;
  width: 100%;
  flex-direction: column;
}
.process-tools {
  height: 3.5rem;
  padding: 0.75rem 0;
  display: flex;
  flex-shrink: 0;
  .process-search {
    width: 17.5rem;
  }
  .button-group {
    flex-shrink: 0;
    margin-left: auto;
    button {
      margin-left: 0.5rem;
    }
  }
}
.process-table {
  width: calc(100% + 10px);
  background: @yn-body-background;
  .group-button {
    visibility: hidden;
  }
  .numbercum {
    display: inline-block;
    white-space: nowrap;
    .numberIndex {
      margin-right: 1rem;
    }
  }
  /deep/ .ant-table-tbody > tr > td {
    padding-top: 0;
    padding-bottom: 0;
    height: 2.25rem;
  }
  .group-name {
    color: @yn-text-color;
    .groupname {
      margin-right: 0.75rem;
      cursor: pointer;
    }
    /deep/ i {
      // color: @yn-label-color;
      color: @yn-label-color;
    }
    &.hovered .group-button {
      visibility: visible;
    }
  }
  /deep/ .ant-table-thead tr th {
    background: @yn-table-header-bg;
  }
  /deep/ .group-row:hover td {
    background: @yn-hover-bg-color !important;
    .group-button {
      // display: inline-block;
      visibility: visible;
    }
  }
}
.txtmodel {
  padding: 0 0.5rem;
  cursor: pointer;
}
.action-button {
  color: @yn-primary-color;
  /deep/ i {
    color: @yn-primary-color;
  }
  &.no-padding {
    height: auto;
    padding-left: 0.125rem !important;
    padding-right: 0.125rem;
    margin-right: 1rem;
  }
}

/deep/ .ant-table-header {
  background: transparent;
}
/deep/ .ant-table-header::-webkit-scrollbar {
  border: 1px solid transparent !important;
  border-width: 0 0 1px 0;
}
</style>
<style lang="less">
.jsx-message .message-title {
  color: @yn-text-color-secondary;
  text-align: left;
  font-weight: 400;
  margin-bottom: 1rem;
}
.jsx-message .message-lists {
  max-height: 25rem;
  overflow: auto;
}
.jsx-message .message-list {
  color: @yn-text-color;
  text-align: left;
  font-weight: 400;
  margin-bottom: 0.25rem;
}
</style>
