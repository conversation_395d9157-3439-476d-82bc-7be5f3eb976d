import startpng from "@/image/start.png";
import endpng from "@/image/end.png";
import persionpng from "@/image/field-people.png";
import activepng from "@/image/c1_cr_activity.png";
import persionhpng from "@/image/hfield-people.png";
import activehpng from "@/image/hc1_cr_activity.png";
import fenpipng from "@/image/fenpi.png";
import fenpibpng from "@/image/fenpib.png";
// import persionpng from "@/image/persion.png";
// import activepng from "@/image/active.png";
// import spersionpng from "@/image/spersion.png";
// import sactivepng from "@/image/sactive.png";
export default {
  start: (node, nodefield) => {
    return `<div class="process-node start-node ${node.active ? "active" : ""}">
                <div class="light-out"><img src="${startpng}"/></div>
            </div>`;
  },
  approval: (node, nodefield) => {
    return `<div class="process-node approval-node ${
      node.active ? "active" : ""
    }">
              <div class="wrap">
                <img src="${node.active ? persionpng : persionhpng}"/>
                <span>${node[nodefield.name]}</span>
              </div>
            </div>`;
  },
  activity: (node, nodefield) => {
    return `<div class="process-node active-node ${
      node.active ? "active" : ""
    }">
              <div class="wrap">
                <img src="${node.active ? activepng : activehpng}"/>
                <span>${node[nodefield.name]}</span>
              </div>
            </div>`;
  },
  batch: (node, nodefield) => {
    return `<div class="process-node batch-node ${node.active ? "active" : ""}">
              <div class="wrap">
                <img src="${node.active ? fenpibpng : fenpipng}"/>
                <span>${node[nodefield.name]}</span>
              </div>
            </div>`;
  },
  end: (node, nodefield) => {
    return `<div class="process-node end-node ${node.active ? "active" : ""}">
                <div class="light-out"><img src="${endpng}"/></div>
            </div>`;
  }
};
