<template>
  <section class="process-nodes-wrap cs-container">
    <NodesList :visible.sync="isedit" :params.sync="params" />
    <EditNodes :visible.sync="isedit" :params.sync="params" />
  </section>
</template>

<script>
import EditNodes from "./editnodes.vue";
import NodesList from "./nodeslist.vue";

export default {
  components: { EditNodes, NodesList },
  data() {
    return {
      isedit: false,
      params: {}
    };
  },

  methods: {}
};
</script>
<style lang="less" scoped>
.process-nodes-wrap {
  height: 100%;
}
</style>
