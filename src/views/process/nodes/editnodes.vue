<template>
  <section v-show="visible" class="process-nodes-edit">
    <header class="process-title cs-header">
      <yn-button type="text" class="goback" size="small" @click="goback">
        <yn-icon type="left" />{{ $t_common("back") }}
      </yn-button>
      <yn-divider type="vertical" class="mydv" />
      {{ root.templateName || "" }}
      <div class="toolbox">
        <yn-button type="primary" class="tool-item" @click="setDetail">
          {{ $t_common("save") }}
        </yn-button>
      </div>
    </header>
    <yn-spin :spinning="spinning" class="process-spinning">
      <YnProcessTopo
        ref="topo"
        :meta="meta"
        :nodes="nodes"
        :links="links"
        :model="model"
        :field="field"
        :root="root"
        @event="handlerEvent"
      >
        <template #approval="{element}">
          <yn-spin :spinning="spinningside">
            <NodePanel
              :element="element"
              :root="root"
              @node-update="handlerUpdate"
            />
          </yn-spin>
        </template>
        <template #activity="{element}">
          <yn-spin :spinning="spinningside">
            <ActivePanel :element="element" @node-update="handlerUpdate" />
          </yn-spin>
        </template>
        <template #batch="{element}">
          <yn-spin :spinning="spinningside">
            <BatchPanel
              :element="element"
              :root="root"
              @node-update="handlerUpdate"
            />
          </yn-spin>
        </template>
        <template #root="{element}">
          <yn-spin :spinning="spinningside">
            <RootPanel
              :id="params.id"
              :element="element"
              @node-update="handlerUpdate"
            />
          </yn-spin>
        </template>
      </YnProcessTopo>
    </yn-spin>
  </section>
</template>
<script>
import YnProcessTopo from "../topo/YnProcessTopo";
import model from "./model";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-button/";
import RootPanel from "./item/rootpanel";
import NodePanel from "./item/nodepanel";
import ActivePanel from "./item/activepanel";
import BatchPanel from "./item/batchpanel";
import precessService from "@/services/process";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import { isType } from "@/utils/common";
import _cloneDeep from "lodash/cloneDeep";
import "yn-p1/libs/components/yn-divider/";
export default {
  components: { YnProcessTopo, RootPanel, NodePanel, ActivePanel, BatchPanel },
  props: {
    params: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      spinning: false,
      template: null,
      spinningside: false,
      field: {
        root: {
          type: "type",
          name: "templateName"
        },
        node: {
          id: "nodeId",
          name: "nodeShowName",
          type: "nodeType",
          anchor: "nodeAnchor",
          source: "sourceFlag",
          target: "targetFlag",
          position: "nodePosition"
        },
        link: {
          id: "objectId",
          type: "lineType",
          source: "sourceNode",
          target: "targetNode",
          anchor: "lineAnchor"
        }
      },
      meta: [
        {
          icon: "icon-c1_cr_begin",
          name: this.$t_process("start"),
          type: "start"
        },
        {
          icon: "icon-c1_cr_approval",
          name: this.$t_process("approve"),
          type: "approval"
        },
        {
          icon: "icon-c1_cr_activity",
          name: this.$t_process("activities"),
          type: "activity"
        },
        { icon: "icon-c1_cr_end", name: this.$t_process("end"), type: "end" },
        {
          icon: "icon-fenpi",
          name: this.$t_process("phase_submit"),
          type: "batch",
          showName: this.$t_process("phase_submit")
        }
      ],
      model: model,
      root: {},
      nodes: [],
      links: []
    };
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.search();
        }
      }
    }
  },
  methods: {
    async getDetail(value) {
      const {
        data: { data }
      } = await precessService("getDetail", value);
      this.template = {
        nodes: data.nodes.map(item => {
          const nodeAttribute = item.nodeAttribute;
          item.nodeShowName = nodeAttribute.nodeShowName;
          item.nodeType = nodeAttribute.nodeType;
          item.sourceFlag = nodeAttribute.sourceFlag;
          item.targetFlag = nodeAttribute.targetFlag;
          item.nodeAnchor = nodeAttribute.nodeAnchor.split(",");
          item.nodePosition = nodeAttribute.nodePosition.split(",");
          nodeAttribute.nodeButton = nodeAttribute.nodeButton.split(",");
          return item;
        }),
        root: data.templateAttribute,
        links: data.lines.map(item => {
          item.lineAnchor = item.lineAnchor.split(",");
          return item;
        })
      };
      return this.template;
    },
    async setDetail() {
      this.spinning = true;
      const params = {
        templateId: this.params.id,
        templateAttribute: this.root,
        nodes: this.nodes.map(item => {
          const temp = _cloneDeep(item);
          if (temp.nodeExecutor && temp.nodeExecutor.copy) {
            for (const key in temp.nodeExecutor.copy) {
              if (isType(temp.nodeExecutor.copy[key], "Array")) {
                temp.nodeExecutor.copy[key] = temp.nodeExecutor.copy[key]
                  .map(sitem => sitem.id)
                  .join(",");
              }
            }
          }
          if (temp.nodeExecutor && temp.nodeExecutor.executor) {
            temp.nodeExecutor.executor = temp.nodeExecutor.executor
              .filter(item => item)
              .map(sitem => {
                sitem.nodeUserAuth = sitem.nodeUserAuth || {};
                for (const key in sitem) {
                  if (isType(sitem[key], "Array")) {
                    sitem.nodeUserAuth[key] = sitem.nodeUserAuth[key] || [];
                    sitem[key].forEach(subItem => {
                      const userAuth = sitem.nodeUserAuth[key].find(
                        item => item.id === subItem.id
                      );
                      if (!userAuth) {
                        sitem.nodeUserAuth[key].push({
                          id: subItem.id,
                          scope: "",
                          entity: ""
                        });
                      }
                    });
                    sitem[key] = sitem[key].map(sitem => sitem.id).join(",");
                  }
                }
                return sitem;
              });
          }
          if (temp.processNodeMessages) {
            temp.processNodeMessages = temp.processNodeMessages
              .filter(item => item)
              .map(item => {
                const executor = item.messageUser;
                if (!executor) {
                  delete item.messageUser;
                  item.executor = "";
                  return item;
                } else {
                  executor.nodeUserAuth = executor.nodeUserAuth || {};
                  for (const key in executor) {
                    if (isType(executor[key], "Array")) {
                      executor.nodeUserAuth[key] =
                        executor.nodeUserAuth[key] || [];
                      executor[key].forEach(subItem => {
                        const userAuth = executor.nodeUserAuth[key].find(
                          item => item.id === subItem.id
                        );
                        if (!userAuth) {
                          executor.nodeUserAuth[key].push({
                            id: subItem.id,
                            scope: "",
                            entity: ""
                          });
                        }
                      });
                      executor[key] = executor[key]
                        .map(item => item.id)
                        .join(",");
                    }
                  }
                  delete item.messageUser;
                  item.executor = executor;
                  return item;
                }
              });
          }
          if (temp.batchAttributes) {
            temp.batchAttributes = temp.batchAttributes.map(batchItem => {
              batchItem.processNodeMessages = batchItem.processNodeMessages
                .filter(item => item)
                .map(item => {
                  const executor = item.messageUser;
                  if (!executor) {
                    delete item.messageUser;
                    item.executor = "";
                    return item;
                  } else {
                    executor.nodeUserAuth = executor.nodeUserAuth || {};
                    for (const key in executor) {
                      if (isType(executor[key], "Array")) {
                        executor.nodeUserAuth[key] =
                          executor.nodeUserAuth[key] || [];
                        executor[key].forEach(subItem => {
                          const userAuth = executor.nodeUserAuth[key].find(
                            item => item.id === subItem.id
                          );
                          if (!userAuth) {
                            executor.nodeUserAuth[key].push({
                              id: subItem.id,
                              scope: "",
                              entity: ""
                            });
                          }
                        });
                        executor[key] = executor[key]
                          .map(item => item.id)
                          .join(",");
                      }
                    }
                    delete item.messageUser;
                    item.executor = executor;
                    return item;
                  }
                });
              return batchItem;
            });
          }
          temp.nodeAttribute = {
            ...temp.nodeAttribute,
            nodeShowName: temp.nodeShowName,
            nodeType: temp.nodeType,
            nodePosition: temp.nodePosition.join(","),
            nodeAnchor: temp.nodeAnchor.join(","),
            sourceFlag: temp.sourceFlag,
            targetFlag: temp.targetFlag,
            nodeButton:
              temp.nodeAttribute &&
              isType(temp.nodeAttribute.nodeButton, "Array")
                ? temp.nodeAttribute.nodeButton.join(",")
                : ""
          };
          return temp;
        }),
        lines: this.links.map(item => {
          const temp = { ...item };
          temp.lineAnchor = temp.lineAnchor.join(",");
          return temp;
        })
      };
      return precessService("setDetail", params)
        .then(res => {
          const data = res.data.data;
          if (data) {
            UiUtils.info({
              title: this.$t_process("save_not_activated"),
              class: "jsx-message",
              content: h => (
                <div>
                  <div class="message-title">
                    {this.$t_process("multiple_templates_warning")}
                  </div>
                  <ul class="message-lists">
                    {data.map(li => {
                      return <li class="message-list">{li}</li>;
                    })}
                  </ul>
                </div>
              )
            });
          } else {
            UiUtils.successMessage(
              this.$t_process("process_template") +
                this.$t_common("save_success")
            );
          }
          this.clearCommonSaveEventsMixin();
          this.goback();
          // this.search();
        })
        .catch(e => {
          if (this.root.entity === "" && this.root.scope === "") {
            this.$refs.topo.showRoot();
          }
          return Promise.reject();
        })
        .finally(e => {
          this.spinning = false;
        });
    },

    async search(value = this.params) {
      this.spinning = true;
      const { root, nodes, links } = await this.getDetail(value);
      this.root = root;
      this.nodes = nodes;
      this.links = links;
      this.spinning = false;
    },
    handlerUpdate(node) {
      this.visible &&
        this.addCallBackFnMixin(
          this.setDetail,
          this.$t_process("save_change", [this.root.templateName])
        );
    },
    handlerEvent(event, data) {
      switch (event) {
        case "node-add":
        case "link-add":
        case "link-delete":
        case "node-delete":
        case "node-drag":
          this.addCallBackFnMixin(
            this.setDetail,
            this.$t_process("save_change", [this.root.templateName])
          );
          break;
      }
    },
    goback() {
      this.savePromptMixin().then(() => {
        this.$nextTick(() => {
          this.$emit("update:visible", false);
        });
      });
    },
    validate() {}
  }
};
</script>

<style lang="less" scoped>
.process-nodes-edit {
  height: 100%;
  display: flex;
  flex-direction: column;
  font-size: 0.875rem;
  // font-family: PingFangSC-Semibold;
}

.process-title {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  height: 2.75rem;
  background: @yn-body-background;
  font-size: 1rem;
  color: @yn-text-color;
  text-align: left;
  font-weight: 600;
  padding: 0.875rem 1rem;
  .goback {
    margin-left: -0.5rem;
    color: @yn-label-color;
    &:hover {
      color: @yn-primary-color;
    }
  }
  .mydv {
    height: 1rem;
    margin-right: 0.75rem;
    margin-left: 0.25rem;
    top: 0;
  }
  .toolbox {
    margin-left: auto;
  }
}
.process-spinning {
  height: 0 !important;
  flex: 1;
  /deep/ & > .ant-spin-container {
    height: 100%;
  }
}
// ======= default ===========
/deep/ .process-node.active {
  color: #fff;
  &.active-node {
    border: 1px solid @yn-primary-color;
    .wrap {
      background: @yn-primary-color;
    }
  }
  &.approval-node {
    border: 1px solid @yn-primary-color;
    .wrap {
      background: @yn-primary-color;
    }
  }
  &.batch-node {
    border: 1px solid @yn-primary-color;
    .wrap {
      background: @yn-primary-color;
    }
  }
}
/deep/ .end-node,
/deep/ .start-node {
  display: inline-block;
  opacity: 1;
  .light-out {
    width: 3.25rem;
    height: 3.25rem;
    line-height: 3.25rem;
    text-align: center;
    border-radius: 50%;
  }
  img {
    width: 2.25rem;
    height: 2.25rem;
  }
}
/deep/ .start-node {
  .light-out {
    background: rgba(23, 192, 164, 0.1);
  }
}
/deep/ .end-node {
  .light-out {
    background: rgba(255, 132, 97, 0.1);
  }
}
/deep/ .active-node,
/deep/ .batch-node,
/deep/ .approval-node {
  border-radius: 0.25rem;
  padding: 1px;
  color: @yn-text-color;
}
/deep/ .active-node {
  border: 1px solid @yn-border-color-base;
}
/deep/ .approval-node {
  border: 1px solid @yn-border-color-base;
}
/deep/ .batch-node {
  border: 1px solid @yn-border-color-base;
  width: 8rem;
}
/deep/ .active-node .wrap {
  background: @yn-background-color;
}
/deep/ .batch-node .wrap {
  background: @yn-background-color;
  max-width: 8rem !important;
}
/deep/ .approval-node .wrap {
  background: @yn-background-color;
}
/deep/ .active-node .wrap,
/deep/ .batch-node .wrap,
/deep/ .approval-node .wrap {
  display: flex;
  min-height: 2.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  text-align: center;
  min-width: 6.125rem;
  max-width: 6.125rem;
  white-space: pre-wrap;
  word-break: break-all;
  padding: 0.5rem 0.75rem;
  img {
    width: 0.875rem;
    margin-right: 0.75rem;
  }
}
</style>

<style lang="less">
.jsx-message .message-title {
  color: @yn-text-color-secondary;
  text-align: left;
  font-weight: 400;
  margin-bottom: 1rem;
}
.jsx-message .message-lists {
  max-height: 25rem;
  overflow: auto;
}
.jsx-message .message-list {
  color: @yn-text-color;
  text-align: left;
  font-weight: 400;
  margin-bottom: 0.25rem;
}
</style>
