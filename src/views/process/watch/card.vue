<template>
  <div v-if="cards.length > 0" class="process-watch-cards">
    <yn-spin :spinning="cardLoading" class="spinning">
      <draggable
        v-model="cards"
        class="cards"
        chosenClass="chosen"
        forceFallback="true"
        group="cards"
        :disabled="true"
        animation="1000"
        @start="onStart"
        @end="onEnd"
      >
        <transition-group>
          <div
            v-for="card in cards"
            :key="card.processMonitorCode"
            class="card"
          >
            <div class="info">
              <span class="card-title">{{
                settingMap[card.processMonitorCode]
              }}</span>
              <yn-tooltip placement="top">
                <template slot="title">
                  <span>{{ card.hint }}</span>
                </template>
                <svg-icon :isIconBtn="false" type="hint" />
              </yn-tooltip>
            </div>
            <div v-if="!card.message" class="card-summary">
              <span class="total">
                <span class="name">{{ $t_process("total") }}</span>
                <span class="num">{{ card.finishNum }}</span>
                / {{ card.totalNum }}
              </span>
              <span class="compare">
                <span class="name">{{
                  $t_process("compared_to_yesterday")
                }}</span>
                <span
                  :class="[
                    'status',
                    {
                      up: card.compareYesterday > 0,
                      down: card.compareYesterday < 0
                    }
                  ]"
                >
                  {{
                    card.compareYesterday === 0
                      ? $t_process("flat")
                      : card.compareYesterday
                  }}
                  <svg-icon
                    v-if="card.compareYesterday"
                    :isIconBtn="false"
                    :type="
                      card.compareYesterday > 0 ? 'header-up' : 'header-down'
                    "
                  />
                </span>
              </span>
            </div>
            <div class="card-chart ">
              <Gauge
                v-if="!card.message"
                :ref="card.processMonitorCode"
                :rate="card.finishRate"
                :name="$t_process('completion_rate')"
                :color="
                  cardColorMap[parseFloat(card.finishRate)] ||
                    cardColorMap[card.processMonitorCode]
                "
              />
              <yn-empty
                v-else
                class="message-empty"
                :description="card.message"
              />
            </div>
          </div>
        </transition-group>
      </draggable>
    </yn-spin>
  </div>
</template>
<script>
import { mapState, mapMutations } from "vuex";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-empty/";
import Gauge from "./Gauge.vue";
import draggable from "vuedraggable";
import "yn-p1/libs/components/yn-tooltip/";
import { settingMap, cardColorMap, hintMap } from "./constants";
// import { card } from "./mock";
export default {
  components: { Gauge, draggable },
  data() {
    return {
      settingMap,
      cardColorMap,
      cards: []
    };
  },
  computed: {
    ...mapState("processStore", {
      setting: state => state.setting,
      cardLoading: state => state.cardLoading,
      cardsData: state => state.cardsData
    })
  },
  watch: {
    setting: {
      handler() {
        this.format();
      }
    },
    cardsData: {
      handler() {
        this.format();
      }
    }
  },
  methods: {
    ...mapMutations({
      setSetting: "processStore/setSetting"
    }),
    onStart(e) {
      this.clone(e);
    },
    onEnd(e) {
      this.setSetting(this.cards.map(card => card.processMonitorCode));
      // todo 保存setting 到后台
    },
    clone(e) {
      const canvas = e.item.querySelector("canvas");
      const target = e.to.querySelector(".sortable-fallback .gauge canvas");
      target
        .getContext("2d")
        .drawImage(canvas, 0, 0, canvas.width, canvas.height);
    },
    format() {
      // const setting = this.setting;
      const setting = Object.keys(settingMap);
      if (this.cardsData.length > 0) {
        this.cards = [...this.cardsData].sort((a, b) => {
          const indexA = setting.indexOf(a.processMonitorCode) || 0;
          const indexB = setting.indexOf(b.processMonitorCode) || 0;
          return indexA - indexB;
        });
      } else {
        this.cards = this.getEmpty();
      }
    },
    getEmpty() {
      // return card.data.processMonitorStatsList;
      return this.setting
        .filter(item => item)
        .map(item => {
          return {
            compareYesterday: 0,
            finishNum: 0,
            finishRate: "0.0%",
            hint: hintMap[item],
            processMonitorCode: item,
            totalNum: 0
          };
        });
    }
  }
};
</script>

<style lang="less" scoped>
.process-watch-cards {
  .spinning {
    position: relative !important;
  }
  .cards {
    display: flex;
    height: 100%;
    width: calc(100% + 1rem);
    > span {
      display: flex;
      width: 100%;
    }
  }
  .card {
    background-color: #fff;
    border-radius: 4px;
    padding: 1rem;
    margin-right: 0.75rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    user-select: none;
    border: solid 1px #fff;
    hover {
      border: solid 1px @yn-primary-color !important;
      cursor: move;
    }
    .info {
      color: @yn-label-color;
      flex-shrink: 0;
      height: 1.5rem;
      margin-bottom: 0.625rem;
      .card-title {
        color: @yn-text-color-secondary;
        margin-right: 0.25rem;
        font-size: 1rem;
      }
    }

    .card-summary {
      font-size: 0.875rem;
      color: @yn-label-color;
      margin-bottom: 0.625rem;
      flex-shrink: 0;
      .name {
        margin-right: 0.25rem;
        color: @yn-label-color;
      }
      .total {
        margin-right: 2rem;
        .num {
          color: @yn-text-color-secondary;
        }
      }
      .compare {
        .status {
          color: @yn-label-color;
          font-size: 0.875rem;
          &.up {
            color: @yn-chart-2;
          }
          &.down {
            color: @yn-error-color;
          }
        }
        /deep/ .svg-icon {
          font-size: 0.875rem;
        }
      }
    }
    .card-chart {
      flex: 1;
      height: 0;
      display: flex;
      justify-content: center;
    }
  }
  .message-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    justify-content: center;

    /deep/ .ant-empty-image {
      height: 5rem;
    }
  }
  /deep/ .chosen {
    border: solid 1px @yn-primary-color !important;
  }
}
</style>
