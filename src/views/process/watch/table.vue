<template>
  <div class="process-watch-table">
    <div class="table-title">
      {{ $t_process("process_progress") }}
      <yn-button
        size="small"
        class="export-button"
        :disabled="gridTableData.length === 0"
        @click="handlerExport"
      >
        <yn-icon type="export" class="export" /> {{ $t_common("export") }}
      </yn-button>
    </div>
    <yn-spin :spinning="loading" class="spinning">
      <TreeGrid
        ref="grid"
        class="grid"
        ident=""
        ident_bk="processMonitor"
        rowKey="objectId"
        :hasMore="false"
        :fixedColumns="flexed"
        :columns="columns"
        :dataSource="gridTableData"
        :paginationInfo="pagination"
        :expandedRowKeys="expandedRowKeys"
        :renderHeaderCell="renderHeaderCell"
        :cellNode="renderBodyCell"
        v-on="$listeners"
        @expand="expand"
      />
    </yn-spin>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-button/";
import TreeGrid from "@/components/hoc/treeGrid";
import precessService from "@/services/process";
import "yn-p1/libs/components/yn-spin/";
import _uniqueId from "lodash/uniqueId";
// import { table } from "./mock";
import processDom from "./jdom/process";
import { settingCodeMap, tableKeyMap, hintMap, settingMap } from "./constants";
import { mapState, mapMutations } from "vuex";
import { downloadFile } from "@/utils/common";

export default {
  components: { TreeGrid },
  data() {
    return {
      arch: [
        {
          fixed: "left",
          title: this.$t_process("organization_structure"),
          dataIndex: "memberName",
          key: "memberName",
          cellType: "text",
          width: 300
        }
      ],
      loading: false,
      message: "",
      OriginColumns: [
        {
          title: this.$t_process("single_data_prep"),
          dataIndex: "entityReady",
          key: "entityReady",
          cellType: "text"
        },
        {
          title: this.$t_process("ic_reconciliation"),
          dataIndex: "entityReconciliation",
          key: "entityReconciliation",
          cellType: "text"
        },
        {
          title: this.$t_process("single_submission"),
          dataIndex: "entitySubmit",
          key: "entitySubmit",
          cellType: "text"
        },
        {
          title: this.$t_process("consolidation_prep"),
          dataIndex: "scopeReady",
          key: "scopeReady",
          cellType: "text"
        },
        {
          title: this.$t_process("consolidation_submission"),
          dataIndex: "scopeSubmit",
          key: "scopeSubmit",
          cellType: "text"
        }
      ],
      columns: [],
      pagination: {
        total: 0,
        current: 1,
        pageSize: 100000,
        hasMore: false,
        offset: 0
      },
      flexed: { left: 1 },
      expandedRowKeys: [],
      gridTableData: []
    };
  },
  computed: {
    ...mapState("processStore", {
      setting: state => state.setting,
      refresh: state => state.refresh,
      watchParams: state => state.watchParams,
      watchViewParams: state => state.watchViewParams,
      scopeInfo: state => state.scopeInfo
    }),
    params() {
      return {
        ...this.watchParams,
        ...this.scopeInfo
      };
    }
  },
  watch: {
    params: {
      handler() {
        this.loading = true;
      },
      immediate: true
    },
    scopeInfo: {
      handler() {
        this.getProcessMonitor();
      }
    },
    setting: {
      handler(value, old) {
        this.$nextTick(() => {
          this.loading = true;
          this.resetColumn();
          const hasParams = !(
            [...new Set(Object.values(this.params))].length < 5
          );
          if (hasParams && old) {
            this.getProcessMonitor();
          } else {
            this.loading = false;
          }
          if (!hasParams) {
            this.setCardLoading(false);
          }
        });
      }
    }
  },
  mounted() {
    this.$resizeObserver = new ResizeObserver(() => {
      // this.render();
      this.resetColumn();
    });
    this.$resizeObserver.observe(this.$el);
  },
  methods: {
    ...mapMutations({
      setCardsData: "processStore/setCardsData",
      setCardLoading: "processStore/setCardLoading"
    }),
    async handlerExport() {
      const { version, year, period } = this.watchViewParams;
      precessService("exportProcessMonitor", {
        excelName: version + year + period + this.scopeInfo.memberName,
        ...this.params
      }).then(res => {
        downloadFile(res);
      });
    },
    async getProcessMonitor(queryId = this.setQueryId()) {
      let result = [];
      let cardRes = [];
      this.loading = true;
      this.setCardLoading(true);
      if (!([...new Set(Object.values(this.params))].length < 5)) {
        const {
          data: { data }
        } = await precessService("getProcessMonitor", this.params).catch(e => {
          this.loading = false;
          this.setCardLoading(false);
        });
        this.message = data ? data.message : "";
        result = data ? data.processMonitorList : [];
        cardRes = data ? data.processMonitorStatsList : [];
      }
      if (queryId === this.getQueryId()) {
        this.setExpand(result);
        this.gridTableData = result;
        this.setCardsData(
          cardRes.map(item => {
            item.hint = hintMap[item.processMonitorCode];
            item.message =
              tableKeyMap.entityReady === item.processMonitorCode
                ? this.message
                : "";
            return item;
          })
        );
        this.updateTable();
        this.setCardLoading(false);
        (this.arrData.length + 1) * 36;
        this.loading = false;
      }
    },
    resetColumn() {
      const setting = Object.keys(settingMap);
      this.columns = [
        ...this.arch,
        ...this.OriginColumns.filter(item => {
          const dataIndex = settingCodeMap[item.dataIndex];
          return this.setting.includes(dataIndex);
        }).sort((a, b) => {
          const indexA = setting.indexOf(tableKeyMap[a.key]) || 0;
          const indexB = setting.indexOf(tableKeyMap[b.key]) || 0;
          return indexA - indexB;
        })
      ];
    },
    updateTable() {
      this.$nextTick(() => {
        this.$refs.grid && this.$refs.grid.updateTable();
      });
    },
    renderHeaderCell(cellContext) {
      return false;
    },
    renderBodyCell(cellContext) {
      const { rowId, col } = cellContext;
      const record = this.mapData[rowId];
      const name = this.columns[col].key;
      switch (name) {
        case "entityReady":
          return this.renderEntityReady(cellContext, record);
        case "entityReconciliation":
          return this.renderEntityReconciliation(cellContext, record);
        case "entitySubmit":
          return this.renderEntitySubmit(cellContext, record);
        case "scopeReady":
          return this.renderScopeReady(cellContext, record);
        case "scopeSubmit":
          return this.renderScopeSubmit(cellContext, record);
        default:
          return "";
      }
    },
    renderEntityReady(cellContext, record) {
      if (this.message) {
        return "-";
      }
      if (record.dimCode === "Entity") {
        return this.renderProcess(record.entityReady.v);
      } else {
        return record.entityReady.v;
      }
    },
    renderEntityReconciliation(cellContext, record) {
      if (record.dimCode === "Entity") {
        return this.renderProcess(record.entityReconciliation.v);
      } else {
        return record.entityReconciliation.v;
      }
    },
    renderEntitySubmit(cellContext, record) {
      if (record.dimCode === "Entity") {
        return this.renderProcess(record.entitySubmit.v);
      } else {
        return record.entitySubmit.v;
      }
    },
    renderScopeReady(cellContext, record) {
      if (record.dimCode === "Scope") {
        return this.renderProcess(record.scopeReady.v);
      } else {
        return record.scopeReady.v;
      }
    },
    renderScopeSubmit(cellContext, record) {
      if (record.dimCode === "Scope") {
        return this.renderProcess(record.scopeSubmit.v);
      } else {
        return record.scopeSubmit.v;
      }
    },
    renderProcess(value) {
      const process = value || "0.0%";
      const status = process === "100%" ? "success" : "process";
      return processDom({
        status: status,
        process: process,
        prefix: `${this.$t_process("completion_rate")} `
      });
    },
    expand(expandedKeys) {
      this.expandedRowKeys = expandedKeys;
    },
    setExpand(data) {
      const expandedRowKeys = [];
      const mapData = {};
      const arrData = [];
      this.setSafeTree(data, expandedRowKeys, mapData, arrData);
      this.expandedRowKeys = expandedRowKeys;
      this.mapData = mapData;
      this.arrData = arrData;
    },
    setPropsToObj(obj) {
      const { objectId, parentId } = obj;
      this.columns.forEach(column => {
        obj[column.dataIndex] = {
          v: obj[column.dataIndex]
        };
      });
      obj.parentId = { v: parentId };
      obj.objectId = objectId;
    },
    setSafeTree(
      tree,
      expandedRowKeys,
      map,
      array,
      level = 0,
      parentId = "-1",
      next = { index: 0 }
    ) {
      if (tree) {
        for (const item of tree) {
          map[item.objectId] = item;
          item.parentId = parentId;
          item.level = level;
          array[next.index] = item;
          next.index += 1;
          expandedRowKeys && expandedRowKeys.push(item.objectId);
          this.setPropsToObj(item);
          if (item.children && item.children.length > 0) {
            this.setSafeTree(
              item.children,
              expandedRowKeys,
              map,
              array,
              level + 1,
              item.objectId,
              next
            );
          } else {
            delete item.children;
          }
        }
      }
    },
    setQueryId() {
      this._currentQueryId = _uniqueId("QueryId");
      return this._currentQueryId;
    },
    getQueryId() {
      return this._currentQueryId;
    }
  }
};
</script>

<style lang="less" scoped>
.process-watch-table {
  border-radius: 4px;
  background: #ffffff;
  padding: 1rem;
  padding-right: calc(1rem - 0.625rem);
  height: 100%;
  display: flex;
  flex-direction: column;
  color: @yn-text-color;
  .table-title {
    display: flex;
    align-items: center;
    font-size: 1rem;
    height: 1.5rem;
    margin-bottom: 0.75rem;
    color: @yn-text-color;
    flex-shrink: 0;
    .export-button {
      margin-left: auto;
      &[disabled="disabled"] {
        .export {
          color: @yn-disabled-color !important;
        }
      }
      margin-right: 0.625rem;
      &:hover {
        .export {
          color: @yn-primary-color;
        }
      }
      .export {
        color: @yn-label-color;
        &:hover {
          color: @yn-primary-color;
        }
      }
    }
  }
  .grid {
    flex: 1;
    width: 100%;
  }
  .spinning {
    position: relative !important;
  }

  /deep/ .htBorders {
    display: none;
  }
  /deep/ .handsontable td.simple-cell {
    border-left-color: transparent !important;
    border-right-color: transparent !important;
  }
  /deep/ .simple-cell .custom-span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
  /deep/ .ant-spin-blur {
    .ant-spin-nested-loading .ant-spin-dot {
      display: none;
    }
    .grid-empty {
      display: none;
    }
  }
  /deep/ .header-col-text {
    color: @yn-label-color;
  }

  /deep/ .handsontable td {
    // border-right: 0;
    // border-left: 0;
  }
}
</style>
