<template>
  <div class="process-chart">
    <yn-empty v-if="data.length === 0" class="empty" />
    <div v-for="item in data" v-else :key="item.name" class="chart-item">
      <div class="chart-item-content">
        <yn-tooltip placement="top">
          <template slot="title">
            <span>{{ item.name }}</span>
          </template>
          <span class="name">{{ item.name }}</span>
        </yn-tooltip>
        <div
          class="process ant-progress ant-progress-line ant-progress-status-normal ant-progress-show-info ant-progress-default"
        >
          <div>
            <div class="ant-progress-outer">
              <div class="ant-progress-inner">
                <div
                  class="ant-progress-bg"
                  :style="{ width: item.value + '%' }"
                >
                  <span class="custom-progress-text">{{ item.rate }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-tooltip/";
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  }
};
</script>

<style scoped lang="less">
.process-chart {
  height: 100%;
  .chart-item {
    margin-bottom: 1.125rem;
    .chart-item-content {
      display: flex;
    }
    &:last-child {
      margin-bottom: 0;
    }
    .name {
      flex-basis: 182px;
      margin-right: 1rem;
      flex-shrink: 0;
      font-size: 0.875rem;
      color: @yn-text-color-secondary;
      text-align: right;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1rem;
    }
    .process {
      flex: 1;
      flex-basis: 597px;
      line-height: 1rem;
    }
  }
  .empty {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  /deep/ .ant-progress-outer {
    margin-right: 0 !important;
    padding-right: 0 !important;
    line-height: 1rem;
  }
  /deep/ .ant-progress-bg {
    position: relative;
    height: 1rem !important;
    box-sizing: border-box;
    border-radius: 0 !important;
  }
  .custom-progress-text {
    position: absolute;
    padding-left: 0.75rem;
    left: 100%;
    color: @yn-label-color;
    line-height: 1rem;
  }
  /deep/ .ant-progress-inner {
    border-radius: 0;
    background-color: @yn-background-color !important;
    padding-right: 3.25rem;
    background-clip: content-box;
  }

  /deep/ .ant-progress-text {
    display: none;
  }
}
</style>
