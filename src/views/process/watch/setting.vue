<template>
  <yn-dropdown
    v-model="visible"
    :trigger="['click']"
    placement="bottomRight"
    @visibleChange="handlerVisibleChange"
  >
    <yn-button class="setting-btn">
      <svg-icon type="set-up" class="setting" />{{ $t_common("settings") }}
    </yn-button>
    <div slot="overlay" class="setting-overlay">
      <yn-checkbox-group v-model="settingCache" @change="onChange">
        <yn-row>
          <yn-col
            v-for="(value, key) in settingMap"
            :key="key"
            :span="24"
            class="setting-col"
          >
            <yn-checkbox
              :disabled="
                settingCache.includes(key) && settingCache.length === 1
              "
              :value="key"
            >
              {{ value }}
            </yn-checkbox>
          </yn-col>
        </yn-row>
      </yn-checkbox-group>
      <yn-divider class="setting-divider" />
      <div class="setting-buttons">
        <yn-button class="reset-btn" @click="cancelSetting">
          {{ $t_common("reset") }}
        </yn-button>
        <yn-button type="primary" @click="submitSetting">
          {{ $t_common("ok") }}
        </yn-button>
      </div>
    </div>
  </yn-dropdown>
</template>
<script>
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-col/";
import "yn-p1/libs/components/yn-row/";
import { mapState, mapMutations } from "vuex";
import commonService from "@/services/common";
import { settingMap } from "./constants";
const EMPTY = "-";
export default {
  data() {
    return {
      visible: false,
      settingMap,
      objectId: "",
      settingCache: []
    };
  },
  computed: {
    ...mapState("processStore", {
      setting: state => state.setting,
      refresh: state => state.refresh
    })
  },
  watch: {
    refresh: {
      handler() {
        this.getLastSettingV();
      }
    },
    setting(value) {
      this.setCardLoading(true);
      this.settingCache = value;
      this.saveSetting();
      this.settingCacheOrigin = [...value];
    }
  },
  created() {
    this.getLastSettingV();
  },
  methods: {
    ...mapMutations({
      setSetting: "processStore/setSetting",
      setCardLoading: "processStore/setCardLoading"
    }),
    async getLastSettingV() {
      commonService("getLastSettingV", {
        key: "processMonitor",
        tag: "userIndexName"
      }).then(res => {
        const { objectId, value } = res.data ? res.data.data : {};
        this.objectId = objectId;
        this.settingCacheOrigin =
          value === EMPTY || value === ""
            ? Object.keys(settingMap)
            : value.split("-");
        this.settingCache = [...this.settingCacheOrigin];
        this.setCardLoading(true);
        this.setSetting(this.settingCache);
      });
    },
    onChange(checkedValues) {
      const setting = Object.keys(settingMap);
      this.settingCache = checkedValues.sort((a, b) => {
        const indexA = setting.indexOf(a) || 0;
        const indexB = setting.indexOf(b) || 0;
        return indexA - indexB;
      });
      this.visible = true;
    },
    handlerVisibleChange(visible) {
      this.visible = visible;
      this.settingCache = this.setting;
    },
    cancelSetting() {
      // this.settingCache = [...this.settingCacheOrigin];
      this.settingCache = Object.keys(settingMap);
      this.submitSetting();
    },
    async submitSetting() {
      await this.saveSetting();
      this.visible = false;
      this.setCardLoading(true);
      this.setSetting(this.settingCache);
      this.settingCacheOrigin = [...this.settingCache];
    },
    async saveSetting() {
      await commonService("saveOrUpdateUserSetting", {
        key: "processMonitor",
        tag: "userIndexName",
        objectId: this.objectId,
        value: this.settingCache.join("-") || EMPTY
      });
    }
  }
};
</script>
<style lang="less" scoped>
.setting-btn {
  &:hover {
    .setting {
      color: @yn-primary-color;
      background: transparent;
    }
  }
}

.setting {
  padding: 0 0.25rem 0 0;
  height: 0.85rem;
  line-height: 0.85rem;
  &:hover {
    color: @yn-primary-color;
    background: transparent;
  }
}
</style>
<style lang="less">
.setting-overlay {
  width: 12.5rem;
  padding-top: 4px;
  border-radius: 4px;
  opacity: 1;
  background: #ffffff;
  box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.14),
    0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 9px 28px 8px rgba(0, 0, 0, 0.05);
  .setting-col {
    width: 100%;
    height: 2rem;
    opacity: 1;
    padding: 0.5rem 1rem;
  }
  .setting-divider {
    margin: 0;
    margin-top: -0.3125rem;
  }
  .setting-buttons {
    padding: 0 0.5rem;
    height: 2.625rem;
    line-height: 2.625rem;
    text-align: right;
    .reset-btn {
      margin-right: 0.5rem;
    }
  }
}
</style>
