<template>
  <div class="dim">
    <yn-popconfirm
      placement="bottomLeft"
      :visible="popVisible"
      :okText="$t_common('ok')"
      :cancelText="$t_common('cancel')"
      overlayClassName="my-popconfirm"
      @cancel="cancelEvent"
      @click.native.stop
      @confirm="search"
    >
      <label slot="icon"></label>
      <template slot="title">
        <yn-form
          :form="filterForm"
          layout="horizontal"
          class="filter-form"
          @click.native.stop
        >
          <yn-form-item
            :label="$t_common('version')"
            :labelCol="formItemLayout.labelCol"
            :wrapperCol="formItemLayout.wrapperCol"
            @click.native.stop
          >
            <asyn-select-dimMember
              v-decorator="['version', { initialValue: defaultObj.version }]"
              dimCode="Version"
              :allowClear="false"
              isSetDefaultVal
              class="my-select"
              :nonleafselectable="false"
              @click.native.stop="clickForm"
              @changeVal="handlerChange"
            />
          </yn-form-item>
          <yn-form-item
            :label="$t_common('year')"
            :labelCol="formItemLayout.labelCol"
            :wrapperCol="formItemLayout.wrapperCol"
            @click.native.stop
          >
            <asyn-select-dimMember
              v-decorator="['year', { initialValue: defaultObj.year }]"
              dimCode="Year"
              :allowClear="false"
              isSetDefaultVal
              class="my-select"
              :nonleafselectable="false"
              @changeVal="handlerChange"
              @click.native.stop="clickForm"
            />
          </yn-form-item>
          <yn-form-item
            :label="$t_common('period')"
            :labelCol="formItemLayout.labelCol"
            :wrapperCol="formItemLayout.wrapperCol"
            @click.native.stop
          >
            <asyn-select-dimMember
              v-decorator="['period', { initialValue: defaultObj.period }]"
              dimCode="Period"
              :allowClear="false"
              isSetDefaultVal
              :nonleafselectable="false"
              class="my-select"
              @changeVal="handlerChange"
              @click.native.stop="clickForm"
            />
          </yn-form-item>
        </yn-form>
      </template>
      <div class="bread" @click="popVisible = true">
        <span class="item"> {{ selectedMap[formValue.version] }} </span> /
        <span class="item"> {{ selectedMap[formValue.year] }} </span> /
        <span class="item"> {{ selectedMap[formValue.period] }}</span>
        <svg-icon type="down" class="open-tilter" :isIconBtn="false" />
      </div>
    </yn-popconfirm>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-popconfirm/";
import _capitalize from "lodash/capitalize";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
import { mapMutations } from "vuex";
import commonService from "@/services/common";

export default {
  components: { AsynSelectDimMember },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  inject: ["defaultParams"],
  data() {
    return {
      formItemLayout: {
        labelCol: {
          span: 4
        },
        wrapperCol: {
          span: 20
        }
      },
      filterForm: this.$form.createForm(this, "filterForm"),
      selectedMap: {},
      selectedMapCache: {},
      formValue: {},
      formValueCache: {},
      defaultObj: {}
    };
  },
  computed: {
    popVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit("update:visible", value);
      }
    }
  },
  watch: {
    popVisible(value) {
      value &&
        this.$nextTick(() => {
          this.filterForm.setFieldsValue(this.formValue);
        });
    }
  },
  async created() {
    this.setDefault();
    this.autoClose();
  },
  methods: {
    ...mapMutations({
      setWatchViewParams: "processStore/setWatchViewParams",
      setWatchParams: "processStore/setWatchParams"
    }),
    autoClose() {
      document.body.addEventListener("click", e => {
        if (
          e.target.classList.contains("ant-popover-message-title") ||
          e.target.classList.contains("ant-popover-buttons") ||
          e.target.classList.contains("ant-popover-inner-content")
        ) {
          e.stopPropagation();
          return false;
        }
        this.popVisible = false;
      });
    },
    handlerChangeScope(values, items) {},
    async setProcessParams() {
      const params = this.defaultParams || {};
      this.defaultObj = {
        year: params.year,
        version: params.version,
        period: params.period
      };
      const asyncArr = [];
      for (const key in this.defaultObj) {
        asyncArr.push(
          this.getMemberName({
            demCode: _capitalize(key),
            dimMemberId: this.defaultObj[key]
          })
        );
      }
      await Promise.all(asyncArr).then(data => {
        data.forEach(item => {
          // 更新表单默认值，同步默认值到表单缓存
          this.selectedMapCache[item.objectId] = item.dimMemberRealName;
          this.formValueCache[item.dimCode.toLowerCase()] = item.objectId;
        });
      });
    },
    async getMemberName(params) {
      const { data } = await commonService("getQueryDimMemberList", {
        dimMemberId: params.dimMemberId,
        dimCode: params.demCode,
        hasAllMembers: false,
        limit: 10,
        memberExp: null,
        needFilterAudittrail: false,
        needFilterShared: false,
        needPermission: false,
        offset: 0
      });
      return data.items[0];
    },
    async setPovParams() {
      // 请求pov的值，设置默认选中结果,初始化
      const DIMS = ["version", "year", "period"];
      await this.selectUserPov(data => {
        DIMS.forEach(item => {
          const id = data[item].memberId;
          // 更新表单默认值，同步默认值到表单缓存
          this.selectedMapCache[id] = data[item].dimMemberRealName;
          this.formValueCache[item] = id;
          this.$set(this.defaultObj, item, id);
        });
      });
    },
    async setDefault() {
      if (Object.keys(this.defaultParams).length > 0) {
        // 从流程打开，参数来自流程，
        await this.setProcessParams();
      } else {
        // 从页签，参数来pov，
        await this.setPovParams();
      }
      Promise.all([
        this.getFirstYear(),
        this.getFirstPeriod(),
        this.getFirstVersion()
      ]).then(() => {
        this.search();
      });
    },
    handlerChange(values, items) {
      // 表单内容改变缓存更新值, 在触发保存事件后再更新值，取消则还原
      const { dimMemberRealName = "", objectId = "", dimCode } = items[0] || {};
      this.selectedMapCache[objectId] = dimMemberRealName; // 缓存选中key
      this.formValueCache[dimCode.toLowerCase()] = values; // 缓存选中value
    },
    search() {
      this.selectedMap = { ...this.selectedMapCache };
      this.formValue = { ...this.formValueCache };
      this.setWatchParams(this.formValue);
      this.setWatchViewParams({
        year: this.selectedMap[this.formValue.year],
        version: this.selectedMap[this.formValue.version],
        period: this.selectedMap[this.formValue.period],
        scope: this.selectedMap[this.formValue.scope]
      });
      this.popVisible = false;
      this.saveOrUpdateUserPov(this.formValue);
    },
    cancelEvent() {
      this.popVisible = false;
    },
    clickForm() {
      Array.from(document.querySelectorAll(".yn-select-menu")).forEach(dom => {
        dom.onclick = e => {
          e.stopPropagation();
          return false;
        };
      });
    },
    async getFirstYear() {
      if (this.defaultObj.year) return true;
      await commonService("getFirstDimMember", {
        dimCode: "Year",
        needPermission: false,
        needFilterAudittrail: false,
        needFilterShared: false
      }).then(res => {
        const { data } = res;
        this.selectedMapCache[data.objectId] = data.dimMemberRealName;
        this.formValueCache["year"] = data.objectId;
        this.$set(this.defaultObj, "year", data.objectId);
      });
    },
    async getFirstPeriod() {
      if (this.defaultObj.period) return true;
      await commonService("getFirstDimMember", {
        dimCode: "Period",
        needPermission: false,
        needFilterAudittrail: false,
        needFilterShared: false
      }).then(res => {
        const { data } = res;
        this.selectedMapCache[data.objectId] = data.dimMemberRealName;
        this.formValueCache["period"] = data.objectId;
        this.$set(this.defaultObj, "period", data.objectId);
      });
    },
    async getFirstVersion() {
      if (this.defaultObj.version) return true;
      await commonService("getFirstDimMember", {
        dimCode: "Version",
        needPermission: false,
        needFilterAudittrail: false,
        needFilterShared: false
      }).then(res => {
        const { data } = res;
        this.selectedMapCache[data.objectId] = data.dimMemberRealName;
        this.formValueCache["version"] = data.objectId;
        this.$set(this.defaultObj, "version", data.objectId);
      });
    }
  }
};
</script>

<style lang="less" scoped>
.dim {
  .bread {
    color: @yn-link-color;
    font-weight: 400;
    font-size: 0.875rem;
    text-align: left;
    line-height: 1.375rem;
    cursor: pointer;
  }
  .open-tilter {
    margin-left: 0.5rem;
    color: @yn-disabled-color;
    /deep/ .svg-icon {
      font-size: 0.8rem;
    }
  }
}
</style>

<style lang="less">
.ant-popover.my-popconfirm {
  width: 15.125rem;
  .ant-popover-buttons {
    border-top: 1px solid @yn-border-color-base;
    padding: 0.5rem;
  }
  .ant-popover-message-title {
    padding: 1rem 1rem 0;
  }
  .ant-popover-inner-content {
    padding: 0;
  }
  .ant-popover-message {
    font-weight: 400;
    padding-bottom: 0;
    font-size: 0.875rem;
  }
  .filter-form {
  }
  .ant-form-item {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
  }
}
</style>
