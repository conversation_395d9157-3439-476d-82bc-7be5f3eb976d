<template>
  <div class="process-watch-arch">
    <div class="arch-title">
      {{ $t_process("process_monitor_chart") }}
      <yn-tooltip placement="top">
        <template slot="title">
          <span>{{ $t_process("current_level_submission") }}</span>
        </template>
        <svg-icon :isIconBtn="false" type="hint" class="hint" />
      </yn-tooltip>
    </div>
    <yn-spin :spinning="loading" :class="['spinning', { scroll: overflow }]">
      <yn-empty v-if="data.length === 0" class="arch-empty" />
      <svg
        v-else
        version="1.1"
        baseProfile="full"
        :style="{ marginTop: groupRect.top + 'px' }"
        class="arch"
        width="100%"
        :height="height"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <marker
            id="arrow"
            viewBox="0 0 10 12"
            refX="6"
            refY="6"
            markerWidth="6"
            markerHeight="6"
            orient="auto-start-reverse"
          >
            <path d="M 0 0 L 10 6 L 0 12" class="arrow" />
          </marker>
          <linearGradient id="line" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" class="start" />
            <stop offset="100%" class="end" />
          </linearGradient>
        </defs>
        <g
          v-for="(subGroup, index) in group"
          :key="groupInfo[index].key"
          class="grow"
          @click="handlerClick($event, groupInfo[index].level)"
        >
          <rect
            v-if="summaryGroup[groupInfo[index].level]"
            :class="[
              'group',
              {
                normal:
                  parseFloat(
                    summaryGroup[groupInfo[index].level].levelSubmitRate
                  ) !== 100 && groupInfo[index].level !== selected,
                done:
                  parseFloat(
                    summaryGroup[groupInfo[index].level].levelSubmitRate
                  ) === 100 && groupInfo[index].level !== selected
              }
            ]"
            :fill="groupInfo[index].level === selected ? 'url(#line)' : ''"
            :x="0"
            rx="4"
            ry="4"
            :width="groupRect.width"
            :height="groupRect.height"
            :y="(groupRect.height + groupRect.mt) * index"
          />
          <g v-for="item in subGroup" :key="item.id">
            <rect
              class="rect"
              :y="item.y"
              :x="item.x"
              rx="4"
              ry="4"
              :height="item.height"
              :width="item.width"
            />
            <foreignObject
              :x="item.x"
              :y="item.y"
              :height="item.height"
              :width="item.width"
            >
              <div
                :class="['text', { more: item.type === 'more' }]"
                :title="item.memberName"
              >
                <span>{{ getText(item.memberName) }}</span>
              </div>
            </foreignObject>
          </g>
          <g>
            <foreignObject
              :x="summary.x"
              :y="summary.top + (groupRect.height + groupRect.mt) * index"
              :height="summary.height"
              :width="summary.width"
            >
              <div v-if="summaryGroup[groupInfo[index].level]" class="summary">
                <div class="process">
                  <span class="total">
                    {{
                      $t_common("company_home", [
                        summaryGroup[groupInfo[index].level].levelTotalNum
                      ])
                    }}
                  </span>
                  <span class="level">
                    ({{
                      $t_process("company_level", [
                        summaryGroup[groupInfo[index].level].level + 1
                      ])
                    }})
                  </span>
                </div>
                <div class="process">
                  {{ $t_process("progress") }}
                  {{ summaryGroup[groupInfo[index].level].levelSubmitRate }}
                </div>
                <yn-progress
                  :showInfo="false"
                  :class="[
                    'process-bar',
                    {
                      scope: index < group.length - 1,
                      success:
                        parseInt(
                          summaryGroup[groupInfo[index].level].levelSubmitRate
                        ) === 100
                    }
                  ]"
                  type="line"
                  :percent="
                    parseFloat(
                      summaryGroup[groupInfo[index].level].levelSubmitRate
                    )
                  "
                />
              </div>
            </foreignObject>
          </g>
        </g>
        <g>
          <path
            v-for="(d, index) in links"
            :key="index"
            :d="d"
            marker-end="url(#arrow)"
            class="path"
          />
        </g>
      </svg>
    </yn-spin>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-progress/";
import "yn-p1/libs/components/yn-tooltip/";
import { polling } from "@/utils/common";
import precessService from "@/services/process";
import { mapState, mapMutations } from "vuex";
import _debounce from "lodash/debounce";
function pxToRem(px) {
  return (px / 16) * parseInt(document.documentElement.style.fontSize);
}
export default {
  data() {
    return {
      loading: false,
      height: "100%",
      summary: {},
      selected: -1,
      overflow: false,
      data: [],
      group: [],
      links: [],
      clipLength: 1000,
      groupRect: {},
      groupInfo: {},
      summaryGroup: [],
      summaryData: []
    };
  },
  computed: {
    ...mapState("processStore", {
      watchParams: state => state.watchParams,
      scopeInfo: state => state.scopeInfo
    }),
    params() {
      return {
        ...this.watchParams,
        ...this.scopeInfo
      };
    }
  },
  watch: {
    scopeInfo: {
      async handler() {
        await this.getMonitorDiagram();
        this.setSort();
        this.render();
      }
    }
  },
  mounted() {
    this.$resizeObserver = new ResizeObserver(() => {
      if (this.$el.offsetHeight === 0 || this.$el.offsetWidth === 0) {
        return;
      }
      this.render();
    });
    this.$resizeObserver.observe(this.$el);
  },
  methods: {
    ...mapMutations("processStore", ["setSortLoading", "setArchInfo"]),
    async getMonitorDiagram() {
      this.loading = true;
      this.setSortLoading(true);
      if (!([...new Set(Object.values(this.params))].length < 5)) {
        const {
          data: { data }
        } = await precessService("getMonitorDiagram", this.params).catch(e => {
          this.data = [];
          this.summaryData = [];
          this.loading = false;
          this.setSortLoading(false);
        });
        this.data = data ? data.processDiagramItems || [] : [];
        this.summaryData = data ? data.processDiagramLevels || [] : [];
      } else {
        this.data = [];
        this.summaryData = [];
        this.loading = false;
        this.setSortLoading(false);
      }
    },
    // 配置预设的UI宽度，rem 自适应
    configRem() {
      // 值均为 1920 尺寸下的基础值
      const groupHeight = pxToRem(84); // 分组高度
      const marginHeight = pxToRem(8); // 多边形底部间隔高度
      const paddingHeight = pxToRem(12); // 分组上下内边距
      const rectHeight = pxToRem(60); // 多边形高度
      const moreWidth = pxToRem(32); // 更多多边形宽度
      const summaryWidth = pxToRem(130); // 统计信息总宽度
      const fontSize = pxToRem(14); // 字体大小
      const groupPadding = pxToRem(14); // 多边形内边距
      const rate = 112 / (794 - 130); // 多边形占比
      const moreRate = 32 / (794 - 130); // 更多多边形占比
      const gap = pxToRem(10); // 多边形间隔
      const summaryMargin = pxToRem(26); // 统计信息开始x位置,左侧离分组的距离
      const summaryTop = pxToRem(19); // 统计信息顶部距离
      const summarySelfWidth = pxToRem(86); // 统计信息宽度
      const summaryHeight = pxToRem(50); // // 统计信息高度
      this.config = {
        gap,
        rate,
        moreRate,
        groupHeight,
        marginHeight,
        paddingHeight,
        fontSize,
        rectHeight,
        moreWidth,
        summaryWidth,
        groupPadding,
        summarySelfWidth,
        summaryHeight,
        summaryTop,
        summaryMargin
      };
    },
    render: _debounce(function() {
      this.loading = true;
      this.overflow = false;
      this.configRem();
      if (this.data && this.data.length > 0) {
        const { group, height, links, groupInfo } = this.formatData(this.data);
        polling(
          () => Promise.resolve(this.$el.querySelector(".arch")),
          100
        ).then(query => {
          this.height = height;
          this.groupInfo = groupInfo;
          this.groupRect = this.setGroupInfo(query, height);
          this.setMemberX(group);
          this.links = this.setLinks(links);
          this.summary = this.setSummary();
          this.clipLength = this.clip();
          this.group = group;
          this.$nextTick(() => {
            this.scrollToView();
          });
          this.loading = false;
        });
      } else {
        this.loading = false;
      }
    }, 100),
    handlerClick(e, index) {
      this.selected = index;
      this.setArchInfo({ level: index });
    },
    setSort() {
      const { lastUnDone, summaryGroup } = this.formatSummary(this.summaryData);
      this.selected = lastUnDone;
      this.summaryGroup = summaryGroup;
      this.setArchInfo({ level: lastUnDone });
    },
    formatSummary(data) {
      const summaryGroup = [];
      let lastUnDone = 0; // 最后一个未完成的分组
      data.forEach(item => {
        summaryGroup[item.level] = item;
        parseFloat(item.levelSubmitRate) !== 100 &&
          (lastUnDone = Math.max(item.level, lastUnDone));
      });
      return { lastUnDone, summaryGroup };
    },
    /**
     * 构造层级结构
     * */
    formatData(data) {
      const group = [];
      const groupInfo = {};
      let height = 0;
      const {
        groupHeight,
        marginHeight,
        paddingHeight,
        rectHeight
      } = this.config;
      const links = [];
      const loop = (data, index = 0, parent = null) => {
        if (!data || data.length === 0) return;
        // svg总高度动态计算
        height = Math.max(
          height,
          (index + 1) * (groupHeight + marginHeight) - marginHeight
        );
        group[index] || (group[index] = []);
        groupInfo[index] = {
          key: Math.random(),
          level: data[0].memberRelationLevel
        };
        for (let i = 0, len = data.length; i < len; i++) {
          const item = data[i];
          if (item.hasAuth) {
            // 子项有权限
            const rect = {
              type: "rect",
              id: item.objectId + Math.random(),
              width: 0,
              height: rectHeight,
              hasMore: item.hasMore,
              y: (groupHeight + marginHeight) * index + paddingHeight,
              ...item
            };
            // 四边形
            group[index].push(rect);
            // 连线
            links.push({
              start: parent,
              end: rect
            });
            loop(item.children, index + 1, rect);
            // 当子项中不存在无权限的子项，且子项有更多时，构造空四边形和连线
            if (!rect.noAuthToMore && rect.hasMore) {
              const empty = {
                type: "more",
                id: item.objectId + "empty" + Math.random(),
                width: 0,
                height: rectHeight,
                y: (groupHeight + marginHeight) * (index + 1) + paddingHeight,
                memberName: "..."
              };
              group[index + 1].push(empty);
              links.push({
                start: rect,
                end: empty
              });
            }
          } else {
            // 子项无权限，将无权限四边形转化为空四边形，
            // 但保持位置关系和连线关系，同一父项的子项多个无权限四边形只存在一个
            if (parent && !parent.noAuthToMore) {
              parent.noAuthToMore = {
                type: "more",
                id: item.objectId + "empty" + Math.random(),
                width: 0,
                height: rectHeight,
                y: (groupHeight + marginHeight) * index + paddingHeight,
                memberName: "..."
              };
            }
            loop(item.children, index + 1, parent ? parent.noAuthToMore : null);
          }
          // 最后一个子项时，同时将转化的空多边形加入末尾，noAuthToMore 记录当前节点的子项是否存在无权限子项，并保持引用
          if (i === len - 1) {
            if (parent && parent.noAuthToMore) {
              group[index].push(parent.noAuthToMore);
              links.push({
                start: parent,
                end: parent.noAuthToMore
              });
            }
          }
        }
      };
      loop(data);

      return {
        group, // 穿透图数据分组
        links, // 穿透图连线
        height: height, // svg总高度
        groupInfo // 穿透图数据分组唯一key, 用来vue强制刷新
      };
    },
    // 设置分组信息
    setGroupInfo(query, height) {
      const width = query.getBoundingClientRect().width;
      const pHeight = query.parentNode.getBoundingClientRect().height;
      const groupWidth = width - this.config.summaryWidth;
      this.overflow = pHeight < height;
      const top = pHeight > height ? (pHeight - height) / 2 : 0;
      return {
        width: groupWidth, // 分组物理宽度， 不包括统计宽度summaryWidth
        top, // 高度不足，居中svg，设置的margin-top
        height: this.config.groupHeight, // 分组物理高度，
        mt: this.config.marginHeight // 分组底部margin-bottom物理高度，
      };
    },
    // 设置连线坐标
    setLinks(links) {
      const a = links
        .map(item => {
          const start = item.start;
          if (!start) return null;
          const end = item.end;
          const sw = start.width;
          const sh = start.height;
          const sx = start.x;
          const sy = start.y;
          const ew = end.width;
          // const eh = end.height;
          const ex = end.x;
          const ey = end.y;
          const dre = sx + sw / 2 === ex + ew / 2;
          const cy = (sy + sh + ey) / 2;
          const fix = dre ? 0 : 6;
          const sign = ex + ew / 2 - (sx + sw / 2) > 0 ? 1 : -1;
          return `M${sw / 2 + sx} ${sy + sh} L${sw / 2 + sx} ${cy - fix}
            Q${sw / 2 + sx} ${cy} ${sign * fix + sw / 2 + sx} ${cy}
            L${ex + ew / 2 - sign * fix} ${cy} Q${ex + ew / 2} ${cy} ${ex +
            ew / 2} ${cy + fix} L${ex + ew / 2} ${ey - 2}`;
        })
        .filter(item => item);
      return a;
    },
    // 设置统计信息位置信息
    setSummary() {
      const groupWidth = this.groupRect.width;
      const {
        summarySelfWidth,
        summaryHeight,
        summaryTop,
        summaryMargin
      } = this.config;
      return {
        x: groupWidth + summaryMargin, // 统计信息开始x位置,左侧离分组的距离
        top: summaryTop, // 统计信息顶部距离
        width: summarySelfWidth, // 统计信息宽度
        height: summaryHeight // // 统计信息高度
      };
    },
    // 设置x坐标
    setMemberX(group) {
      const { gap, rate, moreRate } = this.config;
      const groupWidth = this.groupRect.width;
      for (const subGroup of group) {
        const len = subGroup.length;
        let sum = 0;
        for (let index = 0; index < len; index++) {
          const realRate = subGroup[index].type === "more" ? moreRate : rate;
          subGroup[index].width =
            subGroup[index].width || realRate * groupWidth;
          sum += subGroup[index].width;
        }
        let left = (groupWidth - sum - gap * (len - 1)) / 2;
        for (let index = 0; index < len; index++) {
          subGroup[index].x = left;
          left += subGroup[index].width + gap;
        }
      }
    },
    // 设置截取长度
    clip() {
      const groupWidth = this.groupRect.width * this.config.rate;
      const length =
        ((groupWidth - this.config.groupPadding) / this.config.fontSize) * 2 -
        2;
      return length;
    },
    getText(text) {
      const len = text.length;
      if (len < this.clipLength) return text;
      const str = text.substr(0, this.clipLength);
      const single = str.match(/\w/gi); // 英文和数字
      const fix = single
        ? this.clipLength + single.length / 2
        : this.clipLength;
      return text.substr(0, fix) + "...";
    },
    scrollToView() {
      const dis =
        (this.selected + 1) *
        (this.config.groupHeight + this.config.marginHeight);
      const dom = this.$el.querySelector(".arch").parentNode;
      dom.scrollTo(0, dis - dom.offsetHeight);
    }
  }
};
</script>

<style lang="less" scoped>
.process-watch-arch {
  padding: 1rem 1rem 0.5rem;
  border-radius: 4px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  .arch-empty {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .arch {
  }
  .arch-title {
    font-size: 1rem;
    height: 1.5rem;
    margin-bottom: 0.5rem;
    color: @yn-text-color;
    flex-shrink: 0;
    .hint {
      color: @yn-label-color;
    }
  }
  .spinning {
    position: relative !important;
    height: 0 !important;
    flex: 1;
    /deep/ .ant-spin-container {
      display: flex;
      justify-content: center;
    }
  }
  .grow {
    cursor: pointer;
  }
  .group {
    width: 100%;
    &.done {
      fill: rgba(19, 119, 235, 0.06);
    }
    &.normal {
      fill: rgba(0, 0, 0, 0.03);
    }
  }
  .start {
    stop-color: @yn-primary-color;
    stop-opacity: 1;
  }

  .end {
    stop-color: @yn-primary-color;
    stop-opacity: 0;
  }
  .rect {
    fill: #fff;
  }
  .text {
    color: @yn-text-color;
    text-align: center;
    height: 100%;
    width: 100%;
    display: table;
    overflow: hidden;
    border-radius: 4px;
    &.more {
      span {
        padding: 0;
      }
    }
    span {
      display: table-cell;
      vertical-align: middle;
      text-align: center;
      font-size: 0.875rem;
      padding: 0.625rem 0.4375rem;
      height: 100%;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .path {
    stroke: @yn-disabled-color;
    stroke-width: 1px;
    fill: none;
  }
  .arrow {
    stroke: @yn-disabled-color;
    stroke-width: 2px;
    fill: none;
  }
  .summary {
    height: 100%;
    position: relative;
    .process {
      white-space: nowrap;
    }
    .total {
      font-size: 0.875rem;
      font-weight: 500;
      height: 1.375rem;
      color: @yn-text-color-secondary;
      margin-bottom: 0.1875rem;
    }
    .level {
      font-size: 0.75rem;
      font-weight: 500;
      height: 1rem;
      color: @yn-label-color;
    }
    .process {
      font-size: 0.75rem;
      color: @yn-label-color;
    }
    .process-bar {
      position: absolute;
      left: 0;
      bottom: -0.3125rem;
    }
  }
  .scroll /deep/ .ant-spin-container {
    overflow-y: auto;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      width: 5px;
    }
  }
  /deep/ .ant-spin-container {
    overflow-y: hidden;
    overflow-x: hidden;
  }
  /deep/ .process-bar.scope .ant-progress-bg {
    background-color: @yn-chart-4;
  }
  /deep/ .process-bar.success .ant-progress-bg {
    background-color: @yn-chart-2 !important;
  }
  /deep/ .ant-progress-bg {
    height: 3px !important;
  }
  /deep/ .ant-spin-blur {
    .ant-spin-nested-loading .ant-spin-dot {
      display: none;
    }
    .grid-empty {
      display: none;
    }
  }
}
</style>
