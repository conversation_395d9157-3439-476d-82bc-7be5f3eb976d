import "./process.less";
export default function processDom({ status, process, prefix }) {
  const bg = {
    process: "ant-progress-bg",
    success: "ant-progress-success"
  };
  const dom = `
    <div style="box-sizing: border-box" class="consolidation-jdom ant-progress ant-progress-line ant-progress-status-active ant-progress-show-info ant-progress-small">
      <div class="jdom-process-wrap" style="box-sizing: border-box">
        <div style="box-sizing: border-box" class="ant-progress-outer">
          <div style="box-sizing: border-box" class="ant-progress-inner">
            <div class="${bg[status]}" style="width: ${process || 0};">
            </div>
          </div>
        </div style="box-sizing: border-box">
        <span title="${prefix + (process || 0)}" class="ant-progress-text">
          <span class="prefix">${prefix}</span>${process || 0}
        </span>
      </div>
    </div>
  `;
  const tmp = document.createElement("div");
  tmp.innerHTML = dom;
  return tmp.children[0];
}
