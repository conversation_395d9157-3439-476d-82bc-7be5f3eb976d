export default class Gauge {
  options = {
    name: "",
    color: "#1377EB",
    container: "",
    target: 0,
    total: 0,
    percent: 0
  };
  #origin = {
    name: "",
    color: "#1377EB",
    container: "",
    target: 0,
    total: 0,
    percent: 0
  };
  canvas = null;
  #ctx = null;
  center = { x: 0, y: 0 };
  radius = 0;
  arcWidth = 9.4;
  #dpr = devicePixelRatio;
  offsetArc = 40;
  #rateLayout = null;
  #bgLayout = null;
  #rateCtx = null;
  #bgCtx = null;
  #zero = false;
  distance = 2;
  maxLength = 3;
  minLength = 2;
  constructor(options) {
    this.options = { ...this.options, ...options };
    this._init();
    this.setting();
    this.render();
  }
  _init() {
    this.canvas = document.createElement("canvas");
    this.#rateLayout = document.createElement("canvas");
    this.#bgLayout = document.createElement("canvas");
    this.#ctx = this.canvas.getContext("2d");
    this.#rateCtx = this.#rateLayout.getContext("2d");
    this.#bgCtx = this.#bgLayout.getContext("2d");
    this.options.container.appendChild(this.canvas);
  }
  setting() {
    const width = this.options.container.offsetWidth;
    const height = this.options.container.offsetHeight;
    this.center = {
      x: width / 2,
      y: height / 2 + 5
    };
    this.radius = (Math.min(width, height) / 12) * 5 - this.arcWidth / 2;
    this.width = width;
    this.height = height;
    if (this.width === 0 || this.height === 0) {
      this.#zero = true;
    } else {
      this.#zero = false;
    }

    this.canvas.style.width = width + "px";
    this.canvas.style.height = height + "px";

    this.#rateLayout.style.width = width + "px";
    this.#rateLayout.style.height = height + "px";

    this.#bgLayout.style.width = width + "px";
    this.#bgLayout.style.height = height + "px";

    this.canvas.width = width * this.#dpr;
    this.canvas.height = height * this.#dpr;

    this.#rateLayout.width = width * this.#dpr;
    this.#rateLayout.height = height * this.#dpr;

    this.#bgLayout.width = width * this.#dpr;
    this.#bgLayout.height = height * this.#dpr;

    this.#ctx.scale(this.#dpr, this.#dpr);
    this.#rateCtx.scale(this.#dpr, this.#dpr);
    this.#bgCtx.scale(this.#dpr, this.#dpr);
  }
  render(options = {}) {
    this.#origin = { ...this.options };
    this.options = { ...this.options, ...options };
    this.clear();
    this.setting();
    this._renderBg();
    this._animate();
  }
  _easeOutCubic(x) {
    return 1 - Math.pow(1 - x, 3);
  }
  _animate() {
    if (this.#zero) {
      return;
    }
    const times = 60;
    const newValue = parseFloat(this.options.percent || 0);
    const originValue = parseFloat(this.#origin.percent || 0);
    const isLarge = originValue > newValue;
    const value = isLarge ? originValue : newValue;
    let current = isLarge ? times : 0;
    this.animateId && cancelAnimationFrame(this.animateId);
    const callback = () => {
      const rate = current / times;
      if (rate > 1 || rate < 0) {
        return;
      }
      let target = value * this._easeOutCubic(rate);
      if (isLarge && target <= newValue) {
        target = newValue;
      }
      if (!isLarge && target <= originValue) {
        target = originValue;
      }
      current = isLarge ? current - 1 : current + 1;
      this.#rateCtx.clearRect(
        0,
        0,
        this.width * this.#dpr,
        this.height * this.#dpr
      );
      this._renderRate(target);
      this._renderPointer(target);
      this.#ctx.clearRect(
        0,
        0,
        this.width * this.#dpr,
        this.height * this.#dpr
      );
      this._renderLayout();
      this.animateId = requestAnimationFrame(callback);
    };
    callback();
  }
  _renderNumber() {
    this.#bgCtx.save();
    this.#bgCtx.translate(this.center.x, this.center.y);
    this.#bgCtx.fillStyle = "#8894A8";
    this.#bgCtx.font = "bold 10px";
    const arc = (this.offsetArc / 180) * Math.PI;
    const x = -(this.radius + this.arcWidth + this.distance) * Math.cos(arc);
    const y = (this.radius + this.arcWidth + this.distance) * Math.sin(arc);

    const x2 = (this.radius + this.arcWidth + this.distance) * Math.cos(arc);
    const y2 = (this.radius + this.arcWidth + this.distance) * Math.sin(arc);

    this.#bgCtx.fillText(0, x + 8, y + 15);
    this.#bgCtx.fillText(100, x2 - 18, y2 + 15);
    // this.#bgCtx.setTransform(1, 0, 0, 1, 0, 0);
    this.#bgCtx.restore();
  }
  _renderLayout() {
    this.#ctx.drawImage(this.#bgLayout, 0, 0, this.width, this.height);
    this.#ctx.drawImage(this.#rateLayout, 0, 0, this.width, this.height);
  }
  _renderBg() {
    if (this.#zero) {
      return;
    }
    this._renderThumb();
    this._renderScaleBar();
    this._renderNumber();
    this._renderPointerBar();
    this._renderScale();
    this.#ctx.drawImage(this.#bgLayout, 0, 0, this.width, this.height);
  }
  _renderRate(percent = this.options.percent) {
    if (!percent || !parseFloat(percent)) {
      return;
    }
    this.#rateCtx.save();
    this.#rateCtx.strokeStyle = this.options.color;
    this.#rateCtx.lineCap = "round";
    this.#rateCtx.lineWidth = this.arcWidth;
    this.#rateCtx.translate(this.center.x, this.center.y);
    this.#rateCtx.rotate((Math.PI * 180) / 180);
    this.#rateCtx.beginPath();
    this.#rateCtx.arc(
      0,
      0,
      this.radius,
      (this.offsetArc / 180) * -Math.PI,
      ((((180 + this.offsetArc * 2) * parseFloat(percent)) / 100 -
        this.offsetArc) /
        180) *
        Math.PI
    );
    this.#rateCtx.stroke();
    // this.#rateCtx.setTransform(1, 0, 0, 1, 0, 0);
    this.#rateCtx.restore();
  }
  _renderThumb() {
    this.#bgCtx.save();
    this.#bgCtx.strokeStyle = "#F5F7FA";
    this.#bgCtx.lineCap = "round";
    this.#bgCtx.lineWidth = this.arcWidth;
    this.#bgCtx.translate(this.center.x, this.center.y);
    this.#bgCtx.rotate((Math.PI * 180) / 180);
    this.#bgCtx.beginPath();
    this.#bgCtx.arc(
      0,
      0,
      this.radius,
      (this.offsetArc / 180) * -Math.PI,
      ((180 + this.offsetArc) / 180) * Math.PI
    );
    this.#bgCtx.stroke();
    // this.#bgCtx.setTransform(1, 0, 0, 1, 0, 0);
    this.#bgCtx.restore();
  }
  _renderPointerBar() {
    this.#bgCtx.save();
    this.#bgCtx.strokeStyle = this.options.color;
    this.#bgCtx.lineCap = "round";
    this.#bgCtx.lineWidth = 4;
    this.#bgCtx.translate(this.center.x, this.center.y);
    this.#bgCtx.beginPath();
    this.#bgCtx.arc(0, 0, 4, 0, 2 * Math.PI);
    this.#bgCtx.stroke();
    // this.#bgCtx.setTransform(1, 0, 0, 1, 0, 0);
    this.#bgCtx.restore();
  }
  _renderPointer(percent = this.options.percent) {
    this.#rateCtx.save();
    this.#rateCtx.lineCap = "round";
    this.#rateCtx.strokeStyle = this.options.color;
    this.#rateCtx.lineCap = "round";
    this.#rateCtx.lineWidth = 4;
    this.#rateCtx.translate(this.center.x, this.center.y);
    this.#rateCtx.rotate(
      (((180 + this.offsetArc * 2) * parseFloat(percent)) / 100 / 180) * Math.PI
    );
    const arc = (this.offsetArc / 180) * Math.PI;
    const x = -4 * Math.cos(arc);
    const y = 4 * Math.sin(arc);
    const x1 = -this.radius * 0.7 * Math.cos(arc);
    const y2 = this.radius * 0.7 * Math.sin(arc);
    this.#rateCtx.beginPath();
    this.#rateCtx.moveTo(x, y);
    this.#rateCtx.lineTo(x1, y2);
    this.#rateCtx.stroke();
    // this.#rateCtx.setTransform(1, 0, 0, 1, 0, 0);
    this.#rateCtx.restore();
  }
  _renderScaleBar() {
    this.#bgCtx.save();
    this.#bgCtx.strokeStyle = "#E1E5EB";
    this.#bgCtx.lineCap = "round";
    this.#bgCtx.lineWidth = 1;
    this.#bgCtx.translate(this.center.x, this.center.y);
    this.#bgCtx.rotate((Math.PI * 180) / 180);
    this.#bgCtx.beginPath();
    this.#bgCtx.arc(
      0,
      0,
      this.radius + 9.4,
      (this.offsetArc / 180) * -Math.PI,
      ((180 + this.offsetArc) / 180) * Math.PI
    );
    this.#bgCtx.stroke();
    // this.#bgCtx.setTransform(1, 0, 0, 1, 0, 0);
    this.#bgCtx.restore();
  }
  _renderScale() {
    this.#bgCtx.save();
    this.#bgCtx.lineCap = "round";
    this.#bgCtx.lineWidth = 1;
    this.#bgCtx.translate(this.center.x, this.center.y);
    const num = 15 * 5; // 15组，每组5小格
    const unit = (180 + this.offsetArc * 2) / num;
    const arc = (this.offsetArc / 180) * Math.PI;
    const x = -(this.radius + this.arcWidth + this.distance) * Math.cos(arc);
    const y = (this.radius + this.arcWidth + this.distance) * Math.sin(arc);
    for (let index = 0; index <= num; index++) {
      const rotateAngle = (Math.PI / 180) * unit * index;
      const length = index % 5 === 0 ? this.maxLength : this.minLength;

      const x1 =
        -(this.radius + this.arcWidth + this.distance + length) * Math.cos(arc);
      const y2 =
        (this.radius + this.arcWidth + this.distance + length) * Math.sin(arc);
      this.#bgCtx.save();
      this.#bgCtx.strokeStyle = index % 5 === 0 ? "#8894A8" : "#E1E5EB";
      this.#bgCtx.rotate(rotateAngle);
      this.#bgCtx.beginPath();
      this.#bgCtx.moveTo(x, y);
      this.#bgCtx.lineTo(x1, y2);
      this.#bgCtx.stroke();
      this.#bgCtx.restore();
    }
    // this.#ctx.setTransform(1, 0, 0, 1, 0, 0);
    this.#ctx.restore();
  }
  clear() {
    this.#ctx.clearRect(0, 0, this.width * this.#dpr, this.height * this.#dpr);
    this.#rateCtx.clearRect(
      0,
      0,
      this.width * this.#dpr,
      this.height * this.#dpr
    );
  }
}
