<template>
  <div class="process-watch">
    <SearchFilter ref="Filter" class="filter" />
    <main class="contain">
      <Card class="card" />
      <div class="arch-sort">
        <Arch class="arch" />
        <Sort class="sort" />
      </div>
      <Table class="table" />
    </main>
  </div>
</template>

<script>
import Card from "./card.vue";
import Sort from "./sort.vue";
import Table from "./table.vue";
import Arch from "./arch.vue";
import SearchFilter from "./filter.vue";
import defaultParams from "@/mixin/defaultParams";
export default {
  components: { Card, SearchFilter, Table, Sort, Arch },
  mixins: [defaultParams],
  data() {
    return {};
  },
  methods: {}
};
</script>

<style lang="less" scoped>
.process-watch {
  display: flex;
  flex-direction: column;
  height: 100%;
  color: @yn-text-color;
  padding: 0 0.75rem 0;
  overflow-x: hidden;
  .filter {
    margin-top: 0 !important;
    margin-left: -0.75rem;
    margin-right: -0.75rem;
    flex-shrink: 0;
  }
  .contain {
    height: 0;
    flex: 1;
    margin-right: -0.625rem;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .card {
    height: 14.1875rem;
    margin-bottom: 0.75rem;
  }
  .arch-sort {
    display: flex;
    margin-bottom: 0.75rem;
    height: 26rem;
    .arch {
      flex-basis: 50%;
      height: 100%;
      flex-grow: 1;
      margin-right: 0.75rem;
    }
    .sort {
      flex-basis: 50%;
      height: 100%;
      flex-grow: 1;
    }
  }
}
@media (max-width: 1440px) {
  .card {
    height: 16rem !important;
  }
  /deep/ .card .gauge .percent {
    font-size: 0.625rem;
  }
  /deep/ .card .gauge .name {
    font-size: 0.625rem;
  }
}
</style>
