<template>
  <div class="gauge">
    <div class="info">
      <div class="percent">{{ animateRate }}</div>
      <div class="name">{{ name }}</div>
    </div>
  </div>
</template>

<script>
import Gauge from "./jdom/gauge";
function easeOutCubic(x) {
  return 1 - Math.pow(1 - x, 3);
}

export default {
  props: {
    rate: {
      type: [Number, String],
      default: "0%"
    },
    name: {
      type: String,
      default: ""
    },
    color: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      gauge: null,
      $resizeObserver: null,
      animateRate: this.rate
    };
  },
  watch: {
    rate(value, old) {
      if (value) {
        this.gauge &&
          this.gauge.render({
            color: this.color,
            percent: this.rate
          });
        this.animate(value, old);
      }
    }
  },
  mounted() {
    this.$resizeObserver = new ResizeObserver(() => {
      if (this.$el.offsetHeight === 0 || this.$el.offsetWidth === 0) {
        return;
      }
      this.gauge.render();
    });
    this.$resizeObserver.observe(this.$el);
    this.gauge = new Gauge({
      color: this.color,
      container: this.$el,
      percent: this.rate
    });
  },
  methods: {
    animate(rete, old) {
      const times = 60;
      const newValue = parseFloat(rete || 0);
      const originValue = parseFloat(old || 0);
      const isLarge = originValue > newValue;
      const value = isLarge ? originValue : newValue;
      let current = isLarge ? times : 0;
      this.animateId && cancelAnimationFrame(this.animateId);
      const callback = () => {
        const rate = current / times;
        if (rate > 1 || rate < 0) {
          return;
        }
        const target = value * easeOutCubic(rate);
        if (isLarge && target <= newValue) {
          this.animateRate = rete;
          return;
        }
        current = isLarge ? current - 1 : current + 1;
        this.animateRate = target.toFixed(1) + "%";
        this.animateId = requestAnimationFrame(callback);
      };
      callback();
    }
  }
};
</script>

<style lang="less" scoped>
.gauge {
  width: 0;
  height: 100%;
  flex: 1;
  display: flex;
  justify-content: center;
  position: relative;
  overflow: hidden;
  .info {
    position: absolute;
    bottom: 0.3125rem;
    text-align: center;
  }
  .percent {
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    letter-spacing: 0px;
    color: @yn-text-color;
  }
  .name {
    font-size: 0.75rem;
    letter-spacing: 0px;
    color: @yn-disabled-color;
  }
}
</style>
