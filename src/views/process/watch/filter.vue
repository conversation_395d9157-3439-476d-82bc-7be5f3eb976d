<template>
  <div class="search-filter">
    <section class="filter">
      <div>
        <span class="custitle">{{ $t_process("process_monitor") }}</span>
      </div>
      <yn-divider type="vertical" class="divider" />
      <Dim :visible.sync="dimVisible" />
      <yn-divider type="vertical" class="filter-divider" />
      <Scope :visible.sync="scopeVisible" />
      <yn-divider type="vertical" class="filter-divider" />
      <svg-icon type="icon-shuaxin" class="refresh" @click.native="refresh" />
    </section>
    <aside class="action">
      <Setting />
    </aside>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-divider/";
import Setting from "./setting.vue";
import Scope from "./scope.vue";
import Dim from "./dim.vue";
import { mapMutations } from "vuex";

export default {
  components: { Setting, <PERSON><PERSON>, Dim },
  data() {
    return {
      dimVisible: false,
      scopeVisible: false
    };
  },
  watch: {
    dimVisible(value) {
      if (value) {
        this.scopeVisible = false;
      }
    },
    scopeVisible(value) {
      if (value) {
        this.dimVisible = false;
      }
    }
  },
  methods: {
    ...mapMutations("processStore", ["setRefresh"]),
    refresh() {
      this.setRefresh(Date.now());
    }
  }
};
</script>

<style lang="less" scoped>
.search-filter {
  position: relative;
  display: flex;
  align-items: center;
  margin-top: 1.25rem;
  height: 3.25rem;
  padding: 1rem 0.75rem 1.125rem;
  .filter {
    display: flex;
    align-items: center;
    height: 1.5rem;
    .divider {
      height: 1.3rem;
      margin-right: 1rem;
      margin-left: 1rem;
      top: 0;
    }
  }
  .refresh {
    color: @yn-label-color;
    font-size: 0.875rem;
    &:hover {
      color: @yn-primary-color;
    }
    /deep/ .iconfont {
      font-size: 0.875rem;
    }
    /deep/ .svg-icon {
      width: 0.875rem;
      height: 0.875rem;
      font-size: 0.875rem;
    }
  }
  .action {
    height: 2.25rem;
    line-height: 2.25rem;
    margin-left: auto;
  }
  .filter-divider {
    height: 1rem;
    margin: 0 1rem;
  }
}

.custitle {
  display: inline-block;
  max-width: 24em;
  line-height: 1.5rem;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
  font-size: 1rem;
  font-weight: 600;
  color: @yn-text-color;
}
/deep/ .mydv {
  height: 1rem;
  margin-right: 0.75rem;
  margin-left: 0.25rem;
  top: 0;
}
</style>

<style lang="less">
.ant-popover.my-popconfirm {
  width: 15.125rem;
  .ant-popover-buttons {
    border-top: 1px solid @yn-border-color-base;
    padding: 0.5rem;
  }
  .ant-popover-message-title {
    padding: 1rem 1rem 0;
  }
  .ant-popover-inner-content {
    padding: 0;
  }
  .ant-popover-message {
    font-weight: 400;
    padding-bottom: 0;
    font-size: 0.875rem;
  }
  .filter-form {
  }
  .ant-form-item {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
  }
}
</style>
