export const tree = {
  data: [
    {
      childCount: 3,
      children: [
        {
          childCount: 0,
          children: null,
          dbCodeIndex: "",
          dimCode: "",
          entityReady: "",
          entityReconciliation: "",
          entitySubmit: "",
          hasAuth: true,
          id: "11edd5201ea73ed6a99e6306ca40f137",
          memberDbCode: "",
          memberId: "11edb298fad5ec8f99c2879632b434f9",
          memberName: "欢乐集团欧洲区（合并）",
          memberRelationLevel: 1,
          memberRelationParentId: "11edd51ffe446176a99e3db16b7e336a",
          memberRelationPos: 2,
          memberRelationTotalCode: "Happy-Global-C,Happy-Euro",
          name: "",
          nodeId: "",
          objectId: "11edd5201ea73ed6a99e6306ca40f137",
          parentId: "11edd51ffe446176a99e3db16b7e336a",
          position: 0,
          processId: "",
          scopeReady: "",
          scopeSubmit: ""
        },
        {
          childCount: 2,
          children: [
            {
              childCount: 1,
              children: [
                {
                  childCount: 1,
                  children: [
                    {
                      childCount: 0,
                      children: null,
                      dbCodeIndex: "",
                      dimCode: "",
                      entityReady: "",
                      entityReconciliation: "",
                      entitySubmit: "",
                      hasAuth: true,
                      id: "11edd52093ced9efa99e15dc702e1478",
                      memberDbCode: "",
                      memberId: "11edc9f16cd8767f96275fb029b7b1c6",
                      memberName: "欢乐集团中国直辖市（合并）",
                      memberRelationLevel: 4,
                      memberRelationParentId:
                        "11edd52072f286eba99e274fe801adfd",
                      memberRelationPos: 3,
                      memberRelationTotalCode:
                        "Happy-Global-C,Happy-Asian,Happy-China,Happy-China-Special-Cities-C,Happy-China-ZXS-C",
                      name: "",
                      nodeId: "",
                      objectId: "11edd52093ced9efa99e15dc702e1478",
                      parentId: "11edd52072f286eba99e274fe801adfd",
                      position: 0,
                      processId: "",
                      scopeReady: "",
                      scopeSubmit: ""
                    }
                  ],
                  dbCodeIndex: "",
                  dimCode: "",
                  entityReady: "",
                  entityReconciliation: "",
                  entitySubmit: "",
                  hasAuth: true,
                  id: "11edd52072f286eba99e274fe801adfd",
                  memberDbCode: "",
                  memberId: "11edc9f1521ee1209627fd2d8150e10d",
                  memberName: "欢乐集团中国特级区（合并）",
                  memberRelationLevel: 3,
                  memberRelationParentId: "11edd5204e689612a99ef3eb3b5edc67",
                  memberRelationPos: 6,
                  memberRelationTotalCode:
                    "Happy-Global-C,Happy-Asian,Happy-China,Happy-China-Special-Cities-C",
                  name: "",
                  nodeId: "",
                  objectId: "11edd52072f286eba99e274fe801adfd",
                  parentId: "11edd5204e689612a99ef3eb3b5edc67",
                  position: 0,
                  processId: "",
                  scopeReady: "",
                  scopeSubmit: ""
                }
              ],
              dbCodeIndex: "",
              dimCode: "",
              entityReady: "",
              entityReconciliation: "",
              entitySubmit: "",
              hasAuth: true,
              id: "11edd5204e689612a99ef3eb3b5edc67",
              memberDbCode: "",
              memberId: "11edb29859c885cf99c24f96825fd777",
              memberName: "欢乐集团中国区（合并）",
              memberRelationLevel: 2,
              memberRelationParentId: "11edd5201ea73ed7a99e2571a81a99bd",
              memberRelationPos: 2,
              memberRelationTotalCode: "Happy-Global-C,Happy-Asian,Happy-China",
              name: "",
              nodeId: "",
              objectId: "11edd5204e689612a99ef3eb3b5edc67",
              parentId: "11edd5201ea73ed7a99e2571a81a99bd",
              position: 0,
              processId: "",
              scopeReady: "",
              scopeSubmit: ""
            },
            {
              childCount: 0,
              children: null,
              dbCodeIndex: "",
              dimCode: "",
              entityReady: "",
              entityReconciliation: "",
              entitySubmit: "",
              hasAuth: true,
              id: "11edd5204e689613a99e7532de2bf054",
              memberDbCode: "",
              memberId: "11edb2991ed613b099c207cf506a871a",
              memberName: "欢乐集团日本区（合并）",
              memberRelationLevel: 2,
              memberRelationParentId: "11edd5201ea73ed7a99e2571a81a99bd",
              memberRelationPos: 3,
              memberRelationTotalCode: "Happy-Global-C,Happy-Asian,Happy-Japan",
              name: "",
              nodeId: "",
              objectId: "11edd5204e689613a99e7532de2bf054",
              parentId: "11edd5201ea73ed7a99e2571a81a99bd",
              position: 0,
              processId: "",
              scopeReady: "",
              scopeSubmit: ""
            }
          ],
          dbCodeIndex: "",
          dimCode: "",
          entityReady: "",
          entityReconciliation: "",
          entitySubmit: "",
          hasAuth: true,
          id: "11edd5201ea73ed7a99e2571a81a99bd",
          memberDbCode: "",
          memberId: "11edb2990af62fa099c2c110cae2d78e",
          memberName: "欢乐集团亚洲区（合并）",
          memberRelationLevel: 1,
          memberRelationParentId: "11edd51ffe446176a99e3db16b7e336a",
          memberRelationPos: 3,
          memberRelationTotalCode: "Happy-Global-C,Happy-Asian",
          name: "",
          nodeId: "",
          objectId: "11edd5201ea73ed7a99e2571a81a99bd",
          parentId: "11edd51ffe446176a99e3db16b7e336a",
          position: 0,
          processId: "",
          scopeReady: "",
          scopeSubmit: ""
        },
        {
          childCount: 0,
          children: null,
          dbCodeIndex: "",
          dimCode: "",
          entityReady: "",
          entityReconciliation: "",
          entitySubmit: "",
          hasAuth: true,
          id: "11edd5201ea73ed8a99e65a97ef46fc4",
          memberDbCode: "",
          memberId: "11edcecabb42dc039bdca7463c98cc0b",
          memberName: "欢乐集团北美区（合并）",
          memberRelationLevel: 1,
          memberRelationParentId: "11edd51ffe446176a99e3db16b7e336a",
          memberRelationPos: 4,
          memberRelationTotalCode: "Happy-Global-C,Happy-North-America",
          name: "",
          nodeId: "",
          objectId: "11edd5201ea73ed8a99e65a97ef46fc4",
          parentId: "11edd51ffe446176a99e3db16b7e336a",
          position: 0,
          processId: "",
          scopeReady: "",
          scopeSubmit: ""
        }
      ],
      dbCodeIndex: "",
      dimCode: "",
      entityReady: "",
      entityReconciliation: "",
      entitySubmit: "",
      hasAuth: true,
      id: "11edd51ffe446176a99e3db16b7e336a",
      memberDbCode: "",
      memberId: "11edd51fef4fa521a99eab53716d41fe",
      memberName: "欢乐集团全球股份有限公司（合并）",
      memberRelationLevel: 0,
      memberRelationParentId: "11edd51fef4fa521a99eab53716d41fe",
      memberRelationPos: 1,
      memberRelationTotalCode: "Happy-Global-C",
      name: "",
      nodeId: "",
      objectId: "11edd51ffe446176a99e3db16b7e336a",
      parentId: "11edd51fef4fa521a99eab53716d41fe",
      position: 0,
      processId: "",
      scopeReady: "",
      scopeSubmit: ""
    }
  ],
  message: "",
  messageList: null,
  messageType: null,
  success: true
};

export const card = {
  data: {
    processMonitorList: [
      {
        childCount: 2,
        children: [
          {
            childCount: 0,
            children: null,
            dbCodeIndex: "E0001",
            dimCode: "Entity",
            entityReady: "",
            entityReconciliation: "0.0%",
            entitySubmit: "0.0%",
            hasAuth: true,
            id: "11edd9ce68a50961964c9b720a401f40",
            memberDbCode: "E0001",
            memberId: "11edc242c0ba5408a9e339d89b183cc1",
            memberName: "源培集团股份有限公司",
            memberRelationLevel: 1,
            memberRelationParentId: "11edd9ce68a5095c964cc3a98f1816a1",
            memberRelationPos: 1,
            memberRelationTotalCode: "G0001,E0001",
            name: "",
            nodeId: "",
            nodeStatus: "",
            objectId: "11edd9ce68a50961964c9b720a401f40",
            parentId: "11edd9ce68a5095c964cc3a98f1816a1",
            position: 0,
            processId: "",
            scopeReady: "",
            scopeSubmit: ""
          },
          {
            childCount: 3,
            children: [
              {
                childCount: 0,
                children: null,
                dbCodeIndex: "E0002",
                dimCode: "Entity",
                entityReady: "",
                entityReconciliation: "0.0%",
                entitySubmit: "0.0%",
                hasAuth: true,
                id: "11edd9ce68a50963964c33380c2b5d67",
                memberDbCode: "E0002",
                memberId: "11edc242c0ba540aa9e35fdd0a5c2cca",
                memberName: "源华投资有限公司1",
                memberRelationLevel: 2,
                memberRelationParentId: "11edd9ce68a50962964cad24627a96e6",
                memberRelationPos: 1,
                memberRelationTotalCode: "G0001,G0002,E0002",
                name: "",
                nodeId: "11ecfccd03dd1bb9bff4d1063eda9af7",
                nodeStatus: "processing",
                objectId: "11edd9ce68a50963964c33380c2b5d67",
                parentId: "11edd9ce68a50962964cad24627a96e6",
                position: 0,
                processId: "11ede566d13b42a28b6493433ad7bddb",
                scopeReady: "",
                scopeSubmit: ""
              },
              {
                childCount: 0,
                children: null,
                dbCodeIndex: "E0003",
                dimCode: "Entity",
                entityReady: "",
                entityReconciliation: "0.0%",
                entitySubmit: "0.0%",
                hasAuth: true,
                id: "11edd9ce68a50960964c977198f78ee4",
                memberDbCode: "E0003",
                memberId: "11edc242c0ba540ba9e38361a4d29591",
                memberName: "源泰投资有限公司",
                memberRelationLevel: 2,
                memberRelationParentId: "11edd9ce68a50962964cad24627a96e6",
                memberRelationPos: 2,
                memberRelationTotalCode: "G0001,G0002,E0003",
                name: "",
                nodeId: "11edb650f6d0c8598d0d2dd1c54747e0",
                nodeStatus: "processing",
                objectId: "11edd9ce68a50960964c977198f78ee4",
                parentId: "11edd9ce68a50962964cad24627a96e6",
                position: 0,
                processId: "11ede566d13b42a38b646b17ff5c87e7",
                scopeReady: "",
                scopeSubmit: ""
              },
              {
                childCount: 2,
                children: [
                  {
                    childCount: 0,
                    children: null,
                    dbCodeIndex: "E0011",
                    dimCode: "Entity",
                    entityReady: "",
                    entityReconciliation: "0.0%",
                    entitySubmit: "0.0%",
                    hasAuth: true,
                    id: "11edd9ce68a5095e964cbd2f792a6bae",
                    memberDbCode: "E0011",
                    memberId: "11edc242c0ba7b24a9e303b393555b37",
                    memberName: "中润投资有限公司",
                    memberRelationLevel: 3,
                    memberRelationParentId: "11edd9ce68a5095d964ce55b004cf375",
                    memberRelationPos: 1,
                    memberRelationTotalCode: "G0001,G0002,G0003,E0011",
                    name: "",
                    nodeId: "11ecfccd03dd1bb9bff4d1063eda9af7",
                    nodeStatus: "processing",
                    objectId: "11edd9ce68a5095e964cbd2f792a6bae",
                    parentId: "11edd9ce68a5095d964ce55b004cf375",
                    position: 0,
                    processId: "11ede566d13b42a58b648d422106f943",
                    scopeReady: "",
                    scopeSubmit: ""
                  },
                  {
                    childCount: 0,
                    children: null,
                    dbCodeIndex: "E0012",
                    dimCode: "Entity",
                    entityReady: "",
                    entityReconciliation: "0.0%",
                    entitySubmit: "0.0%",
                    hasAuth: true,
                    id: "11edd9ce68a5095f964cd3de6ea648d2",
                    memberDbCode: "E0012",
                    memberId: "11edc242c0ba7b25a9e393d0234d8ee8",
                    memberName: "中源高新技术实业有限公司",
                    memberRelationLevel: 3,
                    memberRelationParentId: "11edd9ce68a5095d964ce55b004cf375",
                    memberRelationPos: 2,
                    memberRelationTotalCode: "G0001,G0002,G0003,E0012",
                    name: "",
                    nodeId: "11ecfccd03dd1bb9bff4d1063eda9af7",
                    nodeStatus: "processing",
                    objectId: "11edd9ce68a5095f964cd3de6ea648d2",
                    parentId: "11edd9ce68a5095d964ce55b004cf375",
                    position: 0,
                    processId: "11ede566d13b42a68b64b9929b0038ae",
                    scopeReady: "",
                    scopeSubmit: ""
                  }
                ],
                dbCodeIndex: "G0003",
                dimCode: "Scope",
                entityReady: "0/2",
                entityReconciliation: "0/2",
                entitySubmit: "0/2",
                hasAuth: true,
                id: "11edd9ce68a5095d964ce55b004cf375",
                memberDbCode: "G0003",
                memberId: "11ecf908858a7181b70895d092078cf2",
                memberName: "中润投资有限公司（合并）",
                memberRelationLevel: 2,
                memberRelationParentId: "11edd9ce68a50962964cad24627a96e6",
                memberRelationPos: 3,
                memberRelationTotalCode: "G0001,G0002,G0003",
                name: "",
                nodeId: "11ed0197646abf3ebabc67b0aa1f35fd",
                nodeStatus: "processing",
                objectId: "11edd9ce68a5095d964ce55b004cf375",
                parentId: "11edd9ce68a50962964cad24627a96e6",
                position: 0,
                processId: "11ede566d13b42a48b64b329ffe220a7",
                scopeReady: "33.3%",
                scopeSubmit: "0.0%"
              }
            ],
            dbCodeIndex: "G0002",
            dimCode: "Scope",
            entityReady: "0/4",
            entityReconciliation: "0/4",
            entitySubmit: "0/4",
            hasAuth: true,
            id: "11edd9ce68a50962964cad24627a96e6",
            memberDbCode: "G0002",
            memberId: "11ecf9080dda7da2b708fdc6ed9a17bb",
            memberName: "源华投资有限公司合并",
            memberRelationLevel: 1,
            memberRelationParentId: "11edd9ce68a5095c964cc3a98f1816a1",
            memberRelationPos: 2,
            memberRelationTotalCode: "G0001,G0002",
            name: "",
            nodeId: "11ed0197646abf3ebabc67b0aa1f35fd",
            nodeStatus: "processing",
            objectId: "11edd9ce68a50962964cad24627a96e6",
            parentId: "11edd9ce68a5095c964cc3a98f1816a1",
            position: 0,
            processId: "11ede566d13b42a18b64f5b5589540c0",
            scopeReady: "66.6%",
            scopeSubmit: "0.0%"
          }
        ],
        dbCodeIndex: "G0001",
        dimCode: "Scope",
        entityReady: "0/5",
        entityReconciliation: "0/5",
        entitySubmit: "0/5",
        hasAuth: true,
        id: "11edd9ce68a5095c964cc3a98f1816a1",
        memberDbCode: "G0001",
        memberId: "11ecf9080dda7da1b7086d163ce01a5f",
        memberName: "源培集团股份有限公司（合并）",
        memberRelationLevel: 0,
        memberRelationParentId: "11ecf9080dda7da1b7086d163ce01a5f",
        memberRelationPos: 1,
        memberRelationTotalCode: "G0001",
        name: "",
        nodeId: "11ed0197646abf3ebabc67b0aa1f35fd",
        nodeStatus: "processing",
        objectId: "11edd9ce68a5095c964cc3a98f1816a1",
        parentId: "11ecf9080dda7da1b7086d163ce01a5f",
        position: 0,
        processId: "11ede566d13b42a08b647576c508e75e",
        scopeReady: "66.6%",
        scopeSubmit: "0.0%"
      }
    ],
    processMonitorStatsList: [
      {
        compareYesterday: 0,
        finishNum: 0,
        finishRate: "0.0%",
        processMonitorCode: "entity_ready",
        totalNum: 5
      },
      {
        compareYesterday: 0,
        finishNum: 0,
        finishRate: "0.0%",
        processMonitorCode: "entity_reconciliation",
        totalNum: 5
      },
      {
        compareYesterday: 0,
        finishNum: 0,
        finishRate: "0.0%",
        processMonitorCode: "entity_submit",
        totalNum: 5
      },
      {
        compareYesterday: 0,
        finishNum: 0,
        finishRate: "0.0%",
        processMonitorCode: "scope_ready",
        totalNum: 3
      },
      {
        compareYesterday: 0,
        finishNum: 0,
        finishRate: "0.0%",
        processMonitorCode: "scope_submit",
        totalNum: 3
      }
    ]
  },
  message: "",
  messageList: null,
  messageType: null,
  success: true
};

export const table = {
  data: [
    {
      childCount: 3,
      children: [
        {
          childCount: 0,
          children: null,
          dbCodeIndex: "",
          dimCode: "",
          entityReady: "",
          entityReconciliation: "",
          entitySubmit: "",
          hasAuth: true,
          id: "11edd5201ea73ed6a99e6306ca40f137",
          memberDbCode: "",
          memberId: "11edb298fad5ec8f99c2879632b434f9",
          memberName: "欢乐集团欧洲区（合并）",
          memberRelationLevel: 1,
          memberRelationParentId: "11edd51ffe446176a99e3db16b7e336a",
          memberRelationPos: 2,
          memberRelationTotalCode: "Happy-Global-C,Happy-Euro",
          name: "",
          nodeId: "",
          objectId: "11edd5201ea73ed6a99e6306ca40f137",
          parentId: "11edd51ffe446176a99e3db16b7e336a",
          position: 0,
          processId: "",
          scopeReady: "",
          scopeSubmit: ""
        },
        {
          childCount: 2,
          children: [
            {
              childCount: 1,
              children: [
                {
                  childCount: 1,
                  children: [
                    {
                      childCount: 0,
                      children: null,
                      dbCodeIndex: "",
                      dimCode: "",
                      entityReady: "",
                      entityReconciliation: "",
                      entitySubmit: "",
                      hasAuth: true,
                      id: "11edd52093ced9efa99e15dc702e1478",
                      memberDbCode: "",
                      memberId: "11edc9f16cd8767f96275fb029b7b1c6",
                      memberName: "欢乐集团中国直辖市（合并）",
                      memberRelationLevel: 4,
                      memberRelationParentId:
                        "11edd52072f286eba99e274fe801adfd",
                      memberRelationPos: 3,
                      memberRelationTotalCode:
                        "Happy-Global-C,Happy-Asian,Happy-China,Happy-China-Special-Cities-C,Happy-China-ZXS-C",
                      name: "",
                      nodeId: "",
                      objectId: "11edd52093ced9efa99e15dc702e1478",
                      parentId: "11edd52072f286eba99e274fe801adfd",
                      position: 0,
                      processId: "",
                      scopeReady: "",
                      scopeSubmit: ""
                    }
                  ],
                  dbCodeIndex: "",
                  dimCode: "",
                  entityReady: "",
                  entityReconciliation: "",
                  entitySubmit: "",
                  hasAuth: true,
                  id: "11edd52072f286eba99e274fe801adfd",
                  memberDbCode: "",
                  memberId: "11edc9f1521ee1209627fd2d8150e10d",
                  memberName: "欢乐集团中国特级区（合并）",
                  memberRelationLevel: 3,
                  memberRelationParentId: "11edd5204e689612a99ef3eb3b5edc67",
                  memberRelationPos: 6,
                  memberRelationTotalCode:
                    "Happy-Global-C,Happy-Asian,Happy-China,Happy-China-Special-Cities-C",
                  name: "",
                  nodeId: "",
                  objectId: "11edd52072f286eba99e274fe801adfd",
                  parentId: "11edd5204e689612a99ef3eb3b5edc67",
                  position: 0,
                  processId: "",
                  scopeReady: "",
                  scopeSubmit: ""
                }
              ],
              dbCodeIndex: "",
              dimCode: "",
              entityReady: "",
              entityReconciliation: "",
              entitySubmit: "",
              hasAuth: true,
              id: "11edd5204e689612a99ef3eb3b5edc67",
              memberDbCode: "",
              memberId: "11edb29859c885cf99c24f96825fd777",
              memberName: "欢乐集团中国区（合并）",
              memberRelationLevel: 2,
              memberRelationParentId: "11edd5201ea73ed7a99e2571a81a99bd",
              memberRelationPos: 2,
              memberRelationTotalCode: "Happy-Global-C,Happy-Asian,Happy-China",
              name: "",
              nodeId: "",
              objectId: "11edd5204e689612a99ef3eb3b5edc67",
              parentId: "11edd5201ea73ed7a99e2571a81a99bd",
              position: 0,
              processId: "",
              scopeReady: "",
              scopeSubmit: ""
            },
            {
              childCount: 0,
              children: null,
              dbCodeIndex: "",
              dimCode: "",
              entityReady: "",
              entityReconciliation: "",
              entitySubmit: "",
              hasAuth: true,
              id: "11edd5204e689613a99e7532de2bf054",
              memberDbCode: "",
              memberId: "11edb2991ed613b099c207cf506a871a",
              memberName: "欢乐集团日本区（合并）",
              memberRelationLevel: 2,
              memberRelationParentId: "11edd5201ea73ed7a99e2571a81a99bd",
              memberRelationPos: 3,
              memberRelationTotalCode: "Happy-Global-C,Happy-Asian,Happy-Japan",
              name: "",
              nodeId: "",
              objectId: "11edd5204e689613a99e7532de2bf054",
              parentId: "11edd5201ea73ed7a99e2571a81a99bd",
              position: 0,
              processId: "",
              scopeReady: "",
              scopeSubmit: ""
            }
          ],
          dbCodeIndex: "",
          dimCode: "",
          entityReady: "",
          entityReconciliation: "",
          entitySubmit: "",
          hasAuth: true,
          id: "11edd5201ea73ed7a99e2571a81a99bd",
          memberDbCode: "",
          memberId: "11edb2990af62fa099c2c110cae2d78e",
          memberName: "欢乐集团亚洲区（合并）",
          memberRelationLevel: 1,
          memberRelationParentId: "11edd51ffe446176a99e3db16b7e336a",
          memberRelationPos: 3,
          memberRelationTotalCode: "Happy-Global-C,Happy-Asian",
          name: "",
          nodeId: "",
          objectId: "11edd5201ea73ed7a99e2571a81a99bd",
          parentId: "11edd51ffe446176a99e3db16b7e336a",
          position: 0,
          processId: "",
          scopeReady: "",
          scopeSubmit: ""
        },
        {
          childCount: 0,
          children: null,
          dbCodeIndex: "",
          dimCode: "",
          entityReady: "",
          entityReconciliation: "",
          entitySubmit: "",
          hasAuth: true,
          id: "11edd5201ea73ed8a99e65a97ef46fc4",
          memberDbCode: "",
          memberId: "11edcecabb42dc039bdca7463c98cc0b",
          memberName: "欢乐集团北美区（合并）",
          memberRelationLevel: 1,
          memberRelationParentId: "11edd51ffe446176a99e3db16b7e336a",
          memberRelationPos: 4,
          memberRelationTotalCode: "Happy-Global-C,Happy-North-America",
          name: "",
          nodeId: "",
          objectId: "11edd5201ea73ed8a99e65a97ef46fc4",
          parentId: "11edd51ffe446176a99e3db16b7e336a",
          position: 0,
          processId: "",
          scopeReady: "",
          scopeSubmit: ""
        }
      ],
      dbCodeIndex: "",
      dimCode: "",
      entityReady: "",
      entityReconciliation: "",
      entitySubmit: "",
      hasAuth: true,
      id: "11edd51ffe446176a99e3db16b7e336a",
      memberDbCode: "",
      memberId: "11edd51fef4fa521a99eab53716d41fe",
      memberName: "欢乐集团全球股份有限公司（合并）",
      memberRelationLevel: 0,
      memberRelationParentId: "11edd51fef4fa521a99eab53716d41fe",
      memberRelationPos: 1,
      memberRelationTotalCode: "Happy-Global-C",
      name: "",
      nodeId: "",
      objectId: "11edd51ffe446176a99e3db16b7e336a",
      parentId: "11edd51fef4fa521a99eab53716d41fe",
      position: 0,
      processId: "",
      scopeReady: "",
      scopeSubmit: ""
    }
  ],
  message: "",
  messageList: null,
  messageType: null,
  success: true
};

export const chart = {
  data: {
    processDiagramItems: [
      {
        childCount: 2,
        children: [
          {
            childCount: 0,
            children: null,
            childrenTotalNum: null,
            dbCodeIndex: "JH_2023013002",
            dimCode: "Entity",
            hasMore: false,
            id: "11edc243ad6cf632a9e3a1d2b1f048cb",
            levelProcessList: null,
            memberDbCode: "JH_2023013002",
            memberId: "11edc24009aecf85a9e34dbb50767efa",
            memberName: "嘉华股份有限公司",
            memberRelationLevel: 1,
            memberRelationParentId: "11edc243a5833462a9e3e79e6dde9df7",
            memberRelationPos: 1,
            memberRelationTotalCode: "JH_202301301,JH_2023013002",
            name: "",
            nodeStatus: "processing",
            objectId: "11edc243ad6cf632a9e3a1d2b1f048cb",
            parentId: "11edc243a5833462a9e3e79e6dde9df7",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          },
          {
            childCount: 4,
            children: [
              {
                childCount: 0,
                children: null,
                childrenTotalNum: null,
                dbCodeIndex: "YJH_2023013004",
                dimCode: "Entity",
                hasMore: false,
                id: "11edc243b9235071a9e30ff74d4569f6",
                levelProcessList: null,
                memberDbCode: "YJH_2023013004",
                memberId: "11edc24009aecf87a9e3333482ab8ea6",
                memberName: "云南嘉华股份有限公司",
                memberRelationLevel: 2,
                memberRelationParentId: "11edc243a60bc553a9e317c9b7867abd",
                memberRelationPos: 1,
                memberRelationTotalCode:
                  "JH_202301301,YJH_20230130001,YJH_2023013004",
                name: "",
                nodeStatus: "processing",
                objectId: "11edc243b9235071a9e30ff74d4569f6",
                parentId: "11edc243a60bc553a9e317c9b7867abd",
                position: 0,
                submitNum: 0,
                submitRate: "0.0%"
              },
              {
                childCount: 0,
                children: null,
                childrenTotalNum: null,
                dbCodeIndex: "YJH_2023013007",
                dimCode: "Entity",
                hasMore: false,
                id: "11edc243b9235072a9e3bb8c7019be15",
                levelProcessList: null,
                memberDbCode: "YJH_2023013007",
                memberId: "11edc24009aecf88a9e32b6fdcc8d9cb",
                memberName: "云上嘉华股份有限公司",
                memberRelationLevel: 2,
                memberRelationParentId: "11edc243a60bc553a9e317c9b7867abd",
                memberRelationPos: 2,
                memberRelationTotalCode:
                  "JH_202301301,YJH_20230130001,YJH_2023013007",
                name: "",
                nodeStatus: "not_started",
                objectId: "11edc243b9235072a9e3bb8c7019be15",
                parentId: "11edc243a60bc553a9e317c9b7867abd",
                position: 0,
                submitNum: 0,
                submitRate: "0.0%"
              },
              {
                childCount: 2,
                children: [
                  {
                    childCount: 0,
                    children: null,
                    childrenTotalNum: null,
                    dbCodeIndex: "KJH_2023013006",
                    dimCode: "Entity",
                    hasMore: false,
                    id: "11edc243cbc92e01a9e35b5ee6c36d56",
                    levelProcessList: null,
                    memberDbCode: "KJH_2023013006",
                    memberId: "11edc24009aecf8aa9e367a0ebe955f7",
                    memberName: "昆明嘉华股份有限公司",
                    memberRelationLevel: 3,
                    memberRelationParentId: "11edc243c453d8dea9e30ba212e92028",
                    memberRelationPos: 1,
                    memberRelationTotalCode:
                      "JH_202301301,YJH_20230130001,KJH_20230130001,KJH_2023013006",
                    name: "",
                    nodeStatus: "not_started",
                    objectId: "11edc243cbc92e01a9e35b5ee6c36d56",
                    parentId: "11edc243c453d8dea9e30ba212e92028",
                    position: 0,
                    submitNum: 0,
                    submitRate: "0.0%"
                  },
                  {
                    childCount: 0,
                    children: null,
                    childrenTotalNum: null,
                    dbCodeIndex: "KJH_2023013008",
                    dimCode: "Entity",
                    hasMore: false,
                    id: "11edc243cbc92e02a9e355ee786e638c",
                    levelProcessList: null,
                    memberDbCode: "KJH_2023013008",
                    memberId: "11edc24009aecf8ba9e3e9dfac551648",
                    memberName: "五华嘉华股份有限公司",
                    memberRelationLevel: 3,
                    memberRelationParentId: "11edc243c453d8dea9e30ba212e92028",
                    memberRelationPos: 2,
                    memberRelationTotalCode:
                      "JH_202301301,YJH_20230130001,KJH_20230130001,KJH_2023013008",
                    name: "",
                    nodeStatus: "not_started",
                    objectId: "11edc243cbc92e02a9e355ee786e638c",
                    parentId: "11edc243c453d8dea9e30ba212e92028",
                    position: 0,
                    submitNum: 0,
                    submitRate: "0.0%"
                  }
                ],
                childrenTotalNum: null,
                dbCodeIndex: "KJH_20230130001",
                dimCode: "Scope",
                hasMore: true,
                id: "11edc243c453d8dea9e30ba212e92028",
                levelProcessList: null,
                memberDbCode: "KJH_20230130001",
                memberId: "11eda04ef972b1abaf8701c4c4568dc0",
                memberName: "昆明嘉华股份有限公司（合并）",
                memberRelationLevel: 2,
                memberRelationParentId: "11edc243a60bc553a9e317c9b7867abd",
                memberRelationPos: 4,
                memberRelationTotalCode:
                  "JH_202301301,YJH_20230130001,KJH_20230130001",
                name: "",
                nodeStatus: "not_started",
                objectId: "11edc243c453d8dea9e30ba212e92028",
                parentId: "11edc243a60bc553a9e317c9b7867abd",
                position: 0,
                submitNum: 0,
                submitRate: "0.0%"
              },
              {
                childCount: 2,
                children: [
                  {
                    childCount: 0,
                    children: null,
                    childrenTotalNum: null,
                    dbCodeIndex: "DJH_20230130010",
                    dimCode: "Entity",
                    hasMore: false,
                    id: "11edc243ee19834da9e3f76557a02154",
                    levelProcessList: null,
                    memberDbCode: "DJH_20230130010",
                    memberId: "11edc24009aecf8da9e343e6f9013745",
                    memberName: "大理嘉华股份有限公司",
                    memberRelationLevel: 3,
                    memberRelationParentId: "11edc243e1a86498a9e303cfeecaa2ec",
                    memberRelationPos: 1,
                    memberRelationTotalCode:
                      "JH_202301301,YJH_20230130001,DJH_202301301,DJH_20230130010",
                    name: "",
                    nodeStatus: "not_started",
                    objectId: "11edc243ee19834da9e3f76557a02154",
                    parentId: "11edc243e1a86498a9e303cfeecaa2ec",
                    position: 0,
                    submitNum: 0,
                    submitRate: "0.0%"
                  },
                  {
                    childCount: 0,
                    children: null,
                    childrenTotalNum: null,
                    dbCodeIndex: "KJH_2023013006",
                    dimCode: "Entity",
                    hasMore: false,
                    id: "11edc2f7f588d7f4b44fcb1f57a452e7",
                    levelProcessList: null,
                    memberDbCode: "KJH_2023013006",
                    memberId: "11edc24009aecf8aa9e367a0ebe955f7",
                    memberName: "昆明嘉华股份有限公司",
                    memberRelationLevel: 3,
                    memberRelationParentId: "11edc243e1a86498a9e303cfeecaa2ec",
                    memberRelationPos: 2,
                    memberRelationTotalCode:
                      "JH_202301301,YJH_20230130001,DJH_202301301,KJH_2023013006",
                    name: "",
                    nodeStatus: "not_started",
                    objectId: "11edc2f7f588d7f4b44fcb1f57a452e7",
                    parentId: "11edc243e1a86498a9e303cfeecaa2ec",
                    position: 0,
                    submitNum: 0,
                    submitRate: "0.0%"
                  }
                ],
                childrenTotalNum: null,
                dbCodeIndex: "DJH_202301301",
                dimCode: "Scope",
                hasMore: true,
                id: "11edc243e1a86498a9e303cfeecaa2ec",
                levelProcessList: null,
                memberDbCode: "DJH_202301301",
                memberId: "11eda04f1749658daf87ff34e060ce1a",
                memberName: "大理嘉华股份有限公司（合并）",
                memberRelationLevel: 2,
                memberRelationParentId: "11edc243a60bc553a9e317c9b7867abd",
                memberRelationPos: 5,
                memberRelationTotalCode:
                  "JH_202301301,YJH_20230130001,DJH_202301301",
                name: "",
                nodeStatus: "not_started",
                objectId: "11edc243e1a86498a9e303cfeecaa2ec",
                parentId: "11edc243a60bc553a9e317c9b7867abd",
                position: 0,
                submitNum: 0,
                submitRate: "0.0%"
              }
            ],
            childrenTotalNum: null,
            dbCodeIndex: "YJH_20230130001",
            dimCode: "Scope",
            hasMore: true,
            id: "11edc243a60bc553a9e317c9b7867abd",
            levelProcessList: null,
            memberDbCode: "YJH_20230130001",
            memberId: "11eda04ee2d81a69af875b452102c904",
            memberName: "云南嘉华股份有限公司（合并）",
            memberRelationLevel: 1,
            memberRelationParentId: "11edc243a5833462a9e3e79e6dde9df7",
            memberRelationPos: 2,
            memberRelationTotalCode: "JH_202301301,YJH_20230130001",
            name: "",
            nodeStatus: "not_started",
            objectId: "11edc243a60bc553a9e317c9b7867abd",
            parentId: "11edc243a5833462a9e3e79e6dde9df7",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          }
        ],
        childrenTotalNum: null,
        dbCodeIndex: "JH_202301301",
        dimCode: "Scope",
        hasMore: true,
        id: "11edc243a5833462a9e3e79e6dde9df7",
        levelProcessList: null,
        memberDbCode: "JH_202301301",
        memberId: "11eda04ec991c516af870d8063172ad2",
        memberName: "嘉华股份有限公司（合并）",
        memberRelationLevel: 0,
        memberRelationParentId: "11eda04ec991c516af870d8063172ad2",
        memberRelationPos: 2,
        memberRelationTotalCode: "JH_202301301",
        name: "",
        nodeStatus: "not_started",
        objectId: "11edc243a5833462a9e3e79e6dde9df7",
        parentId: "11eda04ec991c516af870d8063172ad2",
        position: 0,
        submitNum: 0,
        submitRate: "0.0%"
      }
    ],
    processDiagramLevels: [
      {
        level: 3,
        levelSubmitRate: "0.0%",
        levelTotalNum: 4,
        processDiagramItemSize: 3,
        processDiagramItems: [
          {
            childCount: 0,
            children: null,
            childrenTotalNum: null,
            dbCodeIndex: "KJH_2023013006",
            dimCode: "Entity",
            hasMore: false,
            id: "11edc243cbc92e01a9e35b5ee6c36d56",
            levelProcessList: null,
            memberDbCode: "KJH_2023013006",
            memberId: "11edc24009aecf8aa9e367a0ebe955f7",
            memberName: "昆明嘉华股份有限公司",
            memberRelationLevel: 3,
            memberRelationParentId: "11edc243c453d8dea9e30ba212e92028",
            memberRelationPos: 1,
            memberRelationTotalCode:
              "JH_202301301,YJH_20230130001,KJH_20230130001,KJH_2023013006",
            name: "",
            nodeStatus: "not_started",
            objectId: "11edc243cbc92e01a9e35b5ee6c36d56",
            parentId: "11edc243c453d8dea9e30ba212e92028",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          },
          {
            childCount: 0,
            children: null,
            childrenTotalNum: null,
            dbCodeIndex: "DJH_20230130010",
            dimCode: "Entity",
            hasMore: false,
            id: "11edc243ee19834da9e3f76557a02154",
            levelProcessList: null,
            memberDbCode: "DJH_20230130010",
            memberId: "11edc24009aecf8da9e343e6f9013745",
            memberName: "大理嘉华股份有限公司",
            memberRelationLevel: 3,
            memberRelationParentId: "11edc243e1a86498a9e303cfeecaa2ec",
            memberRelationPos: 1,
            memberRelationTotalCode:
              "JH_202301301,YJH_20230130001,DJH_202301301,DJH_20230130010",
            name: "",
            nodeStatus: "not_started",
            objectId: "11edc243ee19834da9e3f76557a02154",
            parentId: "11edc243e1a86498a9e303cfeecaa2ec",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          },
          {
            childCount: 0,
            children: null,
            childrenTotalNum: null,
            dbCodeIndex: "KJH_2023013008",
            dimCode: "Entity",
            hasMore: false,
            id: "11edc243cbc92e02a9e355ee786e638c",
            levelProcessList: null,
            memberDbCode: "KJH_2023013008",
            memberId: "11edc24009aecf8ba9e3e9dfac551648",
            memberName: "五华嘉华股份有限公司",
            memberRelationLevel: 3,
            memberRelationParentId: "11edc243c453d8dea9e30ba212e92028",
            memberRelationPos: 2,
            memberRelationTotalCode:
              "JH_202301301,YJH_20230130001,KJH_20230130001,KJH_2023013008",
            name: "",
            nodeStatus: "not_started",
            objectId: "11edc243cbc92e02a9e355ee786e638c",
            parentId: "11edc243c453d8dea9e30ba212e92028",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          }
        ]
      },
      {
        level: 2,
        levelSubmitRate: "0.0%",
        levelTotalNum: 5,
        processDiagramItemSize: 5,
        processDiagramItems: [
          {
            childCount: 0,
            children: null,
            childrenTotalNum: null,
            dbCodeIndex: "YJH_2023013004",
            dimCode: "Entity",
            hasMore: false,
            id: "11edc243b9235071a9e30ff74d4569f6",
            levelProcessList: null,
            memberDbCode: "YJH_2023013004",
            memberId: "11edc24009aecf87a9e3333482ab8ea6",
            memberName: "云南嘉华股份有限公司",
            memberRelationLevel: 2,
            memberRelationParentId: "11edc243a60bc553a9e317c9b7867abd",
            memberRelationPos: 1,
            memberRelationTotalCode:
              "JH_202301301,YJH_20230130001,YJH_2023013004",
            name: "",
            nodeStatus: "processing",
            objectId: "11edc243b9235071a9e30ff74d4569f6",
            parentId: "11edc243a60bc553a9e317c9b7867abd",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          },
          {
            childCount: 0,
            children: null,
            childrenTotalNum: null,
            dbCodeIndex: "YJH_2023013007",
            dimCode: "Entity",
            hasMore: false,
            id: "11edc243b9235072a9e3bb8c7019be15",
            levelProcessList: null,
            memberDbCode: "YJH_2023013007",
            memberId: "11edc24009aecf88a9e32b6fdcc8d9cb",
            memberName: "云上嘉华股份有限公司",
            memberRelationLevel: 2,
            memberRelationParentId: "11edc243a60bc553a9e317c9b7867abd",
            memberRelationPos: 2,
            memberRelationTotalCode:
              "JH_202301301,YJH_20230130001,YJH_2023013007",
            name: "",
            nodeStatus: "not_started",
            objectId: "11edc243b9235072a9e3bb8c7019be15",
            parentId: "11edc243a60bc553a9e317c9b7867abd",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          },
          {
            childCount: 0,
            children: null,
            childrenTotalNum: null,
            dbCodeIndex: "KJH_2023013006",
            dimCode: "Entity",
            hasMore: false,
            id: "11edcea6615c5f189bdceb67cbd01906",
            levelProcessList: null,
            memberDbCode: "KJH_2023013006",
            memberId: "11edc24009aecf8aa9e367a0ebe955f7",
            memberName: "昆明嘉华股份有限公司",
            memberRelationLevel: 2,
            memberRelationParentId: "11edc243a60bc553a9e317c9b7867abd",
            memberRelationPos: 3,
            memberRelationTotalCode:
              "JH_202301301,YJH_20230130001,KJH_2023013006",
            name: "",
            nodeStatus: "not_started",
            objectId: "11edcea6615c5f189bdceb67cbd01906",
            parentId: "11edc243a60bc553a9e317c9b7867abd",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          },
          {
            childCount: 0,
            children: null,
            childrenTotalNum: null,
            dbCodeIndex: "KJH_20230130001",
            dimCode: "Scope",
            hasMore: true,
            id: "11edc243c453d8dea9e30ba212e92028",
            levelProcessList: null,
            memberDbCode: "KJH_20230130001",
            memberId: "11eda04ef972b1abaf8701c4c4568dc0",
            memberName: "昆明嘉华股份有限公司（合并）",
            memberRelationLevel: 2,
            memberRelationParentId: "11edc243a60bc553a9e317c9b7867abd",
            memberRelationPos: 4,
            memberRelationTotalCode:
              "JH_202301301,YJH_20230130001,KJH_20230130001",
            name: "",
            nodeStatus: "not_started",
            objectId: "11edc243c453d8dea9e30ba212e92028",
            parentId: "11edc243a60bc553a9e317c9b7867abd",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          },
          {
            childCount: 0,
            children: null,
            childrenTotalNum: null,
            dbCodeIndex: "DJH_202301301",
            dimCode: "Scope",
            hasMore: true,
            id: "11edc243e1a86498a9e303cfeecaa2ec",
            levelProcessList: null,
            memberDbCode: "DJH_202301301",
            memberId: "11eda04f1749658daf87ff34e060ce1a",
            memberName: "大理嘉华股份有限公司（合并）",
            memberRelationLevel: 2,
            memberRelationParentId: "11edc243a60bc553a9e317c9b7867abd",
            memberRelationPos: 5,
            memberRelationTotalCode:
              "JH_202301301,YJH_20230130001,DJH_202301301",
            name: "",
            nodeStatus: "not_started",
            objectId: "11edc243e1a86498a9e303cfeecaa2ec",
            parentId: "11edc243a60bc553a9e317c9b7867abd",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          }
        ]
      },
      {
        level: 1,
        levelSubmitRate: "0.0%",
        levelTotalNum: 2,
        processDiagramItemSize: 2,
        processDiagramItems: [
          {
            childCount: 0,
            children: null,
            childrenTotalNum: null,
            dbCodeIndex: "JH_2023013002",
            dimCode: "Entity",
            hasMore: false,
            id: "11edc243ad6cf632a9e3a1d2b1f048cb",
            levelProcessList: null,
            memberDbCode: "JH_2023013002",
            memberId: "11edc24009aecf85a9e34dbb50767efa",
            memberName: "嘉华股份有限公司",
            memberRelationLevel: 1,
            memberRelationParentId: "11edc243a5833462a9e3e79e6dde9df7",
            memberRelationPos: 1,
            memberRelationTotalCode: "JH_202301301,JH_2023013002",
            name: "",
            nodeStatus: "processing",
            objectId: "11edc243ad6cf632a9e3a1d2b1f048cb",
            parentId: "11edc243a5833462a9e3e79e6dde9df7",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          },
          {
            childCount: 0,
            children: null,
            childrenTotalNum: null,
            dbCodeIndex: "YJH_20230130001",
            dimCode: "Scope",
            hasMore: true,
            id: "11edc243a60bc553a9e317c9b7867abd",
            levelProcessList: null,
            memberDbCode: "YJH_20230130001",
            memberId: "11eda04ee2d81a69af875b452102c904",
            memberName: "云南嘉华股份有限公司（合并）",
            memberRelationLevel: 1,
            memberRelationParentId: "11edc243a5833462a9e3e79e6dde9df7",
            memberRelationPos: 2,
            memberRelationTotalCode: "JH_202301301,YJH_20230130001",
            name: "",
            nodeStatus: "not_started",
            objectId: "11edc243a60bc553a9e317c9b7867abd",
            parentId: "11edc243a5833462a9e3e79e6dde9df7",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          }
        ]
      },
      {
        level: 0,
        levelSubmitRate: "0.0%",
        levelTotalNum: 1,
        processDiagramItemSize: 0,
        processDiagramItems: [
          {
            childCount: 0,
            children: null,
            childrenTotalNum: null,
            dbCodeIndex: "JH_202301301",
            dimCode: "Scope",
            hasMore: true,
            id: "11edc243a5833462a9e3e79e6dde9df7",
            levelProcessList: null,
            memberDbCode: "JH_202301301",
            memberId: "11eda04ec991c516af870d8063172ad2",
            memberName: "嘉华股份有限公司（合并）",
            memberRelationLevel: 0,
            memberRelationParentId: "11eda04ec991c516af870d8063172ad2",
            memberRelationPos: 2,
            memberRelationTotalCode: "JH_202301301",
            name: "",
            nodeStatus: "not_started",
            objectId: "11edc243a5833462a9e3e79e6dde9df7",
            parentId: "11eda04ec991c516af870d8063172ad2",
            position: 0,
            submitNum: 0,
            submitRate: "0.0%"
          }
        ]
      }
    ]
  },
  message: "",
  messageList: null,
  messageType: null,
  success: true
};
