<template>
  <div class="process-watch-scope">
    <template>
      <yn-dropdown v-model="localVisible" :trigger="['click']">
        <a href="#">
          <span :class="{ 'scope-empty': !scopeId }">{{ scope }}</span>
          <yn-icon type="down" class="drop-icon" />
        </a>
        <div slot="overlay" class="scope-overlay">
          <yn-input-search
            v-model="searchValue"
            class="search"
            :placeholder="$t_common('input_message')"
            @change="onChange"
          />
          <yn-empty v-if="gData.length === 0" class="empty" />
          <yn-tree
            v-else
            :expandedKeys="expandedKeys"
            :autoExpandParent="autoExpandParent"
            :defaultSelectedKeys="[scopeId]"
            :treeData="gData"
            class="tree"
            @expand="onExpand"
          >
            <template
              slot="title"
              slot-scope="{ key, title, disabled, memberRelationTotalCode }"
            >
              <span
                v-if="title.indexOf(searchValue) > -1"
                class="tree-leaf"
                @click="
                  handlerSelect(key, title, disabled, memberRelationTotalCode)
                "
              >
                {{ title.substr(0, title.indexOf(searchValue)) }}
                <span style="color: #1377EB">{{ searchValue }}</span>
                {{
                  title.substr(title.indexOf(searchValue) + searchValue.length)
                }}
              </span>
              <span
                v-else
                class="tree-leaf"
                @click="
                  handlerSelect(key, title, disabled, memberRelationTotalCode)
                "
              >
                {{ title }}
              </span>
            </template>
          </yn-tree>
        </div>
      </yn-dropdown>
    </template>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-tree/";
import { mapMutations, mapState } from "vuex";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import precessService from "@/services/process";
// import { tree } from "./mock";

function formatTree(treeArr, dataList, parent) {
  if (!treeArr) return [];
  for (const tree of treeArr) {
    tree.title = tree.memberName;
    tree.key = tree.memberId;
    tree.disabled = !tree.hasAuth;
    tree.scopedSlots = {
      title: "title"
    };
    const data = {
      key: tree.memberId,
      title: tree.memberName,
      parent: parent
    };
    dataList.push(data);
    formatTree(tree.children, dataList, data);
  }
  return treeArr;
}

function getScopeFirst(treeArr) {
  if (!treeArr) return;
  for (const tree of treeArr) {
    if (tree.hasAuth) {
      return {
        memberName: tree.memberName,
        memberId: tree.memberId,
        memberRelationTotalCode: tree.memberRelationTotalCode
      };
    }
    const scope = getScopeFirst(tree.children);
    if (scope) return scope;
  }
}

function getScopeMatch(treeArr, id) {
  if (!treeArr) return;
  for (const tree of treeArr) {
    if (tree.memberId === id) {
      if (tree.hasAuth) {
        return {
          memberName: tree.memberName,
          memberId: tree.memberId,
          memberRelationTotalCode: tree.memberRelationTotalCode
        };
      } else {
        UiUtils.errorMessage(this.$t_process("no_auth_scope"));
      }
    }
    const scope = getScopeMatch(tree.children, id);
    if (scope) return scope;
  }
}

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  inject: ["defaultParams"],
  data() {
    return {
      scope: this.$t_common("input_select"),
      scopeId: "",
      expandedKeys: [],
      searchValue: "",
      autoExpandParent: true,
      gData: [],
      dataList: []
    };
  },
  computed: {
    ...mapState("processStore", {
      watchParams: state => state.watchParams,
      refresh: state => state.refresh,
      scopeInfo: state => state.scopeInfo
    }),
    localVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.searchValue = "";
        this.gData = this._CacheData;
        this.$emit("update:visible", value);
      }
    }
  },
  watch: {
    refresh: {
      handler() {
        if (this.watchParams && Object.keys(this.watchParams).length > 0) {
          this.setScope(this.scopeId);
        }
      }
    },
    watchParams: {
      handler(value) {
        if (value && Object.keys(value).length > 0) {
          this.setScope();
        }
      }
    }
  },
  methods: {
    ...mapMutations({
      setScopeInfo: "processStore/setScopeInfo"
    }),
    async getScopeTree() {
      this.dataList = [];
      const {
        data: { data }
      } = await precessService("getScopeTree", this.watchParams);
      return data;
    },
    async setScope(scope) {
      this.gData = [];
      const data = await this.getScopeTree();
      this.gData = formatTree(data, this.dataList);
      this._CacheData = this.gData;
      const { memberName, memberId, memberRelationTotalCode } = this.getDefault(
        data,
        scope
      );
      this.scope =
        this.dataList.length > 0
          ? memberName || this.$t_common("input_select")
          : this.$t_common("input_select");
      this.scopeId = this.dataList.length > 0 ? memberId : "";
      this.setScopeInfo({ memberId, memberRelationTotalCode, memberName });
    },
    // 获取scope默认值, scope：主动设置的scope, 优先级最高，次之页面传参，最次首选项
    getDefault(data, scope) {
      const params = this.defaultParams || {};
      let matched = null;
      const select = scope || params.scope;
      if (select) {
        matched = getScopeMatch(data, select);
      }
      return matched || getScopeFirst(data) || {};
    },
    handlerSelect(key, title, disabled, memberRelationTotalCode) {
      if (disabled) return;
      this.scope = title;
      this.scopeId = key;
      this.localVisible = false;
      this.searchValue = "";
      this.setScopeInfo({
        memberId: key,
        memberRelationTotalCode,
        memberName: title
      });
    },

    getParentKey(item, keys) {
      if (item.parent) {
        keys.add(item.parent.key);
        this.getParentKey(item.parent, keys);
      }
    },
    getShowData(data, expandedKeys, matchedKeys) {
      if (!data || data.length === 0) return;
      const arr = [];
      for (const item of data) {
        if (expandedKeys.has(item.key) || matchedKeys.has(item.key)) {
          const temp = { ...item };
          arr.push(temp);
          temp.children = this.getShowData(
            temp.children,
            expandedKeys,
            matchedKeys
          );
        }
      }
      return arr;
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    onChange(e) {
      this.localVisible = true;
      const value = e.target.value;
      const expandedKeys = new Set();
      const matchedKeys = new Set();
      let data;
      this.dataList.forEach(item => {
        if (item.title.indexOf(value) > -1) {
          matchedKeys.add(item.key);
          this.getParentKey(item, expandedKeys, matchedKeys);
        }
      });
      if (matchedKeys.size === 0 && value) {
        data = [];
      } else {
        data = this.getShowData(this._CacheData, expandedKeys, matchedKeys);
      }
      Object.assign(this, {
        expandedKeys: [...expandedKeys],
        searchValue: value,
        autoExpandParent: true,
        gData: data
      });
    }
  }
};
</script>

<style lang="less" scoped>
.process-watch-scope {
  border-radius: 4px;
  .drop-icon {
    color: @yn-disabled-color;
  }
  .scope-empty {
    margin-right: 0.5rem;
    color: @yn-disabled-color;
  }
}
/deep/ .empty {
  height: 100%;
  padding: 0.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.scope-overlay {
  width: 12.5rem;
  padding: 4px 0.625rem;
  width: 14rem;

  border-radius: 4px;
  opacity: 1;
  background: #ffffff;
  box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.14),
    0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 9px 28px 8px rgba(0, 0, 0, 0.05);
  .search {
    margin-bottom: 0;
  }
  .tree {
    max-height: 12.5rem;
    overflow: auto;
    margin-top: 0.5rem;
  }
  .tree-leaf {
    display: block;
    width: 100%;
  }
  /deep/ .ant-tree > li:first-child {
    margin-top: 0;
    padding-top: 0;
  }
}
</style>
