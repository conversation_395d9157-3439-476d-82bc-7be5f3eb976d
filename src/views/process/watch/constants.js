import lang from "../../../mixin/lang";
const { $t_process } = lang;

export const cardColorMap = {
  entity_ready: "#1377EB",
  entity_reconciliation: "#1377EB",
  entity_submit: "#1377EB",
  scope_ready: "#747CFB",
  scope_submit: "#747CFB",
  "0": "#8894A8",
  "100": "#3BB875"
};
export const settingCodeMap = {
  entityReady: "entity_ready",
  entityReconciliation: "entity_reconciliation",
  entitySubmit: "entity_submit",
  scopeReady: "scope_ready",
  scopeSubmit: "scope_submit"
};
export const tableKeyMap = {
  entityReady: "entity_ready",
  entityReconciliation: "entity_reconciliation",
  entitySubmit: "entity_submit",
  scopeReady: "scope_ready",
  scopeSubmit: "scope_submit"
};

export const settingMap = {
  entity_ready: $t_process("single_data_prep"),
  entity_reconciliation: $t_process("ic_reconciliation"),
  entity_submit: $t_process("single_submission"),
  scope_ready: $t_process("consolidation_prep"),
  scope_submit: $t_process("consolidation_submission")
};

export const sortMap = {
  "": $t_process("single_consolidated_submission"),
  ...settingMap
};

export const hintMap = {
  entity_ready: $t_process("assets_total_check"),
  entity_reconciliation: $t_process("ic_reconciliation_report"),
  entity_submit: $t_process("single_submission_status"),
  scope_ready: $t_process("consolidation_fxtrans"),
  scope_submit: $t_process("consolidation_scope_not_started")
};
