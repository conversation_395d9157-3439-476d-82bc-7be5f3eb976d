<template>
  <div class="chart" :style="{ height: height + 'px' }"></div>
</template>

<script>
import * as echarts from "echarts";
import { polling } from "@/utils/common";
function pxToRem(px) {
  return (px / 16) * parseInt(document.documentElement.style.fontSize);
}
const options = {
  color: ["#1377EB"],
  xAxis: {
    type: "value",
    max: 100,
    show: false
  },
  tooltip: {
    trigger: "item",
    axisPointer: {
      type: "none"
    },
    formatter: params => {
      const data = params;
      return `
        ${data.marker} ${data.name}
        ${this.$t_process("progress")}：${data.data.value} %
      `;
    }
  },
  yAxis: {
    type: "category",
    boundaryGap: false,
    axisLabel: {
      color: "#4E5D78",
      fontSize: pxToRem(14),
      formatter: label => label.slice(0, 9) + (label.length > 9 ? "..." : "")
    },
    axisLine: {
      // show: false
    },
    axisTick: {
      // show: false
    }
  },
  dataset: {
    dimensions: ["name", "value", "bg"],
    source: []
  },
  grid: {
    left: "0%",
    right: "40",
    top: "10",
    bottom: "0%",
    containLabel: true
  },
  series: [
    {
      name: "value",
      stack: "chart",
      z: 3,
      label: {
        show: true,
        position: "right",
        color: "#8894A8",
        fontSize: pxToRem(14),
        formatter: params => params.data.value + "%"
      },
      type: "bar",
      barWidth: pxToRem(16)
    },
    {
      name: "bg",
      stack: "chart",
      silent: true,
      itemStyle: {
        color: "#F5F7FA"
      },
      type: "bar",
      barWidth: pxToRem(16)
    }
  ]
};
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      height: 0
    };
  },
  watch: {
    data() {
      this.render();
    }
  },
  mounted() {
    this.initChart();
    this.$resizeObserver = new ResizeObserver(() => {
      this.chart && this.chart.resize();
    });
    this.$resizeObserver.observe(this.$el);
  },
  methods: {
    render() {
      options.dataset.source = [...this.data];
      this.height =
        this.data.length * (pxToRem(16) + pxToRem(18)) - pxToRem(18) + 10;
      this.chart.setOption(options);
    },
    initChart() {
      polling(() => Promise.resolve(this.$el.querySelector), 100).then(() => {
        this.chart = echarts.init(this.$el);
        this.render();
      });
    }
  }
};
</script>

<style lang="less" scoped>
.chart {
  height: 100%;
}
</style>
