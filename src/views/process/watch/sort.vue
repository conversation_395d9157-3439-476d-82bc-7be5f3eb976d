<template>
  <div class="process-watch-sort">
    <div class="sort-title">
      {{ $t_process("monthly_progress_ranking_reverse") }}
      <span class="actions">
        <yn-dropdown v-model="dpVisible" :trigger="['click']">
          <span class="sort-btn">
            {{ sortType }}
            <yn-icon type="down" />
          </span>
          <!-- <div slot="overlay" class="sort-overlay"></div> -->
          <yn-menu slot="overlay" class="sort-custom">
            <yn-menu-item
              v-for="item in sortSetting"
              :key="item"
              @click="selectSort(item, sortMap[item])"
            >
              <span class="txtModel">
                {{ sortMap[item] }}
              </span>
            </yn-menu-item>
          </yn-menu>
        </yn-dropdown>
        <a
          href="#"
          class="viewAll"
          :class="{ disabled: showEmpty }"
          @click.prevent="viewAll"
        >
          {{ $t_process("view_all") }}
        </a>
      </span>
    </div>
    <yn-spin :spinning="loading" class="spinning">
      <!-- <Chart :data="data" /> -->
      <yn-empty v-if="showEmpty" class="message-empty" :description="message" />
      <Process v-else :data="data" class="sort-chart" />
    </yn-spin>
    <DrawerSort
      :visible.sync="visible"
      :title="title"
      :level="level"
      :sortType="sortType"
      :data="total"
      :params="params"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-menu-item/";
import precessService from "@/services/process";
import DrawerSort from "./drawerSort.vue";
import { mapState, mapMutations } from "vuex";
import { sortMap, tableKeyMap } from "./constants";
import Process from "./process.vue";
import _uniqueId from "lodash/uniqueId";
export default {
  components: { Process, DrawerSort },
  data() {
    return {
      title: "",
      sortType: sortMap[""],
      sortKey: "",
      sortMap,
      message: "",
      sortList: [],
      dpVisible: false,
      visible: false
    };
  },
  computed: {
    ...mapState("processStore", {
      level: state => state.archInfo.level,
      archInfo: state => state.archInfo,
      setting: state => state.setting,
      watchParams: state => state.watchParams,
      scopeInfo: state => state.scopeInfo,
      sortLoading: state => state.sortLoading
    }),
    showEmpty() {
      return this.message && this.sortKey === tableKeyMap.entityReady;
    },
    sortSetting() {
      return ["", ...this.setting];
    },
    params() {
      return {
        ...this.watchParams,
        processMonitorCode: this.sortKey,
        memberId: this.scopeInfo.memberId,
        memberRelationTotalCode: this.scopeInfo.memberRelationTotalCode,
        level: this.level
      };
    },
    loading() {
      return this.sortLoading;
    },
    total() {
      return this.sortList.map(item => {
        const value = parseFloat(item.submitRate || 0);
        return {
          name: item.memberName,
          value: value,
          rate: item.submitRate || "0.0%"
        };
      });
    },
    data() {
      return this.total.slice(0, 10);
    }
  },
  watch: {
    setting() {
      this.sortType = sortMap[""];
      this.sortKey = "";
    },
    sortKey: {
      handler() {
        this.getMonitorRank();
      }
    },
    archInfo: {
      handler() {
        this.getMonitorRank();
      }
    }
  },
  mounted() {
    this._scrollDp = () => {
      this.dpVisible = false;
    };
    document
      .querySelector(".contain")
      .addEventListener("scroll", this._scrollDp);
  },
  methods: {
    ...mapMutations("processStore", ["setSortLoading"]),
    async getMonitorRank(queryId = this.setQueryId()) {
      if (!this.scopeInfo.memberRelationTotalCode) {
        this.sortList = [];
        return;
      }
      this.setSortLoading(true);
      const {
        data: { data }
      } = await precessService("getMonitorRank", this.params).catch(e => {
        this.sortList = [];
        this.setSortLoading(false);
      });
      if (queryId === this.getQueryId()) {
        this.message = data ? data.message : "";
        this.sortList = data ? data.processDiagramItems || [] : [];
        this.setSortLoading(false);
      }
    },
    selectSort(key, value) {
      this.sortType = value;
      this.sortKey = key;
    },
    viewAll() {
      if (this.showEmpty) return;
      this.visible = true;
    },
    setQueryId() {
      this._currentQueryId = _uniqueId("QueryId");
      return this._currentQueryId;
    },
    getQueryId() {
      return this._currentQueryId;
    }
  }
};
</script>

<style lang="less" scoped>
.process-watch-sort {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  border-radius: 4px;
  background: #ffffff;
  .sort-title {
    display: flex;
    font-size: 1rem;
    height: 1.5rem;
    margin-bottom: 0.75rem;
    color: @yn-text-color;
    flex-shrink: 0;
    align-items: center;
    .actions {
      display: inline-block;
      margin-left: auto;
    }
    .txtModel {
      padding: 0 0.5rem;
      cursor: pointer;
    }
    .viewAll {
      font-size: 0.875rem;
      margin-left: 1.5rem;
      &.disabled {
        color: @yn-disabled-color;
        cursor: not-allowed;
      }
    }
    .sort-btn {
      color: @yn-text-color-secondary;
      font-size: 0.875rem;
      cursor: pointer;
    }
  }
  .spinning {
    position: relative !important;
    height: 0 !important;
    flex: 1;
  }
  .sort-chart {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .message-empty {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
  /deep/ .ant-spin-blur {
    .ant-spin-nested-loading .ant-spin-dot {
      display: none;
    }
    .grid-empty {
      display: none;
    }
  }
}

/deep/ .ant-dropdown-menu-item:hover {
  background-color: #e7f1fd;
}
</style>
