<template>
  <yn-drawer
    :title="`${$t_process('ranking_reverse_order')}${sortType}`"
    placement="right"
    :closable="true"
    width="45rem"
    class="drawerSort"
    :visible="visible"
    @close="onClose"
  >
    <div class="content">
      <div class="title">
        <span>{{
          `${$t_process("company_level", [level + 1])}${$t_process(
            "consolidation"
          )}_${$t_common("company_home", [data.length])}`
        }}</span>
        <yn-button
          class="export-button"
          :disabled="data.length === 0"
          @click="handlerExport"
        >
          <yn-icon type="export" class="export" /> {{ $t_common("export") }}
        </yn-button>
      </div>
      <Process :data="data" class="chart" />
    </div>
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-drawer/";
import Process from "./process.vue";
import precessService from "@/services/process";
import { downloadFile } from "@/utils/common";
export default {
  components: { Process },
  props: {
    title: {
      type: String,
      default: ""
    },
    level: {
      type: [String, Number],
      default: ""
    },
    sortType: {
      type: String,
      default: ""
    },
    params: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Array,
      default: () => []
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  methods: {
    onClose() {
      this.$emit("update:visible", false);
    },
    async exportProcessRankItem() {
      precessService("exportProcessRankItem", this.params).then(res => {
        downloadFile(res);
      });
    },
    handlerExport() {
      this.exportProcessRankItem();
    }
  }
};
</script>

<style lang="less" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title {
  display: flex;
  flex-shrink: 0;
  margin-bottom: 1rem;
  color: @yn-text-color-secondary;
  align-items: center;
  font-size: 1rem;
  font-weight: 600;
}
.export-button {
  margin-left: auto;
  &[disabled] .export {
    color: @yn-disabled-color !important;
    &:hover {
      color: @yn-disabled-color;
    }
  }
  &:hover .export {
    color: @yn-primary-color;
  }
}
.export {
  color: @yn-label-color;
}
/deep/ .ant-drawer-body {
  padding: 1rem 1.5rem;
}
</style>
