import { FlowchartConnector } from "@jsplumb/connector-flowchart";
export default {
  root: {
    dragOptions: {
      w: 10,
      h: 10,
      containment: "notNegative"
    },
    connectionsDetachable: false,
    paintStyle: {
      stroke: "rgb(48, 196, 138)",
      strokeWidth: 1,
      "stroke-dasharray": "4, 3"
    }
  },
  endpoint: {
    endpoint: {
      type: "Dot",
      options: { cssClass: "process-topo-endpoint" }
    },
    paintStyle: {
      fill: "#7c7c7c"
    },
    reattachConnections: true,
    deleteEndpointsOnDetach: false,
    connectorOverlays: [
      {
        type: "PlainArrow",
        options: { width: 10, length: 10, location: 1 }
      }
    ],
    connectionType: "basic",
    maxConnections: 1
  },
  links: {
    connector: {
      type: FlowchartConnector.type,
      options: {
        stub: 20,
        cornerRadius: 5,
        alwaysRespectStubs: true
      }
    },
    deleteEndpointsOnDetach: false
  },
  anchors: {
    lists: ["Left", "Right", "Bottom", "Top"]
  },
  scope: "process-topo-scope"
};
