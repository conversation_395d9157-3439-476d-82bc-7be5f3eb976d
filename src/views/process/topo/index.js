import TopoCore from "./core";
// import "./style/default.less";
/**
 * Topo Control
 * @param {nodes, links, model, root, meta, config, field}
 */
export default class Topo extends TopoCore {
  constructor({ nodes, links, model, root, meta, config, field }) {
    super({ nodes, links, model, root, meta, config, field });
    this.init();
  }
  // 渲染
  paint(dom) {
    this.dom = dom;
    this.setInstance();
    this.render();
  }
  // 配置初始化
  init() {}
  // 配置重构
  reInit() {}
  // 增量刷新
  refresh(data) {
    this.reset(data);
    this.update(data);
  }
  // 全量重绘
  repaint(data) {
    this.reset(data);
    this.render(data);
  }
}
