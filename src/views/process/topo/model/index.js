import _uniqueId from "lodash/uniqueId";
import lang from "../../../../mixin/lang";
const { $t_common } = lang;

export const Field = {
  root: {
    type: "type",
    name: "name",
    option: "option",
    group: "group",
    multiLanguages: "multiLanguages"
  },
  node: {
    id: "id",
    name: "name",
    type: "type",
    anchor: "anchor",
    source: "source",
    target: "target",
    position: "position"
  },
  link: {
    id: "id",
    source: "source",
    target: "target",
    anchor: "anchor"
  }
};

// link all props
const Node = {
  id: "",
  name: $t_common("unnamed"),
  type: "",
  anchor: ["Left", "Right", "Bottom", "Top"],
  extend: {},
  active: false,
  source: true,
  target: true,
  position: [0, 0]
};
const Root = {
  type: "root",
  name: ""
};

// link all props
const Link = {
  id: "",
  type: "", // dashed // solid
  source: null,
  target: null,
  anchor: ["Bottom", "Top"]
};

export const genNode = (node, field = Field.node) => {
  const newnode = {};
  for (const key in Node) {
    newnode[field[key] || key] = Node[key];
  }
  return { ...newnode, [field.id]: _uniqueId("process-node-"), ...node };
};

export const genLink = (link, field = Field.link) => {
  const newlink = {};
  for (const key in Link) {
    newlink[field[key]] = Link[key];
  }
  return { ...newlink, [field.id]: _uniqueId("process-link-"), ...link };
};

export const genRoot = (root, field = Field.root) => {
  for (const key in Root) {
    root[field[key]] =
      root[field[key]] === undefined ? Root[key] : root[field[key]];
  }
  return root;
};
