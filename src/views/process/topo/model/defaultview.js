import deleteIconPng from "../assets/image/delete.png";
const node = (node, nodefield) => {
  return `<div id="${node[nodefield.id]}" class="process-node-defaults">${
    node[nodefield.name]
  }</div>`;
};
const icon = {
  deleteIcon: () => {
    return `<div class="deleteIcon" dragable="false"  ondragstart="return false" onselectstart="return false;"><img src="${deleteIconPng}"  /></div>`;
  }
};
const overlay = {
  selectOver: () => {
    return `
        <div class="selectOver"><i class="conor lt"></i><i class="conor lb"></i><i class="conor rt"></i><i class="conor rb"></i></div>
      `;
  }
};
export default {
  node,
  icon,
  overlay
};
