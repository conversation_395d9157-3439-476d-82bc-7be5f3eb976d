import defaults from "./defaultview";

export default class View {
  constructor(model, field) {
    this.setModel(model);
    this.field = field;
  }
  setModel(model) {
    const merge = { ...defaults.icon, ...defaults.overlay, ...model };
    for (const key in merge) {
      this[key] = merge[key];
    }
  }
  /**
   * 返回用户自定义 view model
   * @param  {...any} data [{type: required}, ...params]
   * @returns
   */
  getDom(type, ...params) {
    return this.getWrap(type, ...params).firstElementChild;
  }
  getWrap(type, ...params) {
    const str = this[type] ? this[type](...params) : this.defaults(...params);
    var child = document.createElement("div");
    child.innerHTML = str;
    return child;
  }
  getModel(node) {
    const nodefield = this.field.node;
    const dom = this.getWrap(node[nodefield.type], node, nodefield);
    dom.classList.add("process-topo-nodes");
    dom.style.cssText += `
        left: ${node[nodefield.position][0]}px;
        top: ${node[nodefield.position][1]}px;
      `;
    dom.id = node[nodefield.id];
    return dom;
  }
  updateModel(olddom, node) {
    const newdom = this.getModel(node);
    olddom.replaceChild(newdom.firstElementChild, olddom.firstElementChild);
  }
  get defaults() {
    return defaults.node;
  }
}
