/deep/ .process-topo-endpoint {
  opacity: 0;
  z-index: 11;
  position: relative;
  &.drag.enter {
    opacity: 1 !important;
    &::after {
      position: absolute;
      content: " ";
      width: 1.25rem;
      height: 1.25rem;
      border-radius: 50%;
      border: 1px solid #1b1b1b;
      background: #4f6870;
      left: -0.25rem;
      top: -0.25rem;
    }

    &.connect::before {
      position: absolute;
      content: " ";
      width: 1.875rem;
      height: 1.875rem;
      border-radius: 50%;
      border: 1px solid #66b6fc;
      background: #b2e1ff;
      opacity: 0.5;
      left: -0.5625rem;
      top: -0.5625rem;
    }
  }
  &.node.enter {
    opacity: 1 !important;
  }
  &.point.enter {
    opacity: 1;
  }
}
/deep/ .jtk-connected.jtk-drag {
  z-index: -10;
}
/deep/ .deleteIcon {
  width: 1.75rem;
  height: 1.75rem;
  line-height: 1.75rem;
  text-align: center;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  display: inline-block;
  visibility: hidden;
  user-select: none;
  z-index: 10;
  img {
    width: 1.125rem;
    height: 1.125rem;
    opacity: 1;
    user-select: none;
  }
}
/deep/ .process-topo-nodes {
  user-select: none;
  position: absolute;
}
/deep/ .process-node {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
/deep/ .process-node-defaults {
  border-radius: 0.25rem;
  text-align: center;
  line-height: 2.5rem;
  width: 7.5rem;
  height: 2.5rem;
  border: 1px solid rgb(151, 151, 151);
}

/deep/ .selectOver {
  width: 100%;
  padding: 0.1875rem;
  position: absolute;
  left: -0.25rem;
  top: -0.25rem;
  border: 1px solid rgba(44, 112, 255, 0.522);
  height: 100%;
  box-sizing: content-box;
  border-radius: 1px;
  i {
    position: absolute;
    display: inline-block;
    width: 0.5625rem;
    height: 0.5625rem;
    border-radius: 2px;
    border: 1px solid rgba(160, 160, 160, 0.702);

    &.lt {
      left: -0.5rem;
      top: -0.5rem;
    }
    &.lb {
      left: -0.5rem;
      bottom: -0.5rem;
    }
    &.rt {
      right: -0.5rem;
      top: -0.5rem;
    }
    &.rb {
      right: -0.5rem;
      bottom: -0.5rem;
    }
  }
}

/deep/ svg.dashed path {
  stroke-dasharray: 5 5;
}
