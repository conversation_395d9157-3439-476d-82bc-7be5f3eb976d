<template>
  <div class="busness-nodes cs-body">
    <div class="content">
      <section
        :class="['sidebar-meta', { show: sidebarshow, hide: !sidebarshow }]"
      >
        <div class="meta-title">
          <span>{{ $t_process("process_steps") }}</span>
        </div>
        <ul class="meta-list">
          <li v-for="item in meta" :key="item.type">
            <slot name="meta" :data="item">
              <div class="meta-item">
                <SvgIcon
                  :isIconBtn="false"
                  :type="item.icon"
                  class="meta-item-icon"
                />
                <span class="meta-item-name">
                  {{ item.name }}
                </span>
              </div>
            </slot>
          </li>
        </ul>
        <yn-icon-svg
          :type="sidebarshow ? 'left-tree-put-away' : 'left-tree-open'"
          class="sidebar-icon"
          @click="
            () => {
              sidebarshow = !sidebarshow;
            }
          "
        />
      </section>
      <section class="topo-content">
        <div class="topo-canvas"></div>
      </section>
      <aside :class="['aside-props', { show: asideshow, hide: !asideshow }]">
        <div class="custom-cell-props">
          <template v-for="item in meta">
            <slot
              v-if="item.type === cell[field.node.type] && asideshow"
              :name="item.type"
              :element="cell"
            ></slot>
          </template>
          <template>
            <slot
              v-if="'root' === cell[field.root.type] && asideshow"
              name="root"
              :element="cell"
            ></slot>
          </template>
        </div>
        <yn-icon-svg
          :type="asideshow ? 'left-tree-put-away' : 'left-tree-open'"
          class="aside-icon"
          @click="
            () => {
              asideshow = !asideshow;
            }
          "
        />
      </aside>
    </div>
  </div>
</template>
<script>
import Topo from "./index";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-icon-button/";
import SvgIcon from "@/components/ui/SvgIcon.vue";
import YnIconSvg from "yn-p1/libs/components/yn-icon/yn-icon-svg";
export default {
  components: { YnIconSvg, SvgIcon },
  props: {
    meta: {
      type: Array,
      default: () => {
        return [];
      }
    },
    nodes: {
      type: Array,
      default: () => {
        return [];
      }
    },
    config: {
      type: Object,
      default: () => {
        return {};
      }
    },
    field: {
      type: Object,
      default: () => {
        return {};
      }
    },
    links: {
      type: Array,
      default: () => {
        return [];
      }
    },
    root: {
      type: Object,
      default: () => {
        return {};
      }
    },
    model: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      sidebarshow: true,
      asideshow: true,
      cell: {},
      topo: new Topo({
        model: this.model,
        meta: this.meta,
        config: this.config,
        nodes: this.nodes,
        links: this.links,
        root: this.root,
        field: this.field
      })
    };
  },
  watch: {
    nodes: {
      handler(value) {
        this.topo.refresh({ nodes: value });
      }
    },
    links: {
      handler(value) {
        this.topo.refresh({ links: value });
      }
    },
    root: {
      handler(value) {
        this.topo.refresh({ root: value });
      }
    }
  },
  mounted() {
    this.topo.paint(this.$el.querySelector(".topo-canvas"));
    this.topo.drag(this.$el.querySelector(".meta-list"));
    this.topo.on("node-click", node => {
      this.cell = node;
      if (this.asideshow) {
        this.$emit("event", "node-click", node);
      }
    });
    this.topo.on("root", root => {
      this.cell = root;
      if (this.asideshow) {
        this.$emit("event", "root", root);
      }
    });
    this.topo.on("update", cell => {
      this.$nextTick(() => {
        this.cell = cell;
      });
    });
    this.topo.on("node-add", cell => {
      this.$emit("event", "node-add", cell);
    });
    this.topo.on("link-add", cell => {
      this.$emit("event", "link-add", cell);
    });
    this.topo.on("link-delete", cell => {
      this.$emit("event", "link-add", cell);
    });
    this.topo.on("node-delete", cell => {
      this.$emit("event", "node-delete", cell);
    });
    this.topo.on("node-drag", cell => {
      this.$emit("event", "node-drag", cell);
    });
  },
  methods: {
    showRoot() {
      this.cell = this.root;
      if (this.asideshow) {
        this.$emit("event", "root", this.root);
      }
    }
  }
};
</script>

<style lang="less" scoped>
@import "./style/default.less";
.busness-nodes {
  display: flex;
  height: 100%;
  flex-direction: column;
  // font-family: PingFangSC-Medium;
  font-size: 0.875rem;
  .content {
    display: flex;
    height: 100%;
    position: relative;
    overflow: hidden;
  }
  .sidebar-meta {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 15.625rem;
    flex-shrink: 0;
    background-color: rgb(255, 255, 255);
    z-index: 5;
    border-radius: 4px 0px 0px 0px;
    box-shadow: inset -1px 0px 0px 0px rgba(225, 229, 235, 1);
    transition: all 0.3s ease 0s;
    padding: 0.5rem 0;
    .meta-title {
      top: 0px;
      flex-shrink: 0;
      overflow: hidden;
      height: 1.375rem;
      color: @yn-text-color;
      text-align: left;
      line-height: 1.375rem;
      font-weight: 600;
      margin-bottom: 0.625rem;
      span {
        padding-left: 1rem;
      }
    }
    .meta-item {
      display: flex;
      cursor: move;
      z-index: 2;
      height: 2.5rem;
      -webkit-box-align: center;
      align-items: center;
      margin: 0px 1rem;
      &-icon {
        margin-right: 0.5rem;
      }
      &-name {
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 0.875rem;
        color: rgb(74, 74, 74);
        letter-spacing: 0px;
        display: inline-block;
        overflow: hidden;
      }
    }
    .meta-list {
      flex: 1;
      overflow-x: hidden;
      overflow-y: auto;
    }
    .sidebar-icon {
      font-size: 1.875rem;
      position: absolute;
      cursor: pointer;
      top: 50%;
      transform: translateY(-50%);
    }
    &.show {
      .sidebar-icon {
        right: -1.25rem;
      }
    }
    &.hide {
      width: 0 !important;
      .sidebar-icon {
        right: -1.25rem;
      }
    }
  }
  .topo-content {
    position: relative;
    height: 100%;
    width: 100%;
    flex: 1;
    padding: 1rem 2rem;
    background: transparent;
    .topo-canvas {
      width: 100%;
      height: 100%;
      overflow: scroll;
      position: relative;
      box-sizing: border-box;
      background: url("../../../image/grid.png") repeat;
      background-size: 0.625rem 0.625rem;
      // &::-webkit-scrollbar {
      //   width: 6px !important;
      // }
    }
  }
  .aside-props {
    position: relative;
    height: 100%;
    width: 21.875rem;
    flex-shrink: 0;
    margin-left: auto;
    border-radius: 0px 4px 0px 0px;
    box-shadow: inset 1px 0px 0px 0px rgba(225, 229, 235, 1);
    flex: 0 0 auto;
    transition: all 0.3s ease 0s;
    background-color: rgb(255, 255, 255);
    z-index: 15;
    .custom-cell-props {
      height: 100%;
      overflow: hidden;
    }
    .aside-icon {
      font-size: 1.875rem;
      position: absolute;
      top: 50%;
      cursor: pointer;
      transform: rotate(180deg) translateY(50%);
    }
    &.show {
      .aside-icon {
        left: -1.25rem;
      }
    }
    &.hide {
      width: 0 !important;
      .aside-icon {
        left: -1.25rem;
      }
    }
  }
  // ::-webkit-scrollbar-track-piece {
  //   background-color: rgb(228, 228, 228);
  //   border-radius: 0.05rem;
  // }
}
</style>
