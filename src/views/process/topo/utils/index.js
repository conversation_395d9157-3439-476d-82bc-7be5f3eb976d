export function getDomList(content, className) {
  return Array.from(content.querySelectorAll("." + className) || []);
}
export function betchAddClass(content, domClass, className) {
  const clsarr = className.split(" ");
  getDomList(content, domClass).forEach(item => {
    for (const cls of clsarr) {
      item.classList.add(cls);
    }
  });
}
export function addClass(dom, className) {
  const clsarr = className.split(" ");
  for (const cls of clsarr) {
    dom.classList.add(cls);
  }
}
export function betchRemoveClass(content, domClass, className) {
  const clsarr = className.split(" ");
  getDomList(content, domClass).forEach(item => {
    for (const cls of clsarr) {
      item.classList.remove(cls);
    }
  });
}
export function setStyle(dom, style) {
  for (const key in style) {
    dom.style[key] = style[key];
  }
}
export function findDomindex(dom) {
  return Array.from(dom.parentNode.children).indexOf(dom);
}
export function getbgDom(overdom) {
  var dom = document.createElement("div");
  dom.style.cssText = `
    position: absolute;
    width: ${overdom.offsetWidth}px;
    height: ${overdom.offsetHeight}px;
    padding: 15px 10px;
    box-sizing: content-box;
    visibility: hidden;
    z-index: 0;
  `;
  return dom;
}
export function writeover(content, obj) {
  for (const key in obj) {
    content[key] = obj[key];
  }
}
export function getPosition(content, e) {
  const rect = content.getBoundingClientRect();
  return [
    e.pageX - rect.left + content.scrollLeft,
    e.pageY - rect.top + content.scrollTop
  ];
}
export default {
  getDomList,
  betchAddClass,
  betchRemoveClass
};
