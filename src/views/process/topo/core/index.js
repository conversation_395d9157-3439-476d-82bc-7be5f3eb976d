import {
  newInstance,
  EVENT_ENDPOINT_CLICK,
  EVENT_ENDPOINT_MOUSEOVER,
  EVENT_ENDPOINT_MOUSEOUT,
  EVENT_CONNECTION_MOUSEOVER,
  EVENT_CONNECTION_MOUSEOUT,
  EVENT_CONNECTION_CLICK,
  EVENT_DRAG_STOP,
  EVENT_DRAG_START,
  EVENT_DRAG_MOVE
} from "@jsplumb/browser-ui";
import { EVENT_CONNECTION, INTERCEPT_BEFORE_DROP } from "@jsplumb/core";
import _cloneDeep from "lodash/cloneDeep";
import defaultConfig from "../config";
import Event from "./event";
import _debounce from "lodash.debounce";
import {
  betchAddClass,
  betchRemoveClass,
  setStyle,
  findDomindex,
  getbgDom,
  getPosition,
  addClass,
  writeover
} from "../utils";
import { genNode, genLink, genRoot, Field } from "../model";
import View from "../model/view";
import Sortable from "sortablejs";
export default class TopoCore {
  constructor({ nodes, links, model, root, meta, config, field }) {
    this.nodes = nodes || [];
    this.links = links || [];
    this.field = field || Field;
    this.root = genRoot(root);
    this.meta = meta || [];
    this.event = new Event();
    this._endpoints = {};
    this._selecteds = [];
    this._selectedsLinks = [];
    this._focused = {};
    this._connectors = {};
    this._nodesMap = {};
    this._relation = {};
    this._neighbour = [];
    this._mousedown = null;
    this._mouseover = null;
    this._newconnector = true;
    this._showedCell = null;
    this.config = { ...defaultConfig, ...(config || {}) };
    this.view = new View(model, this.field);
    this.render = _debounce(function(...data) {
      this.resetData();
      this.getScroll();
      this.renderRoot();
      this.renderNodes();
      this.renderLink();
      this.updateShowNode();
      this.setScroll();
    }, 200);
    this.update = _debounce(function(...data) {
      this.resetData(true);
      this.getScroll();
      this.renderRoot();
      // this.clearRoot();
      this.renderNodes();
      this.renderLink();
      this.updateShowNode();
      this.setScroll();
    }, 200);
  }
  get nodefields() {
    return this.field.node;
  }
  get linkfields() {
    return this.field.link;
  }
  get rootfields() {
    return this.field.root;
  }
  setInstance() {
    this.instance = newInstance({
      ...this.config.root,
      container: this.dom
    });
    this.instance.setDragGrid(this.config.root.dragOptions);
    this.registerConnectionTypes();
    this.registerEndpointTypes();
    this.addCommonDom();
    this.bindRootEvent();
  }
  renderLink() {
    this.instance.batch(() => {
      const fsource = this.linkfields.source;
      const ftarget = this.linkfields.target;
      const fanchor = this.linkfields.anchor;
      for (const link of this.links) {
        const source = link[fsource];
        const target = link[ftarget];
        const anchor = link[fanchor];
        this._relation[source] || (this._relation[source] = []);
        this._relation[source].push(link);
        this._relation[target] || (this._relation[target] = []);
        this._relation[target].push(link);
        this._neighbour[source].push(target);
        this._neighbour[target].push(source);
        const sourcepoint = this._endpoints[source][anchor[0]];
        const targetpoint = this._endpoints[target][anchor[1]];
        // sourcepoint._node._anchors.push(anchor[0]);
        // targetpoint._node._anchors.push(anchor[1]);
        sourcepoint._node[
          this.nodefields.source + (link[this.linkfields.type] || "solid")
        ] = false;
        targetpoint._node[
          this.nodefields.target + (link[this.linkfields.type] || "solid")
        ] = false;

        const connector = this.instance.connect({
          ...this.config.links,
          type: "base",
          anchors: anchor,
          source: sourcepoint,
          target: targetpoint,
          cssClass: link[this.linkfields.type]
        });
        if (!connector) {
          continue;
        }
        this._connectors[connector.id] = link;
      }
    });
  }
  renderRoot() {
    this.root[this.rootfields.type] = "root";
    this.instance.reset();
  }
  clearRoot() {
    this.instance.select().deleteAll();
    this.instance.selectEndpoints().deleteAll();
    this.clearNodes();
  }
  clearNodes() {
    Array.from(this.dom.querySelectorAll(".process-topo-nodes")).forEach(
      dom => {
        // this.instance.unmanage(dom);
        this.dom.removeChild(dom);
      }
    );
  }
  renderNodes() {
    const fragmeng = document.createDocumentFragment();
    this._nodesMap = {};
    for (const node of this.nodes) {
      const id = node[this.nodefields.id];
      // const source = node[this.nodefields.source + "solid"];
      // const dsource = node[this.nodefields.source + "dashed"];
      // const target = node[this.nodefields.target + "solid"];
      // const dtarget = node[this.nodefields.target + "dashed"];
      this._neighbour[id] = [];
      // node[this.nodefields.source + "solid"] =
      //   source === undefined ? true : source;
      // node[this.nodefields.source + "dashed"] =
      //   dsource === undefined ? true : dsource;
      // node[this.nodefields.target + "solid"] =
      //   target === undefined ? true : target;
      // node[this.nodefields.target + "dashed"] =
      //   dtarget === undefined ? true : dtarget;
      node._anchors = [];
      const dom = this.view.getModel(node);
      this.renderEndpoint(dom, node);
      this.bindNodeEvent(dom, node);
      node.active && this.focusNode(dom, node);
      dom._node = node;
      this._nodesMap[id] = node;
      fragmeng.append(dom);
    }
    this.dom.appendChild(fragmeng);
  }
  renderEndpoint(dom, node) {
    const id = node[this.nodefields.id];
    // const source = node[this.nodefields.source];
    // const target = node[this.nodefields.target];
    this._endpoints[id] = {};
    const anchor = node[this.nodefields.anchor] || this.config.anchors.lists;
    const endpoint = _cloneDeep({ ...this.config.endpoint.endpoint });
    endpoint.options.cssClass += " " + "process-" + id;
    anchor.forEach(key => {
      const point = this.instance.addEndpoint(dom, {
        ...this.config.endpoint,
        endpoint,
        source: true,
        target: true,
        anchor: key,
        type: "basic"
      });
      this._endpoints[id][key] = point;
      point._node = node;
      point.bind("", () => {});
    });
  }
  addCommonDom() {
    this._deleteIcon = this.view.getDom("deleteIcon");
    this.dom.appendChild(this._deleteIcon);
    this._deleteIconBg = getbgDom(this._deleteIcon);
    this.dom.appendChild(this._deleteIconBg);
    this._deleteIcon.addEventListener("click", () => {
      if (this._focused.type === "nodes") {
        this.deleteNode([this._focused.data]);
      } else {
        this.deleteLink([this._focused.data]);
      }
      this._focused = null;
      this.hideDelete();
    });
  }
  on(event, callback) {
    this.event.on(event, callback);
  }

  reset(data) {
    if (data.root) {
      this._showedCell = data.root;
    }
    writeover(this, data);
  }
  updateShowNode() {
    const cell = this._showedCell || {};
    cell.hash = new Date().valueOf();
    this.event.trigger("update", cell);
  }
  bindRootEvent() {
    const domEvents = {
      click: e => {
        this.blurNode();
        if (e.currentTarget === e.target) {
          this.blurLink();
          this.root.hash = new Date().valueOf();
          this._showedCell = this.root;
          this.event.trigger("root", this.root);
        }
        return false;
      },

      mousemove: e => {
        if (e.currentTarget === e.target || this._mousedown) {
          this.hideDelete();
          this._focused = {};
        }
        return false;
      },
      mousedown: e => {
        if (
          typeof e.target.className === "string" &&
          e.target.className.match("process-topo-endpoint")
        ) {
          this._mousedown = this._mouseover;
        }
        return false;
      },
      mouseup: e => {
        // 起始节点或终端节点不存在
        if (!this._mousedown || !this._mouseover) {
          this._newconnector = false;
          this._mousedown = null;
          return false;
        }
        // 始端是结束节点，或结束端是开始节点
        if (
          this._mousedown._node[this.nodefields.type] === "end" ||
          this._mouseover._node[this.nodefields.type] === "start"
        ) {
          this._newconnector = false;
          this._mousedown = null;
          return false;
        }

        // 开始节点不能分批
        if (
          this._mousedown._node[this.nodefields.type] === "start" &&
          this._mouseover._node[this.nodefields.type] === "batch"
        ) {
          this._newconnector = false;
          this._mousedown = null;
          return false;
        }

        // 始端节点不能分批
        if (
          this._mouseover._node[this.nodefields.type] === "end" &&
          this._mousedown._node[this.nodefields.type] === "batch"
        ) {
          this._newconnector = false;
          this._mousedown = null;
          return false;
        }

        // 不是连线节点
        if (
          !(
            typeof e.target.className === "string" &&
            e.target.className.match("process-topo-endpoint")
          )
        ) {
          this._newconnector = false;
          this._mousedown = null;
          return false;
        }
        // 回路
        if (this._mousedown.elementId === this._mouseover.elementId) {
          this._newconnector = false;
          this._mousedown = null;
          return false;
        }
        // 已经相互连接
        if (
          this._neighbour[this._mouseover._node[this.nodefields.id]].indexOf(
            this._mousedown._node[this.nodefields.id]
          ) > 0
        ) {
          this._newconnector = false;
          this._mousedown = null;
          return false;
        }

        // 空端才能连线，一个连线节点只能连一条线
        if (
          !(
            this._mousedown.connections.length === 1 &&
            this._mouseover.connections.length === 0
          )
        ) {
          this._newconnector = false;
          this._mousedown = null;
          return false;
        }

        // 批量不连批量
        if (
          this._mousedown._node[this.nodefields.type] === "batch" &&
          this._mouseover._node[this.nodefields.type] === "batch"
        ) {
          this._newconnector = false;
          this._mousedown = null;
          return false;
        }

        if (
          (this._mousedown._node[this.nodefields.type] === "batch" ||
            this._mouseover._node[this.nodefields.type] === "batch") &&
          this._mousedown._node[this.nodefields.source + "dashed"] !== false &&
          this._mouseover._node[this.nodefields.target + "dashed"] !== false
        ) {
          this._mousedown._node[this.nodefields.source + "dashed"] = false;
          this._mouseover._node[this.nodefields.target + "dashed"] = false;
          const newlink = genLink(
            {
              [this.linkfields.source]: this._mousedown.elementId,
              [this.linkfields.target]: this._mouseover.elementId,
              [this.linkfields.type]: "dashed",
              [this.linkfields.anchor]: [
                this._mousedown._anchor.type,
                this._mouseover._anchor.type
              ]
            },
            this.linkfields
          );
          this.links.push(newlink);
          this.event.trigger("link-add", newlink);
          this._mousedown = null;
          return false;
        }

        if (
          this._mousedown._node[this.nodefields.type] !== "batch" &&
          this._mouseover._node[this.nodefields.type] !== "batch" &&
          this._mousedown._node[this.nodefields.source + "solid"] !== false &&
          this._mouseover._node[this.nodefields.target + "solid"] !== false
          // this._mousedown._node._anchors.length < 2 &&
          // this._mouseover._node._anchors.length < 2
        ) {
          this._mousedown._node[this.nodefields.source + "solid"] = false;
          this._mouseover._node[this.nodefields.target + "solid"] = false;
          // this._mouseover._node._anchors.push(this._mouseover._anchor.type);
          // this._mousedown._node._anchors.push(this._mousedown._anchor.type);
          const newlink = genLink(
            {
              [this.linkfields.source]: this._mousedown.elementId,
              [this.linkfields.target]: this._mouseover.elementId,
              [this.linkfields.type]: "solid",
              [this.linkfields.anchor]: [
                this._mousedown._anchor.type,
                this._mouseover._anchor.type
              ]
            },
            this.linkfields
          );
          this.links.push(newlink);
          this.event.trigger("link-add", newlink);
        }
        this._mousedown = null;
        return false;
      }
    };
    const eventmap = {
      [INTERCEPT_BEFORE_DROP]: (autoevent, userevent) => {
        return false;
      },
      [EVENT_CONNECTION]: (autoevent, userevent) => {
        if (userevent && !this._newconnector) {
          // this.instance.reset();
          // this.renderNodes();
          // this.renderLink();
          // autoevent.connection.detach();
          this._newconnector = true;
        }
      },
      [EVENT_DRAG_MOVE]: data => {},
      [EVENT_DRAG_START]: data => {
        this.hideDelete();
      },
      [EVENT_DRAG_STOP]: data => {
        data.el._node[this.nodefields.position] = [
          parseFloat(data.el.style.left),
          parseFloat(data.el.style.top)
        ];
        this.event.trigger("node-drag", data.el._node);
      },
      [EVENT_ENDPOINT_CLICK]: (e, ...data) => {},
      [EVENT_ENDPOINT_MOUSEOVER]: (e, data) => {
        const node = e._node;
        this._mouseover = e;
        if (
          this._mousedown &&
          data.target.className.match("process-" + node[this.nodefields.id])
        ) {
          addClass(data.target, "enter drag connect");
        } else if (
          data.target.className.match("process-" + node[this.nodefields.id])
        ) {
          betchAddClass(
            this.dom,
            "process-" + node[this.nodefields.id],
            "enter point"
          );
        } else {
          data.target.style.display = "none";
        }
      },
      [EVENT_ENDPOINT_MOUSEOUT]: (e, ...data) => {
        const node = e._node;
        this._mouseover = null;
        betchRemoveClass(
          this.dom,
          "process-" + node[this.nodefields.id],
          "enter point drag connect"
        );
      },
      [EVENT_CONNECTION_CLICK]: (e, ...data) => {
        this.blurLink(e);
        this.focusLink(e);
        return false;
      },
      [EVENT_CONNECTION_MOUSEOVER]: (e, event) => {
        if (!e.target || !e.source) {
          return;
        }
        this._focused = { type: "links", data: this._connectors[e.id] };
        if (!this._mousedown) {
          this.showDelete({
            left:
              parseInt(event.target.parentNode.style.left) + event.offsetX + 5,
            top: parseInt(event.target.parentNode.style.top) + event.offsetY + 5
          });
        }
      },
      [EVENT_CONNECTION_MOUSEOUT]: (e, ...data) => {
        if (!e.target || !e.source) return;
      }
    };
    this.event.betch(this.dom, domEvents);
    this.event.binds(this.instance, eventmap);
  }

  bindNodeEvent(dom, node) {
    const eventmap = {
      mouseover: () => {
        if (this._mousedown) {
          betchAddClass(
            this.dom,
            "process-" + node[this.nodefields.id],
            "enter drag"
          );
        } else {
          betchAddClass(
            this.dom,
            "process-" + node[this.nodefields.id],
            "enter node"
          );
        }
        this._focused = { type: "nodes", data: node };
        if (!this._mousedown) {
          this.showDelete({
            left: parseInt(dom.style.left) + parseInt(dom.offsetWidth) + 5,
            top:
              parseInt(dom.style.top) +
              parseInt((dom.offsetHeight - this._deleteIcon.offsetHeight) / 2)
          });
        }
      },
      mouseout: () => {
        betchRemoveClass(
          this.dom,
          "process-" + node[this.nodefields.id],
          "enter node drag"
        );
      },
      mousedown: () => {
        dom.removeEventListener("mouseover", eventmap.mouseover);
        dom.style.zIndex = 9;
      },
      mouseup: () => {
        dom.addEventListener("mouseover", eventmap.mouseover);
        this.showDelete({
          left: parseInt(dom.style.left) + parseInt(dom.offsetWidth) + 5,
          top:
            parseInt(dom.style.top) +
            parseInt((dom.offsetHeight - this._deleteIcon.offsetHeight) / 2)
        });
        dom.style.zIndex = 0;
      },
      click: e => {
        this.blurNode();
        this.blurLink();
        this.focusNode(dom, node);
        node.hash = new Date().valueOf();
        this._showedCell = node;
        this.event.trigger("node-click", node);
        e.stopPropagation();
        return false;
      }
    };
    this.event.betch(dom, eventmap);
  }
  focusLink(current) {
    current.addType("active");
    this._selectedsLinks.push(current);
  }
  blurLink(current) {
    for (const link of this._selectedsLinks) {
      if (link === current) continue;
      link.removeType("active");
      this.instance.setConnectorHover(link.connector, false);
    }
    this._selectedsLinks = [];
  }
  focusNode(dom, node) {
    this._selecteds.push(dom);
    node.active = true;
    this.view.updateModel(dom, node);
    this.addNodeOverlay(dom, node);
  }
  blurNode() {
    this._selecteds.forEach(dom => {
      dom._node.active = false;
      this.view.updateModel(dom, dom._node);
      this.removeNodeOverlay(dom);
    });
    this._selecteds = [];
  }
  addNodeOverlay(dom, node) {
    const domlay = this.view.getDom("selectOver", node);
    dom._domlay = domlay;
    dom.appendChild(domlay);
  }
  removeNodeOverlay(dom) {
    dom._domlay && dom.removeChild(dom._domlay);
    dom._domlay = null;
  }
  registerConnectionTypes() {
    this.instance.registerConnectionTypes({
      base: {
        paintStyle: {
          stroke: "#7c7c7c",
          strokeWidth: 1,
          "stroke-dasharray": "0",
          outlineStroke: "transparent",
          outlineWidth: 4
        },
        hoverPaintStyle: { stroke: "#108ee9", strokeWidth: 2 }
      },
      conecting: {
        paintStyle: {
          stroke: "rgb(48, 196, 138)",
          strokeWidth: 1,
          "stroke-dasharray": "4, 3"
        }
      },
      active: {
        paintStyle: {
          stroke: "#108ee9",
          strokeWidth: 1,
          "stroke-dasharray": "0",
          outlineStroke: "transparent",
          outlineWidth: 4
        },
        hoverPaintStyle: { stroke: "#108ee9", strokeWidth: 2 }
      }
    });
  }
  registerEndpointTypes() {
    this.instance.registerEndpointTypes({
      basic: {
        paintStyle: { fill: "#4f6870", stroke: "#1b1b1b", strokeWidth: 1 },
        endpointHoverStyle: {
          fill: "#4f6870",
          stroke: "#1b1b1b",
          strokeWidth: 2
        }
      }
    });
  }
  deleteNode(nodes) {
    const newnodes = this.nodes.filter(item => !nodes.includes(item));
    this.nodes.splice(0, this.nodes.length, ...newnodes);
    this.deleteLink([
      ...new Set(
        nodes
          .map(node => {
            return this._relation[node[this.nodefields.id]] || [];
          })
          .flat()
      )
    ]);
    this.event.trigger("node-delete", nodes);
  }
  deleteLink(links) {
    for (const link of links) {
      const source = link[this.linkfields.source];
      const target = link[this.linkfields.target];
      // const anchor = link[this.linkfields.anchor];
      if (this._nodesMap[source] && this._nodesMap[target]) {
        // const sanchors = this._nodesMap[source]._anchors;
        // const tanchors = this._nodesMap[target]._anchors;
        this._nodesMap[source][
          this.nodefields.source + (link[this.linkfields.type] || "solid")
        ] = true;
        this._nodesMap[target][
          this.nodefields.target + (link[this.linkfields.type] || "solid")
        ] = true;
        // this._nodesMap[source]._anchors.splice(sanchors.indexOf(anchor[0]), 1);
        // this._nodesMap[target]._anchors.splice(tanchors.indexOf(anchor[1]), 1);
      }
    }
    const newlinks = this.links.filter(item => !links.includes(item));
    this.links.splice(0, this.links.length, ...newlinks);
    this.event.trigger("link-delete", links);
  }
  showDelete({ left, top }) {
    setStyle(this._deleteIcon, {
      left: left + "px",
      top: top + "px",
      visibility: "visible"
    });
    setStyle(this._deleteIconBg, {
      left: left - 10 + "px",
      top: top - 12 + "px",
      visibility: "visible"
    });
  }
  hideDelete() {
    setStyle(this._deleteIcon, {
      visibility: "hidden"
    });
    setStyle(this._deleteIconBg, {
      visibility: "hidden"
    });
  }
  getScroll() {
    this._scrollLeft = this.dom.scrollLeft;
    this._scrollTop = this.dom.scrollTop;
  }
  setScroll() {
    this.dom.scrollLeft = this._scrollLeft;
    this.dom.scrollTop = this._scrollTop;
  }
  resetData(update = false) {
    this._endpoints = {};
    this._selecteds = [];
    this._selectedsLinks = [];
    this._focused = {};
    this._connectors = {};
    this._nodesMap = {};
    this._relation = {};
    this._neighbour = [];
    this._mousedown = null;
    this._mouseover = null;
    this._newconnector = true;
    this._showedCell = update ? this._showedCell : null;
  }
  drag(dragdom) {
    new Sortable(dragdom, {
      group: {
        name: "meta",
        pull: "clone",
        put: false
      },
      sort: false,
      animation: 150,
      setData: (dataTransfer, dragEl) => {
        const index = findDomindex(dragEl);
        dataTransfer.setData("Text", JSON.stringify(this.meta[index] || {})); // `dataTransfer` object of HTML5 DragEvent
      }
    });
    new Sortable(this.dom);
    this.dom.ondrop = e => {
      const meta = JSON.parse(e.dataTransfer.getData("Text"));
      const newnode = genNode(
        {
          [this.nodefields.name]: meta.showName || meta.name,
          [this.nodefields.type]: meta.type,
          [this.nodefields.position]: getPosition(this.dom, e)
        },
        this.nodefields
      );
      this.nodes.push(newnode);
      this.event.trigger("node-add", newnode);
    };
  }
}
