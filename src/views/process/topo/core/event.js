export default class Event {
  constructor() {
    this._events = {};
  }
  on(event, callback) {
    this._events[event] = callback;
  }
  trigger(type, data) {
    if (this._events[type]) {
      this._events[type](data);
    }
  }
  binds(target, events) {
    for (const event in events) {
      target.bind(event, events[event]);
    }
  }
  betch(target, events) {
    for (const event in events) {
      target.addEventListener(event, events[event]);
    }
  }
}
