<template>
  <section
    :class="['search-like', { focus: focus, blur: !focus }]"
    @click.stop="() => false"
  >
    <input
      ref="local"
      v-model="local"
      :placeholder="placeholder"
      type="text"
      class="cminput"
      @focus="handlerFocus"
      @blur="handlerBlur"
      @keyup="search"
    />
    <div class="tools">
      <span v-if="local" class="summary">{{ current }}/{{ total }}</span>
      <yn-divider class="divider" type="vertical" />
      <svg-icon
        :class="['like-icon', 'slike-icon', { nomatch: !current }]"
        :isIconBtn="false"
        type="down"
        :title="$t_common('next_one')"
        @click.native="next"
      />
      <svg-icon
        :class="['like-icon', 'slike-icon', { nomatch: !current }]"
        :isIconBtn="false"
        type="icon-up"
        :title="$t_common('front_one')"
        @click.native="back"
      />
      <svg-icon
        class="like-icon"
        :isIconBtn="false"
        type="icon-shanchu"
        :title="$t_common('clear')"
        @click.native="clear"
      />
    </div>
  </section>
</template>

<script>
import "yn-p1/libs/components/yn-divider/";
import _debounce from "lodash.debounce";
export default {
  props: {
    data: {
      type: [Array],
      required: true
    },
    formatter: {
      type: [String], // keys formatter liek: "{key}-{key2} {key3}"; wrap key with `{}` and link with any char
      required: true
    },
    id: {
      type: [String, Number],
      default: Date.now(),
      required: false
    },
    placeholder: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      current: 0,
      focus: false,
      total: 0,
      local: ""
    };
  },
  watch: {
    // 组件唯一标识：用于外部手动触发组件更新
    id: {
      handler() {
        this.clear();
      }
    },
    // 关联组件数据：只用作监控关联组件数据变动(虚拟dom)，自动触发组件更新
    // (区别于id触发更新，统一监控,避免在业务层过多的设置不同的id触发组件更新)
    data: {
      handler() {
        this.clear();
      },
      immediate: true
    }
  },
  beforeDestroy() {
    this._cacheMatch = null;
    this._cacheMatchMap = null;
  },
  created() {
    this._cacheMatch = [];
    this._cacheMatchMap = [];
  },
  methods: {
    querylike() {
      this.total = 0;
      this.current = 0;
      this._cacheMatch = [];
      this._cacheMatchMap = [];
      if (this.local) {
        this.getInfo();
        this.setMatch();
      }
    },
    getValue(row, prop) {
      let obj = row;
      const props = prop.split(".");
      for (const key of props) {
        obj = obj[key];
      }
      return obj;
    },
    getInfo(data = this.data, rowindex = 0) {
      const { formatter, local } = this;
      data.forEach((row, i) => {
        if (row) {
          const content = formatter.replace(
            /\{(.*?)\}/g,
            (match, prop, offset, str) => {
              return this.getValue(row, prop);
            }
          );
          const index = content.indexOf(local);
          if (index >= 0) {
            this._cacheMatch.push(row);
            this._cacheMatchMap.push({ row, index: i });
            this.total++;
          }
        }
      });
      this.total && (this.current = 1);
    },
    setMatch() {
      if (!this._cacheMatch[this.current - 1]) return;
      this.$emit(
        "highlight",
        this.local,
        this._cacheMatch[this.current - 1],
        this._cacheMatch,
        this._cacheMatchMap[this.current - 1].index
      );
      this.$emit(
        "match",
        this.local,
        this._cacheMatch[this.current - 1],
        this._cacheMatch,
        this._cacheMatchMap[this.current - 1].index
      );
    },
    search: _debounce(function() {
      this.querylike();
    }, 500),
    back() {
      if (this.total === 0 || this.current === 1) return;
      const { current, local, _cacheMatch, _cacheMatchMap } = this;
      const pro = Math.max(0, current - 2);
      this.current = pro + 1;
      this.$emit(
        "back",
        this._cacheMatch[pro],
        _cacheMatchMap[pro].index,
        _cacheMatchMap[pro + 1].index
      );
      this.$emit(
        "match",
        local,
        _cacheMatch[pro],
        _cacheMatch,
        _cacheMatchMap[pro].index,
        _cacheMatchMap[pro + 1].index
      );
    },
    next() {
      if (this.total === 0 || this.total === this.current) return;
      const { current, local, _cacheMatch, _cacheMatchMap } = this;
      const next = Math.min(_cacheMatch.length - 1, current);
      this.current = next + 1;
      this.$emit(
        "next",
        _cacheMatch[next],
        _cacheMatchMap[next].index,
        _cacheMatchMap[next - 1].index
      );
      this.$emit(
        "match",
        local,
        _cacheMatch[next],
        _cacheMatch,
        _cacheMatchMap[next].index,
        _cacheMatchMap[next - 1].index
      );
    },
    clear() {
      this.local = "";
      this.total = 0;
      this.current = 0;
      this._cacheMatch = [];
      this._cacheMatchMap = [];
      this.$emit("clear", this.local, {}, []);
      this.$emit("match", this.local, {}, []);
    },
    highlight(dom, color) {
      const text = dom.textContent;
      var values = text.split(this.local);
      dom.innerHTML = values.join(
        `<span style="background:${color}">${this.local}</span>`
      );
    },
    handlerFocus() {
      this.focus = true;
    },
    handlerBlur() {
      this.focus = false;
    }
  }
};
</script>

<style scoped lang="less">
.search-like {
  height: 2rem;
  background: @yn-body-background;
  min-width: 10rem;
  display: flex;
  border-radius: 4px;
  align-items: center;
  padding: 0 0.25rem;
  width: 18rem;
  transition: all 0.5s;
  &.focus {
    border: 1px solid rgba(19, 119, 235, 1);
  }
  &.blur {
    border: 1px solid rgba(225, 229, 235, 1);
  }
  .cminput::-webkit-input-placeholder {
    color: @yn-auxiliary-color;
  }
  .cminput {
    padding: 0 0.25rem;
    height: 100%;
    border: none;
    outline: none;
    width: 5.625rem;
    flex: 1;
  }
  .divider {
    top: 0;
    height: 1.8em;
  }
  .summary {
    margin: 0 0.25rem;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    text-align: right;
  }
  .tools {
    user-select: none;
    flex-shrink: 0;
    display: flex;
    align-items: center;
  }
  .like-icon {
    margin: 0 0.25rem;
    cursor: pointer;
    &:hover {
      color: @yn-primary-6;
    }
  }
  .slike-icon /deep/ .svg-icon {
    font-size: 0.875rem;
  }
  .nomatch {
    color: @yn-disabled-color;
  }
}
</style>
