<template>
  <section
    :class="['search-like', { focus: focus, blur: !focus }]"
    @click.stop="() => false"
  >
    <input
      ref="local"
      v-model="local"
      :placeholder="$t_process('search_content')"
      type="text"
      class="cminput"
      @focus="handlerFocus"
      @blur="handlerBlur"
      @keyup="search"
    />
    <div class="tools">
      <span v-if="local" class="summary">{{ current }}/{{ total }}</span>
      <yn-divider class="divider" type="vertical" />
      <svg-icon
        :class="['like-icon', 'slike-icon', { nomatch: !current }]"
        :isIconBtn="false"
        type="down"
        :title="$t_common('next_one')"
        @click.native="next"
      />
      <svg-icon
        :class="['like-icon', 'slike-icon', { nomatch: !current }]"
        :isIconBtn="false"
        type="icon-up"
        :title="$t_common('front_one')"
        @click.native="back"
      />
      <svg-icon
        class="like-icon"
        :isIconBtn="false"
        type="icon-shanchu"
        :title="$t_common('clear')"
        @click.native="clear"
      />
    </div>
  </section>
</template>

<script>
import "yn-p1/libs/components/yn-divider/";
import _debounce from "lodash.debounce";
import { polling } from "../../../../utils/common";
const MATCHCOLOR = Object.freeze({
  select: "#4ccdfe",
  match: "#b8e2ff"
});
export default {
  props: {
    data: {
      type: [Array, Object, Number, String, Boolean],
      required: true
    },
    id: {
      type: [String, Number],
      required: true
    },
    scope: {
      type: [String],
      required: true
    },
    dynamic: {
      type: Boolean,
      required: false
    },
    target: {
      type: [String],
      required: true
    },
    value: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      current: 0,
      focus: false,
      total: 0,
      local: ""
    };
  },
  watch: {
    scope: {
      handler() {
        this.reset();
        this.updateCacheScope();
      },
      immediate: true
    },
    // 组件唯一标识：用于外部手动触发组件更新
    id: {
      handler() {
        this.reset();
        setTimeout(() => {
          this.updateCacheTarget();
        }, 0);
      },
      immediate: true
    },
    // 关联组件数据：只用作监控关联组件数据变动(虚拟dom)，自动触发组件更新
    // (区别于id触发更新，data可以有效监控关联组件$set, obj.prop, pbj[prop]的变动，统一监控,避免在业务层过多的设置不同的id触发组件更新)
    data: {
      handler() {
        this.reset();
        setTimeout(() => {
          this.updateCacheTarget();
        }, 0);
      },
      immediate: true,
      deep: true
    }
  },
  beforeDestroy() {
    this._scopesDOM = [];
    this._targetsDOM = [];
    this._renderDOM = [];
  },
  methods: {
    async updateCacheScope() {
      this._scopesDOM = [];
      await polling(
        () => Promise.resolve(document.querySelectorAll("." + this.scope)),
        300
      ).then(scope => {
        this._scopesDOM = scope;
        this.updateCacheTarget();
      });
    },
    handlerFocus() {
      this.focus = true;
    },
    handlerBlur() {
      this.focus = false;
    },
    updateCacheTarget() {
      this._targetsDOM = [];
      if (this._scopesDOM && this._scopesDOM.length > 0) {
        Array.from(this._scopesDOM).forEach(dom => {
          dom.querySelectorAll("." + this.target).forEach(sdom => {
            this._targetsDOM.push(sdom);
          });
        });
      }
      return this._targetsDOM;
    },
    getCacheDOM(domi) {
      const doms =
        this._targetsDOM && this.dynamic === false
          ? this._targetsDOM
          : this.updateCacheTarget();
      return typeof domi === "number" ? [doms[domi]] : doms;
    },
    querylike() {
      this.resetDOM();
      this.total = 0;
      this.current = 0;
      if (this.local) {
        // window.getSelection().empty();
        this.getCoreInfo();
        this.dividerGroup();
      }
    },
    getCoreInfo() {
      this._renderDOM = this.getCacheDOM().filter((sdom, domi) => {
        const text = sdom.textContent;
        const index = text.indexOf(this.local);
        if (index < 0) {
          return false;
        }
        this.total++;
        // TODO 缓存优化为WeakSet，1.防止内存泄漏， 2.更方便的获取index
        sdom._domindex = domi;
        return true;
      });
      this.total && (this.current = 1);
    },
    renderDOM(data, group) {
      window.requestAnimationFrame(() => {
        data.forEach((sdom, domi) => {
          const isSelect = group === 0 && domi === 0;
          this.highlight2(
            sdom,
            isSelect ? MATCHCOLOR["select"] : MATCHCOLOR["match"]
          );
          // this.setRange(sdom, sdom._matchindex);
          // this.highlight(isSelect ? MATCHCOLOR["select"] : MATCHCOLOR["match"]);
        });
      });
    },
    dividerGroup(number = 20) {
      const doms = this._renderDOM;
      const group = Math.ceil(doms.length / number);
      for (let i = 0; i < group; i++) {
        const index = i;
        window.requestAnimationFrame(() => {
          setTimeout(() => {
            this.renderDOM(
              doms.slice(index * number, (index + 1) * number),
              index
            );
          }, Math.min(index * 500, 1000));
        });
      }
    },
    updatelike(domrepain, domreset = this.current - 1) {
      if (domrepain === domreset) return;
      this.setSelect(domreset, "match");
      this.setSelect(domrepain, "select");
    },
    search: _debounce(function() {
      this.querylike();
    }, 500),
    back() {
      if (this.total === 0) return;
      const pro = Math.max(0, this.current - 2);
      this.updatelike(pro);
      this.current = pro + 1;
      this.$emit("back", this._renderDOM[pro], pro);
    },
    next() {
      if (this.total === 0) return;
      const next = Math.min(this._renderDOM.length - 1, this.current);
      this.updatelike(next);
      this.current = next + 1;
      this.$emit("next", this._renderDOM[next], next);
    },
    reset() {
      this.resetDOM();
      this.local = "";
      this.total = 0;
      this.current = 0;
      this._renderDOM = [];
    },
    clear() {
      this.reset();
      this.$emit("clear");
    },
    resetDOM(domi, doms = this.getCacheDOM(domi)) {
      if (this.total === 0) return;
      doms.forEach(sdom => {
        const text = sdom.textContent;
        sdom.innerHTML = text;
      });
    },
    setSelect(domi, type) {
      const dom = this._renderDOM[domi];
      this.resetDOM(domi, [dom]);
      this.highlight2(dom, MATCHCOLOR[type]);
      // this.setRange(dom[0], dom[0]._matchindex);
      // this.highlight(MATCHCOLOR[type]);
    },

    // dom 实现（高性能）
    highlight2(dom, color) {
      const text = dom.textContent;
      var values = text.split(this.local);
      dom.innerHTML = values.join(
        `<span style="background:${color}">${this.local}</span>`
      );
    },
    // 原生选中
    setRange(dom, index) {
      const newRange = document.createRange();
      newRange.setStart(dom.childNodes[0], index);
      newRange.setEnd(dom.childNodes[0], index + this.local.length);
      window.getSelection().addRange(newRange);
    },
    // 原生高亮
    highlight(color) {
      // 获取文本
      var range;
      var sel = window.getSelection();
      if (sel.rangeCount && sel.getRangeAt) {
        range = sel.getRangeAt(0);
      }
      // 开启文档编辑
      document.designMode = "on";
      if (range) {
        sel.removeAllRanges();
        sel.addRange(range);
      }
      // TODO 稍后实现替代方案
      document.execCommand("BackColor", false, color);
      document.designMode = "off";
      // 去掉默认选中的蓝色
      window.getSelection().empty();
    }
  }
};
</script>

<style scoped lang="less">
.search-like {
  height: 2rem;
  background: @yn-body-background;
  min-width: 10rem;
  display: flex;
  border-radius: 4px;
  align-items: center;
  padding: 0 0.25rem;
  width: 18rem;
  transition: all 0.5s;
  &.focus {
    border: 1px solid rgba(19, 119, 235, 1);
  }
  &.blur {
    border: 1px solid rgba(225, 229, 235, 1);
  }
  .cminput {
    padding: 0 0.25rem;
    height: 100%;
    border: none;
    outline: none;
    width: 5.625rem;
    flex: 1;
  }
  .divider {
    top: 0;
    height: 1.8em;
  }
  .summary {
    margin: 0 0.25rem;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    text-align: right;
  }
  .tools {
    user-select: none;
    flex-shrink: 0;
    display: flex;
    align-items: center;
  }
  .like-icon {
    margin: 0 0.25rem;
    cursor: pointer;
    &:hover {
      color: @yn-primary-6;
    }
  }
  .slike-icon /deep/ .svg-icon {
    font-size: 0.875rem;
  }
  .nomatch {
    color: @yn-disabled-color;
  }
}
</style>
