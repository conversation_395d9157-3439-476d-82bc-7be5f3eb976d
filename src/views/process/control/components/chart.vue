<template>
  <section class="chart-wrap chart" @mousemove="handlerMove">
    <div ref="chart-ref" class="chart-instance"></div>
    <div class="chart-center">
      <slot name="center" :select="chartInfo"></slot>
    </div>
  </section>
</template>

<script>
import * as echarts from "echarts";
import { polling } from "@/utils/common";
export default {
  props: {
    options: {
      type: Object,
      default: () => ({})
    },
    active: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: [Array],
      default: () => []
    },
    hover: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      chartInfo: null
    };
  },
  computed: {
    localHover: {
      set(value) {
        this.$emit("update:hover", value);
      },
      get() {
        return this.hover;
      }
    },
    localActive: {
      set(value) {
        this.$emit("update:active", value);
      },
      get() {
        return this.active;
      }
    }
  },
  watch: {
    options: {
      handler(value) {
        this.chart && this.chart.setOption(value);
      },
      immediate: true,
      deep: true
    },
    data: {
      handler(value) {
        this.options.series[0].data = value;
        this.chart && this.chart.setOption(this.options);
        this.chartInfo = this.localActive
          ? this.genChartInfo(this.localActive.name)
          : null;
      },
      deep: true
    },
    localActive(value, old) {
      old && this.handlerStatus(old.name, "downplay");
      value && this.handlerStatus(value.name, "highlight");
      this.chartInfo = value ? this.genChartInfo(value.name) : null;
    }
  },
  created() {
    this.initChart();
  },
  methods: {
    initChart() {
      polling(() => Promise.resolve(this.$el.querySelector), 100).then(() => {
        this.chart = echarts.init(this.$el.querySelector(".chart-instance"));
        this.chart.setOption(this.options);
        this.chart.on("legendselectchanged", this.handlerLegend);
        this.chart.on("mouseout", this.handlerOut);
        this.chart.on("mousemove", this.handlerOver);
        this.chart.on("click", this.handlerClick);
      });
    },
    handlerLegend(info) {
      const selected = this.chartInfo ? this.chartInfo.name : "";
      const selects = info.selected;
      if (selected && !selects[selected]) {
        this.chartInfo = null;
        this.localActive = null;
      }
      this.$emit("legendselectchanged", info, this.chartInfo);
    },
    handlerOut(info) {
      this.localHover = null;
      const selected = this.chartInfo ? this.chartInfo.name : null;
      if (info.name !== selected) {
        this.handlerStatus(info.name, "downplay");
      }
      this.$emit("mouseout", info);
    },
    handlerOver(info) {
      this.localHover = info;
      const high = this.getHighName();
      this.handlerStatus(high, "highlight");
      this.$emit("mouseover", info);
    },
    handlerClick(info) {
      if (this.chartInfo && this.chartInfo.name === info.name) {
        this.chartInfo = null;
        this.localActive = null;
      } else {
        this.chartInfo = info;
        this.localActive = info;
      }
      this.$emit("click", info);
    },
    handlerMove() {
      const islenged =
        this.localHover && this.localHover.componentType === "legend";
      const high = this.getHighName(islenged);
      this.handlerStatus(high, "highlight");
    },
    genChartInfo(value) {
      const sum = this.data.reduce((pre, next) => pre + next.value, 0);
      const item = this.data.find(item => item.name === value);
      item.percent = sum ? ((item.value / sum) * 100).toFixed(2) : 0;
      return item;
    },
    reset() {
      this.localActive = null;
      this.localHover = null;
      this.chartInfo = null;
    },
    getHighName(islenged) {
      const infoname = this.chartInfo ? this.chartInfo.name : "";
      const hovername =
        this.localHover && !islenged ? this.localHover.name : "";
      const arr = [];
      infoname && arr.push(infoname);
      hovername && arr.push(hovername);
      return arr;
    },
    handlerStatus(name, type) {
      if (name && name.length > 0) {
        this.chart.dispatchAction({
          type: type,
          name: name
        });
      }
    },
    resize() {
      this.chart && this.chart.resize();
    }
  }
};
</script>

<style scoped lang="less">
.chart {
  position: relative;
}
.chart-instance {
  width: 100%;
  height: 100%;
}
</style>
