<template>
  <yn-drawer
    :title="localtitle"
    placement="right"
    :closable="true"
    width="27.5rem"
    class="drawer-form"
    :visible="visible"
    @close="onClose"
  >
    <div class="dataarea">
      <div v-for="(item, index) in forms" :key="index" class="item">
        <div v-if="typeof item != 'string'">
          <div class="item-title">{{ item.dimName }}</div>
          <div
            v-for="(sitem, sindex) in item.dimMemberName"
            :key="sindex"
            class="item"
          >
            {{ sitem }}
          </div>
        </div>
        <div v-else>{{ item }}</div>
      </div>
    </div>
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-menu/";

export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    data: {
      type: Object,
      default: () => ({})
    },
    forms: {
      type: Array,
      default: () => []
    },
    status: {
      type: String,
      default: ""
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  computed: {
    localtitle() {
      return (
        this.data.batchStatus.v + "-" + this.$t_process("batch") + this.status
      );
    }
  },
  methods: {
    onClose() {
      this.tabledata = [];
      this.expandedRowKeys = [];
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style lang="less" scoped>
.dataarea {
  height: 0;
  flex: 1;
  overflow-x: hidden;
  overflow-y: scroll;
  .item-title,
  .item {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0px;
    color: @yn-text-color;
    margin-bottom: 0.875rem;
  }
  .item-title {
    color: @yn-label-color;
  }
}
.drawer-form {
  z-index: 1055 !important;
}
/deep/ .ant-drawer-wrapper-body {
  display: flex;
  flex-direction: column;
  .ant-drawer-body {
    display: flex;
    flex-direction: column;
    height: 0;
    flex: 1;
    overflow: auto;
  }
}
.maxwidth {
  width: 100%;
}
.money {
  text-align: right;
}

.menuwrap {
  padding: 0.25rem 0;
}
.dropmenu {
  margin: 0 !important;
  color: @yn-text-color;
  &:hover {
    background-color: @yn-hover-bg-color;
  }
}
</style>
