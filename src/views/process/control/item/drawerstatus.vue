<template>
  <yn-drawer
    :title="$t_process('process_status_statistics')"
    placement="right"
    :closable="true"
    width="60rem"
    :visible="visible"
    class="drawerstatus"
    @close="handlerClose"
  >
    <yn-spin :spinning="spinning" class="state-spinning">
      <header class="status-header">
        {{ data.memberName.v }}
        <yn-button
          class="down-button"
          :loading="eLoading"
          @click="handlerExport"
        >
          <svg-icon
            class="icon-export"
            :title="$t_common('export')"
            type="icon-yn_cookbook_common_export"
          />
          {{ $t_common("export") }}
        </yn-button>
      </header>
      <section class="card-groups">
        <yn-card
          v-for="(card, index) in cards"
          :key="card.id"
          :hoverable="true"
          :class="['card', { active: card.id === activeId }]"
          @click="handlerCardCLick(card)"
        >
          <div class="summary">
            <span class="summary-type" :title="card.nodeName">
              <span
                class="summary-type-icon"
                :style="{ background: COLOR[index] }"
              >
              </span>{{ card.nodeName }}
            </span>
            <span class="summary-num" :title="card.sumNum">{{
              card.sumNum
            }}</span>
          </div>
          <yn-divider type="vertical" class="divider" />
          <div class="detail">
            <span class="detail-type">
              {{ $t_process("not_validated") }}
              <span class="detail-num">{{ card.notValidatedNum }}</span>
            </span>
            <span class="detail-type">
              {{ $t_process("successful") }}
              <span class="detail-num">{{ card.successNum }}</span>
            </span>
            <span class="detail-type">
              {{ $t_common("failed_n") }}
              <span class="detail-num">{{ card.failNum }}</span>
            </span>
            <span class="detail-type">
              {{ $t_common("warning") }}
              <span class="detail-num">{{ card.warningNum }}</span>
            </span>
          </div>
        </yn-card>
      </section>

      <yn-row :gutter="16" class="status-chart">
        <yn-col class="gutter-row" :span="12">
          <header class="chart-title">
            {{ $t_process("process_status_chart") }}
          </header>
          <Chart
            v-if="visible"
            ref="processChart"
            class="process"
            :active.sync="activeStatus"
            :hover.sync="hoverProcss"
            :data="processData"
            :options="process"
            @click="handlerProcess"
          >
            <template #center="{select}">
              <div class="process-info">
                <div class="process-info-name">
                  {{ select ? select.percent + "%" : "" }}
                </div>
                <div class="process-info-percent">
                  {{ select ? getName(select.name) : "" }}
                </div>
              </div>
            </template>
          </Chart>
        </yn-col>
        <yn-col class="gutter-row" :span="12">
          <header class="chart-title">
            {{ STATUSNAMEMAP[activeId] ? STATUSNAMEMAP[activeId] + " - " : ""
            }}{{ $t_process("validation_status_chart") }}
          </header>
          <Chart
            v-if="visible"
            ref="checkChart"
            class="check"
            :active.sync="activeCheck"
            :hover.sync="hoverCheck"
            :options="check"
            :data="checkData"
            @click="handlerCheck"
            @legendselectchanged="handlerCheckLegend"
          >
            <template #center="{select}">
              <div class="check-info">
                <div class="check-info-name">
                  {{ select ? select.percent + "%" : "" }}
                </div>
                <div class="check-info-percent">
                  {{ select ? select.name : "" }}
                </div>
              </div>
            </template>
          </Chart>
        </yn-col>
      </yn-row>

      <header v-if="activeStatus || activeCheck" class="table-header">
        {{
          (STATUSNAMEMAP[activeId]
            ? (STATUSNAMEMAP[activeId] !== "-"
              ? STATUSNAMEMAP[activeId]
              : $t_process("empty")) + " - "
            : "") + (activeCheck ? activeCheck.name + " - " : "")
        }}{{ $t_process("list_of_entities") }}
        <span class="total">
          {{ $t_process("total") }}:
          <span class="total-num">{{ tableData.length }}</span>
        </span>
      </header>
      <section v-if="activeStatus || activeCheck">
        <yn-table
          :columns="columns"
          :data-source="tableData"
          :loading="loading"
        >
          <div slot="lastSubmitDate" slot-scope="text" class="rejectNum">
            {{ getText(text) }}
          </div>
          <div slot="firstSubmitDate" slot-scope="text" class="rejectNum">
            {{ getText(text) }}
          </div>
          <div slot="rejectNum" slot-scope="text" class="rejectNum">
            {{ getReject(text) }}
          </div>
        </yn-table>
      </section>
    </yn-spin>
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-card/";
import "yn-p1/libs/components/yn-card-meta/";
import "yn-p1/libs/components/yn-divider/";
import precessService from "@/services/process";
import Chart from "../components/chart.vue";
import _uniqueId from "lodash/uniqueId";
import { downloadFile, hasValue } from "@/utils/common";
import UiUtils from "yn-p1/libs/utils/UiUtils";

const STATUSMAPCLASS = Object.freeze({
  not_started: "unstart",
  processing: "process",
  submitted: "checking",
  pending_review: "checked",
  other: "other"
});
const COLOR = [
  "#BCC1CC",
  "#1377EB",
  "#FFB829",
  "#40BD48",
  "#58ccfa",
  "#747CFB",
  "#FF99E6",
  "#CDA5F3"
];
export default {
  components: { Chart },
  props: {
    title: {
      type: String,
      default: ""
    },
    params: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      name: "",
      activeStatus: null,
      activeCheck: null,
      spinning: false,
      eLoading: false,
      loading: false,
      COLOR,
      STATUSMAPCLASS,
      cards: [],
      tableData: [],
      columns: [
        {
          title: this.$t_process("entity"),
          dataIndex: "memberName",
          key: "memberName",
          scopedSlots: {
            customRender: "memberName"
          }
        },
        {
          title: this.$t_process("latest_submission_time"),
          dataIndex: "lastSubmitDate",
          key: "lastSubmitDate",
          width: "11.25rem",
          scopedSlots: {
            customRender: "lastSubmitDate"
          }
        },
        {
          title: this.$t_process("first_submission_time"),
          dataIndex: "firstSubmitDate",
          key: "firstSubmitDate",
          width: "11.25rem",
          scopedSlots: {
            customRender: "firstSubmitDate"
          }
        },
        {
          title: this.$t_process("rejected_times"),
          dataIndex: "rejectNum",
          key: "rejectNum",
          align: "right",
          width: "6.25rem",
          scopedSlots: {
            customRender: "rejectNum"
          }
        }
      ],
      process: {
        color: COLOR,
        legend: {
          orient: "vertical",
          right: "30px",
          top: "middle",
          type: "scroll",
          inactiveColor: "rgb(231,234,238)",
          inactiveBorderColor: "rgb(231,234,238)",
          // data: ["未开始", "进行中", "待审核", "已提交"],
          itemHeight: 12,
          itemWidth: 12,
          itemGap: 16,
          icon: "roundRect",
          textStyle: {
            color: "#8894A8",
            rich: {
              a: {
                width: 60
              },
              b: {
                align: "right"
              }
            }
          },
          itemStyle: {
            borderWidth: 0
          },
          formatter: name => {
            return this.getFormatterName(name, this.processLegendMap);
          },
          tooltip: {
            show: true,
            formatter: params => {
              const index = params.name.lastIndexOf("-");
              return params.name.slice(0, index);
              // setTimeout(() => {
              //   this.hoverProcss = params;
              // }, 0);
            }
          }
        },
        tooltip: {
          trigger: "item",
          // formatter: "{a} <br/>{b} : {c} ({d}%)"
          formatter: params => {
            return this.getTooltip(
              params,
              this.processLegendMap,
              this.$t_process("process_status")
            );
          }
        },
        series: [
          {
            name: this.$t_process("process_status"),
            type: "pie",
            // selectedMode: "single",
            // selectedOffset: "0",
            radius: ["38%", "65%"],
            center: ["30%", "50%"],
            // minAngle: 3,
            stillShowZeroSum: false,
            data: [],
            label: {
              show: false
            },
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 2
            },
            blur: {
              itemStyle: {
                opacity: 0.3
              }
            },
            emphasis: {
              scale: true,
              scaleSize: 15,
              focus: "self",
              itemStyle: {}
            }
          }
        ]
      },
      check: {
        color: ["#BCC1CC", "#40BD48", "#F54645", "#FFB829"],
        tooltip: {
          trigger: "item",
          formatter: params => {
            return this.getTooltip(
              params,
              this.checkLegendMap,
              this.$t_process("validation_status")
            );
          }
        },
        legend: {
          orient: "vertical",
          right: "30px",
          top: "middle",
          type: "scroll",
          inactiveColor: "rgb(231,234,238)",
          inactiveBorderColor: "rgb(231,234,238)",
          // data: ["未校验", "通过", "失败"],
          itemHeight: 12,
          itemWidth: 12,
          itemGap: 16,
          icon: "roundRect",
          textStyle: {
            color: "#8894A8",
            rich: {
              a: {
                width: 60
              },
              b: {
                align: "right"
              }
            }
          },
          itemStyle: {
            borderWidth: 0
          },
          formatter: name => {
            return this.getFormatterName(name, this.checkLegendMap);
          },
          tooltip: {
            show: true
            // formatter: params => {
            //   console.log(params);
            //   // setTimeout(() => {
            //   //   this.hoverCheck = params;
            //   // }, 0);
            //   return ``;
            // }
          }
        },
        series: [
          {
            name: this.$t_process("validation_status"),
            type: "pie",
            selectedMode: "single",
            selectedOffset: "0",
            radius: ["38%", "65%"],
            center: ["30%", "50%"],
            data: [],
            // minAngle: 3,
            stillShowZeroSum: false,
            label: {
              show: false
            },
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 2
            },
            blur: {
              itemStyle: {
                opacity: 0.3
              }
            },
            emphasis: {
              scale: true,
              scaleSize: 15,
              focus: "self",
              itemStyle: {}
            }
          }
        ]
      },
      processChart: null,
      checkChart: null,
      hoverProcss: null,
      hoverCheck: null,
      processLegendMap: {},
      checkLegendMap: {},
      checkData: [],
      lasts: [],
      processData: []
    };
  },
  computed: {
    activeId() {
      return this.activeStatus && this.activeStatus.data
        ? this.activeStatus.data.id
        : "";
    },
    STATUSMAP() {
      const statusMap = {};
      this.cards.reduce(
        (pre, next) => (statusMap[next.id] = next.nodeStatus),
        statusMap
      );
      return statusMap;
    },
    STATUSNAMEMAP() {
      const statusMap = {};
      this.cards.reduce(
        (pre, next) => (statusMap[next.id] = next.nodeName),
        statusMap
      );
      return statusMap;
    }
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.tableData = [];
          this.activeCheck = null;
          this.activeStatus = null;
          this.processChart = null;
          this.checkChart = null;
          this.hoverProcss = null;
          this.hoverCheck = null;
          this.getProcessStatsList();
        }
      },
      immediate: true
    },
    activeId(value, oldValue) {
      if (oldValue && value && value !== oldValue) {
        this.activeCheck = null;
      }
      this.handlerCheck();
      // this.activeCheck = ""
      this.checkData = this.setCheckData(this._checkItemMap[this.activeId]);
    }
  },
  methods: {
    async getProcessStatsList() {
      this.spinning = true;
      const {
        data: { data }
      } = await precessService("getProcessStatsList", {
        ...this.params,
        memberRelationTotalCode: this.data.memberRelationTotalCode
      });
      this.setCard(data || []);
      this.setChartData(this.cards);
      this.spinning = false;
    },
    async getProcessStatsItemList(queryId = this.setQueryId()) {
      this.loading = true;
      const {
        data: { data }
      } =
        this.STATUSMAP[this.activeId] !== "other"
          ? await precessService("getProcessStatsItemList", {
            ...this.params,
            nodeStatus: this.STATUSMAP[this.activeId],
            nodeName: this.activeStatus
              ? this.getName(this.activeStatus.name)
              : "",
            checkStatus: this.activeCheck ? this.activeCheck.name : "",
            memberRelationTotalCode: this.data.memberRelationTotalCode
          })
          : await precessService(
            "batchGetProcessStatsItemList",
            this.lasts.map(item => {
              return {
                ...this.params,
                nodeStatus: item.nodeStatus,
                nodeName: item.nodeName,
                checkStatus: this.activeCheck ? this.activeCheck.name : "",
                memberRelationTotalCode: this.data.memberRelationTotalCode
              };
            })
          );
      if (queryId === this.getQueryId()) {
        this.tableData = data || [];
        this.loading = false;
      }
    },
    async handlerExport() {
      this.eLoading = true;
      precessService("exportProcessStatsItem", {
        ...this.params,
        memberRelationTotalCode: this.data.memberRelationTotalCode
      })
        .then(res => {
          if (res.data) {
            downloadFile(res);
            UiUtils.successMessage(
              this.$t_common("export") + this.$t_common("success")
            );
          } else {
            UiUtils.errorMessage(
              this.$t_common("export") + this.$t_common("failed_n")
            );
          }
        })
        .catch(e => {
          UiUtils.errorMessage(
            this.$t_common("export") + this.$t_common("failed_n")
          );
        })
        .finally(() => {
          this.eLoading = false;
        });
    },
    getText(text) {
      return this.STATUSMAP[this.activeId] !== "processing" &&
        this.STATUSMAP[this.activeId] !== "not_started"
        ? hasValue(text)
          ? text
          : "-"
        : "-";
    },
    getReject(text) {
      return hasValue(text) ? text || "-" : "-";
    },
    getName(name) {
      const index = name.lastIndexOf("-");
      return index > 0 ? name.slice(0, index) : name;
    },
    formatterName(name) {
      const LEGEND = 5;
      const rName = this.getName(name);
      const last = rName.slice(0, 5);
      const other = last.replace(/[\u4E00-\u9FA5]/g, "");
      return (
        last.padEnd(LEGEND, "　") +
        Array(other.length)
          .fill(" ")
          .join("")
      );
    },
    getFormatterName(name, data) {
      const percent =
        data.sum === 0
          ? "0%"
          : ((data[name] / data.sum) * 100).toFixed(2) + "%";
      return "{a|" + this.getName(name).slice(0, 4) + "}{b|" + percent + " }";
    },
    getTooltip(params, chartMap, title) {
      return (
        title +
        "<br/>" +
        this.getName(params.name) +
        ": " +
        chartMap[params.name] +
        " (" +
        (chartMap.sum === 0
          ? "0%"
          : ((chartMap[params.name] / chartMap.sum) * 100).toFixed(2) + "%") +
        ")"
      );
    },
    setCard(data) {
      if (data.length > 8) {
        this.cards = data
          .map((item, index) => {
            item.id = index;
            return item;
          })
          .slice(0, 7);
        this.lasts = data.slice(7);
        const last = {
          id: 7,
          failNum: 0,
          nodeName: this.$t_common("other"),
          nodeStatus: "other",
          notValidatedNum: 0,
          successNum: 0,
          sumNum: 0,
          warningNum: 0
        };
        this.lasts.reduce((pre, next) => {
          last.failNum += next.failNum;
          last.notValidatedNum += next.notValidatedNum;
          last.successNum += next.successNum;
          last.sumNum += next.sumNum;
          last.warningNum += next.warningNum;
        }, last);
        this.cards.push(last);
      } else {
        this.cards = data.map((item, index) => {
          item.id = index;
          return item;
        });
        this.lasts = [];
      }
    },
    setCheckData(checkMap) {
      const checkData = [];
      const checkMapText = {
        notValidatedNum: this.$t_process("not_validated"),
        successNum: this.$t_process("successful"),
        failNum: this.$t_common("failed_n"),
        warningNum: this.$t_common("warning")
      };
      const checkLegendMap = {
        [this.$t_process("not_validated")]: 0,
        [this.$t_process("successful")]: 0,
        [this.$t_common("failed_n")]: 0,
        [this.$t_common("warning")]: 0,
        sum: checkMap.sum
      };
      for (const key in checkMapText) {
        checkLegendMap[checkMapText[key]] = checkMap[key];
        checkData.push({
          value: checkMap[key],
          name: checkMapText[key]
        });
      }
      this.checkLegendMap = checkLegendMap;
      return checkData;
    },
    setChartData(data) {
      this._checkItemMap = {};
      const processLegendMap = { sum: 0 };
      const processData = [];
      const checkMap = {
        notValidatedNum: 0,
        successNum: 0,
        failNum: 0,
        warningNum: 0,
        sum: 0
      };
      (data || []).forEach(item => {
        checkMap.failNum += item.failNum;
        checkMap.successNum += item.successNum;
        checkMap.notValidatedNum += item.notValidatedNum;
        checkMap.warningNum += item.warningNum;
        checkMap.sum +=
          item.failNum +
          item.successNum +
          item.notValidatedNum +
          item.warningNum;
        processLegendMap.sum += item.sumNum;
        item.sum = item.sumNum;
        this._checkItemMap[item.id] = item;
        processLegendMap[item.nodeName + "-" + item.id] = item.sumNum;
        processData.push({
          id: item.id,
          value: item.sumNum,
          name: item.nodeName + "-" + item.id
        });
      });
      this._checkItemMap[""] = checkMap;
      this.processLegendMap = processLegendMap;
      this.processData = processData;
      this.checkData = this.setCheckData(checkMap);
    },
    handlerCheckLegend(info, checkInfo) {
      const selected = checkInfo ? checkInfo.name : "";
      const selects = info.selected;
      if (selected && !selects[selected]) {
        this.getProcessStatsItemList();
      }
    },
    handlerCardCLick(card) {
      if (this.activeId !== card.id) {
        this.activeStatus = {
          data: { ...card },
          name: card.nodeName + "-" + card.id
        };
      } else {
        this.activeStatus = null;
      }
    },
    handlerProcess(info) {
      // this.activeCheck = null;
      // this.handlerCheck();
      // this.checkData = this.setCheckData(this._checkItemMap[this.activeId]);
    },
    handlerCheck() {
      this.getProcessStatsItemList();
    },
    handlerClose() {
      this.$emit("update:visible", false);
    },
    setQueryId() {
      this._currentQueryId = _uniqueId("QueryId");
      return this._currentQueryId;
    },
    getQueryId() {
      return this._currentQueryId;
    }
  }
};
</script>
<style lang="less" scoped>
.drawerstatus {
  // font-family: PingFangSC-Medium;
  font-size: 0.875rem;
  z-index: 1040 !important;
}
/deep/ .ant-drawer-wrapper-body {
  display: flex;
  flex-direction: column;
  .ant-drawer-body {
    height: 0;
    flex: 1;
    overflow: auto;
  }
}
/deep/ .ant-drawer-body {
  padding-right: calc(1.5rem - 10px);
}
.state-spinning {
  height: 100%;
  /deep/ .ant-spin-container {
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
  }
}
.status-chart {
  .gutter-row {
    position: relative;
    height: 19.625rem;
  }
  .chart-title {
    height: 2.375rem;
    padding-top: 1rem;
    padding-left: 1.25rem;
    color: @yn-text-color;
    letter-spacing: 0;
    font-weight: 600;
    background: @yn-background-color;
    z-index: 2;
  }
  .chart {
    background: @yn-background-color;
    border-radius: 0.125rem;
    background-clip: content-box;
    height: calc(100% - 2.375rem);
  }

  .process {
    position: relative;
  }
  .check {
    position: relative;
  }
  .check-info,
  .process-info {
    position: absolute;
    left: 30%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .check-info-name,
  .process-info-name {
    height: 2rem;
    font-size: 1.5rem;
    color: @yn-text-color;
    text-align: center;
    line-height: 2rem;
    font-weight: 500;
  }
  .check-info-percent,
  .process-info-percent {
    height: 1.375rem;

    font-size: 0.875rem;
    color: @yn-text-color-secondary;
    text-align: center;
    line-height: 1.375rem;
    font-weight: 400;
  }
}
.card-groups {
  display: flex;
  margin: 0 -0.5rem 0.5rem;
  flex-wrap: wrap;
  .card {
    width: calc(25% - 1rem);
    height: 8.25rem;
    border-radius: 0.25rem;
    margin: 0.5rem 0.5rem;

    /deep/ .ant-card-body {
      display: flex;
      padding-left: 0.5rem;
    }
    /deep/ .ant-card-body {
      height: 100%;
    }
    &.active {
      border: 1px solid @yn-primary-color;
    }
    &.unstart {
      background: @yn-background-color;
      .summary-type::before {
        background: @yn-label-color;
      }
    }
    &.process {
      background: @yn-link-bg-color;
      .summary-type::before {
        background: @yn-primary-6;
      }
    }
    &.checking {
      background: @yn-warning-bg-color;
      .summary-type::before {
        background: @yn-warning-color;
      }
    }
    &.checked {
      background: @yn-success-bg-color;
      .summary-type::before {
        background: @yn-success-color;
      }
    }
    .divider {
      height: 100%;
      background: @yn-border-color-base;
    }
    .summary {
      width: 45%;
      position: relative;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .summary-type {
      position: relative;
      font-size: 0.875rem;
      color: @yn-text-color;
      text-align: left;
      font-weight: 400;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
      width: 100%;
      .summary-type-icon {
        display: inline-block;
        content: "";
        width: 0.5rem;
        height: 0.5rem;
        border-radius: 50%;
        margin-right: 0.5rem;
      }
    }
    .summary-num {
      font-family: HelveticaNeue;
      font-size: 1.75rem;
      color: @yn-text-color;
      letter-spacing: 0;
      font-weight: 400;
      position: absolute;
      left: 0.875rem;
      top: 40%;
      width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .detail {
      display: flex;
      flex-direction: column;
      width: 55%;
      justify-content: space-between;
    }
    .detail-type {
      display: flex;
      font-size: 0.875rem;
      color: @yn-text-color-secondary;
      letter-spacing: 0;
      font-weight: 400;
      justify-content: space-between;
    }
    .detail-num {
      font-family: HelveticaNeue-Medium;
      color: @yn-text-color;
      text-align: right;
      font-weight: 600;
    }
  }
}
.chart-instance {
  width: 100%;
  height: 100%;
}
.table-header,
.status-header {
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: @yn-text-color;
  letter-spacing: 0;
  font-weight: 600;
  .down-button {
    float: right;

    /deep/ .ynicon-button {
      color: @yn-label-color;
      padding-left: 0rem;
    }
    &:hover /deep/ .ynicon-button {
      color: @yn-primary-color;
    }
  }
}

.table-header {
  display: flex;
  margin-top: 1.5rem;
  align-items: center;
  .total {
    margin-left: auto;
    font-size: 0.875rem;
    font-weight: 400;
  }
  .total-num {
    color: @yn-primary-6;
    font-size: 0.875rem;
    font-weight: 400;
  }
}
</style>
