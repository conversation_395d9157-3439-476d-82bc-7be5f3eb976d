<template>
  <div :class="{ href: names }">
    <span v-if="!names" @click="openForm(href)">
      {{ (text || 0) | toFinance(2, true) }}
    </span>
    <yn-tooltip v-else placement="top">
      <template slot="title">
        <span>{{ names }}</span>
      </template>
      <span v-if="names.split(',').length < 2" @click="openForm(href)">
        {{ (text || 0) | toFinance(2, true) }}
      </span>
      <yn-dropdown v-else :trigger="['click']">
        <span>{{ (text || 0) | toFinance(2, true) }}</span>
        <yn-menu slot="overlay">
          <yn-menu-item
            v-for="item in href.split(';')"
            v-show="echoFormName(item)"
            :key="item"
            @click="openForm(item)"
          >
            {{ item ? echoFormName(item) : "" }}
          </yn-menu-item>
          <yn-menu-divider />
          <yn-menu-item key="3" @click="openForms(href.split(';'))">
            {{ $t_common("open") + $t_common("all") + $t_process("forms") }}
          </yn-menu-item>
        </yn-menu>
      </yn-dropdown>
    </yn-tooltip>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-tooltip/";
import commonService from "@/services/common";
import DIM_INFO from "@/constant/dimMapping";
const MAPPING_OBJ_KEY = ["Entity", "Scope", "SCOPE_G_NONE"];
const TAB_PATH = "/process/form";
const TAB_NAME = "processForm";
export default {
  props: {
    text: {
      type: [String, Number],
      default: 0
    },
    params: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    },
    href: {
      type: String,
      default: ""
    },
    pavingData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  computed: {
    names() {
      return this.echoFormNames(this.href);
    }
  },
  methods: {
    // 获取到上面处理后的参数之后，如果打开的是表单 则需要再处理成如下结构
    //  表单页面维传参 结构：{dimId: memberId}
    generateCustomObj() {
      const obj = Object.create(null);
      Object.keys(this.params).forEach(key => {
        const k = key.slice(0, 1).toUpperCase() + key.slice(1);
        obj[DIM_INFO[k]] = this.params[key];
      });
      return encodeURIComponent(JSON.stringify(obj));
    },
    async openForms(list) {
      for (const item of list) {
        this.openForm(item);
      }
    },
    async getFormParam() {
      const [entityIdent, scopeIdent, scopeGNone] = MAPPING_OBJ_KEY;
      // 点击的单体组织、合并体组织
      const { dimCode, memberId, memberName } = this.data;
      if (dimCode === entityIdent) {
        // 单体组织、G_NONE
        return {
          [DIM_INFO[entityIdent]]: memberId,
          [DIM_INFO[scopeIdent]]: DIM_INFO[scopeGNone]
        };
      } else {
        // 合并体组织、合并体组织对应的组织信息
        const entityMemberId = await this.getEntityDimMemberInfoByName(
          memberName.v
        );
        return {
          [DIM_INFO[entityIdent]]: entityMemberId,
          [DIM_INFO[scopeIdent]]: memberId
        };
      }
    },
    async getEntityDimMemberInfoByName(name) {
      const { data } = await commonService("getDimInfoBycodeAndName", {
        dimCode: "Entity",
        memberName: name
      });
      return data && data.memberId;
    },
    async openForm(item) {
      if (!item) return;
      const id = this.getPermissionId(item);
      if (!id) return;
      // 打开新页签
      let pageDim = this.generateCustomObj();
      // 处理组织、合并组
      const entityAndScopeInfo = await this.getFormParam();
      pageDim = encodeURIComponent(
        JSON.stringify(
          Object.assign(
            {},
            entityAndScopeInfo,
            JSON.parse(decodeURIComponent(pageDim))
          )
        )
      );
      this.newtabMixin({
        id: id,
        title: this.echoFormName(id),
        uri: TAB_PATH,
        router: TAB_NAME, // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
        params: {
          from: "process",
          formIds: id,
          id: id,
          // 流程控制组织 id（table数据的 objectId）
          nodeId: this.data.objectId,
          customObj: pageDim
        }
      });
    },
    getPermissionId(item) {
      return item
        .split(";")
        .filter(key => this.pavingData.find(item => item.key === key))[0];
    },
    echoFormNames(arrStr) {
      return arrStr
        .split(";")
        .map(item => this.echoFormName(item))
        .filter(item => item)
        .join(",");
    },
    echoFormName(key) {
      const target = this.pavingData.find(item => item.key === key);
      return target ? target.title : "";
    }
  }
};
</script>

<style lang="less" scoped>
.href {
  display: inline-block;
  font-size: 0.875rem;
  font-weight: normal;
  text-align: right;
  letter-spacing: 0px;
  color: @yn-primary-color;
  cursor: pointer;
  height: 1.375rem;
  line-height: 1.375rem;
  &:hover {
    padding: 0px 0 0 2px;
    background-color: @yn-background-color-light;
  }
}
</style>
