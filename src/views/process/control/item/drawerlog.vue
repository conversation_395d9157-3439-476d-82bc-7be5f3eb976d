<template>
  <yn-drawer
    :title="$t_process('operation_history')"
    placement="right"
    :closable="true"
    width="42.5rem"
    :visible="visible"
    class="drawer-log"
    @close="onClose"
  >
    <div class="dataarea">
      <yn-table
        ref="mytable"
        rowKey="objectId"
        bordered
        :columns="columns"
        :loading="spinning"
        :data-source="tabledata"
        :scroll="{ y: scrollHeight }"
      >
        <span slot="operatorLog" slot-scope="text" :title="text">
          {{ text || "-" }}
        </span>
        <span slot="attachmentName" slot-scope="text" :title="text">
          <div v-if="text">
            <div
              v-for="item in text
                .split(',')
                .join('，,')
                .split(',')"
              :key="item"
            >
              {{ item }}
            </div>
          </div>
          <span v-else>-</span>
        </span>
      </yn-table>
    </div>
    <div class="pagination">
      <span class="pagination-total">{{
        $t_common("total_result_simple", [total])
      }}</span>
      <yn-pagination
        simple
        :current="current"
        :pageSize="size"
        :total="total"
        @change="search"
      />
    </div>
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-pagination/";
import { polling } from "@/utils/common";
import precessService from "@/services/process";
export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    data: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      current: 1,
      total: 50,
      size: 20,
      spinning: false,
      scrollHeight: false,
      tabledata: [],
      columns: [
        {
          title: this.$t_process("user"),
          dataIndex: "operatorName",
          key: "operatorName"
        },
        {
          title: this.$t_common("operation"),
          dataIndex: "operatorType",
          key: "operatorType",
          width: "4.375rem"
        },
        {
          title: this.$t_process("operation_log"),
          dataIndex: "operatorLog",
          key: "operatorLog",
          scopedSlots: {
            customRender: "operatorLog"
          },
          ellipsis: true
        },
        {
          title: this.$t_common("enclosure"),
          dataIndex: "attachmentName",
          key: "attachmentName",
          scopedSlots: {
            customRender: "attachmentName"
          }
        },
        {
          title: this.$t_common("operation_time"),
          dataIndex: "operatorDate",
          key: "operatorDate",
          width: "11.25rem"
        }
      ]
    };
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.current = 1;
          this.search();
        }
      }
    }
  },
  mounted() {
    this.search();
  },

  methods: {
    async getProcessLogList() {
      const {
        data: {
          data: { data, totalCount }
        }
      } = await precessService("getProcessLogList", {
        processId: this.data.processId,
        operatorType: 1,
        offset: (this.current - 1) * this.size,
        limit: this.size
      });
      this.total = totalCount;
      return (data || []).map((item, index) => {
        item.objectId = index;
        return item;
      });
    },
    async getOperationReportItem() {
      const {
        data: {
          data: { data, totalCount }
        }
      } = await precessService("getOperationReportItem", {
        processId: this.data.processId,
        flowTemplateContentId: this.data.flowTemplateContentId,
        offset: (this.current - 1) * this.size,
        limit: this.size
      });
      this.total = totalCount;
      return (data || []).map((item, index) => {
        item.objectId = index;
        return item;
      });
    },
    async search(page = this.current) {
      this.spinning = true;
      this.current = page;
      const data =
        this.data.from !== "report"
          ? await this.getProcessLogList()
          : await this.getOperationReportItem();
      this.tabledata = data;
      this.spinning = false;
      // this.setScrollHeight();
    },
    onClose() {
      this.$emit("update:visible", false);
    },
    // dom相关
    setScrollHeight() {
      polling(() => Promise.resolve(this.$el.querySelector), 100).then(
        query => {
          const dataarea = this.$el.querySelector(".dataarea");
          const header = 37;
          const redux = header;
          const that = this;
          setTimeout(() => {
            if (
              dataarea.offsetHeight <=
              that.$refs.mytable.$el.offsetHeight + 3
            ) {
              this.scrollHeight = dataarea.offsetHeight - redux;
            } else {
              this.scrollHeight = false;
            }
          }, 0);
        }
      );
    }
  }
};
</script>

<style lang="less" scoped>
.drawer-log {
  z-index: 1040 !important;
}
.pagination {
  height: 2.75rem;
  display: flex;
  align-items: center;
}
.pagination-total {
  margin-right: 0.5rem;
  margin-left: auto;
}
.dataarea {
  height: 0;
  flex: 1;
  overflow-x: hidden;
  overflow-y: scroll;
}
/deep/ .ant-drawer-wrapper-body {
  display: flex;
  flex-direction: column;
  .ant-drawer-body {
    display: flex;
    flex-direction: column;
    height: 0;
    flex: 1;
    overflow: auto;
  }
  .ant-spin-container {
    display: flex;
    flex-direction: column;
  }
}
</style>
