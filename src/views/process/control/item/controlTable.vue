<template>
  <div class="wrap">
    <TreeGrid
      ref="reportGrid"
      :fixedColumns="flexed"
      :ident="$data.$treeGridIdent"
      :columns="columns"
      :hasMore="false"
      rowKey="objectId"
      :sourceMap="mapData"
      :selection="isBatch ? selection : false"
      :filterObject="filterObject"
      :dataSource="gridTableData"
      :paginationInfo="pagination"
      :expandedRowKeys="expandedRowKeys"
      :showTreeSelect="true"
      :loadMore="loadMore"
      :selectable="selectable"
      :getCustomSelect="getCustomSelect"
      :renderHeaderCell="renderHeaderCell"
      :cellNode="renderBodyCell"
      v-on="$listeners"
      @selectChange="handlerSelect"
      @onCheckAllChange="handlerSelectAll"
      @expand="expand"
    />
    <div class="model" :style="{ display: loading ? 'block' : 'none' }"></div>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-icon-button/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-popover/";
import commonService from "@/services/common";
import { icon, button } from "../jdom";
import { genDOM, genVDOM, genInstance } from "../jdom/utils";
import TreeGrid from "@/components/hoc/treeGrid";
import SearchGrid from "../components/searchgrid.vue";
import _debounce from "lodash.debounce";
// import _throttle from "lodash.debounce";
import { actions } from "../constants/action";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import { mapMutations, mapState } from "vuex";
import { isType } from "@/utils/common";
import lang from "@/mixin/lang";
const { $t_common, $t_process } = lang;

const STATUSCLASS = Object.freeze({
  not_started: "unstart",
  processing: "processing",
  submitted: "posted",
  pending_review: "onaccept",
  "": "none"
});

const TIME_CLASS = Object.freeze({
  0: "no_time",
  1: "over_time",
  2: "one_day",
  3: "more_day"
});

const BATCH_COLUMN = {
  title: $t_process("phased_status"),
  isFilter: true,
  dataIndex: "batchStatus",
  key: "batchStatus",
  cellType: "text",
  scopedSlots: {
    customRender: "batchStatus"
  }
};
const TIME_COLUMN = {
  title: $t_process("task_countdown_remaining"),
  dataIndex: "processCountdown",
  key: "processCountdown",
  cellType: "text",
  scopedSlots: {
    customRender: "processCountdown"
  }
};
const ALL_COLUMNS = [
  {
    fixed: "left",
    title: $t_process("organization_structure"),
    dataIndex: "memberName",
    key: "memberName",
    cellType: "text",
    width: 400,
    scopedSlots: {
      customRender: "memberName",
      title: "titleMemberName"
    }
  },
  {
    title: $t_process("process_status"),
    dataIndex: "nodeStatus",
    isFilter: true,
    key: "nodeStatus",
    cellType: "text",
    scopedSlots: {
      customRender: "nodeStatus"
    }
  },
  {
    ...BATCH_COLUMN
  },
  {
    title: $t_process("validation_status"),
    isFilter: true,
    dataIndex: "checkState",
    key: "checkState",
    cellType: "text",
    scopedSlots: {
      customRender: "checkState"
    }
  },
  {
    ...TIME_COLUMN
  },
  {
    title: $t_common("enclosure"),
    dataIndex: "attachmentNum",
    key: "attachmentNum",
    cellType: "text"
  },
  {
    title: $t_process("operation_history"),
    dataIndex: "operatorNum",
    key: "operatorNum",
    cellType: "text"
  },
  {
    title: $t_common("operation"),
    key: "action",
    dataIndex: "action",
    cellType: "text",
    width: 250
  }
];

export default {
  components: { TreeGrid },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    hasBatch: {
      type: Boolean,
      default: false
    },
    hasCountdown: {
      type: Boolean,
      default: false
    },
    part: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      CHECKSTATUS: Object.freeze({
        0: this.$t_process("not_validated"),
        1: this.$t_process("successful"),
        2: this.$t_common("failed_n"),
        3: this.$t_common("warning")
      }),
      CHECKSTATUSCLASS: Object.freeze({
        0: "uncheck",
        1: "pass",
        2: "unpass",
        3: "warning"
      }),
      SORT: {
        fillReport: 0,
        merge: 1,
        converted: 2,
        caculate: 3,
        submit: 4,
        audit: 5,
        start: 6,
        turndown: 7
      },
      dropDown: null,
      timerMap: {},
      renderMap: {},
      mapData: {},
      rowMap: {},
      selection: [],
      $treeGridIdent: "processScope",
      loading: false,
      flexed: { right: 1, left: 1 },
      asyncMap: {},
      isError: false,
      relationMap: {},
      absoluteMap: {},
      columns: [...ALL_COLUMNS],
      columnsft: {
        group: "Name"
      },
      arrData: [],
      pagination: {
        total: 0,
        current: 1,
        pageSize: 100000,
        hasMore: false,
        offset: 0
      },
      $Collect: {
        event: []
      },
      relation: {},
      gridTableData: [],
      expandedRowKeys: []
    };
  },
  computed: {
    ...mapState({
      isBatch: state => !!state.processStore.batchType,
      batchType: state => state.processStore.batchType,
      nodeNameSummary: state => state.processStore.nodeNameSummary,
      checkStateSummary: state => state.processStore.checkStateSummary,
      auditTableFilter: state => state.processStore.auditTableFilter,
      tableFilter: state => state.processStore.tableFilter
    }),
    actionCol() {
      return this.columns.findIndex(item => item.dataIndex === "action");
    },
    fileOperateCol() {
      return this.columns.findIndex(item => item.dataIndex === "attachmentNum");
    },
    timeColIndex() {
      return (
        this.columns.findIndex(item => item.dataIndex === "checkState") + 1
      );
    },
    batchColIndex() {
      return (
        this.columns.findIndex(item => item.dataIndex === "nodeStatus") + 1
      );
    },
    batchStateSummary() {
      return [
        ...new Set(
          this.arrData.map(item => item.batchStatus.v).filter(item => item)
        )
      ].sort();
    },
    filterObject() {
      return {
        nodeStatus: {
          name: this.$t_process("process_status"),
          data: this.nodeNameSummary
        },
        checkState: {
          name: this.$t_process("validation_status"),
          data: this.checkStateSummary
        },
        batchStatus: {
          name: this.$t_process("phased_status"),
          data: this.batchStateSummary
        }
      };
    },
    field() {
      return ["parentId", ...this.columns.map(item => item.key)];
    }
  },
  watch: {
    fileOperateCol: {
      handler() {
        this.setFileOperateCol(this.fileOperateCol);
      },
      immediate: true
    },
    actionCol: {
      handler() {
        this.setActionCol(this.actionCol);
      },
      immediate: true
    },
    auditTableFilter(value) {
      if (value.nodeNameList) {
        const grid = this.$refs.reportGrid.$refs.grid;
        grid.filterCheckDimInfo.nodeStatus = value.nodeNameList;
        grid.$refs.nodeStatus[0].okEvent();
      }
    },
    isBatch(value, oldValue) {
      if (value && !oldValue) {
        this.columns.pop();
      } else if (!value && oldValue) {
        this.columns.push({
          title: this.$t_common("operation"),
          key: "action",
          dataIndex: "action",
          cellType: "text",
          width: 250
        });
      }
    },
    part(value) {
      this.relationMap = {};
      this.setRelationMap(value);
    },
    data: {
      handler(value) {
        this.$data.$Collect.event = [];
        this.asyncMap = {};
        this.relation = {};
        this.rowMap = {};
        this.timerMap = {};
        this.renderMap = {};
        this.setExpand(value);
        this.setColumn();
        this.getRelation(value);
        this.gridTableData = value;
        this.updateTable();
        this.$refs.reportGrid &&
          this.$refs.reportGrid.$refs.grid.resetSelection();
        this.searchBack();
        this.setDom();
      },
      immediate: true
    }
  },
  beforeCreate() {
    commonService("getLastSettingV", {
      key: "processScope", // 持股方
      tag: "userIndexName"
    }).then(res => {
      const { objectId, value } = res.data ? res.data.data : {};
      this.objectId = objectId;
      this.columnsft.group = value || "Name";
    });
  },
  beforeDestroy() {
    this._loadingVNode && this._loadingVNode.$destroy();
    this._loadingVNode = null;
    this._AttachmentVNode && this._AttachmentVNode.$destroy();
    this._AttachmentVNode = null;
    this._searchInstance && this._searchInstance.$destroy();
    this._searchInstance = null;
    this._lightText = null;
    this._lightMatched = null;
    this._lightAll = null;
    this._settingInstance && this._settingInstance.$destroy();
    this._settingInstance = null;
  },
  methods: {
    ...mapMutations("processStore", [
      "setSelection",
      "setTableFilter",
      "setActionCol",
      "setFileOperateCol"
    ]),
    updateTable() {
      this.$nextTick(() => {
        this.$refs.reportGrid && this.$refs.reportGrid.updateTable();
      });
    },
    clearFilter() {
      const grid = this.$refs.reportGrid.$refs.grid;
      this.setTableFilter({
        nodeNameList: [],
        checkStateList: [],
        batchStatusList: []
      });
      // 清空过滤条件
      grid.deleteFilteItemById("all");
    },
    loadMore(params) {
      const { searchObj = [] } = params;
      const filters = searchObj.reduce((pre, next) => {
        pre[next.type] = next.searchArr.map(item => item.id);
        return pre;
      }, {});
      this.setTableFilter({
        nodeNameList: filters.nodeStatus,
        checkStateList: filters.checkState,
        batchStatusList: filters.batchStatus
      });
      return Promise.resolve({
        hasMore: false,
        refresh: false,
        data: this.gridTableData
      });
    },
    getRelation(value) {
      for (const item of value) {
        this.relation[item.memberId] || (this.relation[item.memberId] = []);
        this.relation[item.memberId].push(item);
        if (item.children) {
          this.getRelation(item.children);
        }
      }
    },
    handlerSelect(selection) {
      this.selection = selection;
      this.setSelection(this.selection);
    },
    handlerSelectAll(isCheckAll) {},
    getCustomSelect(rowKey) {
      const data = this.mapData[rowKey];
      return this.relation[data.memberId].map(item => item.objectId);
    },
    selectable(data) {
      if (!data.hasAuth) return false;
      if (this.batchType.operatorCode === "submit") {
        return (
          (data.operatorList || []).some(item => {
            return (
              item.operatorAuth &&
              item.operatorCode === this.batchType.operatorCode
            );
          }) && this.isChildSubmit(data)
        );
      } else {
        return data.filter;
      }
    },
    getAllParent(data, arr = []) {
      const parent = this.mapData[data.parentId.v];
      if (parent) {
        arr.push(parent);
        this.getAllParent(parent, arr);
      }
      return arr;
    },
    isChildSubmit(data) {
      if (data.children) {
        return data.children.every(
          child =>
            child.nodeStatus.v === "submitted" && this.isChildSubmit(child)
        );
      } else {
        return true;
      }
    },
    searchBack() {
      // 搜索实例重置
      this._searchInstance &&
        this._searchInstance.$set(
          this._searchInstance,
          "tabledata",
          this.arrData
        );
    },
    setDom() {
      setTimeout(() => {
        this.$nextTick(() => {
          this.simpleGrid = this.$refs.reportGrid.$refs.grid.$refs.simpleGrid;
          this.table = this.$el.querySelector(".ht_master .wtHolder");
          this.model = this.$el.querySelector(".model");
        });
      }, 0);
    },
    renderHeaderCell(cellContext) {
      const { colName } = cellContext;
      switch (colName) {
        case "memberName":
          return this.renderMemberNameH(cellContext);
        default:
          return false;
      }
    },
    renderMemberNameH(cellContext) {
      const { value, td } = cellContext;
      const self = this;
      const childDiv = genDOM(`<div class="member-name-header"></div>`);
      const wordTag = document.createElement("span");
      wordTag.textContent = value;
      childDiv.appendChild(wordTag);
      this._settingInstance =
        this._settingInstance ||
        genInstance({
          template: `
          <yn-popover trigger="click" placement="topLeft">
            <template slot="content">
              <span class="popover-title">
                ${this.$t_common("organization")}
              </span><br />
              <yn-radio-group v-model="group" @change="changeColumn">
                <yn-radio value="Name">${this.$t_common("name")}</yn-radio>
                <yn-radio value="Code">${this.$t_process("id")}</yn-radio>
                <yn-radio value="CodeAndName">
                  ${this.$t_process("id")}+${this.$t_common("name")}
                </yn-radio>
              </yn-radio-group>
            </template>
            <svg-icon class="icon-file" type="icon-set-up" title="${this.$t_process(
    "settings"
  )}" />
          </yn-popover>
          `,
          data() {
            return {
              group: self.columnsft.group
            };
          },
          methods: {
            changeColumn: self.changeColumn
          }
        });
      const tablekey =
        this.columnsft.group === "Name"
          ? `{memberName.v}`
          : this.columnsft.group === "Code"
            ? `{dbCodeIndex}`
            : `{dbCodeIndex} {memberName.v}`;
      this._searchInstance =
        this._searchInstance ||
        genInstance({
          components: { SearchGrid },
          data() {
            return {
              popVisible: true,
              nexttimer: null,
              backTimer: null,
              tabledata: self.arrData,
              tablekey: tablekey,
              id: ""
            };
          },
          methods: {
            handlerClear(text, matched, all) {
              if (this.popVisible) {
                this.popVisible = false;
                self.lightMemberNameB(text, matched, all);
                this.$nextTick(() => {
                  self.updateTable();
                });
              }
            },
            highlight(text, matched, all, index) {
              self.lightMemberNameB(text, matched, all);
              this.scrollTo(matched, index);
            },
            scrollTo(matched, index, last) {
              self._lightMatched = matched;
              self.simpleGrid.scrollViewportTo({
                row: index + 1
              });
              clearTimeout(this.backTimer);
              clearTimeout(this.nexttimer);
              return setTimeout(() => {
                self.simpleGrid.setCell(index + 1, 0, {
                  custom_className: "bug",
                  noRealTime: false
                });
              }, 100);
            },
            goBack(matched, index, last) {
              this.backTimer = this.scrollTo(matched, index, last);
            },
            goNext(matched, index, last) {
              this.nexttimer = this.scrollTo(matched, index, last);
            },
            searchit() {
              this.popVisible = true;
            }
          },
          template: `
          <yn-popover :visible="popVisible" class="search-grid-popover" trigger="click" ${
  this.$options._scopeId
} placement="topLeft">
            <template slot="content">
              <search-grid
                :id="id"
                :data="tabledata"
                placeholder="${this.$t_process("search_content")}"
                :formatter="tablekey"
                @back="goBack"
                @next="goNext"
                @highlight="highlight"
                @clear="handlerClear"
              />
            </template>
            <svg-icon
              class="icon-file"
              type="icon-a-bianzu18"
              title="${this.$t_common("search")}"
              @click.native="searchit"
            />
          </yn-popover>
        `
        });
      this._expandInstance = this._expandInstance || genInstance({
        // 创建展开折叠图标
        template: `
          <svg-icon
            class="icon-file"
            :type="expandIconName"
            :title="expandAllTitle"
            @click.native="expandOrCloseAll"
          />
        `,
        data() {
          return {
            isExpandAll: true
          };
        },
        computed: {
          expandIconName() {
            return !this.isExpandAll ? "icon-zhankai" : "icon-zhedie";
          },
          expandAllTitle() {
            return !this.isExpandAll ? "全部展开" : "全部收起";
          }
        },
        methods: {
          expandOrCloseAll() {
            this.isExpandAll = !this.isExpandAll;
            self.toggleExpandAll(this.isExpandAll);
          }
        }
      });
      childDiv.appendChild(this._settingInstance.$el);
      childDiv.appendChild(this._searchInstance.$el);
      childDiv.appendChild(this._expandInstance.$el);
      td.appendChild(childDiv);
      return childDiv;
    },
    lightMemberNameB(text, matched, all) {
      this._lightText = text;
      this._lightMatched = matched;
      this._lightAll = all;
    },
    renderMemberNameB(cellContext, record) {
      const member =
        this.columnsft.group === "Name"
          ? record.memberName.v
          : this.columnsft.group === "Code"
            ? record.dbCodeIndex
            : record.dbCodeIndex + " " + record.memberName.v;
      const child = genDOM(`<div class="member-wrap"></div>`);
      const spandom = genDOM(
        `<span class="member-name" title=${member}>${member}</span>`
      );
      this.highlight(spandom);
      if (this._lightMatched) {
        if (record.objectId === this._lightMatched.objectId) {
          child.classList.add("group-matched-select");
        } else {
          child.classList.remove("group-matched-select");
        }
      }

      child.appendChild(spandom);
      const show = record.hasAuth && record.dimCode === "Scope";
      const icondom = icon("icon-c1_cr_analysis", {
        class: "icon-summary",
        isIconBtn: false
      });
      icondom.onmousedown = e => {
        this.handlerAction("handlerDrawer", record, "DrawerStatus");
        e.stopPropagation();
        // return false;
      };
      show && child.appendChild(icondom);
      child.onmousedown = e => {
        // e.stopPropagation();
        // return false;
      };
      return child;
    },
    renderAttachmentNumB(cellContext, record) {
      const { td, col, rowId } = cellContext;
      // const self = this;
      return this.asyncRender(cellContext, () => {
        const hasAuth = record.hasAuth ? "inline-block" : "none";
        let childDiv = "";
        if (!this.asyncMap[`${rowId}-${col}`].loading) {
          childDiv = button(
            [
              icon("icon-c1_cr_accessory", { isIconBtn: false }),
              record.attachmentNum.v || "-"
            ],
            {
              class: "operator-button",
              style: { display: hasAuth }
            }
          );
          childDiv.onmousedown = e => {
            this.handlerAction("handlerDrawer", record, "DrawerOther");
            // e.stopPropagation();
            // return false;
          };
        } else {
          // childDiv = this.getSingleInstance(
          //   {
          //     template: `<yn-button loading class="attach-button" style="border: none;">
          //                     {{name}}
          //                 </yn-button>`,
          //     data() {
          //       return {
          //         name: actions[self.asyncMap[`${rowId}-${col}`].action] || ""
          //       };
          //     }
          //   },
          //   {
          //     name: actions[this.asyncMap[`${rowId}-${col}`].action] || ""
          //   },
          //   "_AttachmentVNode"
          // );
          childDiv = genVDOM({
            template: `<yn-button loading class="attach-button" style="border: none;">
              ${actions[this.asyncMap[`${rowId}-${col}`].action]}
            </yn-button>`
          });
        }
        td.innerHTML = "";
        td.appendChild(childDiv);
      });
    },
    renderOperatorNumB(cellContext, record) {
      const { td } = cellContext;
      const hasAuth = record.hasAuth ? "inline-block" : "none";
      const childDiv = button(
        [
          icon("icon-clock-circle", { isIconBtn: false }),
          record.operatorNum.v || "-"
        ],
        {
          class: "operator-button",
          style: { display: hasAuth }
        }
      );
      childDiv.onmousedown = e => {
        this.handlerAction("handlerDrawer", record, "DrawerLog");
        // e.stopPropagation();
        // return false;
      };

      return this.asyncRender(cellContext, () => {
        td.innerHTML = "";
        td.appendChild(childDiv);
      });
    },
    renderActionB(cellContext, record) {
      const { td, col, rowId } = cellContext;
      const hasAuth = record.hasAuth ? "flex" : "none";
      const wrap = genDOM(
        `<div style="display: ${hasAuth};align-items: center;"></div>`
      );
      const self = this;
      const writeForm = record.writeForm;
      const operatorList = (record.operatorList || []).sort((a, b) => {
        return this.SORT[a.operatorCode] - this.SORT[b.operatorCode];
      });
      const inview = operatorList.slice(0, 3);
      const outview = operatorList.slice(3);
      let text = "";
      for (const item of inview) {
        text =
          item.operatorCode === "fillReport"
            ? writeForm
              ? this.$t_process("input")
              : this.$t_common("view")
            : item.operatorName;
        if (item.operatorAuth) {
          if (
            item.operatorCode !== "fillReport" &&
            this.asyncMap[`${rowId}-${col}`].loading
          ) {
            break;
          }
          const buttondom = button(text, { class: "action-button" });
          buttondom.onmousedown = e => {
            if (item.operatorCode === "fillReport") {
              this._searchInstance &&
                this._searchInstance.$set(
                  this._searchInstance,
                  "popVisible",
                  false
                );
            }
            this.handlerAction("handlerButton", record, item.operatorCode);
            // e.stopPropagation();
            // return false;
          };
          wrap.appendChild(buttondom);
        }
      }

      return this.asyncRender(cellContext, () => {
        if (
          outview &&
          outview.length > 0 &&
          outview.some(item => item.operatorAuth) &&
          !this.asyncMap[`${rowId}-${col}`].loading
        ) {
          wrap.appendChild(
            genVDOM({
              template: `
            <yn-dropdown
              placement="bottomRight"
              :trigger="['click']"
            >
              <yn-icon-button
                type="more"
                class="action-more"
                @click.stop
              />
              <yn-menu slot="overlay">
                <yn-menu-item
                  v-for="item in outview"
                  v-show="item.operatorAuth"
                  :key="item.operatorCode"
                  @click="handlerButton(record, item.operatorCode)"
                >
                  <span class="process__txtmodel">
                    {{ item.operatorName }}
                  </span>
                </yn-menu-item>
              </yn-menu>
            </yn-dropdown>
          `,
              data() {
                return {
                  outview,
                  record: record
                };
              },
              methods: {
                handlerButton(record, type) {
                  self.handlerAction("handlerButton", record, type);
                }
              }
            })
          );
        }
        if (this.asyncMap[`${rowId}-${col}`].loading) {
          // wrap.appendChild(
          //   this.getSingleInstance(
          //     {
          //       template: `<yn-button loading class="action-button" style="border: none;">
          //                     {{name}}
          //                 </yn-button>`,
          //       data() {
          //         return {
          //           name: actions[self.asyncMap[`${rowId}-${col}`].action] || ""
          //         };
          //       }
          //     },
          //     {
          //       name: actions[this.asyncMap[`${rowId}-${col}`].action] || ""
          //     },
          //     "_loadingVNode"
          //   )
          // );
          wrap.appendChild(
            genVDOM({
              template: `<yn-button loading class="action-button" style="border: none;">
              ${actions[this.asyncMap[`${rowId}-${col}`].action] || ""}
            </yn-button>`
            })
          );
        }
        td.innerHTML = "";
        td.appendChild(wrap);
      });
    },
    renderNodeStatusB(cellContext, record) {
      const { td } = cellContext;
      const hasAuth = record.hasAuth ? "inline-block" : "none";
      const childDiv = genDOM(`
        <span
          title="${record.nodeName}"
          class="${STATUSCLASS[record.nodeStatus.v]} custom-span"
          style="display: ${hasAuth}"
        >
          ${record.nodeName}
        </span>
      `);
      return this.asyncRender(cellContext, () => {
        td.innerHTML = "";
        td.appendChild(childDiv);
      });
    },
    renderProcessCountdownB(cellContext, record) {
      const { td } = cellContext;
      const childDiv = genDOM(`
        <span
          title="${record.processCountdown.v}"
          class="${TIME_CLASS[record.processCountdownType]} custom-span"
        >
          ${record.processCountdown.v || "-"}
        </span>
      `);
      return this.asyncRender(cellContext, () => {
        td.innerHTML = "";
        td.appendChild(childDiv);
      });
    },
    renderBatchStatusB(cellContext, record) {
      const { td } = cellContext;
      const hasAuth = record.hasAuth ? "inline-block" : "none";
      const childDiv = genDOM(`
        <span
          title="${record.batchStatus.v}"
          class="${this.getBatchClass(record)} custom-span"
          style="display: ${hasAuth}"
        >
          ${record.batchStatus.v}
        </span>
      `);
      return this.asyncRender(cellContext, () => {
        td.innerHTML = "";
        td.appendChild(childDiv);
      });
    },
    renderCheckStateB(cellContext, record) {
      const { td } = cellContext;
      const hasAuth = record.hasAuth ? "inline-block" : "none";
      const childDiv = genDOM(`
        <span class="${this.CHECKSTATUSCLASS[record.checkCode]}"
         style="display: ${hasAuth}">
            ${this.CHECKSTATUS[record.checkCode]}
        </span>
      `);
      childDiv.onmousedown = e => {
        this.handlerAction("handlerDrawer", record, "DrawerCheck");
        // e.stopPropagation();
        // return false;
      };
      return this.asyncRender(cellContext, () => {
        td.innerHTML = "";
        td.appendChild(childDiv);
      });
    },
    renderBodyCell(cellContext) {
      const { colName, rowId, row, col } = cellContext;
      const record = this.mapData[rowId] || this.arrData[row - 1];
      const name = colName || this.columns[col].key;
      this.rowMap[rowId] = row;
      // 记录操作列的cell position
      this.asyncMap[`${rowId}-${col}`] ||
        (this.asyncMap[`${rowId}-${col}`] = {});
      // td.onmousedown = e => {
      //   e.stopPropagation();
      //   return false;
      // };
      // return ""
      switch (name) {
        case "memberName":
          return this.renderMemberNameB(cellContext, record);
        case "attachmentNum":
          return this.renderAttachmentNumB(cellContext, record);
        case "operatorNum":
          return this.renderOperatorNumB(cellContext, record);
        case "nodeStatus":
          return this.renderNodeStatusB(cellContext, record);
        case "batchStatus":
          return this.renderBatchStatusB(cellContext, record);
        case "processCountdown":
          return this.renderProcessCountdownB(cellContext, record);
        case "checkState":
          return this.renderCheckStateB(cellContext, record);
        case "action":
          return this.renderActionB(cellContext, record);
        default:
          return "";
      }
    },
    getBatchClass(record) {
      const isSubmit =
        record.batchCode === "submitted"
          ? record.lastBatch
            ? "posted"
            : "processing"
          : "unstart";
      return record.hasBatch ? isSubmit : "";
    },
    getSingleInstance(tmp, data, props) {
      if (this[props]) {
        for (const key in data) {
          this[props][key] = data[key];
        }
      } else {
        this[props] = genInstance(tmp);
      }
      return this[props].$el.cloneNode(true);
    },
    asyncRender(cellContext, callback) {
      const { td, row, col } = cellContext;
      const id = row + "-" + col;
      clearTimeout(this.timerMap[id]);
      this.timerMap[id] = setTimeout(() => {
        this.renderMap[id] = Date.now();
        if (this.isInView(row)) {
          callback();
        }
      }, 0);

      return td.children[0] && td.children[0].nodeType !== 3
        ? td.children[0]
        : "-";
      // if (this.renderMap[id] && Date.now() - this.renderMap[id] < 300) {
      //   // 解决在同一个单元格内极端时间内触发了多次渲染问题，单位时间段只渲染一次
      //   this.renderMap[id] = Date.now();
      //   return td.children[0];
      // } else {
      //   // 方式1： 异步更新旧数据，防止阻塞, 更加高效，一般场景滚动更流畅，特殊场景存在过量卡顿的可能
      //   clearTimeout(this.timerMap[id]);
      //   this.timerMap[id] = setTimeout(() => {
      //     this.renderMap[id] = Date.now();
      //     if (this.isInView(row)) {
      //       callback();
      //     }
      //   }, 0);
      //   // 方式2： 异步更新旧数据，防止阻塞  通用场景更加流畅， 一般场景存在小幅延时，不存在过量卡顿的问题
      //   // this._runTask(() => {
      //   //   this.renderMap[id] = Date.now();
      //   //   if (this.isInView(row)) {
      //   //     callback();
      //   //   }
      //   // });

      //   // 默认返回旧的数据，防止空白
      //   return td.children[0] && td.children[0].nodeType !== 3
      //     ? td.children[0]
      //     : "-";
      // }
    },
    _runTask(task) {
      const start = Date.now();
      requestAnimationFrame(() => {
        const now = Date.now();
        if (now - start < 16.6) {
          task();
        } else {
          this._runTask(task);
        }
      });
    },
    // 单元格更新 loading： 单元格loading状态， action 单元格的操作类型
    updateCell({ col, loading, action, rowId }) {
      this.asyncMap[`${rowId}-${col}`] = { rowId, col, loading, action };
      this.rowMap[rowId] !== undefined &&
        this.simpleGrid.setCell(this.rowMap[rowId], col, {
          noRealTime: false
        });
    },
    updateRow(record, ...cellState) {
      // asyncMap 记录行信息
      cellState.forEach(item => {
        // item: { row, col, loading, action }
        this.asyncMap[`${record.objectId}-${item.col}`] = {
          rowId: record.objectId,
          col: item.col,
          loading: item.loading,
          action: item.action
        };
      });
      this.$nextTick(() => {
        // 是否需要更新子列（特殊规则）
        // const need = this.needUpdateChild(record);
        // 更新当前操作行及关联行
        this.updateRelation(record.memberId);
        // 更新子行（此处存在同一合并组同时在不同的父节点下）
        this.relationMap[record.memberId].forEach(item => {
          this.updateRowChild(item[1].children, this.rowMap[item[1].objectId]);
        });
        this.updateRowView();
      });
    },
    getRanger() {
      // 计算视区内的行始末
      const height = this.$el.offsetHeight;
      const scrollHeight = this.table.scrollTop;
      const fix = 20; // 误差修正
      const lineHeight = 35;
      const start = Math.round(scrollHeight / lineHeight) - fix;
      const end = start + Math.round(height / lineHeight) + fix;
      return { start, end };
    },
    isInView(row) {
      const { start, end } = this.getRanger();
      // 在视区内则更新
      if (start <= row && row <= end) {
        // 如果正在滚动或被强制停止，重新放入队列，下次更新
        return true;
      } else {
        return false;
      }
    },
    updateRelation(memberId) {
      const colNum = this.columns.length;
      this.relationMap[memberId].forEach(item => {
        // 属性更新，不引发全量视图更新
        this.updateRecordProp(item[1], item[0]);
        const inRow = this.rowMap[item[1].objectId];
        if (this.isInView(inRow)) {
          for (let i = 0; i < colNum; i++) {
            // 构造更新函数，放在队列，异步更新，便于随时打断更新操作
            const func = () => {
              if (this.isInView(inRow)) {
                this.simpleGrid.setCell(inRow, i, {
                  colName: this.columns[i].key,
                  id: `${inRow},${i}`,
                  noRealTime: false
                });
              }
            };
            this.$data.$Collect.event.push(func);
          }
        }
      });
    },
    updateChildrenRelation(memberId, parentRow) {
      const colNum = this.columns.length;
      this.relationMap[memberId].forEach(item => {
        // 属性更新，不引发全量视图更新
        this.updateRecordProp(item[1], item[0]);
        const inRow = this.rowMap[item[1].objectId];
        if (this.isInView(inRow)) {
          for (let i = 0; i < colNum; i++) {
            // 构造更新函数，放在队列，异步更新，便于随时打断更新操作
            const func = () => {
              if (this.isInView(inRow)) {
                this.simpleGrid.setCell(inRow, i, {
                  colName: this.columns[i].key,
                  id: `${inRow},${i}`,
                  noRealTime: false
                });
              }
            };
            setTimeout(() => {
              // 父项行在视图范围则不用手动更新
              if (!this.isInView(parentRow)) func();
            }, 0);
          }
        }
      });
      this.relationMap[memberId] = [];
    },
    needUpdateChild(record) {
      // 规则1
      // const row = this.rowMap[record.objectId];
      const action = "start";
      const state = this.asyncMap[`${record.objectId}-${this.actionCol}`];
      const isStart = state.loading === true && state.action === action;
      return isStart;
    },
    updateRowChild(children, row) {
      if (children && children.length) {
        children.forEach(child => {
          this.updateChildrenRelation(child.memberId, row);
          if (child.children) {
            this.updateRowChild(child.children, row);
          }
        });
      }
    },
    // 更新视图
    updateRowView: _debounce(function() {
      while (this.$data.$Collect.event.length) {
        const func = this.$data.$Collect.event.pop();
        this.$nextTick(() => func());
      }
    }, 300),
    getRecord(data, key, value) {
      for (const item of data) {
        if (item[key] === value) {
          return { ...item };
        }
        if (item.children) {
          const res = this.getRecord(item.children, key, value);
          if (res) return { ...res };
        }
      }
    },
    setRelationMap(value) {
      const mapPart = this.getMapData(value);
      for (const key in this.mapData) {
        const record = this.mapData[key];
        this.relationMap[record.memberId] =
          this.relationMap[record.memberId] || [];
        if (!mapPart[key]) {
          this.isError = true;
          UiUtils.errorMessage(
            this.$t_process("refresh_process_organizational")
          );
          return;
        }
        this.relationMap[record.memberId].push([mapPart[key], record]);
      }
      this.isError = false;
    },
    hasError() {
      if (this.isError) {
        UiUtils.errorMessage(this.$t_process("refresh_process_organizational"));
      }
      return this.isError;
    },
    handlerAction(type, ...info) {
      this.$emit("action", type, ...info);
    },
    expand(expandedKeys) {
      this.expandedRowKeys = expandedKeys;
    },
    modifyColWidth() {
      this.$nextTick(() => {
        const width = this.$el.querySelector(".process-table-contain")
          .clientWidth;
        const tbwidth = this.$el.querySelector(".wtHider").clientWidth;
        this.flexed =
          width >= tbwidth
            ? { left: 0, right: 0 }
            : this.$options.data().flexed;
      });
    },
    changeColumn(e) {
      this.columnsft.group = e.target.value;
      this._searchInstance.$set(this._searchInstance, "id", Math.random());
      const tablekey =
        this.columnsft.group === "Name"
          ? `{memberName.v}`
          : this.columnsft.group === "Code"
            ? `{dbCodeIndex}`
            : `{dbCodeIndex} {memberName.v}`;
      this._searchInstance.$set(this._searchInstance, "tablekey", tablekey);
      this.$nextTick(() => {
        this.updateTable();
      });
      commonService("saveOrUpdateUserSetting", {
        key: "processScope", // 持股方
        tag: "userIndexName",
        objectId: this.objectId,
        value: this.columnsft.group
      });
    },
    highlight(dom) {
      if (this._lightText) {
        const text = dom.textContent;
        var values = text.split(this._lightText);
        dom.innerHTML = values.join(
          `<span class="group-matched">${this._lightText}</span>`
        );
      }
    },
    setColumn() {
      const clearColumns = this.columns.filter(
        item =>
          item.dataIndex !== "batchStatus" &&
          item.dataIndex !== "processCountdown"
      );
      if (this.hasBatch) {
        clearColumns.splice(this.batchColIndex, 0, BATCH_COLUMN);
      }
      if (this.hasCountdown) {
        clearColumns.splice(this.timeColIndex, 0, TIME_COLUMN);
      }
      this.columns = clearColumns;
    },
    setExpand(data) {
      const expandedRowKeys = [];
      const mapData = {};
      const arrData = [];
      const parentKeys = [];
      this.setSafeTree(data, expandedRowKeys, mapData, arrData, parentKeys);
      this.expandedRowKeys = expandedRowKeys;
      this.mapData = mapData;
      this.arrData = arrData;
      this.parentKeys = parentKeys;
    },
    setPropsToObj(obj) {
      const { objectId, parentId } = obj;
      ALL_COLUMNS.forEach(column => {
        const isObject = isType(obj[column.dataIndex], "Object");
        obj[column.dataIndex] = {
          v: isObject ? obj[column.dataIndex].v : obj[column.dataIndex]
        };
      });
      obj.parentId = { v: parentId };
      obj.objectId = objectId;
      obj._grid = {};
    },
    updateRecordProp(record, newRow) {
      const jump = ["children", "parentId", "level"];
      for (const key in newRow) {
        if (jump.includes(key)) continue;
        if (this.field.includes(key)) {
          record[key].v = newRow[key];
        } else {
          record[key] = newRow[key];
        }
      }
      record.from = "";
    },
    getMapData(treeData) {
      const mapObj = {};
      const loop = data => {
        data.forEach(item => {
          const { children, objectId } = item;
          mapObj[objectId] = item;
          if (children && children.length > 0) {
            loop(children);
          }
        });
      };
      loop(treeData);
      return mapObj;
    },
    setSafeTree(
      tree,
      expandedRowKeys,
      map,
      array,
      parent,
      level = 0,
      parentId = "-1",
      next = { index: 0 }
    ) {
      if (tree) {
        for (const item of tree) {
          map[item.objectId] = item;
          item.parentId = parentId;
          item.level = level;
          // item.index = next.index;
          array[next.index] = item;
          next.index += 1;
          expandedRowKeys && expandedRowKeys.push(item.objectId);
          this.setPropsToObj(item);
          if (item.children && item.children.length > 0) {
            parent.push(item.objectId);
            this.setSafeTree(
              item.children,
              expandedRowKeys,
              map,
              array,
              parent,
              level + 1,
              item.objectId,
              next
            );
          } else {
            delete item.children;
          }
        }
      }
    },
    toggleExpandAll(isExpandAll) {
      if (isExpandAll) {
        // 展开所有节点
        const allKeys = [];
        this.getAllExpandableKeys(this.data, allKeys);
        this.expandedRowKeys = allKeys;
      } else {
        // 收起所有节点
        this.expandedRowKeys = [];
      }
    },
    getAllExpandableKeys(data, keys) {
      data.forEach(item => {
        if (item.children && item.children.length > 0) {
          keys.push(item.objectId);
          this.getAllExpandableKeys(item.children, keys);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.wrap {
  position: relative;
  height: 100%;
  .model {
    position: absolute;
    width: 100%;
    height: 100%;
    display: none;
    z-index: 1000;
    top: 0;
    opacity: 0.2;
  }
}

/deep/ .handsontable td.simple-cell {
  border-left-color: transparent !important;
  border-right-color: transparent !important;
}
/deep/ .simple-cell .custom-span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  &.no_time {
    color: @yn-text-color;
  }
  &.over_time {
    color: @yn-error-color;
  }
  &.one_day {
    color: @yn-warning-color;
  }
  &.more_day {
    color: @yn-text-color;
  }
}
/deep/ .htBorders {
  // display: none;
}
.search-grid-popover {
}
</style>
<style lang="less">
.jsx-message .message-title {
  color: @yn-text-color-secondary;
  text-align: left;
  font-weight: 400;
  margin-bottom: 1rem;
}
.jsx-message .message-lists {
  max-height: 25rem;
  overflow: auto;
}
.jsx-message .message-list {
  color: @yn-text-color;
  text-align: left;
  font-weight: 400;
  margin-bottom: 0.25rem;
}
.process-table-contain {
  .icon-summary {
    color: @yn-primary-color !important;
    &:hover {
      color: @yn-primary-color !important;
    }
  }
  .group-matched-select {
    .group-matched {
      background: @yn-primary-4;
    }
  }
  .group-matched {
    background: @yn-primary-2;
  }
  .member-wrap {
    width: 0;
    flex: 1;
    display: flex;
    align-items: center;
  }
  .member-name {
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .member-name-header {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .icon-action {
    color: @yn-primary-color;
    margin-left: -0.5rem;
    margin-right: 0;
  }
  .icon-file {
    height: 24px;
    padding: 0 4px;
    line-height: 24px;
    font-size: 16px;
    margin-left: 4px;
    .iconfont {
      top: 0;
      width: auto;
      height: auto;
    }
    .svg-icon {
      font-size: 16px;
    }
  }
  .operator-button {
    padding-left: 0.25rem;
    color: @yn-label-color;
  }
  .attach-button {
    display: flex;
    height: 16px;
    line-height: 16px;
    align-items: center;
    margin-left: 8px;
  }
  .action-button {
    display: flex;
    height: 16px;
    line-height: 16px;
    padding-left: 2px !important;
    padding-right: 2px;
    font-size: 14px;
    margin-right: 16px;
    align-items: center;
    color: @yn-primary-color;

    .ant-spin-spinning {
      margin-left: 0 !important;
      height: 32px;
    }
    .ant-spin-dot {
      background-size: 32px;
      width: 32px;
      height: 32px;
    }
  }
  .action-more {
    line-height: 24px;
    font-size: 16px;
    height: 24px;
    padding: 0 4px;
    margin-left: -8px;
    margin-right: 0;
    color: @yn-primary-color;
  }
  .attachmentNum,
  .operatorNum,
  .uncheck,
  .pass,
  .unpass,
  .warning {
    cursor: pointer;
  }
  .uncheck::before,
  .pass::before,
  .unpass::before,
  .warning::before,
  .unstart::before,
  .processing::before,
  .posted::before,
  .none::before,
  .onaccept::before {
    display: inline-block;
    content: "";
    width: 0.5rem;
    height: 0.5rem;
    margin-right: 0.5rem;
    border-radius: 50%;
  }
  .unstart::before {
    background: @yn-disabled-color;
  }
  .processing::before {
    background: @yn-primary-color;
  }
  .posted::before {
    background: @yn-success-color;
  }
  .onaccept::before {
    background: @yn-warning-color;
  }
  .uncheck {
    color: @yn-text-color;
  }
  .pass {
    color: @yn-success-color;
  }
  .unpass {
    color: @yn-error-color;
  }
  .warning {
    color: @yn-warning-color;
  }
  .uncheck::before {
    background: @yn-disabled-color;
  }
  .pass::before {
    background: @yn-success-color;
  }
  .unpass::before {
    background: @yn-error-color;
  }
  .warning::before {
    background: @yn-warning-color;
  }
}
.more-menu-button {
  height: auto;
  line-height: unset;
  vertical-align: baseline;
  &:hover {
    color: @yn-text-color !important;
    background: transparent !important;
  }
}

.process__popover-title {
  display: inline-block;
  font-size: 0.875rem;
  font-weight: 500;
  color: @yn-text-color;
  margin-bottom: 0.625rem;
}
.process__txtmodel {
  padding: 0 0.5rem;
  cursor: pointer;
}
</style>
