<template>
  <yn-drawer
    :title="$t_common('enclosure')"
    placement="right"
    :closable="true"
    width="47.5rem"
    class="drawerother"
    :visible="visible"
    @close="onClose"
  >
    <yn-button class="upload-button" @click="handlerUpload">
      <yn-icon type="upload" /> {{ $t_common("upload") }}
    </yn-button>
    <div class="dataarea">
      <yn-table
        ref="mytable"
        rowKey="objectId"
        bordered
        :columns="columns"
        :loading="spinning"
        :data-source="tabledata"
        :scroll="{ y: scrollHeight, x: false }"
      >
        <span slot="attachmentName" slot-scope="text" class="attachmentName">{{
          text
        }}</span>
        <span slot="operatorName" slot-scope="text" class="operatorName">{{
          text
        }}</span>
        <div slot="table.action" slot-scope="text, record">
          <span
            type="link"
            class="action-button yn-a-link"
            @click="handlerDown(record)"
          >
            {{ $t_common("download") }}
          </span>
          <span
            v-if="record.previewId"
            type="link"
            class="action-button yn-a-link"
            @click="handlerView(record)"
          >
            {{ $t_process("preview") }}
          </span>
          <span
            type="link"
            class="action-button action-last yn-a-link"
            @click="deleteProcessAttachment(record)"
          >
            {{ $t_common("delete") }}
          </span>
        </div>
      </yn-table>
    </div>
    <div class="pagination">
      <span class="pagination-total">{{
        $t_common("total_result_simple", [total])
      }}</span>
      <yn-pagination
        simple
        :current="current"
        :pageSize="size"
        :total="total"
        @change="search"
      />
    </div>
    <yn-modal
      :visible="uploadVisible"
      :title="$t_common('upload')"
      :okText="$t_process('submit')"
      :cancelText="$t_process('cancel')"
      width="35.1875rem"
      class="import-Journal-modal"
      @cancel="cancelEvent"
    >
      <yn-upload-dragger
        name="file"
        :multiple="true"
        :remove="handleRemove"
        :beforeUpload="beforeUpload"
        :fileList="fileList"
      >
        <p class="ant-upload-drag-icon">
          <yn-icon-svg type="Updating-files" />
        </p>
        <p class="ant-upload-text">{{ $t_process("drag_files_upload") }}</p>
        <p class="ant-upload-hint">
          {{ $t_common("support_only") }}： JPG/ PNG/ BMP/ PDF/ WORD/ EXCEL/
          TXT/ PPT/ ZIP/ RAR
        </p>
      </yn-upload-dragger>
      <template slot="footer">
        <yn-button key="back" @click="cancelEvent">
          {{ $t_common("cancel") }}
        </yn-button>
        <yn-button
          key="submit"
          type="primary"
          :loading="loading"
          :disabled="loading || fileList.length === 0"
          @click="okEvent"
        >
          {{ $t_common("ok") }}
        </yn-button>
      </template>
    </yn-modal>
    <PreviewFile :visible.sync="previewFileVisible" :fileId="fileId" />
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-pagination/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-upload-dragger/";
import { isFileType, polling } from "@/utils/common";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import precessService from "@/services/process";
import checkSize from "@/views/process/control/item/checkSize";
import PreviewFile from "@/views/journal/journalList/list/previewFile.vue";
import commonService from "@/services/common";

export default {
  components: { PreviewFile },
  mixins: [checkSize],
  props: {
    title: {
      type: String,
      default: ""
    },
    params: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      current: 1,
      total: 50,
      size: 20,
      previewFileVisible: false,
      fileId: "",
      spinning: false,
      scrollHeight: false,
      loading: false,
      uploadVisible: false,
      fileList: [],
      tabledata: [],
      columns: [
        {
          title: this.$t_process("file_name"),
          dataIndex: "attachmentName",
          key: "attachmentName",
          scopedSlots: {
            customRender: "attachmentName"
          }
        },
        {
          title: this.$t_process("upload_users"),
          dataIndex: "operatorName",
          key: "operatorName",
          width: "6.25rem",
          scopedSlots: {
            customRender: "operatorName"
          }
        },
        {
          title: this.$t_process("belong_operation"),
          dataIndex: "operatorType",
          key: "operatorType",
          width: "6.25rem"
        },
        {
          title: this.$t_common("operation_time"),
          dataIndex: "operatorDate",
          key: "operatorDate",
          width: "11.25rem"
        },
        {
          title: this.$t_common("operation"),
          dataIndex: "action",
          key: "action",
          width: "10.625rem",
          scopedSlots: {
            customRender: "action"
          }
        }
      ]
    };
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.current = 1;
          this.search();
        }
      }
    }
  },
  mounted() {
    this.search();
  },
  methods: {
    async deleteProcessAttachment(record) {
      this.spinning = true;
      precessService("deleteProcessAttachment", {
        attachmentId: record.attachmentId
      })
        .then(res => {
          UiUtils.successMessage(this.$t_process("deleted_attachment_success"));
          precessService("operationAttachment", {
            dimCode: this.data.dimCode,
            attachmentObjectId: record.attachmentObjectId,
            memberId: this.data.memberId,
            version: this.params.version,
            year: this.params.year,
            period: this.params.period,
            processId: this.data.processId,
            nowNodeId: this.data.nodeId,
            objectId: this.data.objectId,
            operatorType: 17,
            attachmentId: record.attachmentId
          });
          this.$emit("ok", "deleteAttachment", this.data);
          this.search();
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    async getProcessAttachmentList() {
      const {
        data: {
          data: { data, totalCount }
        }
      } = await precessService("getProcessAttachmentList", {
        processId: this.data.processId || this._processId,
        operatorType: 2,
        offset: (this.current - 1) * this.size,
        limit: this.size
      }).catch(e => (this.spinning = false));
      this.total = totalCount;
      return (data || []).map((item, index) => {
        item.objectId = index;
        return item;
      });
    },
    async search(page = this.current) {
      this.spinning = true;
      this.current = page;
      const data = await this.getProcessAttachmentList();
      this.tabledata = data;
      this.spinning = false;
      // this.setScrollHeight();
    },
    handlerView(record) {
      if (!record.previewId) {
        return;
      }
      this.fileId = record.previewId;
      this.previewFileVisible = true;
    },
    cancelEvent() {
      this.fileList = [];
      this.mixinOver = false;
      this.loading = false;
      this._processId = "";
      this.uploadVisible = false;
    },
    async uploadToPlatform() {
      const upMap = this.fileList.map(file => {
        const formData = new FormData();
        formData.append("uploadFile", file);
        return commonService("uploadAttachment", formData).then(res => {
          return res.data.data;
        });
      });
      return await Promise.all(upMap);
    },
    async okEvent() {
      if (this.mixinValidateOverSize(this.fileList)) return;
      this.loading = true;
      const filesRes = await this.uploadToPlatform().catch(_ => {
        this.loading = false;
      });
      const formData = new FormData();
      this.fileList.forEach(file => {
        formData.append("files", file);
      });
      formData.append(
        "previewIds",
        filesRes.map(item => item.objectId)
      );
      formData.append("processId", this.data.processId);
      formData.append("nowNodeId", this.data.nodeId);
      formData.append("version", this.params.version);
      formData.append("year", this.params.year);
      formData.append("period", this.params.period);
      formData.append("objectId", this.data.objectId);
      formData.append("memberId", this.data.memberId);
      formData.append("dimCode", this.data.dimCode);
      precessService("processAttachmentUpload", formData)
        .then(res => {
          const { data, success } = res.data;
          if (!success) {
            UiUtils.errorMessage(`${this.data.memberName.v}${data}`);
            return;
          }
          this._processId = data;
          this.data.processId = this._processId;
          this.cancelEvent();
          this.uploadVisible = false;
          this.fileList = [];
          this.search();
          this.$emit("ok", "import", this.data);
        })
        .finally(res => {
          this.loading = false;
        });
    },
    handlerUpload() {
      this.uploadVisible = true;
    },
    handlerDown(record) {
      precessService("downloadFile", record).then(res => {
        if (res.data) {
          this.$emit("ok", "downloadFile", this.data);
          precessService("operationAttachment", {
            attachmentObjectId: record.attachmentObjectId,
            dimCode: this.data.dimCode,
            memberId: this.data.memberId,
            version: this.params.version,
            year: this.params.year,
            period: this.params.period,
            processId: this.data.processId,
            nowNodeId: this.data.nodeId,
            objectId: this.data.objectId,
            operatorType: 18,
            attachmentId: record.attachmentId
          });
          this.readBlobDown(res.data, record.attachmentName);
        }
      });
    },
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      const newFileList = this.fileList.slice();
      newFileList.splice(index, 1);
      this.mixinIsOver50m(newFileList);
      this.fileList = newFileList;
    },
    beforeUpload(file) {
      // JPG/ PNG/ BMP/ PDF/ WORD/ EXCEL/ TXT/ PPT/ ZIP/ RAR
      if (
        !isFileType(
          file.name,
          /(xlsx|xls|jpg|png|bmp|pdf|docx|doc|ppt|txt|pptx|zip|rar)$/
        )
      ) {
        UiUtils.errorMessage(this.$t_common("upload_correct_format"));
        return false;
      }
      if (file.size === 0) {
        UiUtils.errorMessage(this.$t_common("unable_upload_empty"));
        return false;
      }
      if (this.mixinValidateOverSize([...this.fileList, file], false)) {
        return false;
      }
      this.fileList = [...this.fileList, file];
      return false;
    },
    onClose() {
      this.$emit("update:visible", false);
    },
    /**
     * 读取下载的文件流
     */
    readBlobDown(result, filename) {
      var blob = result; // ie 使用
      if (window.navigator.msSaveBlob) {
        // for ie 10 and later
        try {
          var blobObject = new Blob([blob], {
            type: "application/vnd.ms-excel"
          }); // 构造一个blob对象来处理数据
          window.navigator.msSaveBlob(blobObject, filename);
        } catch (e) {}
      } else {
        // 其他浏览器 下载方式
        var reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onload = function(e) {
          // 转换完成，创建一个a标签用于下载
          var a = document.createElement("a");
          a.download = filename; // 设置下载的文件名称
          a.href = e.target.result;
          a.click();
        };
      }
    },
    // dom相关
    setScrollHeight() {
      const mytable = this.$refs.mytable;
      polling(() => Promise.resolve(this.$el.querySelector), 100).then(
        query => {
          const dataarea = this.$el.querySelector(".dataarea");
          const header = 37;
          const redux = header;
          setTimeout(() => {
            if (dataarea.offsetHeight <= mytable.$el.offsetHeight + 3) {
              this.scrollHeight = dataarea.offsetHeight - redux;
            } else {
              this.scrollHeight = false;
            }
          }, 100);
        }
      );
    }
  }
};
</script>

<style lang="less" scoped>
.drawerother {
  font-family: PingFangSC-Regular;
  font-size: 0.875rem;
  z-index: 1040 !important;
}
.import-Journal-modal {
  /deep/ .ant-modal-mask {
    z-index: 1055 !important;
  }
  /deep/ .ant-modal-wrap {
    z-index: 1055 !important;
  }
}
/deep/ .ant-drawer-wrapper-body {
  display: flex;
  flex-direction: column;
  .ant-drawer-body {
    display: flex;
    flex-direction: column;
    height: 0;
    flex: 1;
    overflow-y: hidden;
    overflow-x: hidden;
  }
  .ant-spin-container {
    display: flex;
    flex-direction: column;
  }
}
.dataarea {
  height: 0;
  flex: 1;
  overflow-x: hidden;
  overflow-y: scroll;
  margin-right: -1rem;
  .attachmentName {
    white-space: pre-wrap;
    word-break: break-all;
  }
  .operatorName {
    width: 6rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
}

.upload {
  width: 5.125rem;
  height: 2rem;
  margin-bottom: 1rem;
}
.pagination {
  height: 2.75rem;
  display: flex;
  align-items: center;
  z-index: 10;
  background: @yn-body-background;
}
.pagination-total {
  margin-right: 0.5rem;
  margin-left: auto;
}
.action-button {
  margin-right: 1rem;
  color: @yn-primary-color;
  &.action-last {
    margin-right: 0;
  }
  /deep/ i {
    color: @yn-primary-color !important;
  }
}
.upload-button {
  width: 5.125rem;
  margin-bottom: 1rem;
}
/deep/ .ant-upload-list {
  //max-height: 25rem;
  //overflow-x: hidden;
  //overflow-y: auto;
}
.ant-upload-hint {
  color: @yn-disabled-color !important;
  font-weight: 400;
}
</style>
