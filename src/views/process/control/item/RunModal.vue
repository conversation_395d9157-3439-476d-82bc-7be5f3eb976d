<template>
  <yn-modal
    width="32.5rem"
    :title="$t_process('run_script')"
    :visible="runModalVisible"
    :confirmLoading="confirmLoading"
    @ok="handleRun"
    @cancel="handleCancel"
  >
    <yn-spin size="large" :spinning="spinLoading">
      <yn-form
        hideRequiredMark
        :form="form"
        layout="horizontal"
        class="custom-form"
      >
        <div
          v-for="(script, sIndex) in scriptsData"
          :key="script.objectId"
          class="script-part"
        >
          <yn-divider v-show="sIndex !== 0" />
          <div class="script-part-top">
            <div>
              <div class="blue-icon"></div>
            </div>
            <div class="script-name">
              <!-- <div class="blue-icon"></div> -->
              {{ script.scriptRecordName }}
              <!-- <span class="script-name-text">
              </span
              > -->
            </div>

            <div
              v-if="script.hasDefalutParams"
              class="default-Show-btn "
              @click="onChangeDefaultShow(script, sIndex)"
            >
              <span v-show="!script.defaultShow" class="yn-a-link"><yn-icon-svg type="eye" />
                {{ $t_process("display_default_options") }}
              </span>
              <span v-show="script.defaultShow" class="yn-a-link"><yn-icon-svg type="eye-invisible" />{{
                $t_process("hide_default_options")
              }}</span>
            </div>
          </div>

          <div v-show="script.promptBeforeRunning && true" class="msg">
            {{ script.promptBeforeRunning }}
          </div>
          <div
            v-show="Array.isArray(script.scriptParams) && script.scriptParams"
            class="params-part"
          >
            <div
              v-for="(params, pIndex) in script.scriptParams"
              :key="`${script.objectId}_${pIndex}`"
              class="params"
            >
              <yn-form-item
                v-show="checkDefaultShow(script.defaultShow, params)"
              >
                <span slot="label" class="custom-label">
                  {{ params.paramName }}
                </span>
                <!-- 参数类型：执行人 -->
                <yn-input
                  v-if="params.paramType === 'USER'"
                  disabled
                  :value="`${userInfo.userName}(${userInfo.userLoginName})`"
                />
                <!-- 参数类型：文本 -->
                <yn-input
                  v-else-if="params.paramType === 'STRING'"
                  v-decorator="[
                    `paramsValue_${script.objectId}_${pIndex}`,
                    {
                      initialValue: params.paramValueText,
                      rules: [
                        {
                          required: true,
                          whitespace: true,
                          message: $t_process(
                            'please_fill_in_script_information'
                          )
                        },
                        {
                          max: 100,
                          message: $t_process('maximum_100_characters')
                        }
                      ]
                    }
                  ]"
                  maxlength="100"
                  :placeholder="$t_common('input_message')"
                  :disabled="params.userDefaultValue"
                  @change="
                    e =>
                      onChangeParamsValue(
                        e.target.value,
                        params,
                        pIndex,
                        script,
                        sIndex
                      )
                  "
                />
                <!-- 参数类型：沙箱 -->
                <yn-select
                  v-else-if="params.paramType === 'SANDBOX'"
                  v-decorator="[
                    `paramsValue_${script.objectId}_${pIndex}`,
                    {
                      initialValue: 'base',
                      rules: [
                        {
                          required: true,
                          message: $t_common('input_select')
                        }
                      ]
                    }
                  ]"
                  :disabled="params.userDefaultValue"
                  :allowClear="false"
                  maxlength="100"
                  :placeholder="$t_common('input_message')"
                  @change="
                    (value, val) =>
                      onChangeParamsValue(
                        value,
                        params,
                        pIndex,
                        script,
                        sIndex,
                        val
                      )
                  "
                >
                  <yn-select-option
                    v-for="item in sandBoxList"
                    :key="item.sandboxDbCode"
                    :value="item.sandboxDbCode"
                    :text="item.sandboxName"
                  >
                    {{ item.sandboxName }}
                  </yn-select-option>
                </yn-select>
                <!-- 参数类型：枚举/关联有成员表达式 -->
                <yn-select-tree
                  v-else-if="
                    (params.paramType === 'RELATION' && params.dropDownExps) ||
                      params.paramType === 'ENUM'
                  "
                  :ref="`paramsValue_${script.objectId}_${pIndex}`"
                  v-decorator="[
                    `paramsValue_${script.objectId}_${pIndex}`,
                    {
                      initialValue: setInitialValue(params),
                      rules: [
                        {
                          required: true,
                          message: params.userDefaultValue
                            ? $t_process('contact_the_administrator')
                            : $t_process('please_select_script_information')
                        }
                      ]
                    }
                  ]"
                  :loading="selectTreeLoading"
                  searchMode="multiple"
                  :allowClear="false"
                  :treeCheckStrictly="true"
                  :multiple="params.paramValueChoiceType === 'multiple'"
                  :disabled="params.userDefaultValue"
                  :selectedItems="setSelectedItems(params)"
                  :datasource="
                    params.paramType === 'ENUM'
                      ? params.dropDownExps
                        ? JSON.parse(params.dropDownExps)
                        : []
                      : allDimMemberSourceData[params.objectId] || []
                  "
                  nonleafselectable
                  @change="
                    (value, val) =>
                      onChangeParamsValue(
                        value,
                        params,
                        pIndex,
                        script,
                        sIndex,
                        val
                      )
                  "
                  @dropdownVisibleChange="
                    value =>
                      onOpenDimMemberSelect(value, {
                        ...params,
                        paramDefaultValue:
                          scriptParamsValueData[sIndex].scriptParam[pIndex]
                            .paramDefaultValue
                      })
                  "
                >
                  <template slot="title" slot-scope="item">
                    <span><yn-icon-svg
                      v-if="
                        item.dimMemberShared === 'TRUE' ||
                          item.dimMemberShared === true
                      "
                      style="color: #8894a8;margin-right:0.25rem;"
                      type="copy"
                    />{{ item.label }}</span>
                  </template>
                </yn-select-tree>
                <!-- 参数类型：关联-无成员表达式：全量成员 -->
                <yn-select-tree
                  v-else
                  v-decorator="[
                    `paramsValue_${script.objectId}_${pIndex}`,
                    {
                      initialValue: setInitialValue(params),
                      rules: [
                        {
                          required: true,
                          message: params.userDefaultValue
                            ? $t_process('contact_the_administrator')
                            : $t_process('please_select_script_information')
                        }
                      ]
                    }
                  ]"
                  :allowClear="false"
                  :treeCheckStrictly="true"
                  labelInValue
                  nonleafselectable
                  searchMode="custom"
                  :loading="selectTreeLoading"
                  :itemexpandable="checkExpandableLeaf"
                  :datasource="allDimMemberSourceData[params.objectId] || []"
                  :disabled="params.userDefaultValue"
                  :customloader="
                    e => customTreeloaderDims(e, params, pIndex, script, sIndex)
                  "
                  :selectedItems="setSelectedItems(params)"
                  :multiple="params.paramValueChoiceType === 'multiple'"
                  @dropdownVisibleChange="
                    value =>
                      onOpenDimMemberSelect(value, {
                        ...params,
                        paramDefaultValue:
                          scriptParamsValueData[sIndex].scriptParam[pIndex]
                            .paramDefaultValue
                      })
                  "
                  @customSearch="
                    value => onCustomeSearchDimMember(value, params)
                  "
                  @change="
                    (value, val) =>
                      onChangeParamsValue(
                        value,
                        params,
                        pIndex,
                        script,
                        sIndex,
                        val
                      )
                  "
                >
                  <template slot="value" slot-scope="item">
                    <yn-tooltip
                      visibleOnOverflow
                      :title="item.label"
                      overlayClassName="paramsTypeTitleTip"
                      :getCalendarContainer="
                        triggerNode => triggerNode.parentNode
                      "
                    >
                      <div class="line-ellipsis view-text">
                        {{ item.label }}
                      </div>
                    </yn-tooltip>
                  </template>
                  <template slot="title" slot-scope="item">
                    <span><yn-icon-svg
                      v-if="
                        item.dimMemberShared === 'TRUE' ||
                          item.dimMemberShared === true
                      "
                      style="color: #8894a8;margin-right:0.25rem;"
                      type="copy"
                    />{{ item.label }}</span>
                  </template>
                </yn-select-tree>
              </yn-form-item>
            </div>
          </div>
        </div>
      </yn-form>
    </yn-spin>
  </yn-modal>
</template>
<script>
import { mapActions } from "vuex";
import YnUiUtils from "yn-p1/libs/utils/UiUtils";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { APPS } from "@/config/SETUP";
import {
  changeTreeFieldName,
  changeTreeFieldValue
} from "@/utils/treeUtils.js";

export default {
  data() {
    return {
      form: this.$form.createForm(this),
      execParams: {},
      execParamsLabel: {},
      spinLoading: false,
      runModalVisible: false,
      confirmLoading: false,
      scriptsData: null, // 脚本数据
      scriptParamsValueData: [],
      dimMemberDataSource: [], // 维度成员
      allDimMemberSourceData: {}, // 所有关联相关-成员选择项
      selectTreeLoading: false,
      sandBoxList: [], // 沙箱list
      userInfo: {} // 用户信息
    };
  },
  mounted() {
    this.userInfo = DsUtils.getSessionStorageItem("currentUserInfo", {
      storagePrefix: APPS.NAME,
      isJson: true
    });
  },
  methods: {
    async showExecuteModal(scriptIds, execParams = {}, execParamsLabel = {}) {
      this.runModalVisible = true;
      this.execParams = execParams;
      this.execParamsLabel = execParamsLabel;
      this.spinLoading = true;
      this.scriptsData = [];
      const queryParamsPromise = [];
      // 批量查询勾选执行的脚本详细信息
      scriptIds.forEach(s => {
        queryParamsPromise.push(this.queryParamsData(s));
      });
      this.scriptsData = await Promise.all(queryParamsPromise);
      this.initValueData();
    },
    // 查询参数详情
    queryParamsData(id) {
      return new Promise((resolve, reject) => {
        this.getScriptItem({ id }).then(res => {
          resolve(res ? res.data : {});
        });
      });
    },
    // 初始化脚本参数数据
    initValueData() {
      this.scriptParamsValueData = [];
      const paramsList = [];
      this.allDimMemberSourceData = {};
      this.execParams;
      this.scriptsData.forEach(script => {
        this.$set(script, "isChecked", true);
        this.$set(script, "defaultShow", false);

        const newData = {
          recordId: script.objectId,
          scriptRecordName: script.scriptRecordName,
          isChecked: true,
          scriptParam: []
        };
        let scriptParams = script ? script.scriptParams : [];
        let hasDefalutParams = false;
        scriptParams = scriptParams.sort(function(a, b) {
          // 按参数顺序升序排列
          return a.paramOrder - b.paramOrder;
        });

        scriptParams.forEach(p => {
          const {
            paramType,
            paramDefaultValue,
            paramValueChoiceType,
            paramValueText = {},
            paramValueRunText = paramValueText,
            paramName,
            userDefaultValue = false,
            relationDimId,
            objectId
          } = p;
          const userDefaultValueFLag = userDefaultValue
            ? userDefaultValue.toString().toUpperCase() === "TRUE"
            : false;
          p.userDefaultValue = userDefaultValueFLag;
          if (userDefaultValueFLag) {
            hasDefalutParams = userDefaultValueFLag;
          }
          // 下面开始处理参数值
          const { userLoginName } = this.userInfo;
          let newValue = paramDefaultValue || [];
          switch (paramType) {
            case "USER":
              newValue = [userLoginName];
              hasDefalutParams = true;
              break;
            case "SANDBOX":
              newValue = ["base"];
              break;
            case "STRING":
              newValue = [paramValueText];
              break;
            case "RELATION":
              newValue = Object.keys(paramValueRunText);
              let dimValue = [];
              if (paramValueChoiceType === "multiple") {
                newValue.forEach(item => {
                  if (item && paramValueRunText[item]) {
                    dimValue.push(this.execParams[relationDimId] || item);
                  }
                });
              } else {
                if (newValue[0] && paramValueRunText[newValue[0]]) {
                  dimValue = this.execParams[relationDimId]
                    ? [this.execParams[relationDimId]]
                    : newValue;
                }
              }
              p.paramDefaultValue = dimValue;
              newValue = dimValue;
              // newValue = Object.keys(paramValueText);
              // p.paramDefaultValue = newValue;

              break;
          }

          const newParams = {
            paramId: objectId,
            relationDimId,
            paramName,
            paramType,
            paramDefaultValue: newValue,
            paramValueText: paramType === "SANDBOX" ? ["base"] : null
          };
          const isStringValue = typeof p.paramDefaultValue === "string";
          p.paramDefaultValue = isStringValue ? newValue[0] : newValue;
          const tempText = isStringValue
            ? p.paramDefaultValue
            : p.paramDefaultValue.reduce((pre, next) => {
              pre[next] =
                  this.execParamsLabel[next] || p.paramValueText[next];
              return pre;
            }, {});
          p.paramValueText = isStringValue ? tempText : { ...tempText };
          p.paramValueRunText = isStringValue
            ? tempText
            : { ...p.paramValueText };
          newData.scriptParam.push(newParams);
        });
        this.$set(script, "hasDefalutParams", hasDefalutParams);
        paramsList.push(newData);
      });
      this.scriptParamsValueData = [...paramsList];
      this.spinLoading = false;
    },
    // 参数值 改变
    onChangeParamsValue(value, params, pIndex, script, sIndex, val) {
      const newParam = this.scriptParamsValueData[sIndex].scriptParam[pIndex];
      const { paramType, paramValueChoiceType } = params;
      const newValue =
        paramType === "STRING" ? value.replace(/^\s*|\s*$/g, "") : value;
      let paramDefaultValue = [newValue];
      if (
        (paramType === "RELATION" || paramType === "ENUM") &&
        paramValueChoiceType === "multiple"
      ) {
        paramDefaultValue = newValue;
        if (paramType === "RELATION" && newValue.indexOf("all") !== -1) {
          paramDefaultValue = ["all"];
          this.$nextTick(() => {
            const fiedName = {};
            fiedName[`paramsValue_${script.objectId}_${pIndex}`] = ["all"];
            this.form.setFieldsValue(fiedName);
          });
        }
      }
      if (paramType === "SANDBOX") {
        newParam.paramValueText = [
          val && val.data && val.data.attrs ? val.data.attrs.text : value
        ];
      }
      newParam.paramDefaultValue = paramDefaultValue;
      this.scriptParamsValueData[sIndex].scriptParam[pIndex] = newParam;
      this.restDimMemberSelect({ ...params, paramDefaultValue });
    },
    //
    // 查询全量成员
    queryDimMember(paramItem, searchValue = "") {
      this.getDimMembers({
        dimId: paramItem.relationDimId,
        dimMemberParentId: paramItem.relationDimId,
        searchValue
      })
        .then(res => {
          let newList = [...res];
          if (paramItem.paramValueChoiceType === "multiple" && newList.length) {
            newList = [
              {
                key: "all",
                label: this.$t_process("all"),
                type: "all",
                isLeaf: true
              },
              ...res
            ];
          }
          this.$set(this.allDimMemberSourceData, paramItem.objectId, newList);
          this.restDimMemberSelect(paramItem);
          // this.$set(this.allDimMemberSourceData, paramItem.objectId, res);
        })
        .finally(() => {
          this.selectTreeLoading = false;
        });
    },
    // 根据表达式查询成员
    queryDimMemberByExps(paramItem, searchValue = "") {
      const { dropDownExps = "{}", paramType, relationDimId } = paramItem;
      const newExps = JSON.parse(dropDownExps);
      if (
        !newExps ||
        (newExps.member && newExps.member.length) ||
        (newExps.excludeMember && newExps.excludeMember.length) ||
        newExps.allMember ||
        (newExps.variable && newExps.variable.length)
      ) {
        this.getSelectTreeByExp({
          paramType: paramType,
          dimId: relationDimId,
          dropDownExps: dropDownExps
        })
          .then(res => {
            let newData = res ? res.data : [];
            changeTreeFieldName(newData, "title", "label");
            if (
              paramItem.paramValueChoiceType === "multiple" &&
              newData.length
            ) {
              newData = [
                {
                  key: "all",
                  label: this.$t_process("all"),
                  type: "all",
                  isLeaf: true
                },
                ...newData
              ];
            }
            this.$set(this.allDimMemberSourceData, paramItem.objectId, [
              ...newData
            ]);
            this.restDimMemberSelect(paramItem);
            // this.$set(this.allDimMemberSourceData, paramItem.objectId, newData);
          })
          .finally(() => {
            this.selectTreeLoading = false;
          });
      } else {
        this.selectTreeLoading = false;
      }
    },
    checkExpandableLeaf(item) {
      return !item.isLeaf;
    },
    // 维度成员获取焦点
    onOpenDimMemberSelect(visible, paramItem, pIndex, script, sIndex) {
      if (
        visible &&
        !this.allDimMemberSourceData[paramItem.objectId] &&
        paramItem.paramType === "RELATION"
      ) {
        this.selectTreeLoading = true;
        // 无表达式查询全部
        if (paramItem.dropDownExps) {
          this.queryDimMemberByExps(paramItem);
        } else {
          this.queryDimMember(paramItem);
        }
      } else {
        this.selectTreeLoading = false;
      }
    },

    // 异步加载维度成员
    customTreeloaderDims(treeNode, paramItem, pIndex, script, sIndex) {
      return new Promise((resolve, reject) => {
        if (
          (treeNode.children && treeNode.children.length > 0) ||
          Object.keys(this.reStartTaskData || {}).length > 0
        ) {
          resolve();
          return;
        }
        const parentId = treeNode.key;
        this.getDimMembers({
          dimId: paramItem.relationDimId,
          dimMemberParentId: parentId
        }).then(res => {
          const newParam = this.scriptParamsValueData[sIndex].scriptParam[
            pIndex
          ];
          if (
            newParam.paramDefaultValue &&
            newParam.paramDefaultValue.indexOf("all") !== -1
          ) {
            res.forEach(item => {
              item.disabled = true;
            });
          }
          resolve(res);
        });
      });
    },
    // 搜索成员
    onCustomeSearchDimMember({ searchValue } = {}, paramItem) {
      this.selectTreeLoading = true;
      this.queryDimMember(paramItem, searchValue);
    },
    // 执行
    handleRun() {
      const scriptExecuteParam = [];
      this.form.validateFields((err, values) => {
        if (!err) {
          this.scriptParamsValueData.forEach(script => {
            const { recordId, scriptRecordName, scriptParam } = script;
            const newData = {
              recordId,
              scriptRecordName,
              scriptParam
            };
            scriptExecuteParam.push(newData);
          });
          const runParams = {
            scriptExecuteParam,
            triggerType: "onManage" // 触发类型  onSave（保存触发）   onOpen（打开时触发）   onManual（手动触发）    onManage（脚本管理）
          };
          if (scriptExecuteParam.length) {
            this.confirmLoading = true;
            this.executeScript(runParams)
              .then(res => {
                YnUiUtils.infoMessage(
                  res && res.data
                    ? res.data.msg
                    : this.$t_process("in_progress1")
                );
                this.handleCancel();
                this.$emit(
                  "onPollingScriptStatus",
                  res && res.data ? res.data.taskId : "",
                  runParams
                );
              })
              .catch(function(error) {
                YnUiUtils.errorMessage(
                  error ? error.message : this.$t_process("execution_failed")
                );
              })
              .finally(() => {
                this.$emit("ok");
                this.confirmLoading = false;
              });
          } else {
            this.handleCancel();
          }
        } else {
          const scriptName = [];
          for (const key in err) {
            if (err[key].errors[0].message) {
              // err[key].errors[0].message.indexOf("检查脚本配置") !== -1
              const errList = key.split("_");
              const scriptData = this.scriptsData.filter(
                item => item.objectId === errList[1]
              );
              if (scriptData) {
                scriptName.push(`【${scriptData[0].scriptRecordName}】`);
              }
            }
          }
          if (scriptName.length) {
            YnUiUtils.errorMessage(
              `${scriptName.join("、")}${this.$t_process(
                "no_options_available"
              )}`
            );
          }
        }
      });
    },
    handleCancel() {
      this.runModalVisible = false;
      this.confirmLoading = false;
      this.form.resetFields();
    },
    // 获取沙箱列表
    querySandboxList() {
      this.getSandboxList().then(res => {
        this.sandBoxList = res && res.data ? res.data.items : [];
        this.sandBoxList.unshift({
          sandboxDbCode: "base",
          sandboxName: "base"
        });
      });
    },
    // 设置默认值
    setInitialValue(paramItem) {
      let initialValue = paramItem.paramDefaultValue
        ? paramItem.paramDefaultValue[0]
        : undefined;
      if (paramItem.paramValueChoiceType === "multiple") {
        initialValue = paramItem ? paramItem.paramDefaultValue : undefined;
      }
      return initialValue;
    },
    // 设置默认选中值
    setSelectedItems(paramItem) {
      const {
        paramDefaultValue,
        paramValueText,
        paramValueRunText = paramValueText
      } = paramItem;
      let selectedItems;
      if (paramDefaultValue && paramDefaultValue.length) {
        if (paramItem.paramValueChoiceType === "multiple") {
          selectedItems = [];
          paramDefaultValue.forEach((v, index) => {
            selectedItems.push({ key: v, label: paramValueRunText[v] });
          });
        } else {
          selectedItems = [
            {
              key: paramDefaultValue[0],
              label: paramValueRunText[paramDefaultValue[0]]
            }
          ];
        }
      }

      return selectedItems;
    },
    // 重新处理下拉框成员数据
    restDimMemberSelect(paramItem) {
      if (paramItem.paramType === "RELATION") {
        const isDisabled = paramItem.paramDefaultValue
          ? paramItem.paramDefaultValue.indexOf("all") !== -1
          : false;
        const selectdata = this.allDimMemberSourceData[paramItem.objectId];
        if (selectdata) {
          const allItem = selectdata ? selectdata.splice(0, 1) : [];
          changeTreeFieldValue(selectdata, "disabled", isDisabled);
          this.$set(this.allDimMemberSourceData, paramItem.objectId, [
            ...allItem,
            ...selectdata
          ]);
        }
      }
    },
    // 显示/隐藏脚本参数默认项
    onChangeDefaultShow(script, sIndex) {
      this.$set(script, "defaultShow", !script.defaultShow);
    },
    // 校验是否为默认项
    checkDefaultShow(defaultShow, params) {
      if (
        (params.paramType === "USER" || params.userDefaultValue) &&
        !defaultShow
      ) {
        return false;
      }
      return true;
    },
    ...mapActions("scriptManagement", [
      "getDimMembers",
      "getSelectTreeByExp",
      "executeScript",
      "getSandboxList",
      "getScriptItem"
    ])
  },
  created() {
    this.querySandboxList();
  }
};
</script>
<style lang="less" scoped>
.script-name {
  flex-grow: 1;
  display: flex;
  font-size: @rem16;
  .ant-checkbox-wrapper {
    margin-right: @rem6;
  }
  .script-name-text {
    line-height: @rem24;
    margin-top: -0.29rem;
  }
}
.msg {
  color: @yn-label-color;
  margin-left: @rem22;
  margin-bottom: @rem12;
}

.custom-form {
  :deep(.ant-form-item) {
    display: flex;
    margin-bottom: @rem12;
    .ant-form-item-label,
    .custom-label {
      // 标签换行展示
      display: flex;
      flex: 1;
      width: 6rem;
      line-height: 1.5;
      min-height: 2.25rem;
      padding-right: 0.5rem;
      align-self: flex-start;
      align-items: center;
      justify-content: flex-end;
      white-space: normal;
      word-break: break-all;
      ::after {
        display: none;
      }
    }
    .ant-form-item-control-wrapper {
      width: calc(100% - 6.5rem);
    }
  }
  .ant-divider {
    margin: @rem16 0;
  }
}
.script-part-top {
  display: flex;
  margin-bottom: @rem8;
  align-items: baseline;
  line-height: @rem24;

  .default-Show-btn {
    height: @rem22;
    flex: 0 0 6rem;
    // width: 5.375rem;
    font-size: @rem14;
    line-height: @rem22;
    /* 主题色/@yn-primary-color主题 */
    color: @yn-primary-color;
    cursor: pointer;
    margin-left: @rem16;
    margin-top: -0.125rem;
    .ynicon {
      margin-right: @rem4;
    }
  }
}
.blue-icon {
  width: @rem4;
  height: @rem16;
  /* 主题色/@yn-primary-color主题 */
  background: @yn-primary-color;
  border-radius: 10px;
  margin-right: @rem6;
  flex: 0 0 @rem4;
  margin-bottom: -0.13rem;
}
</style>
