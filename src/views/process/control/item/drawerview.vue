<template>
  <yn-drawer
    :title="$t_process('view_process')"
    class="drawerview"
    width="27.5rem"
    :closable="true"
    :visible="visible"
    @close="onClose"
  >
    <yn-collapse v-model="activeKey" :expandIconPosition="'right'" accordion>
      <yn-collapse-panel
        v-for="(item, index) in listlocal"
        :key="'collapse' + index"
      >
        <template slot="header">
          <span :class="['collapse-heade']">
            {{ item.firstDate }}
            {{ $t_process("submitted_x_times", [index + 1]) }}
          </span>
        </template>
        <template slot="extra">
          {{
            index === activeKey ? $t_common("collapse") : $t_common("unfold")
          }}
        </template>
        <yn-timeline mode="retinue">
          <yn-timeline-item
            v-for="person in item.operatorItemList"
            :key="person.nodeId"
            :class="[
              STATUS[person._nodeOperatorStatus],
              { unstartapply: person.unstartapply }
            ]"
          >
            <yn-icon-svg
              slot="dot"
              class="timeline-icon"
              :type="STATUS[person._nodeOperatorStatus]"
            />
            <p slot="title">
              {{ person.nodeName }}
            </p>

            <div
              v-for="(pitem, pindex) in person.operatorNameList"
              v-show="pindex === 0 || person.field === false"
              :key="pitem + pindex"
            >
              <p>
                {{ pitem }} &nbsp;{{ person.operatorDate }}
                <span
                  v-if="pindex === 0 && person.operatorNameList.length > 1"
                  class="field-action"
                >
                  {{ person.field ? $t_common("unfold") : $t_common("collapse")
                  }}<SvgIcon
                    :class="{ down: person.field }"
                    type="icon-up"
                    @click="handlerField(person)"
                  />
                </span>
              </p>
              <div class="status-detail">
                <span class="yn-timeline-aim"> {{ person.operatorType }}</span>
                <span class="yn-timeline-des">
                  {{ person.des }}
                </span>
              </div>
            </div>
          </yn-timeline-item>
          <yn-timeline-item
            :class="item.processEnd ? 'check-circle' : 'have-in-hand'"
          >
            <yn-icon-svg
              slot="dot"
              class="timeline-end"
              :type="item.processEnd ? 'check-circle' : 'have-in-hand'"
            />
            <p slot="title">
              {{ $t_process("end") }}
            </p>
          </yn-timeline-item>
        </yn-timeline>
      </yn-collapse-panel>
    </yn-collapse>
  </yn-drawer>
</template>
<script>
import SvgIcon from "@/components/ui/SvgIcon.vue";
import "yn-p1/libs/components/yn-collapse/";
import "yn-p1/libs/components/yn-collapse-panel/";
import "yn-p1/libs/components/yn-timeline/";
import YnIconSvg from "yn-p1/libs/components/yn-icon/yn-icon-svg";
import "yn-p1/libs/components/yn-drawer/";

export default {
  components: { SvgIcon, YnIconSvg },
  props: {
    title: {
      type: String,
      default: ""
    },
    list: {
      type: Array,
      default: () => []
    },
    data: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeKey: "",
      listlocal: [],
      STATUS: Object.freeze({
        pass: "check-circle",
        out: "close-circle",
        process: "have-in-hand",
        unstart: "have-in-hand"
      })
    };
  },
  watch: {
    list: {
      immediate: true,
      handler(value) {
        this.listlocal = this.setNodeOperatorStatus();
        this.activeKey = "collapse" + Math.max(value.length - 1, 0);
      }
    }
  },
  methods: {
    setNodeOperatorStatus() {
      const last = Math.max(this.list.length - 1, 0); // 最后一个流程单index
      return this.list.map((item, index) => {
        item.processEnd = false; // 当前流程单是否审批结束， 默认否
        const isPass = !(index < last); // 是否最后一次流程单。每次驳回生成一次流程，说明未通过（驳回才会未通过）
        const itemlast = Math.max(item.operatorItemList.length - 1, 0); // 当前流程单最后一个环节index
        let nextApply = null; // 下个环节
        item.operatorItemList.map((sitem, sindex) => {
          // 设置图标状态
          const islast = sindex === itemlast;
          // 已提交节点，最后一个成员，且是已提交
          const isSubLast =
            sitem.nodeStatus === "submitted" &&
            islast &&
            this.data.nodeStatus.v === "submitted";
          if (isPass) {
            if (sitem.operatorType !== "") {
              sitem._nodeOperatorStatus = "pass";
              if (isSubLast) {
                item.processEnd = true;
              }
            } else {
              if (!nextApply) {
                sitem.unstartapply = false;
                nextApply = sitem;
              } else {
                sitem.unstartapply = true;
              }
              sitem._nodeOperatorStatus = "process";
              if (isSubLast) {
                item.processEnd = true;
                sitem._nodeOperatorStatus = "pass";
              }
            }
          } else {
            if (sitem.operatorTypeCode === "turndown") {
              sitem._nodeOperatorStatus = "out";
            } else {
              sitem._nodeOperatorStatus = "pass";
            }
          }
          // 构造人员数组
          sitem.operatorNameList = isSubLast
            ? []
            : sitem.operatorNameList || [sitem.operatorName];
          // 设置折叠状态
          sitem.field = false;
          return sitem;
        });
        return item;
      });
    },
    handlerField(item) {
      item.field = !item.field;
      this.listlocal = [...this.listlocal];
    },
    onClose() {
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style lang="less" scoped>
.drawerview {
  font-size: 0.875rem;
  z-index: 1055 !important;
  /deep/ .ant-collapse {
    border: none;
  }
  /deep/ .ant-collapse-content-box {
    padding: 1.5rem 0;
  }

  /deep/ .ant-collapse-item {
    margin-bottom: 1.5rem;
  }
  /deep/ .ant-collapse-header {
    color: @yn-label-color;
    text-align: left;
    font-weight: 400;
  }
  /deep/ .ant-collapse-item-active .ant-collapse-header {
    color: @yn-text-color;
  }
  /deep/ .ant-timeline-item-title {
    width: calc(30% - 2rem) !important;
  }
  /deep/ .ant-timeline-item-tail,
  /deep/ .ant-timeline-item-head,
  /deep/ .ant-timeline-item-head-custom {
    left: 30% !important;
  }
  /deep/ .ant-timeline-item-content {
    left: calc(30% + 1.25rem) !important;
    width: calc(70% - 1.5rem) !important;
  }
  /deep/ .ant-drawer-wrapper-body {
    display: flex;
    flex-direction: column;
    .ant-drawer-body {
      height: 0;
      flex: 1;
      overflow: auto;
    }
  }
  .field-action {
    height: 1.375rem;
    color: @yn-primary-color;
    line-height: 1.375rem;
    font-weight: 400;
    font-size: 0.875rem;
    span {
      color: @yn-primary-color;
    }
    .down {
      transform: rotate(180deg);
    }
  }
  .check-circle {
    .timeline-icon {
      color: @yn-success-color;
    }
    .yn-timeline-aim {
      font-size: 0.875rem;
      color: @yn-success-color;
    }
    /deep/ .ant-timeline-item-tail {
      border-color: @yn-success-color !important;
    }
    .timeline-end {
      color: @yn-success-color;
    }
  }
  .close-circle {
    .timeline-icon {
      color: @yn-error-color;
    }
    .yn-timeline-aim {
      font-size: 0.875rem;
      color: @yn-error-color;
    }
    /deep/ .ant-timeline-item-tail {
      border-color: @yn-error-color !important;
    }
  }
  .have-in-hand {
    .timeline-icon {
      color: @yn-primary-color;
    }
    .yn-timeline-aim {
      font-size: 0.875rem;
      color: @yn-primary-color;
    }
    /deep/ .ant-timeline-item-tail {
      border-color: @yn-primary-color !important;
    }
    .timeline-end {
      color: @yn-disabled-color;
    }
  }
  .unstartapply {
    .timeline-icon {
      color: @yn-disabled-color;
    }
    .yn-timeline-aim {
      font-size: 0.875rem;
      color: @yn-disabled-color;
    }
    /deep/ .ant-timeline-item-tail {
      border-color: @yn-disabled-color !important;
    }
    .timeline-end {
      color: @yn-disabled-color;
    }
  }
  .status-detail {
    margin-top: 0.25rem;
  }
  .yn-timeline-des {
    font-size: 0.875rem;
    color: @yn-label-color;
    margin-left: 1rem;
  }
}
</style>
