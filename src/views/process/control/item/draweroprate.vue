<template>
  <yn-drawer
    :title="localTitle"
    class="drawer-oprate"
    placement="right"
    :closable="true"
    width="29.5rem"
    :visible="visible"
    :maskClosable="false"
    @close="onClose"
  >
    <yn-spin :spinning="spinning" class="state-spinning">
      <yn-form
        :form="addForm"
        v-bind="{
          labelCol: { span: 4 },
          wrapperCol: { span: 20 }
        }"
      >
        <yn-form-item :label="$t_common('description')" :colon="false">
          <yn-textarea
            v-decorator="[
              'remark',
              {
                initialValue: '',
                rules: [
                  {
                    required: false,
                    max: 500,
                    message: $t_process('field_length_exceeds_500')
                  }
                ]
              }
            ]"
            :disabled="loading"
            :placeholder="$t_common('input_message')"
            :showCount="false"
            :autoSize="{ minRows: 5, maxRows: 15 }"
            @change="onChange"
          />
          <div class="text-count">{{ descriptionLen }}/500</div>
        </yn-form-item>
        <yn-form-item :label="$t_common('enclosure')" :colon="false">
          <yn-upload
            :disabled="loading"
            :remove="handleRemove"
            :beforeUpload="beforeUpload"
            :multiple="true"
            :fileList="fileList"
            class="upload"
          >
            <yn-button :disabled="loading">
              <yn-icon type="upload" /> {{ $t_common("upload") }}
            </yn-button>
          </yn-upload>
        </yn-form-item>
        <yn-divider />
        <yn-form-item
          :label="$t_process(type === 'turndown' ? 'reject_to' : 'submit_to')"
          :colon="false"
        >
          <span v-show="type !== 'turndown'">{{ nextState }}</span>
          <yn-select
            v-show="type === 'turndown'"
            v-model="nextNodeId"
            :disabled="loading"
            class="action-select"
            :placeholder="
              $t_common('input_message') + $t_process('process_group')
            "
            :options="options"
            :allowClear="false"
          />
          <yn-button
            type="text"
            class="action-button toright"
            :disabled="loading"
            @click="showChildrenDrawer"
          >
            {{ $t_process("view_process") }}
            <SvgIcon :isIconBtn="false" type="icon-c1_cr_form_enter" />
          </yn-button>
        </yn-form-item>
        <yn-divider />
        <yn-form-item :label="$t_process('cc')" :colon="false">
          <PersonInput
            v-show="false"
            v-model="person"
            className="process-person-input"
            :visible.sync="personvisible"
          />
          <yn-button
            type="text"
            :disabled="loading"
            class="action-button"
            @click="handlerPerson"
          >
            <SvgIcon :isIconBtn="false" type="icon-cr_add" />
            {{ $t_process("add_cc_to") }}
          </yn-button>
          <div class="senders">
            <template v-for="item in person">
              <template v-for="sitem in item">
                <yn-tag
                  v-if="sitem.name && sitem.name.trim()"
                  :key="sitem.id"
                  class="sender-tag"
                  closable
                  @close="deleteSender(sitem, item)"
                >
                  {{ sitem.name }}
                </yn-tag>
              </template>
            </template>
          </div>
        </yn-form-item>
        <yn-divider />
        <div v-for="(item, index) in forms" :key="index">
          <yn-form-item
            :label="$t_process('phase_xx', [index + 1])"
            :colon="false"
          >
            <div class="button-process">
              <yn-button
                v-if="item.batchFormName && item.batchFormName.length"
                type="text"
                class="action-button small-action"
                :disabled="loading"
                @click="showFromDrawer(item.batchFormName)"
              >
                {{ $t_process("includes_x_form", [item.batchFormName.length]) }}
                <SvgIcon
                  :isIconBtn="false"
                  class="to-detail"
                  type="icon-c1_cr_form_enter"
                />
              </yn-button>
              <yn-button
                v-if="item.batchDimMemberList && item.batchDimMemberList.length"
                type="text"
                class="action-button small-action"
                :disabled="loading"
                @click="showDimFromDrawer(item.batchDimMemberList)"
              >
                {{
                  $t_process("contains_x_dimension", [
                    item.batchDimMemberList.length
                  ])
                }}
                <SvgIcon
                  :isIconBtn="false"
                  class="to-detail"
                  type="icon-c1_cr_form_enter"
                />
              </yn-button>
              <yn-button
                v-if="index === forms.length - 1"
                type="text"
                :class="[
                  'action-button',
                  'small-action',
                  CHECKSTATUSCLASS[checkCode]
                ]"
                :disabled="loading || !checkState"
                @click="showCheckDrawer(item)"
              >
                {{
                  checkState
                    ? `${$t_process("current")}${
                      checkCode === "0"
                        ? checkState
                        : $t_common("check") + checkState
                    }，${$t_process("go_check_it_out")}`
                    : $t_process("no_verification_available")
                }}
                <SvgIcon
                  :isIconBtn="false"
                  class="to-detail"
                  type="icon-c1_cr_form_enter"
                />
              </yn-button>
            </div>
          </yn-form-item>
          <yn-divider />
        </div>
      </yn-form>
    </yn-spin>
    <div class="buttons-group">
      <yn-button :style="{ marginRight: '0.5rem' }" @click="onClose">
        {{ $t_common("cancel") }}
      </yn-button>
      <yn-button
        v-if="hasMerge"
        :loading="mergeAndSubmitLoading"
        :disabled="spinning || mergeAndSubmitLoading"
        :style="{ marginRight: '0.5rem' }"
        @click="onSubmit('mergeAndSubmit')"
      >
        {{ $t_process("merge_then_submit") }}
      </yn-button>

      <yn-button
        v-if="type !== 'submit' || enAbleMerge !== 'enable_both' || (type == 'submit' && !hasMerge)"
        type="primary"
        :loading="submitLoading"
        :disabled="spinning || submitLoading"
        @click="onSubmit(type)"
      >
        {{ title }}
      </yn-button>
    </div>
    <DrawerView
      v-if="subvisible"
      :visible.sync="subvisible"
      :data="data"
      :list="list"
    />
    <DrawerCheck
      :visible.sync="checkVisible"
      :status="title"
      :params="params"
      :staticData="staticData"
      :data="data"
    />
    <DrawerForm
      v-if="fromVisible"
      :visible.sync="fromVisible"
      :status="title"
      :forms="formData"
      :data="data"
    />
  </yn-drawer>
</template>
<script>
import SvgIcon from "@/components/ui/SvgIcon.vue";
import DrawerView from "./drawerview";
import DrawerForm from "./drawerForm";
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-upload/";
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-tag/";
import "yn-p1/libs/components/yn-textarea/";
import "yn-p1/libs/components/yn-icon/";
import DrawerCheck from "./drawercheck.vue";
import PersonInput from "../../nodes/item/personinput";
import commonService from "@/services/common";
import precessService from "@/services/process";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import checkSize from "./checkSize";
import { actions } from "../constants/action";
import { mapState } from "vuex";
const loadingMap = {
  submit: "submitLoading",
  audit: "submitLoading",
  turndown: "submitLoading",
  start: "submitLoading",
  mergeAndSubmit: "mergeAndSubmitLoading"
};
const CHECKSTATUSCLASS = Object.freeze({
  "": "",
  0: "uncheck",
  1: "pass",
  2: "unpass",
  3: "warning"
});

export default {
  components: { DrawerView, SvgIcon, PersonInput, DrawerForm, DrawerCheck },
  mixins: [checkSize],
  props: {
    title: {
      type: String,
      default: ""
    },
    hasBatch: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: String,
      default: ""
    },
    openUrl: {
      type: String,
      default: ""
    },
    data: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    },
    setLoading: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      CHECKSTATUSCLASS,
      person: {},
      options: [],
      list: [],
      previewIds: [],
      nextState: "",
      canReject: true,
      nextNodeId: "",
      spinning: false,
      submitLoading: false,
      mergeAndSubmitLoading: false,
      personvisible: false,
      fileList: [],
      descriptionLen: 0,
      CHECKSTATUS: Object.freeze({
        0: "not_validated",
        1: "successful",
        2: "failed",
        3: "warning"
      }),
      addForm: this.$form.createForm(this, "addForm"),
      subvisible: false,
      fromVisible: false,
      checkVisible: false,
      enAbleMerge: false,
      checkState: "",
      checkCode: "",
      formData: {},
      staticData: [],
      forms: [],
      allNameMap: {},
      nodeNameMap: {},
      batchNameMap: {},
      allNodeMap: {}
    };
  },
  computed: {
    ...mapState({
      actionCol: state => state.processStore.actionCol
    }),
    loading() {
      return this.mergeAndSubmitLoading || this.submitLoading;
    },
    hasMerge() {
      if (this.type !== "submit") {
        return false;
      }
      const hasConsolidationAuth = (this.data.operatorList || []).find(
        item =>
          item.operatorAuth &&
          item.operatorName === this.$t_process("consolidation")
      );
      // 根据配置值控制按钮显示
      // enable_both: 只显示"合并后提交"按钮
      // enable: 显示"合并后提交"和"提交"按钮
      // disable: 只显示"提交"按钮
      if (this.enAbleMerge === "disable") {
        return false; // 只显示提交按钮
      } else if (this.enAbleMerge === "enable_both") {
        return hasConsolidationAuth; // 只显示合并后提交按钮
      } else if (this.enAbleMerge === "enable") {
        return hasConsolidationAuth; // 显示合并后提交和提交按钮
      }

      return false; // 默认只显示提交按钮
    },
    localTitle() {
      const pre =
        this.data.batchStatus && this.data.batchStatus.v
          ? this.data.batchStatus.v.split("-")[0]
          : "";
      return pre
        ? pre + "-" + this.$t_process("batch") + this.title
        : this.title;
    }
  },
  watch: {
    visible: {
      async handler(value) {
        if (value) {
          this.fileList = [];
          this.person = {};
          this.canReject = true;
          this.enAbleMerge = await this.mddAppParam();
          this.addForm.resetFields();
          this.search();
        }
      },
      immediate: true
    }
  },
  created() {},
  methods: {
    async processOperation() {
      const {
        data: { data }
      } = await precessService("processOperation", {
        processId: this.data.processId,
        batchId: this.data.batchId,
        batchSubmit: this.data.batchSubmit,
        nodeId: this.data.nodeId,
        memberId: this.data.memberId,
        objectId: this.data.objectId,
        hasParent: this.data.memberId !== this.data.memberRelationParentId
      }).catch(e => (this.spinning = false));
      return data;
    },
    async rejectOperation() {
      if (!this.canReject) {
        UiUtils.error({
          title: this.$t_process("xxx_failed", [this.$t_process("reject")]),
          content: this.$t_process("reject_superior_company_first")
        });
        this.submitLoading = false;
        this.setLoading(this.data, this.actionCol, this.type, false);
        return;
      }
      const formData = new FormData();
      const params = {
        nowBatchId: this.data.batchId,
        nowNodeStatus: this.data.nodeStatus.v,
        nextBatchId: this.nodeNameMap[this.nextNodeId] ? "" : this.nextNodeId,
        processId: this.data.processId,
        nowNodeName: this.data.nodeName,
        nowNodeId: this.data.nodeId,
        openUrl: this.openUrl,
        dimCode: this.data.dimCode,
        objectId: this.data.objectId,
        flowTemplateContentId: this.data.flowTemplateContentId || "",
        buttonName: this.data.buttonName,
        previewIds: this.previewIds,
        nextNodeId: this.nodeNameMap[this.nextNodeId]
          ? this.nextNodeId
          : this.allNodeMap[this.nextNodeId].nodeId,
        nextNodeStatus: this.allNameMap[this.nextNodeId],
        ...this.addForm.getFieldsValue(),
        carbonCopyIds: this.getPerson()
      };
      for (const key in params) {
        formData.append(key, params[key]);
      }
      this.fileList.forEach(file => {
        formData.append("files", file);
      });
      let isSuccess = false;
      precessService("rejectOperation", formData)
        .then(res => {
          const {
            data: { success, data }
          } = res;
          if (data && data.failType && data.failType.toString() === "4") {
            UiUtils.error({
              title: this.$t_process("xxx_failed", [this.$t_process("reject")]),
              content: `${this.data.memberName.v}${data.messageInfo}`
            });
          } else if (
            data &&
            data.failType &&
            data.failType.toString() === "6"
          ) {
            // 状态变更校验失败
            this.$emit("refresh");
          } else if (data && !success) {
            UiUtils.error({
              title: this.$t_process("xxx_failed", [this.$t_process("reject")]),
              content: `${data.messageInfo}`
            });
          } else {
            UiUtils.successMessage(
              this.$t_process("xxx_succeeded", [this.$t_process("reject")])
            );
            isSuccess = true;
            this.onClose();
          }
          if (isSuccess) {
            this.$emit("ok", "reject", this.data);
          } else {
            this.setLoading(this.data, this.actionCol, this.type, false);
          }
        })
        .finally(res => {
          this.submitLoading = false;
        });
    },
    async auditOperation() {
      const formData = new FormData();
      const params = {
        nowBatchId: this.data.batchId,
        nowNodeStatus: this.data.nodeStatus.v,
        nextBatchId: this._nextBatchId,
        processId: this.data.processId,
        nowNodeId: this.data.nodeId,
        openUrl: this.openUrl,
        objectId: this.data.objectId,
        dimCode: this.data.dimCode,
        flowTemplateContentId: this.data.flowTemplateContentId || "",
        buttonName: this.data.buttonName,
        nextNodeStatus: this.allNameMap[this._nextNodeId],
        nextNodeId: this._nextNodeId,
        previewIds: this.previewIds,
        ...this.addForm.getFieldsValue(),
        carbonCopyIds: this.getPerson()
        // files: this.fileList
      };
      for (const key in params) {
        formData.append(key, params[key]);
      }
      this.fileList.forEach(file => {
        formData.append("files", file);
      });
      let isSuccess = false;
      precessService("auditOperation", formData)
        .then(res => {
          const {
            data: { success, data }
          } = res;
          if (data && data.failType && data.failType.toString() === "4") {
            UiUtils.error({
              title: this.$t_process("xxx_failed", [
                this.$t_process("approve")
              ]),
              content: `${this.data.memberName.v}${data.messageInfo}`
            });
          } else if (
            data &&
            data.failType &&
            data.failType.toString() === "6"
          ) {
            // 状态变更校验失败
            this.$emit("refresh");
          } else if (data && !success) {
            // 其他错误类型
            UiUtils.error({
              title: this.$t_process("xxx_failed", [
                this.$t_process("approve")
              ]),
              content: `${data.messageInfo}`
            });
          } else {
            UiUtils.successMessage(
              this.$t_process("xxx_succeeded", [this.$t_process("approve")])
            );
            isSuccess = true;
            this.onClose();
          }
          if (isSuccess) {
            this.$emit("ok", "audit", this.data);
          } else {
            this.setLoading(this.data, this.actionCol, this.type, false);
          }
        })
        .finally(res => {
          this.submitLoading = false;
        });
    },
    // 提交和合并的合并接口，入参包含合并和提交入参，返回结构和提交保持一致
    async mergeSubmitOperation() {
      const formData = new FormData();
      const params = {
        ...this.params,
        memberId: this.data.memberId,
        processId: this.data.processId,
        nowNodeId: this.data.nodeId,
        nowNodeStatus: this.data.nodeStatus.v,
        nowBatchId: this.data.batchId,
        batchSubmit: this.data.batchSubmit,
        nextBatchId: this._nextBatchId,
        flowTemplateContentId: this.data.flowTemplateContentId || "",
        buttonName: this.data.buttonName,
        openUrl: this.openUrl,
        objectId: this.data.objectId,
        nextNodeId: this._nextNodeId,
        memberRelationParentId: this.data.memberRelationParentId,
        memberDbCode: this.data.memberDbCode,
        memberRelationTotalCode: this.data.memberRelationTotalCode,
        dimCode: this.data.dimCode,
        nextNodeStatus: this.allNameMap[this._nextNodeId],
        previewIds: this.previewIds,
        ...this.addForm.getFieldsValue(),
        carbonCopyIds: this.getPerson()
        // files: this.fileList
      };
      for (const key in params) {
        formData.append(key, params[key]);
      }
      this.fileList.forEach(file => {
        formData.append("files", file);
      });
      let isSuccess = false;
      precessService("mergeSubmitOperation", formData)
        .then(res => {
          const {
            data: { success, data }
          } = res;
          if (data && data.failType && data.failType.toString() === "3") {
            // 操作校验失败
            UiUtils.error({
              title: this.$t_process("xxx_failed", [this.$t_process("submit")]),
              class: "jsx-message",
              content: h => (
                <div>
                  <div class="message-title">{data.messageInfo}</div>
                  <ul class="message-lists">
                    {data.memberList.map(li => {
                      return <li class="message-list">{li}</li>;
                    })}
                  </ul>
                </div>
              )
            });
          } else if (
            data &&
            data.failType &&
            data.failType.toString() === "4"
          ) {
            // 并发校验失败
            UiUtils.error({
              title: this.$t_process("xxx_failed", [this.$t_process("submit")]),
              content: `${this.data.memberName.v}${data.messageInfo}`
            });
          } else if (
            data &&
            data.failType &&
            data.failType.toString() === "6"
          ) {
            // 状态变更校验失败
            this.$emit("refresh");
          } else if (data && !success) {
            // 常规报错
            UiUtils.error({
              title: this.$t_process("xxx_failed", [this.$t_process("submit")]),
              content: h => <div class="message-title">{data.messageInfo}</div>
            });
          } else if (success) {
            UiUtils.successMessage(
              this.$t_process("xxx_succeeded", [this.$t_process("submit")])
            );
            isSuccess = true;
            this.onClose();
          }
          if (isSuccess) {
            this.$emit("ok", "submit", this.data);
          } else {
            this.setLoading(
              this.data,
              this.actionCol,
              actions[this.type],
              false
            );
          }
        })
        .finally(res => {
          this.mergeAndSubmitLoading = false;
        });
    },
    async submit() {
      const formData = new FormData();
      const params = {
        processId: this.data.processId,
        nowNodeId: this.data.nodeId,
        nowNodeStatus: this.data.nodeStatus.v,
        nowBatchId: this.data.batchId,
        batchSubmit: this.data.batchSubmit,
        nextBatchId: this._nextBatchId,
        flowTemplateContentId: this.data.flowTemplateContentId || "",
        buttonName: this.data.buttonName,
        openUrl: this.openUrl,
        objectId: this.data.objectId,
        nextNodeId: this._nextNodeId,
        memberRelationParentId: this.data.memberRelationParentId,
        memberDbCode: this.data.memberDbCode,
        memberRelationTotalCode: this.data.memberRelationTotalCode,
        dimCode: this.data.dimCode,
        nextNodeStatus: this.allNameMap[this._nextNodeId],
        previewIds: this.previewIds,
        ...this.addForm.getFieldsValue(),
        carbonCopyIds: this.getPerson()
        // files: this.fileList
      };
      for (const key in params) {
        formData.append(key, params[key]);
      }
      this.fileList.forEach(file => {
        formData.append("files", file);
      });
      let isSuccess = false;
      precessService("submitOperation", formData)
        .then(res => {
          const {
            data: { success, data }
          } = res;
          if (data && data.failType && data.failType.toString() === "3") {
            // 操作校验失败
            UiUtils.error({
              title: this.$t_process("xxx_failed", [this.$t_process("submit")]),
              class: "jsx-message",
              content: h => (
                <div>
                  <div class="message-title">{data.messageInfo}</div>
                  <ul class="message-lists">
                    {data.memberList.map(li => {
                      return <li class="message-list">{li}</li>;
                    })}
                  </ul>
                </div>
              )
            });
          } else if (
            data &&
            data.failType &&
            data.failType.toString() === "4"
          ) {
            // 并发校验失败
            UiUtils.error({
              title: this.$t_process("xxx_failed", [this.$t_process("submit")]),
              content: `${this.data.memberName.v}${data.messageInfo}`
            });
          } else if (
            data &&
            data.failType &&
            data.failType.toString() === "6"
          ) {
            // 状态变更校验失败
            this.$emit("refresh");
          } else if (data && !success) {
            // 常规报错
            UiUtils.error({
              title: this.$t_process("xxx_failed", [this.$t_process("submit")]),
              content: h => <div class="message-title">{data.messageInfo}</div>
            });
          } else if (success) {
            UiUtils.successMessage(
              this.$t_process("xxx_succeeded", [this.$t_process("submit")])
            );
            isSuccess = true;
            this.onClose();
          }
          if (isSuccess) {
            this.$emit("ok", "submit", this.data);
          } else {
            this.setLoading(this.data, this.actionCol, this.type, false);
          }
        })
        .finally(res => {
          this.submitLoading = false;
        });
    },
    async submitOperationCheck(callback) {
      if (this.CHECKSTATUS[this.data.checkCode] !== "warning") {
        callback && callback();
        return;
      }
      if (this.data.batchId && this._nextBatchId) {
        callback && callback();
        return;
      }
      UiUtils.confirm({
        title: this.$t_process("confirm_submission") + "?",
        content: h => (
          <div class="message-title">
            {this.$t_process("warning_message_confirmation")}
          </div>
        ),
        onOk: () => {
          callback && callback();
        },
        onCancel: () => {
          this.submitLoading = false;
          this.setLoading(this.data, this.actionCol, this.type, false);
        }
      });
    },
    async search() {
      this.spinning = true;
      const data = await this.processOperation();
      const {
        nextState,
        nextNodeId,
        // frontState,
        frontNodeId,
        nextBatchId,
        frontBatchId,
        nodeBatchList,
        nodeNameList
      } = data;
      this.canReject = (nodeNameList || [])
        .concat(nodeBatchList || [])
        .some(item => item.hasReject);
      this.options = [];
      this.nodeNameMap = {};
      this.batchNameMap = {};
      this.allNodeMap = {};
      (data.nodeNameList || []).forEach((item, index) => {
        const tmp = {
          disabled: !item.hasReject,
          label: item.nodeName,
          value: item.objectId,
          nodeId: item.objectId
        };
        this.allNodeMap[item.objectId] = tmp;
        this.nodeNameMap[item.objectId] = item.nodeStatus;
        if (item.nodeType !== "batch") {
          // 主节点
          this.options.push(tmp);
        }
        // 分批节点
        if (item.nodeBatchList) {
          item.nodeBatchList.forEach(bt => {
            this.batchNameMap[bt.batchId] = item.nodeStatus;
            const tmp = {
              disabled: !bt.hasReject,
              label: this.$t_process("phase_xx", [bt.batchOrder]),
              value: bt.batchId,
              nodeId: item.objectId
            };
            this.allNodeMap[bt.batchId] = tmp;
            this.options.push(tmp);
          });
        }
      });
      this.allNameMap = { ...this.nodeNameMap, ...this.batchNameMap };
      this._operateCache = data;
      this.list = data.operationVOList || [];
      this.forms = data.nodeBatchList || [];
      this.checkState = data.checkState;
      this.checkCode = data.checkCode;
      this.staticData = data.verificationItemList || [];
      this.spinning = false;
      // 设置回显值
      this.$nextTick(() => {
        this.nextNodeId = frontBatchId || frontNodeId; // 驳回时是反向的，上一个id
        this.nextState = nextState;
        // this._frontState = frontState;
        this._nextNodeId = nextNodeId || "";
        this._frontNodeId = frontNodeId || "";
        this._nextBatchId = nextBatchId || "";
        this._frontBatchId = frontBatchId || "";
      });
    },
    action(type) {
      const action = {
        submit: () => this.submitOperationCheck(this.submit),
        audit: this.auditOperation,
        turndown: this.rejectOperation,
        start: () => {
          this.$emit("ok", "start", this.data);
          this.onClose();
        },
        mergeAndSubmit: () =>
          this.submitOperationCheck(this.mergeSubmitOperation)
      };
      return action[type];
    },
    onChange(e) {
      this.descriptionLen = e.target.value.length;
    },
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      const newFileList = this.fileList.slice();
      newFileList.splice(index, 1);
      this.mixinIsOver50m(newFileList);
      this.fileList = newFileList;
    },
    beforeUpload(file) {
      if (file.size === 0) {
        UiUtils.errorMessage(this.$t_common("unable_upload_empty"));
        return false;
      }
      this.fileList = [...this.fileList, file];
      this.mixinValidateOverSize(this.fileList);
      return false;
    },
    showChildrenDrawer() {
      this.subvisible = true;
    },
    showFromDrawer(item) {
      this.formData = item || [];
      this.fromVisible = true;
    },
    showDimFromDrawer(item) {
      this.formData = item;
      this.fromVisible = true;
    },
    showCheckDrawer() {
      this.checkVisible = true;
    },
    handlerPerson() {
      this.personvisible = !this.personvisible;
    },
    getPerson() {
      const arr = [];
      for (const key in this.person) {
        arr.push(...this.person[key]);
      }
      return arr.map(item => item.id).join(",");
    },
    deleteSender(item, group) {
      const index = group.indexOf(item);
      group.splice(index, 1);
      this.person = { ...this.person };
    },
    onClose() {
      this.$emit("update:visible", false);
      this.mixinOver = false;
    },
    async uploadToPlatform() {
      const upMap = this.fileList.map(file => {
        const formData = new FormData();
        formData.append("uploadFile", file);
        return commonService("uploadAttachment", formData).then(res => {
          return res.data.data;
        });
      });
      return await Promise.all(upMap);
    },
    async mddAppParam() {
      const { data } = await precessService("mddAppParam");
      return data.items && data.items[0] ? data.items[0].paramValue : "disable";
    },
    onSubmit(type) {
      if (this.mixinValidateOverSize(this.fileList)) return;
      this.addForm.validateFields(async (err, value) => {
        if (!err) {
          this[loadingMap[type]] = true;
          this.previewIds = await this.uploadToPlatform()
            .then(res => res.map(item => item.objectId))
            .catch(_ => {
              this[loadingMap[type]] = false;
            });
          if (!this.previewIds) return;
          this.setLoading(this.data, this.actionCol, this.type, true);
          this.action(type)();
        }
      });
    }
  }
};
</script>

<style scoped lang="less">
.drawer-oprate {
  font-size: 0.875rem;
  z-index: 1040 !important;
}
.text-count {
  height: @rem22;
  line-height: @rem22;
  position: absolute;
  top: -@rem10;
  right: @rem10;
  color: @yn-border-color-base;
}
/deep/ .ant-form .ant-form-item-label > label {
  color: @yn-text-color-secondary;
}
.action-select {
  width: 50%;
}
.button-process {
  display: flex;
  flex-direction: column;
  align-items: end;
}
.action-button {
  color: @yn-primary-color;
  /deep/ i {
    color: @yn-primary-color !important;
  }
  &.toright {
    float: right;
  }
  .to-detail {
    color: @yn-label-color !important;
  }
}
.small-action {
  width: 15rem;
  text-align: right;
}
.upload {
  // /deep/ .anticon-paper-clip {
  //   display: none;
  // }
}
.uploadIcon {
  position: absolute;
}
.sender-tag {
  height: 1.625rem;
  background: @yn-background-color;
  border: 1px solid @yn-border-color-base;
  border-radius: 0.25rem;
}
.buttons-group {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid @yn-border-color-base;
  padding: 0.625rem 1rem;
  background: @yn-body-background;
  text-align: right;
  z-index: 1;
}

/deep/ .ant-drawer-wrapper-body {
  height: 100%;
  display: block;
  .ant-spin-nested-loading {
    height: auto;
  }
  .ant-drawer-body {
    height: calc(100% - 4.625rem);
    flex: 0;
    overflow: auto;
  }
}
.uncheck {
  color: @yn-text-color;
}
.pass {
  color: @yn-success-color;
}
.unpass {
  color: @yn-error-color;
}
.warning {
  color: @yn-warning-color;
}
</style>
<style lang="less">
.jsx-message .message-title {
  color: @yn-text-color-secondary;
  text-align: left;
  font-weight: 400;
  margin-bottom: 1rem;
}
.jsx-message .message-lists {
  max-height: 25rem;
  overflow: auto;
}
.jsx-message .message-list {
  color: @yn-text-color;
  text-align: left;
  font-weight: 400;
  margin-bottom: 0.25rem;
}
.process-person-input {
  .ant-modal-mask {
    z-index: 1055 !important;
  }
  .ant-modal-wrap {
    z-index: 1055 !important;
  }
}
</style>
