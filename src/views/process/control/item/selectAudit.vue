<template>
  <div>
    <yn-modal
      :title="$t_common('select') + $t_process('process_status')"
      :visible="visible"
      class="select-audit"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <template slot="footer">
        <yn-button key="back" @click="handleCancel">
          {{ $t_common("cancel") }}
        </yn-button>
        <yn-button
          key="submit"
          type="primary"
          :disabled="checkedList.length === 0"
          :loading="confirmLoading"
          @click="handleOk"
        >
          {{ $t_common("ok") }}
        </yn-button>
      </template>
      <p class="select-audit-title">
        {{ $t_process("reject_to_before") }}
      </p>
      <yn-checkbox-group
        v-model="checkedList"
        :options="auditAbleState"
        @change="onChange"
      />
    </yn-modal>
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-checkbox-group/";
import { mapMutations, mapState } from "vuex";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      checkedList: [],
      confirmLoading: false
    };
  },
  computed: {
    ...mapState({
      tableFilter: state => state.processStore.tableFilter,
      auditAbleState: state => state.processStore.auditAbleState,
      batchType: state => state.processStore.batchType
    })
  },
  watch: {
    visible(value) {
      if (value) {
        this.checkedList = [];
        this.setAuditTableFilter({
          nodeNameList: []
        });
      }
    }
  },
  methods: {
    ...mapMutations("processStore", [
      "setAuditTableFilter",
      "setBatchType",
      "setTableFilter",
      "setNodeNameSummary"
    ]),
    onChange() {},
    handleOk(e) {
      this.confirmLoading = true;
      this.setAuditTableFilter({
        nodeNameList: this.checkedList
      });
      this.setTableFilter({
        ...this.tableFilter,
        nodeNameList: this.checkedList
      });
      this.setNodeNameSummary(this.auditAbleState);
      this.setBatchType({
        operatorCode: "turndown",
        operatorName: this.$t_process("reject")
      });
      this.$emit("update:visible", false);
      this.confirmLoading = false;
    },
    handleCancel(e) {
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style lang="less" scoped>
.select-audit {
  /deep/ .ant-modal-body {
    max-height: 50vh;
  }
}
.select-audit-title {
  height: 1.375rem;
  font-weight: 400;
  font-size: 0.875rem;
  color: @yn-text-color-secondary;
  text-align: left;
  line-height: 1.375rem;
  margin-bottom: 1rem;
}
/deep/ .ant-checkbox-group {
  display: flex;
  flex-direction: column;
  .ant-checkbox-wrapper + .ant-checkbox-wrapper {
    margin-left: 0;
  }
  .ant-checkbox {
    top: 0;
  }
  .ant-checkbox-group {
    align-items: center;
    flex-direction: row;
    margin-bottom: 1rem;
  }
}
</style>
