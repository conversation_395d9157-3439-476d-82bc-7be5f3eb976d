<template>
  <div
    v-show="
      !isReport &&
        (processNodeStatusList.length > 0 || processCheckStatusList.length > 0)
    "
    class="parent"
  >
    <div class="child-wrap wrap1" :style="{ 'flex-basis': flexBasis.pre }">
      <div class="child">
        <div class="itemi fix">
          <span class="itemi-title">{{ $t_process("all_process") }}</span>
          <span class="itemi-num fix">
            {{
              processNodeStatusList.reduce(
                (pre, next) => pre + next.statsNum,
                0
              ) | toThousands
            }}
          </span>
          <yn-divider type="vertical" class="itemi-divider" />
        </div>
        <div class="group">
          <div
            v-for="(item, index) in processNodeStatusList"
            :key="index"
            class="itemi"
          >
            <span :class="['itemi-title', STATUSCLASS[item.statsNode]]">{{
              item.statsName
            }}</span>
            <span class="itemi-num">
              {{ item.statsNum | toThousands }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="child-wrap wrap2" :style="{ 'flex-basis': flexBasis.next }">
      <div class="child">
        <div class="itemi fix">
          <span class="itemi-title">{{ $t_process("all_status") }}</span>
          <span class="itemi-num fix">
            {{
              processCheckStatusList.reduce(
                (pre, next) => pre + next.statsNum,
                0
              ) | toThousands
            }}
          </span>
          <yn-divider type="vertical" class="itemi-divider" />
        </div>
        <div class="group">
          <div
            v-for="(item, index) in processCheckStatusList"
            :key="index"
            class="itemi"
          >
            <span :class="['itemi-title', CHECKSTATUSCLASS[item.statsNode]]">{{
              item.statsName
            }}</span>
            <span class="itemi-num">
              {{ item.statsNum | toThousands }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
const STATUSCLASS = Object.freeze({
  not_started: "unstart",
  processing: "processing",
  submitted: "posted",
  pending_review: "onaccept",
  "": "none"
});
export default {
  props: {
    processCheckStatusList: {
      type: Array,
      default: () => []
    },
    processNodeStatusList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      STATUSCLASS,
      CHECKSTATUSCLASS: Object.freeze({
        0: "uncheck",
        1: "pass",
        2: "unpass",
        3: "warning"
      })
    };
  },
  computed: {
    ...mapState("processStore", {
      isReport: state => state.isReport
    }),
    flexBasis() {
      const pre = this.processNodeStatusList.length;
      const next = this.processCheckStatusList.length;
      const sum = pre + next;
      const preBase = Math.floor((1 / sum) * pre * 100);
      const nextBase = 100 - preBase;
      return {
        pre: preBase + "%",
        next: nextBase + "%"
      };
    }
  }
};
</script>

<style lang="less" scoped>
.parent {
  min-height: 5.5rem;
  margin-bottom: 0.75rem;
  display: flex;
  width: calc(100%);
  flex-shrink: 0;
  overflow: auto;
  overflow-y: hidden;
  .wrap1 {
    flex-basis: 62%;
    margin-right: 0.75rem;
  }
  .wrap2 {
    flex-basis: 38%;
  }
  .group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    flex-shrink: 0;
  }
  .child-wrap {
  }
  .child {
    flex-shrink: 0;
    height: 5.5rem;
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    background: @yn-body-background;
    border-radius: 4px;
    .itemi {
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      margin: 0 0.625rem;
      height: 5rem;
      width: 5.625rem;
      padding: 0.9375rem 0;
      &.fix {
        position: relative;
      }
    }
    .itemi-title {
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 0.875rem;
      color: @yn-text-color-secondary;
      letter-spacing: 0;
      line-height: 1.375rem;
      font-weight: 400;
      margin-bottom: 0.5rem;
      white-space: nowrap;
    }
    .itemi-num {
      font-size: 1.125rem;
      padding-left: 1rem;
      overflow: hidden;
      text-overflow: ellipsis;
      color: @yn-text-color;
      letter-spacing: 0;
      line-height: 1.625rem;
      font-weight: 600;
      &.fix {
        padding-left: 0;
      }
    }
    .itemi-divider {
      height: 50%;
      position: absolute;
      top: 25%;
      right: -0.625rem;
    }
  }

  .uncheck::before,
  .pass::before,
  .unpass::before,
  .warning::before,
  .unstart::before,
  .processing::before,
  .posted::before,
  .none::before,
  .onaccept::before {
    display: inline-block;
    content: "";
    width: 0.5rem;
    height: 0.5rem;
    margin-right: 0.5rem;
    border-radius: 50%;
  }
  .unstart::before {
    background: @yn-disabled-color;
  }
  .processing::before {
    background: @yn-primary-color;
  }
  .posted::before {
    background: @yn-success-color;
  }
  .onaccept::before {
    background: @yn-warning-color;
  }
  .uncheck {
    color: @yn-text-color;
  }
  .pass {
    color: @yn-success-color;
  }
  .unpass {
    color: @yn-error-color;
  }
  .warning {
    color: @yn-warning-color;
  }
  .uncheck::before {
    background: @yn-disabled-color;
  }
  .pass::before {
    background: @yn-success-color;
  }
  .unpass::before {
    background: @yn-error-color;
  }
  .warning::before {
    background: @yn-warning-color;
  }
}
</style>
