<template>
  <header class="process-top">
    <div class="process-title">
      <yn-button type="text" class="goback" size="small" @click="goback">
        <yn-icon type="left" />{{ $t_common("back") }}
      </yn-button>
      <yn-divider type="vertical" class="mydv" />
      {{ record.writeForm ? $t_process("input") : $t_common("view") }}
    </div>
    <div class="process-filter">
      <span v-for="item in $data.$pageDimList" :key="item" class="filter-item">
        <span class="filter-key">{{ KEYMAP[item] }}</span>
        <span class="filter-value">{{ `[ ${viewParams[item] || ""} ]` }}</span>
      </span>
      <span>
        <span class="filter-key"> {{ $t_process("company") }} </span>
        <span class="filter-value">{{
          `[ ${(record.memberName && record.memberName.v) || ""} ]`
        }}</span>
      </span>
    </div>
  </header>
</template>

<script>
import { mapMutations } from "vuex";
const PAGE_DIM_LIST = ["version", "year", "period"];
export default {
  props: {
    record: {
      type: Object,
      default: () => ({})
    },
    viewParams: {
      type: Object,
      default: () => {
        return {
          year: "",
          version: "",
          period: ""
        };
      }
    }
  },
  data() {
    return {
      KEYMAP: Object.freeze({
        year: this.$t_common("year"),
        version: this.$t_common("version"),
        period: this.$t_common("period")
      }),
      $pageDimList: PAGE_DIM_LIST
    };
  },
  methods: {
    ...mapMutations("processStore", ["setIsReport"]),
    goback() {
      this.setIsReport(false);
    }
  }
};
</script>

<style lang="less" scoped>
.process-filter {
  padding: 0.25rem 0;
  .filter-item {
    margin-right: 1.5rem;
  }
  .filter-key {
    margin-right: 0.5rem;
    color: @yn-label-color;
  }
  .filter-value {
    color: @yn-text-color;
  }
}
.process-top {
  min-height: 5.625rem;
}
.process-title {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  height: 2.75rem;
  font-size: 1rem;
  color: @yn-text-color;
  text-align: left;
  font-weight: 600;
  padding: 0.875rem 0;

  .goback {
    margin-left: -0.25rem;
  }
  .mydv {
    height: 1rem;
    margin-right: 0.75rem;
    margin-left: 0.25rem;
    top: 0;
  }
}
</style>
