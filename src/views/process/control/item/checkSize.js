const BATE = 1024 * 1024;
import UiUtils from "yn-p1/libs/utils/UiUtils";
import _debounce from "lodash/debounce";

export default {
  data() {
    return {
      mixinOver: false,
      mixinHasOver: false
    };
  },
  methods: {
    mixinIsOver50m(list, addStatus) {
      const isOver = list.reduce((pre, next) => pre + next.size, 0) / BATE > 50;
      if (addStatus) {
        if (isOver) {
          list.forEach(item => {
            item.status = "error";
            item.response = this.$t_process("file_unover50", [50]);
          });
        } else {
          list.forEach(item => {
            item.status = "done";
            delete item.response;
          });
        }
      }
      return isOver;
    },
    mixinHasOver50m(fileList) {
      const isOver = fileList.some(item => this.mixinIsOver50m([item]));
      return isOver;
    },
    mixinValidateOverSize(fileList, addStatus = true) {
      const isOver = this.mixinIsOver50m(fileList, addStatus);
      this.mixinOver = isOver;
      isOver && this.showMessage();
      return isOver;
    },
    mixinValidateHasOverSize(fileList) {
      const isOver = this.mixinHasOver50m(fileList);
      this.mixinHasOver = isOver;
      isOver && this.showMessage();
      return isOver;
    },
    showMessage: _debounce(function() {
      UiUtils.errorMessage(this.$t_process("file_unover50", [50]));
    }, 300)
  }
};
