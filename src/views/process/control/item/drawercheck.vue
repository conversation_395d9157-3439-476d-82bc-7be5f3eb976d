<template>
  <yn-drawer
    :title="localtitle"
    placement="right"
    :closable="true"
    :width="verificationType === 'result' ? '27.5rem' : '45rem'"
    :visible="visible"
    class="drawer-check"
    @close="onClose"
  >
    <div class="dataarea">
      <yn-table
        ref="mytable"
        rowKey="id"
        bordered
        :columns="columns"
        :data-source="tabledata"
        :expandedRowKeys="expandedRowKeys"
        :loading="spinning"
        :scroll="{ y: scrollHeight }"
        @expandedRowsChange="expandedRowsChange"
      >
        <div slot="table.filterDropdown" class="menuwrap">
          <yn-menu>
            <yn-menu-item
              key="zero"
              class="dropmenu"
              @click="() => handleSearch()"
            >
              <span type="text" class="txtmodel">
                {{ $t_process("suppress") }}
              </span>
            </yn-menu-item>
            <yn-menu-item
              key="all"
              class="dropmenu"
              @click="() => handleReset()"
            >
              <span type="text" class="txtmodel">
                {{ $t_process("view_all") }}
              </span>
            </yn-menu-item>
          </yn-menu>
        </div>
        <span slot="leftFormValue" slot-scope="text, record" :class="['money']">
          <form-item
            :pavingData="pavingData"
            :text="text"
            :params="params"
            :href="record.leftFormUrl"
            :data="data"
          />
        </span>
        <span
          slot="rightFormValue"
          slot-scope="text, record"
          :class="['money']"
        >
          <form-item
            :pavingData="pavingData"
            :text="text"
            :params="params"
            :href="record.rightFormUrl"
            :data="data"
          />
        </span>
        <span
          slot="resultFormValue"
          slot-scope="text, record"
          :class="['money']"
        >
          <form-item
            :pavingData="pavingData"
            :text="text"
            :params="params"
            :href="record.resultFormUrl"
            :data="data"
          />
        </span>
        <span
          slot="verificationName"
          slot-scope="text"
          :title="text"
          class="name"
        >
          {{ text }}
        </span>
      </yn-table>
    </div>
  </yn-drawer>
</template>
<script>
import { polling } from "@/utils/common";
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-menu/";

import _cloneDeep from "lodash/cloneDeep";
import precessService from "@/services/process";
import { mapActions, mapState } from "vuex";
import FormItem from "./formItem.vue";

export default {
  components: { FormItem },
  props: {
    title: {
      type: String,
      default: ""
    },
    params: {
      type: Object,
      default: () => ({})
    },
    staticData: {
      type: Array,
      default: () => null
    },
    data: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      columnMap: Object.freeze({
        result: [
          {
            title: this.$t_process("validation_accounts"),
            dataIndex: "verificationName",
            key: "verificationName",
            scopedSlots: {
              customRender: "verificationName"
            }
          },
          {
            title: this.$t_process("results"),
            dataIndex: "resultFormValue",
            key: "resultFormValue",
            align: "right",
            width: "9.375rem",
            scopedSlots: {
              customRender: "resultFormValue",
              filterDropdown: "filterDropdown"
            }
          }
        ],
        equation: [
          {
            title: this.$t_process("validation_accounts"),
            dataIndex: "verificationName",
            key: "verificationName",
            scopedSlots: {
              customRender: "verificationName"
            }
          },

          {
            title: this.$t_process("left_hand_side"),
            dataIndex: "leftFormValue",
            key: "leftFormValue",
            align: "right",
            width: "9.375rem",
            scopedSlots: {
              customRender: "leftFormValue"
            }
          },
          {
            title: this.$t_process("right_hand_side"),
            dataIndex: "rightFormValue",
            key: "rightFormValue",
            align: "right",
            width: "9.375rem",
            scopedSlots: {
              customRender: "rightFormValue"
            }
          },
          {
            title: this.$t_process("results"),
            dataIndex: "resultFormValue",
            key: "resultFormValue",
            align: "right",
            width: "9.375rem",
            scopedSlots: {
              customRender: "resultFormValue",
              filterDropdown: "filterDropdown"
            }
          }
        ]
      }),
      verificationType: "result",
      spinning: false,
      tabledata: [],
      scrollHeight: false,
      expandedRowKeys: []
    };
  },
  computed: {
    ...mapState({
      pavingData: state => state.processStore.pavingFormList
    }),
    columns() {
      return this.columnMap[this.verificationType];
    },
    localtitle() {
      return this.title || this.$t_common("check") + this.data.checkState.v;
    }
  },
  watch: {
    visible: {
      immediate: true,
      async handler(value) {
        if (value) {
          this.spinning = true;
          await this.getFormList();
          this.search();
        }
      }
    }
  },
  methods: {
    ...mapActions({
      getFormList: "processStore/getFormList"
    }),
    async getProcessCheckList() {
      const {
        data: { data }
      } = await precessService("getProcessCheckList", {
        ...this.params,
        objectId: this.data.objectId
      });
      return (data || []).map((item, index) => {
        item.objectId = index;
        return item;
      });
    },
    async search() {
      const data = this.staticData
        ? this.staticData
        : await this.getProcessCheckList();
      this.verificationType = data[0]
        ? data[0].verificationType || "result"
        : "result";
      this.setExpand(data);
      this.tabledata = data;
      this._tableDataCache = _cloneDeep(this.tabledata);
      this.spinning = false;
      // this.setScrollHeight();
    },

    onFilter(value, record) {
      this.spinning = true;
      this.spinning = false;
      return true;
    },
    onClose() {
      this.tabledata = [];
      this.expandedRowKeys = [];
      this.$emit("update:visible", false);
    },
    setExpand(data) {
      // this.expandedRowKeys = [];
      // this.$nextTick(() => {
      //   const expandedRowKeys = [];
      //   this.getSafeTree(data, expandedRowKeys);
      //   this.expandedRowsChange(expandedRowKeys);
      // });
      const expandedRowKeys = [];
      this.getSafeTree(data, expandedRowKeys);
      this.$nextTick(() => {
        this.expandedRowsChange(expandedRowKeys);
      });
    },
    handleSearch() {
      this.tabledata = this.removeZeroNode(_cloneDeep(this.tabledata));
    },
    handleReset() {
      this.tabledata = _cloneDeep(this._tableDataCache);
    },
    expandedRowsChange(rows) {
      if (!this.tabledata.length) return;
      this.expandedRowKeys = rows;
    },
    // dom相关
    setScrollHeight() {
      polling(() => Promise.resolve(this.$el.querySelector), 100).then(
        query => {
          const dataarea = this.$el.querySelector(".dataarea");
          const header = 37;
          const redux = header;
          setTimeout(() => {
            if (
              dataarea.offsetHeight <=
              this.$refs.mytable.$el.offsetHeight + 3
            ) {
              this.scrollHeight = dataarea.offsetHeight - redux;
            } else {
              this.scrollHeight = false;
            }
          }, 0);
        }
      );
    },
    // tool
    removeZeroNode(list) {
      if (!list || list.length === 0) return;
      for (let index = 0; index < list.length; index++) {
        if (list[index].resultFormValue === "0") {
          if (list[index].children) {
            list.push(...list[index].children);
          }
          list.splice(index, 1);
          index--;
        } else {
          this.removeZeroNode(list[index].children);
        }
      }
      return list;
    },
    getSafeTree(tree, expandedRowKeys) {
      if (tree) {
        for (const item of tree) {
          expandedRowKeys && expandedRowKeys.push(item.id);
          if (item.children && item.children.length > 0) {
            this.getSafeTree(item.children, expandedRowKeys);
          } else {
            delete item.children;
          }
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.dataarea {
  height: 0;
  flex: 1;
  overflow-x: hidden;
  overflow-y: scroll;
}
.drawer-check {
  z-index: 1049 !important;
  //z-index: 1040 !important;
}
/deep/ .ant-drawer-wrapper-body {
  display: flex;
  flex-direction: column;
  .ant-drawer-body {
    display: flex;
    flex-direction: column;
    height: 0;
    flex: 1;
    overflow: auto;
  }
}
.maxwidth {
  width: 100%;
}
.money {
  text-align: right;
}

.menuwrap {
  padding: 0.25rem 0;
}
.dropmenu {
  margin: 0 !important;
  color: @yn-text-color;
  &:hover {
    background-color: @yn-hover-bg-color;
  }
}
</style>
