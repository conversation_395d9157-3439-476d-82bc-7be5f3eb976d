<template>
  <section v-show="batchType" class="batch">
    <span class="batch-title">
      {{ $t_common("text_has_chosen") }}
      <span class="batch-number">{{ batchNumber }}</span>
      {{ $t_common("strip") }}
    </span>
    <yn-button class="batch-cancel" @click="cancelBatch">
      {{ $t_common("cancel") }}
    </yn-button>
    <yn-button
      class="batch-submit"
      type="primary"
      :disabled="selection.length === 0"
      @click="handlerBatch"
    >
      {{ batchType.operatorName }}
    </yn-button>
  </section>
</template>

<script>
import { mapState } from "vuex";
export default {
  props: {
    selection: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState("processStore", {
      batchType: state => state.batchType,
      batchNumber: state => state.batchNumber
    })
  },
  methods: {
    handlerBatch() {
      this.$emit("ok", this.batchType);
    },
    cancelBatch() {
      this.$emit("cancel");
    }
  }
};
</script>

<style lang="less" scoped>
.batch {
  display: flex;
  align-items: center;
  height: 3.25rem;
  padding: 1rem 2rem;
  background: @yn-component-background;
  box-shadow: inset 0 1px 0 0 @yn-border-color-base;
  .batch-title {
    font-size: 0.875rem;
    margin-right: 1rem;
  }
  .batch-number {
  }
  .batch-cancel {
    width: 5rem;
    margin-right: 0.5rem;
  }
  .batch-submit {
    width: 5rem;
  }
}
</style>
