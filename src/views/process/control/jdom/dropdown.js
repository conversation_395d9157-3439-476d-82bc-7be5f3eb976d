import { genDOM } from "./utils";
import "./style.less";

/**
 *
 * @param {*} icondom 触发dom
 * @param {*} domlist domlist, 支持dom节点和函数式延迟dom,防止无意义渲染
 * @returns
 */

export default function dropdown(icondom, domlist) {
  const dropdown = genDOM(`<span class="dom-dropdown"></span>`);
  const ul =
    document.body.querySelector(".dom-dropdown-list") ||
    genDOM(`<ul class="dom-dropdown-list"></ul>`);
  icondom.addEventListener("click", e => {
    ul.innerHTML = null;
    const rect = icondom.getBoundingClientRect();
    const fg = document.createDocumentFragment();
    for (const dom of domlist) {
      const li = document.createElement("li");
      li.className = "dom-dropdown-list-item";
      if (typeof dom === "function") {
        li.appendChild(dom());
      } else {
        li.appendChild(dom);
      }

      fg.appendChild(li);
    }
    ul.style.top = rect.top + rect.height + 5 + "px";
    ul.style.left = rect.right - rect.width + "px";
    ul.appendChild(fg);
    ul.classList.add("dom-dropdown-show");

    e.stopPropagation();
    return false;
  });
  window.addEventListener("click", e => {
    ul && ul.classList.remove("dom-dropdown-show");
  });
  document.body.appendChild(ul);
  dropdown.appendChild(icondom);

  return dropdown;
}
