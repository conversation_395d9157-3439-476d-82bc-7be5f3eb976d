import Vue from "vue";

/* eslint-disable indent */
export const isDOM =
  typeof HTMLElement === "object"
    ? function(obj) {
        return obj instanceof HTMLElement;
      }
    : function(obj) {
        return (
          obj &&
          typeof obj === "object" &&
          obj.nodeType === 1 &&
          typeof obj.nodeName === "string"
        );
      };

export function genDOM(str) {
  const fg = document.createElement("div");
  fg.innerHTML = str;
  return fg.children[0];
}

export function genVDOM(component, id) {
  window.consolidation_vdom || (window.consolidation_vdom = {});
  if (window.consolidation_vdom[id]) {
    window.consolidation_vdom[id].$destroy();
  }
  const instance = genInstance(component);
  id !== undefined && (window.consolidation_vdom[id] = instance);
  return instance.$el;
}

export function genInstance(component, id) {
  window.consolidation_vdom || (window.consolidation_vdom = {});
  if (window.consolidation_vdom[id]) {
    window.consolidation_vdom[id].$destroy();
  }
  const Component = Vue.extend(component);
  const instance = new Component();
  instance.$mount();
  id !== undefined && (window.consolidation_vdom[id] = instance);
  return instance;
}
