/** icon*/

.dom-icon {
  display: inline-block;
  line-height: 32px;
  font-size: 16px;
  height: 32px;
  padding: 0 8px;
  border-radius: 0.25rem;
  background: transparent;
  cursor: pointer;
  &:hover {
    color: @yn-primary-color !important;
    background: @yn-icon-bg-color !important;
  }
  &.readonly:hover {
    color: inherit !important;
    background: transparent !important;
  }
  .svg-icon {
    font-size: 16px;
  }
}

/**button*/

.dom-button {
  display: inline-block;
  vertical-align: super;
  line-height: 32px;
  height: 32px;
  position: relative;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  user-select: none;
  padding: 0 12px;
  font-size: 14px;
  border-radius: 0.25rem;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  &:hover {
    color: @yn-primary-color !important;
    background: @yn-icon-bg-color !important;
  }
}

/**dropdown*/

.dom-dropdown {
  display: inline-block;
  position: relative;
}
.dom-dropdown-list {
  position: absolute;
  display: none;
  box-sizing: border-box;
  margin: 0;
  color: @yn-text-color;
  font-size: 0.875rem;
  font-variant: tabular-nums;
  line-height: 1.5;
  font-feature-settings: "tnum";
  z-index: 1050;
  padding: 0.25rem 0;
  text-align: left;
  list-style-type: none;
  background-color: @yn-body-background;
  background-clip: padding-box;
  border-radius: 0.25rem;
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.149);
  &:before {
    position: absolute;
    top: -0.4375rem;
    right: 0;
    bottom: -0.4375rem;
    left: -0.4375rem;
    z-index: -9999;
    opacity: 0.0001;
    content: " ";
  }
  .dom-dropdown-list-item {
    margin: 0;
    padding: 0.3125rem 0.5rem;
    color: #1a253b;
    font-weight: 400;
    font-size: 0.875rem;
    line-height: 1.375rem;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s;
    &:hover {
      background-color: @yn-hover-bg-color;
    }
  }
  &.dom-dropdown-show {
    display: block;
  }
  &.dom-dropdown-hidden {
    display: none;
  }
}

/**popover*/

.dom-popover {
  display: none;
  position: absolute;
  color: @yn-text-color-secondary;
  font-size: 0.875rem;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  font-feature-settings: "tnum";
  padding: 1rem;
  z-index: 1030;
  font-weight: 400;
  white-space: normal;
  text-align: left;
  cursor: auto;
  user-select: text;
  z-index: 999;
  background: @yn-body-background;
  box-shadow: 0 2px 8px hsla(0, 0%, 0%, 0.15);
  box-sizing: border-box;
  padding-bottom: 10px;
  min-width: 11.875rem;
  max-width: 22.5rem;

  &:before {
    position: absolute;
    top: -0.4375rem;
    right: 0;
    bottom: -0.4375rem;
    left: -0.4375rem;
    z-index: -9999;
    opacity: 0.0001;
    content: " ";
  }
  &.dom-popover-show {
    display: block;
  }
  &.dom-popover-hidden {
    display: none;
  }
}
