import { isType } from "@/utils/common";
import { isDOM } from "./utils";
import "./style.less";
/**
 *
 * @param {*} text domArray,domObject,string,function
 * @param {*} props 冗余参数
 * @returns
 */

export default function button(text, props) {
  const textValue = typeof text === "function" ? text() : text;
  const buttondom = document.createElement("span");
  buttondom.className = props.class
    ? "dom-button " + props.class
    : "dom-button";
  for (const key in props.style) {
    buttondom.style[key] = props.style[key];
  }
  if (isType(textValue, "Array")) {
    textValue.forEach(element => {
      if (isDOM(element)) {
        buttondom.appendChild(element);
      } else {
        buttondom.appendChild(document.createTextNode(element || ""));
      }
    });
  } else if (isDOM(textValue)) {
    buttondom.appendChild(buttondom);
  } else {
    buttondom.textContent = textValue;
  }
  return buttondom;
}
