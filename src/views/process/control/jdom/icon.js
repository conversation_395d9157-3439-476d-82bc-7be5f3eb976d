import "./style.less";

function genIcon(icon) {
  return `<i class="iconfont" style="top:0;white-space:nowrap;width:16px;height:16px;font-size: 16px;">
    <svg class="icon" aria-hidden="true">
      <use xlink:href="#${icon}"></use>
    </svg>
  <i/>`;
}

/**
 *
 * @param {*} icon icon名
 * @param {*} props 类组件props，后期拓展
 * @returns icon dom
 */
export default function icon(icon, props) {
  const icondom = document.createElement("span");
  const _isIconBtn =
    typeof props.isIconBtn === "boolean" ? props.isIconBtn : true;
  icondom.className = props.class ? "dom-icon " + props.class : "dom-icon";
  for (const key in props.style) {
    icondom.style[key] = props.style[key];
  }
  if (!_isIconBtn) {
    icondom.classList.add("readonly");
  }

  icondom.innerHTML = genIcon(icon);
  return icondom;
}
