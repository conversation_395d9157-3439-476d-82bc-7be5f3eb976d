import { genDOM, genVDOM } from "./utils";
import "./style.less";
import { Date } from "core-js";

/**
 *
 * @param {*} icondom 触发dom
 * @param {*} domlist domlist, 支持dom节点和函数式延迟dom,防止无意义渲染
 * @returns
 */

export default function popover(icondom, component) {
  const classNameId = `.dom-popover-${Date.now()}`;
  const popoverdom = document.createElement("span");
  let contentdom = document.body.querySelector(classNameId);
  function listener(e) {
    contentdom && contentdom.classList.remove("dom-popover-show");
    window.removeEventListener("click", listener);
  }
  function handler(e) {
    if (!contentdom) {
      contentdom = genDOM(`<div class="dom-popover ${classNameId}"></div>`);
      contentdom.appendChild(genVDOM(component));
      contentdom.addEventListener("click", handler);
      document.body.appendChild(contentdom);
    }
    const rect = icondom.getBoundingClientRect();
    contentdom.classList.add("dom-popover-show");
    setTimeout(() => {
      contentdom.style.top = rect.top - contentdom.offsetHeight - 5 + "px";
      contentdom.style.left = rect.left + "px";
    }, 0);
    window.addEventListener("click", listener);
    e.stopPropagation();
    return false;
  }
  icondom.addEventListener("click", handler);
  popoverdom.appendChild(icondom);

  return popoverdom;
}
