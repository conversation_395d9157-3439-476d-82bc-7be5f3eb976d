<template>
  <div class="search-filter">
    <section class="filter">
      <div>
        <yn-button
          v-if="params.from === 'console'"
          type="text"
          class="goback"
          @click="goback"
        >
          <yn-icon type="left" />{{ $t_common("back") }}
        </yn-button>
        <yn-divider
          v-if="params.from === 'console'"
          type="vertical"
          class="mydv"
        />
        <span class="custitle">{{ $t_process("consolidation_process") }}</span>
      </div>
      <yn-divider type="vertical" class="divider" />
      <yn-popconfirm
        placement="bottomLeft"
        :visible="popVisible"
        :okText="$t_common('ok')"
        :cancelText="$t_common('cancel')"
        overlayClassName="my-popconfirm"
        @cancel="cancelEvent"
        @click.native.stop
        @confirm="search"
      >
        <label slot="icon"></label>
        <template slot="title">
          <yn-form
            :form="filterForm"
            layout="horizontal"
            class="filter-form"
            @click.native.stop
          >
            <yn-form-item
              :label="$t_common('version')"
              :labelCol="formItemLayout.labelCol"
              :wrapperCol="formItemLayout.wrapperCol"
              @click.native.stop
            >
              <asyn-select-dimMember
                v-decorator="['version', { initialValue: povObj.version }]"
                dimCode="Version"
                :allowClear="false"
                isSetDefaultVal
                class="my-select"
                :nonleafselectable="false"
                @click.native.stop="clickForm"
                @changeVal="handlerChange"
              />
            </yn-form-item>
            <yn-form-item
              :label="$t_common('year')"
              :labelCol="formItemLayout.labelCol"
              :wrapperCol="formItemLayout.wrapperCol"
              @click.native.stop
            >
              <asyn-select-dimMember
                v-decorator="['year', { initialValue: povObj.year }]"
                dimCode="Year"
                :allowClear="false"
                isSetDefaultVal
                class="my-select"
                :nonleafselectable="false"
                @changeVal="handlerChange"
                @click.native.stop="clickForm"
              />
            </yn-form-item>
            <yn-form-item
              :label="$t_common('period')"
              :labelCol="formItemLayout.labelCol"
              :wrapperCol="formItemLayout.wrapperCol"
              @click.native.stop
            >
              <asyn-select-dimMember
                v-decorator="['period', { initialValue: povObj.period }]"
                dimCode="Period"
                :allowClear="false"
                isSetDefaultVal
                :nonleafselectable="false"
                class="my-select"
                @changeVal="handlerChange"
                @click.native.stop="clickForm"
              />
            </yn-form-item>
          </yn-form>
        </template>
        <div class="bread" @click="popVisible = true">
          <span class="item"> {{ selectedMap[formValue.version] }} </span> /
          <span class="item"> {{ selectedMap[formValue.year] }} </span> /
          <span class="item"> {{ selectedMap[formValue.period] }} </span>
          <svg-icon type="down" class="open-tilter" :isIconBtn="false" />
        </div>
      </yn-popconfirm>
      <yn-divider type="vertical" class="divider" />
      <svg-icon type="icon-shuaxin" class="refresh" @click.native="refresh" />
    </section>
    <aside class="action">
      <yn-dropdown
        placement="bottomCenter"
        :disabled="!!batchType || batchList.length === 0"
      >
        <yn-button>
          {{ $t_process("batch_operation") }} <yn-icon type="down" />
        </yn-button>
        <yn-menu slot="overlay">
          <yn-menu-item
            v-for="item in batchList"
            :key="item.operatorCode"
            @click="handlerAction(item)"
          >
            <span>{{ item.operatorName }}</span>
          </yn-menu-item>
        </yn-menu>
      </yn-dropdown>
    </aside>

    <selectAudit :visible.sync="visible" />
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-page-list/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-breadcrumb/";
import "yn-p1/libs/components/yn-popconfirm/";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
import { mapMutations, mapState } from "vuex";
import commonService from "@/services/common";

export default {
  components: {
    AsynSelectDimMember,
    selectAudit: () => import("./item/selectAudit.vue")
  },
  data() {
    return {
      visible: false,
      formItemLayout: {
        labelCol: {
          span: 4
        },
        wrapperCol: {
          span: 20
        }
      },
      popVisible: false,
      filterForm: this.$form.createForm(this, "filterForm"),
      selectedMap: {},
      selectedMapCache: {},
      formValue: {},
      formValueCache: {},
      povObj: {}
    };
  },
  computed: {
    ...mapState({
      batchList: state => state.processStore.batchList,
      batchType: state => state.processStore.batchType,
      values: state => state.processStore.params,
      viewParams: state => state.processStore.viewParams
    }),
    params() {
      return this.getParams(location.href);
    }
  },
  watch: {
    popVisible(value) {
      value &&
        this.$nextTick(() => {
          this.filterForm.setFieldsValue(this.formValue);
        });
    }
  },
  async created() {
    this.setDefault();
    this.autoClose();
  },
  methods: {
    ...mapMutations({
      setBatchType: "processStore/setBatchType",
      setParams: "processStore/setParams",
      setViewParams: "processStore/setViewParams"
    }),
    getHistory() {
      this.povObj = this.values;
      this.formValueCache = this.values;
      for (const key in this.values) {
        this.selectedMapCache[this.values[key]] = this.viewParams[key];
      }
    },
    setDefault() {
      if (Object.keys(this.values).length > 0) {
        this.getHistory();
        this.search();
      } else {
        // 请求pov的值，设置默认选中结果,初始化
        const DIMS = ["version", "year", "period"];
        this.selectUserPov(data => {
          DIMS.forEach(item => {
            const id = this.params[item] || data[item].memberId;
            const hasquery = this.params[item + "label"] !== undefined;
            // 更新表单默认值，同步默认值到表单缓存
            this.selectedMapCache[id] =
              (hasquery && decodeURIComponent(this.params[item + "label"])) ||
              data[item].dimMemberRealName;
            this.formValueCache[item] = id;
            this.$set(this.povObj, item, id);
          });
        }).then(() => {
          Promise.all([
            this.getFirstYear(),
            this.getFirstPeriod(),
            this.getFirstVersion()
          ]).then(() => {
            this.search();
          });
        });
      }
    },
    getParams(url) {
      const params = {};
      const index = url.lastIndexOf("?");
      url
        .slice(index + 1)
        .split("&")
        .forEach(item => {
          const tmp = item.split("=");
          params[tmp[0]] = tmp[1];
        });
      return params;
    },
    handlerChange(values, items) {
      // 表单内容改变缓存更新值, 在触发保存事件后再更新值，取消则还原
      const { dimMemberRealName = "", objectId = "", dimCode } = items[0] || {};
      this.selectedMapCache[objectId] = dimMemberRealName; // 缓存选中key
      this.formValueCache[dimCode.toLowerCase()] = values; // 缓存选中value
    },
    search() {
      this.selectedMap = { ...this.selectedMapCache };
      this.formValue = { ...this.formValueCache };
      this.setParams(this.formValue);
      this.setViewParams({
        year: this.selectedMap[this.formValue.year],
        version: this.selectedMap[this.formValue.version],
        period: this.selectedMap[this.formValue.period]
      });
      this.$emit("search");
      this.popVisible = false;
      this.saveOrUpdateUserPov(this.formValue);
    },
    refresh() {
      this.$emit("search");
    },
    goback() {
      window.parent.postMessage(
        JSON.stringify({
          actionType: "close",
          data: { openOrgin: this.params.openOrgin }
        }),
        "*"
      );
    },
    cancelEvent() {
      this.$emit("update:visible", false);
      this.popVisible = false;
    },
    clickForm() {
      Array.from(document.querySelectorAll(".yn-select-menu")).forEach(dom => {
        dom.onclick = e => {
          e.stopPropagation();
          return false;
        };
      });
    },
    autoClose() {
      document.body.addEventListener("click", e => {
        if (
          e.target.classList.contains("ant-popover-message-title") ||
          e.target.classList.contains("ant-popover-buttons") ||
          e.target.classList.contains("ant-popover-inner-content")
        ) {
          e.stopPropagation();
          return false;
        }
        this.popVisible = false;
      });
    },
    handlerAction(item) {
      if (item.operatorCode === "turndown") {
        this.visible = true;
      } else {
        this.setBatchType(item);
      }
    },
    async getFirstYear() {
      if (this.povObj.year) return true;
      await commonService("getFirstDimMember", {
        dimCode: "Year",
        needPermission: false,
        needFilterAudittrail: false,
        needFilterShared: false
      }).then(res => {
        const { data } = res;
        this.selectedMapCache[data.objectId] = data.dimMemberRealName;
        this.formValueCache["year"] = data.objectId;
        this.$set(this.povObj, "year", data.objectId);
      });
    },
    async getFirstPeriod() {
      if (this.povObj.period) return true;
      await commonService("getFirstDimMember", {
        dimCode: "Period",
        needPermission: false,
        needFilterAudittrail: false,
        needFilterShared: false
      }).then(res => {
        const { data } = res;
        this.selectedMapCache[data.objectId] = data.dimMemberRealName;
        this.formValueCache["period"] = data.objectId;
        this.$set(this.povObj, "period", data.objectId);
      });
    },
    async getFirstVersion() {
      if (this.povObj.version) return true;
      await commonService("getFirstDimMember", {
        dimCode: "Version",
        needPermission: false,
        needFilterAudittrail: false,
        needFilterShared: false
      }).then(res => {
        const { data } = res;
        this.selectedMapCache[data.objectId] = data.dimMemberRealName;
        this.formValueCache["version"] = data.objectId;
        this.$set(this.povObj, "version", data.objectId);
      });
    }
  }
};
</script>

<style lang="less" scoped>
.search-filter {
  position: relative;
  display: flex;
  align-items: center;
  margin-top: 1.25rem;
  height: 3.75rem;
  padding: 1rem 2rem 1.125rem;
  background: @yn-body-background;
  .filter {
    display: flex;
    align-items: center;
    height: 1.5rem;
    line-height: 1.5rem;
    .divider {
      height: 1.5rem;
      margin-right: 1rem;
      margin-left: 1rem;
      top: 0;
    }
    .bread {
      color: @yn-link-color;
      font-weight: 400;
      height: 1.5rem;
      font-size: 0.875rem;
      text-align: left;
      cursor: pointer;
      .item {
      }
    }
    .open-tilter {
      color: @yn-link-color;
      /deep/ .svg-icon {
        font-size: 0.8rem;
      }
    }
  }
  .action {
    height: 2.25rem;
    margin-left: auto;
  }
  .refresh {
    color: @yn-label-color;
    &:hover {
      color: @yn-primary-color;
    }
  }
}
/deep/ .page-list-header-title {
  height: auto;
  margin-bottom: 0.5rem;
  line-height: 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: @yn-text-color;
}
.custitle {
  display: inline-block;
  max-width: 24em;
  line-height: 1.5rem;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
  font-size: 1rem;
  font-weight: 600;
  color: @yn-text-color;
}
/deep/ .mydv {
  height: 1rem;
  margin-right: 0.75rem;
  margin-left: 0.25rem;
  top: 0;
}
/deep/ .goback {
  color: @yn-label-color;
  margin-left: -0.5rem;
  &:hover {
    color: @yn-primary-color;
  }
}
</style>

<style lang="less">
.ant-popover.my-popconfirm {
  width: 15.125rem;
  .ant-popover-buttons {
    border-top: 1px solid @yn-border-color-base;
    padding: 0.5rem;
  }
  .ant-popover-message-title {
    padding: 1rem 1rem 0;
  }
  .ant-popover-inner-content {
    padding: 0;
  }
  .ant-popover-message {
    font-weight: 400;
    padding-bottom: 0;
    font-size: 0.875rem;
  }
  .filter-form {
  }
  .ant-form-item {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
  }
}
</style>
