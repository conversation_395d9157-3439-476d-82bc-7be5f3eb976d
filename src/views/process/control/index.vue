<template>
  <section class="process-control cs-container">
    <SearchFilter
      v-if="!isReport"
      ref="SearchFilter"
      class=" search-filter cs-header"
      @search="search"
    />
    <SummaryData
      class=""
      :processNodeStatusList="processNodeStatusList"
      :processCheckStatusList="processCheckStatusList"
    />
    <section v-show="!isReport" class="process-table-contain cs-body-table">
      <yn-spin :spinning="loading || !headerReady" class="spinning">
        <TreeGrid
          ref="reportGrid"
          :data="gridTableData"
          :sourceMap="mapData"
          :hasBatch="hasBatch"
          :hasCountdown="hasCountdown"
          :part="partData"
          @headerReady="handlerHeaderReady"
          @action="handlerAction"
        />
      </yn-spin>
    </section>
    <BatchFooter
      :selection="selection"
      @ok="handlerBatch"
      @cancel="cancelBatch"
    />
    <keep-alive>
      <component
        :is="componentId"
        :key="componentId"
        v-bind="componentProp"
        :visible.sync="visible"
        :setLoading="setLoading"
        @refresh="refreshState"
        @ok="handlerOk"
      />
    </keep-alive>
    <Report
      v-if="isReport"
      ref="Report"
      :record="records"
      :viewParams="viewParams"
      :params="params"
      :dimInfo="dimInfo"
      :loading.sync="reportLoading"
      @operate="handlerButton"
      @report="search"
    />
    <AsyncTask ref="task" :visible.sync="nfcvisible" />
    <DrawerView
      :visible.sync="viewProcessVisible"
      :data="viewProcessData"
      :list="viewProcessList"
    />
  </section>
</template>

<script>
import commonService from "@/services/common";
import SearchFilter from "./search";
import precessService from "@/services/process";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import "yn-p1/libs/components/yn-spin/";
import YnIconSvg from "yn-p1/libs/components/yn-icon/yn-icon-svg";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { polling, Chain } from "@/utils/common";
import { APPS } from "@/config/SETUP";
import _uniqueId from "lodash/uniqueId";
import TreeGrid from "./item/controlTable";
import { mapState, mapMutations } from "vuex";
import { getFilter } from "@/utils/process";

export default {
  components: {
    SearchFilter,
    TreeGrid,
    BatchFooter: () => import("./footer"),
    SummaryData: () => import("./item/summaryData"),
    DrawerStatus: () => import("./item/drawerstatus"),
    DrawerCheck: () => import("./item/drawercheck"),
    DrawerLog: () => import("./item/drawerlog"),
    DrawerOprate: () => import("./item/draweroprate"),
    DrawerOther: () => import("./item/drawerother"),
    DrawerView: () => import("./item/drawerview"),
    AsyncTask: () => import("../asyncTask"),
    Report: () => import("./report")
  },
  data() {
    return {
      visible: false,
      nfcvisible: false,
      reportLoading: false,
      headerReady: true,
      loading: false,
      CHECKSTATUS: Object.freeze({
        0: "not_validated",
        1: "successful",
        2: "failed",
        3: "warning"
      }),
      hasBatch: false,
      hasCountdown: false,
      records: {},
      componentProp: {},
      componentId: "",
      partData: [],
      mapData: {},
      processCheckStatusList: [],
      processNodeStatusList: [],
      gridTableData: [],
      dimInfo: {}, // key ？ value dimMemberId
      // 查看流程相关数据
      viewProcessVisible: false,
      viewProcessData: {},
      viewProcessList: []
    };
  },
  computed: {
    ...mapState({
      isReport: state => state.processStore.isReport,
      params: state => state.processStore.params,
      viewParams: state => state.processStore.viewParams,
      selection: state => state.processStore.selection,
      batchType: state => state.processStore.batchType,
      batchNumber: state => state.processStore.batchNumber,
      tableFilter: state => state.processStore.tableFilter,
      auditTableFilter: state => state.processStore.auditTableFilter,
      actionCol: state => state.processStore.actionCol,
      fileOperateCol: state => state.processStore.fileOperateCol
    }),
    filters() {
      return {
        ...this.tableFilter,
        ...{
          operateName:
            this.batchType.operatorCode === "urge"
              ? ""
              : this.batchType.operatorName
        }
      };
    },
    openUrl() {
      return this.generateURL();
    }
  },
  watch: {
    params(value) {
      this.setFilterInfo();
      this.$refs.reportGrid.clearFilter();
    },
    filters(value, oldValue) {
      // 未进入过批量操作模式或者是从批量操作退出的
      if (!value.operateName && (!oldValue || oldValue.operateName)) return;
      this.search();
    },
    isReport(value) {
      if (value) {
        document.body.click();
      }
    }
  },
  beforeDestroy() {
    this.setIsReport(false);
    this.resetProcessControlStore();
  },
  methods: {
    ...mapMutations("processStore", [
      "setIsReport",
      "resetProcessControlStore",
      "setTableFilter",
      "setBatchType",
      "setNodeNameSummary",
      "setAuditAbleState",
      "setBatchList",
      "setAuditTableFilter",
      "setCheckStateSummary"
    ]),
    async search(queryId = this.setQueryId()) {
      this.loading = true;
      const [data, stats] = await Promise.all([
        this.getProcessTree({
          ...this.params,
          ...this.filters
        }),
        this.getPageStats(null)
      ]);
      if (queryId === this.getQueryId()) {
        this.setStats(stats);
        this.gridTableData = data || [];
        this.loading = false;
        this.setFilterInfo(data);
      }
    },
    async batchUpdateRow(rows, type) {
      const data = await this.getProcessTree({
        ...this.params
      });
      this.partData = data;
      const mapData = this.$refs.reportGrid.mapData;
      rows.forEach(item => {
        this.$refs.reportGrid.updateRow(mapData[item.objectId], {
          col: this.actionCol,
          loading: false,
          action: type
        });
      });
      this.$nextTick(() => {
        this.getPageStats();
        this.setFilterInfo();
      });
    },
    async updateRow(record, type, col) {
      this.setLoading(record, col, type, true);
      const data = await this.getProcessTree({
        ...this.params
      });
      this.partData = data;
      this.$refs.reportGrid.updateRow(record, {
        col: col,
        loading: false,
        action: type
      });
      this.$nextTick(() => {
        this.setFilterInfo();
        this.getPageStats();
      });
    },
    async getProcessTree(params) {
      const {
        data: {
          data: { processTreeList, hasBatch, hasCountdown }
        }
      } = await precessService("getProcessTree", params).catch(
        e => (this.loading = false)
      );
      this.hasBatch = hasBatch;
      this.hasCountdown = hasCountdown;
      return processTreeList || [];
    },
    async queryMergeTaskBatchStatus(id, selection, batchType) {
      polling(async () => {
        const {
          data: { data: state }
        } = await commonService("queryMergeTaskStatus", id);
        if (state !== "RUNNING" && state !== "NOT_STARTED") return state;
      }, 1000).then(async state => {
        const message = await this.getMessage(id);
        let type = "success";
        if (state === "SUCCESS") {
          type = "success";
        } else if (state === "SOME_FAIL") {
          type = "warning";
        } else {
          type = "error";
        }
        UiUtils.customMessage({
          type: type,
          content: message
        });
        this.$refs.task.stopPolling();
        this.$nextTick(() => {
          this.batchUpdateRow(selection, batchType, this.actionCol);
        });
        this.isReport && this.$refs.Report.refresh();
      });
    },
    async queryMergeTaskStatus(id, record, action) {
      polling(async () => {
        const {
          data: { data: state }
        } = await commonService("queryMergeTaskStatus", id);
        if (state !== "RUNNING") return state;
      }, 1000).then(async state => {
        const message = await this.getMessage(id);
        if (state === "FINISHED") {
          UiUtils.successMessage(message);
        } else {
          UiUtils.errorMessage(message);
        }
        this.$refs.task.stopPolling();
        this.updateRow(record, action, this.actionCol);
        this.isReport && this.$refs.Report.refresh();
      });
    },
    async getMessage(id) {
      const {
        data: { data }
      } = await precessService("operationList");
      const task = data.filter(item => item.objectId === id)[0];
      return `${this.$t_process("task_id")}-${task.taskId},「${
        task.taskParam
      }」${task.operatorType}${task.operatorState}`;
    },
    async mergeOperation(record) {
      this.nfcvisible = true;
      this.$refs.task.polling();
      this.setLoading(record, this.actionCol, "merge", true);
      precessService("mergeOperation", {
        ...this.params,
        memberId: record.memberId,
        dimCode: record.dimCode,
        memberDbCode: record.memberDbCode,
        processId: record.processId,
        memberRelationTotalCode: record.memberRelationTotalCode,
        flowTemplateContentId: record.flowTemplateContentId || "",
        buttonName: record.buttonName,
        nowNodeId: record.nodeId
      }).then(res => {
        const {
          data: { success, data }
        } = res;
        if (success) {
          this.queryMergeTaskStatus(data, record, "merge");
        } else {
          UiUtils.error({
            title:
              this.$t_process("consolidation") + this.$t_common("failed_n"),
            class: "jsx-message",
            content: h => (
              <div>
                <div class="message-title">{data}</div>
              </div>
            )
          });
          this.setLoading(record, this.actionCol, "merge", false);
        }
      });
    },
    async computeOperation(record) {
      this.nfcvisible = true;
      this.$refs.task.polling();
      this.setLoading(record, this.actionCol, "compute", true);
      precessService("computeOperation", {
        ...this.params,
        memberId: record.memberId,
        dimCode: record.dimCode,
        processId: record.processId,
        memberDbCode: record.memberDbCode,
        memberRelationTotalCode: record.memberRelationTotalCode,
        memberRelationParentId: record.memberRelationParentId,
        flowTemplateContentId: record.flowTemplateContentId || "",
        buttonName: record.buttonName,
        nowNodeId: record.nodeId
      }).then(res => {
        const {
          data: { success, data }
        } = res;
        if (success) {
          this.queryMergeTaskStatus(res.data.data, record, "caculate");
        } else {
          this.setLoading(record, this.actionCol, "compute", false);
          UiUtils.error({
            title: this.$t_process("calculation") + this.$t_common("failed_n"),
            class: "jsx-message",
            content: h => (
              <div>
                <div class="message-title">{data}</div>
              </div>
            )
          });
        }
      });
    },
    async convertedOperation(record) {
      this.nfcvisible = true;
      this.$refs.task.polling();
      this.setLoading(record, this.actionCol, "converted", true);
      precessService("convertedOperation", {
        ...this.params,
        memberId: record.memberId,
        dimCode: record.dimCode,
        processId: record.processId,
        memberDbCode: record.memberDbCode,
        memberRelationTotalCode: record.memberRelationTotalCode,
        memberRelationParentId: record.memberRelationParentId,
        flowTemplateContentId: record.flowTemplateContentId || "",
        buttonName: record.buttonName,
        nowNodeId: record.nodeId
      }).then(res => {
        const {
          data: { success, data }
        } = res;
        if (success) {
          this.queryMergeTaskStatus(data, record, "converted");
        } else {
          this.setLoading(record, this.actionCol, "converted", false);
          UiUtils.error({
            title: this.$t_process("fxtrans") + this.$t_common("failed_n"),
            class: "jsx-message",
            content: h => (
              <div>
                <div class="message-title">{data}</div>
              </div>
            )
          });
        }
      });
    },
    async startOperation(record) {
      this.setLoading(record, this.actionCol, "start", true);
      precessService("startOperation", {
        batchId: record.batchId,
        year: this.params.year,
        version: this.params.version,
        period: this.params.period,
        openUrl: this.openUrl,
        objectId: record.objectId,
        dimCode: record.dimCode,
        memberId: record.memberId,
        memberRelationTotalCode: record.memberRelationTotalCode,
        flowTemplateContentId: record.flowTemplateContentId || "",
        buttonName: record.buttonName,
        processTemplateId: record.processTemplateId
      })
        .then(res => {
          const data = res.data.data;
          // 执行启动后发生的异常信息
          if (data && data.length > 0) {
            const merge = {};
            for (const index in data) {
              const action = new Chain(this.checkValue4);
              action
                .next(this.checkValue6)
                .next(this.checkValue78)
                .next(this.checkValueHas)
                .next(this.checkValueDef);
              action.run(data[index], record, merge, index);
            }
            // 78合并后一起处理
            if (Object.keys(merge).length) {
              const noTmp = merge[7];
              const notIn = merge[8];
              UiUtils.info({
                title: this.$t_common("tips"),
                class: "jsx-message",
                content: h => (
                  <div class="jsx-message-container">
                    {noTmp ? (
                      <div>
                        <div class="message-title">{noTmp.messageInfo}</div>
                        <ul class="message-lists">
                          {noTmp.memberList.map(li => {
                            return <li class="message-list">{li}</li>;
                          })}
                        </ul>
                      </div>
                    ) : null}
                    {notIn ? (
                      <div class="message-next">
                        <div class="message-title">{notIn.messageInfo}</div>
                        <ul class="message-lists">
                          {notIn.memberList.map(li => {
                            return <li class="message-list">{li}</li>;
                          })}
                        </ul>
                      </div>
                    ) : null}
                  </div>
                )
              });
            }
          } else {
            UiUtils.successMessage(
              this.$t_process("start_up") + this.$t_common("success")
            );
          }
        })
        .finally(async e => {
          this.reportLoading = true;
          this.updateRow(record, "start", this.actionCol);
          if (this.isReport) {
            this.records = this.getUpdatedData();
            this.$refs.Report.refresh();
          }
        });
    },
    async getPageStats(callback = this.setStats) {
      return await precessService("getPageStats", {
        year: this.params.year,
        version: this.params.version,
        period: this.params.period
      }).then(res => {
        const data = res.data.data;
        callback && callback(data);
        return data;
      });
    },
    setStats(data) {
      this.processCheckStatusList = data ? data.processCheckStatusList : [];
      this.processNodeStatusList = data ? data.processNodeStatusList : [];
      this.getCheckBoxFromSum(data);
    },
    async handlerBatch(batchType) {
      this.loading = true;
      if (batchType.operatorCode !== "urge") {
        this.nfcvisible = true;
        this.$refs.task.polling();
      }
      const selection = [...this.selection];
      const data = await this.getProcessTree({
        ...this.params,
        ...this.tableFilter
      });
      this.gridTableData = data || [];
      setTimeout(() => {
        selection.forEach(item => {
          this.setLoading(item, this.actionCol, batchType.operatorCode, true);
        });
      }, 1000);
      switch (batchType.operatorCode) {
        case "caculate":
          this.batchTIOperation();
          break;
        case "converted":
          this.batchTIOperation();
          break;
        case "audit":
          this.auditBatchOperation();
          break;
        case "start":
          this.startBatchOperation();
          break;
        case "turndown":
          this.rejectBatchOperation();
          break;
        case "submit":
          this.submitBatchOperation();
          break;
        case "urge":
          this.batchUrging();
          break;
      }
      this.loading = false;
      this.setBatchType("");
    },
    async batchUrging() {
      const selection = [...this.selection];
      const batchType = { ...this.batchType };
      const params = {
        ...this.params,
        batchMessages: selection.map(item => {
          return {
            batchId: item.batchId,
            batchSubmit: item.batchSubmit,
            objectId: item.objectId,
            memberId: item.memberId,
            processId: item.processId,
            nodeId: item.nodeId,
            dimCode: item.dimCode
          };
        })
      };
      const {
        data: { data, success }
      } = await precessService("batchPushMessage", params).catch(
        e => (this.loading = false)
      );
      if (!success) {
        UiUtils.info({
          title: this.$t_process("urge"),
          class: "jsx-message",
          content: h => (
            <div>
              <div class="message-title">
                {this.$t_process("urge_folows_no_setting")}
              </div>
              <ul class="message-lists">
                {data.map(li => {
                  return <li class="message-list">{li}</li>;
                })}
              </ul>
            </div>
          )
        });
      } else {
        UiUtils.successMessage(
          this.$t_process("urge") + this.$t_common("success")
        );
      }
      this.$nextTick(() => {
        this.batchUpdateRow(selection, batchType.operatorCode, this.actionCol);
      });
      this.loading = false;
    },
    async batchTIOperation() {
      const selection = [...this.selection];
      const batchType = { ...this.batchType };
      const params = {
        ...this.params,
        operateName: batchType.operatorName,
        processIdList: selection.map(item => item.processId)
      };
      const {
        data: { data, success }
      } = await precessService("batchTIOperation", params).catch(
        e => (this.loading = false)
      );
      if (success) {
        this.queryMergeTaskBatchStatus(data, selection, batchType.operatorCode);
      } else {
        this.loading = false;
      }
    },
    async submitBatchOperation() {
      const selection = [...this.selection];
      const batchType = { ...this.batchType };
      const params = {
        ...this.params,
        openUrl: this.openUrl,
        processBatchDTOList: selection.map(item => ({
          batchId: item.batchId,
          batchSubmit: item.batchSubmit,
          processId: item.processId,
          objectId: item.objectId,
          nodeId: item.nodeId,
          memberId: item.memberId
        }))
      };
      const {
        data: { data, success }
      } = await precessService("submitBatchOperation", params).catch(
        e => (this.loading = false)
      );
      if (success) {
        this.queryMergeTaskBatchStatus(data, selection, batchType.operatorCode);
      } else {
        this.loading = false;
      }
    },
    async startBatchOperation() {
      const selection = [...this.selection];
      const batchType = { ...this.batchType };
      const params = {
        ...this.params,
        openUrl: this.openUrl,
        batchStarts: selection.map(item => ({
          batchId: item.batchId,
          objectId: item.objectId,
          memberId: item.memberId
        }))
      };
      const {
        data: { data, success }
      } = await precessService("startBatchOperation", params).catch(
        e => (this.loading = false)
      );
      if (success) {
        this.queryMergeTaskBatchStatus(data, selection, batchType.operatorCode);
      } else {
        this.loading = false;
      }
    },
    async auditBatchOperation() {
      const selection = [...this.selection];
      const batchType = { ...this.batchType };
      const params = {
        ...this.params,
        openUrl: this.openUrl,
        processBatchDTOList: selection.map(item => ({
          batchId: item.batchId,
          processId: item.processId,
          objectId: item.objectId,
          nodeId: item.nodeId,
          memberId: item.memberId
        }))
      };
      const {
        data: { data, success }
      } = await precessService("auditBatchOperation", params).catch(
        e => (this.loading = false)
      );
      if (success) {
        this.queryMergeTaskBatchStatus(data, selection, batchType.operatorCode);
      } else {
        this.loading = false;
      }
    },
    async rejectBatchOperation() {
      const selection = [...this.selection];
      const batchType = { ...this.batchType };
      const params = {
        ...this.params,
        openUrl: this.openUrl,
        processBatchDTOList: selection.map(item => ({
          batchId: item.batchId,
          batchSubmit: item.batchSubmit,
          processId: item.processId,
          objectId: item.objectId,
          nodeId: item.nodeId,
          memberId: item.memberId
        }))
      };
      const {
        data: { data, success }
      } = await precessService("rejectBatchOperation", params).catch(
        e => (this.loading = false)
      );
      if (success) {
        this.queryMergeTaskBatchStatus(data, selection, batchType.operatorCode);
      } else {
        this.loading = false;
      }
    },
    async setFilterInfo(data) {
      const originArch = data || this.gridTableData;
      this.getReject(originArch);
      this.getBatchList(originArch);
    },
    setLoading(record, col, type, loading) {
      this.$refs.reportGrid.updateCell({
        rowId: record.objectId,
        col: col,
        loading: loading,
        action: type
      });
    },
    getBatchList(data) {
      const arr = this.getBatch(data);
      if (data.length > 0) {
        arr.push({
          operatorCode: "urge",
          operatorName: this.$t_process("urge")
        }); // 有数据就可以催办
      }
      this.setBatchList(arr); // 催办前端控制批量必有
    },
    getCheckBoxFromSum(data) {
      if (this.batchType.operatorCode !== "turndown") {
        this.setNodeNameSummary(
          this.processNodeStatusList
            .filter(item => item.statsNum !== 0)
            .map(item => item.statsName)
        );
      }
      this.setCheckStateSummary(
        this.processCheckStatusList
          .filter(item => item.statsNum !== 0)
          .map(item => item.statsName)
      );
    },
    getReject(data) {
      const filters = getFilter(data, "turndown", {
        nodeName: []
      });
      this.setAuditAbleState(filters.nodeName);
    },
    cancelBatch() {
      this.setBatchType("");
      this.search();
    },
    handlerHeaderReady(isReady) {
      this.headerReady = isReady;
    },
    checkValueDef(item, record) {
      const type = ["info", "error", "info"];
      const title = [
        this.$t_common("tips"),
        this.$t_process("start_failed"),
        this.$t_common("tips")
      ];
      UiUtils[type[item.startState]]({
        title: title[item.startState],
        class: "jsx-message",
        content: h => (
          <div>
            <div class="message-title">{item.messageInfo}</div>
            <ul class="message-lists">
              {item.memberList.map(li => {
                return <li class="message-list">{li}</li>;
              })}
            </ul>
          </div>
        )
      });
    },
    checkValueHas(item, record) {
      if (item.failType && item.failType.toString() !== "9") {
        // 其他类型报错
        UiUtils.error({
          title: this.$t_process("start_failed"),
          content: `${item.messageInfo}`
        });
      } else {
        return "next";
      }
    },
    checkValue78(item, record, merge) {
      const failType = item.failType ? item.failType.toString() : "";
      if (failType === "7" || failType === "8") {
        merge[failType] = item;
      } else {
        return "next";
      }
    },
    checkValue6(item, record) {
      if (item.failType && item.failType.toString() === "6") {
        // 状态变更校验失败
        this.refreshState();
      } else {
        return "next";
      }
    },
    checkValue4(item, record) {
      if (item.failType && item.failType.toString() === "4") {
        // 并发校验失败
        UiUtils.error({
          title: this.$t_process("start_failed"),
          content: `${item.messageInfo}`
        });
      } else {
        return "next";
      }
    },
    refreshState() {
      UiUtils.confirm({
        icon: h =>
          h(YnIconSvg, {
            props: {
              type: "information"
            }
          }),
        title: this.$t_process("process_status") + this.$t_common("update"),
        content: this.$t_process("refresh_page_after_status_change"),
        okText: this.$t_common("refresh"),
        cancelText: this.$t_common("close"),
        onOk: () => {
          if (this.isReport) {
            this.$refs.Report.refresh();
          } else {
            this.search();
          }
          this.visible = false;
        }
      });
    },
    handlerAction(type, ...info) {
      this[type](...info);
    },
    getUpdatedData(data = this.gridTableData, id = this.records.objectId) {
      for (const item of data) {
        if (item.objectId === id) {
          return item;
        } else {
          const result = this.getUpdatedData(item.children || []);
          if (result) return result;
        }
      }
    },
    generateURL() {
      const { MenuId, appId, lang } = this.generateParams();
      return `#/process/control?bug=fixed&from=console&appId=${appId}&menuId=${MenuId}&lang=${lang}&inTab=${MenuId}&year=${this.params.year}&version=${this.params.version}&period=${this.params.period}&yearlabel=${this.viewParams.year}&versionlabel=${this.viewParams.version}&periodlabel=${this.viewParams.period}`;
    },
    generateParams() {
      const fieldsMap = {
        lang: "",
        appId: "",
        MenuId: ""
      };
      Object.keys(fieldsMap).forEach(key => {
        fieldsMap[key] = DsUtils.getSessionStorageItem(key, {
          storagePrefix: APPS.NAME,
          isJson: true
        });
      });

      return fieldsMap;
    },
    handlerButton(record, type) {
      const clickMap = {
        operation_history: this.handlerTDrawer,
        fillReport: this.handlerEdit,
        submit: this.handlerTDrawer,
        audit: this.handlerTDrawer,
        start: record.batchId ? this.handlerTDrawer : this.startOperation,
        turndown: this.handlerTDrawer,
        caculate: this.computeOperation,
        merge: this.mergeOperation,
        converted: this.convertedOperation,
        view_process: this.viewProcess
      };
      clickMap[type](record, type);
    },
    handlerOk(type, record) {
      switch (type) {
        case "start":
          this.startOperation(record);
          break;
        case "import":
          this.updateRow(record, "import", this.fileOperateCol);
          break;
        case "reject":
          this.updateRow(record, type, this.actionCol);
          this.isReport && this.$refs.Report.refresh();
          break;
        case "submit":
          this.updateRow(record, type, this.actionCol);
          this.nfcvisible = true;
          this.$refs.task.refresh();
          this.isReport && this.$refs.Report.refresh();
          break;
        case "deleteAttachment":
          this.updateRow(record, "deleteAttachment", this.fileOperateCol);
          break;
        case "downloadFile":
          this.updateRow(record, "downloadFile", this.fileOperateCol);
          break;
        case "audit":
          this.updateRow(record, type, this.actionCol);
          this.isReport && this.$refs.Report.refresh();
          break;
        case "merge":
          this.mergeOperation(record, type);
          break;
      }
    },
    handlerDrawer(data, componentId, title, type) {
      if (
        componentId === "DrawerCheck" &&
        this.CHECKSTATUS[data.checkCode] === "not_validated"
      ) {
        return;
      }
      this.visible = true;
      this.componentId = componentId;
      this.componentProp = {
        title: title,
        data: data,
        hasBatch: this.hasBatch,
        hasCountdown: this.hasCountdown,
        params: this.params,
        type: type,
        openUrl: this.openUrl
      };
    },
    handlerTDrawer(record, type) {
      const title = {
        operation_history: this.$t_process("operation_history"),
        submit: this.$t_process("submit"),
        audit: this.$t_process("approve"),
        turndown: this.$t_process("reject"),
        start: this.$t_process("start_up")
      };
      const Drawer = {
        operation_history: "DrawerLog",
        submit: "DrawerOprate",
        audit: "DrawerOprate",
        turndown: "DrawerOprate",
        start: "DrawerOprate"
      };
      this.handlerDrawer(record, Drawer[type], title[type], type);
    },

    handlerEdit(record) {
      this.setIsReport(true);
      this.records = record;
    },
    setQueryId() {
      this._currentQueryId = _uniqueId("QueryId");
      return this._currentQueryId;
    },
    getQueryId() {
      return this._currentQueryId;
    },

    // 查看流程功能
    async viewProcess(record) {
      try {
        this.loading = true;
        // 调用后端接口获取流程数据
        const {
          data: { data }
        } = await precessService("processOperation", {
          processId: record.processId,
          batchId: record.batchId,
          batchSubmit: record.batchSubmit,
          nodeId: record.nodeId,
          memberId: record.memberId,
          objectId: record.objectId,
          hasParent: record.memberId !== record.memberRelationParentId
        });
        this.viewProcessData = record;
        this.viewProcessList = data.operationVOList || [];
        this.viewProcessVisible = true;
      } catch (error) {
        console.error("获取流程数据失败:", error);
        UiUtils.errorMessage(this.$t_process("get_process_data_failed") || "获取流程数据失败");
      } finally {
        this.loading = false;
      }
    },

    // TODO 优化
    getBatch(data, arr = []) {
      const BATCHLIST = {
        caculate: 1,
        converted: 2,
        submit: 3,
        audit: 4,
        turndown: 5,
        start: 6
      };
      for (const item of data) {
        if (item.hasAuth) {
          const operator = (item.operatorList || []).filter(
            op => op.operatorAuth
          );
          for (const op of operator) {
            if (BATCHLIST[op.operatorCode]) {
              arr[BATCHLIST[op.operatorCode] - 1] = op;
            }
          }
          // 6 BATCHLIST length
          if (arr.length === 6 && !arr.includes(undefined)) {
            break;
          }
        }
        if (item.children) {
          this.getBatch(item.children, arr);
        }
      }
      return arr;
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .ant-spin-blur {
  .ant-spin-nested-loading .ant-spin-dot {
    display: none;
  }
  .grid-empty {
    display: none;
  }
}
.process-control {
  display: flex;
  flex-direction: column;
  height: 100%;
  color: @yn-text-color;
  .container {
    display: flex;
    flex-direction: column;
    flex: 1;
  }
  .search-filter {
    margin-top: 0 !important;
    flex-shrink: 0;
    box-shadow: 0px 1px 0.25rem 0px rgba(22, 24, 35, 0.06);
  }
  .process-table-contain {
    flex: 1;
    height: 0;
    overflow: hidden;
    margin: 1rem -10px 1rem 0;
  }

  .spinning {
    position: relative !important;
    padding-top: 1rem;
    width: calc(100% + 10px);
  }
}
/deep/ .hot-container .ht_master .wtHolder {
  overflow-y: scroll !important;
}
</style>

<style lang="less">
.jsx-message .jsx-message-container {
  max-height: 31.25rem;
  overflow: auto;
}
.jsx-message .message-title {
  color: @yn-text-color-secondary;
  text-align: left;
  font-weight: 400;
  margin-bottom: 1rem;
}
.jsx-message .message-next {
  margin-top: 1rem;
}
.jsx-message .message-lists {
  max-height: 25rem;
  overflow: auto;
}
.jsx-message .message-list {
  color: @yn-text-color;
  text-align: left;
  font-weight: 400;
  margin-bottom: 0.25rem;
}
</style>
