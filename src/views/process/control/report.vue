<template>
  <section class="process-report">
    <ReportHeader v-bind="$props" v-on="$listeners" />
    <yn-layout
      iconPosition="center"
      :extended="checked"
      class="process-contain"
    >
      <yn-layout-content class="process-content">
        <yn-spin :spinning="listLoading" class="listspinning">
          <section v-if="!empty" class="dataarea">
            <div
              v-for="(item, index) in templateList"
              :key="index"
              class="operate"
            >
              <div class="info">
                <div class="info-title">
                  {{ item.operationName }}
                  <span class="record" @click="handlerLog(item)">
                    <SvgIcon :isIconBtn="false" type="icon-clock-circle" />
                    <span class="opnumber">
                      {{ $t_common("operation") }} {{ item.operationNum || 0 }}
                      {{ $t_common("time") }}
                    </span>
                  </span>
                </div>
                <div class="info-des">{{ item.remark }}</div>
              </div>
              <div class="action">
                <yn-tooltip>
                  <template v-if="isDisabled(item)" slot="title">
                    {{ $t_process("process_status_not_executable") }}
                  </template>
                  <yn-button
                    type="primary"
                    :disabled="isDisabled(item)"
                    @click="handlerOpen(item)"
                  >
                    {{ item.buttonName }}
                  </yn-button>
                </yn-tooltip>
              </div>
            </div>
          </section>
          <yn-empty v-else :image="require('@/image/reportEmpty.png')">
            <span slot="description" class="description">
              {{ $t_process("task_flow_empty") }}
            </span>
          </yn-empty>
        </yn-spin>
      </yn-layout-content>
    </yn-layout>
    <!-- 执行弹窗 -->
    <run-modal
      ref="ExecuteModal"
      @ok="handlerExecuteOk"
      @onPollingScriptStatus="onPollingScriptStatus"
    />
  </section>
</template>

<script>
import { polling } from "@/utils/common";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-layout/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-empty/";
import ReportHeader from "./item/reportHeader";
import RunModal from "./item/RunModal";
import cloneDeep from "lodash/cloneDeep";
// import UiUtils from "yn-p1/libs/utils/UiUtils";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-divider/";
import precessService from "@/services/process";
import { mapState } from "vuex";
import SvgIcon from "../../../components/ui/SvgIcon.vue";
import DIM_INFO from "@/constant/dimMapping";
import { composeFuns } from "@/utils/taskFlowTemp.js";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import commonService from "@/services/common";
import taskFlowTempService from "@/services/taskFlowTemp";
const MAPPING_OBJ_KEY = ["Entity", "Scope", "SCOPE_G_NONE"];
const TAB_NAME = "processForm";
const TAB_PATH = "/process/form";
const allDimListMap = {};
export default {
  components: { SvgIcon, RunModal, ReportHeader },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    record: {
      type: Object,
      default: () => ({})
    },
    viewParams: {
      type: Object,
      default: () => {
        return {
          year: "",
          version: "",
          period: ""
        };
      }
    },
    dimInfo: {
      type: Object,
      default: () => {}
    },
    params: {
      type: Object,
      default: () => {
        return {
          year: "",
          version: "",
          period: ""
        };
      }
    }
  },
  data() {
    return {
      checked: true,
      treeLoading: false,
      buttonsAllowed: { nodeButtonList: [] },
      templateList: [],
      mapRouterOptions: {} // 本地开发使用
    };
  },
  computed: {
    ...mapState("common", {
      tabs: state => state.tabs,
      menuInfo: state => state.menuList,
      menuMapObj: state => state.menuMapObj
    }),
    ...mapState("processStore", {
      isReport: state => state.isReport
    }),
    listLoading: {
      get() {
        return this.loading;
      },
      set(value) {
        this.$emit("update:loading", value);
      }
    },
    empty() {
      return !this.listLoading && this.templateList.length === 0;
    },
    group() {
      if (this.record.dimCode === "Scope") {
        const entity = (this.record.children || []).find(
          item => item.dimCode === "Entity"
        );
        return {
          scope: this.record.memberId,
          entity: entity ? entity.memberId : ""
        };
      } else if (this.record.dimCode === "Entity") {
        return {
          scope: "11ece3b6d9384860a77db789e05f52b1", // E_None
          entity: this.record.memberId
        };
      } else {
        return { scope: "", entity: "" };
      }
    }
  },
  watch: {
    isReport: {
      handler(value) {
        if (value) {
          this.refresh();
        }
      },
      immediate: true
    },
    loading: {
      handler(value) {
        this.listLoading = value;
      }
    }
  },
  created() {
    this._actions = {
      0: this.openOutUrl,
      1: this.openUrl,
      2: this.openPage,
      3: this.openForm,
      4: this.openOperate,
      5: this.openScript
    };
    const mapObj = {};
    const loop = data => {
      data &&
        data.forEach(option => {
          const { children, path, meta } = option;
          if (meta.type === "consolidation_module") {
            mapObj[path] = option;
          }
          if (children && children.length > 0) {
            loop(children);
          }
        });
    };
    loop(this.$router.options.routes);
    this.mapRouterOptions = mapObj;
    this.getAllDims();
  },
  methods: {
    async getProcessNodeButtonList() {
      const {
        data: { data }
      } = await precessService(
        "getProcessNodeButtonList",
        this.record.objectId
      );
      this.record.processId = (data && data.processId) || this.record.processId;
      this.record.nodeId = (data && data.nodeId) || this.record.nodeId;
      this.buttonsAllowed = data || { nodeButtonList: [] };
    },
    async getOperationReportList() {
      this.listLoading = true;
      const {
        data: { data }
      } = await precessService("getOperationReportList", {
        ...this.params,
        memberRelationTotalCode: this.record.memberRelationTotalCode,
        dimCode: this.record.dimCode,
        processId: this.record.processId,
        memberId: this.record.memberId
      }).catch(e => (this.listLoading = false));
      this.templateList = data || [];
      this.listLoading = false;
    },
    async operationReport(item) {
      return precessService("operationReport", {
        ...this.params,
        memberId: this.record.memberId,
        processId: this.record.processId,
        dimCode: this.record.dimCode,
        objectId: this.record.objectId,
        nowNodeId: this.record.nodeId,
        operatorType: item.operationType,
        flowTemplateContentId: item.objectId,
        buttonName: item.buttonName
      }).then(res => {
        const { data, success } = res.data;
        if (!success) {
          UiUtils.errorMessage(`${this.record.memberName.v}${data}`);
          return success;
        }
        this._processId = data;
        this.record.processId = this._processId;
        this.refresh();
        return success;
      });
    },
    async search() {
      this.getOperationReportList();
    },
    async refresh() {
      this.listLoading = true;
      await this.getProcessNodeButtonList();
      this.getOperationReportList();
    },
    handlerLog(item) {
      this.record.flowTemplateContentId = item.objectId;
      this.record.buttonName = item.buttonName;
      this.record.from = "report";
      this.$emit("operate", this.record, "operation_history");
    },
    // 按钮操作
    handlerOpen(item) {
      this._catchActiveBtn = item;
      // 统计点击记录，操作类型的记录接口会自动统计
      if (
        item.operationType.toString() !== "4" &&
        item.operationType.toString() !== "5"
      ) {
        this.operationReport(item).then(success => {
          // 上报记录失败，说明出现了并发，直接返回
          success && this._actions[item.operationType](item);
        });
      } else {
        this._actions[item.operationType](item);
      }
    },
    openOutUrl(item) {
      // 外部url 打开浏览器的 页签。
      const { operationLink } = item;
      window.open(operationLink);
    },
    openUrl(item) {
      const { operationLink } = item;
      this.newtabMixin({
        id: item.objectId,
        uri: TAB_PATH,
        title: item.buttonName,
        router: TAB_NAME,
        params: {
          from: "process",
          notForm: true,
          notFormSrc: operationLink
        }
      });
    },
    openPage(item) {
      const { operationLink } = item;
      const { menuId, uri, ...others } = this.menuMapObj[operationLink] || {};
      const { name, meta } = this.getRouterInfo(uri);
      let params = this.getDefaultParams(item);
      if (uri.indexOf("openForm") !== -1) {
        // 如果是打开表单 则需要传 页面维
        params = {
          customObj: this.generateCustomObj(params),
          fromPage: "process"
        };
      }
      this.newtabMixin({
        id: menuId,
        // 流程配置页面 跳转报表。拼好域名（http://192.168.12.178:91）不然会带上 consolidation
        uri: uri.includes("/scene/management")
          ? `${location.origin}/${uri}`
          : uri,
        params: {
          from: "process",
          ...this.group,
          ...params
        },
        noKeepAlive: !!meta.noKeepAlive, // 是否需要缓存
        router: name || "systemTab", // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
        ...others
      });
    },
    async openForm(item) {
      // 打开新页签
      let pageDim = composeFuns(
        this.getDefaultParams,
        this.generateCustomObj
      )(item);
      // 处理组织、合并组
      const entityAndScopeInfo = await this.getFormParam();
      pageDim = encodeURIComponent(
        JSON.stringify(
          Object.assign(
            {},
            entityAndScopeInfo,
            JSON.parse(decodeURIComponent(pageDim))
          )
        )
      );
      this.newtabMixin({
        id: item.objectId,
        title: item.operationName,
        uri: TAB_PATH,
        router: TAB_NAME, // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
        params: {
          from: "process",
          formIds: item.operationLink,
          id: item.objectId,
          // 流程控制组织 id（table数据的 objectId）
          nodeId: this.record.objectId,
          customObj: pageDim
        }
      });
    },
    // 获取传入表单的组织、合并组信息
    async getFormParam() {
      const [entityIdent, scopeIdent, scopeGNone] = MAPPING_OBJ_KEY;
      // 点击的单体组织、合并体组织
      const { dimCode, memberId, memberName } = this.record;
      if (dimCode === entityIdent) {
        // 单体组织、G_NONE
        return {
          [DIM_INFO[entityIdent]]: memberId,
          [DIM_INFO[scopeIdent]]: DIM_INFO[scopeGNone]
        };
      } else {
        // 合并体组织、合并体组织对应的组织信息
        const entityMemberId = await this.getEntityDimMemberInfoByName(
          memberName.v
        );
        return {
          [DIM_INFO[entityIdent]]: entityMemberId,
          [DIM_INFO[scopeIdent]]: memberId
        };
      }
    },
    async getAllDims() {
      await taskFlowTempService("getAllDims").then(res => {
        res.data.data.forEach(item => {
          const ObjKey =
            item.dimCode.slice(0, 1).toLocaleLowerCase() +
            item.dimCode.slice(1);
          allDimListMap[ObjKey] = item.objectId;
        });
      });
    },
    async getEntityDimMemberInfoByName(name) {
      const { data } = await commonService("getDimInfoBycodeAndName", {
        dimCode: "Entity",
        memberName: name
      });
      return data && data.memberId;
    },
    isDisabled(item) {
      const isOperate = item.operationType.toString() === "4";
      // const isScript = item.operationType.toString() === "5";
      const hasOp = this.buttonsAllowed.nodeButtonList.some(button => {
        return button.operatorCode === item.operationLink;
      });
      return isOperate ? !hasOp : false;
    },
    openOperate(item) {
      this.record.flowTemplateContentId = item.objectId;
      this.record.buttonName = item.buttonName;
      this.$emit("operate", this.record, item.operationLink);
    },
    async openScript(item) {
      const hasSc = this.record.writeForm;
      const originParams = {
        ...this.scriptGroup(item),
        ...this.getDefaultParams(item)
      };
      const { data } = await commonService(
        "selectByObjectIds",
        Object.values(originParams)
      );
      const params = Object.keys(originParams).reduce((pre, next) => {
        pre[allDimListMap[next]] = originParams[next];
        return pre;
      }, {});
      if (hasSc) {
        this.$refs.ExecuteModal.showExecuteModal(
          [item.operationLink],
          params,
          data.reduce((pre, next) => {
            pre[next.objectId] = next.dimMemberName;
            return pre;
          }, {})
        );
      } else {
        UiUtils.error({
          title: this.$t_process("script_execution_failed"),
          content: this.$t_process("process_not_editable_script_call_failed")
        });
      }
    },
    onPollingScriptStatus(taskId, params) {
      this.syncToMargeTask(taskId, params);
      this._poller && this._poller.stop();
      this._poller = polling(
        async ids => {
          const { data = [] } = await precessService(
            "executeScriptResult",
            ids
          );
          const state = data[0] ? data[0].status : "";
          if (state !== "RUNNING" && state !== "NON_RUNNING") return data;
        },
        1000,
        [taskId]
      ).then(data => {
        if (!data[0]) return;
        const msg = data[0].result[0].executeMsg;
        if (data[0].status === "SUCCESS") {
          UiUtils.successMessage(msg);
        } else {
          UiUtils.errorMessage(msg);
        }
      });
    },
    handlerExecuteOk() {
      this.operationReport(this._catchActiveBtn);
    },
    scriptGroup(item) {
      if (this.record.dimCode === "Scope") {
        return {
          scope: this.record.memberId,
          entity: item.scopeMethodMember ? item.scopeMethodMember.objectId : ""
        };
      } else if (this.record.dimCode === "Entity") {
        return {
          scope: item.gNoneMember ? item.gNoneMember.objectId : "",
          entity: this.record.memberId
        };
      } else {
        return { scope: "", entity: "" };
      }
    },
    // 同步到合并任务查询
    async syncToMargeTask(taskId, params) {
      precessService("saveMergeTask", {
        // startTime: data.list[0].createDate,
        startTime: "",
        endTime: "",
        taskType: 11,
        taskStatus: "",
        scriptTaskExecuteDto: params,
        scriptId: taskId
      });
      // const { data } = await precessService("executeScriptLog", {
      //   pageNum: 0,
      //   pagesize: 10,
      //   recordId: this._catchActiveBtn.operationLink,
      //   scriptRecordName: "",
      //   tiLogResult: "",
      //   updateLogin: ""
      // });
      // if (data && data.list && data.list[0]) {
      //   precessService("saveMergeTask", {
      //     startTime: data.list[0].createDate,
      //     endTime: "",
      //     taskType: 11,
      //     taskStatus: "",
      //     taskParam: JSON.parse(data.list[0].tiLogMsg),
      //     scriptId: taskId
      //   });
      // }
    },
    // 获取默认参数，如果没有默认 参数，则传当前的版本、年、期间信息
    getDefaultParams(item) {
      const { defaultParam = "[]" } = item;
      const params = cloneDeep(this.dimInfo);
      const tempObj = JSON.parse(defaultParam || "[]");
      tempObj.forEach(item => {
        const key = Object.keys(item)[0];
        const ObjKey = key.slice(0, 1).toLocaleLowerCase() + key.slice(1);
        params[ObjKey] = item[key];
      });
      return Object.assign({}, this.params, params);
    },
    // 获取到上面处理后的参数之后，如果打开的是表单 则需要再处理成如下结构
    //  表单页面维传参 结构：{dimId: memberId}
    generateCustomObj(params) {
      const obj = Object.create(null);
      Object.keys(params).forEach(key => {
        const k = key.slice(0, 1).toUpperCase() + key.slice(1);
        obj[DIM_INFO[k]] = params[key];
      });
      return encodeURIComponent(JSON.stringify(obj));
    },
    getRouterInfo(uri) {
      const path =
        Object.keys(this.mapRouterOptions).find(item => {
          return uri.indexOf(item) !== -1;
        }) || "";
      const defaultObj = {
        name: "",
        meta: {}
      };
      return path ? this.mapRouterOptions[path] : defaultObj;
    }
  }
};
</script>

<style scoped lang="less">
.process-report {
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: PingFangSC-Regular;
  font-size: 0.875rem;
}
.process-contain {
  flex: 1;
}
.process-tree {
  height: 100%;
  background: @yn-body-background;
}
.custom-tree {
  width: 100% !important;
}
.process-content {
  height: 100%;
  padding: 1.25rem -0.625rem 1.25rem 0;
  display: flex;
  flex-direction: column;

  .dataarea {
    width: calc(100% + 10px);
    height: 100%;
    margin-right: -10px;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .listspinning {
    height: 100%;
    /deep/ & > .ant-spin-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }
  .operate {
    height: 5rem;
    display: flex;
    background: @yn-body-background;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    justify-content: center;
    align-items: center;
    padding: 1rem 1.25rem;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .info {
    flex: 1;
    width: 0;
  }
  .info-title {
    height: 1.375rem;
    color: @yn-text-color;
    text-align: left;
    line-height: 1.375rem;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    .record {
      margin-left: 1rem;
      height: 1.375rem;
      color: @yn-disabled-color;
      line-height: 1.375rem;
      font-weight: 400;
      display: flex;
      align-items: center;
      cursor: pointer;
      &:hover {
        color: @yn-primary-7;
      }
      opicon {
        font-size: 1rem;
      }
      .opnumber {
        margin-left: 0.25rem;
      }
    }
  }
  .info-des {
    height: 1.375rem;
    color: @yn-text-color-secondary;
    line-height: 1.375rem;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .action {
    flex-shrink: 0;
    margin-left: auto;
  }
}
</style>
