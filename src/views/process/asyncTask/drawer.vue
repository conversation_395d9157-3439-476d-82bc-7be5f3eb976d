<template>
  <yn-drawer
    :title="$t_process('details')"
    placement="right"
    :closable="true"
    width="27.5rem"
    :visible="visible"
    @close="onClose"
  >
    <yn-spin :spinning="spinning" class="state-spinning">
      <div class="dataarea">
        <section class="task-header">
          <div class="task-header-id">
            <span class="taskId">{{
              `${$t_process("task_id")}-${record.taskId} ${record.operatorType}`
            }}</span>
            <span
              :class="['operatorState', ASYNCSTATUSCLASS[record.taskStatus]]"
            >
              {{ record.operatorState }}
            </span>
          </div>
          <div class="task-param">
            <span>{{ $t_process("total") }} {{ total }} </span>
            <yn-divider type="vertical" />
            <span>{{ record.taskParam }}</span>
          </div>
        </section>
        <yn-divider />
        <section class="task-body">
          <div v-for="(item, key) in task" :key="key" class="task-list">
            <div class="task-number">
              {{ item.state }}
              <span :class="STATECLASS[item.key]">{{ item.number }}</span>
              {{ $t_common("piece") }}, {{ $t_process("detail_like_follows") }}
            </div>
            <div v-for="sitem in item.list" :key="sitem" class="task-item">
              {{ sitem }}
            </div>
          </div>
        </section>
      </div>
    </yn-spin>
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-divider/";
import exchangerateService from "@/services/exchangerate";
export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    record: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      ASYNCSTATUSCLASS: Object.freeze({
        0: "async-unstart",
        1: "async-process",
        3: "async-stop",
        2: "async-success",
        4: "async-success",
        5: "async-stop",
        6: "async-part-error"
      }),
      STATECLASS: Object.freeze({
        NOT_STARTED: "task-number-unstart",
        RUNNING: "task-number-process",
        ABNORMAL_TERMINATION: "task-number-stop",
        FINISHED: "task-number-success",
        SUCCESS: "task-number-success",
        FAIL: "task-number-error",
        SOME_FAIL: "task-number-part"
      }),
      total: 0,
      task: {
        success: {
          number: 0,
          list: []
        },
        error: {
          number: 0,
          list: []
        }
      },
      spinning: false
    };
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.current = 1;
          this.search();
        }
      },
      immediate: true
    }
  },

  methods: {
    async search(page = this.current) {
      this.spinning = true;
      // this.task = this.getSubTasksById(this.record.objectId);
      this.getSubTasksById();
      this.spinning = false;
    },
    // 获取子任务
    getSubTasksById() {
      exchangerateService("getSubTasksById", this.record.objectId).then(res => {
        const {
          subTaskCnt,
          // taskStatus,
          subTaskMap
        } = res.data.data || {};
        const m = {
          NOT_STARTED: this.$t_process("not_started"),
          RUNNING: this.$t_process("in_progress"),
          FINISHED: this.$t_common("completed"),
          ABNORMAL_TERMINATION: this.$t_process("abnormal_termination"),
          SOME_FAIL: this.$t_process("partial_failure"),
          SUCCESS: this.$t_common("success"),
          FAIL: this.$t_common("failed_n")
        };
        this.total = subTaskCnt;
        this.task = Object.keys(subTaskMap).map(key => ({
          state: m[key],
          key: key,
          number: subTaskMap[key].length,
          list: subTaskMap[key]
        }));
      });
    },
    onClose() {
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style lang="less" scoped>
.dataarea {
  height: 0;
  flex: 1;
  overflow-x: hidden;
  overflow-y: scroll;
  .task-header {
  }
  .task-header-id {
    display: flex;
  }
  .taskId {
    height: 1.375rem;
    font-weight: 600;
    font-size: 0.875rem;
    color: #1a253b;
    text-align: left;
    line-height: 1.375rem;
    margin-bottom: 1.125rem;
  }
  .operatorState {
    height: 1.625rem;
    padding: 0 0.5rem;
    line-height: 1.625rem;
    border-radius: 0.25rem;
    margin-left: auto;
  }
  .task-body {
  }
  .task-number {
    height: 1.375rem;
    font-weight: 400;
    font-size: 0.875rem;
    color: #8894a8;
    text-align: left;
    line-height: 1.375rem;
    margin-bottom: 1rem;
  }

  .task-param {
    margin-bottom: 1.5rem;
    color: @yn-label-color;
  }
  .task-list {
  }
  .task-item {
    margin-bottom: 1rem;
  }

  .async-unstart {
    color: @yn-disabled-color;
    background: @yn-disabled-bg-color;
  }
  .async-process {
    color: @yn-primary-color;
    background: @yn-link-bg-color;
  }
  .async-stop {
    color: @yn-error-color;
    background: @yn-error-bg-color;
  }
  .async-success {
    color: @yn-success-color;
    background: @yn-success-bg-color;
  }
  .async-part-error {
    color: @yn-warning-color;
    background: @yn-warning-bg-color;
  }
  .task-number-unstart {
    color: @yn-disabled-color;
  }
  .task-number-process {
    color: @yn-primary-color;
  }
  .task-number-stop {
    color: @yn-error-color;
  }
  .task-number-error {
    color: @yn-error-color;
  }
  .task-number-success {
    color: @yn-success-color;
  }
  .task-number-part {
    color: @yn-warning-color;
  }
}
/deep/ .ant-drawer-wrapper-body {
  display: flex;
  flex-direction: column;
  .ant-drawer-body {
    display: flex;
    flex-direction: column;
    height: 0;
    flex: 1;
    overflow: auto;
  }
  .ant-spin-container {
    display: flex;
    flex-direction: column;
  }
}
</style>
