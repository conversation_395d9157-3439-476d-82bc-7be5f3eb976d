<template>
  <div>
    <Notification
      :visible.sync="notificationVisible"
      width="52.1875rem"
      @expand="refresh"
      @fold="handerFold"
      @close="handerClose"
    >
      <template #title>
        {{ $t_process("consolidation_tasks") }}({{ asyncData.length }})
      </template>
      <template #body>
        <yn-table
          rowKey="objectId"
          :columns="asyncColumns"
          :data-source="asyncData"
          :loading="loading"
          :scroll="{ x: false, y: asyncData.length > 6 ? '15rem' : false }"
        >
          <span slot="taskId" slot-scope="text" class="task-id">{{
            text
          }}</span>
          <span slot="operatorState" slot-scope="text, record">
            <span :class="ASYNCSTATUSCLASS[record.taskStatus]"></span>{{ text }}
          </span>
          <span slot="createDate" slot-scope="text, record" class="task-time">
            {{ text }}
            <SvgIcon
              v-if="isBatch(record)"
              :isIconBtn="false"
              class="task-icon"
              type="icon-c1_cr_form_enter"
              @click.native="handlerOpenBatch(record)"
            />
          </span>
        </yn-table>
      </template>
    </Notification>
    <drawer :record="record" :visible.sync="drawerVisible" />
  </div>
</template>

<script>
import { polling } from "@/utils/common";
import Notification from "./notification";
import SvgIcon from "@/components/ui/SvgIcon.vue";
import precessService from "@/services/process";
import drawer from "./drawer";
import "yn-p1/libs/components/yn-table/";

export default {
  components: { Notification, SvgIcon, drawer },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    api: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      ASYNCSTATUSCLASS: Object.freeze({
        0: "async-unstart",
        1: "async-process",
        3: "async-stop",
        2: "async-success",
        4: "async-success",
        5: "async-stop",
        6: "async-part-error"
      }),
      loading: false,
      record: {},
      drawerVisible: false,
      asyncData: [],
      asyncColumns: [
        {
          title: this.$t_process("task_id"),
          dataIndex: "taskId",
          width: "4.6875rem",
          key: "taskId",
          scopedSlots: {
            customRender: "taskId"
          }
        },
        {
          title: this.$t_process("task_type"),
          dataIndex: "operatorType",
          width: "5rem",
          key: "operatorType",
          scopedSlots: {
            customRender: "operatorType"
          }
        },
        {
          title: this.$t_process("parameters"),
          dataIndex: "taskParam",
          width: "12.625rem",
          ellipsis: true,
          key: "taskParam"
        },
        {
          title: this.$t_process("status"),
          dataIndex: "operatorState",
          width: "5.625rem",
          key: "operatorState",
          scopedSlots: {
            customRender: "operatorState"
          }
        },
        {
          title: this.$t_common("maintenance_mode_start_time"),
          dataIndex: "createDate",
          width: "9.375rem",
          key: "createDate",
          scopedSlots: {
            customRender: "createDate"
          }
        }
      ]
    };
  },
  computed: {
    notificationVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit("update:visible", value);
      }
    }
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.refresh();
        }
      },
      immediate: true
    }
  },
  destroyed() {
    this._poller && (this._poller.number = null);
    this._poller && this._poller.stop();
  },
  methods: {
    async polling() {
      if (this._poller) {
        this._poller.number++;
        return;
      }
      this.loading = true;
      this._poller = polling(async () => {
        const {
          data: { data }
        } = await precessService("operationList");
        this.asyncData = data;
        this.loading = false;
      }, 1000);
      this._poller.number = 1;
    },
    async refresh() {
      this.loading = true;
      const {
        data: { data }
      } = await precessService("operationList");
      this.asyncData = data;
      this.loading = false;
    },
    isBatch(record) {
      const key = this.$t_common("batch");
      const regexp = new RegExp(key, "i");
      return record.operatorType.match(regexp);
    },
    stopPolling() {
      this.refresh();
      if (!this._poller) return;
      this._poller.number -= 1;
      if (this._poller.number <= 0) {
        this._poller.stop();
        this._poller = null;
      }
    },
    handerFold() {},
    handerClose() {
      this.$emit("update:visible", false);
    },
    handlerOpenBatch(record) {
      this.record = record;
      this.drawerVisible = true;
    }
  }
};
</script>

<style scoped lang="less">
.task-id {
  color: @yn-text-color-secondary;
}
.task-icon {
  color: @yn-text-color-secondary;
  margin-left: auto;
}
.task-time {
  display: flex;
  width: 100%;
  align-items: center;
  color: @yn-text-color-secondary;
}
/deep/ .ant-table-body {
  overflow-x: hidden;
}
/deep/ .ant-table-tbody > tr > td {
  padding-top: 0;
  padding-bottom: 0;
  height: 2.25rem;
}
/deep/ .ant-table-thead tr th {
  background: @yn-table-header-bg;
}
.async-unstart {
  display: inline-block;
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
  margin-right: 0.5rem;
  background: @yn-disabled-color;
}
.async-process {
  display: inline-block;
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
  margin-right: 0.5rem;
  background: @yn-primary-color;
}
.async-stop {
  display: inline-block;
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
  margin-right: 0.5rem;
  background: @yn-error-color;
}
.async-success {
  display: inline-block;
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
  margin-right: 0.5rem;
  background: @yn-success-color;
}
.async-part-error {
  display: inline-block;
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
  margin-right: 0.5rem;
  background: @yn-warning-color;
}
</style>
