<template>
  <transition name="notififade">
    <section
      v-if="visible"
      class="notification"
      :style="{ width: width, height: height }"
    >
      <header class="notification-header">
        <div class="notification-title">
          <slot name="title">
            <span>{{ title }}</span>
          </slot>
        </div>
        <div class="notification-icons">
          <SvgIcon
            class="action-icon"
            :type="fold ? 'icon-a-c1_cr_replysize' : 'icon-c1_cr_minimize'"
            @click.native="handlerSize"
          />
          <SvgIcon
            class="action-icon"
            type="close"
            @click.native="handlerClose"
          />
        </div>
      </header>
      <yn-divider v-show="!fold" class="notification-divider" />
      <section v-show="!fold" class="notification-body">
        <slot name="body"> </slot>
      </section>
    </section>
  </transition>
</template>

<script>
import SvgIcon from "@/components/ui/SvgIcon.vue";
import "yn-p1/libs/components/yn-divider/";
export default {
  name: "Notification",
  components: { SvgIcon },
  props: {
    title: {
      type: String,
      default: ""
    },
    width: {
      type: String,
      default: "auto"
    },
    height: {
      type: String,
      default: "auto"
    },
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      fold: false,
      tabledata: []
    };
  },
  computed: {},
  watch: {
    fold(value) {
      if (value) {
        this.$emit("fold");
      } else {
        this.$emit("expand");
      }
    }
  },
  methods: {
    handlerSize() {
      this.fold = !this.fold;
    },
    handlerClose() {
      this.fold = false;
      this.$emit("update:visible", false);
      this.$emit("close");
    }
  }
};
</script>

<style lang="less" scoped>
.notification {
  position: fixed;
  display: flex;
  flex-direction: column;
  right: 2rem;
  bottom: 1.5rem;
  background: @yn-body-background;
  box-shadow: 0px 0.75rem 2.625rem 0px rgba(0, 0, 0, 0.24);
  border-radius: 0.25rem;
  z-index: 999;
  &-header {
    display: flex;
    flex-shrink: 0;
    height: 2.75rem;
    padding: 0.75rem 1rem 0.75rem 1.5rem;
    align-items: center;
    border-radius: 0.25rem 0.25rem 0px 0px;
    z-index: 1000;
  }
  &-title {
    font-size: 1rem;
    color: @yn-text-color-secondary;
    font-weight: 600;
  }
  &-icons {
    font-weight: 600;
    color: @yn-text-color-secondary;
    margin-left: auto;
  }
  &-body {
    padding: 1.5rem;
    overflow: auto;
    overflow-x: hidden;
  }
  &-divider {
    margin: 0;
    background: @yn-border-color-base;
  }
}
.notififade-enter-active {
  transition: opacity 0.5s, right 0.5s ease-in;
}
.notififade-leave-active {
  transition: opacity 0.5s, right 0.5s ease-in;
}
.notififade-enter {
  opacity: 0;
  right: -100%;
}
.notififade-leave-to {
  opacity: 0;
  right: -100%;
}
.notififade-enter-to {
  opacity: 1;
  right: 2rem;
}
</style>
