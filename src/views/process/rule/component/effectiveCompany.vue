<template>
  <div class="effective-period">
    <div class="period-title">{{ $t_process("valid_entities") }}</div>
    <div v-if="!editing" class="effective-period-detail">
      <span>{{
        `“${$t_verify("consolidated_entities")}”${$t_common(
          "and_he"
        )}“${$t_verify("single_entities")}”`
      }}</span>
      <yn-button type="text" @click="drawerVisible = !drawerVisible">
        {{ $t_process("click_to_view") }}
      </yn-button>
    </div>
    <yn-form
      v-else
      :form="periodForm"
      :colon="false"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 18 }
      }"
      class="effective-period-tranfer"
    >
      <yn-form-item
        v-for="item in periodDimList"
        :key="item.objectId"
        class="period-item"
        :label="item.dimName"
      >
        <ShowDimListInput
          v-decorator="[
            item.dimCode,
            {
              rules: [
                {
                  required: ruleCmpValidate ? false : true,
                  message: `${$t_common('input_select')}${
                    item.dimName
                  }, ${$t_process('scope_entiy_unempty')}`
                }
              ],
              initialValue: item.members
            }
          ]"
          dynamicOrStatic="dynamic"
          :dimInfo="item"
          @getExp="val => getExp(val, item)"
        />
      </yn-form-item>
    </yn-form>

    <!-- 查看生效期间 -->
    <yn-drawer
      wrapClassName="period-drawer"
      :title="$t_verify('valid_entity_range')"
      :visible="drawerVisible"
      @close="() => (drawerVisible = !drawerVisible)"
    >
      <div
        class="period-drawer-content"
        :style="{ maxHeight: `${contentHeight}px` }"
      >
        <div
          v-for="item in periodDimList"
          :key="item.dimId"
          class="period-type"
        >
          <p class="period-type-title">{{ item.dimName }}</p>
          <p
            v-for="member in item.selectedItem"
            :key="member.objectId"
            class="period-type-name"
          >
            {{ member.dimMemberName }}
          </p>
        </div>
      </div>
    </yn-drawer>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-button/";
import { mapMutations, mapState } from "vuex";
import ShowDimListInput from "@/components/hoc/ShowDimListInput.vue";
import AppUtils from "yn-p1/libs/utils/AppUtils";
import taskFlowTempService from "@/services/taskFlowTemp";
import DIM_INFO from "@/constant/dimMapping";
import { formatRequestParams } from "@/utils/journal.js";
import { pavingTree, addKeyToData, composeFuns } from "@/utils/taskFlowTemp.js";
import { companyTransferData } from "@/utils/process.js";

export default {
  components: { ShowDimListInput },
  props: {
    editing: {
      type: Boolean,
      default: false
    },
    effectiveCompany: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      periodForm: this.$form.createForm(this, "periodForm"),
      menubars: {},
      periodDimList: [
        {
          dimName: this.$t_verify("single_entities"),
          dimCode: "Entity",
          objectId: DIM_INFO.Entity,
          dimId: DIM_INFO.Entity,
          selectedItem: [],
          members: "",
          dynamicOrStatic: "dynamic"
        },
        {
          dimName: this.$t_verify("consolidated_entities"),
          dimCode: "Scope",
          objectId: DIM_INFO.Scope,
          dimId: DIM_INFO.Scope,
          selectedItem: [],
          members: "",
          dynamicOrStatic: "dynamic"
        }
      ],
      drawerVisible: false,
      contentHeight: 0,
      dimMemberMap: {}
    };
  },
  computed: {
    ...mapState("processStore", {
      ruleCmpValidate: state => state.ruleCmpValidate
    })
  },
  watch: {
    effectiveCompany: {
      deep: true,
      async handler(v) {
        await this.initData(v);
      }
    },
    drawerVisible: {
      handler(v) {
        if (v) {
          this.getPageHeight();
        }
      }
    }
  },
  beforeDestroy() {
    this.setRuleCmpValidate(true);
  },
  methods: {
    ...mapMutations("processStore", [
      "setRuleCompany",
      "setRuleSpinning",
      "setRuleCmpValidate"
    ]),
    async initData(v) {
      this.setRuleSpinning(true);
      const { memberExp } = v;
      const exp = memberExp ? AppUtils.jsonParse(memberExp) : "";
      if (exp) {
        this.menubars = exp.expDtoList.reduce((pre, dim) => {
          pre[dim.dimId] = dim;
          return pre;
        }, {});
      } else {
        this.menubars = {};
        this.menubars = this.periodDimList.reduce((pre, period) => {
          pre[period.dimId] = {
            dimId: period.dimId,
            dimMemberExps: JSON.stringify({
              attrUnion: true,
              union: false,
              allMember: true
            })
          };
          return pre;
        }, {});
      }
      await this.getAllMembers(["Entity", "Scope"]);
      this.periodDimList.forEach(async dim => {
        const type = dim.dimCode.toLowerCase();
        if (v[type]) {
          dim.selectedItem = companyTransferData(v[type]);
        } else {
          dim.selectedItem = this.editing ? this.dimMemberMap[dim.dimCode] : [];
        }
        this.$set(
          dim,
          "members",
          formatRequestParams(
            this.menubars[dim.dimId] && this.menubars[dim.dimId].dimMemberExps
              ? JSON.parse(this.menubars[dim.dimId].dimMemberExps)
              : "",
            true
          )
        );
      });
      this.setRuleCompany(
        JSON.stringify({ expDtoList: Object.values(this.menubars) })
      );
      this.setRuleSpinning(false);
    },
    getPageHeight() {
      const pageH = window.innerHeight - 93;
      this.contentHeight = pageH;
    },
    // 获取单体/合并体所有成员
    async getAllMembers(dimCodeList) {
      if (Object.keys(this.dimMemberMap).length) return;
      const pList = dimCodeList.map(item =>
        taskFlowTempService("getDimMembersTree", item)
      );
      await Promise.allSettled(pList).then(res => {
        for (let i = 0; i < res.length; i++) {
          const data = res[i].value ? res[i].value.data : [];
          const result = composeFuns(pavingTree, addKeyToData)(data);
          this.dimMemberMap[this.periodDimList[i].dimCode] = [...result];
        }
      });
    },
    getExp(val, item) {
      const dataList = [
        "memberType",
        "member",
        "level",
        "attr",
        "subset",
        "variable"
      ];
      const hasData = dataList.some(key => {
        return val[key] && val[key].length > 0;
      });
      if (!hasData && !val.allMember) {
        val = "";
      }
      this.$set(item, "members", val);
      const member = {
        dimId: item.dimId,
        dimMemberExps: formatRequestParams(val)
      };
      this.menubars[item.dimId] = val ? member : "";
      this.periodForm.setFieldsValue({
        [item.dimCode]: this.menubars[item.dimId]
      });
      if (Object.values(this.menubars).some(item => item)) {
        this.setRuleCmpValidate(true);
      } else {
        this.setRuleCmpValidate(false);
      }
      this.$nextTick(() =>
        this.periodForm.validateFields(
          this.periodDimList.map(item => item.dimCode),
          { force: true }
        )
      );
      this.setRuleCompany(
        JSON.stringify({
          expDtoList: Object.values(this.menubars).filter(item => item)
        })
      );
      this.$emit("addSaveEventCb");
    }
  }
};
</script>

<style lang="less">
.period-drawer {
  .ant-drawer-body {
    padding: @yn-padding-xxxl 0 @yn-padding-xxxl @yn-padding-xxxl;
  }
}
</style>
<style lang="less" scoped>
.effective-period {
  .period-title,
  .effective-period-detail {
    height: @rem22;
    line-height: @rem22;
    font-weight: 400;
    font-size: @rem14;
  }
  .period-title {
    font-weight: 500;
    color: @yn-heading-color;
  }
  .effective-period-detail {
    margin-top: @yn-margin-xl;
    color: @yn-text-color-secondary;
    margin-bottom: @yn-margin-xxxl;
  }
  .effective-period-tranfer {
    display: flex;
    flex-wrap: wrap;
    margin-top: @yn-margin-xl;
    .period-item {
      width: 33.33%;
      /deep/.ant-form-item-control {
        line-height: normal;
      }
      /deep/.ant-form-item-no-colon {
        color: @yn-text-color-secondary;
      }
      .dim-list-input {
        width: 100%;
      }
    }
  }
}

.period-drawer-content {
  overflow: scroll;
}
.period-type {
  .period-type-title {
    font-size: @rem14;
    color: @yn-label-color;
  }
  .period-type-name {
    font-size: @rem14;
    color: @yn-text-color;
  }
}
</style>
