<template>
  <div :class="['define-task-flow', { edit: editing }]">
    <div class="task-flow-title">{{ $t_verify("results_display") }}</div>
    <div v-if="!editing" class="effective-period-detail">
      <span>{{ showMap[showType] }}</span>
    </div>
    <yn-radio-group
      v-else
      v-model="showType"
      class="effective-period-detail"
      @change="onChange"
    >
      <yn-radio value="result">{{ $t_verify("validation_results") }}</yn-radio>
      <yn-radio value="equation">{{ $t_verify("lhs_rhs_results") }}</yn-radio>
    </yn-radio-group>
    <yn-table
      rowKey="objectId"
      class="task-flow-table"
      :columns="columns"
      :data-source="tableData"
      :expandedRowKeys="expandedRowKeys"
      :customRow="customRow"
      :rowSelection="
        editing
          ? {
            selectedRowKeys: selectedRowKeysAll,
            onSelectAll: onSelectAll,
            onSelect: handlerSelect
          }
          : false
      "
      :scroll="{ x: true }"
      @expandedRowsChange="expandedRowsChange"
    >
      <div
        v-if="!record.children && !FIXED.includes(record.objectId)"
        slot="table.resultFormUrl"
        slot-scope="text, record"
      >
        <form-item
          :record="record"
          :editing="editing"
          :pavingData="pavingData"
          field="resultFormUrl"
          @click-form="clickFormCell"
        />
      </div>
      <div
        v-if="!record.children && !FIXED.includes(record.objectId)"
        slot="table.leftFormUrl"
        slot-scope="text, record"
      >
        <form-item
          :record="record"
          :editing="editing"
          :pavingData="pavingData"
          field="leftFormUrl"
          @click-form="clickFormCell"
        />
      </div>
      <div
        v-if="!record.children && !FIXED.includes(record.objectId)"
        slot="table.rightFormUrl"
        slot-scope="text, record"
      >
        <form-item
          :record="record"
          :editing="editing"
          :pavingData="pavingData"
          field="rightFormUrl"
          @click-form="clickFormCell"
        />
      </div>
      <span
        slot="table.verificationName"
        slot-scope="text"
        :title="text"
        class="task-flow-name text-ellipsis"
      >
        {{ text }}
      </span>
      <div
        v-if="!record.children && !FIXED.includes(record.objectId)"
        slot="table.remark"
        slot-scope="text, record"
        class="task-flow-remark"
      >
        <template v-if="editing">
          <yn-input
            v-model="record.remark"
            :placeholder="$t_common('input_message')"
            :class="{
              'valid-error-border': record.remark.length > 250
            }"
            @change="handlerChange(record)"
          />
          <span :class="getErrorStyle(record)">
            <span
              v-show="record.remark.length > 250"
              class="invalid-tip-text-task"
            >
              {{ $t_process("field_length_exceeds_250") }}
            </span>
          </span>
        </template>
        <span
          v-else
          v-tooltip="{
            visibleOnOverflow: true,
            title: text
          }"
          class="flow-remark-text"
        >
          {{ text || "-" }}
        </span>
      </div>
    </yn-table>

    <transfer-like
      :visible.sync="formVisible"
      :clickFormInfo="clickFormInfo"
      @getForm="getForm"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-select-option/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-radio/";

import FormItem from "./formItem.vue";
import { mapActions, mapState, mapMutations } from "vuex";
import cloneDeep from "lodash/cloneDeep";

import TransferLike from "@/components/hoc/transferLike/index.vue";

const FIXED = [];

export default {
  components: { TransferLike, FormItem },

  props: {
    editing: {
      type: Boolean,
      default: false
    },
    flowTableInfo: {
      type: Array,
      default: () => []
    },
    verificationType: {
      type: String,
      default: "result"
    }
  },
  data() {
    return {
      columnsManage: Object.freeze({
        equation: [
          {
            width: 300,
            title: this.$t_verify("validation_account"),
            dataIndex: "verificationName",
            scopedSlots: {
              customRender: "verificationName"
            }
          },
          {
            width: 300,
            title: this.$t_verify("lhs_linked_to_forms"),
            dataIndex: "leftFormUrl",
            scopedSlots: {
              customRender: "leftFormUrl"
            }
          },
          {
            width: 300,
            title: this.$t_verify("rhs_linked_to_forms"),
            dataIndex: "rightFormUrl",
            scopedSlots: {
              customRender: "rightFormUrl"
            }
          },
          {
            title: this.$t_verify("comments"),
            dataIndex: "remark",
            scopedSlots: {
              customRender: "remark"
            }
          }
        ],
        result: [
          {
            width: 300,
            title: this.$t_verify("validation_account"),
            dataIndex: "verificationName",
            scopedSlots: {
              customRender: "verificationName"
            }
          },
          {
            width: 300,
            title: this.$t_verify("results_linked_to_validation"),
            dataIndex: "resultFormUrl",
            scopedSlots: {
              customRender: "resultFormUrl"
            }
          },
          {
            title: this.$t_verify("comments"),
            dataIndex: "remark",
            scopedSlots: {
              customRender: "remark"
            }
          }
        ]
      }),
      showMap: Object.freeze({
        result: this.$t_verify("validation_results"),
        equation: this.$t_verify("lhs_rhs_results")
      }),
      edit: [],
      FIXED,
      selections: [],
      selectedRowKeysAll: [],
      tableData: [],
      treeMap: {},
      expandedRowKeys: [],
      formVisible: false,
      clickFormInfo: "",
      fieldEmpty: false
    };
  },
  computed: {
    ...mapState({
      pavingData: state => state.processStore.pavingFormList
    }),
    showType: {
      set(value) {
        this.$emit("update:verificationType", value);
      },
      get() {
        return this.verificationType || "result";
      }
    },
    columns() {
      return this.columnsManage[this.showType];
    }
  },
  watch: {
    flowTableInfo: {
      handler(v) {
        this.initData(v);
      }
    }
  },
  created() {
    this.getFormList();
  },
  methods: {
    ...mapMutations("processStore", ["setRuleFlow"]),
    ...mapActions({
      getFormList: "processStore/getFormList"
    }),
    onSelectAll(selected, selectedRows) {
      if (selected) {
        this.selectedRowKeysAll = selectedRows.map(item => item.objectId);
      } else {
        this.selectedRowKeysAll = [...this.FIXED];
      }
      this.syncRule();
      this.$emit("addSaveEventCb");
    },
    customRow(record) {
      return {
        class: {
          "group-row": record.children || this.FIXED.includes(record.objectId)
        }
      };
    },
    syncRule() {
      this.setRuleFlow(
        this.selectedRowKeysAll.map(key => {
          const item = {
            ...this.treeMap[key],
            verificationId: key,
            verificationType: this.showType
          };
          delete item.children;
          return item;
        })
      );
    },
    handlerSelect(record, selected) {
      if (this.FIXED.includes(record.objectId)) {
        this.selectedRowKeysAll = [
          ...new Set([...this.selectedRowKeysAll, record.objectId])
        ];
        return;
      }
      if (record.children) {
        this.selectedRowKeysAll = [...this.selectedRowKeysAll];
        return;
      }
      if (selected) {
        const pid = this.getPIds(record);
        this.selectedRowKeysAll = [
          ...new Set([...this.selectedRowKeysAll, record.objectId, ...pid])
        ];
      } else {
        const cid = this.getChildIds(record);
        const singlePid = this.getSinglePid(record);
        this.selectedRowKeysAll = this.selectedRowKeysAll.filter(key => {
          return (
            (!cid.includes(key) &&
              !singlePid.includes(key) &&
              key !== record.objectId) ||
            this.FIXED.includes(key)
          );
        });
      }
      this.syncRule();
      this.$emit("addSaveEventCb");
    },
    getSinglePid(record) {
      const pid = [];
      let temp = this.treeMap[record.parentId];
      while (temp) {
        const cIds = temp.children
          .map(item => item.objectId)
          .filter(key => this.selectedRowKeysAll.includes(key));

        cIds.length === 1 && pid.push(temp.objectId);
        temp = this.treeMap[temp.parentId];
      }
      return pid;
    },
    getPIds(record) {
      const pid = [];
      let temp = this.treeMap[record.parentId];
      while (temp) {
        pid.push(temp.objectId);
        temp = this.treeMap[temp.parentId];
      }
      return pid;
    },
    getChildIds(record) {
      const pid = [];
      const loop = arr => {
        for (const item of arr) {
          pid.push(item.objectId);
          if (item.children) {
            loop(item.children);
          }
        }
      };
      record.children && loop(record.children);
      return pid;
    },
    expandedRowsChange(rows) {
      this.expandedRowKeys = rows;
    },
    onChange() {
      this.$nextTick(() => this.syncRule());
      this.$emit("addSaveEventCb");
    },

    getErrorStyle(record) {
      const bool = record.remark.length <= 250;
      if (this.fieldEmpty && !bool) {
        return "invalid-tip-text-task";
      }
      return "";
    },
    initData(v) {
      const data = cloneDeep(v);
      this.expandedRowKeys = [];
      this.FIXED = v.map(item => item.objectId);
      const selection = new Set(this.FIXED);
      const allKeys = [];
      let hasSelect = false;
      this.treeMap = {};
      const selectRow = [];
      const selectRowMap = {};
      // 单次loop处理完，提高效率
      const loop = (arr, parent) => {
        for (const item of arr) {
          // 平铺树缓存map
          this.treeMap[item.objectId] = item;
          // expand 处理
          this.expandedRowKeys.push(item.objectId);
          // selection 处理
          if (item.verificationType) {
            hasSelect = true;
            selection.add(item.objectId);
          }
          allKeys.push(item.objectId);

          // 处理表格行, 查看模式只展示选中
          if (!this.editing && item.verificationType) {
            selectRowMap[item.objectId] = { ...item };
            if (item.children) {
              selectRowMap[item.objectId].children = [];
            } else {
              delete selectRowMap[item.objectId].children;
            }
            if (!parent) {
              selectRow.push(selectRowMap[item.objectId]);
            } else {
              selectRowMap[parent.objectId].children.push(
                selectRowMap[item.objectId]
              );
            }
          }
          // 递归算法逻辑
          if (!item.children) {
            delete item.children;
          } else {
            loop(item.children, item);
          }
        }
      };
      loop(data);
      this.selectedRowKeysAll = hasSelect ? [...selection] : allKeys;
      this.syncRule();
      this.tableData = !this.editing ? selectRow : data;
    },
    getForm(forms) {
      if (this.clickFormRecord[this.clickFiled] !== forms.join(";")) {
        this.$emit("addSaveEventCb");
      }
      this.clickFormRecord[this.clickFiled] = forms.join(";");
      this.edit[this.clickFormRecord.verificationId] = {
        ...this.clickFormRecord,
        verificationId: this.clickFormRecord.objectId,
        verificationType: this.showType
      };
      this.syncRule();
    },

    handlerChange(record) {
      this.edit[record.verificationId] = {
        ...record,
        verificationId: record.objectId,
        verificationType: this.showType
      };
      this.$emit("addSaveEventCb");
      this.syncRule();
      // this.setRuleFlow(Object.values(this.edit));
    },
    clickFormCell(info, record, field) {
      this.formVisible = true;
      this.clickFormInfo = info;
      this.clickFormRecord = record;
      this.clickFiled = field;
    }
  }
};
</script>
<style lang="less">
.operate-menu {
  min-width: 112px;
}
</style>
<style lang="less" scoped>
@import "../../../../commonLess/common.less";

.define-task-flow {
  &.edit .task-flow-table /deep/ .ant-table-tbody > tr > td {
    height: 2.5625rem;
    padding: 0.25rem;
  }

  .effective-period-detail {
    height: @rem22;
    line-height: @rem22;
    font-weight: 400;
    font-size: @rem14;
    margin-top: @yn-margin-xl;
    color: @yn-text-color-secondary;
    margin-bottom: 0.75rem;
  }
  .task-flow-title {
    height: @rem22;
    font-size: @rem14;
    color: @yn-heading-color;
    font-weight: 500;
  }
  .text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: bottom;
  }
  .task-flow-table {
    /deep/ .ant-table-row-expand-icon {
      display: none;
    }
    /deep/ .group-row {
      .ant-checkbox input {
        cursor: not-allowed;
      }
      .ant-checkbox-inner {
        cursor: not-allowed;
        background-color: @yn-primary-3;
        border-color: @yn-primary-3;
      }
      .ant-checkbox-checked::after {
        border-color: @yn-primary-3;
      }
    }
    /deep/ .ant-table-tbody > tr.ant-table-row-selected td {
      color: inherit;
      background: inherit;
    }
    /deep/ .ant-table-row {
      i.anticon.anticon-caret-down,
      i.anticon.anticon-caret-right {
        display: none;
      }
    }

    /deep/.ant-table-tbody > tr > td {
      height: 2.25rem;
    }
    .ml16 {
      margin-left: @yn-margin-xl;
    }
    .task-flow-name {
      max-width: 18.75rem;
      display: inline-block;
    }
    .task-flow-remark {
      position: relative;
      min-width: 6.25rem;
      .error-tips {
        position: absolute;
        display: inline-block;
        width: 100%;
        left: 0;
        padding-left: @rem7;
        top: -@rem22;
        line-height: @rem22;
        height: @rem22;
        background: @yn-error-bg-color;
        color: @yn-error-color;
      }
    }
    .flow-remark-text {
      padding-left: @rem7;
      cursor: pointer;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .flow-remark-text {
      padding: 0 @rem16;
    }

    .link-btn {
      width: 100%;
      text-align: left;
    }
  }
}
.invalid-tip-text-task {
  display: inline-block;
  top: @rem36;
  left: 0;
  height: @rem24;
  line-height: @rem26;
  width: 100%;
  color: @yn-error-color;
  z-index: 100;
}
</style>
