<template>
  <div class="task-flow-url">
    <template v-if="editing">
      <div class="url-form" @click="clickFormCell">
        <span :class="{ 'placeholder-color': !record[field] }">
          {{ record[field] ? value : $t_common("input_select") }}
        </span>
        <svg-icon
          class="icon-cont"
          type="icon-chuansuokuang"
          :isIconBtn="false"
          @click="clickFormCell"
        />
      </div>
    </template>
    <template v-else>
      <span
        v-tooltip="{
          visibleOnOverflow: true,
          title: record[field] ? value : ''
        }"
        class="url-form-text"
      >
        {{ record[field] ? value : "-" }}
      </span>
    </template>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-input/";
export default {
  props: {
    editing: {
      type: Boolean,
      default: false
    },
    field: {
      type: String,
      default: ""
    },
    pavingData: {
      type: Array,
      default: () => []
    },
    record: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {};
  },
  computed: {
    value() {
      return this.echoFormName(this.record[this.field]);
    }
  },
  methods: {
    clickFormCell() {
      this.$emit(
        "click-form",
        this.record[this.field],
        this.record,
        this.field
      );
    },
    echoFormName(list) {
      return list
        .split(";")
        .map(key => {
          const item = this.pavingData.find(item => item.key === key);
          return item ? item.title : "";
        })
        .filter(item => item)
        .join("；");
    }
  }
};
</script>

<style lang="less" scoped>
.task-flow-url {
  max-width: 18.75rem;
  position: relative;
  .url-form {
    display: flex;
    border: 1px solid @yn-border-color-base;
    background: @yn-body-background;
    border-radius: 0.25rem;
    height: @rem32;
    line-height: @rem32;
    padding-left: @rem7;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    > span {
      flex: 1;
      display: inline-block;
      width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .url-form:hover {
    border: 1px solid @yn-primary-color;
    box-shadow: 0 0 0 2px @yn-icon-bg-color;
  }

  .url-form-text {
    display: inline-block;
    vertical-align: bottom;
    padding: 0 @rem16;
    width: 100%;
    padding-left: @rem7;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .placeholder-color {
    color: @yn-auxiliary-color;
  }
  .icon-cont {
    display: inline-block;
    height: @rem30;
    line-height: @rem30;
    cursor: pointer;
    color: @yn-disabled-color;
    margin: 0 0.5rem;
  }
}
</style>
