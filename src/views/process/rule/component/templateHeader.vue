<template>
  <div class="content-header">
    <div v-if="!editingName" class="header-detail">
      <span class="header-title">{{ templateName }}</span>
      <yn-tooltip v-show="ruleIsEdit" :title="$t_common('edit_name')">
        <yn-icon-button
          type="edit"
          class="edit-pan"
          @click="editHeader('name')"
        />
      </yn-tooltip>
    </div>
    <yn-input
      v-else
      id="nameInput"
      v-model="templateName"
      :placeholder="$t_common('input_message')"
      class="edit-name"
      :allowClear="false"
      @keyup="validate('name')"
      @blur="closeInput('name')"
    />
    <div class="header-desc">
      <div v-if="!editingDesc" class="desc-left">
        <span class="title-label label-desc">{{
          templateDesc || $t_common("no_description")
        }}</span>
        <yn-tooltip v-show="ruleIsEdit" :title="$t_common('edit_description')">
          <yn-icon-button
            type="edit"
            class="edit-pan"
            @click="editHeader('desc')"
          />
        </yn-tooltip>
      </div>
      <yn-input
        v-else
        id="descInput"
        v-model="templateDesc"
        class="edit-desc"
        :allowClear="false"
        :placeholder="$t_common('input_message')"
        @keyup="validate('desc')"
        @blur="closeInput('desc')"
      />
      <yn-divider v-show="!editingDesc && updateBy" type="vertical" />
      <span
        v-show="updateBy"
        :class="['title-label', 'update-time', editingDesc ? 'text-right' : '']"
      >
        {{ updateBy }} {{ updateTime }} {{ $t_common("modify") }}
      </span>
    </div>
    <yn-button
      v-show="!ruleIsEdit"
      type="primary"
      class="header-btn-edit"
      @click="setRuleIsEdit(true)"
    >
      {{ $t_common("edit") }}
    </yn-button>
    <yn-divider class="header-divider" />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-icon-button/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-divider/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import _debounce from "lodash/debounce";
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  data() {
    return {
      updateTime: "",
      updateBy: "",
      templateDesc: "",
      cloneTempDesc: "",
      templateName: "",
      cloneTempName: "",
      editingDesc: false,
      editingName: false
    };
  },
  computed: {
    ...mapState("processStore", {
      ruleIsEdit: state => state.ruleIsEdit,
      ruleBaseInfo: state => state.ruleBaseInfo
    })
  },
  watch: {
    ruleBaseInfo: {
      deep: true,
      handler(value) {
        this.updateTime = value.updateDate;
        this.updateBy = value.updateBy;
        this.templateDesc = value.templateDesc;
        this.cloneTempDesc = value.cloneTempDesc || value.templateDesc;
        this.templateName = value.templateName;
        this.cloneTempName = value.cloneTempName || value.templateName;
      }
    }
  },
  beforeDestroy() {
    this.setRuleValidate(true);
  },
  methods: {
    ...mapMutations("processStore", [
      "setRuleIsEdit",
      "setRuleBaseInfo",
      "setRuleValidate"
    ]),
    ...mapActions("processStore", ["validateName"]),
    editHeader(type) {
      const w = type.charAt(0).toUpperCase() + type.slice(1);
      this[`editing${w}`] = true;
      this.setRuleValidate(false);
      this.$nextTick(() => {
        const editInput = document.getElementById(`${type}Input`);
        editInput.focus();
      });
    },
    processError(type) {
      if (type === "name") {
        if (!this.templateName) {
          UiUtils.errorMessage(this.$t_process("config_name_empty"));
          this.templateName = this.cloneTempName;
        }
        if (this.templateName.length > 64) {
          UiUtils.errorMessage(this.$t_process("field_length_exceeds_64"));
          this.templateName = this.cloneTempName;
        }
      }
      if (type === "desc" && this.templateDesc.length > 50) {
        UiUtils.errorMessage(this.$t_process("field_length_exceeds_50"));
        this.templateDesc = this.cloneTempDesc;
      }
    },
    validate: _debounce(function(type) {
      if (type === "name") {
        if (!this.templateName) {
          this.setRuleValidate(false);
          return false;
        }
        if (this.templateName.length > 64) {
          this.setRuleValidate(false);
          return false;
        }
      }
      if (type === "desc" && this.templateDesc.length > 50) {
        this.setRuleValidate(false);
        return false;
      }
      this.setRuleValidate(true);
      this.syncInfo();
      return true;
    }, 300),
    syncInfo() {
      this.setRuleBaseInfo({
        ...this.ruleBaseInfo,
        updateTime: this.updateTime,
        updateBy: this.updateBy,
        cloneTempName: this.cloneTempName,
        cloneTempDesc: this.cloneTempDesc,
        templateDesc: this.templateDesc,
        templateName: this.templateName
      });
    },
    closeInput(type) {
      const w = type.charAt(0).toUpperCase() + type.slice(1);
      this[`editing${w}`] = false;
      if (this.validate(type)) {
        this.validateName().then(flag => {
          if (flag) {
            this.$emit("change");
          } else {
            this.templateName = this.cloneTempName;
            this.syncInfo();
          }
        });
      } else {
        this.processError(type);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.content-header {
  width: 100%;
  background: @yn-component-background;
  padding: 0.25rem 0;
  height: 5rem;
  position: relative;
  .header-divider {
    margin: 0.5rem 0 0;
  }
  .header-detail,
  .edit-name {
    margin-top: -@yn-margin-xs;
  }
  .edit-name,
  .edit-desc {
    border: none;
    caret-color: @yn-primary-color;
    font-weight: 500;
    padding: 0;
    &:focus {
      box-shadow: none;
    }
  }
  .edit-desc {
    font-weight: 400;
    color: @yn-label-color;
  }
  .header-detail {
    width: calc(100% - 5rem);
    display: flex;
    align-items: center;
    .header-title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-weight: 500;
      display: inline-block;
    }
  }
  .header-title {
    display: inline-block;
    height: @rem32;
    line-height: @rem32;
    font-size: @rem14;
    color: @yn-text-color;
    font-weight: 500;
  }
  .header-desc {
    display: flex;
    align-items: center;
    width: 100%;
    .desc-left {
      display: flex;
      align-items: center;
      max-width: calc(100% - 17.5rem);
    }
    .title-label {
      color: @yn-label-color;
      font-size: @rem14;
      font-family: PingFangSC-Regular;
    }
    .label-desc {
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: @rem32;
      line-height: @rem32;
    }
    .update-time {
      min-width: 23.75rem;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }
    .text-right {
      text-align: right;
    }
  }
  .header-btn-edit {
    position: absolute;
    right: 0;
    top: 0;
  }
}
</style>
