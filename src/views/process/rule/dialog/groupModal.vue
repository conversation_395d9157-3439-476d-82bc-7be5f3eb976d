<template>
  <div class="group-modal">
    <yn-modal
      :visible="visible"
      :bodyStyle="{ minHeight: '60px' }"
      :title="title"
      :okText="$t_common('save')"
      @cancel="cancelEvent"
    >
      <template slot="footer">
        <yn-button key="cancel" @click="cancelEvent">
          {{ $t_common("cancel") }}
        </yn-button>
        <yn-button
          key="submit"
          type="primary"
          :loading="btnLoading"
          @click="okEvent"
        >
          {{ $t_common("save") }}
        </yn-button>
      </template>
      <yn-form
        :form="formGroup"
        :colon="false"
        v-bind="{
          labelCol: { span: 6 },
          wrapperCol: { span: 16 }
        }"
      >
        <yn-form-item :label="$t_process('group_name')">
          <yn-input
            v-decorator="[
              'groupName',
              {
                initialValue: model === 'update' ? data.title : '',
                rules: [
                  { required: true, message: $t_common('input_message') },
                  { max: 64, message: $t_process('field_length_exceeds_64') }
                ]
              }
            ]"
            :placeholder="$t_common('input_message')"
            @change="syncLang"
          >
            <!-- <yn-icon
              slot="suffix"
              class="cursor-p"
              type="global"
              @click="handleLanguages"
            /> -->
            <svg-icon
              slot="suffix"
              class="cursor-p"
              type="icon-multilingual"
              @click="handleLanguages"
            />
          </yn-input>
          <div v-show="renameTips" class="rename-tips">
            {{ renameTipInfo }}
          </div>
        </yn-form-item>
      </yn-form>
    </yn-modal>
    <language-modal
      :languageVisible="languageVisible"
      :languageList.sync="languageList"
      :curText="curText"
      :addCurLang="true"
      @cancelMultilanguage="cancelMultilanguage"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-option/";
import LanguageModal from "@/components/multilanguage";
import UiUtils from "yn-p1/libs/utils/UiUtils.js";
import { mapState, mapActions } from "vuex";
import renameTip from "@/mixin/renameTip";
import { PROCESS_RULE_CODE } from "@/constant/templateCode";
import precessService from "@/services/process";
export default {
  components: { LanguageModal },
  mixins: [renameTip],
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    model: {
      type: String,
      default: "add"
    },
    title: {
      type: String,
      default: ""
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formGroup: this.$form.createForm(this, "formGroup"),
      btnLoading: false,
      curText: "",
      languageVisible: false,
      languageList: []
    };
  },
  computed: {
    ...mapState({
      // 当前语言环境
      lang: state => state.common.lang
    })
  },
  created() {
    this.setSameNameF();
    this.formGroup.resetFields();
    this.setLang();
    if (this.model === "update") {
      this.curText = this.data.title;
      this.getGroupLanguage();
    }
  },
  methods: {
    ...mapActions({
      setLang: "common/getEnableLanguages"
    }),
    syncLang(e) {
      this.curText = e.target.value;
      this.setSameNameF();
    },
    // 获取分组多语言
    getGroupLanguage() {
      precessService("getTemplateInfoById", this.data.objectId).then(res => {
        const {
          commonTemplate: { multiLanguages }
        } = res.data.data;
        multiLanguages &&
          (this.languageList = multiLanguages.filter(
            item => item.languageCode !== this.lang
          ));
      });
    },
    handleLanguages() {
      this.languageVisible = true;
    },
    cancelMultilanguage(languageInfo) {
      this.languageVisible = false;
      this.languageList = languageInfo;
    },
    cancelEvent() {
      this.formGroup.resetFields();
      this.$emit("update:visible", false);
    },
    okEvent() {
      this.formGroup.validateFields((err, value) => {
        if (!err) {
          this.handleOk(value);
        }
      });
    },
    handleOk(values) {
      const { groupName: templateName } = values;
      const params = {
        operateType: this.model,
        templateName,
        templateFlag: false,
        templatePosition: "",
        templateModule: PROCESS_RULE_CODE,
        multiLanguages: this.languageList
      };
      if (this.model === "update") {
        params.objectId = this.data.objectId;
        params.systemFlag = this.data.systemFlag;
      }
      this.btnLoading = true;
      precessService("addEditTemplate", params)
        .then(res => {
          UiUtils.successMessage(
            this.model === "add"
              ? this.$t_common("add_success")
              : this.$t_common("edit_success")
          );
          this.cancelEvent();
          this.$emit("onSave", "group");
        })
        .finally(() => {
          this.btnLoading = false;
        });
    }
  }
};
</script>

<style lang="less" scoped>
.rename-tips {
  color: @yn-error-color;
  height: @rem22;
  line-height: @rem22;
}
.cursor-p {
  cursor: pointer;
  line-height: 1.5rem !important;
  height: 1.5rem !important;
  padding: 0 0.25rem !important;
}
</style>
