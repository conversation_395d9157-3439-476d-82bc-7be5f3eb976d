<template>
  <div class="template-modal">
    <yn-modal
      :visible="visible"
      :title="title"
      :okText="$t_common('save')"
      @cancel="cancelEvent"
    >
      <template slot="footer">
        <yn-button key="cancel" @click="cancelEvent">
          {{ $t_common("cancel") }}
        </yn-button>
        <yn-button
          key="submit"
          type="primary"
          :loading="btnLoading"
          @click="okEvent"
        >
          {{ $t_common("save") }}
        </yn-button>
      </template>
      <yn-spin :spinning="spinning">
        <yn-form
          :form="formTemplate"
          :colon="false"
          v-bind="{
            labelCol: { span: 6 },
            wrapperCol: { span: 16 }
          }"
        >
          <yn-form-item :label="$t_verify('name')">
            <yn-input
              v-decorator="[
                'templateName',
                {
                  initialValue: templateName,
                  rules: [
                    { required: true, message: $t_common('input_message') },
                    { max: 64, message: $t_process('field_length_exceeds_64') }
                  ]
                }
              ]"
              :placeholder="$t_common('input_message')"
              @change="syncLang"
            >
              <svg-icon
                slot="suffix"
                class="cursor-p"
                type="icon-multilingual"
                @click="handleLanguages"
              />
            </yn-input>
            <div v-show="renameTips" class="rename-tips">
              {{ renameTipInfo }}
            </div>
          </yn-form-item>
          <yn-form-item :label="$t_verify('group')">
            <yn-select
              v-decorator="[
                'parentId',
                {
                  initialValue: data.parentId,
                  rules: [
                    { required: true, message: $t_common('input_select') }
                  ]
                }
              ]"
              :placeholder="$t_common('input_select')"
              showSearch
              :filterOption="filterOption"
            >
              <yn-select-option v-for="item in groupList" :key="item.key">
                {{ item.label }}
              </yn-select-option>
            </yn-select>
          </yn-form-item>
          <yn-form-item :label="$t_common('explain')" class="form-remark">
            <yn-textarea
              v-decorator="[
                'remark',
                {
                  initialValue: templateDesc,
                  rules: [
                    {
                      required: false,
                      max: 50,
                      message: $t_process('field_length_exceeds_50')
                    }
                  ]
                }
              ]"
              :allowClear="false"
              type="textarea"
              :placeholder="$t_common('input_message')"
              :autoSize="{ minRows: 3, maxRows: 8 }"
              @change="onChange($event)"
            />
            <div class="text-count">{{ descriptionLen }}/50</div>
          </yn-form-item>
        </yn-form>
      </yn-spin>
    </yn-modal>

    <language-modal
      :languageVisible="languageVisible"
      :languageList.sync="languageList"
      :curText="templateName"
      :addCurLang="true"
      @cancelMultilanguage="cancelMultilanguage"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-select-option/";
import "yn-p1/libs/components/yn-textarea/";
import LanguageModal from "@/components/multilanguage";
import { mapState, mapMutations, mapActions } from "vuex";
import UiUtils from "yn-p1/libs/utils/UiUtils.js";
import renameTip from "@/mixin/renameTip";
import { PROCESS_RULE_CODE } from "@/constant/templateCode";
import precessService from "@/services/process";
import { nextTick } from "vue";
export default {
  components: { LanguageModal },
  mixins: [renameTip],
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    model: {
      type: String,
      default: "add"
    },
    title: {
      type: String,
      default: ""
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      spinning: false,
      formTemplate: this.$form.createForm(this, "formTemplate"),
      btnLoading: false,
      groupList: [],
      templateName: "",
      descriptionLen: 0,
      templateDesc: "",
      languageVisible: false,
      languageList: []
    };
  },
  computed: {
    ...mapState({
      // 当前语言环境
      lang: state => state.common.lang
    })
  },
  created() {
    this.setSameNameF();
    this.setLang();
    this.getGroups();
    this.getTemplateBase(this.data.key);
  },
  methods: {
    ...mapMutations("processStore", ["setRuleIsEdit"]),
    ...mapActions({
      setLang: "common/getEnableLanguages"
    }),
    syncLang(e) {
      this.templateName = e.target.value;
      this.setSameNameF();
    },
    handleLanguages() {
      this.languageVisible = true;
    },
    cancelMultilanguage(languageInfo) {
      this.languageVisible = false;
      this.languageList = languageInfo;
    },
    getTemplateBase(key) {
      if (!key) {
        this.templateDesc = "";
        this.descriptionLen = 0;
        return;
      }
      this.spinning = true;
      precessService("getTemplateInfoById", key)
        .then(res => {
          const {
            commonTemplate: { templateDesc, multiLanguages, templateName }
          } = res.data.data;
          this.templateDesc = templateDesc;
          this.templateName = templateName;
          this.descriptionLen = templateDesc.length;
          const currentLang = this.$store.state.common.lang;

          const langList = multiLanguages && multiLanguages.filter(
            item => item.languageCode !== currentLang
          ).map(item => ({
            languageCode: item.languageCode,
            text: item.text
          }));
          nextTick(() => {
            // this.$set(this, "languageList", langList);
            this.languageList = langList || [];
            // this.$emit("update:languageList", langList);
          });
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    async getGroups() {
      const {
        data: { data }
      } = await precessService("getTemplateTree", PROCESS_RULE_CODE);
      this.spinning = false;
      if (data && data.length) {
        data.forEach(item => {
          item.label = item.templateName;
          item.key = item.objectId;
        });
        this.groupList = data;
      } else {
        this.groupList = [
          {
            label: this.$t_common("default_group"),
            key: "-1"
          }
        ];
      }
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    cancelEvent() {
      this.formTemplate.resetFields();
      this.$emit("update:visible", false);
    },
    okEvent() {
      this.formTemplate.validateFields((err, value) => {
        if (!err) {
          this.handleOk(value);
        }
      });
    },
    handleOk(values) {
      const { templateName, parentId, remark } = values;
      const params = {
        operateType: this.model,
        templateName,
        templateFlag: true,
        templateDesc: remark,
        templateModule: PROCESS_RULE_CODE,
        multiLanguages: this.languageList
      };
      this.model === "update" && (params.objectId = this.data.objectId);
      parentId !== "-1" && (params.templateParentId = parentId);
      this.btnLoading = true;
      precessService("addEditTemplate", params)
        .then(res => {
          UiUtils.successMessage(
            this.model === "add"
              ? this.$t_common("add_success")
              : this.$t_common("edit_success")
          );
          this.cancelEvent();
          this.$emit("onSave", "template", res.data.data);
          this.model === "add" && this.setRuleIsEdit(true);
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    onChange(e) {
      this.descriptionLen = e.target.value.length;
    }
  }
};
</script>

<style lang="less" scoped>
.rename-tips {
  color: @yn-error-color;
  height: @rem22;
  line-height: @rem22;
}

.same-name {
  color: @yn-error-color;
  height: @rem22;
  line-height: @rem22;
}
.cursor-p {
  cursor: pointer;
  line-height: 1.5rem !important;
  height: 1.5rem !important;
  padding: 0 0.25rem !important;
}
.text-count {
  height: @rem22;
  line-height: @rem22;
  position: absolute;
  top: -@rem10;
  right: @rem10;
  color: @yn-disabled-color;
}
.form-remark {
  /deep/.ant-form-item-control {
    .ant-input {
      height: 6rem;
    }
  }
}
</style>
