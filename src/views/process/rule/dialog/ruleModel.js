export default {
  data() {
    return {
      model: {},
      groupVisible: false,
      templateVisible: false
    };
  },
  methods: {
    addGroup(data, item) {
      this.savePromptMixin().then(() => {
        this.model = { title: item.label, data: {}, model: "add" };
        this.groupVisible = true;
      });
    },
    editGroup(data, item) {
      this.savePromptMixin().then(() => {
        this.model = { title: item.label, data: data, model: "update" };
        this.groupVisible = true;
      });
    },
    addTemplate(data, item) {
      this.savePromptMixin().then(() => {
        if (data) {
          this.model = {
            title: item.label,
            data: { parentId: data.key },
            model: "add"
          };
        } else {
          this.model = {
            title: item.label,
            model: "add",
            data: {
              parentId: this.treeConfig.treeData.filter(
                item => item.systemFlag === "true"
              )[0].key
            }
          };
        }

        this.templateVisible = true;
      });
    },
    editTemplate(data, item) {
      this.savePromptMixin().then(res => {
        const { status, id } = res || {};
        if (status === "reject") return; // 保存失败，返回
        if (data.flag && status === "notSave") return; // 复制但不保存，返回
        if (data.flag) {
          // 复制的模板，需要设置新key
          data.key = id;
          data.objectId = id;
        }
        this.model = { title: item.label, data: data, model: "update" };
        this.templateVisible = true;
      });
    }
  }
};
