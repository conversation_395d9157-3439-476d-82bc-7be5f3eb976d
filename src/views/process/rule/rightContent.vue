<template>
  <yn-spin :spinning="ruleSpinning" class="right-content">
    <template-header @change="addSaveEventCb" />
    <div
      :class="['content-main', ruleIsEdit ? 'edit-template' : 'view-template']"
    >
      <!-- 生效期间 -->
      <effective-period
        :editing="ruleIsEdit"
        :effectivePeriod="effectivePeriod"
        @addSaveEventCb="addSaveEventCb"
      />
      <effective-company
        :editing="ruleIsEdit"
        :effectiveCompany="effectiveCompany"
        @addSaveEventCb="addSaveEventCb"
      />
      <!-- 定义任务流 -->
      <define-task-flow
        :editing="ruleIsEdit"
        :flowTableInfo="flowTableInfo"
        :verificationType.sync="verificationType"
        @addSaveEventCb="addSaveEventCb"
      />
    </div>
  </yn-spin>
</template>

<script>
import EffectivePeriod from "./component/effectivePeriod.vue";
import EffectiveCompany from "./component/effectiveCompany.vue";
import TemplateHeader from "./component/templateHeader.vue";

import DefineTaskFlow from "./component/defineTaskFlow.vue";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-spin/";
import { query } from "@/utils/process";
import _debounce from "lodash/debounce";
import precessService from "@/services/process";
import "yn-p1/libs/components/yn-icon-button/";
import tip from "./plugin/tip";
import moment from "moment";
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  components: {
    EffectivePeriod,
    DefineTaskFlow,
    EffectiveCompany,
    TemplateHeader
  },
  mixins: [tip],
  props: {
    cancel: {
      type: Function,
      default: null
    },
    updateMessage: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      flowTableInfo: [],
      verificationType: "result",
      saveTips: this.$t_process("do_save_check_config"),
      effectivePeriod: {},
      effectiveCompany: {}
    };
  },
  computed: {
    ...mapState("common", {
      currentUserInfo: state => state.currentUserInfo
    }),
    ...mapState("processStore", {
      ruleIsEdit: state => state.ruleIsEdit,
      ruleSpinning: state => state.ruleSpinning,
      ruleSelectTreeNode: state => state.ruleSelectTreeNode
    })
  },
  watch: {
    ruleSelectTreeNode: {
      deep: true,
      immediate: true,
      handler(newNode) {
        if (newNode) {
          const { key, flag, copyKey } = newNode;
          const k = flag ? copyKey : key;
          if (!k) return;
          if (flag) {
            this.setRuleIsEdit(true);
            this.addSaveEventCb();
          }
          this.getTemplateInfo(k);
        }
      }
    }
  },
  methods: {
    ...mapActions("processStore", ["saveRule"]),
    ...mapMutations("processStore", [
      "setRuleIsEdit",
      "setRuleSpinning",
      "setRuleBaseInfo"
    ]),
    getTemplateInfo: _debounce(
      query(
        function(key) {
          this.setRuleSpinning(true);
          return precessService("getTemplateInfoById", key).catch(res => {
            this.setRuleSpinning(false);
            this.$emit("reset");
          });
        },
        function(res) {
          const {
            flag,
            name: copyName,
            key: objectId,
            sourceObjectId
          } = this.ruleSelectTreeNode;
          const {
            data: {
              data: {
                processVerificationItemList,
                entityMembers,
                periodMembers,
                scopeMembers,
                versionMembers,
                yearMembers,
                commonTemplate,
                companyObjectId,
                memberExp,
                commonTemplate: {
                  updateBy,
                  updateDate,
                  templateName,
                  templateDesc
                }
              }
            }
          } = res;

          this.setRuleBaseInfo({
            ...commonTemplate,
            companyObjectId,
            sourceObjectId,
            objectId,
            updateBy: flag ? this.currentUserInfo.userName : updateBy,
            updateDate: flag
              ? moment().format("YYYY-MM-DD HH:mm:ss")
              : updateDate,
            templateDesc,
            templateName: flag ? copyName : templateName
          });
          this.effectiveCompany = {
            memberExp,
            scope: scopeMembers,
            entity: entityMembers
          };
          this.effectivePeriod = {
            period: periodMembers,
            version: versionMembers,
            year: yearMembers
          };
          this.flowTableInfo = processVerificationItemList || [];
          this.verificationType = processVerificationItemList[0]
            ? processVerificationItemList[0].verificationType
            : "result";
          this.setRuleSpinning(false);
        }
      ),
      300
    ),
    notSaveEvent() {
      if (this.ruleSelectTreeNode.flag) {
        this.cancel && this.cancel({ dataRef: this.ruleSelectTreeNode });
        // this.$emit("cancel", { dataRef: this.selectTreeNode });
      }
      this.setRuleIsEdit(false);
    },
    onSave() {
      return this.saveRule()
        .then(id => {
          this.clearCommonSaveEventsMixin();
          return id;
        })
        .catch(message => {
          if (Array.isArray(message)) {
            this.saveFailed({
              list: message,
              tip: this.$t_process("has_check_more_config")
            });
          }
          return Promise.reject({ status: "reject" });
        });
    },
    addSaveEventCb() {
      this.addCallBackFnMixin(this.onSave, this.notSaveEvent, this.saveTips);
    }
  }
};
</script>

<style lang="less" scoped>
@import "./plugin/tip.less";
.right-content {
  display: flex;
  flex-direction: column;
  padding-right: 0.625rem;
  width: 100%;
  height: 100%;
  .content-main {
    height: 0;
    flex: 1;
    width: 100%;
    overflow-y: scroll;
    background: @yn-component-background;
    padding: 0.25rem 0.375rem 0 0;
    margin-right: -0.3125rem;
    width: calc(100% + 0.375rem);
    &::-webkit-scrollbar {
      width: 0.3125rem;
    }
  }
  .view-template {
    height: calc(100% - 5rem);
  }
  .edit-template {
    height: calc(100% - 5rem);
  }
}
</style>
