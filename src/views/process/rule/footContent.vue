<template>
  <div class="content-footer">
    <yn-divider class="footer-divider" />
    <div class="content">
      <yn-button class="footer-btn" @click="onCancel">
        {{ $t_common("cancel") }}
      </yn-button>
      <yn-button
        class="footer-btn"
        type="primary"
        :loading="ruleSaving"
        @mousedown="onSave"
      >
        {{ $t_common("save") }}
      </yn-button>
    </div>
  </div>
</template>

<script>
import tip from "./plugin/tip";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-divider/";
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  mixins: [tip],
  data() {
    return {};
  },
  computed: {
    ...mapState("processStore", {
      ruleSaving: state => state.ruleSaving,
      ruleSelectTreeNode: state => state.ruleSelectTreeNode
    })
  },
  methods: {
    ...mapActions("processStore", ["saveRule"]),
    ...mapMutations("processStore", ["setRuleIsEdit", "setRuleSaving"]),
    async onSave() {
      this.saveRule()
        .then(id => {
          this.clearCommonSaveEventsMixin();
        })
        .catch(message => {
          if (Array.isArray(message)) {
            this.saveFailed({
              list: message,
              tip: this.$t_process("has_check_more_config")
            });
          }
        });
    },
    onCancel() {
      this.savePromptMixin().then(() => {
        this.setRuleIsEdit(false);
        this.setRuleSaving(false);
      });
    }
  }
};
</script>

<style lang="less" scoped>
@import "./plugin/tip.less";
.content-footer {
  height: 3.25rem;
  .footer-divider {
    margin: 0 -1rem;
    width: calc(100% + 2rem);
  }
  .content {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 100%;
    background: @yn-component-background;
    .footer-btn {
      min-width: 5rem;
      &:first-child {
        margin-right: @yn-margin-s;
      }
    }
  }
}
</style>
