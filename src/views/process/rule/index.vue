<template>
  <div class="process-rule">
    <yn-page-tree-list
      ref="pageTreeList"
      :pageTitle="pageTitle"
      :layoutConfig="layoutConfig"
      :viewType="viewType"
      :viewTypeSource="viewTypeSource"
      treeMenuMode="normal"
      :treeConfig="treeConfig"
      :treePanelSkeleton="treePanelSkeleton"
      :initLeft="initLeft"
      :initCenter="initCenter"
      @tree_select="onSelect"
      @tree_dragenter="onDragEnter"
      @tree_drop="onDrop"
      @tree_expand="onExpand"
    >
      <template slot="initCenter.option">
        <yn-button
          type="primary"
          :title="$t_verify('new')"
          @click="addTemplate(null, { label: $t_verify('new') })"
        >
          {{ $t_verify("new") }}
        </yn-button>
      </template>
      <template slot="treeTop.operateBar">
        <top-operate @operate="handlerOperate" @refresh="refreshTree" />
      </template>
      <template v-if="ruleIsEdit" slot="page.footer">
        <foot-content />
      </template>
      <template slot="dataPanel.list">
        <right-content :cancel="deleteTemplate" @reset="refreshTree" />
      </template>
      <span slot="tree.custom" slot-scope="obj">
        <custom-title :data="obj" @operate="handlerOperate" />
      </span>
    </yn-page-tree-list>
    <group-modal
      v-if="groupVisible"
      v-bind="model"
      :visible.sync="groupVisible"
      @onSave="onSaveEvent"
    />
    <template-modal
      v-if="templateVisible"
      v-bind="model"
      :visible.sync="templateVisible"
      @onSave="onSaveEvent"
    />
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-page-tree-list/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-dropdown/";
import RightContent from "./rightContent.vue";
import CustomTitle from "./customTitle.vue";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import topOperate from "./topOperate.vue";
import FootContent from "./footContent.vue";
import GroupModal from "./dialog/groupModal.vue";
import TemplateModal from "./dialog/templateModal.vue";
import precessService from "@/services/process";
import { PROCESS_RULE_CODE } from "@/constant/templateCode";
import { mapState, mapMutations } from "vuex";
import copy from "./plugin/copy";
import ruleModel from "./dialog/ruleModel";
import { formatterData } from "@/utils/process";
import { deleteConfirmModal } from "./plugin/deleteConfirm";
export default {
  components: {
    RightContent,
    CustomTitle,
    topOperate,
    GroupModal,
    TemplateModal,
    FootContent
  },
  mixins: [copy, ruleModel],
  data() {
    return {
      searching: false,
      searchValue: "",
      viewType: "list",
      defaultGroup: [
        {
          title: this.$t_common("default_group"),
          systemFlag: "true",
          key: "-1",
          isLeaf: false,
          scopedSlots: {
            title: "custom"
          },
          children: []
        }
      ],
      viewTypeSource: [
        {
          key: "list",
          label: this.$t_common("tabulation")
        }
      ],
      pageTitle: {
        allowBack: false,
        title: this.$t_verify("urge")
      },
      layoutConfig: {
        iconPosition: "center",
        leftShow: true
      },
      treePanelSkeleton: {
        loading: false
      },
      treeConfig: {
        noFound: false,
        draggable: true,
        selectedKeys: [],
        expandedKeys: [],
        treeData: []
      },

      initLeft: {
        show: false
      },
      initCenter: {
        show: false,
        tipText: this.$t_process("check_empty_to_add")
      }
    };
  },
  computed: {
    ...mapState("processStore", {
      ruleIsEdit: state => state.ruleIsEdit,
      ruleResult: state => state.ruleResult,
      ruleIsSearch: state => state.ruleIsSearch,
      ruleSelectTreeNode: state => state.ruleSelectTreeNode
    })
  },
  watch: {
    ruleResult: {
      handler(value) {
        this.updateTree(value);
      }
    }
  },
  async created() {
    this.refreshTree();
  },
  beforeDestroy() {
    this.setRuleIsEdit(false);
    this.setRuleSelectTreeNode(null);
  },
  methods: {
    ...mapMutations("processStore", [
      "setRuleIsEdit",
      "setRuleSelectTreeNode",
      "setRuleBaseInfo",
      "setRuleIsSearch"
    ]),
    async getTemplateTree(params) {
      return precessService("getTemplateTree", PROCESS_RULE_CODE)
        .then(res => {
          return res.data.data && res.data.data.length
            ? formatterData(res.data.data)
            : this.defaultGroup;
        })
        .catch(e => {
          this.loading = false;
          return this.defaultGroup;
        });
    },
    async refreshTree(choose = true) {
      this.treeConfig.treeData = await this.getTemplateTree();
      this._catchData = this.treeConfig.treeData;
      if (this.treeConfig.treeData.some(item => item.children.length > 0)) {
        this.initCenter.show = false;
      } else {
        this.initCenter.show = true;
      }
      this.search();
      choose && this.chooseFirst();
      this.ruleSelectTreeNode.flag && this.addToParent(this.ruleSelectTreeNode);
    },
    async onSaveEvent(type, id) {
      await this.refreshTree(false);
      if (type === "template") {
        const data = await this.getTemplateBase(id);
        // 新增完模板之后选中
        const {
          objectId,
          parentId,
          updateBy,
          updateDate,
          templateName,
          templateDesc
        } = data;
        this.setRuleBaseInfo({
          ...data,
          updateBy,
          templateName, // 名称  右侧同步修改
          templateDesc, // 备注
          updateDate
        });
        const nodeInfo = {
          parentId,
          node: {
            dataRef: {
              ...data,
              isLeaf: true,
              key: objectId,
              parentId: parentId
            }
          }
        };
        this.onSelect([objectId], nodeInfo);
      }
    },
    async getTemplateBase(key) {
      if (!key) return;
      return precessService("getTemplateInfoById", key).then(res => {
        const { commonTemplate } = res.data.data;
        return commonTemplate;
      });
    },
    chooseFirst() {
      const { treeData, expandedKeys } = this.treeConfig;
      if (this.ruleIsSearch) {
        this.setRuleSelectTreeNode(treeData[0]);
        this.treeConfig.selectedKeys = [treeData[0].key];
        return;
      }
      const group = treeData.find(
        item => item.children && item.children.length > 0
      );
      if (group) {
        this.setRuleSelectTreeNode(group.children[0]);
        this.treeConfig.expandedKeys = [...expandedKeys, group.key];
        this.treeConfig.selectedKeys = [group.children[0].key];
      } else {
        this.setRuleSelectTreeNode(null);
      }
    },
    // 操作管理器
    handlerOperate(item, data) {
      this[item.key](data, item);
    },
    search(value = this._searchValue) {
      this._searchValue = value;
      if (value) {
        this.treeConfig.treeData = this._catchData
          .map(item =>
            item.children.filter(child => child.title.indexOf(value) > -1)
          )
          .flat();
      } else {
        this.treeConfig.treeData = this._catchData;
      }
    },

    deleteGroup(data) {
      const {
        dataRef: { key, title }
      } = data;
      deleteConfirmModal({
        title: this.$t_process("sure_delete_file", [title]),
        content: this.$t_process("file_config_delete_all"),
        cb: () =>
          precessService("deleteRuleTemplate", {
            objectId: key,
            templateModule: PROCESS_RULE_CODE,
            templateFlag: false
          }).then(() => {
            UiUtils.successMessage(this.$t_common("delete_success"));
            this.refreshTree();
          })
      });
    },
    deleteTemplate(data) {
      const {
        dataRef: { key, title, parentId, flag }
      } = data;
      // 删除复制模板
      if (flag) {
        const parent = this._catchData.find(item => item.key === parentId);
        let index = -1;
        for (let i = 0; i < parent.children.length; i++) {
          if (parent.children[i].key === key) {
            index = i;
            break;
          }
        }
        parent.children.splice(index, 1);
        this.search();
        this.chooseFirst();
        this.clearCommonSaveEventsMixin();
        return;
      }
      // 删除已经入库的模板
      this.savePromptMixin().then(() => {
        deleteConfirmModal({
          title: this.$t_process("sure_delete_config", [title]),
          content: this.$t_process("check_config_delete_after"),
          cb: () =>
            precessService("deleteRuleTemplate", {
              objectId: key,
              templateModule: PROCESS_RULE_CODE,
              templateFlag: true
            }).then(res => {
              UiUtils.successMessage(this.$t_common("delete_success"));
              if (key === this.ruleSelectTreeNode.objectId) {
                this.refreshTree();
              } else {
                this.refreshTree(false);
              }
            })
        });
      });
    },
    updateTree({ objectId }) {
      this.refreshTree(false).then(() => {
        if (this.ruleIsSearch) {
          const item = this.treeConfig.treeData.find(
            item => item.key === objectId
          );
          if (!item) {
            this.setRuleIsSearch(false);
            this._searchValue = "";
            this.search();
          }
        }
        if (this.ruleSelectTreeNode.flag) return;
        this.treeConfig.selectedKeys = [objectId];
      });
    },
    // tree组件方法
    onSelect(selectedKeys, info) {
      this.savePromptMixin().then(() => {
        if (selectedKeys.length === 0) return;
        const { node } = info;
        if (node.dataRef.isLeaf) {
          // 当前节点为最末子节点
          this.treeConfig.selectedKeys = selectedKeys;
          this.setRuleSelectTreeNode(node.dataRef);
          if (info.parentId) {
            this.treeConfig.expandedKeys = [
              ...this.treeConfig.expandedKeys,
              info.parentId
            ];
          }
        } else {
          // 当前节点为父节点
          let tempKeys = [...this.treeConfig.expandedKeys];
          // 当前节点未展开
          if (tempKeys.indexOf(node._props.dataRef.key) < 0) {
            tempKeys.push(node._props.dataRef.key);
          } else {
            tempKeys = tempKeys.filter(item => {
              return item !== node._props.dataRef.key;
            });
          }
          this.treeConfig.expandedKeys = tempKeys;
        }
      });
    },
    onExpand(expandedKeys) {
      this.treeConfig.expandedKeys = expandedKeys;
      this.treeConfig.autoExpandParent = false;
    },
    onDragEnter(info) {
      this.expandedKeys = info.expandedKeys;
    },
    onDrop(info) {
      this.savePromptMixin().then(res => {
        // 目标节点的信息
        const dropInfo = info.node;
        const {
          eventKey: dropKey,
          dataRef: { isLeaf: dropIsLeaf }
        } = dropInfo;
        // 拖动节点的信息
        const dragInfo = info.dragNode;
        // 拖动节点的key
        const {
          eventKey: dragKey,
          dataRef: { isLeaf: dragIsLeaf }
        } = dragInfo;
        const dropPos = info.node.pos.split("-");
        // 拖动到目标节点位置 0: 中、1:下 -1:上
        const dropPosition =
          info.dropPosition - Number(dropPos[dropPos.length - 1]);
        if (dragIsLeaf && !dropIsLeaf && dropPosition !== 0) {
          UiUtils.errorMessage(this.$t_process("not_drag_outside"));
          return;
        }
        if (!dragIsLeaf) {
          if (dropPosition === 0 || dropIsLeaf) {
            UiUtils.errorMessage(this.$t_process("not_drag_outside_file"));
            return;
          }
        }
        const positionMap = {
          "0": "down",
          "-1": "up",
          "1": "down"
        };
        const params = {
          sourceId: res && res.id ? res.id : dragKey,
          targetId: dropKey,
          moveType: positionMap[String(dropPosition)]
        };
        precessService("dragMove", params).then(() => {
          this.refreshTree(false);
        });
      });
    }
  }
};
</script>
<style lang="less" scoped>
@import "./plugin/deleteConfirm.less";
.process-rule {
  height: 100%;
  /deep/ .yn-page-list > span {
    display: flex;
    flex-direction: column;
    height: 100%;
    .ypl-data-panel {
      height: 0 !important;
      flex: 1;
    }
    .yn-page-list-footer {
      flex-shrink: 0;
    }
  }
  /deep/ .ant-tree-title {
    width: 100%;
    display: inline-block;
    & > span {
      display: inline-block;
      width: 100%;
    }
  }
  /deep/ .ant-tree-node-content-wrapper .anticon-more {
    position: absolute;
    right: 0;
    display: none;
    color: @yn-primary-color;
    line-height: @rem26;
    height: @rem26;
    width: 1.625rem;
    margin-top: 0.1875rem;
  }
  /deep/ .ant-tree-node-content-wrapper:hover .anticon-more {
    display: inline-block;
  }
  /deep/ span.ant-tree-node-selected .anticon-more {
    display: inline-block;
  }
}
</style>
