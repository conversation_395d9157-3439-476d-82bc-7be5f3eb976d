import lang from "@/mixin/lang";
const { $t_common } = lang;
function getNodeNameList(tree, field) {
  if (!Array.isArray(tree)) return [];
  const ans = [];
  const stack = [...tree];
  while (stack.length > 0) {
    const cur = stack.shift();
    ans.push(cur[field]);
    if (cur.children && cur.children.length) {
      stack.push(...cur.children);
    }
  }
  return ans;
}
export default {
  data() {
    return {};
  },
  computed: {
    nodeNameList() {
      return getNodeNameList([...this.treeConfig.treeData], "title");
    }
  },
  methods: {
    genCopy(data) {
      const {
        dataRef,
        dataRef: { key, title, flag, copyKey }
      } = data;
      let count = 1;
      while (
        ~this.nodeNameList.indexOf(`${title}_${$t_common("replica")}${count}`)
      ) {
        count++;
      }
      return {
        ...dataRef,
        key: `${key}-copy`,
        copyKey: flag ? copyKey : key,
        sourceObjectId: flag ? copyKey : key,
        templateName: `${title}_${$t_common("replica")}${count}`,
        name: `${title}_${$t_common("replica")}${count}`,
        title: `${title}_${$t_common("replica")}${count}`,
        flag: true
      };
    },
    addToParent(copyInfo) {
      const { copyKey, parentId } = copyInfo || {};
      const parent = this._catchData.find(item => item.key === parentId);
      if (!parent || parent.children.length === 0) return;
      let index = 0;
      if (parent.children.some(item => item.key === copyInfo.key)) return;
      for (let i = 0; i < parent.children.length; i++) {
        if (parent.children[i].key === copyKey) {
          index = i + 1;
          break;
        }
      }
      parent.children.splice(index, 0, copyInfo);
    },
    copyTemplate(data) {
      this.savePromptMixin().then(res => {
        const { status } = res || {};
        if (status === "reject") return; // 保存失败，返回
        const copyInfo = this.genCopy(data);
        this.addToParent(copyInfo);
        this.search();
        this.treeConfig.selectedKeys = [copyInfo.key];
        this.setRuleSelectTreeNode(copyInfo);
      });
    }
  }
};
