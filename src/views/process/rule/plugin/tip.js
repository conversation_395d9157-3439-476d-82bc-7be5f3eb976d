import { confirm } from "@/views/journal/journalList/exconfirm";
import "yn-p1/libs/components/yn-button/";
export default {
  methods: {
    renderDetail(info) {
      const { tip, list } = info;
      return (
        <div class="info-content">
          <div class="info-content-title">{tip}</div>
          <ul class="info-list">
            {list.map(item => (
              <li class="info-content-detail">{item}</li>
            ))}
          </ul>
        </div>
      );
    },
    saveFailed(info) {
      this._confirmModal = confirm({
        title: this.$t_common("save_failed"),
        type: "error",
        content: h => this.renderDetail(info),
        button: btn => (
          <yn-button
            type="primary"
            onClick={() => {
              this._confirmModal.destroy();
            }}
          >
            {this.$t_common("maintenance_oktext")}
          </yn-button>
        )
      });
    }
  }
};
