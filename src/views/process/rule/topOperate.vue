<template>
  <div v-if="ruleIsSearch" class="left-header-menu">
    <yn-input-search
      v-model="searchValue"
      class="menu-simple-search"
      :placeholder="$t_common('input_message')"
      @search="onSearch"
    />
  </div>
  <div v-else class="left-header-menu">
    <yn-dropdown>
      <yn-menu slot="overlay">
        <yn-menu-item
          v-for="item in Operate"
          :key="item.key"
          @click="handleNodeOperate(item.key)"
        >
          <span>{{ item.label }}</span>
        </yn-menu-item>
      </yn-menu>
      <yn-button type="primary" size="small" class="header-menu-btn">
        {{ $t_common("add") }}
        <yn-icon type="down" />
      </yn-button>
    </yn-dropdown>
    <div class="header-icon">
      <svg-icon
        type="icon-shuaxin"
        :title="$t_common('refresh')"
        @onClick="refreshTree"
      />
      <svg-icon
        type="icon-search1"
        :title="$t_common('search')"
        @onClick="showSearch"
      />
    </div>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-button/";
import { OperateMap } from "./constant";
import { mapState, mapMutations } from "vuex";
const OperateMapLangValue = OperateMap();
const Operate = Object.freeze([
  OperateMapLangValue.addGroup,
  OperateMapLangValue.addTemplate
]);
export default {
  data() {
    return {
      Operate,
      searchValue: ""
    };
  },
  computed: {
    ...mapState("processStore", ["ruleIsSearch"])
  },
  methods: {
    ...mapMutations("processStore", ["setRuleIsSearch"]),
    onSearch() {
      this.$emit("operate", { key: "search" }, this.searchValue);
    },
    handleNodeOperate(item) {
      this.$emit("operate", this.Operate.filter(op => op.key === item)[0]);
    },
    refreshTree() {
      this.savePromptMixin().then(() => {
        this.$emit("refresh");
      });
    },
    showSearch() {
      this.searchValue = "";
      this.setRuleIsSearch(!this.ruleIsSearch);
      this.$nextTick(() => {
        const leftSimpleSearch = document.getElementsByClassName(
          "menu-simple-search"
        )[0];
        const searchDom =
          leftSimpleSearch &&
          leftSimpleSearch.getElementsByClassName("ant-input")[0];
        if (searchDom) {
          searchDom.focus();
          searchDom.onblur = () => {
            if (!this.searchValue) {
              this.setRuleIsSearch(false);
            }
          };
        }
      });
    }
  }
};
</script>

<style scoped lang="less">
.left-header-menu {
  display: flex;
  width: 100%;
  height: @rem40;
  align-items: center;
  justify-content: space-between;
  .menu-simple-search {
    height: @rem26;
    width: 100%;
    /deep/ .ant-input {
      height: @rem26;
    }
  }
}
</style>
