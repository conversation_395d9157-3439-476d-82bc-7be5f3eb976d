import lang from "@/mixin/lang";
const { $t_common, $t_structures } = lang;

export const OperateMap = () =>
  Object.freeze({
    addGroup: {
      key: "addGroup",
      label: $t_common("new_group")
    },
    editGroup: {
      key: "editGroup",
      label: $t_common("edit_group")
    },
    deleteGroup: {
      key: "deleteGroup",
      label: $t_structures("delete_group")
    },
    addTemplate: {
      key: "addTemplate",
      label: $t_common("add_config")
    },
    editTemplate: {
      key: "editTemplate",
      label: $t_common("edit_config")
    },
    copyTemplate: {
      key: "copyTemplate",
      label: $t_common("copy_config")
    },
    deleteTemplate: {
      key: "deleteTemplate",
      label: $t_common("delete_config")
    }
  });
