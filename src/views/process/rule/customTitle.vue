<template>
  <span class="tree-title">
    <svg-icon
      v-show="!data.isLeaf"
      type="icon-wenjianjia"
      class="folder-icon"
      :isIconBtn="false"
    />
    <span class="title-content">
      {{ data.title }}{{ data.isLeaf ? "" : `(${data.children.length})` }}
    </span>
    <yn-dropdown
      placement="bottomLeft"
      overlayClassName="tree-menu-dropdown"
      :trigger="['click']"
    >
      <yn-icon-button
        class="anticon-more"
        type="more"
        size="small"
        @click.stop
      />
      <yn-menu slot="overlay">
        <template
          v-for="(item, index) in data.isLeaf
            ? leafNodeOperate
            : nonLeafNodeOperate"
        >
          <yn-menu-item
            v-if="!item.divider"
            :key="item.key"
            :disabled="item.key === 'deleteGroup' && data.systemFlag === 'true'"
            class="nodeOperate"
            @click="handleNodeOperate($event, item)"
          >
            {{ item.label }}
          </yn-menu-item>
          <yn-divider v-else :key="index" class="operate-divider" />
        </template>
      </yn-menu>
    </yn-dropdown>
  </span>
</template>

<script>
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-icon-button/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-menu/";
import { OperateMap } from "./constant";
const OperateMapLangValue = OperateMap();
const nonLeafNodeOperate = Object.freeze([
  OperateMapLangValue.addGroup,
  OperateMapLangValue.editGroup,
  OperateMapLangValue.deleteGroup,
  {
    divider: true
  },
  OperateMapLangValue.addTemplate
]);
const leafNodeOperate = Object.freeze([
  OperateMapLangValue.editTemplate,
  OperateMapLangValue.copyTemplate,
  OperateMapLangValue.deleteTemplate
]);
export default {
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      nonLeafNodeOperate: nonLeafNodeOperate,
      leafNodeOperate: leafNodeOperate
    };
  },
  methods: {
    handleNodeOperate($event, item) {
      this.$emit("operate", item, this.data);
    }
  }
};
</script>

<style lang="less" scoped>
.folder-icon {
  margin-right: 0.25rem;
  color: @yn-label-color;
}
.tree-title {
  display: inline-flex;
  align-items: center;
  width: 100%;
  .title-content {
    display: inline-block;
    width: calc(100% - 2.5rem) !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
/deep/ .yn-page-list .yn-component {
  display: flex;
  flex-direction: column;
  .yn-page-list {
    height: 0;
    flex: 1;
  }
  .yn-page-list-footer {
    flex-shrink: 0;
  }
}

/deep/ .operate-divider {
  margin: 0;
}
</style>
