<!-- eslint-disable vue/no-unused-vars -->
<template>
  <yn-page-list
    v-if="rateRead"
    ref="pageList"
    class="exchangeRate"
    :pageTitle="pageTitle"
    :pageHeader="pageHeader"
    :tableConfig="tableConfig"
    :toolsConfig="toolsConfig"
  >
    <template v-if="rateEdit && isDetail" v-slot:[`pageTitle.extraRender`]>
      <yn-button class="btn btn-edit" type="primary" @click="handleEdit">
        编辑
      </yn-button>
    </template>
    <template
      v-for="(item, index) in dimList"
      v-slot:[`page.filter.ynslot${index+1}`]
    >
      <AsynSelectDimMember
        v-if="isPovDataLoaded"
        :key="item.dimCode"
        :ref="item.dimCode"
        class="dim-select-item"
        :dimCode="item.dimCode"
        :nonleafselectable="false"
        :allowClear="false"
        :value="searchObj[toLowerFirWord(item.dimCode)]"
        :selctedFirstLeafNode="true"
        :isSetDefaultVal="item.dimCode === DIMCODE"
        :disabled="item.dimCode === DIMCODE && !isDetail"
        :dropdownVisibleChange="dropdownVisibleChange"
        @changeVal="handleChangeDim($event, item.dimCode)"
      />
    </template>
    <template
      v-if="isDetail && !!filterObj.searchList.length"
      v-slot:[`table.title`]
    >
      <template v-for="(item, itemIndex) in filterObj.searchList">
        <yn-tag :key="item" closable @close="clearSearchItem(itemIndex)">
          报告币种：{{ item }}
        </yn-tag>
      </template>
      <yn-button
        v-show="filterObj.searchList.length"
        class="condition-none"
        type="text"
        @click="handleClearAll"
      >
        全部清除
      </yn-button>
    </template>
    <template v-if="isDetail" v-slot:[`tools.btns`]>
      <span class="label-accuracy">小数位数：</span>
      <yn-input-number
        v-model="accuracyValue"
        addonBefore="小数位数"
        :min="0"
        :max="9"
        :step="1"
        :precision="0"
        class="set-accuracy"
        size="small"
        @blur="setPrecision"
      />
      <yn-divider class="oper-divider" type="vertical" />
      <svg-icon type="icon-shuaxin" title="刷新" @click="handleRefreshData" />
    </template>
    <template v-slot:[`table.currencyMemberNameCol`]="record">
      <div :class="['td-item', isDetail ? '' : 'noEdit']">
        <span class="td-text">
          {{ record.currencyMemberName }}
        </span>
      </div>
    </template>
    <template v-slot:[`table.averageRateCol`]="record">
      <div class="td-item valid-prop calign-right average-input">
        <span v-if="isDetail" class="td-text text-money">
          {{ record.averageRate | toFinance(accuracy) }}
        </span>
        <yn-input
          v-else
          v-model="record.averageRate"
          placeholder="请输入"
          :class="[isAverageRateInValid(record) ? 'valid-error-border' : '']"
          @change="handleChangeInput($event, record, 'averageRate')"
          @focus="handleChangeInput($event, record, 'averageRate')"
          @blur="handleBlurInput($event, record, 'averageRate')"
        />
        <span v-show="isAverageRateInValid(record)" class="invalid-tip-text">
          {{ $data.$cellErrorMessage }}
        </span>
      </div>
    </template>
    <template v-slot:[`table.finalRateCol`]="record">
      <div class="td-item valid-prop calign-right average-input">
        <span v-if="isDetail" class="td-text text-money">
          {{ record.finalRate | toFinance(accuracy) }}
        </span>
        <yn-input
          v-else
          v-model="record.finalRate"
          placeholder="请输入"
          :class="[isFinalRateInValid(record) ? 'valid-error-border' : '']"
          @change="handleChangeInput($event, record, 'finalRate')"
          @focus="handleChangeInput($event, record, 'finalRate')"
          @blur="handleBlurInput($event, record, 'finalRate')"
        />
        <span v-show="isFinalRateInValid(record)" class="invalid-tip-text">
          {{ $data.$cellErrorMessage }}
        </span>
      </div>
    </template>
    <template v-slot:[`table.operation`]="record">
      <a
        href="javascript:;"
        class="yn-a-link edit-btn"
        @click="editRuleSet(record.key, true)"
      >
        编辑
      </a>
      <yn-popconfirm
        title="你要删除当前规则吗？"
        placement="bottomRight"
        okText="删除"
        cancelText="取消"
        @confirm="deleteRuleSet(record.key)"
      >
        <a class="yn-a-link" href="javascript:;" @click.stop>删除</a>
      </yn-popconfirm>
      <svg-icon
        class="cell-operation-btns"
        type="icon-c1_cr_form_enter"
        @onClick="editRuleSet(record.key)"
      />
    </template>
    <template
      v-if="isDetail"
      v-slot:[`table.filterCurrencyMemberNameCol`]="{ confirm, clearFilters }"
    >
      <tableFilterSearch
        ref="reCurrencyMemberName"
        :dataSource="filterObj.dataSource"
        :clearFilters="clearFilters"
        @handSearch="chooseList => handFilterSearch(confirm, chooseList)"
      />
    </template>
    <template v-if="!isDetail" v-slot:[`page.footer`]>
      <div class="footer">
        <yn-button class="footer-btn" @click="handleCancel">
          取消
        </yn-button>
        <yn-button
          :loading="saveLoadding"
          class="footer-btn"
          type="primary"
          @click="handleSave"
        >
          保存
        </yn-button>
      </div>
    </template>
  </yn-page-list>
  <yn-empty
    v-else
    description="您没有汇率管理的查看权限，如需要开通请联系管理员"
    :image="require('@/image/noPrivilige.png')"
    class="no-permission"
  />
</template>

<script>
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-input-number/";
import "yn-p1/libs/components/yn-tag/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-page-list";

import { mapMutations, mapState } from "vuex";
import exchangerateService from "@/services/exchangerate";
import tableFilterSearch from "@/components/hoc/tableFilterSearch";
import AsynSelectDimMember from "../../components/hoc/asynSelectDimMember";
import cloneDeep from "lodash/cloneDeep";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import { dealNumberFocusChange, dealNumberBlurInput } from "@/utils/common";
import commonService from "@/services/common";

const warnTitle = "您要保存对“汇率管理”所做的更改吗";
const CELL_ERROR_MESSAGE = "数值范围大于0";
export default {
  components: { tableFilterSearch, AsynSelectDimMember },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      pageHeader: {
        filterKey: "",
        collapsed: false,
        hideOptions: ["collapse", "setting", "query", "reset"], // 折叠：collapse，设置：setting，分割线：divider， 查询：query，重置：reset
        formProrps: {
          layout: "horizontal"
        },
        formOptions: [
          {
            label: "版本",
            field: "version",
            slotName: "ynslot1"
          },
          {
            label: "年",
            field: "year",
            slotName: "ynslot2"
          },
          {
            label: "期间",
            field: "period",
            slotName: "ynslot3"
          },
          {
            label: "转换货币",
            field: "transCurrency",
            slotName: "ynslot4"
          }
        ]
      },
      tableConfig: {
        dataSource: [],
        columns: [
          {
            title: "报告币种",
            key: "currencyMemberName",
            filtered: false,
            width: 150,
            scopedSlots: {
              customRender: "currencyMemberNameCol",
              filterIcon: "filterIcon",
              filterDropdown: "filterCurrencyMemberNameCol"
            }
          },
          {
            title: "平均汇率",
            key: "averageRate",
            width: 250,
            scopedSlots: {
              customRender: "averageRateCol"
            },
            align: "right"
          },
          {
            title: "期末汇率",
            key: "finalRate",
            width: 250,
            scopedSlots: {
              customRender: "finalRateCol"
            },
            align: "right"
          }
        ],
        loading: false
      },
      DIMCODE: "TransCurrency",
      regRate: /^(\d+)(\.\d+)?$/, // 汇率的匹配规则
      isDetail: true, // true-查看 false-编辑
      accuracy: 2, // 数值精确位数 默认2
      accuracyValue: 2, //
      searchObj: {
        transCurrency: ""
      }, // 查询数据结构
      transObj: {}, // 转化货币对象，编辑时请求接口拿到数据
      isPovDataLoaded: false, // 数据是否加载完成

      cloneTableData: [],
      confirmObj: {
        toBeSavedObj: {} // 记录更新数据行
      },
      // 列表报告币种搜索对象
      filterObj: {
        isSearching: false, // 是否在搜索
        dataSource: [],
        searchList: [] // 过滤后的数据源
      },
      $cellErrorMessage: CELL_ERROR_MESSAGE,
      dimList: [
        {
          dimCode: "Version",
          dimName: "版本"
        },
        {
          dimCode: "Year",
          dimName: "年"
        },
        {
          dimCode: "Period",
          dimName: "期间"
        },
        {
          dimCode: "TransCurrency",
          dimName: "转换货币"
        }
      ],
      rateRead: false,
      rateEdit: false,
      saveLoadding: false
    };
  },
  computed: {
    ...mapState({
      activeKey: state => state.common.tabActiveId
    }),
    toolsConfig() {
      if (!this.isDetail) {
        return null;
      } else {
        return {
          options: [
            [
              {
                slotName: "btns"
              }
            ]
          ]
        };
      }
    },
    pageTitle() {
      if (!this.isDetail) {
        return {
          title: "汇率管理",
          size: ""
        };
      } else {
        return {
          title: "汇率管理",
          size: "large"
        };
      }
    }
  },
  watch: {
    activeKey: {
      handler(newKey) {
        if (newKey === this.params.id) {
          this.isDetail = true;
          this.getExchangeRateList();
        }
      }
    },
    searchObj: {
      handler(newObj) {
        this.getExchangeRateList();
      },
      immediate: true,
      deep: true
    },
    filterObj: {
      deep: true,
      immediate: true,
      handler(nv, ov) {
        if (this.filterObj.searchList.length === 0) {
          this.tableConfig.columns[0].filtered = false;
          this.tableConfig.dataSource = this.cloneTableData;
        } else {
          this.tableConfig.dataSource = this.cloneTableData.filter(item => {
            return this.filterObj.searchList.includes(item.currencyMemberName);
          });
        }
        this.$refs.pageList && this.$refs.pageList.resize();
      }
    }
  },
  created() {
    this.getExchangeRateAuth();
    this.getPrecision();
    this.selectUserPov(data => {
      const defaultParams = this.selfTab
        ? this.params.params
        : this.getTabParamsMixin() || {}; // 流程pov
      for (const key in data) {
        const v = data[key] ? defaultParams[key] || data[key].memberId : null;
        this.$set(this.searchObj, key, v);
      }
      this.isPovDataLoaded = true;
    });
  },
  methods: {
    ...mapMutations({
      updatePageStaus: "exchangerateset/updatePageStaus",
      addTab: "common/addTab"
    }),
    getPrecision() {
      commonService("selectUserScale", "rateManagement").then(res => {
        this._precision = res.data.data;
        this.handleSetAccuracy(this._precision.decimalPlaces);
      });
    },
    setPrecision() {
      this.accuracy = this.accuracyValue || 0;
      commonService("saveOrUpdateUserScale", {
        decimalPlaces: this.accuracyValue || 0,
        objectId: this._precision.objectId,
        pageName: "rateManagement"
      });
    },
    isAverageRateInValid(record) {
      const valid =
        (record.averageRate && !this.regRate.test(record.averageRate)) ||
        record.averageRate === "0" ||
        record.averageRate === 0;
      return valid;
    },
    isFinalRateInValid(record) {
      return (
        (record.finalRate && !this.regRate.test(record.finalRate)) ||
        record.finalRate === "0" ||
        record.finalRate === 0
      );
    },
    handleSetAccuracy(value) {
      if (value > 9) {
        this.accuracyValue = 9;
      } else if (value < 0 || !value) {
        this.accuracyValue = 0;
      } else {
        this.accuracyValue = value;
      }
      this.accuracy = this.accuracyValue;
    },
    async getExchangeRateAuth() {
      const {
        data: { items }
      } = await commonService("getMetadataRecords", "rateManagementAuth");
      const refId = items.length
        ? items[0].objectId
        : "11edd516a4b78285a99e4d4cf28e7f99";
      exchangerateService("getExchangerateAuth", refId)
        .then(res => {
          if (res.data.items) {
            const [read, edit] = res.data.items;
            this.rateRead = read.value === "TRUE" || edit.value === "TRUE";
            this.rateEdit = edit.value === "TRUE";
          }
        })
        .catch(() => {
          UiUtils.errorMessage("汇率管理权限接口报错");
        });
    },
    getExchangeRateList(callback) {
      if (
        Object.values(this.searchObj).every(id => !id) ||
        !this.searchObj.transCurrency
      ) {
        return;
      }
      this.tableConfig.loading = true;
      exchangerateService("getRateList", this.searchObj)
        .then(res => {
          const list = res.data;
          this.filterObj.dataSource = [];
          list.forEach(item => {
            item.key = item.currencyMemberId;
            this.filterObj.dataSource.push(item.currencyMemberName);
          });
          this.tableConfig.dataSource = list;
          this.cloneTableData = cloneDeep(this.tableConfig.dataSource);
          callback && callback();
        })
        .finally(() => {
          this.tableConfig.loading = false;
        });
    },

    clearSearchItem(itemIndex) {
      this.filterObj.searchList.splice(itemIndex, 1);
      this.$refs.reCurrencyMemberName &&
        this.$refs.reCurrencyMemberName.setSelectedItems(
          this.filterObj.searchList
        );
    },

    handleClearAll() {
      this.filterObj.searchList.splice(0);
      this.tableConfig.columns[0].filtered = false;
      this.$refs.reCurrencyMemberName &&
        this.$refs.reCurrencyMemberName.clearSelectedItems();
    },

    handFilterSearch(confirm, chooseList) {
      confirm({ closeDropdown: true });
      this.filterObj.searchList = [...chooseList];
      const showFilter = !!(
        this.filterObj.searchList && this.filterObj.searchList.length
      );
      this.tableConfig.columns[0].filtered = showFilter;
      this.$set(this.tableConfig, "columns", [...this.tableConfig.columns]);
    },

    // 处理数据变更 start

    addChangedData(record) {
      const { key } = record;
      this.confirmObj.toBeSavedObj[key] = record;
    },

    clearChangedData() {
      this.confirmObj.toBeSavedObj = {};
    },

    // 处理数据变更 end
    // 首字母小写
    toLowerFirWord(word) {
      return `${word[0].toLowerCase()}${word.slice(1)}`;
    },
    handleChangeDim(e, dimCode) {
      const key = this.toLowerFirWord(dimCode);
      this.savePromptMixin().then(() => {
        this.$set(this.searchObj, key, e);
      });
    },
    // dropdownVisibleChange 设置 pov
    dropdownVisibleChange(e) {
      const { dimCode } = e;
      if (dimCode === this.DIMCODE) return;
      // 由于切换下拉值的时候改值（searchObj）在 savePromptMixin
      // Promise 中导致下面的 searchObj 不是最新的值。
      setTimeout(() => {
        this.saveOrUpdateUserPov(this.searchObj);
      });
    },

    handleChangeInput(e, record, prop) {
      // const { value } = e.target;
      // 没理解 set 的用处。此处 set 应该是不需要的 （dom 上 v-mode）。
      // this.$set(record, prop, value.replace(/,/g, ""));
      dealNumberFocusChange(e, record, prop, this, "journal");
      this.addChangedData(record);
      this.addSaveEventCb();
    },

    handleBlurInput(e, record, prop) {
      dealNumberBlurInput(e, record, prop, this);
    },

    addSaveEventCb(notSaveCb) {
      this.addCallBackFnMixin(this.handleSave.bind(this), warnTitle, notSaveCb);
    },

    handleEdit() {
      this.tableConfig.loading = true;
      new Promise((resolve, reject) => {
        exchangerateService("getTransCurrency", this.searchObj)
          .then(res => {
            const data = res.data;
            this.transObj = {
              objectId: data.objectId,
              dimMemberName: data.dimMemberName
            };
            this.searchObj.transCurrency = data.objectId;
            resolve();
          })
          .catch(err => {
            this.tableConfig.loading = false;
            reject(err);
          });
      }).then(() => {
        this.tableConfig.loading = false;
        this.isDetail = false;
        this.$refs.pageList.resize();
      });
    },

    handleSave() {
      this.saveLoadding = true;
      // 校验输入汇率的合法性
      let existError = false;
      this.tableConfig.dataSource.forEach((record, index) => {
        if (
          (record.averageRate && !this.regRate.test(record.averageRate)) ||
          record.averageRate === "0" ||
          record.averageRate === 0 ||
          (record.finalRate && !this.regRate.test(record.finalRate)) ||
          record.finalRate === "0" ||
          record.finalRate === 0
        ) {
          existError = true;
        }
      });
      if (existError) return;
      const params = {
        ...this.searchObj,
        rateVoList: this.tableConfig.dataSource.map(v => {
          return {
            averageRate: v.averageRate,
            finalRate: v.finalRate,
            currencyMemberId: v.currencyMemberId,
            currencyMemberName: v.currencyMemberName
          };
        })
      };
      return exchangerateService("saveRate", params)
        .then(res => {
          this.isDetail = true;
          this.$refs.pageList.resize();
          this.clearCommonSaveEventsMixin();
          this.clearChangedData();
          UiUtils.successMessage("保存成功");
        })
        .finally(() => {
          this.tableConfig.loading = false;
          this.saveLoadding = false;
        });
    },

    handleCancel() {
      if (this.saveLoadding) return;
      this.savePromptMixin().then(() => {
        this.getExchangeRateList(() => {
          this.isDetail = true;
          this.$refs.pageList.resize();
        });
      });
    },
    handleRefreshData() {
      if (!this.isDetail) {
        this.savePromptMixin().then(() => {
          this.getExchangeRateList();
        });
      } else {
        this.getExchangeRateList();
      }
    },
    newTabMixin(tabInfo) {
      const { id, router, title, uri, routerName, params } = tabInfo;
      this.newtabMixin({ id, router, title, uri, routerName, params });
    }
  }
};
</script>
<style lang="less">
.pageDim {
  border-top-left-radius: @yn-console-content-radius !important;
  border-top-right-radius: @yn-console-content-radius !important;
  .yn-card-layout {
    overflow: hidden;
    border-bottom: 1px solid @yn-border-color-base;
  }
  .ant-row {
    margin-bottom: @yn-margin-xl;
  }
}
.exchangeRate {
  .option-col-end {
    display: none;
  }
  .yn-page-list-footer {
    position: relative;
    z-index: 1;
    border-top: 1px solid @yn-border-color-base;
  }
}
</style>
<style scoped lang="less">
@import "../../commonLess/common.less";
.no-permission {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.exchangeRate {
  /deep/.ant-table-tbody > tr > td {
    padding: 0;
    height: 2.1875rem;
  }
  /deep/.ant-tag {
    background-color: @yn-background-color-light;
    color: @yn-text-color-secondary;
  }
  .td-item {
    position: relative;
    width: 100%;
    overflow: visible;
    .td-text {
      display: inline-block;
      height: @rem16;
      width: 100%;
      padding-left: @yn-padding-xl;
      line-height: @rem16;
    }
    .td-text.text-money {
      padding-right: @rem10;
      text-align: right;
    }
    /deep/ input[type="text"] {
      line-height: @rem34;
      padding-left: @rem16;
      border: none;
      text-align: right;
    }
  }
  .average-input {
    width: calc(100% - 2px);
  }
  .noEdit {
    display: inline-block;
    width: 100%;
    height: 2.125rem;
    line-height: 2.125rem;
    position: relative;
    background: @yn-background-color !important;
  }
  .footer {
    display: flex;
    height: 100%;
    justify-content: flex-end;
    align-items: center;
    .footer-btn:first-child {
      margin-right: @yn-margin-s;
    }
  }
}
</style>
