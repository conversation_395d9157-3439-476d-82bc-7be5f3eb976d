// 表格列信息
export const TABLE_COLUMNS = [
  {
    title: "引用类型",
    dataIndex: "moduleType",
    width: 200,
    minWidth: 100,
    scopedSlots: {
      customRender: "moduleType"
    }
  },
  {
    title: "对象名称",
    dataIndex: "sourceObjectName",
    minWidth: 200,
    width: 300,
    scopedSlots: {
      customRender: "sourceObjectName"
    }
  },
  {
    title: "最近修改人",
    dataIndex: "updateBy",
    width: 200,
    minWidth: 200,
    scopedSlots: {
      customRender: "updateBy"
    }
  },
  {
    title: "最近修改时间",
    dataIndex: "updateDate",
    minWidth: 250,
    width: 250,
    scopedSlots: {
      customRender: "updateDate"
    }
  }
];
// 合并配置引用
export const CONSOLIDATION_RELATION = {
  title: "合并配置引用",
  key: "consolidationRelation"
};
// 分页 每页数据大小
export const PAGE_SIZE = 20;
// 下钻的方式
export const PAGE_DRILL_DOWN = {
  setPov: ["organization", "equity", "rate"], // 设置pov
  openSecondaryTab: ["currentOffset", "reclassification", "journal"], //  打开二级页签
  selectNode: [
    // 选中指定树节点
    "ruleSet",
    "taskFlowTemplate",
    "journalTemplate",
    "reconciliation"
  ]
};
// 引用类型对应的路由信息
export const MODULE_ROUTER_INFO = {
  taskFlowTemplate: {
    name: "processTemplate",
    uri: "/process/template"
  },
  organization: {
    name: "organization",
    uri: "/ownership/organization"
  },
  equity: {
    name: "equity",
    uri: "ownership/equity"
  },
  currentOffset: {
    // 往来抵销
    name: "currentoffsetRule",
    uri: "/ruleConfig/currentoffsetRule",
    pUri: "ruleConfig/currentoffset"
  },
  reclassification: {
    // 重分类
    name: "rearrangeRule",
    uri: "ruleConfig/rearrangeRule",
    pUri: "ruleConfig/rearrange"
  },
  ruleSet: {
    // 规则集
    name: "ruleSetMaintenance",
    uri: "/ruleConfig/ruleSetMaintenance"
  },
  rate: {
    name: "exchangerateSet",
    uri: "/exchangerateSet"
  },
  reconciliation: {
    name: "reconciliationStatement",
    uri: "/reconciliation/reportList"
  },
  journalTemplate: {
    name: "journalTemplate",
    uri: "/journal/template"
  },
  journal: {
    // 日记账
    name: "journalDetail",
    uri: "/journal/detail",
    pUri: "journal/list"
  }
};
export const NO_MODULE_PERMISSIONS = "没有模块权限";
