<template>
  <dimTab
    ref="dimensionmanage"
    class="dim-content"
    :isExternalReference="true"
    :quoteDetailConfig="quoteDetailConfig"
    @quote_MenuSelect="handleSelectQuoteMenu"
    @quote_Created="qutoInit"
  >
    <template #menu1Table>
      <member-reference :quoteMemberData="quoteMemberData" />
    </template>
  </dimTab>
</template>
<script>
import dimTab from "yn-mdd/libs/views/dimensionmanage/newDimTab";
import "yn-mdd/libs/assets/mdd-iconfont/iconfont_mdd.js";
import MemberReference from "./MemberReference.vue";
import { CONSOLIDATION_RELATION } from "./constant";
export default {
  name: "AA",
  components: {
    dimTab,
    MemberReference
  },
  data() {
    return {
      // 查看引用组件配置项
      quoteDetailConfig: {
        menuOption: [],
        explainInfo:
          "当前仅支持查看成员被“表单/报告、维度属性、维度关联、成员变量、合并配置”的引用情况，被“权限、规则”等引用情况暂不支持查看",
        tableOption: [
          // 自定义表格配置
          {
            menuKey: CONSOLIDATION_RELATION.key, // 菜单key
            slot: "menu1Table" // 对应右侧slot
          }
        ]
      }
    };
  },
  computed: {
    quoteMemberData() {
      // 进入引用查看：参数quoteMemberData是当前成员数据,默认null
      let res;
      try {
        res = this.$refs.dimensionmanage.quoteMemberData;
      } catch (e) {
        res = null;
      }
      return res;
      //   return this.$refs?.dimensionmanage?.quoteMemberData ?? null;
    }
  },
  methods: {
    // 引用查看菜单选择事件 quote_MenuSelect
    handleSelectQuoteMenu({ key }) {
      // if (key === CONSOLIDATION_RELATION.key) {
      //   this.requestData();
      // }
    },
    // 查看引用--create
    qutoInit(data) {
      this.quoteInfo = data;
      // 根据成员自定义菜单
      this.quoteDetailConfig.menuOption.push(CONSOLIDATION_RELATION);
    }
  }
};
</script>
<style lang="less">
.quote-page #centerDiv > div {
  width: 100%;
  height: 100%;
  background-color: @yn-background-color;
}
.quote-page .yn-page-title {
  border-bottom: 1px solid @yn-border-color-base !important;
}
</style>
<style lang="less" scoped></style>
