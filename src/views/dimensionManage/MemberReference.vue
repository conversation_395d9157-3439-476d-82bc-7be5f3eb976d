<template>
  <yn-skeleton :loading="loading">
    <yn-page-list
      ref="pageList"
      :pageHeader="pageHeader"
      :tableConfig="tableConfig"
      @pageHeader_search="onSearch"
      @table_change="onTableChange"
    >
      <template slot="table.moduleType" slot-scope="text">
        {{ mapModuleType[text].label }}
      </template>
      <template slot="table.sourceObjectName" slot-scope="text">
        <yn-tooltip :visibleOnOverflow="true" :title="text">
          <div class="cellContent">
            {{ text }}
          </div>
        </yn-tooltip>
      </template>
      <template slot="table.updateBy" slot-scope="text">
        <yn-tooltip :visibleOnOverflow="true" :title="text">
          <div class="cellContent">
            {{ text }}
          </div>
        </yn-tooltip>
      </template>
      <template slot="table.updateDate" slot-scope="text">
        <span>{{ text }}</span>
        <yn-button class="cell-operation-btns" type="text" size="small">
          <yn-icon-svg type="right" />
        </yn-button>
        <!-- <svg-icon class="cell-operation-btns" type="icon-c1_cr_form_enter" /> -->
      </template>
    </yn-page-list>
  </yn-skeleton>
</template>

<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-page-list/";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-skeleton/";
import dimensionManageService from "@/services/dimensionManage";
import newtab from "@/mixin/newtab.js";
import { mapState } from "vuex";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import {
  TABLE_COLUMNS,
  CONSOLIDATION_RELATION,
  PAGE_SIZE,
  PAGE_DRILL_DOWN,
  MODULE_ROUTER_INFO,
  NO_MODULE_PERMISSIONS
} from "./constant";
export default {
  name: "MemberReference",
  mixins: [newtab],
  props: {
    quoteMemberData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pageHeader: {
        filterKey: "",
        collapsed: true,
        title: CONSOLIDATION_RELATION.title,
        hideOptions: ["setting"], // 折叠：collapse，设置：setting，分割线：divider， 查询：query，重置：reset
        formProrps: {
          layout: "horizontal"
        },
        formOptions: [
          {
            label: "引用类型",
            field: "moduleType",
            element: "yn-select",
            options: []
          },
          {
            label: "对象名称",
            field: "sourceObjectName",
            element: "yn-input",
            props: {
              placeholder: "请输入"
            }
          },
          {
            label: "最近修改人",
            field: "updateBy",
            element: "yn-input",
            props: {
              placeholder: "请输入"
            }
          },
          {
            label: "最近修改时间",
            field: "date",
            element: "yn-range-picker", // TODO
            props: {
              placeholder: ["开始日期", "结束日期"]
            }
          }
        ]
      },
      tableConfig: {
        locale: {
          emptyText: "暂无引用数据"
        },
        tableDraggable: false,
        pagination: {
          showTotal: total => `共计${total}条`,
          current: 1,
          pageSize: PAGE_SIZE,
          total: 0,
          showQuickJumper: true,
          pageSizeOptions: ["10", "20", "50", "100", "200"],
          showSizeChanger: true
        },
        columns: TABLE_COLUMNS,
        dataSource: [],
        customRow: this.handlerCustomRow
      },
      loading: true
    };
  },
  computed: {
    ...mapState("common", {
      menuMapObj: state => state.menuMapObj
    })
  },
  async created() {
    await this.requestModuleTypeList();
    this.$set(this.pageHeader.formOptions[0], "options", this.moduleType);
  },
  async mounted() {
    const initRequestParams = { moduleType: "" };
    await this.requestTableData(1, initRequestParams);
    this.loading = false;
    this.$nextTick(() => {
      const form = this.$refs.pageList.filterForm;
      form.setFieldsValue(initRequestParams);
    });
  },
  methods: {
    async requestModuleTypeList() {
      const { data } = await dimensionManageService("moduleTypeList");
      const mapObj = {};
      const moduleType = data.map(item => {
        const { name, value } = item;
        const finallyItem = { label: name, value };
        let drillDownType = "";
        if (PAGE_DRILL_DOWN.setPov.includes(value)) {
          drillDownType = "setPov";
        } else if (PAGE_DRILL_DOWN.openSecondaryTab.includes(value)) {
          drillDownType = "openSecondaryTab";
        } else {
          drillDownType = "selectNode";
        }
        mapObj[value] = { ...finallyItem, drillDownType };
        return finallyItem;
      });

      this.moduleType = moduleType;
      this.mapModuleType = mapObj;
    },
    onTableChange({ pagingInfo }) {
      const { current, pageSize } = pagingInfo;
      this.tableConfig.pagination.pageSize = pageSize;
      this.requestTableData(current);
    },
    handlerCustomRow(record) {
      return {
        on: {
          click: e => {
            this.jumpPage(record);
            return false;
          }
        }
      };
    },
    jumpPage(rowData) {
      const { moduleType } = rowData;
      //  页面 调整
      const { name, uri } = MODULE_ROUTER_INFO[moduleType];
      const params = this.getDrillPageParams(rowData);
      if (params === NO_MODULE_PERMISSIONS) {
        UiUtils.warningMessage(
          `没有${this.mapModuleType[moduleType].label}模块权限,请联系管理员`
        );
        return;
      }
      const drillInfo = {
        uri,
        routerName: name,
        router: name,
        ...params
      };
      this.newtabMixin(drillInfo);
    },
    getDrillPageParams(rowData) {
      const { moduleType, sourceObjectParam } = rowData;
      const { drillDownType } = this.mapModuleType[moduleType];
      const tempObj = JSON.parse(sourceObjectParam);
      const routerInfo = this.getRouterInfoByModuleType(moduleType);
      const { title, menuId: id } = routerInfo;
      if (!title && !id) return NO_MODULE_PERMISSIONS;
      let params;
      if (drillDownType === "setPov") {
        params = { id, title, params: { ...tempObj, form: "memberReference" } };
      } else if (drillDownType === "selectNode") {
        params = {
          id,
          title,
          params: {
            MRJ: { id: tempObj.objectId, name: title },
            form: "memberReference"
          }
        };
      } else {
        const { objectId: id, name } = tempObj;
        params = {
          id,
          title: name,
          params: { id, name, status: "detail", form: "memberReference" }
        };
      }
      return params;
    },
    getRouterInfoByModuleType(moduleType) {
      const { uri, pUri } = MODULE_ROUTER_INFO[moduleType];
      const tempArr = Object.values(this.menuMapObj);
      let routerInfo;
      for (let i = 0, LEN = tempArr.length; i < LEN; i++) {
        if (tempArr[i].uri.indexOf(pUri || uri) !== -1) {
          routerInfo = tempArr[i];
          break;
        }
      }
      return routerInfo || {};
    },
    onSearch() {
      this.requestTableData(1);
    },
    async requestTableData(pageNum, requestParams) {
      const params = {};
      if (!requestParams) {
        const form = this.$refs.pageList.filterForm;
        const { date, ...others } = form.getFieldsValue();
        if (Array.isArray(date) && date.length) {
          const [startDate, endDate] = date;
          params.startUpdateDate = startDate._d
            .toLocaleDateString()
            .replace(/\//g, "-");
          params.endUpdateDate = endDate._d
            .toLocaleDateString()
            .replace(/\//g, "-");
        }
        Object.assign(params, others);
      } else {
        Object.assign(params, requestParams);
      }
      const {
        pagination: { current, total, pageSize }
      } = this.tableConfig;
      let finalPageNum = (typeof pageNum === "number" ? pageNum : current) - 1;
      const maxPageNum = total ? Math.ceil(total / pageSize) : 1;
      if (finalPageNum >= maxPageNum) {
        finalPageNum = maxPageNum - 1;
      } else if (finalPageNum < 0) {
        finalPageNum = 0;
      }
      const offset = finalPageNum * pageSize;
      await dimensionManageService("relationPage", {
        ...params,
        offset,
        dimMemberId: this.quoteMemberData.objectId,
        limit: pageSize
      }).then(res => {
        const { list = [], total } = res.data;
        const { pagination } = this.tableConfig;
        this.tableConfig.pagination = Object.assign(pagination, {
          current: finalPageNum + 1,
          total
        });
        this.$set(this.tableConfig, "dataSource", list);
      });
    }
  }
};
</script>
<style lang="less" scoped>
.cell-operation-btns {
  float: right;
  color: @yn-label-color;
}
.cellContent {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  vertical-align: middle;
}
</style>
