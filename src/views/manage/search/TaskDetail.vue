<template>
  <yn-drawer
    title="详情"
    :visible="visible"
    wrapClassName="detail-drawer"
    @close="onClose"
  >
    <div class="task-detail-top">
      <div class="top-title">
        <span class="top-title-id">
          任务ID-{{ subTaskBaseInfo.taskSortId }} {{ subTaskBaseInfo.taskType }}
        </span>
        <!-- <yn-tag :color="$data.$tagColor[subTaskBaseInfo.taskStatus]">
          {{ $data.$TASK_STATUS[subTaskBaseInfo.taskStatus] }}
        </yn-tag> -->
        <span
          :class="[
            'operatorState',
            ASYNCSTATUSCLASS[$data.$TASK_STATUS[subTaskBaseInfo.taskStatus]]
          ]"
        >
          {{ $data.$TASK_STATUS[subTaskBaseInfo.taskStatus] }}
        </span>
      </div>
      <div class="top-content">
        <span>总计 {{ subTaskBaseInfo.subTaskCnt }} 个</span>
        <yn-divider type="vertical" class="divider-line" />
        <span>{{ subTaskBaseInfo.taskParam }}</span>
      </div>
    </div>
    <div class="task-detial-content">
      <div
        v-for="(subTask, index) in subTaskList"
        :key="index"
        class="content-list"
      >
        <span
          v-show="!(subTask.key === 'NOT_STARTED' && subTaskList.length === 1)"
          class="list-title"
        >
          {{ subTask.status }}
          <span :class="`count-${subTask.key.toLowerCase()}`">
            {{ subTask.list.length }} </span>个，明细如下
        </span>
        <ul>
          <li v-for="(item, k) in subTask.list" :key="k" class="list-item">
            {{ item }}
          </li>
        </ul>
      </div>
    </div>
  </yn-drawer>
</template>

<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-tag/";
import "yn-p1/libs/components/yn-divider/";

import exchangerateService from "@/services/exchangerate";
const ASYNCSTATUSCLASS = Object.freeze({
  未开始: "async-unstart",
  进行中: "async-process",
  异常中止: "async-stop",
  已完成: "async-success",
  成功: "async-success",
  失败: "async-stop",
  部分失败: "async-part-error"
});
const TASK_STATUS = {
  0: "未开始",
  1: "进行中",
  3: "失败",
  4: "成功",
  5: "失败",
  6: "部分失败"
};

const tagColor = {
  0: "yn-tag-7",
  3: "yn-tag-10",
  4: "yn-tag-3",
  5: "yn-tag-10",
  6: "yn-tag-8"
};

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      ASYNCSTATUSCLASS,
      $TASK_STATUS: TASK_STATUS,
      $tagColor: tagColor,
      subTaskList: [],
      subTaskBaseInfo: {}
    };
  },
  computed: {},
  watch: {
    visible: {
      handler(v) {
        if (!v) return;
        const { taskId: id } = this.recordInfo;
        this.getSubTasksById(id);
      }
    }
  },
  methods: {
    onClose() {
      this.$emit("update:visible", false);
    },
    // 获取子任务
    getSubTasksById(id) {
      exchangerateService("getSubTasksById", id).then(res => {
        const {
          subTaskCnt,
          taskType,
          taskStatus,
          taskSortId,
          taskParam,
          subTaskMap
        } = res.data.data || {};
        this.$set(this, "subTaskBaseInfo", {
          taskSortId,
          taskType,
          taskStatus,
          subTaskCnt,
          taskParam
        });
        const m = {
          NOT_STARTED: "未开始",
          RUNNING: "进行中",
          SUCCESS: "成功",
          FAIL: "失败"
        };
        this.subTaskList = Object.keys(subTaskMap).map(key => ({
          key,
          status: m[key],
          list: subTaskMap[key]
        }));
      });
    }
  }
};
</script>

<style lang="less"></style>
<style lang="less" scoped>
.count-success {
  color: @yn-success-color;
}
.count-fail {
  color: @yn-error-color;
}
.task-detail-top {
  // height: 50px;
  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .top-title-id {
      display: inline-block;
      font-weight: 500;
      font-size: 0.875rem;
      color: @yn-text-color;
    }
  }
  .top-content {
    margin-top: 1.125rem;
    margin-bottom: 1.5rem;
    font-weight: 400;
    font-size: 0.875rem;
    color: @yn-label-color;
    /deep/ .ant-divider {
      background: @yn-label-color;
    }
  }
}
.task-detial-content {
  .content-list {
    border-top: 1px solid @yn-border-color-base;
    padding-top: 1.5rem;
    padding-bottom: 0.875rem;
    .list-title {
      display: inline-block;
      margin-bottom: 1rem;
      font-size: 0.875rem;
      color: @yn-label-color;
    }
    .list-item {
      margin-bottom: 1rem;
    }
  }
}
.operatorState {
  height: 1.625rem;
  padding: 0 0.5rem;
  line-height: 1.625rem;
  border-radius: 0.25rem;
  margin-left: auto;
}
.async-unstart {
  color: @yn-disabled-color;
  background: @yn-disabled-bg-color;
}
.async-process {
  color: @yn-primary-color;
  background: @yn-link-bg-color;
}
.async-stop {
  color: @yn-error-color;
  background: @yn-error-bg-color;
}
.async-success {
  color: @yn-success-color;
  background: @yn-success-bg-color;
}
.async-part-error {
  color: @yn-warning-color;
  background: @yn-warning-bg-color;
}
</style>
