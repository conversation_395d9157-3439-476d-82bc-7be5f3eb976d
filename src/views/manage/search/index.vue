<template>
  <div class="tree-list-demo">
    <div style="height: 100%">
      <yn-page-list
        ref="pageList"
        :pageTitle="pageTitle"
        :tableConfig="tableConfig"
        :pageHeader="pageHeader"
        :pageSkeleton="pageSkeleton"
        :dataPanelSkeleton="dataPanelSkeleton"
        @pageHeader_search="(val, bool) => onSearch(val, true)"
        @pageHeader_reset="onReset"
        @pageHeader_viewChange="onViewChangeAfter"
        @table_change="onTableChange"
      >
        <!-- 任务类型 下拉树 -->
        <template slot="page.filter.tasktypelist">
          <yn-select-tree
            v-decorator="[
              'taskTypeList',
              {
                initialValue: []
              }
            ]"
            searchMode="multiple"
            :datasource="$data.$TASK_TYPE_DATA"
            multiple
          />
        </template>
        <!-- 任务状态 下拉树 -->
        <template slot="page.filter.taskstatuslist">
          <yn-select-tree
            v-decorator="[
              'taskStatusList',
              {
                initialValue: []
              }
            ]"
            searchMode="multiple"
            :datasource="$data.$TASK_STATUS_DATA"
            multiple
          />
        </template>
        <template slot="table.status" slot-scope="text, record">
          <div class="task-status">
            <div :class="['task-circle', $data.$TASK_STATUS_COLOR[text]]"></div>
            <span v-if="text !== 3">{{ $data.$TASK_STATUS[text] }}</span>
            <yn-tooltip v-else>
              <template slot="title">
                {{ record.exceptionInfo }}
              </template>
              {{ $data.$TASK_STATUS[text] }}
            </yn-tooltip>
          </div>
        </template>
        <template slot="table.action" slot-scope="text, record">
          <a
            v-if="record.showOperateButton"
            class="yn-a-link"
            href="javascript:;"
            @click="handlerOperate(record)"
          >
            {{ showOperate(record) }}
          </a>
        </template>
      </yn-page-list>
    </div>

    <TaskDetail :visible.sync="detailVisible" :recordInfo="recordInfo" />
  </div>
</template>
<script>
import "yn-p1/libs/components/yn-page-list";
import "yn-p1/libs/components/yn-button";
import "yn-p1/libs/components/yn-switch";
import "yn-p1/libs/components/yn-date-picker";
import "yn-p1/libs/components/yn-icon/yn-icon-svg";
import "yn-p1/libs/components/yn-tooltip";
import "yn-p1/libs/components/yn-select-tree";

import TaskDetail from "./TaskDetail.vue";

import exchangerateService from "@/services/exchangerate";
import cloneDeep from "lodash/cloneDeep";

import moment from "moment";

const TASK_STATUS = {
  0: "未开始",
  1: "进行中",
  2: "已完成",
  3: "异常中止",
  4: "成功",
  5: "失败",
  6: "部分失败"
};

const TASK_STATUS_COLOR = {
  0: "task-unstart",
  1: "task-running",
  2: "task-success",
  3: "task-stop",
  4: "task-success",
  5: "task-stop",
  6: "task-part-fail"
};

const TASK_TYPE = {
  0: "合并",
  1: "对账",
  2: "折算",
  3: "计算",
  4: "提交",
  5: "批量折算",
  6: "批量计算",
  7: "批量提交",
  8: "批量审核",
  9: "批量驳回",
  10: "批量启动",
  11: "脚本"
};

const hasDetail = [
  "批量折算",
  "批量计算",
  "批量提交",
  "批量审核",
  "批量驳回",
  "批量启动"
];

const TASK_TYPE_DATA = Object.keys(TASK_TYPE).map(key => ({
  label: TASK_TYPE[key],
  key
}));

const TASK_STATUS_DATA = Object.keys(TASK_STATUS).map(key => ({
  label: TASK_STATUS[key],
  key
}));

const TAB_URI = "/reconciliation/report";

export default {
  components: { TaskDetail },
  data() {
    return {
      title: "合并任务查询",
      $TASK_STATUS: TASK_STATUS,
      $TASK_STATUS_COLOR: TASK_STATUS_COLOR,
      $TASK_TYPE_DATA: TASK_TYPE_DATA,
      $TASK_STATUS_DATA: TASK_STATUS_DATA,
      showView: false,
      layoutConfig: {
        iconPosition: "center",
        leftShow: true
      },
      pageSkeleton: {
        loading: false
      },
      dataPanelSkeleton: {
        loading: false
      },
      pageTitle: {
        title: "合并任务查询"
      },
      pageHeader: {
        filterKey: "",
        collapsed: true,
        title: "",
        hideOptions: ["setting"], // 折叠：collapse，设置：setting，分割线：divider， 查询：query，重置：reset
        formProrps: {
          layout: "horizontal"
        },
        formOptions: [
          {
            label: "任务类型",
            field: "taskTypeList",
            slotName: "tasktypelist"
          },
          {
            label: "任务状态",
            field: "taskStatusList",
            slotName: "taskstatuslist"
          },
          {
            label: "开始日期",
            field: "startTime",
            element: "yn-range-picker",
            props: {
              placeholder: ["开始日期", "结束日期"]
            }
          },
          {
            label: "结束日期",
            field: "endTime",
            element: "yn-range-picker",
            props: {
              placeholder: ["开始日期", "结束日期"]
            }
          },
          {
            label: "操作人",
            field: "taskOperator",
            element: "yn-input",
            props: {
              allowClear: true,
              placeholder: "请输入"
            }
          },
          {
            label: "任务ID",
            field: "taskSortId",
            element: "yn-input",
            props: {
              allowClear: true,
              placeholder: "请输入",
              autocomplete: "off"
            }
          },
          {
            label: "参数",
            field: "paramKeyWord",
            element: "yn-input",
            props: {
              autocomplete: "off",
              allowClear: true,
              placeholder: "请输入"
            }
          }
        ]
      },
      tableConfig: {
        tableDraggable: true,
        pagination: {
          current: 1,
          pageSize: 20,
          showTotal: total => "总计 " + total + " 条",
          showQuickJumper: true,
          total: 105,
          pageSizeOptions: ["10", "20", "50", "100", "200"],
          showSizeChanger: true
        },
        columns: [
          {
            title: "任务ID",
            dataIndex: "taskSortId",
            width: "5.3125rem"
          },
          {
            title: "任务类型",
            dataIndex: "taskType",
            width: "6.25rem"
          },
          {
            title: "参数",
            dataIndex: "taskParam",
            width: "18.75rem",
            maxWidth: "50rem"
          },
          {
            title: "状态",
            dataIndex: "taskStatus",
            width: "6.875rem",
            scopedSlots: {
              customRender: "status"
            }
          },
          {
            title: "开始时间",
            dataIndex: "startTime",
            minWidth: "18.75rem",
            customRender: text => text || "-"
          },
          {
            title: "结束时间",
            dataIndex: "endTime",
            minWidth: "18.75rem",
            customRender: text => text || "-"
          },
          {
            title: "操作人",
            dataIndex: "taskOperator"
          },
          {
            title: "操作",
            key: "operation",
            fixed: "right",
            width: "6.25rem",
            scopedSlots: {
              customRender: "action"
            }
          }
        ],
        dataSource: []
      },
      detailVisible: false,
      recordInfo: {}
    };
  },
  created() {
    this.onSearch();
  },
  methods: {
    onTableChange(pagination) {
      const { current, pageSize } = pagination.pagingInfo;
      this.tableConfig.pagination.current =
        pageSize === this.tableConfig.pagination.pageSize ? current : 1;
      this.tableConfig.pagination.pageSize = pageSize;
      this.onSearch(this._filter || {});
    },
    onDataInitChange(checked) {
      this.tableConfig.dataSource = checked || [];
    },
    onViewChangeAfter(e) {
      this.refreshDataPanel();
    },
    // 获取检索条件
    getFormData() {
      // const formData = this.$refs.pageList.getFormData();
    },
    refreshPage() {
      this.pageSkeleton.loading = true;
      setTimeout(() => {
        this.pageSkeleton.loading = false;
      }, 500);
    },
    refreshDataPanel() {
      this.dataPanelSkeleton.loading = true;
      setTimeout(() => {
        this.dataPanelSkeleton.loading = false;
      }, 500);
    },
    // 数据处理 bool 是否需要重置 pagination 的 current
    onSearch(val, bool) {
      if (bool) {
        this.tableConfig.pagination.current = 1;
      }
      this._filter = val;
      // events中事件触发
      const params = cloneDeep(val) || {};
      const fieldList = ["startTime", "endTime"];
      for (const key in params) {
        if (fieldList.includes(key)) {
          if (params[key] && params[key].length) {
            params[`${key}BeginDate`] = moment(params[key][0]).format(
              "YYYY-MM-DD 00:00:00"
            );
            params[`${key}EndDate`] = moment(params[key][1]).format(
              "YYYY-MM-DD 23:59:59"
            );
          }
          delete params[key];
        }
      }
      params.page = this.tableConfig.pagination.current;
      params.pageSize = this.tableConfig.pagination.pageSize;
      exchangerateService("queryMergeTask", params).then(res => {
        if (res.data && res.data.data) {
          res.data.data.data.forEach(item => {
            item.key = item.taskId;
          });
          this.tableConfig.pagination.total = res.data.data.total;
          this.onDataInitChange(res.data.data.data);
        }
      });
    },
    onReset() {
      this.onSearch();
    },
    showOperate(record) {
      if (
        // 已完成的对账
        record.taskStatus === 2 &&
        record.taskType === TASK_TYPE[1]
      ) {
        return "查看对账";
      }
      // 批量任务
      if (hasDetail.includes(record.taskType)) {
        return "查看详情";
      }
      return "";
    },
    handlerOperate(record) {
      const operate = this.showOperate(record);
      switch (operate) {
        case "查看对账":
          this.seeReconciliation(record);
          break;
        case "查看详情":
          this.seeDetail(record);
      }
    },
    stopOPerate(record) {},
    seeDetail(record) {
      this.recordInfo = {
        ...record
      };
      this.detailVisible = true;
    },
    seeReconciliation(record) {
      this.newtabMixin({
        id: record.taskId,
        title: record.reportTitle || "查看对账",
        router: "reconciliationReport",
        uri: TAB_URI,
        params: {
          ...record,
          fromPage: "merge_search"
        },
        newTabParams: {
          tabId: record.taskId,
          title: record.title || "查看对账",
          taskParam: record.taskParam
        }
      });
    }
  }
};
</script>
<style lang="less">
.tree-list-demo .tree-list-demo-action {
  padding: 0.5rem 0.5rem 0;
  border-bottom: 1px solid @yn-border-color-base;
}

.tree-list-demo .tree-list-demo-action > button {
  margin-bottom: 0.5rem !important;
}
</style>

<style scoped lang="less">
.tree-list-demo {
  height: calc(100% - 1px);
  .list-title {
    margin-bottom: 0.5rem;
    line-height: 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: @yn-text-color;
  }
  /deep/.ant-table-content {
    .task-status {
      height: @rem22;
      display: flex;
      align-items: center;
      user-select: none;
      .task-circle {
        display: inline-block;
        width: @rem8;
        height: @rem8;
        border-radius: 50%;
        margin-right: @rem8;
      }
      .task-unstart {
        background: @yn-label-color;
      }
      .task-running {
        background: @yn-chart-1;
      }
      .task-success {
        background: @yn-success-color;
      }
      .task-stop {
        background: @yn-error-color;
      }
      .task-part-fail {
        background: @yn-warning-color;
      }
    }
  }
}
</style>
