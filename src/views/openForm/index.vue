<template>
  <!-- 这是表单外挂的方式 -->
  <!-- 从菜单上 也就是说在 菜单上配置了表单（并不是从我的场景去打开表单）走 合并内部的路由 然后再在页面内 嵌套一层 iframe。 -->
  <!-- 原因：打开表单需要传入 pov。平台开页签的方式。目前不会给 url 传参。以后也不知道会不会支持。。。 -->
  <div class="open-form">
    <yn-spin v-if="loading" size="large" class="spin-cont" />
    <iframe
      frameborder="0"
      width="100%"
      height="100%"
      scroll="auto"
      :src="src"
    ></iframe>

    <ModalForm
      v-for="item in modalFormList"
      :key="item.key"
      :modalInfo="item"
      @handleCancel="handleCancel"
    />
  </div>
</template>

<script>
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { APPS } from "@/config/SETUP";
import { mapState } from "vuex";
import { queryString } from "../../utils/common";
import Logger from "yn-p1/libs/modules/log/logger";
import ModalForm from "../process/form/openFormbyModal.vue";
import { forwordMessage, setIframeP } from "../process/form/forwordMessage";
import { FRONTEND } from "@/config/SETUP";
export default {
  components: { ModalForm },
  props: {},
  data() {
    return {
      loading: true,
      src: "",
      modalFormList: []
    };
  },
  computed: {
    ...mapState("common", {
      menuMapObj: state => state.menuMapObj,
      tabActiveId: state => state.tabActiveId
    })
  },
  mounted() {
    this.generateSrc();
    Logger.info("post message to: ");
    forwordMessage(this.openRelate);
  },
  methods: {
    handleCancel() {
      this.$set(
        this.modalFormList[this.modalFormList.length - 1],
        "visible",
        false
      );
      this.modalFormList.pop();
    },
    openRelate(v) {
      if (!v || !v[0]) return;
      const type = v[0].openType;
      // 要么是 页签 要么是 弹窗 tab || modal
      const key = v[0].jumpParam.key;
      const title = v[0].jumpParam.title || v[0].jumpParam.name;
      if (type === "tab") {
        const lastP = this.getTabParamsMixin();
        let bool = false;
        if (
          lastP &&
          lastP.src &&
          lastP.treeKeyList &&
          lastP.treeKeyList.find(item => item === key)
        ) {
          bool = true;
        }
        if (bool) {
          // 存在左侧树上
          const { formIds, nodeId, customObj } = lastP;
          this.newtabMixin({
            id: key,
            title,
            uri: "/process/form",
            router: "processForm",
            params: {
              formIds,
              nodeId,
              customObj,
              from: "process",
              id: key,
              dataRef: {
                key,
                title,
                data: {
                  elementType: v[0].jumpParam.type
                }
              }
            }
          });
        } else {
          // 不在左侧树上
          const elementType = v[0].jumpParam.type;
          const dataRef = {
            key,
            title,
            data: {
              elementType
            }
          };
          const params = {
            ...this.getTabParamsMixin()
          };
          params.src = setIframeP(dataRef, {
            ...this.getTabParamsMixin(),
            id: key
          });
          params.dataRef = dataRef;
          this.newtabMixin({
            id: key,
            title,
            uri: elementType === "report" ? "/openReport" : "/openForm",
            router: elementType === "report" ? "openReport" : "openForm",
            params
          });
        }
      } else {
        // 弹窗方式打开
        const modalInfo = {
          visible: true,
          src: setIframeP(
            {
              key,
              title,
              data: {
                elementType: v[0].jumpParam.type
              }
            },
            {
              id: key,
              ...this.getTabParamsMixin()
            }
          ),
          title
        };
        this.modalFormList.push(modalInfo);
      }
    },
    generateParams() {
      const fieldsMap = {
        lang: "",
        TOKEN: "",
        appId: "",
        MenuId: "",
        securityFlag: "",
        ServiceName: ""
      };
      Object.keys(fieldsMap).forEach(key => {
        fieldsMap[key] = DsUtils.getSessionStorageItem(key, {
          storagePrefix: APPS.NAME,
          isJson: true
        });
      });
      const {
        lang,
        TOKEN,
        appId,
        MenuId: menuId,
        securityFlag = true,
        ServiceName = "consolidation"
      } = fieldsMap;
      return `&appId=${appId}&serviceName=${ServiceName}&menuId=${menuId}&lang=${lang}&TOKEN=${TOKEN}&securityFlag=${securityFlag}&timeDelta=-110`;
    },
    generateSrc() {
      // 线上的话 可以直接从 href 取
      if (!this.selfTab) {
        const params = this.getTabParamsMixin();
        this.selectUserPov(data => {
          const pageDimObj = {};
          Object.values(data)
            .filter(v => !!v)
            .forEach(item => {
              pageDimObj[item.dimId] = item.memberId;
            });
          let customObj = encodeURIComponent(JSON.stringify(pageDimObj));
          if (params && params.fromPage) {
            // 从 流程 配置的页面 打开表单（左侧菜单上的表单）则取流程中的 customObj（页面维）
            customObj = params.customObj;
          }
          const finallySrc = `${FRONTEND.MR_FRONT_VIEW_URL}/${location.hash}&customObj=${customObj}`;
          this.src = params && params.src ? params.src : finallySrc;
          this.loading = false;
        });
        return;
      }
      // 本地
      this.loading = true;
      const { href } = location;
      let key = this.tabActiveId;
      if (!this.selfTab) {
        key = queryString(href).inTab;
      }
      const { uri } = this.menuMapObj[key];
      const formName = encodeURIComponent(queryString(uri).formName);
      const formId = queryString(uri).formId;
      this.selectUserPov(data => {
        const pageDimObj = {};
        Object.values(data)
          .filter(v => !!v)
          .forEach(item => {
            pageDimObj[item.dimId] = item.memberId;
          });
        const params = this.generateParams();
        const customObj = encodeURIComponent(JSON.stringify(pageDimObj));
        const finallySrc = `${FRONTEND.MR_FRONT_VIEW_URL}/#/openForm?bubbleStep=true&customObj=${customObj}&menuHide=true&formId=${formId}&formName=${formName}&withAuthOpen=true&${params}&securityFlag=false&timeDelta=-110`;
        this.src = finallySrc;
        this.loading = false;
      });
    }
  }
};
</script>

<style lang="less" scoped>
.open-form {
  width: 100%;
  height: 100%;
  .spin-cont {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
