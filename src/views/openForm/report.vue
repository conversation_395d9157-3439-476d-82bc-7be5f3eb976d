<template>
  <div class="open-report">
    <yn-spin v-if="loading" size="large" class="spin-cont" />
    <iframe
      frameborder="0"
      width="100%"
      height="100%"
      scroll="auto"
      :src="src"
    ></iframe>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: true,
      src: ""
    };
  },
  mounted() {
    this.generateSrc();
  },
  methods: {
    generateSrc() {
      if (this.selfTab) return;
      const params = this.getTabParamsMixin();
      this.selectUserPov(data => {
        const pageDimObj = {};
        Object.values(data)
          .filter(v => !!v)
          .forEach(item => {
            pageDimObj[item.dimId] = item.memberId;
          });
        let customObj = encodeURIComponent(JSON.stringify(pageDimObj));
        if (params && params.fromPage) {
          // 从流程配置的页面打开报告
          customObj = params.customObj;
        }
        const finallySrc = `${location.href}&customObj=${customObj}`;
        this.src =
          params && params.src
            ? params.src
            : finallySrc.replace(
              "consolidation/#/openReport",
              "dashboard/#/dashboard_rt"
            );
        this.loading = false;
      });
    }
  }
};
</script>

<style lang="less" scoped>
.open-report {
  height: 100%;
}
</style>
