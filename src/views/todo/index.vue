<template>
  <div class="todo">
    <section class="overview">
      <Person
        class="person"
        @seeTodo="seeTodo"
        @seeCC="seeCC"
        @seeDone="seeDone"
      />
      <Todolist class="todolist" @seeTodo="seeTodo" />
    </section>
    <Info class="info" />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-tag/";
import "yn-p1/libs/components/yn-button/";
// import Recent from "./items/recent.vue";
import Info from "./items/info.vue";
import Person from "./items/person.vue";
import Todolist from "./items/todolist.vue";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { APPS } from "@/config/SETUP";

export default {
  components: { Info, Person, Todolist },
  data() {
    return {};
  },
  methods: {
    seeTodo() {
      window.parent.postMessage(
        JSON.stringify({
          source: "",
          actionType: "openMenu",
          messageType: "success", // success, error, warning 根据实际情况选择支持
          data: {
            appId: "af9c68b6b75011e8b3cf99828262d71a",
            menuId: "e323921b602b11e98df4d1739114363d"
          } // 额外返回参数，根据实际情况选择支持
        }),
        "*"
      );
      // const uri = this.generateURL("task", "e323921b602b11e98df4d1739114363d");
      // this.newtabMixin({
      //   id: "e323921b602b11e98df4d1739114363d",
      //   title: "我的待办",
      //   uri,
      //   router: "systemTab"
      // });
    },
    seeCC() {
      window.parent.postMessage(
        JSON.stringify({
          source: "",
          actionType: "openMenu",
          messageType: "success", // success, error, warning 根据实际情况选择支持
          data: {
            appId: "af9c68b6b75011e8b3cf99828262d71a",
            menuId: "e323b92d602b11e98df447ca7ead59b8"
          } // 额外返回参数，根据实际情况选择支持
        }),
        "*"
      );
      // const uri = this.generateURL(
      //   "copyFor",
      //   "e323b92d602b11e98df447ca7ead59b8"
      // );
      // this.newtabMixin({
      //   id: "e323b92d602b11e98df447ca7ead59b8",
      //   title: "抄送我的",
      //   uri,
      //   router: "systemTab"
      // });
    },
    seeDone() {
      window.parent.postMessage(
        JSON.stringify({
          source: "",
          actionType: "openMenu",
          messageType: "success", // success, error, warning 根据实际情况选择支持
          data: {
            appId: "af9c68b6b75011e8b3cf99828262d71a",
            menuId: "e323b92c602b11e98df43b3a1f62a3db"
          } // 额外返回参数，根据实际情况选择支持
        }),
        "*"
      );
      // const uri = this.generateURL(
      //   "hadDone",
      //   "e323b92c602b11e98df43b3a1f62a3db"
      // );
      // this.newtabMixin({
      //   id: "e323b92c602b11e98df43b3a1f62a3db",
      //   title: "我的已办",
      //   uri,
      //   router: "systemTab"
      // });
    },
    generateURL(router, id) {
      const { baseUrl, appId, TOKEN, lang, origin } = this.generateParams();
      return `${baseUrl}/ecs-console/index.html#/${router}?appId=${appId}&menuId=${id}&serviceName=console&TOKEN=${TOKEN}&lang=${lang}&origin=${origin}&inTab=${id}`;
    },
    generateParams() {
      const fieldsMap = {
        lang: "",
        TOKEN: "",
        appId: "",
        origin: "",
        MenuId: ""
      };
      Object.keys(fieldsMap).forEach(key => {
        fieldsMap[key] = DsUtils.getSessionStorageItem(key, {
          storagePrefix: APPS.NAME,
          isJson: true
        });
      });
      const baseUrl = location.origin;
      return {
        // baseUrl: "http://**************:91",
        ...fieldsMap,
        baseUrl
      };
    }
  }
};
</script>
<style scoped lang="less">
.todo {
  display: flex;
  padding: 1rem 2rem;
  height: 100%;
}
.overview {
  display: flex;
  flex-basis: 57rem;
  width: 57rem;
  margin-right: 1rem;
  flex-grow: 57;
  flex-shrink: 0;
  flex-direction: column;
}
.info {
  display: flex;
  flex-direction: column;
  width: 28rem;
  flex-grow: 28;
  flex-shrink: 0;
  flex-basis: 28rem;
}

.person,
.info,
.todolist {
  background: @yn-body-background;
  border: 1px solid rgba(225, 229, 235, 1);
  border-radius: 4px;
}
.person {
  display: flex;
  height: 16.875rem;
  flex-direction: column;
  flex-basis: 16.875rem;
  flex-grow: 270;
  flex-shrink: 0;
  margin-bottom: 1rem;
}

.todolist {
  height: 25.625rem;
  flex-basis: 25.625rem;
  flex-grow: 410;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}
</style>

<style>
/**解决平台样式问题**/
.platform-todo-formcontent__submitBtn--1zjvpf2p {
  display: none;
}
</style>
