<template>
  <section class="person">
    <div class="person-banner todobanner">
      <UserCard />
    </div>
    <div class="person-info todoinfo">
      <div class="info-item" @click="seeTodo">
        <div class="infoitem-icon">
          <svg
            width="4.125rem"
            height="4.125rem"
            viewBox="0 0 66 66"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
          >
            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <g
                transform="translate(-128.000000, -266.000000)"
                fill-rule="nonzero"
              >
                <g transform="translate(32.000000, 96.000000)">
                  <g transform="translate(24.000000, 156.000000)">
                    <g transform="translate(72.000000, 14.000000)">
                      <path
                        d="M32.5786925,0.00521827702 C28.3743675,0.109446431 24.4400442,1.77091453 21.4499089,4.6186805 L21.3037846,4.76113846 L21.5130603,4.75869805 C17.0982918,4.75869805 12.8501765,6.48098268 9.66609573,9.66506347 L9.37187871,9.96666095 C6.47269357,13.0132609 4.86544474,16.9704 4.76476811,21.0986591 L4.76215385,21.3027692 L4.90849296,21.1529724 C1.78631143,24.274058 0,28.4964016 0,33 L0.00521906772,33.4213404 C0.109463126,37.625982 1.77118974,41.56028 4.61969974,44.5501785 L4.76215385,44.6952 L4.7597303,44.4869397 C4.7597303,48.9017082 6.48201493,53.1498235 9.66609573,56.3339043 L9.96772777,56.6281571 C13.0146691,59.5277077 16.9720158,61.1355478 21.0997467,61.2362622 L21.3037846,61.2378462 L21.1540677,61.0926023 C24.2751707,64.2137054 28.4964614,65.9989677 33,65.9989677 L33.4213779,65.9937507 C37.6264098,65.8895467 41.5615319,64.2284623 44.5511767,61.3812623 L44.6962154,61.2378462 L44.4879719,61.241302 C48.9017267,61.241302 53.1492017,59.5186028 56.3348781,56.3339627 L56.6291004,56.0323599 C59.528337,52.9857086 61.1355875,49.0285678 61.2362641,44.9003086 L61.2378462,44.6952 L61.0925393,44.8459954 C64.2144709,41.7251595 66,37.5039009 66,33 L65.9947821,32.5785893 C65.8905623,28.3732406 64.2292195,24.4381443 61.3812753,21.4487359 L61.2378462,21.3027692 L61.241302,21.5120281 C61.241302,17.0972595 59.5190173,12.8491443 56.3349365,9.66506347 L56.0332252,9.37082494 C52.985549,6.47143852 49.0291628,4.86439813 44.9013023,4.76373516 L44.6962154,4.76113846 L44.8469646,4.90739768 C41.7256116,1.78604463 37.5032361,0 33,0 L32.5786925,0.00521827702 Z"
                      />
                      <path
                        d="M32.5786925,0.00521827702 C28.3743675,0.109446431 24.4400442,1.77091453 21.4499089,4.6186805 L21.3037846,4.76113846 L21.5130603,4.75869805 C17.0982918,4.75869805 12.8501765,6.48098268 9.66609573,9.66506347 L9.37187871,9.96666095 C6.47269357,13.0132609 4.86544474,16.9704 4.76476811,21.0986591 L4.76215385,21.3027692 L4.90849296,21.1529724 C1.78631143,24.274058 0,28.4964016 0,33 L0.00521906772,33.4213404 C0.109463126,37.625982 1.77118974,41.56028 4.61969974,44.5501785 L4.76215385,44.6952 L4.7597303,44.4869397 C4.7597303,48.9017082 6.48201493,53.1498235 9.66609573,56.3339043 L9.96772777,56.6281571 C13.0146691,59.5277077 16.9720158,61.1355478 21.0997467,61.2362622 L21.3037846,61.2378462 L21.1540677,61.0926023 C24.2751707,64.2137054 28.4964614,65.9989677 33,65.9989677 L33.4213779,65.9937507 C37.6264098,65.8895467 41.5615319,64.2284623 44.5511767,61.3812623 L44.6962154,61.2378462 L44.4879719,61.241302 C48.9017267,61.241302 53.1492017,59.5186028 56.3348781,56.3339627 L56.6291004,56.0323599 C59.528337,52.9857086 61.1355875,49.0285678 61.2362641,44.9003086 L61.2378462,44.6952 L61.0925393,44.8459954 C64.2144709,41.7251595 66,37.5039009 66,33 L65.9947821,32.5785893 C65.8905623,28.3732406 64.2292195,24.4381443 61.3812753,21.4487359 L61.2378462,21.3027692 L61.241302,21.5120281 C61.241302,17.0972595 59.5190173,12.8491443 56.3349365,9.66506347 L56.0332252,9.37082494 C52.985549,6.47143852 49.0291628,4.86439813 44.9013023,4.76373516 L44.6962154,4.76113846 L44.8469646,4.90739768 C41.7256116,1.78604463 37.5032361,7.10542736e-15 33,7.10542736e-15 L32.5786925,0.00521827702 Z M33,1.01538462 C37.2311614,1.01538462 41.1961535,2.69255732 44.1289793,5.62538303 C44.22419,5.72059381 44.3533236,5.77408266 44.4879719,5.77408266 C48.6357647,5.77408266 52.6240696,7.39114084 55.6170096,10.3831072 C58.6086317,13.3747293 60.2259173,17.3638585 60.2259173,21.5120281 C60.2259173,21.6467073 60.2794307,21.7758679 60.37468,21.8710838 C63.3077886,24.8031629 64.9846154,28.7679593 64.9846154,33 C64.9846154,37.2318009 63.3079836,41.19561 60.37468,44.127884 C60.2794307,44.2230999 60.2259173,44.3522604 60.2259173,44.4869397 C60.2259173,48.6351093 58.6086317,52.6242384 55.6169512,55.6159189 C52.623666,58.6082304 48.6351083,60.2259173 44.4879719,60.2259173 C44.3533236,60.2259173 44.22419,60.2794062 44.1289793,60.374617 C41.1966147,63.3069815 37.2317283,64.9835831 33,64.9835831 C28.7685115,64.9835831 24.8046126,63.3071765 21.872053,60.374617 C21.7768422,60.2794062 21.6477087,60.2259173 21.5130603,60.2259173 C17.3655471,60.2259173 13.3761648,58.6080027 10.3840811,55.6159189 C7.39240055,52.6242384 5.77511492,48.6351093 5.77511492,44.4869397 C5.77511492,44.3522604 5.72160159,44.2230999 5.62635227,44.127884 C2.69258747,41.1951489 1.01538462,37.2312341 1.01538462,33 C1.01538462,28.7685262 2.6927826,24.8036237 5.62635227,21.8710838 C5.72160159,21.7758679 5.77511492,21.6467073 5.77511492,21.5120281 C5.77511492,17.3638585 7.39240055,13.3747293 10.3840811,10.3830488 C13.3757616,7.3913683 17.3648907,5.77408266 21.5130603,5.77408266 C21.6477087,5.77408266 21.7768422,5.72059381 21.872053,5.62538303 C24.8050739,2.69236217 28.7690783,1.01538462 33,1.01538462 Z"
                        fill="#CCDCF5"
                      />
                      <path
                        d="M30.421875,23.2026008 C30.8373682,23.2026008 31.2122791,23.4519477 31.3729254,23.8351283 C31.9060583,25.1067802 32.5883361,25.6455289 33.515625,25.6455289 C34.4429139,25.6455289 35.1251917,25.1067802 35.6583246,23.8351283 C35.8189709,23.4519477 36.1938818,23.2026008 36.609375,23.2026008 L36.609375,23.2026008 L42.796875,23.2026008 C43.3664186,23.2026008 43.828125,23.6643072 43.828125,24.2338508 L43.828125,24.2338508 L43.828125,42.7963508 C43.828125,43.3658945 43.3664186,43.8276008 42.796875,43.8276008 L42.796875,43.8276008 L24.234375,43.8276008 C23.6648314,43.8276008 23.203125,43.3658945 23.203125,42.7963508 L23.203125,42.7963508 L23.203125,24.2338508 C23.203125,23.6643072 23.6648314,23.2026008 24.234375,23.2026008 L24.234375,23.2026008 Z M29.7721875,25.2651008 L25.265625,25.2651008 L25.265625,41.7651008 L41.765625,41.7651008 L41.765625,25.2651008 L37.2580313,25.2651008 L37.2227682,25.3348029 C36.4161912,26.8003235 35.2375371,27.6269623 33.742281,27.7023666 L33.742281,27.7023666 L33.515625,27.7080289 C31.9125086,27.7080289 30.6553876,26.8735995 29.8084818,25.3348029 L29.8084818,25.3348029 L29.7721875,25.2651008 Z M38.8854539,29.6921469 C39.257203,30.063896 39.285799,30.6488531 38.9712421,31.0534034 L38.8854539,31.1505547 L32.6979539,37.3380547 C32.3262048,37.7098038 31.7412478,37.7383998 31.3366974,37.4238429 L31.2395461,37.3380547 L28.1457961,34.2443047 C27.743068,33.8415765 27.743068,33.1886251 28.1457961,32.7858969 C28.5175452,32.4141479 29.1025022,32.3855518 29.5070526,32.7001087 L29.6042039,32.7858969 L31.96875,35.1496321 L37.4270461,29.6921469 C37.8297743,29.2894188 38.4827257,29.2894188 38.8854539,29.6921469 Z"
                        fill="#0052CC"
                      />
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </svg>
        </div>
        <div>
          <div class="info-title">待办任务</div>
          <div class="info-num">{{ todo }}</div>
        </div>
      </div>
      <div class="info-item" @click="seeDone">
        <div class="infoitem-icon">
          <svg
            width="4.125rem"
            height="4.125rem"
            viewBox="0 0 66 66"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
          >
            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <g
                transform="translate(-424.000000, -265.000000)"
                fill-rule="nonzero"
              >
                <g transform="translate(32.000000, 96.000000)">
                  <g transform="translate(320.000000, 156.000000)">
                    <g transform="translate(72.000000, 13.000000)">
                      <path
                        d="M32.5786925,0.00521827702 C28.3743675,0.109446431 24.4400442,1.77091453 21.4499089,4.6186805 L21.3037846,4.76113846 L21.5130603,4.75869805 C17.0982918,4.75869805 12.8501765,6.48098268 9.66609573,9.66506347 L9.37187871,9.96666095 C6.47269357,13.0132609 4.86544474,16.9704 4.76476811,21.0986591 L4.76215385,21.3027692 L4.90849296,21.1529724 C1.78631143,24.274058 0,28.4964016 0,33 L0.00521906772,33.4213404 C0.109463126,37.625982 1.77118974,41.56028 4.61969974,44.5501785 L4.76215385,44.6952 L4.7597303,44.4869397 C4.7597303,48.9017082 6.48201493,53.1498235 9.66609573,56.3339043 L9.96772777,56.6281571 C13.0146691,59.5277077 16.9720158,61.1355478 21.0997467,61.2362622 L21.3037846,61.2378462 L21.1540677,61.0926023 C24.2751707,64.2137054 28.4964614,65.9989677 33,65.9989677 L33.4213779,65.9937507 C37.6264098,65.8895467 41.5615319,64.2284623 44.5511767,61.3812623 L44.6962154,61.2378462 L44.4879719,61.241302 C48.9017267,61.241302 53.1492017,59.5186028 56.3348781,56.3339627 L56.6291004,56.0323599 C59.528337,52.9857086 61.1355875,49.0285678 61.2362641,44.9003086 L61.2378462,44.6952 L61.0925393,44.8459954 C64.2144709,41.7251595 66,37.5039009 66,33 L65.9947821,32.5785893 C65.8905623,28.3732406 64.2292195,24.4381443 61.3812753,21.4487359 L61.2378462,21.3027692 L61.241302,21.5120281 C61.241302,17.0972595 59.5190173,12.8491443 56.3349365,9.66506347 L56.0332252,9.37082494 C52.985549,6.47143852 49.0291628,4.86439813 44.9013023,4.76373516 L44.6962154,4.76113846 L44.8469646,4.90739768 C41.7256116,1.78604463 37.5032361,0 33,0 L32.5786925,0.00521827702 Z M33,1.01538462 C37.2311614,1.01538462 41.1961535,2.69255732 44.1289793,5.62538303 C44.22419,5.72059381 44.3533236,5.77408266 44.4879719,5.77408266 C48.6357647,5.77408266 52.6240696,7.39114084 55.6170096,10.3831072 C58.6086317,13.3747293 60.2259173,17.3638585 60.2259173,21.5120281 C60.2259173,21.6467073 60.2794307,21.7758679 60.37468,21.8710838 C63.3077886,24.8031629 64.9846154,28.7679593 64.9846154,33 C64.9846154,37.2318009 63.3079836,41.19561 60.37468,44.127884 C60.2794307,44.2230999 60.2259173,44.3522604 60.2259173,44.4869397 C60.2259173,48.6351093 58.6086317,52.6242384 55.6169512,55.6159189 C52.623666,58.6082304 48.6351083,60.2259173 44.4879719,60.2259173 C44.3533236,60.2259173 44.22419,60.2794062 44.1289793,60.374617 C41.1966147,63.3069815 37.2317283,64.9835831 33,64.9835831 C28.7685115,64.9835831 24.8046126,63.3071765 21.872053,60.374617 C21.7768422,60.2794062 21.6477087,60.2259173 21.5130603,60.2259173 C17.3655471,60.2259173 13.3761648,58.6080027 10.3840811,55.6159189 C7.39240055,52.6242384 5.77511492,48.6351093 5.77511492,44.4869397 C5.77511492,44.3522604 5.72160159,44.2230999 5.62635227,44.127884 C2.69258747,41.1951489 1.01538462,37.2312341 1.01538462,33 C1.01538462,28.7685262 2.6927826,24.8036237 5.62635227,21.8710838 C5.72160159,21.7758679 5.77511492,21.6467073 5.77511492,21.5120281 C5.77511492,17.3638585 7.39240055,13.3747293 10.3840811,10.3830488 C13.3757616,7.3913683 17.3648907,5.77408266 21.5130603,5.77408266 C21.6477087,5.77408266 21.7768422,5.72059381 21.872053,5.62538303 C24.8050739,2.69236217 28.7690783,1.01538462 33,1.01538462 Z"
                        fill="#D5F0E9"
                      />
                      <g
                        transform="translate(24.000000, 23.000000)"
                        stroke="#2BB291"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                      >
                        <path
                          d="M3.47826087,1.73913043 C1.55727044,1.73913043 -6.79342688e-16,3.29640087 0,5.2173913 L0,16 C-1.59869171e-15,18.209139 1.790861,20 4,20 L14.2608696,20 C16.4700086,20 18.2608696,18.209139 18.2608696,16 L18.2608696,5.2173913 C18.2608696,3.29640087 16.7035991,1.73913043 14.7826087,1.73913043 L14.7826087,1.73913043 L14.7826087,1.73913043"
                        />
                        <polyline
                          points="13.6216667 7.65946861 7.74623187 13.5346377 4.7339855 10.5223913"
                        />
                        <path
                          d="M6.95652174,1.73913043 L11.3043478,1.73913043 L6.95652174,1.73913043 Z"
                        />
                        <path
                          d="M2.17391304,1.73913043 L5.65217391,1.73913043 L2.17391304,1.73913043 Z"
                          transform="translate(3.913043, 1.739130) rotate(-90.000000) translate(-3.913043, -1.739130) "
                        />
                        <path
                          d="M12.6086957,1.73913043 L16.0869565,1.73913043 L12.6086957,1.73913043 Z"
                          transform="translate(14.347826, 1.739130) rotate(-90.000000) translate(-14.347826, -1.739130) "
                        />
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </svg>
        </div>
        <div>
          <div class="info-title">已办任务</div>
          <div class="info-num">{{ hasDone }}</div>
        </div>
      </div>
      <div class="info-item" @click="seeCC">
        <div class="infoitem-icon">
          <svg
            width="4.125rem"
            height="4.125rem"
            viewBox="0 0 66 66"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
          >
            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <g transform="translate(-720.000000, -265.000000)">
                <g transform="translate(32.000000, 96.000000)">
                  <g transform="translate(616.000000, 156.000000)">
                    <g transform="translate(72.000000, 13.000000)">
                      <path
                        d="M32.5786925,0.00521835991 C28.3743675,0.10944817 24.4400442,1.77094266 21.4499089,4.61875387 L21.3037846,4.76121409 L21.5130603,4.75877364 C17.0982918,4.75877364 12.8501765,6.48108563 9.66609573,9.665217 L9.37187871,9.96681927 C6.47269357,13.0134676 4.86544474,16.9706696 4.76476811,21.0989943 L4.76215385,21.3031076 L4.90849296,21.1533084 C1.78631143,24.2744436 0,28.4968542 0,33.0005242 L0.00521906772,33.4218712 C0.109463126,37.6265797 1.77118974,41.5609402 4.61969974,44.5508861 L4.76215385,44.69591 L4.7597303,44.4876463 C4.7597303,48.902485 6.48201493,53.1506678 9.66609573,56.3347991 L9.96772777,56.6290566 C13.0146691,59.5286533 16.9720158,61.136519 21.0997467,61.237235 L21.3037846,61.2388189 L21.1540677,61.0935728 C24.2751707,64.2147254 28.4964614,66.0000161 33,66.0000161 L33.4213779,65.994799 C37.6264098,65.8905933 41.5615319,64.2294826 44.5511767,61.3822373 L44.6962154,61.2388189 L44.4879719,61.2422748 C48.9017267,61.2422748 53.1492017,59.5195482 56.3348781,56.3348575 L56.6291004,56.03325 C59.528337,52.9865502 61.1355875,49.0293466 61.2362641,44.9010218 L61.2378462,44.69591 L61.0925393,44.8467077 C64.2144709,41.7258223 66,37.5044966 66,33.0005242 L65.9947821,32.5791068 C65.8905623,28.3736913 64.2292195,24.4385325 61.3812753,21.4490766 L61.2378462,21.3031076 L61.241302,21.5123698 C61.241302,17.0975311 59.5190173,12.8493484 56.3349365,9.665217 L56.0332252,9.37097379 C52.985549,6.47154132 49.0291628,4.8644754 44.9013023,4.76381083 L44.6962154,4.76121409 L44.8469646,4.90747564 C41.7256116,1.78607301 37.5032361,0 33,0 L32.5786925,0.00521835991 Z M33,1.01540074 C37.2311614,1.01540074 41.1961535,2.6926001 44.1289793,5.62547239 C44.22419,5.72068468 44.3533236,5.77417438 44.4879719,5.77417438 C48.6357647,5.77417438 52.6240696,7.39125824 55.6170096,10.3832722 C58.6086317,13.3749418 60.2259173,17.3641343 60.2259173,21.5123698 C60.2259173,21.6470512 60.2794307,21.7762138 60.37468,21.8714312 C63.3077886,24.8035569 64.9846154,28.7684163 64.9846154,33.0005242 C64.9846154,37.2323923 63.3079836,41.1962643 60.37468,44.128585 C60.2794307,44.2238024 60.2259173,44.3529649 60.2259173,44.4876463 C60.2259173,48.6358819 58.6086317,52.6250743 55.6169512,55.6168024 C52.623666,58.6091613 48.6351083,60.226874 44.4879719,60.226874 C44.3533236,60.226874 44.22419,60.2803637 44.1289793,60.375576 C41.1966147,63.3079871 37.2317283,64.9846154 33,64.9846154 C28.7685115,64.9846154 24.8046126,63.3081822 21.872053,60.375576 C21.7768422,60.2803637 21.6477087,60.226874 21.5130603,60.226874 C17.3655471,60.226874 13.3761648,58.6089337 10.3840811,55.6168024 C7.39240055,52.6250743 5.77511492,48.6358819 5.77511492,44.4876463 C5.77511492,44.3529649 5.72160159,44.2238024 5.62635227,44.128585 C2.69258747,41.1958033 1.01538462,37.2318255 1.01538462,33.0005242 C1.01538462,28.7689832 2.6927826,24.8040177 5.62635227,21.8714312 C5.72160159,21.7762138 5.77511492,21.6470512 5.77511492,21.5123698 C5.77511492,17.3641343 7.39240055,13.3749418 10.3840811,10.3832138 C13.3757616,7.39148571 17.3648907,5.77417438 21.5130603,5.77417438 C21.6477087,5.77417438 21.7768422,5.72068468 21.872053,5.62547239 C24.8050739,2.69240494 28.7690783,1.01540074 33,1.01540074 Z"
                        fill="#E3E5FE"
                        fill-rule="nonzero"
                      />
                      <g
                        transform="translate(22.000000, 24.000000)"
                        stroke="#747CFB"
                        stroke-linejoin="round"
                        stroke-width="2"
                      >
                        <polygon
                          points="20 0 0 7.44121053 10.5263158 9.47805263 13.1606316 20"
                        />
                        <line
                          x1="10.5306842"
                          y1="9.47810526"
                          x2="13.5079474"
                          y2="6.50084211"
                          stroke-linecap="round"
                        />
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </svg>
        </div>
        <div>
          <div class="info-title">抄送我的</div>
          <div class="info-num">{{ tome }}</div>
        </div>
      </div>
    </div>
  </section>
</template>
<script>
import UserCard from "./usercard.vue";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
import todoService from "../../../services/todo";
export default {
  components: { UserCard },
  data() {
    return {
      tome: 0,
      todo: 0,
      total: 0
    };
  },
  computed: {
    hasDone() {
      const num = this.total - this.todo;
      return num > 0 ? num : 0;
    }
  },
  created() {
    this.getTaskList();
    this.getTotalForMenu();
  },
  methods: {
    async getTotalForMenu() {
      const {
        data: {
          data: { TODO, CC }
        }
      } = await todoService("getTotalForMenu", {});
      this.tome = CC || 0;
      this.todo = TODO || 0;
    },
    async getTaskList() {
      const {
        data: {
          data: { total }
        }
      } = await todoService("getTaskList", {
        dateBegin: null,
        dateEnd: null,
        searchKey: null,
        pageNum: 1,
        taskType: ["TODO", "DONE"],
        pageSize: 5,
        taskStatus: null,
        requestUser: null,
        appId: UrlUtils.getQuery("appId")
      });
      this.total = total;
    },
    seeCC() {
      this.$emit("seeCC");
    },
    seeTodo() {
      this.$emit("seeTodo");
    },
    seeDone() {
      this.$emit("seeDone");
    }
  }
};
</script>

<style lang="less">
.person {
  .person-info {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .todotitle {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem 0.75rem;
    height: 3.125rem;
    font-size: 1rem;
    color: @yn-text-color;
    letter-spacing: 0;
    font-weight: 600;
    flex-shrink: 0;
    .todomore {
      margin-left: auto;
      font-size: 0.75rem;
      color: @yn-label-color;
      letter-spacing: 0;
      font-weight: 400;
      cursor: pointer;
      &:hover {
        color: @yn-link-color;
      }
    }
  }
  .todobanner {
    height: 8.25rem;
    flex-basis: 8.25rem;
    flex-grow: 33;
  }
  .todoinfo {
    height: 8.75rem;
    flex-basis: 8.75rem;
    flex-grow: 35;
    flex-shrink: 0;
  }
  .info-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 17rem;
    height: 5.75rem;
    text-align: left;
    cursor: pointer;
    &:hover {
      background: @yn-hover-bg-color;
      border-radius: 4px;
    }
  }
  .infoitem-icon {
    width: 4.125rem;
    height: 4.125rem;
    margin-right: 0.75rem;
  }
  .info-title {
    font-size: 0.875rem;
    color: @yn-text-color-secondary;
    letter-spacing: 0;
    font-weight: 400;
    margin-bottom: 8px;
  }
  .info-num {
    font-size: 1.5rem;
    color: @yn-text-color;
    letter-spacing: 0;
    line-height: 2rem;
    font-weight: 600;
  }
}
</style>
