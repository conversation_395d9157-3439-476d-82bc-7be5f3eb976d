<template>
  <section class="recent">
    <header class="todotitle">
      最近浏览
    </header>
    <article class="recent-info">
      <div v-for="item in recent" :key="item.id" class="recent-item">
        <div class="item-self">
          <img src="item.src" alt="" class="recent-icon" />
          <div class="recent-name">{{ item.name }}</div>
        </div>
      </div>
      <em v-for="item in recent" :key="item.id" class="iblank"></em>
    </article>
  </section>
</template>

<script>
// import todoService from "../../../services/todo";
export default {
  data() {
    return {
      recentView: JSON.parse(sessionStorage.getItem("histroy")) || [],
      recent: [
        {
          id: 1,
          src: "https://mt=a40935",
          name: "日记账"
        },
        {
          id: 2,
          src: "https://mt=a40935",
          name: "日记账"
        },
        {
          id: 3,
          src: "https://mt=a40935",
          name: "日记账"
        },
        {
          id: 4,
          src: "https://mt=a40935",
          name: "日记账"
        },
        {
          id: 5,
          src: "https://mt=a40935",
          name: "日记账"
        },
        {
          id: 6,
          src: "https://mt=a40935",
          name: "日记账"
        },
        {
          id: 7,
          src: "https://mt=a40935",
          name: "日记账"
        },
        {
          id: 8,
          src: "https://mt=a40935",
          name: "日记账"
        },
        {
          id: 9,
          src: "https://mt=a40935",
          name: "日记账"
        },
        {
          id: 10,
          src: "https://mt=a40935",
          name: "日记账"
        },
        {
          id: 11,
          src: "https://mt=a40935",
          name: "日记账"
        },
        {
          id: 12,
          src: "https://mt=a40935",
          name: "日记账"
        }
      ]
    };
  },
  created() {
    this.getTotalForMenu();
  },
  methods: {
    async getTotalForMenu() {
      // const { data } = await todoService("getSceneViewData");
    }
  }
};
</script>

<style lang="less" scoped>
.recent {
  .todotitle {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem 0.75rem;
    height: 3.125rem;
    font-size: 1rem;
    color: @yn-text-color;
    letter-spacing: 0;
    font-weight: 600;
    flex-shrink: 0;
    .todomore {
      margin-left: auto;
      font-size: 0.75rem;
      color: @yn-label-color;
      letter-spacing: 0;
      font-weight: 400;
      cursor: pointer;
      &:hover {
        color: @yn-link-color;
      }
    }
  }
  .todoinfo {
    height: 8.75rem;
    flex-basis: 8.75rem;
    flex-grow: 35;
    flex-shrink: 0;
  }
  .recent-info {
    display: flex;
    flex-flow: wrap;
    flex: 1;
    justify-content: space-between;
    height: 0;
    padding: 0 1.25rem;
    overflow: auto;
  }
  .recent-item {
    width: 33.3%;
    min-width: 5.5rem;
    flex-grow: 1;
    display: flex;
    padding: 0 1.5rem;
    justify-content: center;
    align-items: center;
  }
  .item-self {
    width: 5.5rem;
    height: 6.25rem;
    flex-shrink: 0;
    text-align: center;
    cursor: pointer;
    &:hover {
      background: @yn-hover-bg-color;
      border-radius: 4px;
    }
  }
  .iblank {
    width: 33.3%;
    height: 0;
    padding: 0 1.5rem;
    min-width: 5.5rem;
    flex-shrink: 0;
  }
  .recent-icon {
    margin: 1rem 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
  }
  .recent-name {
    font-size: 0.875rem;
    color: @yn-text-color-secondary;
    letter-spacing: 0;
    text-align: center;
    line-height: 1.25rem;
  }
}
</style>
