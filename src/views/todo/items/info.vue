<template>
  <section class="info">
    <div class="info-banner">
      <yn-carousel :autoplay="autoplay">
        <template v-if="carousel.length">
          <!-- <div>
            <img src="../../../image/noticeImg.png" />
          </div> -->
          <div v-for="(item, index) in carousel" :key="index">
            <img :src="item" />
          </div>
        </template>
        <template v-else>
          <div>
            <img src="../../../image/noticeImg.png" />
          </div>
        </template>
      </yn-carousel>
    </div>
    <div class="info-info">
      <header class="todotitle">
        系统公告
        <yn-button
          v-if="!selfTab"
          class="todomore"
          type="text"
          @click="seeMore"
        >
          更多
          <svg-icon :isIconBtn="false" type="icon-c1_cr_form_enter" />
        </yn-button>
      </header>
      <article class="msglist-info">
        <ul v-if="message.length" class="msglist">
          <li
            v-for="msg in message"
            :key="msg.noticeId"
            class="msg-item"
            @click="handlerItem(msg)"
          >
            <span class="msg-content">
              <yn-tooltip>
                <template slot="title">
                  {{ msg.title }}
                </template>
                <span class="msg-title">{{ msg.title }}</span>
              </yn-tooltip>
            </span>
            <span class="msg-date">{{ msg.createTime.split(" ")[0] }}</span>
          </li>
        </ul>
        <yn-empty v-else class="list-empty" />
      </article>
    </div>
    <InfoModal v-if="visible" :data="news" :visible.sync="visible" />
  </section>
</template>

<script>
import todoService from "../../../services/todo";
import todoApi from "../../../services/todo/api";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
import "yn-p1/libs/components/yn-empty/";
import InfoModal from "./infomodal.vue";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-carousel/";
export default {
  components: { InfoModal },
  data() {
    return {
      selfTab: UrlUtils.getQuery("selfTab"),
      news: {},
      visible: false,
      autoplay: true,
      message: [],
      carousel: []
    };
  },
  created() {
    this.getNoticeList();
  },
  methods: {
    async getNoticeList() {
      const {
        data: { data }
      } = await todoService("getNoticeList", { userPowerType: "ordinary" });
      this.getCarousel(data.list);
      this.message = data.list;
    },
    handlerItem(item) {
      this.visible = true;
      this.news = item;
    },
    getCarousel(data) {
      const baseUrl = `${
        todoApi.downloadAttachment
      }?LoginToken=${DsUtils.getSessionStorageItem("TOKEN", {
        storagePrefix: "consolidation",
        isJson: true
      })}&fileId=`;
      const imgMap = new Map([
        ["default_sky_home.png", icon => require(`../../../image/${icon}`)],
        ["default_team_home.png", icon => require(`../../../image/${icon}`)],
        ["default_tech_home.png", icon => require(`../../../image/${icon}`)],
        ["", icon => require(`../../../image/default_team_home.png`)]
      ]);
      this.carousel = data.map(v => {
        return imgMap.has(v.iconPath)
          ? imgMap.get(v.iconPath)(v.iconPath)
          : baseUrl + v.iconPath;
      });
    },
    seeMore() {
      const messageData = {
        source: "",
        actionType: "noticeList",
        messageType: "success", // success, error, warning 根据实际情况选择支持
        data: null // 额外返回参数，根据实际情况选择支持
      };
      window.parent.postMessage(JSON.stringify(messageData), "*");
    }
  }
};
</script>

<style lang="less" scoped>
.info {
  .info-banner {
    height: 8.25rem;
    flex-basis: 8.25rem;
    flex-grow: 132;
    flex-shrink: 0;
    /deep/ .ant-carousel {
      width: 100%;
    }
    /deep/ div {
      height: 100%;
    }
    /deep/ .ant-carousel .slick-slide {
      overflow: hidden;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
  }
  .info-info {
    display: flex;
    flex-direction: column;
    height: 35.375rem;
    flex-basis: 35.375rem;
    flex-grow: 566;
    flex-shrink: 0;
  }
  .msglist-info {
    height: 0;
    flex: 1;
  }
  .msglist {
    height: 100%;
    overflow: auto;
    padding: 0 0.75rem;
  }
  .msg-item {
    display: flex;
    position: relative;
    margin: 0.375rem 0;
    padding: 0 0.5rem;
    cursor: pointer;
    &:hover {
      background: @yn-hover-bg-color;
      border-radius: 4px;
    }
    &:first-child {
      margin-top: 0;
    }
  }
  .msg-content {
    width: 0;
    flex: 1;
    font-size: 0.875rem;
    color: @yn-text-color;
    line-height: 1.375rem;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 20px;
    white-space: nowrap;
  }
  .msg-title {
  }
  .msg-date {
    flex-shrink: 0;
    font-size: 0.75rem;
    color: @yn-label-color;
    text-align: right;
    line-height: 1.375rem;
    font-weight: 400;
  }
  .todotitle {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem 0.75rem;
    height: 3.125rem;
    font-size: 1rem;
    color: @yn-text-color;
    letter-spacing: 0;
    font-weight: 600;
    flex-shrink: 0;
    .todomore {
      margin-right: -0.75rem;
      margin-left: auto;
      font-size: 0.75rem;
      color: @yn-label-color;
      letter-spacing: 0;
      font-weight: 400;
      cursor: pointer;
      &:hover {
        color: @yn-link-color;
      }
    }
  }
  .list-empty {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .content-des {
    height: @rem22;
    color: @yn-label-color;
    text-align: center;
    line-height: @rem22;
    font-weight: 400;
  }
}
</style>
