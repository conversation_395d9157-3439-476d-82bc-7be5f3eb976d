<!-- eslint-disable vue/no-v-html -->
<template>
  <yn-modal
    width="45rem"
    :title="'查看公告'"
    :visible="visible"
    :footer="null"
    @ok="onOk"
    @cancel="onCancel"
  >
    <section class="textcontent">
      <h1 class="title">{{ data.title }}</h1>
      <span class="otherinfo">
        <span>{{ data.createTime }}</span>
        &nbsp;&nbsp;&nbsp;&nbsp;
        <span>{{ data.authorName }}</span>
      </span>
      <p class="text" v-html="data.text"></p>
    </section>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-avatar/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-card/";
import "yn-p1/libs/components/yn-card-meta/";
export default {
  props: {
    visible: {
      type: <PERSON><PERSON>an,
      default: false
    },
    data: {
      type: Object,
      default: () => ({ infos: {} })
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    onOk() {
      this.$emit("update:visible", false);
    },
    onCancel() {
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style scoped lang="less">
/deep/ .ant-modal-body {
  height: 37.5rem;
  overflow: auto;
}
.textcontent {
  .title {
    font-size: 1.5rem;
    line-height: 1.875rem;
    font-weight: 500;
    color: @yn-text-color;
    margin: 0 0 0.5rem 0;
  }
  .otherinfo {
    display: inline-block;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5rem;
    color: @yn-label-color;
    margin-bottom: 0;
  }
  .text {
    padding: 1.5rem 0 2.5rem;
  }
}
.notice-infos {
  .avater {
    background-color: @yn-primary-color;
  }
  .tit {
    white-space: normal;
  }
  .description {
    font-size: 0.75rem;
    span {
      margin-right: 1.25rem;
    }
  }
}
</style>
