<template>
  <section class="todolist">
    <header class="todotitle">
      待办任务
      <yn-button class="todomore" type="text" @click="seeTodo">
        更多
        <svg-icon :isIconBtn="false" type="icon-c1_cr_form_enter" />
      </yn-button>
    </header>
    <article class="todolist-info">
      <ul v-if="data.length" class="list-group">
        <li
          v-for="(item, index) in data"
          :key="item.taskIdent"
          class="list-item"
          @click="handlerTask(item)"
        >
          <div v-if="icon[index]" class="user-info">
            <div class="circle1">
              <img
                v-if="icon[index].iconBase64"
                class="usericon"
                :src="icon[index].iconBase64"
                alt=""
              />
              <img v-else src="../../../image/userIcon.png" class="usericon" />
            </div>
            <yn-tooltip>
              <template slot="title">
                {{ item.requestUserName }}
              </template>
              <span class="uername">{{ item.requestUserName }}</span>
            </yn-tooltip>
          </div>
          <div class="other-info">
            <div class="other-item">
              <span class="other-name">公司：</span>
              <span class="other-value">
                <yn-tooltip>
                  <template slot="title">
                    {{ (item.remark && item.remark.memberName) || "" }}
                  </template>
                  <span>{{
                    (item.remark && item.remark.memberName) || ""
                  }}</span>
                </yn-tooltip>
              </span>
            </div>
            <div class="other-item">
              <span class="other-name">期间：</span>
              <span class="other-value">
                <yn-tooltip>
                  <template slot="title">
                    {{ (item.remark && item.remark.periodKeyName) || "" }}
                  </template>
                  <span>{{
                    (item.remark && item.remark.periodKeyName) || ""
                  }}</span>
                </yn-tooltip>
              </span>
            </div>
          </div>
          <div class="state-info">
            <yn-tag :class="['statetag', CLASSTAG[item.taskStatus]]">
              {{ item.nodeName }}
            </yn-tag>
            <div class="statedate">{{ item.dateShow }}</div>
          </div>
        </li>
      </ul>
      <yn-empty v-else class="list-empty" />
    </article>
  </section>
</template>

<script>
import todoService from "../../../services/todo";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
import "yn-p1/libs/components/yn-empty/";
// PRE_SUBMIT("exception.ecs.console.task.console_task_status_pre_submit", "待提交"),
// PRE_PLAN("exception.ecs.console.task.console_task_status_pre_plan", "待编制"),
// PRE_APPROVAL("exception.ecs.console.task.console_task_status_pre_approval", "待审批"),
// NO_REACH("exception.ecs.console.task.console_task_status_no_reach", "未到达"),
// SUBMIT("exception.ecs.console.task.console_task_status_submit", "已提交"),
// PLAN("exception.ecs.console.task.console_task_status_plan", "已编制"),
// APPROVAL("exception.ecs.console.task.console_task_status_approval", "已审批"),
// HANDOVER("exception.ecs.console.task.console_task_status_handover", "已转审"),
// RETURN_DONE("exception.ecs.console.task.console_task_status_return_done", "已退回"),
// RETURN_TODO("exception.ecs.console.task.console_task_status_return_todo", "被退回"),
// CC("exception.ecs.console.task.console_task_status_cc", "抄送"),
// APPROVALING("exception.ecs.console.task.console_task_status_approvaling", "审批中"),
// CANCEL("exception.ecs.console.task.console_task_status_cancel", "已撤回"),
// TERMINATION("exception.ecs.console.task.console_task_status_termination", "已终止"),
// FINISH("exception.ecs.console.task.console_task_status_finish", "已完成");
const CLASSTAG = Object.freeze({
  // PRE_SUBMIT: "unstart",
  PRE_PLAN: "process",
  PRE_APPROVAL: "wait",
  FINISH: "submit"
});
export default {
  data() {
    return {
      CLASSTAG: CLASSTAG,
      icon: [],
      data: []
    };
  },
  created() {
    this.getTaskList();
  },
  methods: {
    async getTaskList() {
      const {
        data: {
          data: { list }
        }
      } = await todoService("getTaskList", {
        dateBegin: null,
        dateEnd: null,
        searchKey: null,
        pageNum: 1,
        taskType: ["TODO"],
        pageSize: 5,
        taskStatus: null,
        requestUser: null,
        appId: UrlUtils.getQuery("appId")
      });
      Promise.all(
        list.map(item => {
          return todoService("getIconByLoginName", {
            loginName: item.requestUser
          });
        })
      ).then(res => {
        this.icon = res.map(item => item.data.data);
      });
      this.data = list.map(item => {
        // 临时方案
        const arr = item.remark.replace(/【.*?】/g, ",").split(",");
        item.remark = {
          memberName: arr[1] || "",
          periodKeyName: arr[2] || ""
        };
        return item;
      });
    },
    seeTodo() {
      this.$emit("seeTodo");
    },
    getParams(url) {
      const params = {};
      const index = url.lastIndexOf("?");
      url
        .slice(index + 1)
        .split("&")
        .forEach(item => {
          const tmp = item.split("=");
          params[tmp[0]] = tmp[1];
        });
      return params;
    },
    handlerTask(item) {
      const params = this.getParams(item.openUrl);
      window.parent.postMessage(
        JSON.stringify({
          source: "",
          actionType: "openMenu",
          messageType: "success", // success, error, warning 根据实际情况选择支持
          data: {
            appId: "3b81d5d055ef11e9b8da4572f5e1f83b",
            menuId: "11ecd0171f617b32b2fa950cbe8c9532",
            menuQuery: `&year=${params.year}&version=${params.version}&period=${params.period}&yearlabel=${params.yearlabel}&versionlabel=${params.versionlabel}&periodlabel=${params.periodlabel}`
          } // 额外返回参数，根据实际情况选择支持
        }),
        "*"
      );
      // this.newtabMixin({
      //   id: "11ecd0171f617b32b2fa950cbe8c9532",
      //   title: "流程控制",
      //   uri: "/process/control",
      //   params: {
      //     todo: item
      //   },
      //   router: "processControl"
      // });
    }
  }
};
</script>
<style scoped lang="less">
.todolist {
  .todotitle {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem 0.75rem;
    height: 3.125rem;
    font-size: 1rem;
    color: @yn-text-color;
    letter-spacing: 0;
    font-weight: 600;
    flex-shrink: 0;
    .todomore {
      margin-right: -0.75rem;
      margin-left: auto;
      font-size: 0.75rem;
      color: @yn-label-color;
      letter-spacing: 0;
      font-weight: 400;
      cursor: pointer;
      &:hover {
        color: @yn-link-color;
      }
    }
  }
  .todoinfo {
    height: 8.75rem;
    flex-basis: 8.75rem;
    flex-grow: 35;
    flex-shrink: 0;
  }
  .todolist-info {
    height: 0;
    padding: 0 1.25rem;
    flex: 1;
  }
  .list-group {
    height: 100%;
    overflow: auto;
    .list-item {
      display: flex;
      height: 4rem;
      padding: 0.625rem 0;
      box-shadow: inset 0px -1px 0px 0px rgba(225, 229, 235, 1);
      cursor: pointer;
      &:hover {
        background: @yn-hover-bg-color;
        border-radius: 4px;
      }
    }
    .user-info {
      width: 18.0625rem;
      flex-shrink: 0;
      display: flex;
      align-items: center;
    }
    .uername {
      padding-right: 1.25rem;
      font-size: 0.875rem;
      color: @yn-text-color;
      letter-spacing: 0;
      font-weight: 600;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .circle1 {
      display: inline-block;
      position: relative;
      width: 2.25rem;
      height: 2.25rem;
      border-radius: 50%;
      margin-right: 0.75rem;
      flex-shrink: 0;
      background: rgba(141, 166, 252, 0.2);
      .usericon {
        width: 2rem;
        height: 2rem;
        position: absolute;
        top: 0.125rem;
        left: 0.125rem;
        border-radius: 50%;
      }
    }

    .other-info {
      width: 0;
      flex: 1;
    }
    .other-item {
      margin-bottom: 0.25rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .other-name {
      font-size: 0.75rem;
      color: @yn-text-color-secondary;
      letter-spacing: 0;
      line-height: 1.25rem;
      font-weight: 600;
    }
    .other-value {
      font-size: 0.75rem;
      color: @yn-text-color-secondary;
      letter-spacing: 0;
      font-weight: 400;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .state-info {
      width: 10.2rem;
      flex-shrink: 0;
      text-align: right;
    }
    .statetag {
      font-size: 0.75rem;
      border: none;
      height: 1.25rem;
      line-height: 1.25rem;
      margin-right: 0;
      margin-bottom: 0.25rem;
      &.unstart {
        color: @yn-disabled-color;
        background: @yn-disabled-bg-color;
      }
      &.process {
        color: @yn-link-color;
        background: @yn-link-bg-color;
      }
      &.wait {
        color: @yn-warning-color;
        background: @yn-warning-bg-color;
      }
      &.submit {
        color: @yn-success-color;
        background: @yn-success-bg-color;
      }
    }
    .statedate {
      font-size: 0.75rem;
      color: @yn-text-color-secondary;
      letter-spacing: 0;
      text-align: right;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  .list-item {
    display: flex;
    align-items: center;
  }
  .list-empty {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .content-des {
    height: @rem22;
    color: @yn-label-color;
    text-align: center;
    line-height: @rem22;
    font-weight: 400;
  }
}
</style>
