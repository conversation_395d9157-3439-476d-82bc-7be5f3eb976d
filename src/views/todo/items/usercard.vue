<template>
  <div class="mr-user-info">
    <div class="circle1">
      <img v-if="userIcon" :src="userIcon" width="100%" height="100%" />
      <img
        v-else
        src="../../../image/userIcon.png"
        width="100%"
        height="100%"
      />
    </div>
    <div class="nameBox">
      <p>{{ setTime() }}，@{{ sysInfo.userName }}</p>
      <h1>欢迎您回来</h1>
    </div>
  </div>
</template>

<script>
import DsUtils from "yn-p1/libs/utils/DsUtils";
export default {
  name: "UserInfo",
  data() {
    return {
      sysInfo: {}
    };
  },
  computed: {
    userIcon() {
      return this.sysInfo.userIcon;
    }
  },
  mounted() {
    this.sysInfo = DsUtils.getSessionStorageItem("currentUserInfo", {
      storagePrefix: "consolidation",
      isJson: true
    });
  },
  methods: {
    setTime() {
      const now = new Date();
      const hour = now.getHours();
      let str = "早上好";
      if (hour < 12) {
        str = "早上好";
      } else if (hour < 18) {
        str = "下午好";
      } else if (hour <= 24) {
        str = "晚上好";
      }
      return str;
    }
  }
};
</script>

<style lang="less" scoped>
.mr-user-info {
  padding-left: 3.125rem;
  height: 100%;
  width: 100%;
  background-image: url("../../../image/userInfoBg.png"),
    linear-gradient(270deg, rgba(93, 126, 255, 0.58) 0%, #5d7eff 82%);
  background-size: cover;
  background-repeat: no-repeat;
  opacity: 0.87;
  border-radius: 4px 4px 0px 0px;
  display: flex;
  align-items: center;
  .nameBox {
    p {
      line-height: 1.25rem;
      height: 1.25rem;
      font-size: 0.875rem;
      color: @yn-component-background;
      margin-bottom: 0.25rem;
    }
    h1 {
      height: 1.75rem;
      line-height: 1.75rem;
      font-size: 1.25rem;
      color: @yn-component-background;
      margin: 0;
    }
  }
}
.circle1 {
  position: relative;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  margin-right: 1.4375rem;
  background: rgba(255, 255, 255, 0.2);
  img {
    width: 3rem;
    height: 3rem;
    position: absolute;
    top: 0.25rem;
    left: 0.25rem;
    border-radius: 50%;
  }
}
</style>
