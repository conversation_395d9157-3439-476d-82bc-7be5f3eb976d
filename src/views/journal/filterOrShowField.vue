<template>
  <yn-modal
    wrapClassName="modal-field"
    :title="
      useType === 'searchFilter' || useType === 'listFilter'
        ? '过滤字段'
        : '显示字段'
    "
    :visible="fieldVisible"
    :bodyStyle="bodyStyle"
    @cancel="onCancel"
    @ok="onOk"
  >
    <div class="field-content">
      <div class="content-left">
        <div>
          <yn-checkbox
            :indeterminate="indeterminate"
            :checked="checkAll"
            @change="onCheckAllChange"
          >
            全选
          </yn-checkbox>
        </div>
        <br />
        <yn-checkbox-group
          v-model="checkedList"
          :options="plainOptions"
          @change="onChange"
        />
      </div>
      <div class="content-right">
        <div class="right-head">
          <span class="head-l">已选：{{ rightList.length }}个字段</span>
          <span class="yn-a-link" @click="onReset">恢复默认</span>
        </div>
        <div class="checked-list">
          <transition-group name="flip-list" tag="ul">
            <li
              v-for="(item, index) in rightList"
              :key="`${item}key`"
              draggable
              class="list-item"
              @dragstart="dragstart(index)"
              @dragenter="dragenter($event, index)"
              @dragover="dragover($event)"
            >
              {{ item }}
            </li>
          </transition-group>
        </div>
      </div>
    </div>
    <template slot="footer">
      <yn-button key="back" @click="onCancel">取消</yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="confirmLoading"
        @click="onOk"
      >
        保存
      </yn-button>
    </template>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-checkbox/";
import "yn-p1/libs/components/yn-checkbox-group/";

import journalService from "@/services/journal";
import { USER_TYPE_MAP } from "./constant.js";
import UiUtils from "yn-p1/libs/utils/UiUtils";

export default {
  props: {
    fieldVisible: {
      type: Boolean,
      default: false
    },
    useType: {
      type: String,
      default: ""
    },
    plainOptions: {
      type: Array,
      default: () => []
    },
    lastCheckedList: {
      type: Array,
      default: () => []
    },
    defaultDisabled: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      confirmLoading: false,
      bodyStyle: {
        width: 764
      },
      indeterminate: true, // 实现全选效果属性
      checkAll: false, // 是否全选
      checkedList: [],
      rightList: [],
      dragStartIndex: "" // 拖拽初始索引
    };
  },
  watch: {
    fieldVisible: {
      handler(nv) {
        if (nv) {
          // 如果是显示字段 则传过来的 是 table 的 cloumns 所以需要过滤掉最后一项操作列
          const len = this.useType.includes("Show")
            ? this.lastCheckedList.length - 1
            : this.lastCheckedList.length;
          this.checkedList = this.lastCheckedList
            .slice(0, len)
            .map(item => item.title || item.label);
          this.setDisabledToItem(this.plainOptions, this.defaultDisabled);
          if (this.lastCheckedList.length === this.plainOptions.length) {
            this.checkAll = true;
            this.indeterminate = false;
          }
        }
      }
    },
    checkedList(nv, ov) {
      if (nv.length === ov.length) return;
      if (nv.length > ov.length) {
        // 新增选中
        this.rightList.push(...nv.filter(item => !ov.includes(item)));
      } else {
        // 取消已选中
        const cancleItemList = ov.filter(item => !nv.includes(item));
        const copyRightList = [...this.rightList];
        const indexList = cancleItemList.map(item =>
          copyRightList.indexOf(item)
        );
        this.rightList = copyRightList.filter(
          (item, index) => !indexList.includes(index)
        );
      }
    }
  },
  methods: {
    setDisabledToItem(allList, checkedList) {
      allList.forEach(item => {
        if (checkedList.includes(item.value)) {
          item["disabled"] = true;
        }
      });
    },
    dragstart(index) {
      this.dragStartIndex = index;
    },
    dragenter(e, index) {
      e.preventDefault();
      if (this.dragStartIndex !== index) {
        const moveItem = this.rightList[this.dragStartIndex];
        this.rightList.splice(this.dragStartIndex, 1);
        this.rightList.splice(index, 0, moveItem);
        this.dragStartIndex = index;
      }
    },
    dragover(e) {
      e.preventDefault();
    },
    onCheckAllChange(checked, e) {
      Object.assign(this, {
        checkedList: e.target.checked
          ? this.plainOptions.map(item => item.value)
          : [...this.defaultDisabled],
        indeterminate: !e.target.checked,
        checkAll: e.target.checked
      });
    },
    onChange(checkedList) {
      this.indeterminate =
        !!checkedList.length && checkedList.length < this.plainOptions.length;
      this.checkAll = checkedList.length === this.plainOptions.length;
    },
    onReset() {
      const len = this.checkedList.length;
      this.checkedList.splice(0, len, ...this.defaultDisabled);
      this.rightList.splice(0, len, ...this.defaultDisabled);
    },
    onCancel() {
      this.$emit("closeFieldModal", false);
    },
    onOk() {
      const chooseList = [];
      this.rightList.forEach(checked => {
        const currentItem = this.plainOptions.filter(
          item => item.value === checked
        );
        chooseList.push(...currentItem);
      });
      const params = {
        pageName: USER_TYPE_MAP[this.useType],
        headerNames: [...chooseList]
      };
      this.confirmLoading = true;
      journalService("saveOrUpdateUserColumn", params)
        .then(res => {
          this.$emit("closeFieldModal", false, chooseList);
          UiUtils.successMessage("保存成功");
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    }
  }
};
</script>

<style lang="less" scoped>
.flip-list-move {
  transition: transform 1s;
}
.field-content {
  display: flex;
  width: 100%;
  border: 1px solid @yn-border-color-base;
  box-sizing: border-box;
  min-height: 25rem;
  flex-wrap: wrap;
  .content-left {
    flex: 1;
    padding: @yn-padding-xl;
    border-right: 1px solid @yn-border-color-base;
    /deep/ .ant-checkbox-wrapper {
      min-width: 30%;
      margin: 0;
      margin-bottom: @rem10;
    }
  }
  .content-right {
    width: 15rem;
    .right-head {
      display: flex;
      height: @rem32;
      justify-content: space-between;
      align-items: center;
      padding: 0 @yn-padding-xl;
      border-bottom: 1px solid @yn-border-color-base;
    }
    .checked-list {
      max-height: 22.5rem;
      width: 100%;
      overflow-y: auto;
      overflow-x: hidden;
      .list-item {
        height: @rem30;
        line-height: @rem30;

        padding-left: @yn-padding-xl;
        cursor: pointer;
        .icon {
          margin-right: @rem5;
        }
      }
      .list-item:hover {
        position: relative;
        background: @yn-primary-1;
        &::before {
          margin: 0 @yn-margin-xs;
          display: block;
          position: absolute;
          content: url("../../image/drag-and-drop.svg");
          width: 100%;
          height: @rem24;
          top: 0.125rem;
          left: 0px;
        }
      }
    }
  }
}
/deep/.ant-modal {
  width: 43.75rem !important;
}
</style>
