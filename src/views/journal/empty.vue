<template>
  <div class="right-empty">
    <div class="empty-content">
      <yn-empty>
        <img slot="image" src="../../image/reportEmpty.png" alt="" style="" />
        <span slot="description" class="content-des">
          当前日记账{{ fileName }}为空，马上开始新增!
        </span>
      </yn-empty>
      <br />
      <yn-button type="primary" @click="btnClick">{{ buttonName }}</yn-button>
    </div>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-button/";
export default {
  props: {
    fileName: {
      type: String,
      default: "模板"
    },
    buttonName: {
      type: String,
      default: "新增模板"
    }
  },
  methods: {
    btnClick() {
      this.$emit("btnClick");
    }
  }
};
</script>

<style lang="less" scoped>
.right-empty {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .empty-content {
    text-align: center;
    .content-des {
      height: @rem22;
      color: @yn-label-color;
      text-align: center;
      line-height: @rem22;
      font-weight: 400;
    }
  }
}
</style>
