const USER_TYPE_MAP = {
  searchShow: "journalQueryPageShow",
  listShow: "journalListPageShow",
  searchFilter: "journalQueryPageFilter",
  listFilter: "journalListPageFilter"
};

const BANLANCETYPE = {
  NO_BALANCE: "不平衡",
  ENTITY_BALANCE: "按组织平衡",
  JOURNAL_BALANCE: "按日记账平衡",
  SCOPE_BALANCE: "按合并组平衡"
};

const POV_OBJECTID_MAP = {
  "11ece3ad1557b6c3a77dbb834859cd67": "scope", // 合并组
  "11ece3ad04cfd7f4a77d7dafcecdf7b5": "entity" // 组织
};

const POV_PREFIX_LIST = ["year", "version", "period"];

// 日记账查询页面表格列 scopedSlots
const SEARCH_COLUMNS_SCOPED = [
  "postStatus",
  "journalName",
  "journalDesc",
  "journalCode",
  "balanceType",
  "debit",
  "credit",
  "fileName"
];

// 日记账查询 列 右侧对齐
const TEXTALIGNR_TITLE = ["debit", "credit"];
// 复制校验 错误 title
const COPY_CHECK_ERROR_TYPE_TITLE_WORD = {
  1: "缺少科目成员",
  2: "科目类型错误"
};

export {
  USER_TYPE_MAP,
  BANLANCETYPE,
  POV_OBJECTID_MAP,
  POV_PREFIX_LIST,
  SEARCH_COLUMNS_SCOPED,
  TEXTALIGNR_TITLE,
  COPY_CHECK_ERROR_TYPE_TITLE_WORD
};
