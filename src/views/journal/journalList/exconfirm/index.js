import UiUtils from "yn-p1/libs/utils/UiUtils";
import YnIcon from "yn-p1/libs/components/yn-icon";
import { isType } from "@/utils/common";
import "./index.less";
/**
 * jsx及render 比较麻烦，二次封装confirm，拓展confirm,
 * 目的: 解决 confirm 无法图标自定义，按钮自定义，直接写jsx引入大量冗余less问题
 * {
 *   UiUtils 属性
 *   title title
 *   content 完全自定义confirm 内容，自定义样式
 *   style,
 *   ok,
 *   cancel,
 *   okText,
 *   cancelText,
 *   ...自行扩充
 *
 *   拓展属性：
 *   type, 图标类型 function vnode,
 *   button 按钮 function vnode, 当按钮数量超过 confirm 自身默认数量时
 *   style 字体样式,
 *   vm content上下文，自动添加页签级别动态弹框
 *
 *   预置的 less 类
 *   main 主要信息 .exconfirm-main
 *   des, 描述 .exconfirm-des
 *   list, 列表 .exconfirm-list
 * }
 */
const iconstatus = {
  success: "check-circle",
  error: "close-circle",
  warning: "exclamation-circle"
};
export function confirm(options) {
  const {
    title,
    content,
    type,
    style,
    ok,
    cancel,
    okText,
    cancelText,
    button
  } = options;
  return UiUtils.confirm({
    title: title,
    content: h => (
      <div>
        {isType(content, "Function") ? content(h) : content}
        {button && <div class="exconfirm-button">{button(h)}</div>}
      </div>
    ),
    // YnIconSvg 图标不显示
    icon: h =>
      h(YnIcon, {
        props: {
          type: iconstatus[type],
          theme: "filled"
        },
        style: {
          ...style
        },
        class: `${type}-color`
      }),
    onOk() {
      ok && ok();
    },
    onCancel() {
      cancel && cancel();
    },
    okText: okText || "确定",
    cancelText: cancelText || "取消",
    class: `exconfirm ${button ? "exconfirm-custom-button" : ""} ${
      options.class
    }`
  });
}
