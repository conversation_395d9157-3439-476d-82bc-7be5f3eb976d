.exconfirm {
  font-family: PingFangSC-Regular;
  font-size: @rem14;
  .exconfirm-main {
    color: @yn-text-color;
    font-weight: 400;
    margin-bottom: @rem20;
  }
  .exconfirm-des {
    color: @yn-text-color-secondary;
    margin-bottom: @rem12;
    font-weight: 400;
  }
  .exconfirm-list {
    font-weight: 400;
    max-height: 50vh;
    overflow: auto;
    li {
      color: @yn-link-color;
      cursor: pointer;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      &:hover {
        text-decoration: underline;
      }
    }
  }
  .exconfirm-button {
    position: absolute;
    bottom: @rem24;
    right: @rem24;
    button {
      margin-left: @rem8;
    }
  }
  &.exconfirm-custom-button {
    .ant-modal-confirm-btns {
      display: none;
    }
  }
  .ant-modal-confirm-body {
    .success-color {
      color: @yn-success-color;
    }
    .warning-color {
      color: @yn-warning-color;
    }
    .error-color {
      color: @yn-error-color;
    }
  }
}
