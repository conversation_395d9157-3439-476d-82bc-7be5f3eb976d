<template>
  <div class="journal-list-container cs-container">
    <yn-spin :spinning="spinning" class="spinning">
      <SearchList
        v-show="!empty"
        ref="searchListRef"
        :data.sync="formData"
        useType="listFilter"
        class="list-search"
        title="日记账列表"
        :isList="isList"
        @reset="resetForm"
        @searchEvent="search"
      />
      <section v-show="!empty" class="cs-body-table list-container dataarea">
        <section class="toolgroup cs-body-table-action">
          <div v-if="isNormal" class="btngroup">
            <yn-button type="primary" @click="handlerModel('AddModal')">
              新增日记账
            </yn-button>
            <yn-button @click="resolveEvent('isResolve', true, setFormPost)">
              过账
            </yn-button>
            <yn-button
              @click="resolveEvent('isUnResolve', true, setFormCancel)"
            >
              取消过账
            </yn-button>
            <yn-button @click="resolveEvent('isDelete', true, setFormDel)">
              删除
            </yn-button>
            <yn-button @click="resolveEvent('isCopy', true, setFormOrigin)">
              复制
            </yn-button>
          </div>
          <div v-if="!isNormal" class="selected-number">
            已选
            <span>{{ selectedRowKeys.length }}</span>
            条
          </div>
          <div v-if="isResolve" class="btngroup resolve-group">
            <yn-button
              type="primary"
              :disabled="selectedRowKeys.length == 0"
              @click="postingJournal()"
            >
              过账
            </yn-button>
            <yn-button @click="resolveEvent('isResolve', false, setFormOrigin)">
              取消
            </yn-button>
          </div>
          <div v-if="isUnResolve" class="btngroup unresolve-group">
            <yn-button
              type="primary"
              :disabled="selectedRowKeys.length == 0"
              @click="cancelPostingJournal()"
            >
              取消过账
            </yn-button>
            <yn-button
              @click="resolveEvent('isUnResolve', false, setFormOrigin)"
            >
              取消
            </yn-button>
          </div>
          <div v-if="isDelete" class="btngroup delete-group">
            <yn-button
              type="primary"
              :disabled="selectedRowKeys.length == 0"
              @click="deleteJournal()"
            >
              删除
            </yn-button>
            <yn-button @click="resolveEvent('isDelete', false, setFormOrigin)">
              取消
            </yn-button>
          </div>
          <div v-if="isCopy" class="btngroup delete-group">
            <yn-button
              type="primary"
              :disabled="selectedRowKeys.length == 0"
              @click="copyJournal()"
            >
              复制
            </yn-button>
            <yn-button @click="resolveEvent('isCopy', false, setFormOrigin)">
              取消
            </yn-button>
          </div>
          <div v-if="isExport" class="btngroup export-group">
            <yn-button
              type="primary"
              :disabled="selectedRowKeys.length == 0"
              @click="handlerModel('ExportJournal', selectedRowKeys)"
            >
              导出
            </yn-button>
            <yn-button @click="resolveEvent('isExport', false)">
              取消
            </yn-button>
          </div>
          <div v-show="isNormal" class="icongroup">
            <yn-dropdown v-model="showHeadMenu" :trigger="['click']">
              <svg-icon
                class="icon-export"
                title="导出"
                type="icon-a-c1_cr_export-dropdown"
              />
              <yn-menu slot="overlay">
                <yn-menu-item @click="exportAll">
                  <span>全部导出</span>
                </yn-menu-item>
                <yn-menu-item @click="exportPart">
                  <span>部分导出</span>
                </yn-menu-item>
                <yn-menu-item @click="exportTmp">
                  <span>模板导出</span>
                </yn-menu-item>
              </yn-menu>
            </yn-dropdown>
            <svg-icon
              type="icon-Import"
              title="导入"
              @click="handlerModel('ImportJournal', params.id)"
            />
            <svg-icon
              type="icon-set-up"
              title="配置表头"
              @click="() => (fieldVisible = true)"
            />
          </div>
        </section>
        <yn-table
          ref="mytable"
          :customRow="handlerCustomRow"
          :class="['list-table', { normal: isNormal }]"
          :columns="columns"
          :components="components"
          rowKey="journalId"
          :dataSource="dataSource"
          :loading="loading"
          :scroll="{ x: '100%', y: scrollHeight }"
          :rowSelection="
            isNormal ? false : { selectedRowKeys: selectedRowKeys }
          "
          @rowSelectChange="handlerSelectChange"
        >
          <span slot="table.postStatus" slot-scope="postStatus">
            <span :class="['post-status', STATUSMAP[postStatus]]"></span>
            {{ POSTSTATUS[postStatus] }}
          </span>
          <span slot="table.balanceType" slot-scope="balanceType">
            {{ BANLANCETYPE[balanceType] }}
          </span>
          <span slot="table.fileName" slot-scope="text, record">
            <yn-tooltip
              :visibleOnOverflow="false"
              overlayClassName="attachmentList-tooltip"
              placement="top"
            >
              <template slot="title">
                <div
                  v-for="(item, index) in record.journalAttachmentList"
                  :key="index"
                >
                  {{ item.attachmentName }}
                </div>
              </template>
              <yn-button
                class="attachmentList"
                @click.stop="handlerModel('DrawerOther', record)"
              ><svg-icon
                 class="attachmentList-icon"
                 :isIconBtn="false"
                 type="icon-c1_cr_accessory"
               />
                <span class="attachmentList-name">
                  {{
                    record.journalAttachmentList &&
                      record.journalAttachmentList[0]
                      ? record.journalAttachmentList[0].attachmentName
                      : "-"
                  }}
                </span>
              </yn-button>
            </yn-tooltip>
          </span>
          <span
            slot="table.action"
            slot-scope="text, record"
            class="action-btns"
            @dblclick.stop
          >
            <div class="samewidth">
              <yn-button
                :class="[
                  'action-btn  yn-a-link',
                  {
                    loading: record.postloading,
                    'hidden-btn': record.postStatus === POSTFIELD.POSTING
                  }
                ]"
                type="text"
                :disabled="record.disabled || record.postloading"
                :loading="record.postloading"
                @click.stop="handlerJournal(record)"
              >
                {{ getStatusText(record) }}
              </yn-button>
            </div>
            <span
              :class="[
                'action-btn  yn-a-link to-right',
                {
                  'hidden-btn': record.postloading
                }
              ]"
              type="text"
              :disabled="record.disabled"
              @click.stop="handlerModel('CopyJournalModal', record)"
            >
              复制
            </span>
            <div
              :class="{
                'hidden-btn':
                  record.postStatus === POSTFIELD.POSTED ||
                  record.postStatus === POSTFIELD.POSTING ||
                  record.postloading
              }"
            >
              <yn-popconfirm
                title="你要删除当前日记账吗？"
                okText="删除"
                cancelText="取消"
                placement="bottomLeft"
                @confirm="deleteJournal(record)"
              >
                <span
                  :class="[
                    'action-btn  yn-a-link jn-delete',
                    { loading: record.deleteloading }
                  ]"
                  type="text"
                  :disabled="record.disabled || record.deleteloading"
                  :loading="record.deleteloading"
                  @click.stop
                >
                  删除
                </span>
              </yn-popconfirm>
            </div>
            <SvgIcon
              type="icon-c1_cr_form_enter"
              class="jn-detail"
              :isIconBtn="false"
            />
          </span>
        </yn-table>
        <yn-pagination
          ref="mainPagination"
          v-model="currentPage"
          :disabled="loading"
          class="pagination"
          :total="listTotal"
          :pageSize.sync="currentPageSize"
          :showTotal="total => '总计 ' + total + ' 条'"
          showSizeChanger
          showQuickJumper
          :pageSizeOptions="pageSizeOptions"
          @change="search"
          @showSizeChange="searchReset"
        >
          <template slot="buildOptionText" slot-scope="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </yn-pagination>
      </section>
      <yn-empty
        v-if="empty"
        class="list-empty"
        :image="require('../../../../image/reportEmpty.png')"
      >
        <span slot="description" class="content-des">
          当前日记账列表为空，马上开始新增!
        </span>
        <yn-button
          class="content-btn"
          type="primary"
          @click="handlerModel('AddModal')"
        >
          新增日记账
        </yn-button>
      </yn-empty>
    </yn-spin>
    <TableFilter
      useType="listShow"
      :fieldVisible="fieldVisible"
      :plainOptions="plainOptions"
      :defaultDisabled="defaultDisabled"
      :lastCheckedList="columns"
      @closeFieldModal="closeFieldModal"
    />
    <keep-alive>
      <component
        :is="componentId"
        :key="componentId"
        :visible.sync="visible"
        v-bind="componentProps"
        @ok="handlerOk"
      />
    </keep-alive>
  </div>
</template>
<script>
import SearchList from "../../searchList.vue";
import { mapState } from "vuex";
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-pagination/";
import "yn-p1/libs/components/yn-menu/";
import YnIconSvg from "yn-p1/libs/components/yn-icon/yn-icon-svg";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-tooltip/";
import journalService from "@/services/journal";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import SvgIcon from "@/components/ui/SvgIcon.vue";
import _debounce from "lodash.debounce";
import _uniqueId from "lodash/uniqueId";
import { confirm } from "../exconfirm";
import { EventCollect } from "@/utils/common";
import "yn-p1/libs/components/yn-icon-button/";
import { JOURNAL_LIST_PATH } from "@/constant/common";
import { USER_TYPE_MAP } from "../../constant";
import defaultParams from "@/mixin/defaultParams";
import commonService from "@/services/common";
import { getComponents } from "@/utils/dragWidth";
import { replaceEmptyFields } from "../../../../utils/journal";
// 定义状态常量
const POSTED = "POSTED";
const POSTING_FAILED = "POSTING_FAILED";
const NOT_POSTED = "NOT_POSTED";
const POSTING = "POSTING";

const TABNAME = "journalDetail";
const TAB_URI = "/journal/detail";

const TEXTMAP = {
  [POSTED]: "取消过账",
  [POSTING_FAILED]: "过账",
  [NOT_POSTED]: "过账",
  [POSTING]: ""
};
const POSTSTATUS = Object({
  [POSTED]: "已过帐",
  [POSTING_FAILED]: "过帐失败",
  [NOT_POSTED]: "未过帐",
  [POSTING]: "过帐中"
});
const STATUSMAP = Object({
  [POSTED]: "success",
  [POSTING_FAILED]: "error",
  [NOT_POSTED]: "unresolve",
  [POSTING]: "resolving"
});
const POSTFIELD = Object({
  POSTED: POSTED,
  POSTING_FAILED: POSTING_FAILED,
  NOT_POSTED: NOT_POSTED,
  POSTING: POSTING
});
const BANLANCETYPE = Object({
  NO_BALANCE: "不平衡",
  ENTITY_BALANCE: "按组织平衡",
  JOURNAL_BALANCE: "按日记账平衡",
  SCOPE_BALANCE: "按合并组平衡"
});
const columnsMap = {};
const postCollector = new EventCollect(1000);
const unPostCollector = new EventCollect(1000);
export default {
  name: "JournalList",
  components: {
    SearchList,
    YnIconSvg,
    SvgIcon,
    DrawerOther: () => import("./drawerother"),
    CopyJournalModal: () => import("./CopyJournalModal"),
    AddModal: () => import("./AddJournalModal"),
    ExportJournal: () => import("./ExportJournalModal"),
    ExportTemp: () => import("./ExportTempModal"),
    ImportJournal: () => import("./ImportJournalModal"),
    TableFilter: () => import("../../filterOrShowField.vue"),
    BatchCopyJournalModal: () => import("./BatchCopyJournalModal.vue")
  },
  mixins: [defaultParams],
  data() {
    return {
      components: getComponents(() => ({
        columns: this.columns,
        right: 1,
        table: this.$refs.mytable,
        callback: () => {
          this.syncJson();
          this.saveColumnWidth();
        }
      })),
      BANLANCETYPE: BANLANCETYPE,
      POSTSTATUS: POSTSTATUS,
      STATUSMAP: STATUSMAP,
      POSTFIELD: POSTFIELD,
      scrollHeight: true,
      showHeadMenu: false,
      componentId: null,
      isFilterVisible: false,
      isNormal: true,
      isExport: false,
      isDelete: false,
      isUnResolve: false,
      isResolve: false,
      isCopy: false,
      visible: false,
      componentProps: {},
      formData: {},
      columns: [
        {
          title: "编码",
          width: 120,
          key: "journalCode",
          dataIndex: "journalCode"
        },
        {
          title: "版本",
          width: 120,
          key: "version",
          dataIndex: "version"
        },
        {
          title: "年",
          width: 130,
          key: "year",
          dataIndex: "year"
        },
        {
          title: "期间",
          width: 70,
          key: "period",
          dataIndex: "period"
        },
        {
          title: "日记账名称",
          width: 210,
          ellipsis: true,
          key: "journalName",
          dataIndex: "journalName",
          customCell: (record, rowIndex, column) => {
            return {
              title: record["journalName"]
            };
          }
        },
        {
          title: "日记账说明",
          key: "journalDesc",
          ellipsis: true,
          width: 299,
          dataIndex: "journalDesc",
          customCell: (record, rowIndex, column) => {
            return {
              title: record["journalDesc"]
            };
          }
        },
        {
          title: "过账状态",
          width: 120,
          key: "postStatus",
          dataIndex: "postStatus",
          scopedSlots: {
            customRender: "postStatus"
          }
        },
        {
          dataIndex: "updateDate",
          key: "updateDate",
          title: "最后修改时间",
          width: 210
        },
        {
          dataIndex: "Audittrail",
          key: "Audittrail",
          title: "审计线索",
          width: 210
        },
        {
          title: "操作",
          fixed: "right",
          width: 230,
          key: "action",
          dataIndex: "action",
          scopedSlots: {
            customRender: "action"
          }
        }
      ],
      dataSource: [],
      spinning: false,
      loading: false,
      paramChanged: false, // 参数变动标识
      listTotal: 120,
      currentPage: 1,
      currentPageSize: 20,
      pageSizeOptions: ["10", "20", "50", "100"],
      fieldVisible: false,
      isPartExport: false,
      selectedRows: [],
      selectedRowKeys: [],
      plainOptions: [
        {
          label: "编码",
          value: "编码",
          type: "yn-select",
          field: "journalCode"
        },
        {
          label: "日记账名称",
          value: "日记账名称",
          type: "yn-select",
          field: "journalName"
        },
        {
          label: "日记账说明",
          value: "日记账说明",
          type: "yn-input",
          field: "journalDesc"
        },
        {
          label: "过账状态",
          value: "过账状态",
          type: "yn-select",
          field: "postStatus"
        },
        {
          label: "最后修改人",
          value: "最后修改人",
          type: "yn-select",
          field: "updateBy"
        },
        {
          label: "最后修改时间",
          value: "最后修改时间",
          type: "yn-range-picker",
          field: "updateDate"
        },
        {
          label: "平衡类型",
          value: "平衡类型",
          type: "yn-select",
          field: "balanceType"
        },
        {
          label: "附件",
          value: "附件",
          type: "yn-input",
          field: "fileName"
        },
        {
          label: "版本",
          value: "版本",
          dimName: "版本",
          dimCode: "Version",
          type: "yn-select-tree",
          field: "version",
          objectId: "11ec45fe70e7ec75891db3348c1632bd"
        },
        {
          label: "年",
          value: "年",
          dimName: "年",
          dimCode: "Year",
          type: "yn-select-tree",
          field: "year",
          objectId: "11ec45fe39dcbd7d891dd934dba15806"
        },
        {
          label: "期间",
          value: "期间",
          dimName: "期间",
          dimCode: "Period",
          type: "yn-select-tree",
          field: "period",
          objectId: "11ec45fe5b895852891d0d7e9c566254"
        }
      ],
      defaultDisabled: ["编码", "日记账名称", "日记账说明", "过账状态"],
      lastCheckedList: [],
      // 用户拖动列宽json
      json: {}
    };
  },
  computed: {
    ...mapState({
      activeKey: state => state.common.tabActiveId
    }),
    journalIds() {
      const param = !this.selfTab
        ? this.getTabParamsMixin()
        : this.params.params;
      return param ? param.journalIds : null;
    },
    isList() {
      return this.journalIds && this.journalIds.length > 0;
    },
    empty() {
      return false;
    }
  },
  watch: {
    formData() {
      this.paramChanged = true;
    },
    isNormal() {
      this.resetSelection();
    },
    activeKey(newVal) {
      if (newVal === this.params.id) {
        this.search();
      }
    },
    visible(newVal) {
      if (!newVal) {
        this.$nextTick(() => {
          this.componentId = null;
        });
      }
    }
  },
  async created() {
    this.spinning = true;
    await this.getUserColumns();
    await this.getShowDims();
    await this.getColumnWidth();
    this.activeTabCbMixin(this.search);
  },

  mounted() {
    this.$nextTick(() => {
      this.setScrollHeight();
    });
  },
  beforeDestroy() {
    this.sizeObserve && this.sizeObserve.unobserve(this.dom_dataarea);
    this.dom_dataarea = null;
  },
  methods: {
    async postingJournal(data) {
      this.setPostingload(data, true);
      const params = data ? [data.journalId] : this.selectedRowKeys;
      // 多个异步过账收集参数，一次性批量处理
      postCollector.collect(params).then(async paramsAll => {
        const paramsTmp = paramsAll.flat();
        if (!(await this.checkSpecialAccount(paramsTmp))) {
          this.setPostingload(data, false);
          return;
        }
        const res = await journalService(
          "postingJournal",
          paramsTmp
        ).catch(() => this.setPostingload(data, false));
        const tableItems = await this.queryJournal(false);
        this.setPostStatus(tableItems, data);
        this.postingConfirm(paramsTmp.length > 1, res);
        this.resetSelection();
        this.setPostingload(data, false);
      });
    },
    async cancelPostingJournal(data) {
      this.setCelPostingload(data, true);
      const params = data ? [data.journalId] : this.selectedRowKeys;
      // 多个异步过账收集参数，一次性处理
      unPostCollector.collect(params).then(async paramsall => {
        const paramstmp = paramsall.flat();
        const res = await journalService(
          "cancelPostingJournal",
          paramstmp
        ).catch(() => this.setCelPostingload(data, false));
        const tableItems = await this.queryJournal(false);
        this.setPostStatus(tableItems, data);
        this.cancelPostingConfirm(paramstmp.length > 1, res);
        this.resetSelection();
        this.setCelPostingload(data, false);
      });
    },
    async queryJournal(loading = true, queryId = this.setQueryId()) {
      this.loading = loading;
      // this.spinning = true;
      const param = !this.selfTab
        ? this.getTabParamsMixin()
        : this.params.params;
      const journalIds = param && param.journalIds;
      this.currentPage = this.paramChanged ? 1 : this.currentPage;
      let params = {
        page: this.currentPage,
        pageSize: this.currentPageSize
      };
      this.paramChanged = false;
      if (journalIds && journalIds.length) {
        params.journalIdList = journalIds;
      }
      const formatFormData = this.getFormatFormData();
      params = {
        ...params,
        ...formatFormData
      };
      const {
        data: {
          data: { data, total },
          success
        }
      } = await journalService("queryJournal", params);
      this.spinning = false;
      if (success && queryId === this.getQueryId()) {
        this.dataSource = data.map(item => ({
          ...replaceEmptyFields(item),
          ...replaceEmptyFields(item.dimCodeMemberNameMap)
        }));
        this.listTotal = total;
      }
      if (queryId === this.getQueryId()) {
        this.loading = false;
      }
      return data;
    },
    getFormatFormData() {
      const baseDim = ["periodList", "versionList", "yearList"];
      const res = {};
      Object.keys(this.formData).forEach(item => {
        const currVal = this.formData[item];
        if (baseDim.indexOf(item) === -1) {
          res[item] = currVal;
        } else {
          res[item] = currVal
            ? currVal.map(item => item.split("-")[item.split("-").length - 1])
            : [];
        }
      });
      return res;
    },
    async batchDeleteJournal(record) {
      const params = record ? [record.journalId] : this.selectedRowKeys;
      journalService("batchDeleteJournal", params)
        .then(async res => {
          const { data } = res;
          if (data.success) {
            this.closetabMixin(params);
            this.resetSelection();
            UiUtils.successMessage("删除日记账成功");
            if (!record) {
              this.resolveEvent("isDelete", false, this.setFormOrigin);
            } else {
              await this.queryJournal();
              this.loading = false;
            }
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    async checkSpecialAccount(id) {
      return journalService("checkSpecialAccount", id).then(res => {
        const { checkCode, checkDesc } = res.data;
        if (checkCode !== 0) {
          UiUtils.warning({
            title: checkCode === 2 ? "科目类型错误" : "缺少科目成员",
            content: checkDesc
          });
          return false;
        } else {
          return true;
        }
      });
    },
    deleteJournal(record) {
      if (record) {
        this.$set(record, "disabled", true);
        this.$set(record, "deleteloading", true);
        this.batchDeleteJournal(record);
      } else {
        if (record) {
          UiUtils.confirm({
            title: "你确定要删除当前已选日记账吗？",
            okText: "确认",
            cancelText: "取消",
            onOk: () => {
              // this.spinning = true;
              this.loading = true;
              this.batchDeleteJournal(record);
            }
          });
        } else {
          UiUtils.confirm({
            title: "删除已选日记账",
            class: "jsx-message",
            content: h => (
              <div class="message-title">日记账删除后，列表不再显示</div>
            ),
            okText: "删除",
            cancelText: "取消",
            onOk: () => {
              // this.spinning = true;
              this.loading = true;
              this.batchDeleteJournal();
            }
          });
        }
      }
    },
    copyJournal() {
      this.handlerModel("BatchCopyJournalModal", {
        journalIdList: this.selectedRowKeys,
        formData: this.formData
      });
    },
    search: _debounce(async function() {
      await this.queryJournal();
    }, 300),
    resetForm() {
      // 重置后恢复批量操作的参数
      this.isResolve && this.setFormPost();
      this.isUnResolve && this.setFormCancel();
      this.isDelete && this.setFormDel();
      this.isCopy && this.setFormOrigin();
    },
    searchReset() {
      this.search();
    },
    // 列宽记忆相关
    async getColumnWidth() {
      await new Promise(resolve => {
        commonService("getLastSettingV", {
          tag: "columnWidth",
          key: "journalList"
        }).then(res => {
          if (res.data && res.data.data && res.data.data.value) {
            this.json = JSON.parse(res.data.data.value);
            this.cwObjectId = res.data.data.objectId;
            this.columns = this.columns.map(item => {
              item.width = this.json[item.key] || item.width || 210;
              return item;
            });
            resolve();
          }
        });
      });
    },
    syncJson() {
      this.json = this.columns.reduce((pre, next) => {
        pre[next.key] = next.width;
        return pre;
      }, {});
    },
    saveColumnWidth: _debounce(function() {
      commonService("saveOrUpdateUserSetting", {
        tag: "columnWidth",
        objectId: this.cwObjectId,
        key: "journalList",
        value: JSON.stringify(this.json)
      });
    }, 300),
    handlerAttachmentList(record) {},
    // 获取可变维度后添加字段
    addParamsToData(data) {
      let arr = [...data];
      while (arr.length) {
        const currentItem = arr.shift();
        currentItem.label = currentItem.dimMultiName;
        currentItem.value = currentItem.dimMultiName;
        currentItem.objectId = currentItem.dimId;
        if (currentItem.children && currentItem.children.length > 0) {
          arr = arr.concat(currentItem.children);
        }
      }
      return data;
    },
    // 获取用户自定义列
    async getUserColumns() {
      await journalService("getUserColumns", USER_TYPE_MAP["listShow"]).then(
        res => {
          this._lastSave = res.data.data;
        }
      );
    },
    async getShowDims() {
      await journalService("getShowDims").then(async res => {
        const dims = this.addParamsToData(res.data.data);
        this.plainOptions.push(...dims);
        const { headerNames } = this._lastSave;
        const lastSet = headerNames && headerNames.length ? headerNames : [];
        this.initData(lastSet);
      });
    },
    initData(data) {
      this.columns.forEach(item => {
        columnsMap[item.dataIndex] = item;
      });
      if (data.length === 0) {
        this.lastCheckedList = [
          ...this.defaultDisabled,
          "版本",
          "年",
          "期间",
          "审计线索",
          "最后修改时间"
        ];
        this.setColumnWidth();
        return;
      }
      this.columns.splice(
        0,
        this.columns.length - 1,
        ...this.getSelection(data)
      );
      this.lastCheckedList = [
        ...data.map(item => item.label || item.dimMultiName)
      ];
    },
    closeFieldModal(bool, chooseList) {
      this.fieldVisible = bool;
      if (!chooseList) return;
      this.columns.splice(
        0,
        this.columns.length - 1,
        ...this.getSelection(chooseList)
      );
    },
    handlerSelectChange(selectedRow, selectedRowKeysAll) {
      this.selectedRows = selectedRow;
      this.selectedRowKeys = selectedRowKeysAll;
    },
    handlerCustomRow(record, index) {
      return {
        on: {
          // 事件
          click: e => {
            this.isNormal && !record.disabled && this.clickRow(record);
            return false;
          } // 点击行
        }
      };
    },
    handlerJournal(record) {
      if (record.postStatus === POSTED) {
        this.cancelPostingJournal(record);
      } else {
        this.postingJournal(record);
      }
    },
    exportAll() {
      this.showHeadMenu = false;
      const searchCondition = this.$refs.searchListRef.searchForm.getFieldsValue();
      const reqP = this.$refs.searchListRef.formatParams(searchCondition, true);
      // 过账失败列表页签上全部导出 参数传 journalIds
      let journalIds =
        this.params && this.params.params && this.params.params.journalIds;
      if (!this.selfTab) {
        const params = this.getTabParamsMixin();
        journalIds = params && params.journalIds;
      }
      reqP.journalIds = journalIds;
      this.handlerModel("ExportJournal", reqP, "all");
    },
    exportPart() {
      this.showHeadMenu = false;
      this.resolveEvent("isExport", true);
    },
    exportTmp() {
      this.showHeadMenu = false;
      this.handlerModel("ExportTemp");
    },
    handlerModel(componentId, record, type) {
      this.visible = true;
      this.componentId = componentId;
      this.componentProps = {
        data: record,
        type
      };
    },
    getStatusText(record) {
      return TEXTMAP[record.postStatus];
    },
    resolveEvent(doType, status, callback) {
      this[doType] = status;
      this.isNormal = !status;
      if (!status) {
        this.columns.push(this._columnsAction);
      } else {
        this._columnsAction = this.columns.pop();
      }
      callback && callback();
      this.searchReset();
    },
    handlerOk(type, params) {
      const typeMap = {
        add: () => {
          this.clickRow(params, true);
        },
        copy: () => {
          if (params === "batchCopy") {
            this.resolveEvent("isCopy", false, this.setFormOrigin);
          } else {
            this.search();
          }
        },
        import: () => {
          this.search();
        },
        importAttachment: () => {
          this.search();
        },
        deleteAttachment: () => {
          this.search();
        }
      };
      typeMap[type](params);
    },
    clickRow(record, isAdd) {
      if (record.disabled) return;
      this.openDetail(record, isAdd);
    },
    // 子方法拆分
    resetSelection() {
      this.selectedRows = [];
      this.selectedRowKeys = [];
    },
    setPostingload(data, status) {
      !data && (this.loading = status);
      data && this.$set(data, "disabled", status);
      data && this.$set(data, "postloading", status);
    },
    setCelPostingload(data, status) {
      data && this.$set(data, "disabled", status);
      data && this.$set(data, "postloading", status);
      !data && (this.loading = status);
    },
    setPostStatus(data, tableItem) {
      if (tableItem) {
        const selfTableItem = data.filter(
          item => item.journalId === tableItem.journalId
        );
        selfTableItem[0] &&
          this.$set(tableItem, "postStatus", selfTableItem[0].postStatus);
      }
    },
    setFormPost() {
      this.$set(this.formData, "postStatusList", [POSTING_FAILED, NOT_POSTED]);
    },
    setFormCancel() {
      this.$set(this.formData, "postStatusList", [POSTED]);
    },
    setFormDel() {
      this.$set(this.formData, "postStatusList", [POSTING_FAILED, NOT_POSTED]);
    },
    setFormOrigin() {
      this.$set(this.formData, "postStatusList", []);
    },
    setColumnWidth(data = this.columns, columns = this.columns) {
      data.forEach(item => {
        const key = item.key;
        item.width = this.json[key] || item.width || 210;
      });
    },
    getSelection(data) {
      return data.map(item => {
        const key = item.field || item.dimCode;
        const tempObj = {
          title: item.label,
          key: key,
          dataIndex: key
        };
        const curItem = columnsMap[key] ? { ...columnsMap[key] } : tempObj;
        this.setColumnWidth([curItem], data);
        if (
          item.field === "postStatus" ||
          item.field === "balanceType" ||
          item.field === "fileName"
        ) {
          curItem.scopedSlots = {
            customRender: item.field
          };
        }
        return curItem;
      });
    },
    cancelPostingConfirm(isbatch, res) {
      const {
        data: {
          data: { operationResult, relationList, failedList }
        }
      } = res;
      if (operationResult) {
        if (relationList.length === 0) {
          UiUtils.successMessage("取消过账成功");
        } else {
          this.addModalMixin(() => {
            return confirm({
              title: "取消过账成功",
              class: "jsx-message",
              content: h => this.vnodeRenderCS(res),
              type: "success",
              ok: () => {
                this.openList(
                  relationList.map(item => item.objectId),
                  "日记账列表关联日记账清单"
                );
              },
              okText: "查询关联"
            });
          }, this.params.id);
        }
      } else {
        if (relationList.length === 0) {
          if (isbatch) {
            this.addModalMixin(() => {
              return UiUtils.warning({
                title: "取消过账完成",
                class: "jsx-message",
                content: h => this.vnodeRenderSF(res),
                type: "warning",
                okText: "知道了"
              });
            }, this.params.id);
          } else {
            this.addModalMixin(() => {
              return UiUtils.error({
                title: "取消过账失败",
                content:
                  failedList[0].postingReason ||
                  "当前日记账在流程状态中“允许填写表单”为关闭，无法取消过账！"
              });
            }, this.params.id);
          }
        } else {
          this.addModalMixin(() => {
            return UiUtils.warning({
              title: "取消过账完成",
              class: "jsx-message",
              content: h => this.vnodeRenderContent(res)
            });
          }, this.params.id);
        }
      }
    },
    openList(journalIds, title) {
      const id = _uniqueId("PostId");
      this.newtabMixin({
        id,
        title: title || "日记账列表过账失败清单",
        router: "journalList",
        uri: JOURNAL_LIST_PATH,
        params: {
          journalIds: journalIds || [],
          type: "failed"
        },
        newTabParams: {
          journalIds: journalIds || [],
          type: "failed"
        }
      });
    },

    // isAdd=true, 表示列表点击“新增日记账”弹框，填写完信息后，需要带着填写的信息到编辑/详情页中
    openDetail(record, isAdd = false) {
      const params = this.selfTab
        ? this.params.params
        : this.getTabParamsMixin();
      this.newtabMixin({
        id: record.journalId || record.objectId,
        title: `${record.journalName}`,
        uri: TAB_URI,
        router: TABNAME, // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
        params: {
          isAdd,
          journalId: record.journalId || record.objectId,
          ...record,
          pov: Object.keys(params || {}).length ? params : null
        },
        newTabParams: {}
      });
    },

    postingConfirm(isbatch, res) {
      const { data: { data: { failedList, operationResult } = {} } = {} } = res;
      if (isbatch) {
        if (operationResult) {
          UiUtils.successMessage("过账成功");
        } else {
          this.addModalMixin(() => {
            return confirm({
              title: "过账完成",
              content: h => this.vnodeRenderPF(res),
              type: "warning",
              ok: () => {
                this.openList(failedList.map(item => item.objectId));
              },
              okText: "打开失败清单"
            });
          }, this.params.id);
        }
      } else {
        if (operationResult) {
          UiUtils.successMessage("过账成功");
        } else {
          this.addModalMixin(() => {
            return confirm({
              title: "过账失败",
              content: failedList[0].postingReason,
              type: "error",
              ok: () => {
                this.openDetail(failedList[0]);
              },
              okText: "打开详情"
            });
          }, this.params.id);
        }
      }
    },
    vnodeRenderCS(res) {
      const {
        data: {
          data: { continueCount, successCount, relationList }
        }
      } = res;
      return (
        <div>
          <div class="exconfirm-main">
            取消过账成功 <span> {successCount}</span> 条<br />
            <br />
            其中有 {continueCount} 条持续日记账，共关联{relationList.length}{" "}
            条日记账
          </div>
          <div class="exconfirm-des">
            点击“日记账名称”打开详情，点击“关联查询”列表显示关联日记账
          </div>
          <ul class="exconfirm-list">
            {relationList.map(item => (
              <li
                style="cursor: pointer"
                onClick={() => {
                  this.openDetail(item);
                }}
              >
                {item.journalName}
              </li>
            ))}
          </ul>
        </div>
      );

      //  <div class="exconfirm-des">点击“查询关联”列表显示关联日记账</div>
    },
    vnodeRenderPF(res) {
      const {
        data: {
          data: { failedList, successCount, failedCount }
        }
      } = res;
      return (
        <div>
          <div class="exconfirm-main">
            过账成功 {successCount} 条，失败
            <span class="exconfirm-main-error"> {failedCount} </span>条
          </div>
          <div class="exconfirm-des">
            以下是过账失败日记账，点击“日记账”打开详情
          </div>
          <ul class="exconfirm-list">{this.vnodeRenderList(failedList)}</ul>
        </div>
      );
    },
    vnodeRenderSF(res) {
      const {
        data: {
          data: { failedList, successCount, failedCount }
        }
      } = res;
      return (
        <div>
          <div class="exconfirm-main">
            取消过账成功 {successCount} 条，失败
            <span class="exconfirm-main-error"> {failedCount} </span>条
          </div>
          <div class="exconfirm-des">
            {Object.entries(failedList.reduce((acc, item) => {
              if (!acc[item.postingReason]) {
                acc[item.postingReason] = [];
              }
              acc[item.postingReason].push(item);
              return acc;
            }, {})).map(([reason, items], index, array) => (
              <div key={index}>
                <div>{reason}</div>
                <ul class="exconfirm-list">{this.vnodeRenderList(items)}</ul>
                {index !== array.length - 1 && <br/>}
              </div>
            ))}
          </div>
        </div>
      );
    },
    vnodeRenderContent(res) {
      const {
        data: {
          data: {
            failedList,
            continueCount,
            relationList,
            successCount,
            failedCount
          }
        }
      } = res;
      return (
        <div>
          <div class="exconfirm-main">
            取消过账成功 <span> {successCount}</span> 条, 其中有 {continueCount}{" "}
            条持续日记账，共关联 {relationList.length + " "}
            条日记账
            <div class="gorelate exconfirm-list">
              <li
                onClick={() => {
                  this.openList(
                    relationList.map(item => item.objectId),
                    "日记账列表关联日记账清单"
                  );
                }}
              >
                查看关联日记账
              </li>
            </div>
          </div>
          <div>
            取消过账失败 <span style="color:red">{failedCount}</span> 条
            <br />
            <div class="exconfirm-des">
              以下日记账在流程状态中“允许填写表单”为关闭，无法取消过账！
            </div>
          </div>
          <ul class="exconfirm-list">{this.vnodeRenderList(failedList)}</ul>
        </div>
      );
    },
    vnodeRenderList(list) {
      return list.map(item => {
        return (
          <li
            key={item.objectId}
            onClick={() => {
              this.newtabMixin({
                id: item.objectId,
                title: "日记账详情",
                uri: TAB_URI,
                router: TABNAME, // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
                params: {
                  journalId: item.objectId
                },
                newTabParams: {}
              });
            }}
          >
            {item.journalName}
          </li>
        );
      });
    },
    // dom相关
    setScrollHeight() {
      this.dom_dataarea = this.$el.querySelector(".dataarea");
      const toolgroup = this.$el.querySelector(".toolgroup");
      const padding = 12;
      const header = 37;
      const pagination = this.$el.querySelector(".pagination");
      this.sizeObserve = new ResizeObserver(() => {
        const redux =
          header + toolgroup.offsetHeight + padding + pagination.offsetHeight;
        this.scrollHeight = this.dom_dataarea.offsetHeight - redux;
      });
      this.sizeObserve.observe(this.dom_dataarea);
    },
    // tools
    setQueryId() {
      this._currentQueryId = _uniqueId("QueryId");
      return this._currentQueryId;
    },
    getQueryId() {
      return this._currentQueryId;
    }
  }
};
</script>

<style lang="less" scoped>
.journal-list-container {
  width: 100%;
  height: 100%;
  font-family: PingFangSC-Regular;
  font-size: @rem14;
  display: flex;
  flex-direction: column;
  .list-container {
    height: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: @rem12 @rem32 0;
    height: 100%;
  }
  /deep/.ant-spin-nested-loading,
  /deep/.ant-spin-container {
    height: 100%;
  }
  .spinning {
    & /deep/ .ant-spin-container {
      display: flex;
      flex-direction: column;
    }
  }
  .jn-detail {
    color: @yn-label-color;
  }
  .list-empty {
    margin-top: 50vh;
    transform: translateY(-50%);
  }
  .list-search {
    flex-shrink: 0;
  }
  .toolgroup {
    display: flex;
    align-items: center;
    padding-bottom: @rem16;
  }
  .selected-number {
    height: @rem22;
    color: @yn-text-color-secondary;
    text-align: left;
    line-height: @rem22;
    font-weight: 400;
    margin-right: @rem16;
    & > span {
      padding: 0 @rem4;
      display: inline-block;
      text-align: center;
      width: @rem32;
    }
  }
  .btngroup {
    & > button {
      margin-right: @rem8;
    }
  }
  .icongroup {
    display: flex;
    align-items: center;
    margin-left: auto;
    .icon-export {
      &::before {
        content: "";
      }
      /deep/.iconfont {
        font-size: 2rem;
        .svg-icon {
          font-size: 2rem;
        }
      }
    }
    .icongroup-item {
      font-size: @rem14;
      padding: @rem8;
    }
    .custom-download {
      padding-right: @rem4;
    }
  }
  .journalDesc {
    display: block;
    min-width: 6.25rem;
  }
  .attachmentList {
    display: flex;
    align-items: center;
    width: 100%;
    border: none;
    color: @yn-text-color;
    text-align: left;
    background: transparent;
    .attachmentList-name {
      display: inline-block;
      width: 90%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &:hover {
      background: @yn-background-color-light;
    }
    .attachmentList-icon {
      margin-right: 0.5rem;
      color: @yn-label-color;
    }
  }
  .post-status {
    display: inline-block;
    border-radius: 50%;
    margin-right: 0.125rem;
    width: @rem8;
    height: @rem8;
    &.success {
      background: @yn-success-color;
    }
    &.error {
      background: @yn-error-color;
    }
    &.unresolve {
      background: @yn-warning-color;
    }
    &.resolving {
      background: @yn-chart-1;
    }
  }
  .icon-expand {
    font-size: 0.625rem;
    margin-left: 0.1875rem;
  }
  .action-btns {
    display: flex;
    align-items: center;
    overflow: hidden;
  }
  .yn-a-link {
    display: inline-block;
    padding-left: 2px;
    padding-right: 2px;
    border-radius: 0.25rem;
  }
  .action-btn {
    color: @yn-chart-1;
    margin-right: @rem12;
    padding: 0 2px;
    height: auto;
    line-height: normal;
    border-radius: 0.25rem;
    &.jn-delete {
      margin-right: @rem6;
    }
    &.to-right {
      margin-left: auto;
    }
  }
  .hidden-btn {
    position: relative;
    opacity: 0;
    height: 100%;
    transition: opacity 0.5s;
    &::after {
      position: absolute;
      display: block;
      content: "";
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
    }
  }
  .ato-detail {
    color: @yn-primary-color;
  }
  .to-detail {
    color: @yn-label-color;
  }
  .samewidth {
    min-width: 4.25rem;
    .action-btn {
      padding-left: 0;
    }
  }
  .list-table {
    width: calc(100% + 10px);
    background: @yn-body-background;
  }
  .export-icon {
    /deep/.icon {
      width: @rem28;
    }
  }
  /deep/ .ant-table-tbody .ant-table-row {
    cursor: pointer;
  }
  .pagination {
    display: flex;
    align-items: center;
    height: 2.75rem;
    padding: @rem8 0;
    margin-left: auto;
  }

  .content-des {
    height: @rem22;
    color: @yn-label-color;
    text-align: center;
    line-height: @rem22;
    font-weight: 400;
  }

  /deep/ .ant-table-thead > tr > th {
    background: @yn-table-header-bg;
  }
  .normal /deep/ .ant-table-tbody td {
    height: 2.4375rem;
    padding: 0px @rem16;
  }
  .pointer {
    cursor: pointer;
  }
  .pointer:hover {
    color: @yn-primary-5;
  }
  /deep/ .ant-table-row-cell-break-word {
    word-break: break-word;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
/deep/ .ant-modal-confirm .ant-modal-confirm-title {
  font-weight: bold;
}
.jsx-message /deep/ .message-title {
  color: @yn-text-color-secondary;
  text-align: left;
  font-weight: 400;
  margin-bottom: 1rem;
}
/deep/
  .ant-table-scroll
  .resize-table-th[key="action"]
  .ant-table-column-title {
  display: none;
}
/deep/ .ant-table-header {
  background: transparent;
}
/deep/ .ant-table-header::-webkit-scrollbar {
  border: 1px solid transparent !important;
  border-width: 0 0 1px 0;
}
</style>

<style lang="less">
.jsx-message .relation-items {
  max-height: 50vh;
}
.jsx-message .gorelate {
}
.jsx-message .exconfirm-main {
  color: @yn-text-color;
  font-weight: 400;
  margin-bottom: @rem20;
}
.jsx-message .exconfirm-main-error {
  color: @yn-error-color;
}
.jsx-message .exconfirm-des {
  color: @yn-text-color-secondary;
  margin-bottom: @rem12;
  font-weight: 400;
}
.jsx-message .exconfirm-list {
  font-weight: 400;
  max-height: 50vh;
  overflow: auto;
  li {
    color: @yn-link-color;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    &:hover {
      text-decoration: underline;
    }
  }
}
.attachmentList-tooltip .ant-tooltip-inner {
  max-height: 25rem;
  overflow: auto;
}
</style>
