<template>
  <yn-modal
    v-if="show && visible"
    title="文件预览"
    dialogClass="journal-preview"
    :visible="show && visible"
    size="max"
    @cancel="handleCancel"
  >
    <iframe
      id="email"
      :style="{ height: '100%', width: '100%' }"
      :src="pageUrl"
      :frameBorder="0"
      scrolling="auto"
    ></iframe>
    <template slot="footer">
      <yn-button key="back" @click="handleCancel">关闭</yn-button>
    </template>
  </yn-modal>
</template>

<script>
import DsUtils from "yn-p1/libs/utils/DsUtils";
import "yn-p1/libs/components/yn-modal/";
import { APPS } from "@/config/SETUP";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import commonService from "@/services/common";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    fileId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      show: false,
      pageUrl: ""
    };
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.show = false;
          this.getConsoleFrontEnd();
        }
      },
      immediate: true
    }
  },
  methods: {
    handleCancel(e) {
      this.pageUrl = "";
      this.show = false;
      this.$emit("update:visible", false);
    },
    generateParams() {
      const fieldsMap = {
        TOKEN: "",
        appId: "",
        MenuId: ""
      };
      Object.keys(fieldsMap).forEach(key => {
        fieldsMap[key] = DsUtils.getSessionStorageItem(key, {
          storagePrefix: APPS.NAME,
          isJson: true
        });
      });
      const { TOKEN, appId, MenuId: menuId } = fieldsMap;
      return `?appId=${appId}&menuId=${menuId}&token=${TOKEN}&LoginToken=${TOKEN}&fileId=${this.fileId}`;
    },
    getConsoleFrontEnd() {
      const formData = new FormData();
      formData.append("fileId", this.fileId);
      commonService("canPreview", formData)
        .then(res => {
          if (!res.data.data) {
            const frontEnd = window.location.origin; // `http://**************:91`; // `http://**************:91`;
            const params = this.generateParams();
            const url = `${frontEnd}/ecs/file/v2/network/filePreview${params}`;
            this.pageUrl = url;
            this.show = true;
          } else {
            this.handleCancel();
            UiUtils.warningMessage("文件正在转换，请稍后预览");
          }
        })
        .catch(() => {
          this.handleCancel();
        });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .ant-modal-wrap {
  z-index: 9999 !important;
}
</style>
