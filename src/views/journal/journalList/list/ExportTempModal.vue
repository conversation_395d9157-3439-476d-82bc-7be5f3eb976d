<template>
  <yn-modal
    :visible="visible"
    title="模板导出"
    width="25rem"
    :bodyStyle="{ display: 'flex', 'align-items': 'center' }"
    @cancel="cancelEvent"
  >
    <yn-form
      :form="addForm"
      class="exportform"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 18 }
      }"
    >
      <yn-form-item label="导出成员" :colon="false">
        <yn-radio-group v-decorator="['exportType']">
          <yn-radio class="item" :value="0">名称</yn-radio>
          <yn-radio class="item" :value="1">唯一标识</yn-radio>
        </yn-radio-group>
      </yn-form-item>
      <yn-form-item label="日记账模板" :colon="false">
        <yn-select-tree
          v-decorator="[
            'templateId',
            {
              initialValue: defaultV,
              rules: [{ required: true, message: '请选择日记账模板' }]
            }
          ]"
          searchMode="custom"
          :allowClear="true"
          :nonleafselectable="false"
          :datasource="options"
          placeholder="请选择"
          @customSearch="customSearch"
        />
      </yn-form-item>
    </yn-form>
    <template slot="footer">
      <yn-button key="back" @click="cancelEvent">取消</yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="loading"
        :disabled="loading"
        @click="okEvent"
      >
        确定
      </yn-button>
    </template>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-option/";
import journalService from "@/services/journal";
import { downloadFile } from "@/utils/common";
import { setDataKey2key, filterTree } from "@/utils/common";
import { defaultChooseFirst } from "../../../../utils/journal";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      options: [],
      defaultV: "",
      loading: false,
      addForm: this.$form.createForm(this, "addForm")
    };
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          journalService("queryAllTemplate").then(res => {
            const data = setDataKey2key(
              res.data,
              ["templateId", "name", "subNodes"],
              ["key", "label", "children"]
            );
            this.filterGroup(data);
            this.options = data;
            this._optionsCache = data;
            const firstItem = defaultChooseFirst(data);
            this.defaultV = firstItem && firstItem.templateId;
          });
          this.$nextTick(() => {
            this.addForm.setFieldsValue({ exportType: 0 });
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    customSearch(event) {
      const { searchValue: word } = event;
      if (!word) {
        this.options = this._optionsCache;
        return;
      }
      this.options = filterTree(this._optionsCache, "label", word);
    },
    cancelEvent() {
      this.addForm.resetFields();
      this.$emit("update:visible", false);
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    filterGroup(data) {
      for (const item of data) {
        item.key || (item.key = new Date().valueOf() + Math.random());
        if (item.isLeaf) {
          delete item.children;
        } else {
          this.filterGroup(item.children);
        }
      }
    },
    okEvent() {
      this.addForm.validateFields((err, value) => {
        if (!err) {
          this.loading = true;
          journalService("exportJournalTemplate", value)
            .then(res => {
              if (res.data) {
                downloadFile(res);
                this.cancelEvent();
              }
            })
            .finally(e => {
              this.loading = false;
            });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.exportform {
  width: 100%;
}
.item {
  margin-right: 3.125rem;
  width: 4.0625rem;
  height: @rem22;
  font-family: PingFangSC-Regular;
  font-size: @rem14;
  color: @yn-text-color-secondary;
  text-align: left;
  line-height: @rem22;
  font-weight: 400;
}
/deep/ .ant-modal-body {
  min-height: auto;
}
/deep/ .ant-form-item:last-of-type {
  margin-bottom: 0;
}
</style>
