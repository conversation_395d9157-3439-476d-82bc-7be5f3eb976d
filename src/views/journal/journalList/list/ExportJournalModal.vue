<template>
  <yn-modal
    :visible="visible"
    :title="type === 'all' ? '全部导出' : '部分导出'"
    width="25rem"
    :bodyStyle="{ display: 'flex', 'align-items': 'center' }"
    @cancel="cancelEvent"
  >
    <yn-form
      :form="addForm"
      class="exportform"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 18 }
      }"
    >
      <yn-form-item label="导出成员" :colon="false">
        <yn-radio-group v-decorator="['exportType']">
          <yn-radio class="item" :value="0">名称</yn-radio>
          <yn-radio class="item" :value="1">唯一标识</yn-radio>
        </yn-radio-group>
      </yn-form-item>
    </yn-form>
    <template slot="footer">
      <yn-button key="back" @click="cancelEvent">取消</yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="loading"
        :disabled="loading"
        @click="okEvent"
      >
        确定
      </yn-button>
    </template>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-option/";
import journalService from "@/services/journal";
import { downloadFile } from "@/utils/common";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: [Array, Object],
      default: () => []
    },
    type: {
      type: String,
      default: "part"
    },
    // 后端要求 日记账查询、日记账列表导出 加上该字段： 0 列表、1: 日记账查询
    exportPage: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      addForm: this.$form.createForm(this, "addForm")
    };
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.$nextTick(() => {
            this.addForm.setFieldsValue({ exportType: 0 });
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    cancelEvent() {
      this.$emit("update:visible", false);
    },
    okEvent() {
      this.addForm.validateFields((err, value) => {
        if (!err) {
          this.loading = true;
          const bool = this.type === "all";
          const exportParams = {
            exportAll: bool,
            exportPage: this.exportPage,
            ...value,
            [bool ? "queryParam" : "journalIds"]: this.data
          };
          if (bool && this.data.journalIds) {
            exportParams.journalIds = this.data.journalIds;
            delete this.data.journalIds;
          }
          journalService("exportJournals", exportParams)
            .then(res => {
              if (res.data) {
                downloadFile(res);
                this.cancelEvent();
              }
            })
            .finally(e => {
              this.loading = false;
            });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.exportform {
  width: 100%;
}
.item {
  margin-right: 3.125rem;
  width: 4.0625rem;
  height: @rem22;
  font-family: PingFangSC-Regular;
  font-size: @rem14;
  color: @yn-text-color-secondary;
  text-align: left;
  line-height: @rem22;
  font-weight: 400;
}
/deep/ .ant-modal-body {
  min-height: auto;
}
/deep/ .ant-form-item:last-of-type {
  margin-bottom: 0;
}
</style>
