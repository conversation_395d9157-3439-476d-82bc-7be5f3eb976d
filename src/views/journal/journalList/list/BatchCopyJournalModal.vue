<template>
  <yn-modal :visible="visible" title="批量复制到" @cancel="hideModal">
    <yn-form
      :form="batchCopyForm"
      class="copyForms"
      v-bind="{
        labelCol: { span: 3 },
        wrapperCol: { span: 21 }
      }"
    >
      <yn-form-item
        v-for="(item, index) in selectGroup"
        :key="item.key"
        :label="item.dimName"
        :colon="false"
      >
        <component
          :is="item.component"
          v-decorator="[
            item.key,
            { rules: [{ required: item.required, message: item.message }] }
          ]"
          :itemexpandable="itemExpandable"
          v-bind="item.props"
        />
        <yn-checkbox
          v-if="showCrossYear(index)"
          v-decorator="['crossCarryForward']"
        >
          跨年结转
        </yn-checkbox>
      </yn-form-item>
    </yn-form>
    <template slot="footer">
      <yn-button key="back" @click="hideModal">取消</yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="loading"
        :disabled="loading"
        @click="handlerOk"
      >
        确定
      </yn-button>
    </template>
  </yn-modal>
</template>
<script>
import "yn-p1/libs/components/yn-modal/";
import YnIconSvg from "yn-p1/libs/components/yn-icon/yn-icon-svg";
import "yn-p1/libs/components/yn-checkbox/";
import journalService from "@/services/journal";
import { setDataKey2key } from "@/utils/common";
import copyMixin from "../mixin/copyMixin";
export default {
  name: "BatchCopyJournalModal",
  components: { YnIconSvg },
  mixins: [copyMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      batchCopyForm: this.$form.createForm(this, "batchCopyForm"),
      loading: false,
      selectGroup: [
        {
          key: "versionList",
          dimName: "版本",
          dimCode: "Version",
          component: "yn-select-tree",
          message: "请选择版本",
          required: true,
          props: {
            placeholder: "请选择",
            multiple: true,
            allowClear: true,
            nonleafselectable: false,
            searchMode: "single",
            datasource: []
          }
        },
        {
          dimName: "年",
          dimCode: "Year",
          key: "yearList",
          component: "yn-select-tree",
          message: "请选择年份",
          required: true,
          props: {
            placeholder: "请选择年，如 2022 或 2022年",
            multiple: true,
            allowClear: true,
            nonleafselectable: false,
            searchMode: "single",
            datasource: []
          }
        },
        {
          dimName: "期间",
          dimCode: "Period",
          key: "periodList",
          component: "yn-select-tree",
          message: "请选择期间",
          required: true,
          props: {
            placeholder: "请选择月，如 1月 或 Jan",
            multiple: true,
            allowClear: true,
            nonleafselectable: false,
            searchMode: "single",
            datasource: []
          }
        }
      ]
    };
  },
  computed: {
    showCrossYear() {
      return index => {
        if (index !== 1) return false;
        let { yearList } = this.data.formData;
        if (!yearList) {
          yearList = [];
        }
        yearList = yearList.map(year => {
          const tempArr = year.split("-");
          return tempArr[tempArr.length - 1];
        });
        const currYearVal = this.batchCopyForm
          ? this.batchCopyForm.getFieldValue("yearList") || []
          : [];
        if (!currYearVal.length) {
          return false;
        }
        if (yearList.length !== currYearVal.length) {
          return true;
        }
        let isMatch = true;
        for (let i = 0, LEN = yearList.length; i < LEN; i++) {
          isMatch = currYearVal.indexOf(yearList[i]) !== -1;
          if (!isMatch) {
            break;
          }
        }
        return !isMatch;
      };
    }
  },
  created() {
    this.getResource();
  },
  methods: {
    hideModal() {
      this.loading = false;
      this.$emit("update:visible", false);
      this.batchCopyForm.resetFields();
    },
    getResource() {
      this.selectGroup.forEach((item, index) => {
        const { dimCode } = item;
        const params = {
          dimCode,
          needFormat: false
        };
        if (["Year", "Period"].includes(dimCode)) {
          params.needFormat = true;
        }
        journalService("getDimMembersTree", params).then(res => {
          const data = setDataKey2key(
            res.data,
            ["id", "name"],
            ["key", "label"]
          );
          this.$set(item.props, "datasource", data);
        });
      });
    },
    handlerOk() {
      this.batchCopyForm.validateFields((err, value) => {
        if (!err) {
          this.loading = true;
          journalService("copyJournal", {
            journalIdList: this.data.journalIdList,
            ...value
          })
            .then(res => {
              this.copyComplteMixin(res, "batchCopy");
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    itemExpandable(item) {
      const { isLeaf } = item;
      return !isLeaf;
    }
  }
};
</script>

<style lang="less" scoped>
.dimName {
  height: @rem20;
  font-size: @rem14;
  color: @yn-text-color;
  font-weight: 600;
}
.copyForms /deep/ .ant-form-explain {
  margin-top: @rem4;
}
/deep/ .ant-form-item {
  margin-bottom: @rem12;
}
/deep/ .ant-form-item:last-of-type {
  margin-bottom: 0;
}
</style>
