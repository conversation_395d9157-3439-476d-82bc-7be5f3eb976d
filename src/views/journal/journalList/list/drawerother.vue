<template>
  <yn-drawer
    title="附件"
    placement="right"
    :closable="true"
    width="720px"
    class="drawerother"
    :visible="visible"
    @close="onClose"
  >
    <yn-button class="upload-button" @click="handlerUpload">
      <yn-icon type="upload" /> 上传
    </yn-button>
    <div class="dataarea">
      <yn-table
        ref="mytable"
        rowKey="attachmentId"
        bordered
        :columns="columns"
        :loading="spinning"
        :data-source="tabledata"
        :scroll="{ y: scrollHeight, x: false }"
      >
        <span slot="attachmentName" slot-scope="text" class="attachmentName">{{
          text
        }}</span>
        <span slot="operatorName" slot-scope="text" class="operatorName">{{
          text
        }}</span>
        <div slot="table.action" slot-scope="text, record">
          <span
            type="link"
            class="action-button yn-a-link"
            @click="handlerDown(record)"
          >
            下载
          </span>
          <span
            type="link"
            class="action-button yn-a-link"
            @click="handlerView(record)"
          >
            预览
          </span>

          <yn-popconfirm
            title="你要删除当前日记账吗？"
            okText="删除"
            cancelText="取消"
            overlayClassName="journal-pop-other"
            placement="bottomLeft"
            @confirm="deleteJournalFiles(record)"
          >
            <span type="link" class="action-button action-last yn-a-link">
              删除
            </span>
          </yn-popconfirm>
        </div>
      </yn-table>
    </div>
    <div class="pagination">
      <span class="pagination-total">总计 {{ total }} 条</span>
      <yn-pagination
        simple
        :current="current"
        :pageSize="size"
        :total="total"
        @change="search"
      />
    </div>
    <yn-modal
      :visible="uploadVisible"
      title="上传"
      okText="提交"
      cancelText="取消"
      width="35.1875rem"
      class="import-Journal-modal"
      @cancel="cancelEvent"
    >
      <yn-upload-dragger
        name="file"
        :multiple="true"
        :remove="handleRemove"
        :beforeUpload="beforeUpload"
        :fileList="fileList"
      >
        <p class="ant-upload-drag-icon">
          <yn-icon-svg type="Updating-files" />
        </p>
        <p class="ant-upload-text">单击或拖动文件/文件夹到此区域上传</p>
        <p class="ant-upload-hint">
          仅支持： JPG/ PNG/ BMP/ PDF/ WORD/ EXCEL/ TXT/ PPT/ ZIP/ RAR
        </p>
      </yn-upload-dragger>
      <template slot="footer">
        <yn-button key="back" @click="cancelEvent">取消</yn-button>
        <yn-button
          key="submit"
          type="primary"
          :loading="loading"
          :disabled="loading || fileList.length === 0"
          @click="okEvent"
        >
          确定
        </yn-button>
      </template>
    </yn-modal>
    <PreviewFile :visible.sync="previewFileVisible" :fileId="fileId" />
  </yn-drawer>
</template>
<script>
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-pagination/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-upload-dragger/";
import { isFileType, polling } from "@/utils/common";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import journalService from "@/services/journal";
import commonService from "@/services/common";
import checkSize from "@/views/process/control/item/checkSize";
import PreviewFile from "./previewFile.vue";

export default {
  components: { PreviewFile },
  mixins: [checkSize],
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      current: 1,
      total: 50,
      size: 20,
      fileId: "",
      previewFileVisible: false,
      spinning: false,
      scrollHeight: false,
      loading: false,
      uploadVisible: false,
      totalSize: 0,
      fileList: [],
      filesRes: [],
      tabledata: [],
      columns: [
        {
          title: "附件名称",
          dataIndex: "attachmentName",
          key: "attachmentName",
          scopedSlots: {
            customRender: "attachmentName"
          }
        },
        {
          title: "上传用户",
          dataIndex: "operatorName",
          key: "operatorName",
          width: "6.25rem",
          scopedSlots: {
            customRender: "operatorName"
          }
        },
        {
          title: "操作时间",
          dataIndex: "operatorDate",
          key: "operatorDate",
          width: "11.25rem"
        },
        {
          title: "操作",
          dataIndex: "action",
          key: "action",
          width: "9.0625rem",
          scopedSlots: {
            customRender: "action"
          }
        }
      ]
    };
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.current = 1;
          this.search();
        }
      },
      immediate: true
    }
  },
  methods: {
    async deleteJournalFiles(record) {
      this.spinning = true;
      journalService("deleteJournalFilesRelation", {
        journalId: record.journalId,
        attachmentIds: [record.attachmentId]
      })
        .then(res => {
          UiUtils.successMessage("删除附件成功");
          this.$emit("ok", "deleteAttachment", this.data);
          this.search();
        })
        .finally(() => {
          this.spinning = false;
        });
    },
    async getJournalFiles() {
      const {
        data: {
          data: { data, totalCount }
        }
      } = await journalService("getJournalFiles", {
        journalId: this.data.journalId,
        offset: (this.current - 1) * this.size,
        limit: this.size
      }).catch(e => (this.spinning = false));
      this.total = totalCount;
      this.totalSize = data ? data.totalFileSize || 0 : 0;
      return data ? data.attachmentVOList || [] : [];
    },
    async search(page = this.current) {
      this.spinning = true;
      this.current = page;
      const data = await this.getJournalFiles();
      this.tabledata = data;
      this.spinning = false;
    },
    cancelEvent() {
      this.fileList = [];
      this.filesRes = [];
      this.mixinOver = false;
      this.loading = false;
      this.uploadVisible = false;
    },
    async uploadToPlatform() {
      const upMap = this.fileList.map(file => {
        const formData = new FormData();
        formData.append("uploadFile", file);
        return commonService("uploadAttachment", formData).then(res => {
          this.filesRes.push(res.data.data);
        });
      });
      return Promise.all(upMap);
    },
    okEvent() {
      if (
        this.mixinValidateOverSize([
          ...this.fileList,
          { name: "total", size: this.totalSize }
        ])
      ) {
        return;
      }
      this.loading = true;
      this.uploadToPlatform()
        .then(() => {
          journalService("journalFilesRelation", {
            journalId: this.data.journalId,
            operationType: "ADD",
            journalAttachmentList: this.filesRes.map(item => ({
              attachmentId: item.objectId,
              attachmentName: item.name,
              fileSize: item.fileSize || 0
            }))
          })
            .then(res => {
              this.cancelEvent();
              this.uploadVisible = false;
              this.fileList = [];
              this.filesRes = [];
              this.search();
              this.$emit("ok", "importAttachment", this.data);
            })
            .finally(res => {
              this.loading = false;
            });
        })
        .catch(res => {
          this.loading = false;
        });
    },
    handlerUpload() {
      this.uploadVisible = true;
    },
    handlerDown(record) {
      commonService("downloadFile", record.attachmentId).then(res => {
        if (res.data) {
          this.$emit("ok", "downloadFile", this.data);
          this.readBlobDown(res.data, record.attachmentName);
        }
      });
    },
    handlerView(record) {
      this.fileId = record.attachmentId;
      this.previewFileVisible = true;
    },
    handleRemove(file) {
      const index = this.fileList.indexOf(file);
      const newFileList = this.fileList.slice();
      newFileList.splice(index, 1);
      this.mixinIsOver50m(newFileList);
      this.fileList = newFileList;
      this.filesRes = this.filesRes.splice(index, 1);
    },
    beforeUpload(file) {
      // JPG/ PNG/ BMP/ PDF/ WORD/ EXCEL/ TXT/ PPT/ ZIP/ RAR
      if (
        !isFileType(
          file.name,
          /(xlsx|xls|jpg|png|bmp|pdf|docx|doc|ppt|txt|pptx|zip|rar)$/
        )
      ) {
        UiUtils.errorMessage("请上传正确的格式");
        return false;
      }
      if (file.size === 0) {
        UiUtils.errorMessage("不能上传空文件");
        return false;
      }
      if (
        this.mixinValidateOverSize(
          [...this.fileList, file, { name: "total", size: this.totalSize }],
          false
        )
      ) {
        return false;
      }
      this.fileList = [...this.fileList, file];
      return false;
    },
    onClose() {
      this.$emit("update:visible", false);
    },
    /**
     * 读取下载的文件流
     */
    readBlobDown(result, filename) {
      var blob = result; // ie 使用
      if (window.navigator.msSaveBlob) {
        // for ie 10 and later
        try {
          var blobObject = new Blob([blob], {
            type: "application/vnd.ms-excel"
          }); // 构造一个blob对象来处理数据
          window.navigator.msSaveBlob(blobObject, filename);
        } catch (e) {}
      } else {
        // 其他浏览器 下载方式
        var reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onload = function(e) {
          // 转换完成，创建一个a标签用于下载
          var a = document.createElement("a");
          a.download = filename; // 设置下载的文件名称
          a.href = e.target.result;
          a.click();
        };
      }
    },
    // dom相关
    setScrollHeight() {
      const mytable = this.$refs.mytable;
      polling(() => Promise.resolve(this.$el.querySelector), 100).then(
        query => {
          const dataarea = this.$el.querySelector(".dataarea");
          const header = 37;
          const redux = header;
          setTimeout(() => {
            if (dataarea.offsetHeight <= mytable.$el.offsetHeight + 3) {
              this.scrollHeight = dataarea.offsetHeight - redux;
            } else {
              this.scrollHeight = false;
            }
          }, 100);
        }
      );
    }
  }
};
</script>

<style lang="less" scoped>
.drawerother {
  font-family: PingFangSC-Regular;
  font-size: 0.875rem;
  z-index: 1040 !important;
}
.import-Journal-modal {
  /deep/ .ant-modal-mask {
    z-index: 1055 !important;
  }
  /deep/ .ant-modal-wrap {
    z-index: 1055 !important;
  }
}
/deep/ .ant-drawer-wrapper-body {
  display: flex;
  flex-direction: column;
  .ant-drawer-body {
    display: flex;
    flex-direction: column;
    height: 0;
    flex: 1;
    overflow-y: hidden;
    overflow-x: hidden;
  }
  .ant-spin-container {
    display: flex;
    flex-direction: column;
  }
}
.dataarea {
  height: 0;
  flex: 1;
  overflow-x: hidden;
  overflow-y: scroll;
  margin-right: -1rem;
  .attachmentName {
    white-space: pre-wrap;
    word-break: break-all;
  }
  .operatorName {
    width: 6rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
}

.upload {
  width: 5.125rem;
  height: 2rem;
  margin-bottom: 1rem;
}
.pagination {
  height: 2.75rem;
  display: flex;
  align-items: center;
  z-index: 10;
  background: @yn-body-background;
}
.pagination-total {
  margin-right: 0.5rem;
  margin-left: auto;
}
.action-button {
  margin-right: 0.5rem;
  color: @yn-primary-color;
  &.action-last {
    margin-right: 0;
  }
  /deep/ i {
    color: @yn-primary-color !important;
  }
}
.upload-button {
  width: 5.125rem;
  margin-bottom: 1rem;
}
/deep/ .ant-upload-list {
  //max-height: 25rem;
  //overflow-x: hidden;
  //overflow-y: auto;
}
.ant-upload-hint {
  color: @yn-disabled-color !important;
  font-weight: 400;
}
</style>

<style lang="less">
.journal-pop-other {
  z-index: 99999;
}
</style>
