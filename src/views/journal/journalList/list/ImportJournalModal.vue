<template>
  <yn-modal
    :visible="visible"
    title="批量导入"
    okText="确定"
    cancelText="取消"
    width="31.625rem"
    class="import-Journal-modal"
    @cancel="cancelEvent"
  >
    <div class="download" @click="() => (tempVisible = true)">下载模板</div>
    <yn-upload-dragger
      :class="{ uploaded: hasFile }"
      :fileList="fileList"
      :disabled="hasFile"
      name="journalFile"
      :multiple="false"
      :remove="handlerDelete"
      :beforeUpload="beforeUpload"
    >
      <p :class="['ant-upload-drag-icon upload-icon', { disabled: hasFile }]">
        <yn-icon type="inbox" />
      </p>
      <p :class="['upload-text', { disabled: hasFile }]">
        {{ $data.$uploadMessage[status] }}
      </p>
      <SvgIcon :isIconBtn="false" type="icon-file-Excel" class="uploadIcon" />
      <SvgIcon
        type="icon-shanchuicon"
        class="deleteIcons"
        @click.native="handlerDelete"
      />
    </yn-upload-dragger>
    <yn-form
      :form="addForm"
      :colon="false"
      v-bind="{
        labelCol: { span: 4 },
        wrapperCol: { span: 20 }
      }"
    >
      <yn-form-item label="识别成员" :colon="false">
        <yn-radio-group v-decorator="['importType']">
          <yn-radio class="item" :value="0">名称</yn-radio>
          <yn-radio class="item" :value="1">唯一标识</yn-radio>
        </yn-radio-group>
      </yn-form-item>
    </yn-form>
    <ExportTempModal :visible.sync="tempVisible" />
    <template slot="footer">
      <yn-button key="back" @click="cancelEvent">取消</yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="loading"
        :disabled="loading"
        @click="okEvent"
      >
        确定
      </yn-button>
    </template>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-upload-dragger/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
// import journalService from "@/services/journal";
import journalService from "@/services/journal";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import { confirm } from "../exconfirm";
import { isFileType, polling, downloadFile } from "@/utils/common";
import ExportTempModal from "./ExportTempModal";
import SvgIcon from "@/components/ui/SvgIcon.vue";
import { JOURNAL_LIST_PATH } from "@/constant/common";
export default {
  components: { ExportTempModal, SvgIcon },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      tempVisible: false,
      fileList: [],
      $uploadMessage: {
        uploaded: "请先删除已上传的文件，再将文件拖拽或选择文件上传",
        unuploaded: "单击或拖动Excel文件到此区域上传"
      },
      loading: false,
      addForm: this.$form.createForm(this, "addForm"),
      action: ""
    };
  },
  computed: {
    status() {
      return this.hasFile ? "uploaded" : "unuploaded";
    },
    hasFile: {
      get() {
        return this.fileList.length > 0;
      },
      set(value) {
        return value;
      }
    }
  },
  watch: {
    visible: {
      handler(value) {
        if (value) {
          this.$nextTick(() => {
            this.addForm.setFieldsValue({ importType: 0 });
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    async getTaskResult(id) {
      return journalService("getTaskResult", id).then(res => {
        if (res.data.importStatus !== "RUNNING") {
          return res;
        }
      });
    },
    cancelEvent() {
      this.addForm.resetFields();
      this.$emit("update:visible", false);
      this.fileList = [];
    },
    beforeUpload(file, fileList) {
      if (!isFileType(file.name, /(xlsx|xls)$/)) {
        UiUtils.errorMessage("请上传正确的格式");
        return false;
      }
      const hasFile = this.hasFile;
      if (!hasFile) {
        this.fileList = fileList;
      }
      return false;
    },
    okEvent() {
      this.addForm.validateFields((err, value) => {
        if (!err) {
          if (this.fileList.length === 0) {
            UiUtils.errorMessage("请上传附件");
            return;
          }
          this.loading = true;
          const formData = new FormData();
          formData.append("journalFile", this.fileList[0]);
          formData.append("importType", value["importType"]);
          journalService("importJournals", formData)
            .then(res => {
              polling(this.getTaskResult, 1000, res.data.data).then(resinfo => {
                this.handlerConfirm(resinfo, res.data.data);
                this.$emit("ok", "import");
                this.cancelEvent();
                this.loading = false;
              });
            })
            .catch(() => {
              UiUtils.errorMessage("请重新上传附件");
              this.loading = false;
            });
        }
      });
    },
    handlerDelete() {
      this.fileList = [];
    },
    handlerDownload(taskId, successNum) {
      journalService("downloadImportResult", taskId).then(res => {
        if (res.data) {
          downloadFile(res);
          UiUtils.successMessage("失败清单下载成功");
        }
      });
      if (successNum === 0) {
        this._importmodel.destroy();
      }
    },
    openList(journalIds, taskId, errList) {
      this.newtabMixin({
        id: taskId,
        router: "journalList",
        title: "日记账列表导入成功清单",
        uri: JOURNAL_LIST_PATH,
        params: {
          journalIds: journalIds || [],
          type: "success"
        },
        newTabParams: {
          journalIds: journalIds || [],
          type: "success"
        }
      });
      if (errList === 0) {
        this._importmodel.destroy();
      }
    },
    // ----------子方法拆分------------
    handlerConfirm(resinfo, taskId) {
      const {
        data: { resultInfo }
      } = resinfo;
      const arr = resultInfo.split(",");
      const successNum = parseInt(arr[0]);
      const errorNum = parseInt(arr[1]);
      this.addModalMixin(() => {
        this._importmodel = confirm({
          title: "操作成功",
          content: h => (
            <div>
              导入日记账总计 {successNum + errorNum} 条，成功 {successNum}{" "}
              条，失败 {errorNum} 条。
            </div>
          ),
          button: h =>
            this.vnodeButtonRender(successNum, errorNum, taskId, arr.slice(2)),
          type: "success"
        });
        return this._importmodel;
      }, this.data);
    },
    vnodeButtonRender(successNum, errorNum, taskId, journalIds) {
      return (
        <div>
          <yn-button
            class="exportbtn"
            onClick={() => {
              this._importmodel.destroy();
            }}
          >
            关闭
          </yn-button>
          {successNum > 0 && (
            <yn-button
              class="exportbtn"
              type="primary"
              onClick={() => this.openList(journalIds, taskId, errorNum)}
            >
              查询成功清单
            </yn-button>
          )}
          {errorNum > 0 && (
            <yn-button
              class="exportbtn"
              type="primary"
              onClick={() => this.handlerDownload(taskId, successNum)}
            >
              下载失败清单
            </yn-button>
          )}
        </div>
      );
    }
  }
};
</script>

<style lang="less" scoped>
.import-Journal-modal {
  font-family: PingFangSC-Regular;
  font-size: @rem14;
}
.upload-text {
  text-align: center;
  height: @rem24;
  font-weight: 400;
  line-height: @rem24;
  color: @yn-text-color;
}
.upload-icon.disabled {
  /deep/ .anticon {
    color: @yn-disabled-color !important;
  }
}
.upload-text.disabled {
  color: @yn-disabled-color;
}
.download {
  height: @rem22;
  margin-bottom: @rem8;
  color: @yn-chart-1;
  text-align: right;
  line-height: @rem22;
  font-weight: 400;
  cursor: pointer;
}
.exportbtn {
  margin-left: @rem8;
}
/deep/ .ant-upload-list-item {
  height: @rem36;
  background: @yn-table-header-bg;
  line-height: @rem36;
}
.uploadIcon,
.deleteIcon,
.deleteIcons {
  display: none;
}
.uploaded .uploadIcon {
  position: absolute;
  display: inline-block;
  width: @rem20;
  height: @rem20;
  left: @rem8;
  bottom: -@rem36;
  z-index: 10;
}
.uploaded .deleteIcons {
  position: absolute;
  display: inline-block;
  // width: @rem16;
  // height: @rem16;
  bottom: -@rem42;
  z-index: 10;
  right: @rem8;
  cursor: pointer;
  // /deep/ .svg-icon {
  //   vertical-align: unset;
  // }
}
/deep/ .ant-upload-list-item-card-actions {
  display: none;
}

/deep/ .ant-upload-list-item-name {
  position: relative;
  margin-left: @rem12;
  padding-right: @rem32;
}
/deep/ .ant-form-item:last-of-type {
  margin-bottom: 0;
}
/deep/ .anticon-paper-clip {
  width: 0;
  height: 0;
  top: @rem8;
  svg {
    display: none;
  }
}
</style>
