<template>
  <yn-modal
    class="add-Journal-modal"
    :visible="visible"
    title="新增日记账"
    @cancel="cancelEvent"
  >
    <yn-form
      :form="addForm"
      v-bind="{
        labelCol: { span: 6 },
        wrapperCol: { span: 16 }
      }"
    >
      <yn-form-item label="日记账名称" :colon="false">
        <yn-input
          v-decorator="[
            'journalName',
            {
              rules: [
                { required: true, message: '请输入日记账名称' },
                {
                  max: 64,
                  message: '字段长度超过64位限制，请检查。'
                }
              ]
            }
          ]"
          placeholder="请输入"
        />
      </yn-form-item>
      <yn-form-item label="日记账说明" :colon="false">
        <yn-textarea
          v-decorator="[
            'desc',
            {
              initialValue: '',
              rules: [{ required: false, max: 50, message: '输入长度超出限制' }]
            }
          ]"
          placeholder="请输入"
          :showCount="false"
          :autoSize="{ minRows: 3, maxRows: 8 }"
          @change="onChange"
        />
        <div class="text-count">{{ descriptionLen }}/50</div>
      </yn-form-item>
      <yn-form-item label="日记账模板" :colon="false">
        <yn-select-tree
          ref="templateRef"
          v-decorator="[
            'templateId',
            { rules: [{ required: true, message: '请选择日记账模板' }] }
          ]"
          searchMode="single"
          :allowClear="true"
          :nonleafselectable="false"
          :datasource="options"
          placeholder="请选择"
          @customSearch="customSearch"
          @dropdownVisibleChange="close"
          @change="handleChange"
        />
      </yn-form-item>
    </yn-form>
    <template slot="footer">
      <yn-button key="back" @click="cancelEvent">取消</yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="loading"
        :disabled="loading"
        @click="okEvent"
      >
        保存
      </yn-button>
    </template>
  </yn-modal>
</template>
<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-option/";
import "yn-p1/libs/components/yn-textarea/";
import journalService from "@/services/journal";
import { setDataKey2key, filterTree } from "@/utils/common";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    templeteData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      options: [],
      addForm: this.$form.createForm(this, "addForm"),
      descriptionLen: 0
    };
  },
  watch: {
    visible: {
      handler(value) {
        if (!value) return;
        const {templeteData} = this;
        if (!templeteData.length) {
          journalService("queryAllTemplate").then(res => {
            const data = setDataKey2key(
              res.data,
              ["templateId", "name", "subNodes"],
              ["key", "label", "children"]
            );
            this.filterGroup(data);
            this._optionsCache = data;
            this.options = data;
          });
        } else {
          this.options = templeteData;
          this._optionsCache = templeteData;
        }
      },
      immediate: true
    }
  },
  methods: {
    customSearch(event) {
      const { searchValue: word } = event;
      if (!word) {
        this.options = this._optionsCache;
        return;
      }
      this.options = filterTree(this._optionsCache, "label", word);
    },
    close(open) {
      if (!open) {
        this.options = this._optionsCache;
      }
    },
    cancelEvent() {
      this.addForm.resetFields();
      this.$emit("update:visible", false);
    },
    filterGroup(data) {
      for (const item of data) {
        item.key || (item.key = new Date().valueOf());
        if (item.isLeaf) {
          delete item.children;
        } else {
          this.filterGroup(item.children);
        }
      }
    },
    okEvent() {
      this.addForm.validateFields(async(err, value) => {
        if (!err) {
          this.loading = true;
          const {addBefore} = this.$attrs;
          let addBeforeResult = true;
          if (addBefore) {
            const templateName = this.$refs.templateRef.valueMap[value.templateId].label;
            addBeforeResult = await this.$attrs.addBefore({...value, templateName });
          }
          if (!addBeforeResult) {
            this.loading = false;
            return;
          }
          journalService("generateJournalId", {
            ...value
          }).then(res => {
            if (res.data.success) {
              this.$emit("ok", "add", { ...value, journalId: res.data.data });
              this.cancelEvent();
            }
            this.loading = false;
          });
        }
      });
    },
    onChange(e) {
      this.descriptionLen = e.target.value.length;
    },
    handleChange(key, selectItems) {

    }
  }
};
</script>

<style lang="less" scoped>
.add-Journal-modal {
  // font-family: PingFangSC-Medium;
  /deep/ .ant-modal-title {
    font-size: @rem16;
    color: @yn-text-color-secondary;
    font-weight: 500;
  }
  /deep/ .ant-form-item:last-of-type {
    margin-bottom: 0;
  }
  /deep/ .ant-form-item-label {
    color: @yn-text-color-secondary;
  }
  .text-count {
    height: @rem22;
    line-height: @rem22;
    position: absolute;
    top: -@rem10;
    right: @rem10;
    color: @yn-border-color-base;
  }
}
</style>
