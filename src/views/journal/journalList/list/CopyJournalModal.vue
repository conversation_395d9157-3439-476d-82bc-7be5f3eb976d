<template>
  <yn-modal :visible="visible" title="复制日记账" @cancel="hideModal">
    <yn-form
      :form="copyForm"
      class="copyForms"
      v-bind="{
        labelCol: { span: 3 },
        wrapperCol: { span: 21 }
      }"
    >
      <template v-for="(item, index) in selectGroup">
        <yn-form-item
          v-if="index % 2 === 0"
          :key="index"
          :label="item.dimName"
          class="dimName"
        />
        <yn-form-item :key="item.key" :label="item.label" :colon="false">
          <component
            :is="item.component"
            v-decorator="[
              item.key,
              { rules: [{ required: item.required, message: item.message }] }
            ]"
            :itemexpandable="itemExpandable"
            v-bind="item.props"
          />
          <yn-checkbox v-if="showCrossYear(index)" v-decorator="['crossCarryForward']">跨年结转</yn-checkbox>
        </yn-form-item>
      </template>
    </yn-form>
    <template slot="footer">
      <yn-button key="back" @click="hideModal">取消</yn-button>
      <yn-button
        key="submit"
        type="primary"
        :loading="loading"
        :disabled="loading"
        @click="handlerOk"
      >
        确定
      </yn-button>
    </template>
  </yn-modal>
</template>

<script>
import "yn-p1/libs/components/yn-modal/";
import YnIconSvg from "yn-p1/libs/components/yn-icon/yn-icon-svg";
import journalService from "@/services/journal";
import { setDataKey2key } from "@/utils/common";
import copyMixin from "../mixin/copyMixin";
import omit from "lodash/omit";
// const TABNAME = "journalDetail";
export default {
  components: { YnIconSvg },
  mixins: [copyMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      copyForm: this.$form.createForm(this, "copyForm"),
      loading: false,
      selectGroup: [
        {
          label: "从",
          key: "fromversion",
          dimName: "版本",
          component: "yn-select-tree",
          props: {
            placeholder: "请选择",
            allowClear: true,
            disabled: true,
            nonleafselectable: false,
            searchMode: "single",
            datasource: []
          }
        },
        {
          label: "到",
          key: "versionList",
          dimCode: "Version",
          component: "yn-select-tree",
          message: "请选择版本",
          required: true,
          props: {
            multiple: true,
            placeholder: "请选择",
            allowClear: true,
            nonleafselectable: false,
            searchMode: "single",
            datasource: []
          }
        },
        {
          label: "从",
          dimName: "年",
          key: "fromyear",
          component: "yn-select-tree",
          props: {
            placeholder: "请选择",
            allowClear: true,
            disabled: true,
            nonleafselectable: false,
            searchMode: "single",
            datasource: []
          }
        },
        {
          label: "到",
          key: "yearList",
          message: "请选择年份",
          required: true,
          dimCode: "Year",
          component: "yn-select-tree",
          props: {
            multiple: true,
            placeholder: "请选择年，如 2022 或 2022年",
            allowClear: true,
            nonleafselectable: false,
            searchMode: "single",
            datasource: []
          }
        },
        {
          label: "从",
          dimName: "期间",
          key: "fromperiod",
          component: "yn-select-tree",
          props: {
            placeholder: "请选择",
            allowClear: true,
            disabled: true,
            nonleafselectable: false,
            searchMode: "single",
            datasource: []
          }
        },
        {
          label: "到",
          key: "periodList",
          dimCode: "Period",
          component: "yn-select-tree",
          message: "请选择期间",
          required: true,
          props: {
            multiple: true,
            placeholder: "请选择月，如 1月 或 Jan",
            allowClear: true,
            nonleafselectable: false,
            searchMode: "single",
            datasource: []
          }
        }
      ],
      mapYearTreeData: {}
    };
  },
  computed: {
    showCrossYear() {
      return index => {
        if (index !== 3) return false;
        const currYearVal = this.copyForm ? this.copyForm.getFieldValue("yearList") || [] : [];
        if (!currYearVal.length) { return false; }
        if (currYearVal.length > 1) { return true; }
        const tempTreeNode = this.mapYearTreeData[this.data.year] || {};
        return currYearVal[0] !== tempTreeNode.id;
      };
    }
  },
  watch: {
    data: {
      handler() {
        this.setForm();
      }
    }
  },
  activated() {
    this.setForm();
  },
  methods: {
    hideModal() {
      this.loading = false;
      this.$emit("update:visible", false);
      this.copyForm.resetFields();
    },
    setForm() {
      this.copyForm.setFieldsValue({
        fromyear: this.data.year,
        fromversion: this.data.version,
        fromperiod: this.data.period
      });
      this.getResource();
    },
    getResource() {
      this.selectGroup.forEach((item, index) => {
        if (index % 2 !== 0) {
          const { dimCode } = item;
          const params = {
            dimCode,
            needFormat: false
          };
          if (["Year", "Period"].includes(dimCode)) {
            params.needFormat = true;
          }
          journalService("getDimMembersTree", params).then(res => {
            const data = setDataKey2key(
              res.data,
              ["id", "name"],
              ["key", "label"]
            );
            if (item.dimCode === "Year") {
              this.setMapYearTreeData(res.data);
            }
            this.$set(item.props, "datasource", data);
          });
        }
      });
    },
    setMapYearTreeData(data) {
      const mapObj = {};
      const loop = data => {
        data.forEach(item => {
          const { name, children } = item;
          mapObj[name] = item;
          if (children && children.length) {
            loop(children);
          }
        });
      };
      loop(data);
      this.$set(this, "mapYearTreeData", mapObj);
    },
    formatTree(trees) {
      trees.forEach(element => {
        element.label = element.name;
        element.key = element.id;
        if (element.children) {
          this.formatTree(element.children);
        }
      });
    },
    handlerOk() {
      this.copyForm.validateFields((err, value) => {
        if (!err) {
          this.loading = true;
          const params = omit(value, "fromversion", "fromyear", "fromperiod");
          journalService("copyJournal", {
            ...params,
            journalIdList: [this.data.journalId]
          }).then(res => {
            this.copyComplteMixin(res);
          }).finally(() => {
            this.loading = false;
          });
        }
      });
    },
    itemExpandable(item) {
      const { isLeaf } = item;
      return !isLeaf;
    }
  }
};
</script>

<style lang="less" scoped>
.dimName {
  height: @rem20;
  font-size: @rem14;
  color: @yn-text-color;
  font-weight: 600;
}
.copyForms /deep/ .ant-form-explain {
  margin-top: @rem4;
}
/deep/ .ant-form-item {
  margin-bottom: @rem12;
}
/deep/ .ant-form-item:last-of-type {
  margin-bottom: 0;
}
</style>
