import UiUtils from "yn-p1/libs/utils/UiUtils";
import { COPY_CHECK_ERROR_TYPE_TITLE_WORD } from "../../constant";
const OK_TEXT = "知道了";
const TABNAME = "journalDetail";
const TAB_URI = "/journal/detail";
const BATCH_COPY_TITLE = "批量复制完成";
export default {
  methods: {
    copyComplteMixin(res, copyType) {
      const { checkAccountResult } = res.data;
      const { checkCode = 0, checkDesc } = checkAccountResult || {};
      if (checkCode === 0) {
        this.hideModal();
        this.$emit("ok", "copy", copyType);
        this.copyConfirmMixin(res);
      } else {
        UiUtils.warning({
          title: COPY_CHECK_ERROR_TYPE_TITLE_WORD[checkCode],
          content: checkDesc,
          okText: OK_TEXT
        });
      }
    },
    copyConfirmMixin(res) {
      this.addModalMixin(() => {
        return UiUtils.warning({
          title: BATCH_COPY_TITLE,
          class: "jsx-message",
          content: h => this.vnodeRenderCCMixin(res),
          type: "warning",
          okText: OK_TEXT
        });
      });
    },
    vnodeRenderCCMixin(res) {
      const {
        data: { successCnt, failedCount, successList }
      } = res;
      return (
        <div>
          <div class="exconfirm-main">
                  批量复制成功 <span> {successCnt}</span> 条，失败<span class="exconfirm-main-error">{failedCount}</span>条日记账
          </div>
          <div class="exconfirm-des">
              以下是复制成功日记账，点击“日记账”打开详情
          </div>
          <ul class="exconfirm-list">
            {successList && successList.map(item => (
              <li
                style="cursor: pointer"
                key={item.objectId}
                onClick={() => {
                  this.newtabMixin({
                    id: item.objectId,
                    title: item.journalName,
                    uri: TAB_URI,
                    router: TABNAME, // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
                    params: {
                      journalId: item.objectId
                    },
                    newTabParams: {}
                  });
                }}
              >
                {item.journalName}
              </li>
            ))}
          </ul>
        </div>
      );
    }
  }
};
