<template>
  <yn-spin class="spinning detail-container" :spinning="spinning">
    <section class="overview">
      <div class="overview-main">
        <span v-show="!isEditName" class="overview-title">{{
          datainfo.journalName
        }}</span>
        <yn-input
          v-show="isEditName && isEdit"
          ref="editName"
          v-model="datainfo.journalName"
          class="overview-title textmode"
          placeholder="请输入日记账名称"
          :allowClear="false"
          @change="handlerBaseInfo"
          @blur="handlerBlurName"
        />
        <yn-tooltip title="编辑日记账名称">
          <yn-icon-button
            v-show="!isEditName && isEdit && !spinning"
            type="edit"
            @click.native="handlerEditName"
          />
        </yn-tooltip>
        <span v-show="!isEditName" :class="['overview-status', status]">{{
          POSTSTATUS[datainfo.postStatus]
        }}</span>
      </div>
      <yn-alert
        v-if="status === 'error'"
        ref="alertRef"
        type="error"
        :showIcon="true"
        :closable="true"
        class="overview-tip"
      >
        <span slot="message">
          {{ datainfo.postingReason }}
        </span>
        <yn-icon slot="icon" type="close-circle" class="closecircle" />
      </yn-alert>
      <div class="overview-info">
        <div class="overview-infomain">
          <span class="codename">编码</span>
          <span class="codenumber">{{ datainfo.journalCode }}</span>
          <yn-divider type="vertical" class="vertical" />
          <span v-show="!isEditDis" class="discription">{{
            datainfo.desc || "暂无说明"
          }}</span>
          <yn-input
            v-show="isEditDis && isEdit"
            ref="editDis"
            v-model="datainfo.desc"
            class="discription textmode"
            placeholder="添加日记账描述信息"
            :allowClear="false"
            @change="handlerBaseInfo"
            @blur="handlerBlurDis"
          />
          <yn-tooltip title="点击添加日记账描述信息">
            <yn-icon-button
              v-show="!isEditDis && isEdit && !spinning"
              type="edit"
              @click.native="handlerEditDis"
            />
          </yn-tooltip>
        </div>
        <div class="overview-others">
          <span>{{ datainfo.updateBy }}</span>
          <span>{{ datainfo.updateDate }}</span>
          <span v-show="datainfo.updateBy">修改</span>
        </div>
      </div>
    </section>
    <section class="contentinfo">
      <section class="baseinfo">
        <h4 class="blocktitle">基础信息</h4>
        <yn-form
          :form="addForm"
          class="baseinfo-form"
          v-bind="{
            labelCol: { span: 7 },
            wrapperCol: { span: 17 }
          }"
        >
          <yn-form-item
            v-for="item in baseinfo.base"
            :key="item.key"
            :label="item.label"
            :colon="false"
            :class="['baseinfo-form-item', { isedit: isEdit }]"
          >
            <transition name="myfade">
              <div v-if="!isEdit" class="form-value">
                {{ readOnly[item.key] }}
              </div>
              <component
                :is="item.component"
                v-else
                v-decorator="[
                  item.key,
                  {
                    rules: [{ required: item.required, message: '请选择' }],
                    initialValue: undefined
                  }
                ]"
                class="form-value"
                v-bind="item.props"
                v-on="item.events"
                @change="handlerFormChange"
              />
            </transition>
          </yn-form-item>
          <em v-for="i in baseinfo.base.length" :key="i"></em>
          <yn-divider :class="['formdivider', { isedit: isEdit }]" />
          <yn-form-item
            v-for="item in baseinfo.extend"
            v-show="item.show"
            :key="item.key"
            :label="item.label"
            :colon="false"
            :class="['baseinfo-form-item', { isedit: isEdit }]"
          >
            <transition name="myfade">
              <div
                v-if="!isEdit && item.key !== 'journalAttachmentList'"
                class="form-value"
              >
                {{ readOnly[item.key] }}
              </div>
              <template v-else>
                <component
                  :is="item.component"
                  v-if="item.key !== 'journalAttachmentList'"
                  v-decorator="[
                    item.key,
                    { rules: [{ required: item.required, message: '请选择' }] }
                  ]"
                  class="form-value"
                  v-bind="item.props"
                  v-on="item.events"
                  @change="handlerFormChange()"
                />
                <yn-attachment
                  v-else
                  v-model="files"
                  class="form-value"
                  v-bind="baseinfo.extend.journalAttachmentList.props"
                  @change="baseinfo.extend.journalAttachmentList.events.change"
                />
              </template>
            </transition>
          </yn-form-item>
          <em
            v-for="i in baseinfo.base.length"
            :key="i + baseinfo.base.length"
          ></em>
        </yn-form>
      </section>
      <section class="detailinfo">
        <h4 class="blocktitle">明细信息</h4>
        <details-table
          ref="mytable"
          :isEdit="isEdit"
          :gridData="data"
          :columnsInfo="columns"
          :dimInfo="dimmemberMap"
          :summary.sync="summary"
          @setBeforeLeave="setBeforeLeave"
        />
      </section>
    </section>
    <section class="summary">
      <div class="summary-date">
        <span
          v-for="(item, key) in summary"
          :key="key"
          class="summary-date-item"
        >
          <span class="summary-item-key">{{ item.label }}</span>
          <span
            :title="item.value"
            :class="[
              'summary-item-value',
              { 'number-marker-error': hasError(item, key) }
            ]"
          >
            <template v-if="isEdit">{{
              item.value | toFinance(2, true)
            }}</template>
            <template v-else>{{ item.value | toFinance }}</template>
          </span>
        </span>
      </div>
      <div v-show="isBaseOk && showAction" class="summary-operate">
        <yn-button v-if="isEdit" :disabled="isWaiting" @click="handlerDelete">
          删除
        </yn-button>
        <yn-button v-if="isEdit" :disabled="isWaiting" @mousedown="handlerSave">
          保存
        </yn-button>
        <yn-button
          v-if="isEdit"
          type="primary"
          :disabled="isWaiting"
          @click="postingJournal"
        >
          过账
        </yn-button>
        <yn-button
          v-if="!isEdit"
          :disabled="isWaiting"
          @click="cancelPostingJournal"
        >
          取消过账
        </yn-button>
      </div>
    </section>
  </yn-spin>
</template>
<script>
import "yn-p1/libs/components/yn-tag/";
import "yn-p1/libs/components/yn-icon/";
import YnIconSvg from "yn-p1/libs/components/yn-icon/yn-icon-svg";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-radio/";
import "yn-p1/libs/components/yn-radio-group/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-alert/";
import "yn-p1/libs/components/yn-attachment/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import commonService from "@/services/common";
import api from "@/services/api";
import DetailsTable from "./detailsTable.vue";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
import { JOURNAL_LIST_PATH } from "@/constant/common";
import { filterTree, setDataKey2key } from "@/utils/common.js";
import journalService from "@/services/journal";
import _uniqueId from "lodash/uniqueId";
import _debounce from "lodash/debounce";
// import _capitalize from "lodash/capitalize";
import "yn-p1/libs/components/yn-icon-button/";
import { mapMutations } from "vuex";
import defaultParams from "@/mixin/defaultParams";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { APPS } from "@/config/SETUP";
import checkSize from "@/views/process/control/item/checkSize";
// 需要特殊转换字段（后台部分动态字段无法平铺，前台需要动态定制处理的字段）
const DIMFIELDS = ["period", "version", "year"]; // 嵌套大小写不一致根字段, 且无关联id, 文本直接显示
const NORMALFIELD = [
  "continueDuration",
  "balanceType",
  "continuedYear",
  "continuedJournal"
]; // 原生根字段

// journalId to label map
let membersCache = {};
let dimIds = {};
let dimCodes = {};
const STATUSMAP = {
  POSTED: "success",
  POSTING_FAILED: "error",
  NOT_POSTED: "unresolve",
  POSTING: "resolving"
};
const POSTSTATUS = Object.freeze({
  POSTED: "已过帐",
  POSTING_FAILED: "过账失败",
  NOT_POSTED: "未过账",
  POSTING: "过帐中"
});
export default {
  name: "JournalListDetail",
  components: { YnIconSvg, AsynSelectDimMember, DetailsTable },
  mixins: [defaultParams, checkSize],
  data() {
    return {
      pov: {},
      POSTSTATUS: POSTSTATUS,
      journalId: "",
      isWaiting: false,
      spinning: false,
      isEditDis: false,
      isEditName: false,
      datasourceMap: {},
      dimmemberMap: {},
      isBaseOk: false,
      datainfo: {},
      readOnly: {},
      writeOnly: {},
      data: [],
      columns: [
        {
          title: "序号",
          width: 70,
          dataIndex: "rowNum"
        },
        {
          name: "借方",
          dataIndex: "debit",
          required: true,
          finance: true,
          width: 242,
          message: "必填项为空或不合法",
          scopedSlots: {
            customRender: "debit",
            title: "debitTitle"
          },
          component: "yn-input",
          props: {
            placeholder: "请输入",
            allowClear: false
          }
        },
        {
          name: "贷方",
          dataIndex: "credit",
          required: true,
          finance: true,
          width: 242,
          message: "必填项为空或不合法",
          scopedSlots: {
            customRender: "credit",
            title: "creditTitle"
          },
          component: "yn-input",
          props: {
            placeholder: "请输入",
            allowClear: false
          }
        },
        {
          title: "备注",
          width: 242,
          dataIndex: "remark",
          limit: 500,
          message: "最大长度不能超过500",
          scopedSlots: {
            customRender: "remark",
            title: "remarkTitle"
          },
          component: "yn-input",
          props: {
            placeholder: "请输入",
            allowClear: true
          }
        }
      ],
      editCol: {
        title: "操作",
        width: 65,
        fixed: "right",
        key: "action",
        dataIndex: "action",
        scopedSlots: {
          customRender: "action"
        }
      },
      addForm: null,
      summary: {
        difference: {
          label: "差值",
          value: 0
        },
        totalDebit: {
          label: "借方合计",
          value: 0
        },
        totalCredit: {
          label: "贷方合计",
          value: 0
        }
      },
      files: [],
      baseinfo: {
        base: [],
        extend: {
          balanceType: {
            label: "平衡类型",
            key: "balanceType",
            show: true,
            required: true,
            component: "yn-select-tree",
            props: {
              placeholder: "请选择",
              allowClear: true,
              nonleafselectable: false,
              searchMode: "single",
              datasource: [
                {
                  label: "按组织平衡",
                  key: "ENTITY_BALANCE"
                },
                {
                  label: "按合并组平衡",
                  key: "SCOPE_BALANCE"
                },
                {
                  label: "按日记账平衡",
                  key: "JOURNAL_BALANCE"
                },
                {
                  label: "不平衡",
                  key: "NO_BALANCE"
                }
              ]
            }
          },
          continuedJournal: {
            label: "日记账类型",
            component: "yn-select-tree",
            show: true,
            required: true,
            key: "continuedJournal",
            props: {
              placeholder: "请选择",
              searchMode: "single",
              allowClear: false,
              nonleafselectable: false,
              datasource: [
                {
                  label: "非持续性日记账",
                  key: "0"
                },
                {
                  label: "普通持续性日记账",
                  key: "1"
                },
                {
                  label: "跨年结转持续性日记账",
                  key: "2"
                }
              ]
            }
          },
          continuedYear: {
            label: "持续年",
            key: "continuedYear",
            show: false,
            required: true,
            component: "yn-select-tree",
            events: {
              dropdownVisibleChange: open => {
                if (!open) {
                  this.$set(
                    this.baseinfo.extend.continuedYear.props,
                    "datasource",
                    this.datasourceMap["continuedYear"]
                  );
                }
              },
              customSearch: event => {
                const { searchValue: word } = event;
                if (!word) {
                  this.$set(
                    this.baseinfo.extend.continuedYear.props,
                    "datasource",
                    this.datasourceMap["continuedYear"]
                  );
                  return;
                }
                const datasource = filterTree(
                  this.datasourceMap["continuedYear"],
                  "label",
                  word
                );
                this.$set(
                  this.baseinfo.extend.continuedYear.props,
                  "datasource",
                  datasource
                );
              }
            },
            props: {
              placeholder: "请选择",
              searchMode: "single",
              allowClear: true,
              nonleafselectable: false,
              datasource: []
            }
          },
          continueDuration: {
            label: "持续期间",
            key: "continueDuration",
            show: false,
            required: true,
            component: "yn-select-tree",
            events: {
              dropdownVisibleChange: open => {
                if (!open) {
                  this.$set(
                    this.baseinfo.extend.continueDuration.props,
                    "datasource",
                    this.datasourceMap["continueDuration"]
                  );
                }
              },
              customSearch: event => {
                const { searchValue: word } = event;
                if (!word) {
                  this.$set(
                    this.baseinfo.extend.continueDuration.props,
                    "datasource",
                    this.datasourceMap["continueDuration"]
                  );
                  return;
                }
                const datasource = filterTree(
                  this.datasourceMap["continueDuration"],
                  "label",
                  word
                );
                this.$set(
                  this.baseinfo.extend.continueDuration.props,
                  "datasource",
                  datasource
                );
              }
            },
            props: {
              placeholder: "请选择",
              searchMode: "single",
              allowClear: true,
              nonleafselectable: false,
              datasource: []
            }
          },
          journalAttachmentList: {
            label: "附件",
            key: "journalAttachmentList",
            show: true,
            component: "yn-attachment",
            events: {
              change: info => {
                this.journalFilesRelation(this.files);
              }
            },
            props: {
              allowClear: false,
              placeholder: "请选择",
              multiple: true,
              beforeUpload: file => {
                return new Promise((resolve, reject) => {
                  if (
                    this.mixinValidateOverSize(
                      [...(this.files || []), file],
                      false
                    )
                  ) {
                    reject({
                      uuid: file.uuid,
                      status: "error",
                      file: file,
                      message: "附件大小超过50M"
                    });
                  } else {
                    resolve();
                  }
                });
              },
              accept:
                ".jpg, .png, .txt, .doc, .xlsx, .xls, .bmp, .pdf, .docx, .ppt, .pptx, .zip, .rar",
              attachmentType: "form",
              attachmentConfig: {
                uploadHeaders: {},
                previewHeaders: {},
                uploadUrl: api.uploadAttachment,
                downloadHeaders: {},
                downloadUrl: api.downloadFile,
                batchDownloadUrl: api.batchDownloadFile,
                previewUrl: api.filePreview,
                checkUrl: api.canPreview
              },
              fileName: "我的附件.zip",
              fileTooltip: {
                tooltip: "附件大小不超过50M",
                desc: "附件大小不超过50M"
              }
            }
          }
        }
      },
      tableWrapWidth: 0,
      directSave: false,
      povDimMemberInfo: {} // 表格pov成员信息
    };
  },
  computed: {
    fields() {
      return this.columns.map(item => item.dataIndex);
    },
    status() {
      return STATUSMAP[this.datainfo.postStatus];
    },
    showAction() {
      return this.datainfo.postStatus !== "POSTING";
    },
    isEdit: {
      get() {
        return (
          this.datainfo &&
          this.datainfo.postStatus !== "POSTED" &&
          this.datainfo.postStatus !== "POSTING"
        );
      }
    }
  },
  async created() {
    this.setUpdateAppInfo();
    await this.mergePov();
    this.init();
    this.getTableWrapWidth();
  },
  beforeDestroy() {
    this.resetCache();
    this.resetData();
  },
  methods: {
    ...mapMutations({
      updateTabTitle: "common/updateTabTitle"
    }),
    setUpdateAppInfo() {
      const fieldsMap = {
        TOKEN: "",
        appId: "",
        MenuId: ""
      };
      Object.keys(fieldsMap).forEach(key => {
        fieldsMap[key] = DsUtils.getSessionStorageItem(key, {
          storagePrefix: APPS.NAME,
          isJson: true
        });
      });
      const info = {
        token: fieldsMap.TOKEN,
        LoginToken: fieldsMap.TOKEN,
        appId: fieldsMap.appId,
        MenuId: fieldsMap.MenuId
      };
      this.$set(
        this.baseinfo.extend.journalAttachmentList.props.attachmentConfig,
        "downloadHeaders",
        info
      );
      this.$set(
        this.baseinfo.extend.journalAttachmentList.props.attachmentConfig,
        "uploadHeaders",
        info
      );
      this.$set(
        this.baseinfo.extend.journalAttachmentList.props.attachmentConfig,
        "previewHeaders",
        info
      );
    },
    async mergePov() {
      // 合并流程和pov值：流程值优先， 无值则使用pov
      const params = this.selfTab
        ? this.params.params
        : this.getTabParamsMixin() || {};
      const data = await this.selectUserPov(); // ps: key 首字母小写
      // pov结构不同，先处理成key, value 对象（拆分reduce， 便于理解）
      const pov = Object.keys(data).reduce((pre, nextKey) => {
        pre[nextKey] = data[nextKey].memberId;
        return pre;
      }, {});
      const parameter = params.pov || pov;
      // 统一结构后转换
      this.pov = Object.keys(parameter).reduce((pre, nextKey) => {
        const lowerCaseKey =
          nextKey.slice(0, 1).toLowerCase() + nextKey.slice(1);
        const upperCaseKey =
          nextKey.slice(0, 1).toUpperCase() + nextKey.slice(1);
        pre[upperCaseKey] = parameter[nextKey] || parameter[lowerCaseKey];
        return pre;
      }, {});
    },
    journalFilesRelation: _debounce(function(files) {
      if (!this.isEdit) {
        journalService("journalFilesRelation", {
          journalId: this.journalId,
          operationType: "UPDATE",
          journalAttachmentList: files
            .filter(item => item.status !== "error" && item.objectId)
            .map(item => ({
              attachmentId: item.objectId,
              attachmentName: item.name,
              fileSize: item.size || 0
            }))
        });
      } else {
        this.setBeforeLeave();
      }
    }, 600),
    getTableWrapWidth() {
      this.$nextTick(() => {
        this.tableWrapWidth = document.getElementsByClassName(
          "detailinfo"
        )[0].clientWidth;
      });
    },
    async drillingJournalBase() {
      const { data: { data = {} } = {} } = await journalService(
        "drillingJournalBase",
        this.journalId
      );
      this.datainfo = data;
      const year = data.baseInfo.filter(item => item.dimCode === year);
      this.getContinuedYearList(year[0] ? year[0].memberId : "");
      this.datainfo._journalName = data.journalName; // 名称缓存
      this.isBaseOk = true;
      this.files = data.journalAttachmentList.map(item => ({
        uuid: item.attachmentId,
        objectId: item.attachmentId,
        name: item.attachmentName,
        size: item.fileSize || 0
      }));
      this.delayDo(() => {
        this.datainfo.baseInfo.map(item => {
          this.setMembersCache(
            item.dimCode,
            item.memberId,
            item.memberRealName
          );
        });
      }, this.isEdit);
      this.delayDo(this.initForm, !this.isEdit);
      this.delayDo(this.setFixedExtend, this.isEdit);
      this.initSummary();
    },
    async queryJournalDims(templateId) {
      let dimInfo = null;
      if (templateId) {
        dimInfo = await journalService(
          "queryJournalDimsByTemplateId",
          templateId
        );
      } else {
        dimInfo = await journalService("queryJournalDims", this.journalId);
      }
      const {
        data: { data: { baseDims = [], detailDims = [] } = {} } = {}
      } = dimInfo;
      // TODO 请求 pov里面维度成员信息
      this.resetData();
      this.initBaseInfo(baseDims);
      this.initTableHeader(detailDims);
      await this.getPovDimMemberInfo(detailDims);
      await this.getExpMember();
      return detailDims;
    },
    async getPovDimMemberInfo(detailDims) {
      const requestArr = [];
      detailDims.forEach(item => {
        const { dimCode, dimId } = item;
        const povVal = this.pov[dimCode];
        if (povVal) {
          requestArr.push(
            commonService("getQueryDimMemberList", {
              dimCode,
              dimMemberId: povVal,
              limit: 10,
              needFilterShared: true,
              needPermission: false,
              needFilterAudittrail: dimCode === "Audittrail",
              offset: 0,
              memberExp: {
                expDtoList: [
                  {
                    dimId: this.getDimCodeToId(dimCode),
                    dimMemberExps: this.dimmemberMap[dimId].members
                  }
                ]
              }
            })
          );
        }
      });
      await Promise.all(requestArr).then(res => {
        res.forEach((item, index) => {
          if (item.data.items && item.data.items.length > 0) {
            const { dimMemberRealName, dimCode, objectId } = item.data.items[0];
            this.povDimMemberInfo[dimCode] = {
              dimMemberRealName,
              dimMemberId: objectId
            };
          } else {
            this.povDimMemberInfo[detailDims[index].dimCode] = {
              dimMemberRealName: "",
              dimMemberId: ""
            };
          }
        });
      });
      this.$set(this, "povDimMemberInfo", this.povDimMemberInfo);
    },
    async drillingJournalContents() {
      const { data: { data = [] } = {} } = await journalService(
        "drillingJournalContents",
        this.journalId
      );
      this.delayDo(() => {
        data.forEach(item => {
          item.journalDimInfo.forEach(dim => {
            this.setMembersCache(dim.dimCode, dim.memberId, dim.memberRealName);
          });
        });
      }, this.isEdit);
      return data;
    },
    async queryDimMembers() {
      // 持续期间和期间接口相同
      const { data } = await journalService("expParsingWithFilter", {
        expDtoList: [
          {
            dimId: this.getDimCodeToId("Period"),
            dimMemberExps: ""
          }
        ]
      });
      this.datasourceMap["continueDuration"] = setDataKey2key(
        data[0].members,
        ["name", "name"],
        ["key", "label"]
      );
      this.$set(
        this.baseinfo.extend.continueDuration.props,
        "datasource",
        this.datasourceMap["continueDuration"]
      );
    },
    // 持续年获取接口
    async getContinuedYearList(id) {
      const { data } = await journalService("getContinuedYearList", id);
      this.datasourceMap["continuedYear"] = this.formatTree(
        data,
        "continuedYear"
      );
      this.$set(
        this.baseinfo.extend.continuedYear.props,
        "datasource",
        this.datasourceMap["continuedYear"]
      );
    },
    async getExpMember(dimmember = this.dimmemberMap) {
      await Promise.all(
        Object.keys(dimmember).map(item => {
          return commonService("getFirstDimMemberList", {
            offset: 0,
            limit: 10,
            needPermission: ["Scope", "Entity"].includes(
              dimmember[item].dimCode
            ),
            needFilterShared: true,
            needFilterAudittrail: dimmember[item].dimCode === "Audittrail",
            memberExp: {
              expDtoList: [
                {
                  dimId: item,
                  dimMemberExps: dimmember[item].members
                }
              ]
            },
            dimCode: dimmember[item].dimCode
          });
        })
      ).then(res => {
        this.setDatasource(res);
      });
    },
    async updateJournalContent() {
      const params = {
        journalId: this.journalId,
        journalContentBoList: this.$refs.mytable.getTobeSaveInfo()
      };
      return journalService("updateJournalContent", params);
    },
    async updateJournal() {
      if ((this.files || []).some(item => item.status === "error")) {
        UiUtils.errorMessage("存在上传失败的文件，请重新上传");
        return Promise.reject(false);
      }
      const tempMap = { ...this.datainfo, baseInfo: [] };
      const formData = this.addForm.getFieldsValue();
      for (const key in formData) {
        if (NORMALFIELD.includes(key)) {
          tempMap[key] = formData[key];
        } else if (DIMFIELDS.includes(key.toLowerCase())) {
          tempMap[key.toLowerCase()] = this.getMembersCache(key, formData[key]);
        } else {
          tempMap.baseInfo.push({
            journalId: this.journalId, // 维度值所对应的日记账Id
            dimId: this.getDimCodeToId(key), // 维度Id
            memberId: formData[key] // 用户所选择的维度成员id
          });
        }
      }
      tempMap.journalAttachmentList = (this.files || []).map(item => {
        return {
          attachmentName: item.name,
          attachmentId: item.objectId,
          fileSize: item.size || 0
        };
      });
      return journalService("updateJournal", tempMap);
    },
    async updateWrap() {
      const tsign = await this.validateRow();
      const fsign = await this.validateForm();
      const isempty = this.datainfo.journalName === "";
      return new Promise((resolve, reject) => {
        if (tsign && fsign && !isempty) {
          if (this.hasSaveEventMixin()) {
            this.updateJournal()
              .then(res => {
                if (res.data.success) {
                  this.updateJournalContent()
                    .then(res => {
                      if (res.data.success) {
                        this.updateTabTitle({
                          id: this.journalId,
                          title: this.datainfo.journalName
                        });
                        this.clearCommonSaveEventsMixin();
                        resolve(true);
                      } else {
                        resolve(false);
                      }
                    })
                    .catch(() => {
                      resolve(false);
                    });
                } else {
                  resolve(false);
                }
              })
              .catch(() => {
                resolve(false);
              });
          } else {
            resolve(true);
          }
        } else {
          reject(false);
        }
      });
    },
    async checkSpecialAccount() {
      return journalService("checkSpecialAccount", [this.journalId]).then(
        res => {
          const { checkCode, checkDesc } = res.data;
          if (checkCode !== 0) {
            UiUtils.warning({
              title: checkCode === 2 ? "科目类型错误" : "缺少科目成员",
              content: checkDesc
            });
            return false;
          } else {
            return true;
          }
        }
      );
    },
    async postingJournal() {
      this.setStatus(true, "isWaiting", "spinning");
      this.updateWrap()
        .then(async sign => {
          if (sign) {
            if (!(await this.checkSpecialAccount())) {
              this.setStatus(false, "isWaiting", "spinning");
              return;
            }
            const {
              data: {
                data: { operationResult },
                success
              }
            } = await journalService("postingJournal", [this.journalId]);
            if (success && operationResult) {
              UiUtils.successMessage("日记账过账成功");
            }
            this.saveAfterUpdateTab();
            await this.reInit();
          } else {
            this.setBeforeLeave();
          }
        })
        .finally(() => {
          this.setStatus(false, "isWaiting", "spinning");
        });
    },
    async cancelPostingJournal() {
      this.setStatus(true, "isWaiting", "spinning");
      const {
        data: {
          data: { operationResult, relationList, failedList },
          success
        }
      } = await journalService("cancelPostingJournal", [this.journalId]);
      if (success && operationResult) {
        await this.reInit();
        if (relationList.length > 0) {
          this.addModalMixin(() => {
            return UiUtils.success({
              title: "取消过账成功",
              class: "jsx-message",
              content: h => (
                <div class="exconfirm-main">
                  <div class="exconfirm-des">
                    当前持续性日记账关联 {relationList.length} 个日记账
                  </div>
                  <div class="exconfirm-list">
                    <li
                      onClick={() => {
                        this.openList(relationList.map(item => item.objectId));
                      }}
                    >
                      查看关联日记账
                    </li>
                  </div>
                </div>
              )
            });
          }, this.params.id);
        } else {
          UiUtils.successMessage("日记账取消过账成功");
        }
      } else {
        this.addModalMixin(() => {
          return UiUtils.error({
            title: "取消过账失败",
            content:
              failedList[0].postingReason ||
              "当前日记账在流程状态中“允许填写表单”为关闭，无法取消过账！"
          });
        }, this.params.id);
      }
      this.setStatus(false, "isWaiting", "spinning");
    },
    async handlerSave() {
      if (document.activeElement.tagName === "INPUT") {
        // 没失去焦点直接点保存
        this.directSave = true;
      }
      if (
        this.datainfo.journalName.length > 64 ||
        this.datainfo.desc.length > 50
      ) {
        this.directSave = false;
        return;
      }
      this.setStatus(true, "isWaiting", "spinning");
      return this.updateWrap()
        .then(async sign => {
          if (sign) {
            await this.reInit();
            this.saveAfterUpdateTab();
            UiUtils.successMessage("日记账保存成功");
            return Promise.resolve(sign);
          } else {
            this.setBeforeLeave();
            return Promise.reject(sign);
          }
        })
        .finally(() => {
          this.directSave = false;
          this.setStatus(false, "isWaiting", "spinning");
        });
    },
    saveAfterUpdateTab() {
      const { journalId, journalName, templateId } = this.datainfo;
      this.newtabMixin({
        id: journalId,
        params: {
          journalId,
          title: journalName,
          isAdd: false,
          templateId: templateId
        }
      });
    },
    async init() {
      this.$refs.alertRef && (this.$refs.alertRef.$data.closed = false);
      let dataParams = this.params ? this.params.params : {};
      if (!this.selfTab) {
        dataParams = this.getTabParamsMixin();
      }
      const {
        isAdd,
        journalId,
        journalName,
        templateId,
        templateName,
        id,
        desc
      } = dataParams;
      this.spinning = true;
      this.journalId = journalId || id;
      this.data = [];
      if (isAdd) {
        this.datainfo = {
          postStatus: "NOT_POSTED", // 用于修改页面的展示状态，默认应该是编辑态
          journalId: journalId,
          journalName: journalName,
          _journalName: journalName,
          templateId: templateId,
          templateName: templateName,
          desc: desc
        };
        new Promise(resolve => {
          const data = this.queryJournalDims(templateId);
          resolve(data);
        })
          .then(detailDims => {
            this.queryDimMembers();
            this.setTbwrite(detailDims, true);
            this.initForm();
            this.setFixedExtend();
            this.$nextTick(() => {
              this.setFormWrite(true);
            });
          })
          .finally(() => {
            this.spinning = false;
            this.isBaseOk = true;
          });
      } else {
        // 有journalId的情况
        Promise.all([
          this.drillingJournalBase(),
          this.queryJournalDims(),
          this.drillingJournalContents()
        ])
          .then(res => {
            this.queryDimMembers();
            this.setFormWrite();
            this.setTbwrite(res[2]);
            // 成员引用功能 初始化时，重新更改tab 名称
            this.updateTabTitle({
              id: this.journalId,
              title: this.datainfo._journalName
            });
          })
          .finally(() => {
            this.spinning = false;
          });
      }
    },
    async reInit() {
      return Promise.all([
        this.drillingJournalBase(),
        this.drillingJournalContents()
      ]).then(res => {
        this.data = [];
        this.setActionCol();
        this.setFormWrite();
        this.setTbwrite(res[1]);
      });
    },
    setDatasource(data) {
      (data || []).forEach(element => {
        const {
          data,
          data: { items }
        } = element;
        if (!Array.isArray(data)) {
          if (!items) return;
          const dimId = items[0] ? items[0].dimId : "";
          this.$set(this.dimmemberMap[dimId].props, "dataInfo", data);
        } else {
          data.map(item => {
            const dimCode = this.getDimIdToCode(item.dimId);
            const data = this.formatTree(item.members || [], dimCode);
            this.$set(this.dimmemberMap[item.dimId].props, "dataInfo", data);
            this.$set(this.dimmemberMap[item.dimId].props, "datasource", data);
          });
        }
      });
    },
    handlerDelete() {
      UiUtils.confirm({
        title: "确认删除当前日记账吗?",
        okText: "确认",
        cancelText: "取消",
        onOk: () => {
          this.setStatus(true, "isWaiting");
          journalService("batchDeleteJournal", [this.journalId])
            .then(res => {
              const { data } = res;
              if (data.success) {
                UiUtils.successMessage("删除日记账成功");
                this.clearCommonSaveEventsMixin();
                // TODO 平台目前 没有能力关闭指定页签
                this.closetabMixin(
                  this.params.params && this.params.params.journalId
                );
              }
            })
            .finally(e => {
              this.setStatus(false, "isWaiting");
            });
        }
      });
    },
    handlerEditDis() {
      this.isEditDis = true;
      if (this.datainfo.desc === "暂无说明") {
        this.datainfo.desc = "";
      }
      this.$nextTick(() => {
        this.$refs.editDis.focus();
      });
    },
    handlerEditName() {
      this.isEditName = true;
      this.$nextTick(() => {
        this.$refs.editName.focus();
      });
    },
    handlerBaseInfo() {
      this.setBeforeLeave();
    },
    handlerBlurDis() {
      if (this.datainfo.desc.length > 50) {
        UiUtils.errorMessage("字段长度超过50位限制，请检查。");
        this.handlerEditDis();
        return;
      }
      this.isEditDis = false;
    },
    handlerBlurName() {
      if (this.datainfo.journalName === "") {
        UiUtils.errorMessage("日记账名称不可为空！");
        this.handlerEditName();
        return;
      } else if (this.datainfo.journalName.length > 64) {
        this.$set(this.datainfo, "journalName", this.datainfo._journalName);
        UiUtils.errorMessage("字段长度超过64位限制，请检查。");
        this.handlerEditName();
        return;
      }
      this.isEditName = false;
    },
    selectedItems(item) {
      if (!item) return;
      this.datainfo[item.dimCode.toLowerCase() + "DbCodeIndex"] =
        item.dbCodeIndex;
    },
    handlerFormChange(value, item) {
      if (item) {
        this.datainfo[item.dimCode.toLowerCase() + "DbCodeIndex"] =
          item.dbCodeIndex;
      }
      this.setBeforeLeave();
    },
    initForm() {
      this.addForm = this.$form.createForm(this, {
        name: "addForm",
        onValuesChange: (props, fields) => {
          if (fields.hasOwnProperty("Year")) {
            this.getContinuedYearList(fields.Year);
          }
          if (fields.hasOwnProperty("continuedJournal")) {
            this.$set(
              this.baseinfo.extend.continuedYear,
              "show",
              fields["continuedJournal"] !== "0"
            );
            this.$set(
              this.baseinfo.extend.continuedYear,
              "required",
              fields["continuedJournal"] !== "0"
            );
            this.$set(
              this.baseinfo.extend.continueDuration,
              "show",
              fields["continuedJournal"] !== "0"
            );
            this.$set(
              this.baseinfo.extend.continueDuration,
              "required",
              fields["continuedJournal"] !== "0"
            );
          }
        }
      });
    },
    initSummary() {
      const summary = this.summary;
      const datainfo = this.datainfo;
      for (const key in summary) {
        summary[key].value = datainfo[key];
      }
    },
    initBaseInfo(baseDims = []) {
      (baseDims || []).forEach(element => {
        const item = {
          label: element.dimMultiName,
          key: element.dimCode,
          component: "asyn-select-dimMember",
          // element.dimCode !== "Audittrail"
          //   ? "asyn-select-dimMember"
          //   : "yn-select-tree",
          required: true,
          props: {
            placeholder: "请选择",
            allowClear: false,
            searchMode: "single",
            nonleafselectable: false,
            checkTabaseType: true,
            needFilterAudittrail: element.dimCode === "Audittrail",
            needPermission: ["Scope", "Entity"].includes(element.dimCode),
            needFilterShared: true,
            dimCode: element.dimCode,
            memberExp: {
              expDtoList: [
                {
                  dimId: element.dimId,
                  dimMemberExps: element.members
                }
              ]
            },
            forceRender: true,
            dataInfo: {},
            datasource: []
          },
          events: {
            changeVal: (values, items) => {
              for (const item of items) {
                const { dimCode, objectId, dimMemberRealName } = item;
                this.setMembersCache(dimCode, objectId, dimMemberRealName);
              }
            },
            selectedItems: items => {
              this.selectedItems(items[0]);
            }
          }
        };
        this.baseinfo.base.push(item);
        this.setDimMap(element.dimId, element.dimCode);
        this.dimmemberMap[element.dimId] = {
          dimCode: element.dimCode,
          members: element.members,
          props: item.props
        };
      });
    },
    initTableHeader(detailDims) {
      this.setActionCol();
      const dyCol = (detailDims || []).map(element => {
        const item = {
          name: element.dimMultiName,
          width: element.width || 242,
          dataIndex: element.dimCode,
          key: element.dimCode,
          required: true,
          message: "必填项为空或不合法",
          scopedSlots: {
            customRender: element.dimCode,
            title: element.dimCode + "Title"
          },
          component: "asyn-select-dimMember",
          // element.dimCode !== "Audittrail"
          //   ? "asyn-select-dimMember"
          //   : "yn-select-tree",
          props: {
            placeholder: "请选择",
            allowClear: false,
            nonleafselectable: false,
            checkTabaseType: true,
            needPermission: true,
            needFilterShared: true,
            needFilterAudittrail: element.dimCode === "Audittrail",
            dimCode: element.dimCode,
            memberExp: {
              expDtoList: [
                {
                  dimId: element.dimId,
                  dimMemberExps: element.members
                }
              ]
            },
            searchMode: "single",
            forceRender: true,
            dataInfo: {},
            datasource: []
          },
          events: {
            changeVal: (values, items) => {
              for (const item of items) {
                const { dimCode, objectId, dimMemberRealName } = item;
                this.setMembersCache(dimCode, objectId, dimMemberRealName);
              }
            }
          }
        };
        this.setDimMap(element.dimId, element.dimCode);
        this.dimmemberMap[element.dimId] = {
          dimCode: element.dimCode,
          members: element.members,
          props: item.props
        };
        return item;
      });
      Array.prototype.splice.apply(this.columns, [1, 0, ...dyCol]);
      const columnW = this.columns.reduce(
        (pre, next) => pre + (next.width || 0),
        0
      );
      if (this.tableWrapWidth > columnW) {
        this.columns.forEach(c => {
          if (c.dataIndex === "credit" || c.dataIndex === "debit") {
            c.width = "";
          }
        });
      }
    },
    setFormRead() {
      for (const field in this.writeOnly) {
        // 设置持续期间显示状态
        if (
          field === "continuedJournal" &&
          this.writeOnly.continuedJournal !== "0"
        ) {
          this.$set(this.baseinfo.extend.continueDuration, "show", true);
          this.$set(this.baseinfo.extend.continuedYear, "show", true);
        }
        this.$set(
          this.readOnly,
          field,
          this.getMembersCache(field, this.writeOnly[field]) ||
            this.writeOnly[field]
        );
      }
    },
    setFormWrite(isAddFlag) {
      const tempMap = this.getDimCode(this.datainfo.baseInfo);
      NORMALFIELD.forEach(item => {
        tempMap[item] = this.datainfo[item];
      });
      this.writeOnly = { ...tempMap };
      this.delayDo(this.setFormRead, this.isEdit);
      if (this.isEdit) {
        if (isAddFlag) {
          this.writeOnly = {
            ...this.writeOnly,
            ...this.pov,
            continuedJournal: "0",
            balanceType: "ENTITY_BALANCE"
          };
        }
        this.addForm && this.addForm.setFieldsValue(this.writeOnly);
      }
    },
    setTbwrite(data = [], isNewFlag) {
      if ((data.length === 0 && this.isEdit) || isNewFlag) {
        const row = this.getEmptyRow();
        this.data.push(row);
        return;
      }

      this.data = data.map((item, index) => {
        const journalDimInfo = this.getDimCode(item.journalDimInfo);
        const dimObj = {
          ...item,
          ...journalDimInfo,
          inView: true,
          rowNum: index + 1,
          uniqueId: _uniqueId("front")
        };
        return dimObj;
      });
    },
    setActionCol() {
      if (this.isEdit) {
        this.editCol && this.columns.push({ ...this.editCol });
        this.editCol = null;
      } else {
        !this.editCol && (this.editCol = this.columns.pop());
      }
    },
    setStatus(status, ...keys) {
      (keys || []).forEach(key => {
        this[key] = status;
      });
    },
    setBeforeLeave() {
      // this.reInit
      this.addCallBackFnMixin(
        this.handlerSave,
        `您要保存对 "${this.datainfo.journalName}" 所做的更改吗`
      );
    },
    setFixedExtend() {
      // 配置在前端的项目，独立处理，只处理一次
      this.baseinfo.extend.balanceType.props.datasource.forEach(item => {
        this.setMembersCache("balanceType", item.key, item.label);
      });
      this.baseinfo.extend.continuedJournal.props.datasource.forEach(item => {
        this.setMembersCache("continuedJournal", item.key, item.label);
      });
    },
    resetData() {
      this.columns = this.$options.data().columns.concat();
      this.editCol = { ...this.$options.data().editCol };
      this.baseinfo.base = this.$options.data().baseinfo.base.concat();
    },
    getEmptyRow() {
      const obj = {};
      // 不是下拉类型的单元格名
      const noDimMemberCell = ["rowNum", "debit", "credit", "remark", "action"];
      const dimCodeMapObj = this.getDimCodeMapDim();
      const journalDimInfo = [];
      this.fields.forEach(item => {
        obj[item] = this.pov[item] || "";
        if (noDimMemberCell.indexOf(item) === -1) {
          const { dimMemberId = "", dimMemberRealName = "" } =
            this.povDimMemberInfo[item] || {};
          dimCodeMapObj[item].memberId = dimMemberId;
          dimCodeMapObj[item].memberRealName = dimMemberRealName;
          journalDimInfo.push(dimCodeMapObj[item]);
        }
      });
      // 新增 属性下拉类型的成员 信息
      obj.journalDimInfo = journalDimInfo;
      obj.contentId = new Date().getTime();

      obj.rowNum = 1;
      obj.inView = false;
      obj.uniqueId = _uniqueId("front");
      return obj;
    },
    getDimCodeMapDim() {
      const obj = {};
      Object.keys(this.dimmemberMap).forEach(key => {
        const { dimCode } = this.dimmemberMap[key] || {};
        obj[dimCode] = {
          dimCode,
          dimId: key,
          memberId: "",
          memberRealName: ""
        };
      });
      return obj;
    },
    openList(journalIds) {
      const id = _uniqueId("PostId");
      this.newtabMixin({
        id,
        title: "日记账列表关联日记账清单",
        router: "journalList",
        uri: JOURNAL_LIST_PATH,
        params: {
          journalIds: journalIds || []
        },
        newTabParams: {
          journalId: journalIds || []
        }
      });
    },
    // --------------- unObserverData getter and setter ------------------------
    // 缓存 dimCode 下 memberId 和 dimMemberRealName 的映射关系, 用于编辑和只读模式回显
    setMembersCache(scope, key, value) {
      membersCache[scope] || (membersCache[scope] = {});
      membersCache[scope][key] = value;
    },
    getMembersCache(scope, key) {
      return membersCache[scope] ? membersCache[scope][key] : "";
    },
    // 缓存 dimid 和 dimCode 的映射关系, 用于获取表单字段
    setDimMap(id, key) {
      dimIds[id] = key;
      dimCodes[key] = id;
    },
    getDimIdToCode(id) {
      return dimIds[id];
    },
    getDimCodeToId(key) {
      return dimCodes[key];
    },
    getDimCode(data) {
      const tempMap = {};
      (data || []).map(item => {
        tempMap[this.getDimIdToCode(item.dimId)] = item.memberId;
      });
      return tempMap;
    },
    resetCache() {
      this.readOnly = {};
      this.writeOnly = {};
      membersCache = {};
      dimIds = {};
      dimCodes = {};
    },
    // -------- status validate ------
    isDisabled(tableItem, record) {
      if (tableItem.dataIndex === "credit" && record.debit) {
        return true;
      } else if (tableItem.dataIndex === "debit" && record.credit) {
        return true;
      } else {
        return false;
      }
    },
    validateRow(records = this.data, column) {
      try {
        this.$refs.mytable.checkTableRequired(true);
        // 强制simpleGrid 刷新，将错误提示显示出来
        this.$refs.mytable.$refs.grid.$refs.simpleGrid.hot.setDataAtCell(
          1,
          0,
          1
        );
      } catch {}
      return new Promise((resolve, reject) => {
        resolve(!Object.keys(this.$refs.mytable.errorInfo).length);
      });
    },
    validateForm() {
      return new Promise((resolve, reject) => {
        this.addForm.validateFields(err => {
          if (err) {
            UiUtils.errorMessage("日记账基础信息存在未填项");
          }
          resolve(!err);
        });
      });
    },
    hasError(item, key) {
      if (key === "difference" && parseFloat(item.value) !== 0) {
        return true;
      }
      return false;
    },
    // ------------ special methods ---------
    // 逻辑延迟渲染
    async delayDo(callback, isDelay) {
      if (isDelay) {
        return new Promise((resolve, reject) => {
          this.$nextTick(() => {
            resolve(callback());
          });
        });
      } else {
        return callback();
      }
    },
    // 接口业务性参数构造(非稳定代码)
    formatTree(trees, key) {
      return trees.filter(element => {
        this.setMembersCache(key, element.id, element.name);
        element.label = element.name;
        element.key = element.id;
        if (element.children) {
          element.children = this.formatTree(element.children, key);
        }
        return !(
          element.scopedSlots &&
          element.scopedSlots.isParent &&
          !element.children
        );
      });
    }
  }
};
</script>
<style lang="less" scoped>
@import "../../../../commonLess/common.less";

.detail-container {
  height: 100%;
  overflow-y: hidden;
  font-family: PingFangSC-Medium;
  font-size: @rem14;
  /deep/ .ant-spin-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .closecircle {
    color: @yn-error-color;
    font-size: 0.9375rem;
  }
  .contentinfo {
    overflow-y: scroll;
    overflow-x: hidden;
    background: @yn-body-background;
    position: relative;
    margin: 0 0.75rem;
    padding: @rem16 0.125rem @rem16 1rem;
    height: 0;
    flex: 1;
  }
  /deep/.ant-form-item-label > label {
    color: @yn-text-color-secondary;
  }
  .baseinfo {
    flex-shrink: 0;
    .baseinfo-form {
      display: flex;
      flex-wrap: wrap;
      margin-right: -5rem;
      .baseinfo-form-item {
        width: 33.3%;
        min-width: 26.25rem;
        flex: 1;
        position: relative;
        padding-right: 5rem;
        margin-bottom: @rem16;
      }
      .baseinfo-form-item.isedit {
        margin-bottom: @rem24;
      }
      & > em {
        width: 33.3%;
        min-width: 26.25rem;
        padding-right: 5rem;
        flex: 1;
      }
      & /deep/ .ant-form-item-control,
      & /deep/ .ant-form-item-control-wrapper {
        height: 100%;
      }
      & /deep/ .ant-form-item-children {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
      }
    }
    .formdivider {
      margin: 0;
      margin-bottom: @rem16;
    }
    .formdivider.isedit {
      margin: 0;
      margin-bottom: @rem24;
    }
  }
  .detailinfo {
    /deep/ .ant-table-fixed-right {
      z-index: 20;
    }
  }
  .blocktitle {
    color: @yn-text-color;
    font-weight: 600;
    margin-bottom: @rem16;
  }
  .overview {
    display: flex;
    flex-direction: column;
    padding: 0.5rem 0.75rem;
    .overview-main {
      display: flex;
      align-items: center;
      height: @rem32;
      width: 100%;
    }
    .overview-title {
      height: @rem32;
      margin-right: @rem8;
      line-height: @rem32;
      font-weight: 600;
      font-size: @rem16;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      caret-color: @yn-primary-color;
      color: @yn-text-color;
    }
    .overview-status {
      display: inline-block;
      padding: 0 @rem8;
      margin-left: @rem24;
      flex-shrink: 0;
      border-radius: @rem4;
      font-size: @rem12;

      font-weight: 400;

      &.success {
        color: @yn-success-color;
        background: @yn-success-bg-color;
      }
      &.error {
        color: @yn-error-color;
        background: @yn-error-bg-color;
      }
      &.unresolve {
        color: @yn-warning-color;
        background: @yn-warning-bg-color;
      }
      &.resolving {
        color: @yn-chart-1;
        background: @yn-background-color-light;
      }
    }

    .overview-info {
      display: flex;
      align-items: center;
      height: @rem32;
      width: 100%;
      color: @yn-label-color;
      .discription {
        height: @rem32;
        line-height: @rem32;
        margin-right: @rem8;
        margin-left: @rem8;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        caret-color: @yn-primary-color;
      }
      .overview-infomain {
        display: flex;
        flex: 1;
        width: 0;
        align-items: center;
        margin-right: @rem20;
        .codename {
          flex-shrink: 0;
          margin-right: @rem8;
        }
        .codenumber {
          margin-right: @rem8;
          color: @yn-text-color-secondary;
        }
      }
    }
    .overview-others {
      margin-left: auto;
      flex-shrink: 0;
      span {
        margin-left: @rem8;
      }
    }
    .unlight {
      color: @yn-label-color;
    }
  }
  .summary {
    height: 3.25rem;
    display: flex;
    flex-shrink: 0;
    margin-top: auto;
    padding: @rem16 1.25rem;
    margin: 0 0.75rem;
    background: @yn-body-background;
    box-shadow: inset 0px 1px 0px 0px @yn-border-color-base;
    .summary-date {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      font-weight: 400;
      max-width: calc(100% - 12.5rem);
      overflow: hidden;
      .summary-date-item {
        display: flex;
        height: @rem24;
        flex: 1;
        line-height: @rem24;
        padding-right: @rem32;
        white-space: nowrap;
      }
      .summary-item-key {
        flex-shrink: 0;
        display: inline-block;
        padding-right: @rem8;
        vertical-align: top;
      }
      .summary-item-value {
        display: inline-block;
        font-size: @rem16;
        vertical-align: top;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        &.haserror {
          color: @yn-error-color;
        }
      }
    }
    .summary-operate {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      flex-wrap: nowrap;
      margin-left: auto;
      .ant-btn {
        margin-left: @rem8;
      }
    }
    .number-marker-error {
      color: @yn-error-color;
    }
  }
  .textmode {
    width: 0;
    display: flex;
    padding: 0;
    border: none !important;
    outline: none;
    box-shadow: none;
    background: transparent;
    flex: 1;
    flex-wrap: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    &:focus {
      border: none !important;
      outline: none;
      box-shadow: none;
    }
  }
  .pointer {
    cursor: pointer;
  }
  .vertical {
    height: 0.9375rem;
    top: 0;
  }
  .pointer:hover {
    color: @yn-primary-5;
  }
  .requiretitle::after {
    display: inline-block;
    margin-right: @rem4;
    color: @yn-error-color;
    font-size: @rem14;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
  }
  .isedit /deep/.ant-form-item-label {
    overflow: hidden;
    text-overflow: ellipsis;
    height: 2.25rem;
    line-height: 2.25rem;
  }
  /deep/ .ant-form-item-label {
    overflow: hidden;
    text-overflow: ellipsis;
    height: 2rem;
    line-height: 2rem;
  }
  /** 过度效果 */
  .form-value {
    position: absolute;
    width: 100%;
    height: 2rem;
    line-height: 2rem;
  }
  .myfade-enter-active,
  .myfade-leave-active {
    transition: opacity 0.1s, left 0.5s;
  }
  .myfade-enter,
  .myfade-leave-to {
    opacity: 0;
  }
  .myfade-enter-to {
    left: 0;
  }
  .myfade-enter {
    left: @rem10;
  }
}
</style>

<style lang="less">
.jsx-message .exconfirm-main {
  color: @yn-text-color;
  font-weight: 400;
  margin-bottom: @rem20;
}
.jsx-message .exconfirm-des {
  color: @yn-text-color-secondary;
  margin-bottom: @rem12;
  font-weight: 400;
}
.jsx-message .exconfirm-list {
  font-weight: 400;
  max-height: 50vh;
  overflow: auto;
  li {
    color: @yn-link-color;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
