<template>
  <div style="position:relative">
    <div class="grid_content" :style="tableContStyle">
      <grid
        ref="grid"
        :ident="$data.$gridIdent"
        :hasMore="false"
        :columns="columns"
        :readOnly="isEdit"
        :dataSource="tableData || []"
        :fixedColumns="fixedColumns"
        :mixCellWidth="200"
        :renderHeaderCell="renderHeaderCell"
        :cellNode="renderNode"
        :scrollTable="scrollTable"
        :cellMouseUp="cellMouseUp"
        :customBeforeColumnResize="customBeforeColumnResize"
      />
      <div :style="showComInfo.style" class="suspension-components-cont">
        <asyn-select-dim-member
          v-if="showSelectInfo.dimCode === showComInfo.dimCode"
          :key="showSelectInfo.dimCode"
          ref="selectCom"
          :value="showComInfo.value"
          :memberExp="getMemberExpByDimInfo(showSelectInfo)"
          dropDownClass="details-table-cell-dim"
          :needFilterShared="true"
          :needPermission="['Scope', 'Entity'].includes(showSelectInfo.dimCode)"
          v-bind="showSelectInfo.props"
          @dropdownVisibleChange="dropdownVisibleChange"
          @changeVal="
            (...$event) => {
              changeCellVal('select', $event);
            }
          "
        />
        <template v-if="showComInfo.type === 'input'">
          <yn-input
            ref="inputRef"
            class="grid-input"
            :value="showComInfo.value"
            :allowClear="false"
            placeholder="请输入"
            @change="changeCellVal('input', $event)"
            @blur="hideComponents"
          />
        </template>
        <template v-if="showComInfo.type === 'remark'">
          <yn-input
            ref="inputRemarkRef"
            class="grid-input"
            :value="showComInfo.value"
            :allowClear="false"
            placeholder="请输入"
            @change="changeCellVal('remark', $event)"
            @blur="hideComponents"
          />
        </template>
      </div>
    </div>
    <div v-if="isEdit" class="new-add-btn-cont">
      <yn-button type="dashed" @click="newAddRow">
        <span class="custom-add">+</span> 添加
      </yn-button>
    </div>
    <div
      v-show="verificationPopInfo.isShow"
      ref="errorTipsCont"
      class="error-tips-cont"
      :style="errorTipsStyle"
    >
      <span class="tips">{{ verificationPopInfo.word }}</span>
    </div>
  </div>
</template>
<script>
import Grid from "@/components/hoc/grid/index.vue";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
import "yn-p1/libs/components/yn-tooltip/";
import { genVDOM } from "@/views/process/control/jdom/utils";
import { Decimal } from "decimal.js";
import omit from "lodash/omit";
import cloneDeep from "lodash/cloneDeep";
import { toFinance } from "@/utils/filters";
const NUMBER_TYPE_ARR = ["debit", "credit"]; // 借方、贷方
const GRID_IDENT = "DetailsTable"; // grid 标识
// 操作列名称
const ACTION_COLUMN_NAME = "action";
// 非必填表头名称
const NOT_REQUIRED_CELL_NAME = ["rowNum", "action", "remark"];
// 单元格 值 分隔符
const SEPARATOR = "@@";
// 单元格内容 内边距
const CELL_CONTENT_PADDING = 16;
// 小数位数
const DECIMAL_DIGITS = 2;
// 借方、贷方映射关系
const MAPPING_RELATION = {
  debit: "credit",
  credit: "debit"
};
// 错误提示最小宽度
const ERROR_TIPS_MIN_WIDTH = 200;
// 新增行数据标识
const NEW_ADD_ROW_DATA_IDENT = "customIdent";
// 行高
const ROW_HEIGHT = 35;
// 错误提示标识
const ERROR_TIP_IDENT = "verification-error";
export default {
  name: "DetailsTable",
  components: { Grid, AsynSelectDimMember },
  props: {
    gridData: {
      type: Array,
      default: () => []
    },
    columnsInfo: {
      type: Array,
      default: () => []
    },
    dimInfo: {
      type: Object,
      default: () => {}
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    summary: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: null,
      columns: [],
      $gridIdent: GRID_IDENT,
      showComInfo: {
        value: "",
        cellContext: {},
        style: {},
        dimCode: "",
        type: "" // selete input
      },
      toBeSaveObj: {},
      deleteRowArrs: [], // 删除行id集合
      errorInfo: {},
      verificationPopInfo: {
        isShow: false
      } // 错误提示位置、文案信息
    };
  },
  computed: {
    fixedColumns() {
      return this.isEdit ? { right: 1 } : {};
    },
    tableDimInfo() {
      // 表格用到的维度信息
      const dimInfo = {};
      this.gridData.forEach(rowData => {
        const { journalDimInfo } = rowData;
        journalDimInfo.forEach(dimItem => {
          const { dimCode } = dimItem;
          if (dimCode) {
            dimInfo[dimCode] = dimItem;
          }
        });
      });
      return dimInfo;
    },
    dimCellName() {
      const { gridData } = this;
      if (gridData && gridData.length > 0) {
        const { journalDimInfo = [] } = gridData[0];
        return journalDimInfo.map(item => {
          return item.dimCode;
        });
      }
      return [];
    },
    dropDownData() {
      const { gridData } = this;
      if (gridData && gridData.length > 0) {
        const { journalDimInfo = [] } = gridData[0];
        const resArr = [];
        journalDimInfo.forEach(item => {
          const tempObj = this.dimInfo[item.dimId];
          if (tempObj) {
            tempObj.dimId = item.dimId;
            resArr.push(tempObj);
          }
        });
        return resArr;
      }
      return [];
    },
    tableContStyle() {
      const tableDataLen = this.tableData.length;
      // TODO 后期再调整临时方案
      // const tableRowNum = this.tableData ? tableDataLen > 10 ? 11 : tableDataLen + 1 : 0;
      const { tableH: tableMaxH } = this.getTableContHeight();
      const calcH = (tableDataLen + 1) * ROW_HEIGHT + 11;
      const tableHeight = calcH > tableMaxH ? tableMaxH : calcH;
      return {
        height: tableHeight + "px"
      };
    },
    errorTipsStyle() {
      const { width, top, left } = this.verificationPopInfo;
      return {
        width: width + "px",
        top: top + "px",
        left: left + "px"
      };
    },
    showSelectInfo() {
      let res = {};
      this.dropDownData.forEach(item => {
        if (item.dimCode === this.showComInfo.dimCode) {
          res = item;
        }
      });
      return res;
    }
  },
  watch: {
    gridData: {
      handler(newVal) {
        this.formatTableData(newVal);
        setTimeout(() => {
          try {
            // 滚动到底部
            document.querySelector(".contentinfo").scrollTop = 100000000;
            this.$refs.grid.$refs.simpleGrid.scrollViewportTo({
              row: newVal.length
            });
          } catch {}
        }, 300);
        this.$nextTick(() => {});
      },
      immediate: true,
      deep: true
    },
    columnsInfo: {
      handler(newVal) {
        this.formatColumns(newVal);
      },
      immediate: true
    }
  },
  mounted() {
    this.attachResize();
    document.addEventListener("mouseover", this.bodyOverEvent);
  },
  destroyed() {
    this.observer.disconnect();
    document.removeEventListener("mouseover", this.bodyOverEvent);
  },
  methods: {
    attachResize() {
      this.observer = new ResizeObserver(() => {
        setTimeout(
          self => {
            try {
              self.$refs.grid.$refs.simpleGrid.updateSettings();
            } catch (e) {}
          },
          300,
          this
        );
      });
      this.observer.observe(document.querySelector(".detailinfo"));
    },
    customBeforeColumnResize() {
      this.hideComponents();
    },
    cellMouseUp(event, cellContext) {
      const { colName } = cellContext;
      if (!this.isEdit) return;
      // 维度下拉类型
      if (this.dimCellName.indexOf(colName) !== -1) {
        this.showSelectTreeCom(cellContext);
      }
      // number 类型单元格（借方、贷方）
      if (NUMBER_TYPE_ARR.indexOf(colName) !== -1) {
        // 显示数值输入框
        this.showInputCom(cellContext);
      }
      // 备注
      if (colName === "remark") {
        this.showRemarkInputCom(cellContext);
      }
    },
    getMemberExpByDimInfo(dimInfo) {
      const { dimId, members = "{}" } = dimInfo;
      return {
        expDtoList: [
          {
            dimId,
            dimMemberExps: members
          }
        ]
      };
    },
    getTableContHeight() {
      // 必须滚动到底部  才行
      let tableH;
      try {
        tableH = document.querySelector(".contentinfo").clientHeight;
        const addBtnH = document.querySelector(".new-add-btn-cont")
          .clientHeight;
        const titleH = document.querySelector(".blocktitle").clientHeight;
        tableH = tableH - addBtnH - titleH - 20;
      } catch {
        tableH = 500;
      }
      return {
        tableH: tableH > ROW_HEIGHT ? tableH : 500
      };
    },
    bodyOverEvent(e) {
      const className = e.target.className || "";
      if (
        className &&
        typeof className === "string" &&
        className.indexOf(ERROR_TIP_IDENT) !== -1
      ) {
        const tdDom = e.target.parentElement.parentElement.parentElement;
        this.showValidateInfo(tdDom);
      } else {
        this.verificationPopInfo.isShow = false;
      }
    },
    showValidateInfo(td) {
      const classArr = td.className.split(" ");
      const tdIdent =
        classArr.filter(item => item.indexOf(GRID_IDENT) !== -1)[0] || "";
      const message = this.errorInfo[tdIdent] || "";
      if (!tdIdent || !message) {
        // 隐藏错误提示
        this.verificationPopInfo.isShow = false;
      } else {
        const { left, top, width } = this.$refs.grid.getPopPosByTd(td);

        this.verificationPopInfo = {
          isShow: true,
          left:
            width > ERROR_TIPS_MIN_WIDTH
              ? left
              : left - (ERROR_TIPS_MIN_WIDTH - width),
          top,
          width,
          word: this.errorInfo[tdIdent] || ""
        };
      }
    },
    formatTableData(data = []) {
      const setDimCellValFn = this.getDimCellName(data[0]);
      const tableData = data.map(item => {
        const { contentId = new Date().getTime(), ...formatField } = omit(
          item,
          "journalDimInfo"
        );
        const rowData = { objectId: contentId };
        Object.keys(formatField).map(key => {
          rowData[key] = {
            v: setDimCellValFn(item, key)
          };
        });
        return rowData;
      });
      this.tableData = tableData;
      if (this.tableData) {
        this.$refs.grid && this.$refs.grid.updateTableData(this.tableData);
      }
    },
    getDimCellName(firstRowData = {}) {
      const { journalDimInfo = [] } = firstRowData;
      const dimCell = journalDimInfo.map(item => {
        return item.dimCode;
      });
      return (rowData, key) => {
        const indexes = dimCell.indexOf(key);
        const { journalDimInfo: dimInfo } = rowData;
        if (indexes !== -1) {
          const { memberId, memberRealName } = dimInfo.filter(
            item => item.dimCode === key
          )[0];
          return `${memberId}${SEPARATOR}${memberRealName}`;
        } else {
          if (NUMBER_TYPE_ARR.indexOf(key) !== -1) {
            return typeof rowData[key] === "object" ? 0 : rowData[key];
          }
          return rowData[key];
        }
      };
    },
    formatColumns(data = []) {
      const columns = data.map(item => {
        const { title, width, dataIndex, name } = item;
        const resObj = {
          title: title || name,
          width,
          dataIndex,
          cellType: "text"
        };
        // 数字类型 居右 显示
        if (NUMBER_TYPE_ARR.indexOf(dataIndex) !== -1) {
          resObj.align = "right";
        }
        resObj.readOnly = true;
        if (dataIndex === ACTION_COLUMN_NAME) {
          resObj.width = 100;
        }
        return resObj;
      });
      this.columns = columns;
    },
    renderHeaderCell(cellContext) {
      const { td, colName, value: cellValue } = cellContext;
      if (NOT_REQUIRED_CELL_NAME.indexOf(colName) !== -1) return;
      const childDiv = document.createElement("div");
      childDiv.textContent = cellValue;
      childDiv.className = "header-col require-cell";
      td.appendChild(childDiv);
      return childDiv;
    },
    renderNode(cellContext) {
      // 模拟渲染异步下拉组件
      const { colName } = cellContext;
      let childDom;
      // 操作列
      if (colName === "action") {
        childDom = this.renderActionColumn(cellContext);
      }
      // 下拉组件列
      if (this.dimCellName.indexOf(colName) !== -1) {
        childDom = this.renderDimCell(cellContext);
      }
      // number 类型单元格（借方、贷方）
      if (NUMBER_TYPE_ARR.indexOf(colName) !== -1) {
        childDom = this.renderNumberCell(cellContext);
      }
      // 备注
      if (colName === "remark") {
        childDom = this.renderRemarkCell(cellContext);
      }
      if (childDom) {
        // td.appendChild(childDom);
        return childDom;
      }
      return;
    },
    getShowWordDom(currCellVal, id) {
      return genVDOM(
        {
          template: `<yn-tooltip :visibleOnOverflow="true" title="${currCellVal}">
            <span class="remark-val" >
                ${currCellVal}
            </span>
        </yn-tooltip>`
        },
        id
      );
    },
    renderRemarkCell(cellContext) {
      const { value: cellValue, id } = cellContext;
      const updateCellVal = this.getTobeSaveValByCellContext(cellContext);
      const currCellVal =
        typeof updateCellVal === "string" ? updateCellVal : cellValue;
      const childDiv = document.createElement("div");
      const spanNode = document.createElement("span");
      const spanWordNode = this.getShowWordDom(currCellVal, id);
      childDiv.appendChild(spanNode);
      childDiv.appendChild(spanWordNode);
      let className = "number-cell";
      const isVerificationFailed = this.isVerificationFailed(cellContext);
      if (isVerificationFailed) {
        className += " verification-error";
      }
      spanNode.className = className;
      return childDiv;
    },
    // 渲染number 类型单元格
    renderNumberCell(cellContext) {
      const { value: cellValue, row, colName: dimCode } = cellContext;
      const childDiv = document.createElement("div");
      const spanNode = document.createElement("span");
      const spanWordNode = document.createElement("span");
      const updateCellVal = this.getTobeSaveValByCellContext(cellContext);
      const currCellVal =
        typeof updateCellVal === "string" ? updateCellVal : cellValue;
      const showVal = this.formatNumber(currCellVal);
      spanWordNode.textContent = showVal;
      childDiv.appendChild(spanNode);
      childDiv.appendChild(spanWordNode);
      const rowData = this.getRowDataByRowNum(row);
      let className = "number-cell";
      const isVerificationFailed = this.isVerificationFailed(cellContext);
      if (isVerificationFailed) {
        className += " verification-error";
      }
      if (NUMBER_TYPE_ARR.indexOf(dimCode) === -1) return "";
      // 当前数值为空，另外一个数值 不为空或者0
      if (!currCellVal && Number(rowData[MAPPING_RELATION[dimCode]])) {
        className += " disabled-number-cell";
      }
      spanNode.className = className;
      spanWordNode.className = "number-val";
      return childDiv;
    },
    formatNumber(num) {
      const number = Number(num);
      if (!number || isNaN(number)) {
        return "--";
      }
      return toFinance(number, DECIMAL_DIGITS);
    },
    showRemarkInputCom(cellContext) {
      // 显示输入框
      this.hideComponents();
      const { row, colName: dimCode, col } = cellContext;
      const td = document.querySelector(`.${GRID_IDENT}_${row}_${col}`);
      const rowData = this.getRowDataByRowNum(row);
      const cellValue = rowData[dimCode];
      const { top, left, width } = this.getTdPosStyle(td);
      this.showComInfo = {
        value: cellValue ? cellValue + "" : "",
        type: "remark",
        dimCode,
        cellContext,
        style: {
          top: top + "px",
          left: left + "px",
          width: width - 1 + "px"
        }
      };
      this.$set(this, "showComInfo", this.showComInfo);
      this.$nextTick(() => {
        try {
          this.$refs.inputRemarkRef.$el.focus();
        } catch {}
      });
    },
    showInputCom(cellContext, isRemarkCell) {
      this.hideComponents();
      const { row, colName: dimCode, col } = cellContext;
      const td = document.querySelector(`.${GRID_IDENT}_${row}_${col}`);
      const rowData = this.getRowDataByRowNum(row);
      const cellValue = rowData[dimCode];
      // 判断借方、贷方是否有存在值，并且有值的单元格与当前激活是否一致
      if (
        !isRemarkCell &&
        !cellValue &&
        Number(rowData[MAPPING_RELATION[dimCode]])
      ) {
        return;
      }
      const { top, left, width } = this.getTdPosStyle(td);
      this.showComInfo = {
        value: cellValue ? cellValue + "" : "",
        type: "input",
        dimCode,
        cellContext,
        style: {
          top: top + "px",
          left: left + "px",
          width: width - 1 + "px"
        }
      };
      this.$set(this, "showComInfo", this.showComInfo);
      this.$nextTick(() => {
        this.$refs.inputRef.$el.focus();
      });
    },
    getRowDataByRowNum(rowNum) {
      const rowData = this.tableData[rowNum - 1];
      const { objectId } = rowData;
      const unformatRowData = this.unformatRowData(rowData);
      const toBeSaveItem = this.toBeSaveObj[objectId] || {};
      const res = {};
      Object.keys(unformatRowData).forEach(keyName => {
        if (keyName !== "contentId") {
          res[keyName] =
            typeof toBeSaveItem[keyName] !== "undefined"
              ? toBeSaveItem[keyName]
              : unformatRowData[keyName];
        }
      });
      return res;
    },
    hideComponents() {
      this.$set(this.showComInfo, "type", "");
      this.$set(this.showComInfo, "dimCode", "");
    },
    // 渲染 维度单元格
    renderDimCell(cellContext) {
      const { value: cellValue, id } = cellContext;
      const childDiv = document.createElement("div");
      const iconNode = document.createElement("i");
      iconNode.className = "iconfont icon-c1_cr_form_enter drop-down-btn";
      childDiv.className = "simulation-select";
      const showWord =
        this.getTobeSaveValByCellContext(cellContext) || cellValue || "";
      const spanNode = this.getShowWordDom(
        showWord.split(SEPARATOR)[1] || "",
        id
      );
      if (this.isVerificationFailed(cellContext)) {
        spanNode.className = "verification-error";
      }
      childDiv.appendChild(spanNode);
      if (this.isEdit) {
        childDiv.appendChild(iconNode);
      }
      return childDiv;
    },
    getTobeSaveValByCellContext(cellContext) {
      const { rowId, colName } = cellContext;
      const tempObj = this.toBeSaveObj[rowId] || {};
      const updateVal = tempObj[colName];
      if (NUMBER_TYPE_ARR.indexOf(colName) !== -1) {
        return typeof updateVal === "string" || typeof updateVal === "number"
          ? updateVal
          : null;
      } else {
        return typeof updateVal !== "undefined" ? updateVal : null;
      }
    },
    // 显示下拉树组件
    showSelectTreeCom(cellContext) {
      const { colName: dimCode, row, col } = cellContext;
      const rowData = this.getRowDataByRowNum(row);
      const value = rowData[dimCode];
      const td = document.querySelector(`.${GRID_IDENT}_${row}_${col}`);
      const { top, left, width } = this.getTdPosStyle(td);
      this.showComInfo = {
        value: value ? value.split(SEPARATOR)[0] : "",
        dimCode,
        cellContext,
        style: {
          left: left + CELL_CONTENT_PADDING - 6 + "px",
          top: top + 1 + "px",
          width: width - CELL_CONTENT_PADDING * 2 + 6 + "px"
        },
        type: "selete"
      };
      this.$nextTick(() => {
        this.$refs.selectCom.$refs.selector.openMenu();
      });
      this.$set(this, "showComInfo", this.showComInfo);
    },
    getTdPosStyle(td) {
      const { left, top, width } = this.$refs.grid.getPopPosByTd(td);
      const { offsetHeight } = td;
      return {
        left: left,
        top: top - offsetHeight,
        width: width
      };
    },
    getTdDom(e) {
      let tdDom = e.target;
      let isContinue = true;
      while (isContinue) {
        if (tdDom.tagName === "BODY") {
          isContinue = false;
          tdDom = "";
        }
        if (tdDom.tagName !== "TD") {
          tdDom = tdDom.parentElement;
        } else {
          isContinue = false;
        }
      }
      return tdDom;
    },
    // 渲染操作列
    renderActionColumn(cellContext) {
      const { row, rowId } = cellContext;
      const childDiv = document.createElement("div");
      const spanNode = document.createElement("span");
      spanNode.textContent = "删除";
      let classNameText = "header-col action delete-btn";
      // 只有一行数据时，删除 置灰，不可用
      if (row === 1 && this.tableData.length === 1) {
        classNameText += " disabled";
      }
      childDiv.className = classNameText;
      childDiv.appendChild(spanNode);
      spanNode.onmousedown = e => {
        // 删除行数据
        this.deleteRow(rowId);
        e.stopImmediatePropagation();
      };
      return childDiv;
    },
    // 获取是否校验失败
    isVerificationFailed(cellContext) {
      const { row, col } = cellContext;
      return !!this.errorInfo[`${GRID_IDENT}_${row}_${col}`];
    },
    deleteRow(rowId) {
      // 一条数据时候 不允许删除
      if (this.tableData.length === 1) return;
      this.$emit("setBeforeLeave");
      this.deleteRowArrs.push(rowId);
      let rowNum = 1;
      let delNum = 1;
      const tableData = [];
      this.tableData.forEach((rowData, index) => {
        const { objectId } = rowData;
        if (rowId !== objectId) {
          this.updateErrorKey(rowNum, rowData.rowNum.v);
          rowData.rowNum.v = rowNum;
          tableData.push(rowData);
          rowNum++;
        } else {
          this.$refs.grid.$refs.simpleGrid.removeRows(index + 1, 1);
          this.deleteErrorInfoByRowNo(rowNum);
          // 删除行数据
          this.calculationSummary(0, {
            type: "del",
            rowData: this.getRowDataByRowNum(rowNum)
          });
          delNum = index + 1;
        }
      });
      this.$set(this, "tableData", tableData);
      this.$refs.grid.updateTableData(this.tableData);
      this.$nextTick(() => {
        this.$refs.grid.$refs.simpleGrid.updateSettings();
      });
      setTimeout(() => {
        // 滚动到指定删除位置
        this.$refs.grid.$refs.simpleGrid.scrollViewportTo({
          row: delNum
        });
      }, 0);
    },
    updateErrorKey(newRowNum, oldRowNum) {
      const toDoUpdate = Object.keys(this.errorInfo).filter(item => {
        const rowNum = Number(item.split("_")[1]);
        return rowNum === oldRowNum;
      });
      toDoUpdate.forEach(key => {
        const message = this.errorInfo[key];
        if (message) {
          const colNum = key.split("_")[2];
          delete this.errorInfo[key];
          this.errorInfo[`${GRID_IDENT}_${newRowNum}_${colNum}`] = message;
        }
      });
      this.$set(this, "errorInfo", this.errorInfo);
    },
    deleteErrorInfoByRowNo(rowNum) {
      const res = {};
      Object.keys(this.errorInfo).forEach(keyName => {
        const errorRowNum = keyName.split("_")[1];
        if (errorRowNum !== rowNum.toString()) {
          res[keyName] = this.errorInfo[keyName];
        }
      });
      this.$set(this, "errorInfo", res);
    },
    scrollTable() {
      this.dropDownData.forEach(item => {
        const { dimCode } = item;
        this.$refs &&
          this.$refs[dimCode] &&
          this.$refs[dimCode][0].$refs.selector.closeMenu();
      });
      this.hideComponents();
    },
    changeCellVal(type, $event) {
      // 添加保存提示
      this.$emit("setBeforeLeave");
      const mapingObj = {
        input: "getNumberVal",
        select: "getDimMemberVal",
        remark: "changeRemarkCellVal"
      };
      let params = $event;
      if (!Array.isArray(params)) {
        params = [params];
      }
      if (type === "remark") {
        this.changeRemarkCellVal(...params);
        return;
      }
      const showWord =
        this[mapingObj[type]] && this[mapingObj[type]](...params);
      const { cellContext = {} } = this.showComInfo;
      const { row, col, rowId, colName } = cellContext;
      // 判断 是否跟源数据一致，一致不计入toBeSaveObj
      const orgVal = this.tableData[row - 1][colName] || {};
      if (orgVal.v === showWord) {
        try {
          delete this.toBeSaveObj[rowId][colName];
        } catch {}
        this.$refs.grid.$refs.simpleGrid.hot.setDataAtCell(row, col, showWord);
        return;
      }
      if (!this.toBeSaveObj[rowId]) {
        this.toBeSaveObj[rowId] = {
          [colName]: showWord
        };
      } else {
        this.toBeSaveObj[rowId][colName] = showWord;
      }
      this.$set(this, "toBeSaveObj", this.toBeSaveObj);
      this.$refs.grid.$refs.simpleGrid.hot.setDataAtCell(row, col, showWord);
    },
    dropdownVisibleChange(open) {
      if (!open) {
        this.hideComponents();
      }
    },
    changeRemarkCellVal(event) {
      const value = event.target.value;
      const { cellContext, dimCode } = this.showComInfo;
      const { rowId, row, col } = cellContext;
      if (!this.toBeSaveObj[rowId]) {
        this.toBeSaveObj[rowId] = {
          [dimCode]: value
        };
      } else {
        this.toBeSaveObj[rowId][dimCode] = value;
      }
      // 校验是否超过500个字
      if (value.length > 500) {
        const message = this.getErrorMessageByDimCode(this.showComInfo.dimCode);
        this.errorInfo[`${GRID_IDENT}_${row}_${col}`] = message;
      } else {
        delete this.errorInfo[`${GRID_IDENT}_${row}_${col}`];
      }
      this.$set(this, "toBeSaveObj", this.toBeSaveObj);
      this.$refs.grid.$refs.simpleGrid.hot.setDataAtCell(row, col, value);
    },
    calculationSummary(num, opearRowData) {
      let colName = "";
      let diffVal = num;
      const propMapObj = {
        debit: "totalDebit",
        credit: "totalCredit"
      };

      const orgDiffVal = new Decimal(this.summary.difference.value);
      // 增加删除行 操作
      if (opearRowData) {
        const { type, rowData } = opearRowData; // type： add or del,rowData:行数据
        const {
          colName: columnName,
          value
        } = this.getDebitOrCreditValAndColName(type, rowData);
        colName = columnName;
        diffVal = new Decimal(value);
      } else {
        const { dimCode } = this.showComInfo;
        colName = dimCode;
      }
      let calcDiffVal = orgDiffVal;
      if (colName === "debit") {
        calcDiffVal = calcDiffVal.add(diffVal);
      } else {
        calcDiffVal = calcDiffVal.sub(diffVal);
      }
      const orgVal = this.summary[propMapObj[colName]].value;
      const calcRes = new Decimal(orgVal).add(diffVal);
      this.summary[propMapObj[colName]].value = calcRes;
      this.summary.difference.value = calcDiffVal;
      this.$emit("update:summary", this.summary);
    },
    getDebitOrCreditValAndColName(type, rowData) {
      let colName;
      const { debit, credit } = rowData;
      let currVal = new Decimal(0);
      if (debit) {
        colName = "debit";
        currVal = new Decimal(Number(debit));
      } else {
        colName = "credit";
        currVal = new Decimal(Number(credit));
      }
      if (type === "del") {
        currVal = new Decimal(-1).mul(currVal);
      }
      return {
        colName,
        value: currVal
      };
    },
    getNumberVal(event) {
      let tempVal = event.target.value;
      const orgVal = this.showComInfo.value;
      if (!tempVal.match(/^[\+\-]?\d*?\.?\d*?$/)) {
        tempVal = orgVal;
      }
      event.target.value = tempVal;
      this.$set(this.showComInfo, "value", tempVal);
      if (tempVal && Number(tempVal)) {
        //  不为空，需要将借方、贷方的校验信息删除掉
        this.deleteNumberErrorInfo();
      } else {
        // 为空，将校验信息放出去
        this.addNumberErrorInfo();
      }
      // 重新计算
      this.calculationSummary(
        new Decimal(Number(tempVal) || 0).sub(
          new Decimal(isNaN(orgVal) ? 0 : orgVal || 0)
        )
      );
      return Number(tempVal) ? tempVal : "";
    },
    getDimMemberVal(value, selectItem) {
      const { label } = selectItem[0] || {};
      // 删除 对应的错误校验信息
      const { cellContext } = this.showComInfo;
      const { row: rowIndex, col: colIndex } = cellContext;
      delete this.errorInfo[`${GRID_IDENT}_${rowIndex}_${colIndex}`];
      return `${value}${SEPARATOR}${label}`;
    },
    addNumberErrorInfo() {
      const { credit, debit } = this.getDebitAndCreditCellIdent();
      const message = this.getErrorMessageByDimCode(this.showComInfo.dimCode);
      this.errorInfo[`${credit}`] = message;
      this.errorInfo[`${debit}`] = message;
    },
    deleteNumberErrorInfo() {
      const { credit, debit } = this.getDebitAndCreditCellIdent();
      delete this.errorInfo[`${credit}`];
      delete this.errorInfo[`${debit}`];
    },
    // 获取借方 贷方的单元格标识
    getDebitAndCreditCellIdent() {
      const { cellContext, dimCode } = this.showComInfo;
      const { row: rowIndex, col: colIndex } = cellContext;
      const otherCol = {
        credit: colIndex - 1,
        debit: colIndex + 1
      };
      return {
        [dimCode]: `${GRID_IDENT}_${rowIndex}_${colIndex}`,
        [MAPPING_RELATION[
          dimCode
        ]]: `${GRID_IDENT}_${rowIndex}_${otherCol[dimCode]}`
      };
    },
    // 通过dimCode 获取错误提示信息
    getErrorMessageByDimCode(dimCode) {
      const columnsInfo = this.columnsInfo;
      let message = "";
      for (let i = 0, LEN = columnsInfo.length; i < LEN; i++) {
        const { dataIndex, message: currMessage } = columnsInfo[i];
        if (dataIndex === dimCode) {
          message = currMessage;
          break;
        }
      }
      return message;
    },
    newAddRow() {
      // 新增行
      const errorInfo = this.checkTableRequired();
      if (Object.keys(errorInfo).length) {
        document.querySelector(".contentinfo").scrollTop = 100000000;
        this.$refs.grid.$refs.simpleGrid.scrollViewportTo({
          row: this.tableData.length
        });
        // 强制触发simpleGrid render
        this.$refs.grid.$refs.simpleGrid.hot.setDataAtCell(1, 0, 1);
        return;
      }
      this.$emit("setBeforeLeave");
      const lastRowData = this.getLastRowData();
      lastRowData.objectId = NEW_ADD_ROW_DATA_IDENT + new Date().getTime();
      lastRowData.credit.v = "";
      lastRowData.debit.v = "";
      lastRowData.rowNum.v += 1;
      this.tableData.push(lastRowData);
      this.$refs.grid.updateTableData(this.tableData);
      this.checkTableRequired();
      this.$nextTick(() => {
        // 滚动到底部
        document.querySelector(".contentinfo").scrollTop = 100000000;
        this.$refs.grid.$refs.simpleGrid.scrollViewportTo({
          row: this.tableData.length
        });
      });
    },
    getLastRowData() {
      const lastRowData = cloneDeep(this.tableData.slice(-1)[0]);
      const { objectId } = lastRowData;
      const toBeSaveItem = this.toBeSaveObj[objectId] || {};
      Object.keys(toBeSaveItem).forEach(item => {
        const tempVal = toBeSaveItem[item];
        if (tempVal) {
          lastRowData[item] = { v: tempVal };
        }
      });
      if (toBeSaveItem["debit"]) {
        lastRowData["credit"].v = null;
      } else if (toBeSaveItem["credit"]) {
        lastRowData["debit"].v = null;
      }
      return lastRowData;
    },
    checkTableRequired(isScrollToErrorRow) {
      this.$set(this, "errorInfo", {});
      this.tableData.forEach((item, rowIndex) => {
        this.checkTableRowData(item, rowIndex);
      });
      this.$set(this, "errorInfo", this.errorInfo);
      if (isScrollToErrorRow) {
        const keys = Object.keys(this.errorInfo);
        if (keys.length) {
          // 滚动到底部
          document.querySelector(".contentinfo").scrollTop = 100000000;
          const rowIndex = keys[0].split("_")[1];
          this.$refs.grid.$refs.simpleGrid.scrollViewportTo({
            row: Number(rowIndex)
          });
        }
      }
      return this.errorInfo;
    },
    checkTableRowData(rowData, rowIndex) {
      this.columnsInfo.forEach((colItem, colIndex) => {
        const { dataIndex, required, message } = colItem;
        // 贷方、借方校验
        if (NUMBER_TYPE_ARR.indexOf(dataIndex) !== -1) {
          this.checkDebitAndCredit({ rowData, rowIndex, colIndex, message });
          return;
        }
        // 备注列 校验
        if (dataIndex === "remark") {
          this.checkRemark(rowData, rowIndex, colIndex, message);
        } else if (required) {
          // 维度 单元格校验
          const currCellVal = this.getCellValByColName(rowData, dataIndex);
          if (!currCellVal.replace(SEPARATOR, "")) {
            this.errorInfo[
              `${GRID_IDENT}_${rowIndex + 1}_${colIndex}`
            ] = message;
          }
        }
      });
    },
    getCellValByColName(rowData, colName) {
      const { objectId } = rowData;
      const toBeSaveItem = this.toBeSaveObj[objectId];
      const orgVal = rowData[colName].v;
      if (toBeSaveItem && typeof toBeSaveItem[colName] !== "undefined") {
        return toBeSaveItem[colName];
      } else {
        return orgVal;
      }
    },
    // 校验借方、贷方
    checkDebitAndCredit(obj) {
      const { rowData, rowIndex, colIndex, message } = obj;
      const debitVal = this.getCellValByColName(rowData, NUMBER_TYPE_ARR[0]);
      const creditVal = this.getCellValByColName(rowData, NUMBER_TYPE_ARR[1]);
      if (!debitVal && !creditVal) {
        this.errorInfo[`${GRID_IDENT}_${rowIndex + 1}_${colIndex}`] = message;
      }
    },
    // 校验备注列
    checkRemark(rowData, rowIndex, colIndex, message) {
      const cellVal = this.getCellValByColName(rowData, "remark");
      if (cellVal && cellVal.length > 500) {
        this.errorInfo[`${GRID_IDENT}_${rowIndex + 1}_${colIndex}`] = message;
      }
    },
    checkNumberCell(rowData, rowIndex, colIndex, colName) {
      if (colName === "credit") {
        const debitErrorInfo = this.errorInfo[
          `${GRID_IDENT}_${rowIndex}_${colIndex}`
        ];
        if (debitErrorInfo) return;
      }
    },
    // 对外提供待保存参数
    getTobeSaveInfo() {
      const tableData = this.tableData.map((rowData, index) => {
        const { objectId } = rowData;
        const currRowData = this.getRowDataByRowNum(index + 1);
        if (
          typeof objectId !== "number" &&
          objectId.indexOf(NEW_ADD_ROW_DATA_IDENT) === -1
        ) {
          currRowData.contentId = objectId;
        }
        return this.formatDimCell(currRowData);
      });
      return tableData;
    },
    formatDimCell(rowData = {}) {
      const res = {};
      const journalDimInfo = {};
      Object.keys(rowData).forEach(keyName => {
        if (this.dimCellName.indexOf(keyName) !== -1) {
          journalDimInfo[keyName] = rowData[keyName].split(SEPARATOR)[0];
        } else {
          let cellVal = rowData[keyName];
          if (NUMBER_TYPE_ARR.indexOf(keyName) !== -1) {
            cellVal = Number(cellVal) || null;
          }
          res[keyName] = cellVal;
        }
      });
      if (Object.keys(journalDimInfo).length) {
        res.journalDimInfo = journalDimInfo;
      }
      return res;
    },
    unformatRowData(rowData) {
      const res = {};
      Object.keys(rowData).forEach(key => {
        if (key === "objectId") {
          res["contentId"] = rowData[key];
        } else if (NUMBER_TYPE_ARR.indexOf(key) !== -1) {
          res[key] = rowData[key].v ? rowData[key].v : null;
        } else {
          const tempVal = rowData[key].v ? rowData[key].v + "" : "";
          res[key] = tempVal.split(SEPARATOR)[0];
        }
      });
      return res;
    }
  }
};
</script>
<style lang="less" scoped>
.grid_content {
  height: 12.5rem; // 临时
  position: relative;
  /deep/.require-cell {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  /deep/.require-cell::after {
    content: "*";
    color: @yn-error-color;
    margin-left: @rem4;
    display: inline-block;
    font-size: @rem14;
    line-height: 1;
    font-family: SimSun, sans-serif;
  }
  /deep/.delete-btn {
    display: inline-block;
    // font-size: @rem14;
    // height: @rem30;
    // line-height: @rem30;
    font-size: 14px;
    height: 30px;
    line-height: 30px;
    border-radius: @yn-border-radius-base;
    color: @yn-primary-color;
    width: auto;
  }
  /deep/.disabled.delete-btn {
    color: @yn-disabled-color;
    background-color: @yn-disabled-bg-color;
  }
  /deep/.delete-btn:hover {
    background-color: @yn-icon-bg-color;
  }
  /deep/.disabled.delete-btn {
    background-color: @yn-disabled-bg-color;
  }
  /deep/.drop-down-btn {
    transform: rotate(90deg);
    float: right;
    color: @yn-disabled-color;
    font-size: 20px;
    margin-right: 3px;
  }
  /deep/.simulation-select {
    cursor: pointer;
    font-size: 14px;
    margin-top: 3px;
    & > span {
      display: inline-block;
      width: calc(100% - @rem24);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: @yn-text-color;
    }
  }
  .suspension-components-cont {
    position: absolute;
    z-index: 20;
    /deep/.yn-select-tree {
      border: 0;
      box-shadow: none;
    }
  }
  /deep/.number-val {
    position: relative;
    z-index: 11;
  }
  /deep/.remark-val {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  /deep/.disabled-number-cell::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 10;
    top: 0;
    left: 0;
    background: @yn-disabled-bg-color;
    // pointer-events: none;
    // padding-left: @rem16;
  }
  /deep/.verification-error {
    position: absolute;
    width: 100% !important;
    height: 100%;
    z-index: 10;
    top: 0;
    left: 0;
    border: 1px solid @yn-error-color;
  }
  /deep/.simple-cell {
    position: relative;
  }
  .grid-input {
    height: 33px;
  }
}
.new-add-btn-cont {
  margin-top: 0.75rem;
  & > button {
    width: 100%;
  }
}
.error-tips-cont {
  position: absolute;
  display: flex;
  justify-content: end;
  min-width: 200px; // 与ERROR_TIPS_MIN_WIDTH对应
  & > .tips {
    display: inline-block;
    position: absolute;
    height: 24px;
    line-height: 26px;
    padding: 0 12px;
    color: @yn-error-color;
    background: @yn-error-bg-color;
    z-index: 100;
    box-shadow: inset 0 0 5px 0 @yn-border-color-base;
    border-bottom-left-radius: @yn-border-radius-base;
    border-bottom-right-radius: @yn-border-radius-base;
  }
}
// 下拉tree 组件样式覆盖
/deep/.yn-select-tree-value-single {
  padding-left: 12px;
}
/deep/.yn-select-tree-value {
  height: 30px;
  line-height: 30px;
}
/deep/.yn-select-tree {
  height: 30px;
  font-size: 14px;
  padding: 0 8px 0 4px;
  width: calc(100% + 10px);
  margin-left: -10px;
}
/deep/.yn-select-tree-btn {
  font-size: 12px;
  line-height: 30px;
  right: 8px;
}
</style>
<style lang="less">
.grid_content {
  .simple-cell-readonly {
    background-color: @yn-component-background !important;
  }
  .handsontable td {
    min-width: 100px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.details-table-cell-dim {
  max-width: 432px;
  min-width: 108px;
  width: auto !important;
}
</style>
