<template>
  <div>
    <yn-modal
      :visible="visible"
      :bodyStyle="{ minHeight: '3.75rem' }"
      :title="groupObj.title ? '编辑分组' : '新增'"
      okText="保存"
      @cancel="cancelEvent"
    >
      <template slot="footer">
        <yn-button key="cancel" @click="cancelEvent">
          取消
        </yn-button>
        <yn-button
          key="submit"
          type="primary"
          :loading="btnLoading"
          @click="okEvent"
        >
          保存
        </yn-button>
      </template>
      <yn-form :form="groupForm" v-bind="layout">
        <yn-form-item label="分组名称">
          <yn-input
            v-decorator="[
              'groupName',
              {
                initialValue: groupObj.title ? groupObj.title : '',
                rules: [
                  { required: true, message: '请输入' },
                  { max: 64, message: '字段长度超过64位限制, 请检查' },
                  { validator: validatorSameName }
                ]
              }
            ]"
            class="group-input"
            @change="onChange"
          >
            <yn-icon
              slot="suffix"
              class="cursor-p"
              type="global"
              @click="openMultilingual"
            />
          </yn-input>
          <div v-if="sameNameTip" class="sameName">
            当前名称已被占用，请修改
          </div>
        </yn-form-item>
      </yn-form>
    </yn-modal>
    <!-- <Multilanguage
      :languageVisible="languageVisible"
      @cancelMultilanguage="cancelMultilanguage"
    /> -->
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-form-item/";

// import Multilanguage from "@/components/multilanguage";
import UiUtils from "yn-p1/libs/utils/UiUtils.js";

import journalService from "@/services/journal";
export default {
  // components: { Multilanguage },
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    groupObj: {
      type: Object,
      default: () => {}
    },
    rootId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      groupForm: this.$form.createForm(this, "groupForm"),
      layout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 16 }
      },
      sameNameTip: false,
      languageVisible: false,
      btnLoading: false
    };
  },
  methods: {
    cancelEvent() {
      this.$emit("closeGroupModal", "group", false);
    },
    okEvent() {
      this.groupForm.validateFields((err, values) => {
        if (!err) {
          this.handleSave(values);
        }
      });
    },
    handleSave(values) {
      const { groupName } = values;
      const addParams = {
        parentId: this.rootId || 0,
        name: groupName
      };
      const editParams = {
        parentId: this.groupObj.parentId,
        name: groupName,
        objectId: this.groupObj.objectId
      };
      const params = this.groupObj.title ? editParams : addParams;
      const methods = this.groupObj.title ? "updateGroupNode" : "addGroupNode";
      this.btnLoading = true;
      journalService(methods, params)
        .then(res => {
          if (res.data.messageType === "ERROR") {
            this.sameNameTip = true;
          } else {
            const tip = this.groupObj.title ? "编辑成功" : "新增成功";
            UiUtils.successMessage(tip);
            this.$emit("onSave", "group");
          }
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    openMultilingual() {
      // TODO 多语言
      this.languageVisible = true;
    },
    validatorSameName(rule, value, callback) {
      if (value === 1) {
        callback("当前名称已被占用，请修改");
      } else {
        callback();
      }
    },
    onChange() {
      this.sameNameTip = false;
    },
    cancelMultilanguage() {
      this.languageVisible = false;
    }
  }
};
</script>

<style lang="less" scoped>
.cursor-p {
  cursor: pointer;
}
.sameName {
  color: @yn-error-color;
  height: @rem22;
  line-height: @rem22;
}
</style>
