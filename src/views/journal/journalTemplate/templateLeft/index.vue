<template>
  <yn-spin :spinning="loading">
    <div class="template-left">
      <yn-tree-menu
        class="template-tree"
        :treePanelSkeleton="treePanelSkeleton"
        :treeConfig="treeConfig"
        @expand="onExpand"
        @select="selectNode"
        @drop="onDrop"
      >
        <span slot="operateBar" class="operate-bar">
          <div v-if="searching" class="header-input-search">
            <yn-input-search
              v-model="searchValue"
              class="menu-input-search"
              placeholder="请输入"
              @search="handleSearch"
            />
          </div>
          <div v-else class="left-header">
            <yn-dropdown>
              <yn-menu slot="overlay">
                <yn-menu-item @click="() => handleMenuClick('temp', true)">
                  <span>新增模板</span>
                </yn-menu-item>
                <yn-menu-item @click="() => handleMenuClick('group', true)">
                  <span>新增分组</span>
                </yn-menu-item>
              </yn-menu>
              <yn-button type="primary" size="small" class="add-top">
                新增
                <yn-icon type="down" />
              </yn-button>
            </yn-dropdown>
            <div class="header-icon">
              <svg-icon
                :isIconBtn="true"
                type="icon-shuaxin"
                title="刷新"
                @click="refreshEvent"
              />
              <svg-icon
                :isIconBtn="true"
                type="icon-search1"
                title="搜索"
                @click="showSearch"
              />
            </div>
          </div>
        </span>
        <template slot="tree.custom" slot-scope="item">
          <span class="node-title">
            <svg-icon
              v-if="!item.isLeaf"
              class="wjj-icon"
              :isIconBtn="false"
              type="icon-wenjianjia"
            />
            <span
              v-tooltip="{ visibleOnOverflow: true, title: item.title }"
              :class="[
                'node-title-content',
                item.isLeaf ? 'node-title-leaf' : 'node-title-not-leaf'
              ]"
            >
              {{ item.title }}
              <span v-show="!item.isLeaf">({{ item.subNodes.length }})</span>
            </span>
            <span class="node-operate-icon" @click.stop>
              <yn-dropdown
                placement="bottomRight"
                overlayClassName="tree-menu-dropdown"
                :trigger="['click']"
              >
                <!-- <svg-icon
                  type="icon-more"
                  :isIconBtn="true"
                  class="icon-small"
                /> -->
                <yn-icon-button
                  type="more"
                  class="more-btn"
                  size="small"
                  @click.stop
                />
                <yn-menu slot="overlay" @click="e => handleOperate(e, item)">
                  <yn-menu-item
                    v-for="btn in item.isLeaf ? templateMenu : groupMenu"
                    :key="btn.key"
                    class="templateMenu"
                    :disabled="btn.key === 'deleteGroup' && item.defaultGroup"
                  >
                    {{ btn.label }}
                  </yn-menu-item>
                </yn-menu>
              </yn-dropdown>
            </span>
          </span>
        </template>
      </yn-tree-menu>
      <AddTempModal
        v-if="tempVisible"
        :visible="tempVisible"
        :tempObj="tempObj"
        :rootId="rootId"
        @closeTempModal="handleMenuClick"
        @onSave="onSaveEvent"
      />
      <AddGroupModal
        v-if="groupVisible"
        :groupObj="groupObj"
        :visible="groupVisible"
        :rootId="rootId"
        @closeGroupModal="handleMenuClick"
        @onSave="onSaveEvent"
      />
    </div>
  </yn-spin>
</template>

<script>
import "yn-p1/libs/components/yn-tree/";
import "yn-p1/libs/components/yn-tree-menu/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-empty/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-input-search/";
import "yn-p1/libs/components/yn-simple-search/";
import "yn-p1/libs/components/yn-icon-button/";
import "yn-p1/libs/components/yn-tooltip/";

import UiUtils from "yn-p1/libs/utils/UiUtils";
import Logger from "yn-p1/libs/modules/log/logger";

import AddTempModal from "../addTempModal.vue";
import AddGroupModal from "../addGroupModal.vue";

import { handleTempTree, pavingTree } from "@/utils/journal.js";
import journalService from "@/services/journal";
import cloneDeep from "lodash/cloneDeep";

export default {
  components: {
    AddTempModal,
    AddGroupModal
  },
  inject: ["defaultParams"],
  data() {
    return {
      treePanelSkeleton: {
        loading: false
      },
      initConfig: {
        show: true,
        tipText: "请新增节点"
      },
      treeConfig: {
        selectedKeys: [],
        // 默认展开节点
        expandedKeys: [],
        draggable: true,
        treeData: []
      },

      searching: false,
      dataList: [],
      selectTreeNode: null, // 树的选中节点信息
      tempVisible: false, // 新增模板 visible
      groupVisible: false, // 新增分组 visible
      groupMenu: [
        {
          label: "新增分组",
          key: "addGroup"
        },
        {
          label: "编辑分组",
          key: "editGroup"
        },
        {
          label: "删除分组",
          key: "deleteGroup"
        },
        {
          label: "新增模板",
          key: "addTemplate"
        }
      ],
      templateMenu: [
        {
          label: "编辑模板",
          key: "editTemplate"
        },
        {
          label: "删除模板",
          key: "deleteTemplate"
        }
      ],
      tempObj: {}, // 模板对象 编辑反显使用
      groupObj: {},
      searchValue: "",
      rootId: "",
      loading: false,
      originData: []
    };
  },
  created() {
    this.getTemplateData(() => {
      const { id: templateId } = this.defaultParams.MRJ || {};
      let selectTreeNode;
      const [treeNode] = this.dataList.filter(
        item => item.templateId === templateId
      );
      if (templateId) {
        if (treeNode) {
          const { objectId, parentId } = treeNode;
          this.treeConfig.selectedKeys = [objectId];
          this.treeConfig.expandedKeys.push(parentId);
          selectTreeNode = treeNode;
        } else {
          this.defaultSelectFirst();
          UiUtils.errorMessage("日记账模板不存在");
          return;
        }
      } else {
        this.defaultSelectFirst();
      }
      this.$emit("setSelectTreeNode", selectTreeNode || this.selectTreeNode);
    });
  },
  methods: {
    // 默认展开第一个分组下的第一项 第一项没有依次往后
    defaultSelectFirst() {
      const { treeData, expandedKeys } = this.treeConfig;
      for (let i = 0; i < treeData.length; i++) {
        const curItem = treeData[i];
        if (curItem.children && curItem.children.length) {
          this.selectTreeNode = curItem.children[0];
          this.treeConfig.expandedKeys = [...expandedKeys, curItem.key];
          this.treeConfig.selectedKeys = [curItem.children[0].key];
          break;
        }
      }
    },
    getTemplateData(cb) {
      this.loading = true;
      journalService("getJournalTempTree")
        .then(res => {
          res = res.data;
          if (res.data.subNodes && res.data.subNodes.length) {
            this.rootId = res.data.objectId;
            this.treeConfig.treeData = handleTempTree(res.data.subNodes);
            this.originData = cloneDeep(this.treeConfig.treeData);
            this.dataList = pavingTree(res.data.subNodes);
            cb && cb();
          } else {
            this.treeConfig.treeData = [];
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleMenuClick(type, bool) {
      this[`${type}Visible`] = bool;
      this[`${type}Obj`] = {};
      if (type === "temp") {
        if (!bool) return;
        const defaultG = this.treeConfig.treeData.filter(
          item => item.defaultGroup
        );
        this.tempObj.groupId = defaultG[0].objectId;
      }
    },
    onSaveEvent(type, values) {
      this[`${type}Visible`] = false;
      this.getTemplateData(() => {
        if (type === "temp") {
          this.treeConfig.expandedKeys = [
            ...this.treeConfig.expandedKeys,
            values.groupId
          ];
          const curItem = this.dataList.filter(
            item => item.objectId === values.objectId
          );
          // 编辑模板弹窗后刷一遍接口获取最新数据。但是树节点信息并没有提供 下面两个信息。。。。。
          curItem[0].remark = values.remark;
          curItem[0].cubeCode = values.cubeCode;
          this.selectNode([values.objectId], curItem[0], values.isAdd);
        }
      });
    },
    updateNameAndDesc({ parentId, objectId, remark, name }) {
      const { treeData } = this.treeConfig;
      const parentItem = treeData.find(item => item.objectId === parentId);
      if (!parentItem) return;
      const curItem = parentItem.children.find(
        item => item.objectId === objectId
      );
      if (!curItem) return;
      this.$set(curItem, "name", name);
      this.$set(curItem, "title", name);
      this.$set(curItem, "remark", remark);
    },
    onExpand(expandedKeys) {
      this.treeConfig.expandedKeys = expandedKeys;
      this.treeConfig.autoExpandParent = false;
    },
    selectNode(selectedKeys, e, bool) {
      this.savePromptMixin().then(() => {
        // 点击节点展开收起
        if (selectedKeys.length === 0) return;
        if (e && e.node && e.node.dataRef) {
          if (e.node.dataRef.isLeaf) {
            this.treeConfig.selectedKeys = selectedKeys;
            this.selectTreeNode = e.node.dataRef;
            this.selectTreeNode.isNewTemp = bool;
          } else {
            e.node.onExpand();
          }
        } else if (e && e.isLeaf) {
          this.treeConfig.selectedKeys = selectedKeys;
          this.selectTreeNode = e;
          this.selectTreeNode.isNewTemp = bool;
        }
        this.$emit("setSelectTreeNode", this.selectTreeNode);
      });
    },
    refreshEvent() {
      this.treeConfig.expandedKeys = [];
      this.selectNode();
      this.getTemplateData();
    },
    showSearch() {
      this.searching = true;
      this.searchValue = "";
      this.$nextTick(() => {
        const searchDom = document.getElementsByClassName(
          "ant-input-search"
        )[0];
        const inputDom =
          searchDom && searchDom.getElementsByClassName("ant-input")[0];
        if (inputDom) {
          inputDom.focus();
          inputDom.onblur = () => {
            if (!this.searchValue) {
              this.searching = false;
            }
          };
        }
      });
    },
    handleSearch(e) {
      if (!e) {
        this.searching = false;
        this.treeConfig.treeData = this.originData;
        return;
      }
      const allTemplate = [];
      const loop = list => {
        list.forEach(item => {
          if (item.isLeaf) {
            allTemplate.push(item);
          }
          if (item.children && item.children.length) {
            loop(item.children);
          }
        });
      };
      loop(this.originData);
      this.treeConfig.treeData = allTemplate.filter(
        item => item.name.indexOf(e) > -1
      );
    },
    handleOperate(e, item) {
      const { key } = e;
      this[key](item);
    },
    addGroup(item) {
      Logger.info(item);
      this.groupVisible = true;
      this.groupObj = {};
    },
    editGroup(item) {
      Logger.info(item);
      this.groupVisible = true;
      this.groupObj = item;
    },
    deleteGroup(item) {
      Logger.info(item);
      const groupNodeId = item.objectId || item.dataRef.objectId;
      const isSelectTmpInGroup = (item.children || []).some(
        item => item.key === this.selectTreeNode.key
      );
      UiUtils.confirm({
        icon: () =>
          this.$createElement("yn-icon", {
            props: {
              type: "exclamation-circle",
              theme: "filled"
            }
          }),
        // 一切文案以ux为准
        title: `删除 ${item.title} 文件夹？`,
        content: "删除后，文件夹和其下日记账模板同时移除",
        onOk: () => {
          journalService("deleteGroupNode", { groupNodeId }).then(res => {
            if (res.data) {
              UiUtils.successMessage("删除成功");
              this.getTemplateData(() => {
                if (isSelectTmpInGroup) {
                  this.defaultSelectFirst();
                  this.$emit("setSelectTreeNode", this.selectTreeNode);
                }
              });
            }
          });
        }
      });
    },
    addTemplate(item) {
      // 分组上新增模板 返显当前 groupId 分组
      this.tempVisible = true;
      item["clickType"] = "groupAdd";
      this.tempObj = {
        groupId: item.objectId
      };
    },
    async editTemplate(item) {
      Logger.info(item);
      this.tempVisible = true;
      this.tempObj = item;
    },
    deleteTemplate(item) {
      const _this = this;
      const bool = _this.selectTreeNode.key === item.key;
      this.savePromptMixin().then(() => {
        UiUtils.confirm({
          icon: () =>
            _this.$createElement("yn-icon", {
              props: {
                type: "exclamation-circle",
                theme: "filled"
              }
            }),
          title: `删除 ${item.name} 日记账模板？`,
          content: "删除后，日记账模板移除",
          onOk: () => {
            journalService("deleteJournalTemplate", {
              leafId: item.objectId
            }).then(res => {
              if (res.data) {
                UiUtils.successMessage("删除成功");
                this.getTemplateData();
              }
              if (bool) {
                this.getTemplateData(() => {
                  this.defaultSelectFirst();
                  this.$emit("setSelectTreeNode", _this.selectTreeNode);
                });
              }
            });
          }
        });
      });
    },
    findIndex(arr, key) {
      if (!Array.isArray(arr)) return;
      for (let i = 0; i < arr.length; i++) {
        const curItem = arr[i];
        if (curItem.key === key) return i;
      }
      return null;
    },
    onDrop({ node, dragNode, dragNodesKeys, dropPosition }) {
      const dragInfo = dragNode.dataRef;
      // 目标节点节点的 key
      const nodeInfo = node.dataRef;
      const dropPos = node.pos.split("-");
      const position = dropPosition - Number(dropPos[dropPos.length - 1]);
      // 分组不能拖拽到其他分组内
      if (!dragInfo.isLeaf) {
        if (nodeInfo.isLeaf || position === 0) {
          UiUtils.warningMessage("分组不能拖拽到其他分组内");
          return;
        }
      }
      if (dragInfo.isLeaf && nodeInfo.isLeaf && position === 0) {
        UiUtils.warningMessage("模板不能拖拽到其他模板内");
        return;
      }
      if (dragInfo.isLeaf && !nodeInfo.isLeaf && position !== 0) {
        UiUtils.warningMessage("模板不能拖拽分组外");
        return;
      }
      const reqParams = {
        srcId: dragInfo.objectId,
        targetId: nodeInfo.objectId,
        direction: position > 0 ? "down" : "up"
      };
      journalService("moveGroupNode", reqParams).then(() => {
        this.getTemplateData(() => {});
      });
    }
  }
};
</script>

<style lang="less">
.more-menu {
  min-width: 5rem;
  text-align: center;
  .ant-dropdown-menu-item {
    text-align: center;
  }
}
</style>
<style lang="less" scoped>
.template-left {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  /deep/.yn-tree-menu-wrapper {
    overflow-x: hidden;
  }
  .template-tree {
    width: 100% !important;
    height: 100%;
  }
  .node-title {
    display: inline-block;
    width: 100%;
    height: @rem32;
    line-height: @rem28;
    position: relative;
    // left: -@rem8;
    .wjj-icon {
      top: -@rem8;
      position: relative;
      color: @yn-label-color;
    }
    .node-title-content {
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .node-title-leaf {
      width: calc(100% - 2rem);
    }
    .node-title-not-leaf {
      width: calc(100% - 4rem);
      margin-left: 0.25rem;
    }
  }
  /deep/.ant-tree-treenode-selected .ant-tree-node-selected .node-operate-icon {
    display: inline-block;
  }
  .node-operate-icon {
    display: none;
    position: absolute;
    right: 0.125rem;
    height: 2rem;
    line-height: 2rem;
    .more-btn {
      transform: translateY(-2px);
    }
  }
  /deep/.ant-tree-title:hover {
    .node-operate-icon {
      display: inline-block;
    }
  }
  /deep/.operate-bar {
    display: inline-block;
    width: 100%;
  }
  .header-input-search {
    height: @rem40;
    line-height: @rem40;
    box-sizing: border-box;
    border-bottom: 1px solid @yn-border-color-base;
    .menu-input-search {
      height: @rem26;
      width: 100%;
      /deep/ .ant-input {
        height: @rem26;
      }
    }
  }
  .left-header {
    height: @rem40;
    box-sizing: border-box;
    border-bottom: 1px solid @yn-border-color-base;
    display: flex;
    align-items: center;
    .add-top {
      font-family: PingFangSC-Regular;
      font-size: @rem14;
      color: @yn-component-background;
    }
    .header-icon {
      margin-left: auto;
      display: flex;
      align-items: center;
      .search-icon {
        color: @yn-scrollbar-color-hover;
        font-size: @rem16;
        margin-left: @yn-margin-s;
        cursor: pointer;
      }
    }
  }
}
</style>
