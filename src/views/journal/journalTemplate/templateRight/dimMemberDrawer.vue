<template>
  <yn-drawer
    title="查看成员"
    placement="right"
    closable
    :width="400"
    wrapClassName="dim-member-list"
    :visible="drawerVisible"
    @close="onClose"
  >
    <yn-spin :spinning="spinning">
      <div class="drawer-content" :style="{ maxHeight: `${contentHeight}px` }">
        <p class="member-title">
          当前模板选择的“<span class="member-dim">{{ drawerInfo.title }} </span>
          ”成员如下：
        </p>
        <p v-for="item in memberList" :key="item.objectId" class="member-name">
          {{ item.dimMemberName }}
        </p>
      </div>
    </yn-spin>
    <!-- 当 ui 给的 和 ux 不一致的时候 一定一定要让他们确定到底以谁为准 -->
    <!-- 就这玩意ui说根据平台为准，ui规范 ux规范上都加的按钮，结果ux提了个bug -->
    <!-- <div class="footer-btn">
      <yn-button type="primary" @click="onClose">
        关闭
      </yn-button>
    </div> -->
  </yn-drawer>
</template>

<script>
import "yn-p1/libs/components/yn-drawer/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-spin/";
import { formatRequestParams } from "@/utils/journal.js";

import commonService from "@/services/common";

export default {
  props: {
    drawerVisible: {
      type: Boolean,
      default: false
    },
    drawerInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      memberList: [],
      contentHeight: 0,
      spinning: false
    };
  },
  watch: {
    drawerVisible: {
      handler(newV) {
        if (newV) {
          this.getPageHeight();
          this.getAllMember();
        }
      }
    }
  },
  methods: {
    // 获取全部成员
    getAllMember() {
      const { dimId, exp } = this.drawerInfo;
      const dimMemberExps = formatRequestParams(exp);
      if (
        !dimId ||
        !dimMemberExps ||
        Object.keys(JSON.parse(dimMemberExps)).length === 0
      ) {
        this.memberList = [];
        return;
      }
      const p = {
        expDtoList: [
          {
            dimId,
            dimMemberExps
          }
        ]
      };
      this.spinning = true;
      // const methodName =
      //   dimCode === "Audittrail" ? "getAudittraiMembers" : "getExpMember";
      commonService("getAudittraiMembers", p).then(res => {
        this.memberList = res.data[0].members;
        this.spinning = false;
      });
    },
    onClose() {
      this.$emit("update:drawerVisible", false);
    },
    getPageHeight() {
      const pageH = window.innerHeight - 93;
      this.contentHeight = pageH;
    }
  }
};
</script>

<style lang="less">
.dim-member-list {
  .ant-drawer-content {
    overflow: inherit;
  }
  .ant-drawer-body {
    padding: 0;
  }
}
</style>
<style lang="less" scoped>
.drawer-content {
  padding: @rem24 @rem24 0 @rem24;
  overflow-y: scroll;
  .member-title {
    color: @yn-text-color-secondary;
    .member-dim {
      color: @yn-text-color;
    }
  }
}
.footer-btn {
  width: 100%;
  position: absolute;
  bottom: 0;
  height: 3rem;
  line-height: 3rem;
  text-align: right;
  padding-right: @yn-padding-xxl;
  background: @yn-component-background;
  border-top: 1px solid @yn-border-color-base;
}
</style>
