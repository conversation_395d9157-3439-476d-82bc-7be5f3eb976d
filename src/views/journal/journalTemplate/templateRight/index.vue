<template>
  <yn-spin :spinning="loading">
    <div class="template-right">
      <div v-if="treeNode && treeNode.isLeaf" class="right-container">
        <div class="container-header">
          <div class="header-title">
            <div v-if="isEditName" class="header-input">
              <yn-input
                id="nameInput"
                v-model="templateName"
                :allowClear="false"
                @change="addSaveEventCb"
                @blur="closeInput('name')"
              />
            </div>
            <div v-else class="title-content">
              <span
                :class="[
                  'template-name',
                  isEditing ? '' : 'template-name-view'
                ]"
              >
                {{ templateName }}
              </span>
              <img
                v-if="isEditing"
                class="iconBtn icon-wenjianjia"
                src="@/image/edit-pan.svg"
                alt=""
                @click="editHeader('name')"
              />
            </div>
          </div>
          <div class="header-desc">
            <span
              v-tooltip="{
                visibleOnOverflow: true,
                title: templateInfo.cubeCode
              }"
              class="desc-left left-code"
            >
              {{ templateInfo.cubeCode }}
            </span>
            <yn-divider type="vertical" />
            <div class="desc-right">
              <div v-if="isEditDesc" class="header-input header-input-desc">
                <yn-input
                  id="descInput"
                  v-model="templateDesc"
                  :allowClear="false"
                  @change="addSaveEventCb"
                  @blur="closeInput('desc')"
                />
              </div>
              <div v-else class="title-content">
                <span class="template-desc">{{
                  templateDesc || "暂无说明"
                }}</span>
                <img
                  v-if="isEditing"
                  slot="image"
                  class="iconBtn icon-wenjianjia"
                  src="@/image/edit-pan.svg"
                  alt=""
                  @click="editHeader('desc')"
                />
              </div>
              <template>
                <!-- <yn-divider type="vertical" /> -->
                <span class="desc-left">
                  {{ templateInfo.updateBy }}
                  {{ templateInfo.updateDate }} 修改
                </span>
              </template>
            </div>
          </div>

          <yn-button
            v-if="!isEditing"
            class="btn-edit"
            type="primary"
            @click="() => (isEditing = !isEditing)"
          >
            编辑
          </yn-button>
        </div>
        <yn-divider class="header-divider" />
        <div class="container-content">
          <div
            :class="[
              'content-form',
              isEditing ? 'content-form-edit' : 'content-form-noEdit'
            ]"
          >
            <div class="show-dim">
              <span class="dim-title">
                <span class="title-one">展示的维度</span>
                <span v-show="isEditing" class="title-two">
                  可“
                  <span class="title-drag">拖拽 </span>
                  ”成员至不展示维度，成员在日记账详情不展示
                </span>
              </span>
              <div class="show-dim-form">
                <div>
                  <span class="title-tag">基础信息</span>
                  <yn-form
                    :colon="false"
                    :form="showBaseForm"
                    v-bind="formLayout"
                    :class="[
                      isEditing ? 'form-item-mb' : 'form-item-mb-detail'
                    ]"
                  >
                    <vuedraggable
                      id="baseInfo"
                      v-model="baseInfo"
                      class="hidden-items"
                      dragClass="dragging-item"
                      draggable=".drag-item"
                      handle=".drag-item label"
                      group="dimList"
                      :emptyInsertThreshold="5"
                      :move="onMove"
                      @start="e => handlerDropStart(e, 'baseInfo')"
                      @end="e => handlerDropEnd(e, 'baseInfo')"
                      @change="addSaveEventCb"
                    >
                      <yn-form-item
                        v-for="(item, index) in baseInfo"
                        :key="item.objectId"
                        :label="item.label"
                        style="width: 50%"
                        :class="{
                          'drag-item': isEditing && !cantDrag(item.key),
                          'form-item-edit': isEditing,
                          'form-item-preview': !isEditing
                        }"
                      >
                        <div v-if="setBaseDimDisabled(item.objectId)">
                          <yn-select-tree
                            v-if="isEditing"
                            :disabled="setBaseDimDisabled(item.objectId)"
                            :value="
                              item.members.dimMemberRealName
                                ? item.members.dimMemberRealName
                                : '-'
                            "
                          />
                          <span v-else>
                            {{
                              item.members.dimMemberRealName
                                ? item.members.dimMemberRealName
                                : "-"
                            }}
                          </span>
                        </div>
                        <ShowDimListInput
                          v-if="!setBaseDimDisabled(item.objectId) && isEditing"
                          v-decorator="[
                            item.dimCode,
                            {
                              initialValue: Array.isArray(item.members)
                                ? ''
                                : item.members,
                              rules: [{ required: true, message: '请选择' }]
                            }
                          ]"
                          dynamicOrStatic="dynamic"
                          :ifEmptySelectedAll="false"
                          :filterSharedMember="true"
                          :tipsWord="tipsWord"
                          :dimInfo="item.dimInfo"
                          @getExp="
                            val => getExp(val, index, 'baseInfo', item.dimCode)
                          "
                        />
                        <span
                          v-if="
                            !isEditing && !setBaseDimDisabled(item.objectId)
                          "
                          :class="[
                            'text-hidden',
                            item.members ? 'see-member-color' : ''
                          ]"
                          @click="seeAllMember(item)"
                        >
                          {{ item.members ? "点击查看成员" : "-" }}
                        </span>
                      </yn-form-item>
                    </vuedraggable>
                  </yn-form>
                </div>
                <yn-divider type="horizontal" />
                <div>
                  <span class="title-tag">明细信息列维</span>
                  <yn-form
                    ref="detailInfo"
                    :form="showDetailForm"
                    :colon="false"
                    :hideRequiredMark="!isEditing"
                    v-bind="formLayout"
                    :class="[
                      isEditing ? 'form-item-mb' : 'form-item-mb-detail'
                    ]"
                  >
                    <vuedraggable
                      id="detailInfo"
                      v-model="detailInfo"
                      class="hidden-items"
                      draggable=".drag-item"
                      dragClass="dragging-item"
                      handle=".drag-item label"
                      group="dimList"
                      :emptyInsertThreshold="5"
                      :move="onMove"
                      @start="e => handlerDropStart(e, 'detailInfo')"
                      @end="e => handlerDropEnd(e, 'detailInfo')"
                      @change="addSaveEventCb"
                    >
                      <template v-if="detailInfo.length">
                        <yn-form-item
                          v-for="(item, index) in detailInfo"
                          :key="item.objectId"
                          :label="item.label"
                          style="width: 50%"
                          :class="{
                            'drag-item': isEditing,
                            'form-item-edit': isEditing,
                            'form-item-preview': !isEditing
                          }"
                        >
                          <ShowDimListInput
                            v-if="isEditing"
                            :key="index"
                            v-decorator="[
                              item.dimCode,
                              {
                                initialValue: Array.isArray(item.members)
                                  ? ''
                                  : item.members,
                                rules: [{ required: true, message: '请选择' }]
                              }
                            ]"
                            :dimInfo="item.dimInfo"
                            :ifEmptySelectedAll="false"
                            :filterSharedMember="true"
                            :tipsWord="tipsWord"
                            dynamicOrStatic="dynamic"
                            @getExp="
                              val =>
                                getExp(val, index, 'detailInfo', item.dimCode)
                            "
                          />
                          <span
                            v-if="!isEditing"
                            :class="[
                              'text-hidden',
                              item.members ? 'see-member-color' : ''
                            ]"
                            @click="seeAllMember(item)"
                          >
                            {{ item.members ? "点击查看成员" : "-" }}
                          </span>
                        </yn-form-item>
                      </template>
                      <template v-if="!detailInfo.length">
                        <div
                          :class="[
                            'detail-info',
                            dragging ? 'border-dashed' : ''
                          ]"
                        >
                          <span class="info-empty">
                            当前列表区域成员为空，可 “
                            <span class="empty-drag"> 拖拽</span>”
                            其它区域维度成员添加
                          </span>
                        </div>
                      </template>
                    </vuedraggable>
                  </yn-form>
                </div>
              </div>
            </div>
            <yn-divider type="horizontal" />
            <div class="hidden-dim">
              <span class="dim-title">
                <span class="title-one">不展示的维度</span>
                <span v-show="isEditing" class="title-two">
                  可“<span class="title-drag">
                    拖拽
                  </span>
                  ”成员至展示维度，成员在日记账详情展示
                </span>
              </span>
              <yn-form
                v-bind="formLayout"
                :hideRequiredMark="!isEditing"
                :form="hiddenForm"
                :colon="false"
                :class="[isEditing ? 'form-item-mb' : 'form-item-mb-detail']"
              >
                <vuedraggable
                  id="notDisplayedInfo"
                  v-model="notDisplayedInfo"
                  class="hidden-items"
                  draggable=".drag-item"
                  dragClass="dragging-item"
                  handle=".drag-item label"
                  group="dimList"
                  :emptyInsertThreshold="5"
                  :move="onMove"
                  @start="e => handlerDropStart(e, 'notDisplayedInfo')"
                  @end="e => handlerDropEnd(e, 'notDisplayedInfo')"
                  @change="addSaveEventCb"
                >
                  <template v-if="notDisplayedInfo.length">
                    <yn-form-item
                      v-for="item in notDisplayedInfo"
                      :key="item.objectId"
                      :label="item.label"
                      style="width: 50%"
                      :class="[
                        !isEditing
                          ? 'edit-label-color form-item-preview'
                          : 'drag-item'
                      ]"
                    >
                      <yn-select-tree
                        v-if="isEditing && item.dimCode === DIMCODE_AUDITTRAIL"
                        v-decorator="[
                          item.dimCode,
                          {
                            initialValue: Array.isArray(item.members)
                              ? `${item.members[0].dimMemberName}-${item.members[0].memberId}`
                              : '',
                            rules: [{ required: true, message: '请选择' }]
                          }
                        ]"
                        :datasource="notDisplayList[item.dimCode]"
                        searchMode="single"
                        :allowClear="false"
                        @change="addSaveEventCb"
                      />
                      <AsynSelectDimMember
                        v-if="isEditing && item.dimCode !== DIMCODE_AUDITTRAIL"
                        v-decorator="[
                          item.dimCode,
                          {
                            initialValue: Array.isArray(item.members)
                              ? `${item.members[0].dimMemberName}-${item.members[0].memberId}`
                              : '',
                            rules: [{ required: true, message: '请选择' }]
                          }
                        ]"
                        :dimCode="item.dimCode"
                        forceRender
                        class="dimSelectTree"
                        :nonleafselectable="false"
                        :allowClear="false"
                        :formatter="formatter"
                        :unformatter="unformatter"
                        placeholder="请选择"
                        @change="addSaveEventCb"
                      />
                      <span v-if="!isEditing" class="text-hidden">
                        {{
                          (item.members && item.members[0].dimMemberName) || "-"
                        }}
                      </span>
                    </yn-form-item>
                  </template>
                  <template v-if="!notDisplayedInfo.length">
                    <div
                      :class="['detail-info', dragging ? 'border-dashed' : '']"
                    >
                      <span class="info-empty">
                        当前列表区域成员为空，可 “
                        <span class="empty-drag">拖拽</span>”
                        其它区域维度成员添加
                      </span>
                    </div>
                  </template>
                </vuedraggable>
              </yn-form>
            </div>
          </div>
          <div v-show="isEditing" class="no-content"></div>
          <div v-if="isEditing" class="footer">
            <yn-button class="btn" @click="onUpdate">取消</yn-button>
            <yn-button
              class="btn"
              type="primary"
              :loading="btnLoading"
              @mousedown="onSave"
            >
              保存
            </yn-button>
          </div>
        </div>
      </div>
    </div>
    <DimMemberDrawer
      :drawerVisible.sync="drawerVisible"
      :drawerInfo="drawerInfo"
    />
  </yn-spin>
</template>

<script>
import svgEdit from "@/image/edit-pan.svg";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-form-item/";
import vuedraggable from "vuedraggable";
import cloneDeep from "lodash/cloneDeep";
import DimMemberDrawer from "./dimMemberDrawer.vue";
import ShowDimListInput from "../../../../components/hoc/ShowDimListInput.vue";
import AsynSelectDimMember from "../../../../components/hoc/asynSelectDimMember";
import { TRANSFER_TIPS_WORD } from "@/constant/common.js";

import {
  addParamsToData,
  handleDimInfoData,
  formatRequestParams
} from "@/utils/journal.js";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import journalService from "@/services/journal";
import DIM_INFO from "@/constant/dimMapping";
const saveTips = "您要保存对“日记账模板”所做的更改吗？";

// 审计线索dimCode
const DIMCODE_AUDITTRAIL = "Audittrail";

// 默认基础需要禁用的维度
const baseDimsList = [
  DIM_INFO.Version,
  DIM_INFO.Year,
  DIM_INFO.Period,
  DIM_INFO.Scope,
  DIM_INFO.Entity
];

// 禁止拖动的维度
const baseNotDragDim = [DIM_INFO.Version, DIM_INFO.Year, DIM_INFO.Period];
export default {
  components: {
    vuedraggable,
    ShowDimListInput,
    DimMemberDrawer,
    AsynSelectDimMember
  },
  props: {
    treeNode: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      svgEdit,
      formLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 }
      },
      showBaseForm: this.$form.createForm(this, "showBaseForm"),
      showDetailForm: this.$form.createForm(this, "showDetailForm"),
      hiddenForm: this.$form.createForm(this, "hiddenForm"),
      baseInfo: [],
      detailInfo: [],
      notDisplayedInfo: [],
      tempVisible: false,
      isEditName: false,
      isEditDesc: false,
      isEditing: false,
      loading: false,
      btnLoading: false,
      drawerVisible: false,
      drawerInfo: {},
      templateInfo: {},
      templateName: "",
      templateDesc: "",
      searchType: "", // 树搜索的类型 dimCode
      searchResult: [], // 树搜索的结果
      dragging: false,
      dragInfo: {},
      oldIndex: "",
      enterType: "",
      notDisplayList: {},
      DIMCODE_AUDITTRAIL, // 审计线索 dimCode
      directSave: false, // 是否直接保存。
      tipsWord: TRANSFER_TIPS_WORD.shareMember()
    };
  },
  watch: {
    treeNode: {
      deep: true,
      async handler(newVal, oldVal) {
        if (newVal) {
          this.templateName = newVal.name;
          this.templateInfo.cubeCode = newVal.cubeCode;
          this.templateDesc = newVal.remark;
          if (newVal.isNewTemp) {
            this.isEditing = newVal.isNewTemp;
          }
          if (newVal.isLeaf) {
            if (oldVal && oldVal.objectId === newVal.objectId) return;
            this.resetAllForm();
            await this.getTemplateInfo(newVal.objectId, this.setPovValue);
          }
        }
      }
    },
    notDisplayedInfo: {
      // 此处改成 只有 审计线索 调下面的接口。其他的都在 下拉组件。
      // methodName 可以不要了 只有 queryAudittrailMembers
      async handler(nv) {
        const dimCodeList = nv.map(item => item.dimCode);
        for (let i = 0; i < dimCodeList.length; i++) {
          const cur = dimCodeList[i];
          if (cur === DIMCODE_AUDITTRAIL && !this.notDisplayList[cur]) {
            const methodName =
              dimCodeList[i] === "Audittrail"
                ? "queryAudittrailMembers"
                : "getDimMembersTree";
            await journalService(methodName, dimCodeList[i]).then(res => {
              const addKey = addParamsToData(res.data);
              this.$set(this.notDisplayList, dimCodeList[i], addKey);
            });
          }
        }
      }
    }
  },
  methods: {
    // 重置表单
    resetAllForm() {
      ["showBaseForm", "showDetailForm", "hiddenForm"].forEach(item => {
        this[item].resetFields();
      });
    },
    getExp(val, index, type, dimCode) {
      const value = Object.values(val).filter(Boolean).length ? val : {};
      const copyInfo = JSON.parse(JSON.stringify(this[type]));
      copyInfo[index].dimInfo.members = value;
      copyInfo[index].members = formatRequestParams(value);
      this.$set(this, type, copyInfo);
      const form = type === "baseInfo" ? "showBaseForm" : "showDetailForm";
      this[form].setFieldsValue({
        [dimCode]: formatRequestParams(value)
      });
      this.addSaveEventCb();
    },
    setBaseDimDisabled(key) {
      return baseDimsList.includes(key);
    },
    cantDrag(key) {
      return baseNotDragDim.includes(key);
    },
    // 模板设置 pov 的值
    setPovValue() {
      this.selectUserPov(data => {
        this.baseInfo.forEach(info => {
          if (["Version", "Year", "Period"].includes(info.dimCode)) {
            const key = info.dimCode.toLowerCase();
            info.members = data[key];
          }
        });
        this.backUpInfo.copyBaseInfo = cloneDeep(this.baseInfo);
      });
    },
    // 获取模板信息
    async getTemplateInfo(groupNodeId, cb) {
      this.loading = true;
      await journalService("selectGroupNode", { groupNodeId }).then(res => {
        this.templateInfo = res.data.data;
        this.templateName = this.templateInfo.name;
        this.templateDesc = this.templateInfo.remark;
        this.baseInfo = handleDimInfoData(
          this.templateInfo.baseInfo,
          "baseInfo"
        );
        this.detailInfo = handleDimInfoData(
          this.templateInfo.detailInfo,
          "detailInfo"
        );
        this.notDisplayedInfo = handleDimInfoData(
          this.templateInfo.notDisplayedInfo
        );
        this.backUpInfo = cloneDeep({
          copyBaseInfo: this.baseInfo,
          copyDetailInfo: this.detailInfo,
          copyNotDisplayInfo: this.notDisplayedInfo,
          copyTemplateName: this.templateName,
          copyTemplateDesc: this.templateDesc
        });
        this.loading = false;
        cb && cb();
      });
    },
    onUpdate() {
      this.savePromptMixin().then(() => {
        this.hanleCancelSave();
      });
    },
    hanleCancelSave() {
      this.isEditing = false;
      this.templateName = this.templateInfo.name;
      this.templateDesc = this.templateInfo.remark;
      const {
        copyBaseInfo,
        copyDetailInfo,
        copyNotDisplayInfo
      } = this.backUpInfo;
      this.baseInfo = copyBaseInfo;
      this.detailInfo = copyDetailInfo;
      this.notDisplayedInfo = JSON.parse(JSON.stringify(copyNotDisplayInfo));
      this.clearCommonSaveEventsMixin();
    },
    formatter(item) {
      const { dimMemberRealName, objectId } = item;
      return `${dimMemberRealName}-${objectId}`;
    },
    unformatter(key) {
      const arr = key.split("-");
      return arr[arr.length - 1];
    },
    handleParams(arr, params, type) {
      if (!Array.isArray(arr)) return [];
      return arr.map(item => {
        if (!item) return [];
        let members;
        if (type === "notDisplay") {
          const arr = params[item.dimCode].split("-");
          const arrName = arr.slice(0, arr.length - 1);
          members = [
            {
              dimMemberName: arrName.join("-"),
              memberId: arr[arr.length - 1],
              dimCode: item.dimCode
            }
          ];
        } else {
          members = Object.keys(params).length ? params[item.dimCode] : "";
        }
        return {
          dimId: item.dimId,
          dimCode: item.dimCode,
          dimNameId: item.dimNameId,
          dimMultiName: item.dimMultiName || item.dimName,
          members
        };
      });
    },
    getFormValue(formName) {
      return new Promise(resolve => {
        this[formName].validateFields((err, value) => {
          if (!err) {
            resolve(value);
          }
        });
      });
    },
    addSaveEventCb() {
      this.addCallBackFnMixin(this.onSave, saveTips, this.hanleCancelSave);
    },
    onSave() {
      if (document.activeElement.tagName === "INPUT") {
        // 没失去焦点直接点保存
        this.directSave = true;
      }
      if (this.templateName.length > 64 || this.templateDesc.length > 50) {
        this.directSave = false;
        return;
      }
      const basePro = this.getFormValue("showBaseForm");
      const detailInfoPro = this.getFormValue("showDetailForm");
      const notDisplayedInfoPro = this.getFormValue("hiddenForm");
      return Promise.all([basePro, detailInfoPro, notDisplayedInfoPro]).then(
        res => {
          const [baseParams, detailParams, notDisplayParams] = res;
          const reqParams = {
            templateId: this.treeNode.templateId,
            name: this.templateName,
            remark: this.templateDesc,
            baseInfo: this.handleParams(this.baseInfo, baseParams),
            detailInfo: this.handleParams(this.detailInfo, detailParams),
            notDisplayedInfo: this.handleParams(
              this.notDisplayedInfo,
              notDisplayParams,
              "notDisplay"
            )
          };
          this.btnLoading = true;
          journalService("updateJournalTemplate", reqParams)
            .then(res => {
              // 后端返回的错误信息
              if (res.data.data) {
                UiUtils.errorMessage("模板名称已存在");
                return;
              }
              UiUtils.successMessage("保存成功");
              this.isEditing = false;
              this.clearCommonSaveEventsMixin();
              // 如果修改了名称/说明 则 同步树上的名称
              if (this.templateName !== this.backUpInfo.copyTemplateName) {
                this.$emit("updateGroupNode", {
                  parentId: this.treeNode.parentId,
                  objectId: this.treeNode.objectId,
                  remark: this.templateDesc,
                  name: this.templateName
                });
              }
            })
            .finally(() => {
              this.btnLoading = false;
              this.directSave = false;
              this.getTemplateInfo(this.treeNode.objectId, this.setPovValue);
            });
        }
      );
    },
    editHeader(type) {
      const typeCapitalized = type.charAt(0).toUpperCase() + type.slice(1);
      this[`isEdit${typeCapitalized}`] = true;
      this.$nextTick(() => {
        const editInput = this.$el.querySelector(`#${type}Input`);
        editInput.focus();
      });
    },
    closeInput(type) {
      const w = type.charAt(0).toUpperCase() + type.slice(1);
      this[`isEdit${w}`] = false;
      if (type === "name") {
        if (!this.templateName) {
          UiUtils.errorMessage("模板名称不可为空");
          this.templateName = this.backUpInfo.copyTemplateName;
          return;
        }
        if (this.templateName.length > 64) {
          UiUtils.errorMessage("字段长度超过64位限制，请检查。");
          this.templateName = this.backUpInfo.copyTemplateName;
          return;
        }
        if (this.directSave) return;
        journalService("checkTemplateName", {
          templateId: this.treeNode.templateId,
          templateName: this.templateName
        }).then(res => {
          if (res.data.data) {
            UiUtils.errorMessage(res.data.data);
            this.templateName = this.backUpInfo.copyTemplateName;
          } else {
            this.addSaveEventCb();
          }
        });
      }
      if (type === "desc" && this.templateDesc.length > 50) {
        UiUtils.errorMessage("字段长度超过50位限制，请检查。");
        this.templateDesc = this.backUpInfo.copyTemplateDesc;
        return;
      }
    },
    seeAllMember(item) {
      if (!item.members) return;
      this.drawerVisible = true;
      const { dimId, dimMultiName: title, dimCode } = item;
      Object.assign(this.drawerInfo, {
        dimId,
        exp: item.dimInfo.members,
        title,
        dimCode
      });
    },
    handlerDropStart(e, type) {
      const { oldIndex } = e;
      this.dragging = true;
      this.dragInfo = {
        oldIndex,
        type,
        dragItem: e.item._underlying_vm_
      };
    },
    handlerDropEnd(e, type) {
      this.dragging = false;
      const dragItem = this.dragInfo.dragItem;
      const source = this.dragInfo.type;
      const target = this.enterType;
      if (this[target] && this[target].length === 0) {
        this[target].push(dragItem);
        this[source] = this[source].filter(
          item => item.objectId !== dragItem.objectId
        );
      }
    },
    onMove(e) {
      this.enterType = e.related.id;
      if (this[this.enterType] && this[this.enterType].length === 0) {
        return false;
      }
      return true;
    }
  }
};
</script>
<style lang="less">
.ant-spin-nested-loading {
  height: 100%;
  .ant-spin-container {
    height: 100%;
  }
}
</style>
<style lang="less" scoped>
.iconBtn {
  font-size: @rem16;
  cursor: pointer;
  margin-left: @yn-padding-s;
}
.see-member-color {
  color: @yn-primary-color !important;
  font-weight: 600;
  cursor: pointer;
}
.template-right {
  width: 100%;
  height: 100%;
  background: @yn-component-background;
  .drag-item {
    /deep/.ant-form-item-label label {
      cursor: move;
    }
    /deep/.ant-form-item-label {
      height: @rem32;
      line-height: @rem32;
    }
    /deep/.ant-form-item-label:hover {
      background: @yn-table-header-bg;
    }
  }
  .right-container {
    width: 100%;
    height: 100%;
    background: @yn-background-color;
    .container-header {
      background: @yn-component-background;
      padding: 1rem 1rem;
      height: 5.5rem;
      box-sizing: border-box;
      .header-title,
      .header-desc {
        .desc-left,
        .template-desc {
          color: @yn-label-color;
        }
        .header-input-desc {
          width: calc(100% - 20rem);
        }
        .header-input {
          /deep/ .ant-input {
            border: none;
            caret-color: @yn-primary-color;
            font-weight: 600;
            padding: 0;
          }
          /deep/ input:focus {
            box-shadow: none;
          }
        }
      }
      .header-title {
        .title-content {
          height: @rem32;
          line-height: @rem32;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          .template-name {
            font-size: @rem14;
            color: @yn-text-color;
            font-weight: 600;
          }
          .template-name-view {
            display: inline-block;
            width: calc(100% - 5rem);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      .header-desc {
        height: @rem32;
        display: flex;
        align-items: center;
        color: @yn-label-color;
        font-size: @rem14;
        /deep/.ant-divider-vertical {
          margin: 0 @yn-margin-xl;
        }
        .desc-right {
          width: calc(100% - 7.25rem);
          display: flex;
          align-items: center;
          .title-content {
            width: calc(100% - 18.75rem);
            display: flex;
            align-items: center;
            .template-desc {
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .desc-left {
            margin-left: auto;
          }
          .header-input {
            /deep/ .ant-input {
              font-weight: 500;
              color: @yn-label-color;
            }
          }
        }
      }
      .left-code {
        display: inline-block;
        width: 5.25rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .btn-edit {
        position: absolute;
        float: right;
        top: @rem8;
        right: 1rem;
        width: 5rem;
      }
    }
    .header-divider {
      width: calc(100% - 2rem);
      min-width: calc(100% - 2rem);
      margin: -0.25rem 1rem 0;
    }
    .container-content {
      width: 100%;
      box-sizing: border-box;
      height: calc(100% - 5.5rem);
      background: @yn-component-background;
      position: relative;

      /deep/.ant-form-item-label > label {
        color: @yn-text-color-secondary;
      }
      .content-form-edit {
        height: calc(100% - 3.75rem);
        overflow-y: scroll;
      }
      .content-form-noEdit {
        height: 100%;
        overflow: scroll;
      }
      .content-form {
        width: 100%;
        padding: 0.5rem 1rem 0;
        box-sizing: border-box;
        &::-webkit-scrollbar {
          width: 0.3125rem;
        }
        .dim-title {
          display: inline-block;
          width: 100%;
          .title-one {
            margin-right: @yn-margin-xl;
            font-size: @rem14;
            color: @yn-text-color-secondary;
            font-weight: 600;
          }
          .title-two {
            font-size: @rem14;
            color: @yn-label-color;
            .title-drag {
              color: @yn-warning-color;
            }
          }
        }
        .form-item-mb {
          /deep/.ant-form-item {
            margin-bottom: @rem24;
          }
        }
        .form-item-mb-detail {
          /deep/.ant-form-item {
            margin-bottom: @rem16;
          }
        }
        .show-dim {
          .show-dim-form {
            .title-tag {
              display: inline-block;
              margin-top: @yn-margin-xl;
              color: @yn-text-color;
            }
            /deep/.ant-divider-horizonta {
              margin: @yn-margin-xl 0;
            }
          }
        }
        .hidden-dim {
          padding-top: @yn-padding-xl;
        }
        .hidden-dim,
        .show-dim {
          .hidden-items {
            margin-top: @yn-margin-xl;
            display: flex;
            flex-wrap: wrap;
            .dimListInput {
              width: 100%;
            }
          }
          .dragging-item {
            height: 3rem !important;
            display: flex !important;
            padding: @yn-padding-s;
            align-items: center;
            font-family: PingFangSC-Regular;
            font-size: @rem14;
            color: @yn-text-color-secondary;
            font-weight: 400;
            background: @yn-background-color-light;
            opacity: 1 !important;
          }
        }
        .detail-info {
          width: 100%;
          height: 6rem;
          margin-top: @yn-margin-l;
          background: @yn-background-color;
          line-height: 6rem;
          text-align: center;
          .info-empty {
            font-size: @rem14;
            color: @yn-label-color;
            .empty-drag {
              color: @yn-warning-color;
            }
          }
        }
      }
      .no-content {
        width: 100%;
        height: @rem8;
        background: @yn-background-color;
      }
      .border-dashed {
        border: 1px dashed @yn-chart-1;
      }
      .footer {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 3.25rem;
        line-height: 3.25rem;
        border-top: 1px solid @yn-background-color;
        text-align: right;
        padding-right: @rem32;
        button {
          &:first-child {
            margin-right: @yn-margin-s;
          }
        }
        .btn {
          min-width: 80px;
        }
      }
      /deep/.ant-divider-horizontal {
        margin: 0;
      }
      .form-item-edit {
        height: @rem32;
        line-height: @rem32;
        .dim-list-input {
          width: 100%;
        }
      }
      .form-item-preview {
        height: @rem32;
        line-height: @rem32;
        margin-bottom: @yn-margin-xl;
      }
    }
  }
  .text-hidden {
    display: inline-block;
    width: 100%;
    font-size: @rem14;
    color: @yn-text-color;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
