<template>
  <div class="journal-template cs-container">
    <div class="template-title cs-header-single">
      日记账模板
    </div>
    <div class="template-container cs-body">
      <yn-lc-layout iconPosition="center" class="template-container-content">
        <div slot="left" class="slot-content">
          <TemplateLeft ref="leftTree" @setSelectTreeNode="setSelectTreeNode" />
        </div>
        <div slot="center" class="slot-content">
          <TemplateRight
            v-show="treeNode"
            :treeNode="treeNode"
            @updateGroupNode="updateGroupNode"
          />
          <Empty v-show="!treeNode" @btnClick="addTemplate" />
        </div>
      </yn-lc-layout>
    </div>
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-lc-layout/";

import TemplateLeft from "./templateLeft";
import TemplateRight from "./templateRight";
import Empty from "../empty.vue";
import defaultParams from "@/mixin/defaultParams";

export default {
  components: {
    Empty,
    TemplateLeft,
    TemplateRight
  },
  mixins: [defaultParams],
  data() {
    return {
      treeNode: null
    };
  },
  methods: {
    setSelectTreeNode(treeNode, len = true) {
      this.treeNode = treeNode;
    },
    addTemplate() {
      this.$refs.leftTree && this.$refs.leftTree.handleMenuClick("temp", true);
    },
    updateGroupNode(params) {
      this.$refs.leftTree && this.$refs.leftTree.updateNameAndDesc(params);
    }
  }
};
</script>

<style lang="less" scoped>
.journal-template {
  width: 100%;
  height: 100%;
  .template-title {
    width: 100%;
    height: @rem40;
    line-height: @rem40;
    font-size: @rem16;
    padding-left: @rem16;
    background: @yn-component-background;
    color: @yn-heading-color;
  }

  .template-container {
    width: 100%;
    height: calc(100% - 2.75rem);
  }
  .slot-content {
    width: 100%;
    height: 100%;
  }
}
</style>
