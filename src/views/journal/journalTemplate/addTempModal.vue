<template>
  <div>
    <yn-modal
      :visible="visible"
      :title="tempObj.title ? '编辑模板' : '新增模板'"
      okText="保存"
      @cancel="cancelEvent"
    >
      <template slot="footer">
        <yn-button key="cancel" @click="cancelEvent">
          取消
        </yn-button>
        <yn-button
          key="submit"
          type="primary"
          :loading="btnLoading"
          @click="okEvent"
        >
          保存
        </yn-button>
      </template>
      <yn-spin :spinning="loading">
        <yn-form :form="addTmpForm" v-bind="formLayout">
          <yn-form-item label="模板名称" :colon="false" class="temp-name">
            <yn-input
              v-decorator="[
                'name',
                {
                  initialValue: tempObj.title ? tempObj.title : '',
                  rules: [
                    { required: true, message: '请输入' },
                    { max: 64, message: '字段长度超过64位限制, 请检查' }
                  ]
                }
              ]"
              placeholder="请输入"
              @change="changeVal"
            >
              <yn-icon
                slot="suffix"
                type="global"
                class="iconBtn"
                @click="openMultilingual"
              />
            </yn-input>
            <div v-if="sameNameTip" class="same-name">
              当前名称已被占用，请修改
            </div>
          </yn-form-item>
          <yn-form-item label="模板分组" :colon="false" class="temp-group">
            <yn-select
              v-decorator="[
                'groupId',
                {
                  initialValue: tempObj.groupId
                    ? tempObj.groupId
                    : tempObj.parentId,
                  rules: [{ required: true, message: '请选择' }]
                }
              ]"
              showSearch
              :filterOption="filterOption"
            >
              <yn-select-option v-for="item in groupList" :key="item.objectId">
                {{ item.name }}
              </yn-select-option>
            </yn-select>
          </yn-form-item>
          <yn-form-item :colon="false" label="写入Cube">
            <yn-select
              v-decorator="[
                'cubeCode',
                {
                  initialValue: tempInfo.cubeCode || defaultCubeCode, // 暂时需求只能创建 sys_10 写死 item.cubeCode
                  rules: [{ required: true, message: '请选择' }]
                }
              ]"
              placeholder="请选择"
              showSearch
              :disabled="false"
              :filterOption="filterOption"
            >
              <!-- :disabled="tempObj.title ? true : false" -->
              <yn-select-option v-for="item in cubeList" :key="item.cubeCode">
                {{ item.cubeCode }}
              </yn-select-option>
            </yn-select>
          </yn-form-item>
          <yn-form-item label="说明">
            <yn-textarea
              v-decorator="[
                'remark',
                {
                  initialValue: tempInfo.remark ? tempInfo.remark : '',
                  rules: [
                    { required: false, max: 50, message: '输入长度超出限制' }
                  ]
                }
              ]"
              :allowClear="false"
              type="textarea"
              placeholder="请输入"
              :autoSize="{ minRows: 3, maxRows: 8 }"
              @change="onChange($event)"
            />
            <div class="text-count">{{ descriptionLen }}/50</div>
          </yn-form-item>
        </yn-form>
      </yn-spin>
    </yn-modal>
    <!-- <Multilanguage
      :languageVisible="languageVisible"
      @cancelMultilanguage="cancelMultilanguage"
    /> -->
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-modal/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-option/";
import "yn-p1/libs/components/yn-textarea/";

// import Multilanguage from "@/components/multilanguage";
import UiUtils from "yn-p1/libs/utils/UiUtils.js";

import journalService from "@/services/journal";
const DEFAULT_CUBE_CODE = "sys10主附表";
export default {
  // components: { Multilanguage },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tempObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      addTmpForm: this.$form.createForm(this, "addTmpForm"),
      formLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 16 }
      },
      descriptionLen: 0,
      languageVisible: false,
      cubeList: [],
      groupList: [],
      loading: false,
      btnLoading: false,
      tempInfo: {},
      sameNameTip: false,
      defaultCubeCode: DEFAULT_CUBE_CODE
    };
  },
  created() {
    this.getCubeList();
    this.getTempGroupList();
  },
  mounted() {
    this.getTemlateInfo();
  },
  methods: {
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    // 获取模板信息
    getTemlateInfo() {
      if (!this.tempObj.title) return;
      this.loading = true;
      const groupNodeId = this.tempObj.objectId;
      journalService("selectGroupNode", { groupNodeId }).then(res => {
        if (res.status === 200) {
          this.tempInfo = {
            remark: res.data.data.remark,
            cubeCode: res.data.data.cubeCode
          };
          this.descriptionLen = res.data.data.remark.length;
        }
        this.loading = false;
      });
    },
    getTempGroupList() {
      journalService("getTempGroupList").then(res => {
        if (res.status === 200) {
          this.groupList = res.data.data;
        }
      });
    },
    getCubeList() {
      this.loading = true;
      journalService("getCubeLists")
        .then(res => {
          if (res.status === 200) {
            this.cubeList = res.data;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    cancelEvent() {
      this.$emit("closeTempModal", "temp", false);
    },
    okEvent() {
      this.addTmpForm.validateFields((err, value) => {
        if (!err) {
          this.handleSave(value);
        }
      });
    },
    getCurItem(arr, id, type) {
      if (!Array.isArray(arr)) return;
      const res = arr.filter(item => item[type] === id);
      return res;
    },
    handleSave(values) {
      // 名称是必填项 可根据名称判断新增、编辑
      const type = this.tempObj.title ? "edit" : "add";
      const groupItem = this.getCurItem(
        this.groupList,
        values["groupId"],
        "objectId"
      )[0];
      const cubeId = this.getCurItem(
        this.cubeList,
        values["cubeCode"],
        "cubeCode"
      )[0].objectId;
      const addParams = {
        ...values,
        groupName: groupItem.name,
        cubeId,
        parentId: values.groupId
      };
      const methods =
        type === "edit" ? "updateJournalTemplate" : "addJournalTemplate";
      if (type === "edit") {
        addParams.templateId = this.tempObj.templateId;
      } else {
        addParams.groupParentId = values.groupId;
      }
      this.btnLoading = true;
      journalService(methods, addParams)
        .then(res => {
          if (res.data.messageType === "ERROR") {
            this.sameNameTip = true;
          } else {
            const tip = this.tempObj.title ? "编辑成功" : "新增成功";
            UiUtils.successMessage(tip);
            const v = {
              type,
              groupId: values.groupId
            };
            if (type === "add") {
              v.objectId = res.data.data;
              v.isAdd = true;
            } else {
              v.objectId = this.tempObj.objectId;
              v.cubeCode = values.cubeCode;
              v.remark = values.remark;
            }
            this.$emit("onSave", "temp", v);
          }
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    onChange(e) {
      this.descriptionLen = e.target.value.length;
    },
    changeVal() {
      this.sameNameTip = false;
    },
    openMultilingual() {
      this.languageVisible = true;
    },
    cancelMultilanguage() {
      this.languageVisible = false;
    }
  }
};
</script>

<style lang="less"></style>
<style lang="less" scoped>
.iconBtn {
  cursor: pointer;
}
.text-count {
  height: @rem22;
  line-height: @rem22;
  position: absolute;
  top: -@rem4;
  right: @rem10;
  color: @yn-border-color-base;
}
.same-name {
  color: @yn-error-color;
  height: @rem22;
  line-height: @rem22;
}
</style>
