<template>
  <div :class="['search-list']">
    <span class="list-title cs-header-single">{{ title }}</span>
    <div class="cs-body-table">
      <yn-form
        ref="searchform"
        :form="searchForm"
        class="item-list cs-body-filter"
        v-bind="formLayout"
        :colon="false"
      >
        <yn-form-item
          v-for="item in formHandleItemList"
          v-show="item.isShow"
          :key="item.field"
          class="form-item"
          :style="responseWidth"
        >
          <span slot="label" :title="item.label">{{ item.label }}</span>
          <component
            :is="item.componentName"
            v-decorator="[
              item.field,
              {
                rules: [],
                initialValue: undefined
              }
            ]"
            v-bind="item.props"
            v-on="item.events"
          >
            <template v-if="item.type === 'yn-select'">
              <yn-select-option v-for="cur in item.options" :key="cur.type">
                {{ cur.value }}
              </yn-select-option>
            </template>
            <yn-icon
              v-if="item.type === 'yn-range-picker'"
              slot="suffixIcon"
              type="calendar"
            />
          </component>
        </yn-form-item>
        <yn-form-item class="form-item last-item">
          <div class="list-menu ">
            <yn-button class="btn-style" type="primary" @click="onSearch">
              查询
            </yn-button>
            <yn-button class="btn-reset btn-style" @click="onReset">
              重置
            </yn-button>
            <yn-icon-button
              class="icon-style"
              type="screening"
              @click="showField"
            />
            <yn-button class="fold-style " type="text" @click="expandAndShrink">
              {{ isExpand ? "收起" : "展开" }}
              <yn-icon class="expand-icon" :type="isExpand ? 'up' : 'down'" />
            </yn-button>
          </div>
        </yn-form-item>
      </yn-form>
      <yn-divider class="cs-body-divider" />
    </div>

    <ShowField
      filterOrShow="filter"
      :useType="useType"
      :fieldVisible="fieldVisible"
      :plainOptions="
        useType === 'searchFilter'
          ? journalSearchPlainOptions
          : journalSearchPlainOptions
      "
      :lastCheckedList="formHandleItemList"
      :defaultDisabled="defaultDisabled"
      @closeFieldModal="closeFieldModal"
    />
  </div>
</template>

<script>
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-form/";
import "yn-p1/libs/components/yn-input/";
import "yn-p1/libs/components/yn-button/";
import "yn-p1/libs/components/yn-select/";
import "yn-p1/libs/components/yn-select-option/";
import "yn-p1/libs/components/yn-divider/";
import "yn-p1/libs/components/yn-form-item/";
import "yn-p1/libs/components/yn-select-tree";
import "yn-p1/libs/components/yn-icon-button/";
import "yn-p1/libs/components/yn-range-picker/";
import response from "./response.js";
import throttle from "lodash/throttle";
import _lowerFirst from "lodash/lowerFirst";
import AsynSelectDimMember from "@/components/hoc/asynSelectDimMember";
import Logger from "yn-p1/libs/modules/log/logger";

import ShowField from "./filterOrShowField.vue";
import moment from "moment";
import journalService from "@/services/journal";
import { deepCompare } from "@/utils/common";

import { USER_TYPE_MAP, POV_OBJECTID_MAP, POV_PREFIX_LIST } from "./constant";

export default {
  components: { ShowField, AsynSelectDimMember },
  props: {
    isList: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: "日记账查询"
    },
    useType: {
      type: String,
      default: ""
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  inject: ["defaultParams"],
  data() {
    return {
      formHandleItemList: [],
      searchForm: null,
      formLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 }
      },
      fieldWidth: "25%",
      isExpand: true,
      showSearchField: [],
      fieldVisible: false,
      journalSearchPlainOptions: [
        {
          label: "版本",
          value: "版本",
          dimName: "版本",
          dimCode: "Version",
          type: "asyn-select-dimMember",
          field: "versionList",
          objectId: "11ec45fe70e7ec75891db3348c1632bd"
        },
        {
          label: "年",
          value: "年",
          dimName: "年",
          dimCode: "Year",
          type: "asyn-select-dimMember",
          field: "yearList",
          objectId: "11ec45fe39dcbd7d891dd934dba15806"
        },
        {
          label: "期间",
          value: "期间",
          dimName: "期间",
          dimCode: "Period",
          type: "asyn-select-dimMember",
          field: "periodList",
          objectId: "11ec45fe5b895852891d0d7e9c566254"
        },
        {
          label: "过账状态",
          value: "过账状态",
          type: "yn-select",
          field: "postStatusList",
          options: [
            {
              type: "POSTED",
              value: "已过账"
            },
            {
              type: "NOT_POSTED",
              value: "未过账"
            },
            {
              type: "POSTING_FAILED",
              value: "过账失败"
            },
            {
              type: "POSTING",
              value: "过账中"
            }
          ]
        },
        {
          label: "编码",
          value: "编码",
          type: "yn-input",
          field: "journalCode"
        },
        {
          label: "日记账名称",
          value: "日记账名称",
          type: "yn-input",
          field: "journalName"
        },
        {
          label: "日记账说明",
          value: "日记账说明",
          type: "yn-input",
          field: "journalDesc"
        },
        {
          label: "平衡类型",
          value: "平衡类型",
          type: "yn-select",
          field: "balanceTypeList",
          options: [
            {
              type: "JOURNAL_BALANCE",
              value: "日记账平衡"
            },
            {
              type: "ENTITY_BALANCE",
              value: "组织平衡"
            },
            {
              type: "SCOPE_BALANCE",
              value: "合并体平衡"
            },
            {
              type: "NO_BALANCE",
              value: "不平衡"
            }
          ]
        },
        {
          label: "最后修改时间",
          value: "最后修改时间",
          type: "yn-range-picker",
          field: "updateDate"
        },
        {
          label: "附件",
          value: "附件",
          type: "yn-input",
          field: "fileName"
        },
        {
          label: "最后修改人",
          value: "最后修改人",
          type: "yn-input",
          field: "updateBy"
        }
      ],
      journalListPlainOptions: [],
      lastCheckedList: [],
      defaultDisabled: ["版本", "年", "期间"],
      dimDataSource: {},
      mappingDimDataSource: {}
    };
  },
  computed: {
    dimId() {
      return this.journalSearchPlainOptions.reduce((pre, next) => {
        next.dimCode && (pre[_lowerFirst(next.dimCode)] = next.objectId);
        return pre;
      }, {});
    },
    responseWidth() {
      return { width: this.fieldWidth };
    },
    formData() {
      // props 内更新代理
      return { ...this.data };
    }
  },
  watch: {
    formData: {
      handler(value, old) {
        !deepCompare(value, old) && this.setFormData(value);
      }
    }
  },
  async created() {
    this.initForm();
    await this.getShowDims();
    this.getFieldList();
  },
  mounted() {
    this.$nextTick(() => {
      this.fieldWidth = this.calcColWidth();
      this.attachResize();
    });
    // 回车搜索
    document.addEventListener("keyup", this.enterSearch);
  },
  beforeDestroy() {
    this._observer && this._observer.disconnect();
    document.removeEventListener("keyup", this.enterSearch);
  },
  methods: {
    enterSearch(e) {
      if (e.keyCode === 13) {
        this.onSearch();
      }
    },
    attachResize() {
      const that = this;
      // eslint-disable-next-line no-unused-vars
      const callback = throttle((entries, observer) => {
        const newFieldWidth = that.calcColWidth();
        if (that.fieldWidth !== newFieldWidth) {
          that.fieldWidth = newFieldWidth;
        }
      }, 50);

      this._observer = new ResizeObserver(callback);
      this.$refs.searchform &&
        this._observer.observe(this.$refs.searchform.$el);
    },

    calcColWidth() {
      const domWidth = this.$el.clientWidth;
      const colSize = response.getCol(domWidth);
      this._colSize = colSize;
      const colWidth = colSize < 1 ? "100%" : `${100 / colSize}%`;
      return colWidth;
    },
    getWindowWidth() {
      try {
        return window.top.innerWidth || window.innerWidth;
      } catch (error) {
        // alert("应用地址和平台地址不统一，请联系管理员重新配置！");
        return window.innerWidth;
      }
    },
    initForm() {
      this.searchForm = this.$form.createForm(this, {
        name: "searchForm",
        onValuesChange: (props, fields) => {
          // 还原添加非表单字段， 反射出组件内部数据，自动更新
          this.$nextTick(() => {
            this.resetFormData();
          });
        }
      });
    },
    setFormData() {
      // 装配表单，过滤非表单字段
      const formData = this.searchForm ? this.searchForm.getFieldsValue() : {};
      Object.keys(formData).forEach(field => {
        if (this.data[field] !== undefined) {
          formData[field] = this.data[field];
        }
      });
      this.searchForm.setFieldsValue(formData);
    },
    resetFormData() {
      const formData = this.searchForm.getFieldsValue();
      const data = { ...this.data, ...formData };
      // 格式化 参数
      this.$emit("update:data", this.formatParams(data, false));
    },
    // 获取过滤字段
    async getUserColumns() {
      return await journalService(
        "getUserColumns",
        USER_TYPE_MAP[this.useType]
      ).then(res => res.data.data);
    },
    async getFieldList() {
      const res = await this.getUserColumns();
      const list =
        res.headerNames && res.headerNames.length
          ? res.headerNames
          : this.journalSearchPlainOptions.filter(item =>
            this.defaultDisabled.includes(item.value)
          );
      this.lastCheckedList = list.map(item => item.label || item.dimMultiName);
      this.formHandleItemList = [...this.handleFormComponents(list)];
      !this.isList && (await this.setDefaultPovValue());
      setTimeout(() => {
        this.onSearch();
      }, 1000);
    },
    handleDims(data) {
      let arr = [...data];
      while (arr.length) {
        const currentItem = arr.shift();
        currentItem.label = currentItem.dimMultiName;
        currentItem.value = currentItem.dimMultiName;
        currentItem.objectId = currentItem.dimId;
        currentItem.type = "asyn-select-dimMember";
        currentItem.field = `flexibleParam@${currentItem.objectId}`;
        if (currentItem.children && currentItem.children.length > 0) {
          arr = arr.concat(currentItem.children);
        }
      }
      return data;
    },
    async getShowDims() {
      await journalService("getShowDims").then(res => {
        const dims = this.handleDims(res.data.data);
        this.journalSearchPlainOptions = [
          ...this.journalSearchPlainOptions,
          ...dims
        ];
      });
    },
    handleDefaultComponent(values, componentName, props, events) {
      return {
        ...values,
        componentName,
        props,
        events
      };
    },
    // 处理动态组件数据
    handleFormComponents(formItemList) {
      if (!Array.isArray(formItemList)) return [];
      return formItemList.map((formItem, index) => {
        formItem.isShow = true;
        switch (formItem.type) {
          case "yn-input":
            return this.handleDefaultComponent(
              formItem,
              "yn-input",
              {
                placeholder: "请输入",
                // 禁用浏览器下拉框记忆
                autocomplete: "off"
              },
              {
                // 统一处理，输入框添加 回车搜索事件
                // pressEnter: this.onSearch
              }
            );
          case "yn-select":
            return this.handleDefaultComponent(
              formItem,
              "yn-select",
              {
                showArrow: true,
                showSearch: true,
                mode: "multiple",
                placeholder: "请选择",
                filterOption: (input, option) =>
                  option.componentOptions.children[0].text
                    .toLowerCase()
                    .indexOf(input.toLowerCase()) >= 0
              },
              {
                change: v => this.handleChange(v)
                // inputKeydown: e => {
                //   if (e.keyCode === 13) {
                //     this.onSearch();
                //   }
                // }
              }
            );
          case "yn-range-picker":
            return this.handleDefaultComponent(
              formItem,
              "yn-range-picker",
              {},
              { change: () => this.onDateChange() }
            );
          case "asyn-select-dimMember":
          case "yn-select-tree":
            return this.handleDefaultComponent(
              formItem,
              "asyn-select-dimMember",
              {
                searchMode: "custom",
                allowClear: false,
                defer: true,
                multiple: true,
                dimCode: formItem.dimCode,
                treeCheckStrictly: true,
                needPermission: ["Scope", "Entity"].includes(formItem.dimCode),
                needFilterShared: true,
                formatter: item => {
                  return `${item.dimMemberRealName}-${item.objectId}`;
                },
                unformatter: key => {
                  return key.replace(/.+-/, "");
                },
                dropdownVisibleChange: this.onDropdownVisibleChange,
                nonleafselectable: true,
                setEmpty: () => {
                  this.searchForm.setFieldsValue({
                    [formItem.field]: ""
                  });
                  this.resetFormData();
                }
              },
              {
                changeVal: (values, items) => {
                  for (const item of items) {
                    const { dimCode, objectId } = item;
                    const key =
                      dimCode.slice(0, 1).toLowerCase() + dimCode.slice(1);
                    this.mappingDimDataSource[key] ||
                      (this.mappingDimDataSource[key] = {});
                    this.mappingDimDataSource[key][objectId] = item;
                  }
                }
              }
            );
          default:
            return this.handleDefaultComponent();
        }
      });
    },
    setMappingDimDataSource(dimCode, data) {
      if (!dimCode) {
        return;
      }
      const key = dimCode.slice(0, 1).toLowerCase() + dimCode.slice(1);
      const mapingObj = {};
      const loop = data => {
        data.forEach(item => {
          const { children, objectId } = item;
          mapingObj[objectId] = item;
          if (children && children.length) {
            loop(children);
          }
        });
      };
      loop(data);
      this.mappingDimDataSource[key] = mapingObj;
    },
    handleChange(v) {
      Logger.log(v);
    },
    onDateChange(date, dateString) {
      Logger.log(date, dateString);
    },
    expandAndShrink() {
      this.isExpand = !this.isExpand;
      if (this.isExpand) {
        this.formHandleItemList = this.formHandleItemList.map(item => {
          item.isShow = true;
          return item;
        });
      } else {
        this.formHandleItemList = this.formHandleItemList.map((item, index) => {
          if (index >= this._colSize - 1) {
            item.isShow = false;
          } else {
            item.isShow = true;
          }
          return item;
        });
        this.searchForm.validateFields((err, value) => {
          Logger.log(err);
          const res = this.formHandleItemList.map(item => {
            if (value[item.field]) {
              return item.label;
            }
            return "";
          });
          this.showSearchField = res.filter(Boolean);
        });
      }
    },
    onReset() {
      this.searchForm.resetFields();
      this.resetFormData();
      this.$emit("reset");
      this.onSearch();
    },
    onDropdownVisibleChange({ dimCode }) {
      if (this.getPovTypeList().includes(dimCode.toLowerCase())) {
        !this.isList && this.savePovValue();
      }
    },
    onSearch() {
      this.searchForm.validateFields((err, values) => {
        if (!err) {
          const params = this.formatParams(values, true);
          this.$emit("searchEvent", params);
        }
      });
    },
    formatParams(values, isOutReference) {
      const params = { ...values };
      const baseDim = ["versionList", "yearList", "periodList"];
      const flexibleParamMap = Object.create(null);
      Object.keys(values).forEach(key => {
        if (key.includes("@")) {
          const k = key.split("@")[1];
          flexibleParamMap[k] = values[key]
            ? values[key].map(
              item => item.split("-")[item.split("-").length - 1]
            )
            : [];
          delete params[key];
        }
        if (key === "updateDate" && values["updateDate"]) {
          params["beginDate"] = values["updateDate"].length
            ? moment(values["updateDate"][0]).format("YYYY-MM-DD 00:00:00")
            : "";
          params["endDate"] = values["updateDate"].length
            ? moment(values["updateDate"][1]).format("YYYY-MM-DD 23:59:59")
            : "";
          delete params[key];
        }
        if (baseDim.includes(key) && isOutReference) {
          params[key] = values[key]
            ? values[key].map(
              item => item.split("-")[item.split("-").length - 1]
            )
            : [];
        }
      });
      params.flexibleParamMap = flexibleParamMap;
      return params;
    },
    showField() {
      this.fieldVisible = true;
    },
    async closeFieldModal(bool, chooseList) {
      this.isExpand = chooseList ? true : this.isExpand;
      this.fieldVisible = bool;
      if (!chooseList) return;
      if (chooseList) {
        this.formHandleItemList = [...this.handleFormComponents(chooseList)];
      }
      !this.isList && this.setDefaultPovValue();
    },
    // 设置默认的POV值
    async setDefaultPovValue() {
      const povObj = {};
      const prefix = "flexibleParam@";
      const params = this.defaultParams || {};
      const pov = await this.selectUserPov();
      const objKeys = Object.keys(this.searchForm.domFields);
      Object.keys(pov).reduce((pre, key) => {
        params[key] = params[key] || pov[key].memberId;
      }, params);
      for (const key in params) {
        if (POV_PREFIX_LIST.includes(key)) {
          povObj[`${key}List`] = this.getPovVal(params, key);
        } else {
          const fieldKey = prefix + this.dimId[key]; // dimId
          if (objKeys.includes(fieldKey)) {
            povObj[fieldKey] = this.getPovVal(params, key);
          }
        }
      }
      this.searchForm.setFieldsValue(povObj);
      // await this.selectUserPov(data => {
      //   const objKeys = Object.keys(this.searchForm.domFields);
      //   for (const key in data) {
      //     if (Object.values(POV_OBJECTID_MAP).includes(key)) {
      //       const fieldKey = prefix + data[key].dimId;
      //       if (objKeys.includes(fieldKey)) {
      //         povObj[fieldKey] = this.getPovVal(data[key], key);
      //       }
      //     } else {
      //       povObj[`${key}List`] = this.getPovVal(data[key], key);
      //     }
      //   }
      //   this.searchForm.setFieldsValue(povObj);
      // });
    },
    getPovVal(povData, key) {
      // 流程打开
      const povid = povData[key];
      if (povid) {
        return [povid];
      } else {
        const { dimMemberRealName, memberId } = povData;
        return povData && memberId ? [`${dimMemberRealName}-${memberId}`] : [];
      }
    },
    savePovValue() {
      const formFields = this.searchForm.getFieldsValue();
      const params = {};
      const prefix = "flexibleParam@";
      const setItemValue = (key, prop) => {
        // 第一个叶子节点
        const tempArr = formFields[prop];
        let memberId = tempArr.length ? tempArr[tempArr.length - 1] : "";
        // memberId = memberId.slice(memberId.indexOf("-") + 1, memberId.length);
        memberId = memberId.split("-")[memberId.split("-").length - 1];
        // let memberId = "";
        // for (let i = 0, LEN = tempArr.length; i < LEN; i++) {
        // memberId = formFields[prop][i];
        // memberId = memberId.slice(memberId.indexOf("-") + 1, memberId.length);
        // const { isLeaf } = this.mappingDimDataSource[key][memberId] || {};
        // if (!isLeaf) {
        //   break;
        // }
        // }
        params[key] = memberId;
      };
      for (const prop in formFields) {
        if (prop === "postStatusList") continue;
        if (prop.indexOf("List") > 0) {
          const key = prop.substr(0, prop.length - 4);
          if (
            this.getPovTypeList().includes(key) &&
            formFields[prop] &&
            formFields[prop].length
          ) {
            setItemValue(key, prop);
          } else {
            params[key] = null;
          }
        } else if (prop.indexOf(prefix) > -1) {
          const objectId = prop.substr(prefix.length);
          // TODO 这里可能出现id不一致问题
          const key = POV_OBJECTID_MAP[objectId];
          if (key) {
            if (
              this.getPovTypeList().includes(key) &&
              formFields[prop] &&
              formFields[prop].length
            ) {
              setItemValue(key, prop);
            } else {
              params[key] = null;
            }
          }
        }
      }
      this.saveOrUpdateUserPov(params);
    }
  }
};
</script>

<style lang="less" scoped>
.search-list {
  width: 100%;
  position: relative;
  overflow: hidden;
  .list-title {
    margin-bottom: 0.5rem;
    line-height: 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: @yn-text-color;
  }
  .item-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: @yn-margin-s;
    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: @rem10;
      &.last-item {
        margin-left: auto;
      }
      /deep/.ant-form-item-control {
        line-height: 1;
      }
      /deep/.ant-form-item-control-wrapper {
        flex: 1;
      }
    }
    /deep/ .ant-form-item-label {
      line-height: @rem32;
    }
    /deep/.ant-form-item-label > label {
      font-size: @rem14;
      color: @yn-text-color-secondary;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    /deep/.ant-calendar-picker {
      width: 100%;
    }
    .list-menu {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      /deep/.ant-divider-vertical {
        height: @rem22;
      }

      .btn-style {
        margin-right: 0.5rem;
      }
      .fold-style {
        margin-left: @rem8;
      }
      .expand-icon {
        font-size: 0.625rem;
        margin-left: 0.25rem;
      }
      .icon-style {
        margin-right: @rem8;
      }
      .iconBtn {
        cursor: pointer;
        color: @yn-text-color-secondary;
      }
    }
  }
  .search-tj {
    margin-top: @yn-margin-s;
    .tj-content {
      color: @yn-text-color-secondary;
    }
  }
  .icon {
    width: @rem32;
    height: @rem22;
    text-align: center;
    line-height: @rem24;
    position: absolute;
    bottom: 0;
    right: @rem32;
    background: @yn-background-color;
    cursor: pointer;
    border-top-left-radius: @rem10;
    border-top-right-radius: @rem10;
    /deep/.ynicon-button {
      line-height: 1.375rem;
    }
  }
}
.form-item {
  /deep/.ant-input &::placeholder {
    font-size: @rem14;
    color: @yn-auxiliary-color !important;
  }
  /deep/.ant-select-selection__placeholder {
    font-size: @rem14;
    color: @yn-auxiliary-color !important;
  }
  /deep/.yn-select-tree-value-placeholder {
    font-size: @rem14;
    color: @yn-auxiliary-color !important;
  }
}
@media screen and (min-width: 1482px) {
}
</style>
