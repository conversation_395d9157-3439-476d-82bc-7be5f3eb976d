export const HORIZONTAL = "horizontal"; // 水平
export const VERTICAL = "vertical"; // 垂直

const LayoutRuler = {
  layout: HORIZONTAL,

  [HORIZONTAL]: {
    S: { width: 600, col: 2 },
    M: { width: 600, col: 2 },
    L: { width: 850, col: 3 },
    XL: { width: 1500, col: 4 },
    XXL: { width: 2400, col: 5 }
  },

  [VERTICAL]: {
    S: { width: 600, col: 2 },
    M: { width: 700, col: 3 },
    L: { width: 1100, col: 4 },
    XL: { width: 1500, col: 5 },
    XXL: { width: 2400, col: 6 }
  },

  setLayout(value) {
    if (!value) return;
    this.layout = value;
    return this;
  },

  getCol: function(width) {
    const breakpoint = this[this.layout];
    if (width >= breakpoint.XXL.width) {
      return breakpoint.XXL.col;
    } else if (width >= breakpoint.XL.width) {
      return breakpoint.XL.col;
    } else if (width >= breakpoint.L.width) {
      return breakpoint.L.col;
    } else if (width >= breakpoint.M.width) {
      return breakpoint.M.col;
    } else if (width >= breakpoint.S.width) {
      return breakpoint.S.col;
    } else {
      return breakpoint.S.col;
    }
  }
};

export default LayoutRuler;
