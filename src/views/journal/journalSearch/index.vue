<template>
  <div class="journal-search cs-container">
    <SearchList
      ref="searchListRef"
      useType="searchFilter"
      :data.sync="formData"
      @searchEvent="searchEvent"
    />
    <div class="journal-search-list cs-body-table">
      <div
        v-show="!isPartExport"
        class="head-menu cs-body-action-icons tool-group"
      >
        <yn-dropdown v-model="showHeadMenu" :trigger="['click']">
          <svg-icon
            class="search-icon-export"
            title="导出"
            type="icon-a-c1_cr_export-dropdown"
          />
          <yn-menu slot="overlay">
            <yn-menu-item @click="exportAll">
              <span>全部导出</span>
            </yn-menu-item>
            <yn-menu-item @click="exportPart">
              <span>部分导出</span>
            </yn-menu-item>
          </yn-menu>
        </yn-dropdown>
        <yn-tooltip title="设置">
          <yn-icon-button type="set-up" @click="filterField" />
        </yn-tooltip>
      </div>
      <div class="list-table">
        <div v-show="isPartExport" class="table-part-export tool-group">
          <span>已选 {{ selectedRowKeysAll.length }} 条</span>
          <yn-button
            type="primary"
            class="part-export-cancel"
            @click="handlePartExport"
          >
            导出
          </yn-button>
          <yn-button class="part-export" @click="unExport">
            取消
          </yn-button>
        </div>
        <yn-spin :spinning="spinning">
          <yn-table
            ref="mytable"
            class="search-table"
            :columns="columns"
            :components="components"
            :dataSource="dataSource"
            :loading="loading"
            :rowSelection="
              isPartExport ? { selectedRowKeys: selectedRowKeysAll } : false
            "
            :customRow="handlerCustomRow"
            :scroll="dataSource.length ? scrollXy : $data.$scrollX"
            @rowSelectChange="onSelectChange"
          >
            <div slot="table.postStatus" slot-scope="post" class="post-item">
              <span
                :class="[
                  'post-status',
                  `${post.toLowerCase().replace('_', '-')}-color`
                ]"
              ></span>
              {{ postStatusObj[post] }}
            </div>
            <span slot="table.balanceType" slot-scope="balanceType">
              {{ BANLANCETYPE[balanceType] }}
            </span>
            <div slot="table.debitTitle" class="pos-right">
              借方
            </div>
            <span slot="table.fileName" slot-scope="text, record">
              <yn-tooltip
                :visibleOnOverflow="false"
                overlayClassName="attachmentList-tooltip"
                placement="top"
              >
                <template slot="title">
                  <div
                    v-for="(item, index) in record.journalAttachmentList"
                    :key="index"
                  >
                    {{ item.attachmentName }}
                  </div>
                </template>
                <yn-button
                  class="attachmentList"
                  @click.stop="handlerFile(record)"
                ><svg-icon
                   class="attachmentList-icon"
                   :isIconBtn="false"
                   type="icon-c1_cr_accessory"
                 />
                  <span class="attachmentList-name">
                    {{
                      record.journalAttachmentList &&
                        record.journalAttachmentList[0]
                        ? record.journalAttachmentList[0].attachmentName
                        : "-"
                    }}
                  </span>
                </yn-button>
              </yn-tooltip>
            </span>
            <div slot="table.debit" slot-scope="debit" class="pos-right">
              {{ debit }}
            </div>
            <div slot="table.creditTitle" class="pos-right">
              贷方
            </div>
            <div slot="table.credit" slot-scope="credit" class="pos-right">
              {{ credit }}
            </div>
            <!-- <div slot="table.drillTitle" class="drill-title">&nbsp;</div> -->
            <template slot="table.drill" class="drill-item">
              <!-- <svg-icon type="icon-c1_cr_form_enter" /> -->
              <svg-icon
                type="icon-c1_cr_form_enter"
                class="jn-detail"
                :isIconBtn="false"
              />
            </template>
          </yn-table>
          <div class="search-footer">
            <div class="footer-total">总计{{ listTotal }}条</div>
            <yn-pagination
              class="searchPagination"
              :total="listTotal"
              :current="currentPage"
              :pageSize="currentPageSize"
              showSizeChanger
              showQuickJumper
              :pageSizeOptions="pageSizeOptions"
              @change="changePage"
              @showSizeChange="showSizeChange"
            />
          </div>
        </yn-spin>
      </div>
    </div>

    <FilterField
      useType="searchShow"
      :fieldVisible="fieldVisible"
      :plainOptions="plainOptions"
      :lastCheckedList="columns"
      :defaultDisabled="defaultDisabled"
      @closeFieldModal="closeFieldModal"
    />
    <DrawerOther
      :visible.sync="drawerOtherVisible"
      :data="drawerOtherData"
      @ok="handlerUploadEnd"
    />
    <ExportJournalModal
      :exportPage="1"
      :type="exporType"
      :visible.sync="exportVisible"
      :data="exporType === 'all' ? searchCondition : idList"
    />
  </div>
</template>

<script>
import SearchList from "../searchList.vue";
import FilterField from "../filterOrShowField.vue";
import "yn-p1/libs/components/yn-menu/";
import "yn-p1/libs/components/yn-icon/";
import "yn-p1/libs/components/yn-table/";
import "yn-p1/libs/components/yn-dropdown/";
import "yn-p1/libs/components/yn-menu-item/";
import "yn-p1/libs/components/yn-pagination/";
import "yn-p1/libs/components/yn-icon-button/";
import "yn-p1/libs/components/yn-tooltip/";
import "yn-p1/libs/components/yn-popconfirm/";
import "yn-p1/libs/components/yn-spin/";
import _debounce from "lodash.debounce";
import commonService from "@/services/common";
import { mapState } from "vuex";
import journalService from "@/services/journal";
import { toFinance } from "@/utils/filters";
import ExportJournalModal from "../journalList/list/ExportJournalModal.vue";
import defaultParams from "@/mixin/defaultParams";
import { getComponents } from "@/utils/dragWidth";
import DrawerOther from "../journalList/list/drawerother.vue";
import {
  USER_TYPE_MAP,
  BANLANCETYPE,
  SEARCH_COLUMNS_SCOPED,
  TEXTALIGNR_TITLE
} from "../constant.js";
import { replaceEmptyFields } from "../../../utils/journal";
const scrollX = { x: true };
const TABNAME = "journalDetail";
const TAB_URI = "/journal/detail";

const COLUMNS_WIDTH = {
  year: 130,
  postStatus: 120
};

export default {
  components: { SearchList, FilterField, ExportJournalModal, DrawerOther },
  mixins: [defaultParams],
  data() {
    return {
      components: getComponents(() => ({
        columns: this.columns,
        right: 1,
        table: this.$refs.mytable,
        callback: () => {
          this.syncJson();
          this.saveColumnWidth();
        }
      })),
      drawerOtherData: null,
      drawerOtherVisible: false,
      BANLANCETYPE,
      scrollXy: {
        x: "100%",
        y: true
      },
      $scrollX: scrollX,
      spinning: false,
      showHeadMenu: false,
      formData: {},
      columns: [
        {
          key: "Drill",
          width: 40,
          scopedSlots: {
            customRender: "drill",
            title: "drillTitle"
          },
          fixed: "right"
        }
      ],
      dataSource: [],
      postStatusObj: {
        POSTED: "已过账",
        NOT_POSTED: "未过账",
        POSTING_FAILED: "过账失败",
        POSTING: "过账中"
      },
      loading: false,
      listTotal: 0,
      currentPage: 1,
      currentPageSize: 20,
      pageSizeOptions: ["10", "20", "50", "100"],
      fieldVisible: false,
      isPartExport: false,
      requestParams: "",
      selectedRowKeysAll: [],
      plainOptions: [
        {
          label: "编码",
          value: "编码",
          type: "yn-select",
          field: "journalCode"
        },
        {
          label: "日记账名称",
          value: "日记账名称",
          type: "yn-select",
          field: "journalName"
        },
        {
          label: "日记账说明",
          value: "日记账说明",
          type: "yn-input",
          field: "journalDesc"
        },
        {
          label: "过账状态",
          value: "过账状态",
          type: "yn-select",
          field: "postStatus"
        },
        {
          label: "最后修改人",
          value: "最后修改人",
          type: "yn-select",
          field: "updateBy"
        },
        {
          label: "最后修改时间",
          value: "最后修改时间",
          type: "yn-range-picker",
          field: "updateDate"
        },
        {
          label: "平衡类型",
          value: "平衡类型",
          type: "yn-select",
          field: "balanceType"
        },
        {
          label: "附件",
          value: "附件",
          type: "yn-input",
          field: "fileName"
        },
        {
          label: "版本",
          value: "版本",
          dimName: "版本",
          dimCode: "Version",
          type: "yn-select-tree",
          field: "version",
          objectId: "11ec45fe70e7ec75891db3348c1632bd"
        },
        {
          label: "年",
          value: "年",
          dimName: "年",
          dimCode: "Year",
          type: "yn-select-tree",
          field: "year",
          objectId: "11ec45fe39dcbd7d891dd934dba15806"
        },
        {
          label: "期间",
          value: "期间",
          dimName: "期间",
          dimCode: "Period",
          type: "yn-select-tree",
          field: "period",
          objectId: "11ec45fe5b895852891d0d7e9c566254"
        },
        {
          label: "借方",
          value: "借方",
          type: "yn-input",
          field: "debit"
        },
        {
          label: "贷方",
          value: "贷方",
          type: "yn-input",
          field: "credit"
        },
        {
          label: "备注",
          value: "备注",
          type: "yn-input",
          field: "remark"
        },
        {
          label: "行号",
          value: "行号",
          type: "yn-input",
          field: "rowNo"
        }
      ],
      lastCheckedList: [],
      defaultDisabled: ["编码", "日记账名称", "日记账说明", "过账状态"],
      exportVisible: false,
      idList: [],
      exporType: "part",
      searchCondition: {},
      // 用户拖动列宽json
      json: {}
    };
  },
  computed: {
    ...mapState({
      activeKey: state => state.common.tabActiveId
    })
  },
  watch: {
    formData() {
      this.paramChanged = true;
    },
    activeKey: {
      handler(newVal) {
        if (newVal === this.params.id) {
          this.searchEvent(this.requestParams);
        }
      }
    }
  },
  async created() {
    await this.getUserColumns();
    await this.getShowDims();
    await this.getColumnWidth();
    this.activeTabCbMixin(() => {
      this.searchEvent(this.requestParams);
    });
  },
  mounted() {
    this.setScrollHeight();
  },
  methods: {
    handlerCustomRow(record, index) {
      return {
        on: {
          // 事件
          click: e => {
            this.clickRow(record, index);
            return false;
          } // 点击行
        }
      };
    },
    handlerFile(record) {
      this.drawerOtherVisible = true;
      this.drawerOtherData = record;
    },
    handlerUploadEnd() {
      this.searchEvent(this.requestParams);
    },
    clickRow(record) {
      if (record.disabled) return;
      journalService("checkUserAuth", record.journalId).then(res => {
        this.handleDirll(record);
      });
    },
    // 获取可变维度
    addParamsToData(data) {
      let arr = [...data];
      while (arr.length) {
        const currentItem = arr.shift();
        currentItem.label = currentItem.dimMultiName;
        currentItem.value = currentItem.dimMultiName;
        currentItem.objectId = currentItem.dimId;
        if (currentItem.children && currentItem.children.length > 0) {
          arr = arr.concat(currentItem.children);
        }
      }
      return data;
    },
    // 初始化列和已经选项
    initData(data) {
      this.columns.splice(
        0,
        0,
        ...data.map(item => {
          const curItem = {
            title: item.label,
            label: item.label,
            width:
              this.json[item.field] ||
              COLUMNS_WIDTH[item.field] ||
              item.width ||
              210,
            key: item.field || item.dimCode,
            dataIndex: item.field || item.dimCode
          };
          if (SEARCH_COLUMNS_SCOPED.includes(item.field)) {
            curItem.scopedSlots = {
              customRender: item.field
            };
            if (TEXTALIGNR_TITLE.includes(item.field)) {
              delete curItem.title;
              curItem.scopedSlots.title = `${item.field}Title`;
            }
          }
          return curItem;
        })
      );
      this.lastCheckedList.push(
        ...data.map(item => item.label || item.dimMultiName)
      );
    },
    async getUserColumns() {
      await journalService("getUserColumns", USER_TYPE_MAP["searchShow"]).then(
        res => {
          this._lastSave = res.data.data;
        }
      );
    },
    async getShowDims() {
      await journalService("getShowDims").then(res => {
        const dims = this.addParamsToData(res.data.data);
        this.plainOptions.splice(this.plainOptions.length - 4, 0, ...dims);
        const { headerNames } = this._lastSave;
        if (!headerNames || !headerNames.length) {
          this.initData(this.plainOptions);
        } else {
          this.initData(headerNames);
        }
      });
    },
    searchEvent(values) {
      this.requestParams = values;
      this.currentPage = this.paramChanged ? 1 : this.currentPage;
      const params = {
        ...values,
        page: this.currentPage,
        pageSize: this.currentPageSize
      };
      this.paramChanged = false;
      this.spinning = true;
      journalService("queryJournalContent", params).then(res => {
        const data = res.data.data.data.map(item => {
          return {
            ...replaceEmptyFields(item),
            ...replaceEmptyFields(item.dimCodeMemberNameMap),
            key: `${item.journalId}-${item.contentId}`,
            debit: toFinance(item.debit),
            credit: toFinance(item.credit)
          };
        });
        this.dataSource = data;
        this.listTotal = res.data.data.total;
        this.spinning = false;
      });
    },
    exportAll() {
      this.exportVisible = true;
      this.showHeadMenu = false;
      this.exporType = "all";
      this.searchCondition = this.$refs.searchListRef.formatParams(
        this.$refs.searchListRef.searchForm.getFieldsValue(),
        true
      );
    },
    exportPart() {
      this.showHeadMenu = false;
      this.isPartExport = true;
      this.exporType = "part";
    },
    filterField() {
      this.fieldVisible = true;
    },
    changePage(page, pageSize) {
      this.currentPage = page;
      this.searchEvent(this.requestParams);
    },
    showSizeChange(current, size) {
      this.currentPage = 1;
      this.currentPageSize = size;
      this.searchEvent(this.requestParams);
    },
    closeFieldModal(bool, chooseList) {
      this.fieldVisible = bool;
      if (!chooseList) return;
      const selectColumns = chooseList.map(item => {
        const curItem = {
          title: item.label,
          label: item.label,
          width:
            this.json[item.field] ||
            COLUMNS_WIDTH[item.field] ||
            item.width ||
            210,
          key: item.field || item.dimCode,
          dataIndex: item.field || item.dimCode
        };
        if (SEARCH_COLUMNS_SCOPED.includes(item.field)) {
          curItem.scopedSlots = {
            customRender: item.field
          };
          if (TEXTALIGNR_TITLE.includes(item.field)) {
            delete curItem.title;
            curItem.scopedSlots.title = `${item.field}Title`;
          }
        }
        return curItem;
      });
      this.columns.splice(0, this.columns.length - 1, ...selectColumns);
    },
    unExport() {
      this.isPartExport = false;
      this.selectedRowKeysAll = [];
    },
    handlePartExport() {
      this.exportVisible = true;
      this.idList = this.selectedRowKeysAll.map(item => item.split("-")[0]);
    },
    onSelectChange(selectedRow, selectedRowKeysAll) {
      this.selectedRowKeysAll = selectedRowKeysAll;
    },
    handleDirll(item) {
      this.newtabMixin({
        id: item.journalId,
        title: item.journalName,
        uri: TAB_URI,
        router: TABNAME, // 如果当前项目没有配置对应的路由，都走systemTab（会缓存）
        params: {
          journalId: item.journalId
        },
        newTabParams: {}
      });
    },
    setScrollHeight() {
      this.domList = this.$el.querySelector(".journal-search-list");
      const toolgroup = this.$el.querySelector(".tool-group");
      const pagination = this.$el.querySelector(".search-footer");
      const others = 100;
      this.sizeObserve = new ResizeObserver(() => {
        const redux = toolgroup.offsetHeight + pagination.offsetHeight + others;
        this.scrollXy.y = this.domList.offsetHeight - redux;
      });
      this.sizeObserve.observe(this.domList);
    },
    // 列宽记忆相关
    async getColumnWidth() {
      await new Promise(resolve => {
        commonService("getLastSettingV", {
          tag: "columnWidth",
          key: "journalSearch"
        }).then(res => {
          if (res.data && res.data.data && res.data.data.value) {
            this.json = JSON.parse(res.data.data.value);
            this.cwObjectId = res.data.data.objectId;
            this.columns = this.columns.map(item => {
              item.width = this.json[item.key] || 210;
              return item;
            });
          }
          resolve();
        });
      });
    },
    syncJson() {
      this.json = this.columns.reduce((pre, next) => {
        pre[next.key] = next.width;
        return pre;
      }, {});
    },
    saveColumnWidth: _debounce(function() {
      this.json = this.columns.reduce((pre, next) => {
        pre[next.key] = next.width;
        return pre;
      }, {});
      commonService("saveOrUpdateUserSetting", {
        tag: "columnWidth",
        objectId: this.cwObjectId,
        key: "journalSearch",
        value: JSON.stringify(this.json)
      });
    }, 300)
  }
};
</script>

<style lang="less" scoped>
.posted-color {
  background: @yn-success-color;
}
.posting-failed-color {
  background: @yn-error-color;
}
.not-posted-color {
  background: @yn-warning-color;
}
.posting-color {
  background: @yn-chart-1;
}
.journal-search {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: @yn-background-color;
  .journal-search-list {
    width: 100%;
    height: 0;
    flex: 1;
    padding: 0 @yn-padding-xxxxl;
    .head-menu {
      width: 100%;
      margin: @yn-margin-l 0 @yn-margin-xl 0;
      display: flex;
      justify-content: flex-end;
      .search-icon-export {
        &::before {
          content: "";
        }
        /deep/.iconfont {
          font-size: 2rem;
          .svg-icon {
            font-size: 2rem;
          }
        }
      }
    }

    .list-table {
      /deep/ .ant-table-tbody td {
        padding: 0px @rem16;
      }
      .drill-title {
        // width: @rem30;
      }
      /deep/.ant-table-thead {
        background: @yn-table-header-bg;
      }
      /deep/.ant-table-tbody {
        background: @yn-body-background;
      }
      .post-item {
        display: flex;
        align-items: center;
        .post-status {
          display: inline-block;
          border-radius: 50%;
          margin-right: @rem8;
          width: @rem8;
          height: @rem8;
        }
      }
      .table-part-export {
        margin: @yn-margin-l 0 @yn-margin-xl 0;
        color: @yn-text-color-secondary;
        .part-export {
          width: 5rem;
        }
        .part-export-cancel {
          margin: 0 @rem10;
          width: 5rem;
        }
      }
      .pos-right {
        text-align: right;
      }
      /deep/.ant-table-header-column {
        width: 100%;
        .ant-table-header-column-title {
          width: 100%;
        }
      }
    }
  }
  .attachmentList {
    display: flex;
    align-items: center;
    width: 100%;
    border: none;
    color: @yn-text-color;
    text-align: left;
    background: transparent;
    .attachmentList-name {
      display: inline-block;
      width: 90%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &:hover {
      background: @yn-background-color-light;
    }
    .attachmentList-icon {
      margin-right: 0.5rem;
      color: @yn-label-color;
    }
  }
  .search-footer {
    display: flex;
    align-items: center;
    margin-top: @rem10;
    justify-content: flex-end;
    .searchPagination {
      margin-left: @yn-margin-xl;
    }
  }
  .jn-detail {
    color: @yn-label-color;
    font-size: @rem24;
    display: inline-block;
    margin-left: -0.25rem;
    width: 2.5rem;
  }
  /deep/ .ant-table-row-cell-break-word {
    word-break: break-word;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  /deep/
    .ant-table-scroll
    .resize-table-th[key="action"]
    .ant-table-column-title {
    display: none;
  }
}
.search-table {
  width: calc(100% + 10px);
}
/deep/ .ant-table-header {
  background: transparent;
}
/deep/ .ant-table-header::-webkit-scrollbar {
  border: 1px solid transparent !important;
  border-width: 0 0 1px 0;
}
</style>

<style lang="less">
.attachmentList-tooltip .ant-tooltip-inner {
  max-height: 25rem;
  overflow: auto;
}
</style>
