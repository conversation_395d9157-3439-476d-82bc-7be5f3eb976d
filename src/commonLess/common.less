/**
通用样式
*/

// 列表多行/单行 校验提示 start-------------------

// 需要验证的元素
.valid-prop {
  position: relative;
  max-height: 3.625rem;
  &.calign-right .invalid-tip-text {
    right: 0px;
    left: auto;
  }
  &.height34 .invalid-tip-text {
    top: @rem34;
  }
}

// 验证异常元素加边框，比如：input框、select等，看情况再补充
.valid-error-border {
  border: 1px solid @yn-error-color !important;
  border-radius: @rem4;
  // /deep/ input[type="text"] {
  //   border: 1px solid @yn-error-color !important;
  //   border-radius: @rem4;
  // }
  &:hover ~ .invalid-tip-text {
    display: block;
  }
  /deep/.ant-input:focus {
    box-shadow: none !important;
  }
  &:focus {
    outline: 1px solid rgba(187, 11, 8, 0.3) !important;
    box-shadow: none;
  }
  /deep/.yn-select-tree-focus {
    box-shadow: none !important;
    border-color: none;
  }
  /deep/.ant-input {
    &:focus {
      border-color: transparent;
    }
    &:hover {
      border-color: transparent;
    }
  }
  /deep/.ant-select-selection {
    border-color: transparent;
    outline: none;
    box-shadow: none;
    &:hover {
      border-color: transparent;
    }
    &:focus {
      border-color: transparent;
    }
  }
  // /deep/.ant-select-open {
  //   .ant-select-selection {
  //     border-color: transparent;
  //     box-shadow: none;
  //   }
  // }
}

// 验证异常的提示文本
.invalid-tip-text {
  display: none;
  position: absolute;
  top: @rem36;
  left: 0;
  height: @rem24;
  line-height: @rem26;
  padding: 0 @yn-padding-l;
  color: @yn-error-color;
  background: @yn-error-bg-color;
  z-index: 100;
  box-shadow: inset 0px 0px 5px 0px rgba(225, 229, 235, 1);
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

table tr:last-child .valid-prop .invalid-tip-text {
  top: -24px;
}

table tr:last-child .valid-prop.height34 .invalid-tip-text {
  top: -24px;
}

// 列表多行/单行 校验提示 end -------------------
