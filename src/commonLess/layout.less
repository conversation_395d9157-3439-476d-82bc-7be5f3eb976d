.cs {
  //---------------container wrap----------------------
  &-container {
    padding: 0 @yn-console-content-margin !important;
  }
  &-container-no-header {
    // 特殊情况，没有头部header
    padding: @yn-console-content-margin !important;
  }

  //---------------header----------------------
  &-header {
    display: flex;
    align-items: center;
    height: 3.25rem !important;
    background: transparent !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    font-weight: 600;
  }
  &-header-single {
    // 特殊：只有title, 面包屑导航等，即不包含可操作类型功能时
    display: flex;
    align-items: center;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    height: 2.75rem !important;
    background: transparent !important;
    font-weight: 600;
  }

  //---------------body----------------------
  // body 整体主体为表格构成,有padding
  &-body-table {
    padding: 0 1rem !important;
  }
  // body 整体主体为树和表格构成, 没有padding
  &-body {
    padding: 0 !important;
  }

  &-body-table,
  &-body {
    background-color: @yn-component-background !important;
    border-top-left-radius: @yn-console-content-radius !important;
    border-top-right-radius: @yn-console-content-radius !important;
    margin: 0 !important;
    &-filter {
      // 表格上方的筛选区域: 筛选组件整区
      height: auto !important;
      background-color: @yn-component-background !important;
      padding: 1rem 0 0.25rem 0 !important;
      margin: 0 !important;
      .form-item.ant-form-item {
        margin-bottom: @yn-console-content-margin !important;
      }
    }
    &-divider {
      margin: 0 !important;
      height: 1px !important;
      color: #e1e5eb !important;
    }
    &-action {
      // 表格上方的操作区域: 新增，导入，导出，配置等"按钮+图标"操作组成的行块
      background-color: @yn-component-background !important;
      padding: @yn-console-content-margin 0 !important;
      margin: 0 !important;
    }
    &-action-icons {
      // 表格上方的操作区域: 导入，导出， 配置等"图标"操作组成的行块
      background-color: @yn-component-background !important;
      padding: 0.25rem 0 !important;
      margin: 0 !important;
    }
  }
  &-body-headerComponent :extend(.cs-body) {
    margin: 0 @yn-console-content-margin !important;
    width: calc(100% - 1.5rem) !important;
    background: @yn-component-background;
    border-top-left-radius: @yn-console-content-radius !important;
    border-top-right-radius: @yn-console-content-radius !important;
  }
}
