/**
 * 引用组件的入口，将打包好的组件 在此js 导出，此文件做组件初始化工作（全局过滤器、指令、store注入）
 * */
import Vue from "vue";
import organizationComponent from "@/views/ownershipManagement/organization";
import equity from "@/views/ownershipManagement/equity";

import "@/directive";
import mixin from "@/mixin/index";
import filters from "@/utils/filters.js";

import routerState from "@/store/navi/";
import themeState from "@/store/theme/";
import common from "@/store/common";
import organization from "@/store/organization";
import rearrange from "@/store/rearrange";
import currentoffset from "@/store/currentoffset";
import reconciliation from "@/store/reconciliation";
import ruleSetMaintenance from "@/store/ruleSetMaintenance";
import processStore from "@/store/process";
import todoStore from "@/store/todo";

import "../public/iconfont/iconfont.js";

import SvgIcon from "@/components/ui/SvgIcon.vue";

const rootComInstall = {
  created() {
    const store = this.$store;
    if (!store) return;
    this.isEntrance && store.registerModule(["routerState"], routerState);
    this.isEntrance && store.registerModule(["themeState"], themeState);
    this.isEntrance && store.registerModule(["common"], common);
    this.isEntrance && store.registerModule(["organization"], organization);
    this.isEntrance && store.registerModule(["rearrange"], rearrange);
    this.isEntrance && store.registerModule(["reconciliation"], reconciliation);
    this.isEntrance && store.registerModule(["currentoffset"], currentoffset);
    this.isEntrance &&
      store.registerModule(["ruleSetMaintenance"], ruleSetMaintenance);
    this.isEntrance && store.registerModule(["processStore"], processStore);
    this.isEntrance && store.registerModule(["todoStore"], todoStore);
  },
  destroyed() {
    const store = this.$store;
    if (!store) return;
    this.isEntrance && store.unregisterModule(["routerState"], routerState);
    this.isEntrance && store.unregisterModule(["themeState"], themeState);
    this.isEntrance && store.unregisterModule(["common"], common);
    this.isEntrance && store.unregisterModule(["organization"], organization);
    this.isEntrance && store.unregisterModule(["rearrange"], rearrange);
    this.isEntrance &&
      store.unregisterModule(["reconciliation"], reconciliation);
    this.isEntrance && store.unregisterModule(["currentoffset"], currentoffset);
    this.isEntrance &&
      store.unregisterModule(["ruleSetMaintenance"], ruleSetMaintenance);
    this.isEntrance && store.unregisterModule(["processStore"], processStore);
    this.isEntrance && store.unregisterModule(["todoStore"], todoStore);
  }
};
~(function() {
  // 合并组件 注入合并的store
  Vue.component("svg-icon", SvgIcon);
  Vue.mixin(mixin);
  // 合并的全局指令
  // filters();
  // Vue.use(filters);
})();
// 导出组件
const OrganizationComponent = Vue.extend({
  components: { Comp: organizationComponent },
  mixins: [rootComInstall],
  data() {
    return {
      isEntrance: true
    };
  },
  template: `<Comp></ComP>`
});
const EquityComp = Vue.extend({
  components: { Comp: equity },
  mixins: [rootComInstall],
  data() {
    return {
      isEntrance: true
    };
  },
  template: `<Comp/>`
});
export const Organization = OrganizationComponent;
export const Equity = EquityComp;
