import povService from "@/services/pov";
const povMixin = {
  selectUserPov: async callback => {
    return await povService("selectUserPov").then(res => {
      const result = res.data.data;
      // 如果是新用户 则需要内置数据为空。
      for (const key in result) {
        if (!result[key]) {
          result[key] = {
            dimCode: "",
            dimId: "",
            dimMemberCode: "",
            dimMemberDbCode: "",
            dimMemberName: "",
            dimMemberRealName: "",
            hasChildren: null,
            hasShared: null,
            memberId: ""
          };
        }
      }
      callback && callback(result);
      return result;
    });
  },
  saveOrUpdateUserPov: function(params, callback) {
    const paramsObj = this.convertPovData(params);
    povService("saveOrUpdateUserPov", paramsObj).then(res => {
      callback && callback();
    });
  },

  /**
     数据结构
     入参, dataObj: {
     year:11ec55ae745d88aab46ef52af9eaa395,
     period:11ec55ae745d88aab46ef52af9eaa395,
     version:11ec55ae745d88aab46ef52af9eaa395,
     scope:11ec55ae745d88aab46ef52af9eaa395,
     entity:11ec55ae745d88aab46ef52af9eaa395}
      返回, returnObj: {
      "year": { "memberId": "11ec55ae745d88aab46ef52af9eaa395"  },
      "period": { "memberId": "11ec55ae745d88aab46ef52af9eaa395"  },
      "version": { "memberId": "11ec55ae745d88aab46ef52af9eaa395"  },
      "scope": { "memberId": "11ec55ae745d88aab46ef52af9eaa395"  },
      "entity": { "memberId": "11ec55ae745d88aab46ef52af9eaa395"  }}
      */

  convertPovData(dataObj) {
    if (!dataObj) return {};
    const returnObj = {};
    for (const key in dataObj) {
      returnObj[key] = {
        memberId: dataObj[key]
      };
    }
    return returnObj;
  },

  getPovTypeList: function() {
    return ["entity", "period", "scope", "version", "year"];
  },

  // 数据转化
  toPovUpperCase(value) {
    if (!value) return value;
    return value.slice(0, 1).toUpperCase() + value.slice(1).toLowerCase();
  }
};
export default povMixin;
