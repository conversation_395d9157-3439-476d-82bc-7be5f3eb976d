import { isType } from "@/utils/common";

/**
 * 使用动态弹框:（只需要在合适的时候添加modal）
 *
        // 同步弹框: 弹框随操作即时展示，此期间用户操作阻塞
        this.addModalMixin(confirm({
            title: "过账完成"
        }));

        // 异步中添加弹框: 是用箭头函数包装返回原提示即可,
        // 常见于异步回调中展示弹框，在未完成回调时，此期间用户操作非阻塞， 切换到了其他页签, 防止当前弹框在其它页面展示
        this.addModalMixin(() => {
          return confirm({
            title: "过账完成"
          });
        }, this.params.id);
 *
 */

/**
 * 缓存modal
 * @param {*} modal
 * @param {} id 异步中添加的非阻塞modal 最好提供tab 的 id
*/
export function addModal(modal, id) {
  const activeId = this.$store.state.common.tabActiveId;
  id = id || activeId;
  if (id === activeId) {
    if (isType(modal, "Function")) {
      const modalId = modal.call(this);
      this.$store.commit("common/addModal", { id: id, modals: modalId });
    } else {
      this.$store.commit("common/addModal", { id: id, modals: modal });
    }
  } else {
    this.$store.commit("common/addModal", { id: id, modals: modal });
  }
}

/**
 * 展示model, 清理垃圾
 * @param {*} id 不传则为跳转页
 */
export function showOnModel(id) {
  this.$nextTick(() => {
    id = id || this.$store.state.common.tabActiveId;
    this.$store.commit("common/resolveModal", { id: id, status: "show" });
  });
}

/**
 * 隐藏model, 清理垃圾
 * @param {*} id 不传则为当前页
*/
export function hideOnModel(id) {
  id = id || this.$store.state.common.tabActiveId;
  this.$store.commit("common/resolveModal", { id: id, status: "hide" });
}

export default {
  methods: {
    addModalMixin: addModal
  }
};
