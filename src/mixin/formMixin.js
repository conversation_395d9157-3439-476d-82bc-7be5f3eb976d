import taskFlowServeice from "@/services/taskFlowTemp";
import { pavingTree } from "@/utils/taskFlowTemp.js";

export default {
  data() {
    return {
      dataJson: [],
      pavingData: []
    };
  },
  created() {
  },
  methods: {
    handleDim(arr) {
      const loop = data => {
        data.forEach(item => {
          item.key = item.data.elementId;
          item.title = item.data.elementName;
          item.parentKey = item.data.parentId;
          item.children = item.subNodes;
          item.scopedSlots = {
            title: "custom"
          };
          if (item.children && item.children.length) {
            loop(item.children);
          }
        });
      };
      loop(arr);
    },
    findParentKeys(key) {
      const targetKey = [...key];
      const keys = [];
      while (targetKey.length) {
        const k = targetKey.shift();
        const parentKey = this.pavingData.find(item => item.key === k).parentKey;
        parentKey && keys.push(parentKey);
        parentKey && targetKey.push(parentKey);
      }
      return keys;
    },
    async getFormList() {
      await taskFlowServeice("getSceneList").then(res => {
        if (res.data.length) {
          const result = res.data;
          this.handleDim(result);
          this.dataJson = result;
          this.pavingData = pavingTree(result);
        }
      });
    }
  }
};
