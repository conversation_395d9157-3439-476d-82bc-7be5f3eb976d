import PlatformCommunication from "../utils/platformCommunication";
import { showOnModel, hideOnModel } from "./saveModal";

export default {
  methods: {
    /**
     * 新建tab页，目前系统匹配规则： menuId 等于 id 标识tab页
     * tab {
            id: "", //tab id
            router: "", // 路由视图名
            title: "", //tab name

            // 可选参数
            uri: "", ? //暂留历史属性
            noKeepAlive: "", // 是否需要缓存, true，不需要缓存， false, 需要缓存， 不传默认缓存
            params: {} tab 页自定义数据存储
        }
    */
    newtabMixin(tab) {
      this.savePromptMixin().then(res => {
        // 平台多页签
        const { id, params, newTabParams } = tab;
        tab.params = Object.assign({}, params, newTabParams || {});
        if (!this.selfTab) {
          // 设置tab签所需参数
          this.setTabParamsMixin(
            id,
            { ...tab.params }
            // Object.assign({}, params, newTabParams || {})
          );
          PlatformCommunication.openTab(tab);
          return;
        }
        hideOnModel.call(this);
        const mapTabObj = this.$store.getters["common/mapTabObj"];
        if (mapTabObj[id]) {
          tab.params &&
            this.$store.commit("common/updateTab", {
              id: tab.id,
              params: tab.params
            });
          this.$store.commit("common/selectTab", id);
        } else {
          this.$store.commit("common/addTab", {
            tabInfo: {
              menuId: id,
              uri: "",
              noKeepAlive: false,
              params: {},
              ...tab
            }
          });
        }
        showOnModel.call(this);
      });
    },
    /**
     * 关闭指定tab页
     * @param {*} tabid tabid 传输组批量关闭
     */
    closetabMixin(tabid) {
      if (!this.selfTab) {
        if (Array.isArray(tabid)) {
          tabid.forEach(id => {
            PlatformCommunication.closeTab(id);
          });
        } else {
          PlatformCommunication.closeTab(tabid);
        }
        return;
      }
      if (Array.isArray(tabid)) {
        tabid.forEach(id => {
          this.$store.commit("common/removeModal", { id: id });
          this.$store.commit("common/remove", {
            id: id
          });
        });
      } else {
        this.savePromptMixin().then(res => {
          this.$store.commit("common/removeModal", { id: tabid });
          this.$store.commit("common/remove", {
            id: tabid
          });
        });
      }
    },
    /**
     * 跳转到指定tab页
     * @param {*} id tabid
     */
    gotabMixin(tabid) {
      hideOnModel.call(this);
      this.$store.commit("common/selectTab", tabid);
      showOnModel.call(this);
    },
    /**
     *@description 新版导航 获取tab 参数
     * */
    getTabParamsMixin() {
      return PlatformCommunication.getQueryParam();
    },
    /**
     *@description 新版导航 设置tab 参数
     * */
    setTabParamsMixin(tabId, params) {
      if (arguments.length !== 2 || typeof tabId !== "string") {
        return;
      }
      if (!this.selfTab) {
        PlatformCommunication.setQuertParam(tabId, params);
      }
    },
    activeTabCbMixin(cb) {
      PlatformCommunication.activeTabCb(() => {
        cb && cb();
      });
    }
  }
};
