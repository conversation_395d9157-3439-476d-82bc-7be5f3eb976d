import YnP1 from "yn-p1";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
import DsUtils from "yn-p1/libs/utils/DsUtils";
const { i18n } = YnP1.instances;

const $t = function(key, defaultText, locale, values) {
  const queryLang = UrlUtils.getQuery("lang") || "";
  const lang =
    queryLang ||
    DsUtils.getSessionStorageItem("lang") ||
    DsUtils.getLocalStorageItem("lang") ||
    "zh_CN";
  const langData = i18n.getLocaleMessage(lang);
  if (langData.hasOwnProperty(key)) {
    return i18n.t(key, locale, values);
  } else {
    const msg = defaultText || key || "";
    return msg;
  }
};

const render = (tmp, value) => {
  return tmp.replace(/\{(\d)\}/g, (matcher, d) => value[d]);
};
const getLang = (scope, key) => {
  return this && this.$t && this.$i18n ? this.$t(scope + key) : $t(scope + key);
};
const getModuleLang = (key, value = [], scope = "") => {
  const lang = getLang(scope, key);
  return value.length ? render(lang, value) : lang;
};

export default {
  $t_common(key, value = [], scope = "c1.consolidation.common.") {
    return getModuleLang(key, value, scope);
  },
  $t_process(key, value = [], scope = "c1.consolidation.process.") {
    return getModuleLang(key, value, scope);
  },
  $t_verify(
    key,
    value = [],
    scope = "c1.consolidation.verify_jump_configuration."
  ) {
    return getModuleLang(key, value, scope);
  },
  $t_structures(
    key,
    value = [],
    scope = "c1.consolidation.consolidation_structures."
  ) {
    return getModuleLang(key, value, scope);
  },
  $t_equity(key, value = [], scope = "c1.consolidation.equity_manager.") {
    return getModuleLang(key, value, scope);
  }
};
