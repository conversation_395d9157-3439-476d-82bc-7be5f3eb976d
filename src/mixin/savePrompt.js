import UiUtils from "yn-p1/libs/utils/UiUtils";
import PlatformCommunication from "../utils/platformCommunication";
import lang from "@/mixin/lang";
const { $t_common } = lang;

// import Logger from "yn-p1/libs/modules/log/logger";
let saveCallBack = "";
let notOkCb = "";
let saveTips = "";
let saveModuleInfo = {};
const savePromptMixin = {
  methods: {
    savePromptMixin: function(bool) {
      const self = this;
      const content =
        saveModuleInfo.content || $t_common("changes_will_be_lost");
      const okText = saveModuleInfo.okText || $t_common("save");
      const icon = saveModuleInfo.icon || "question-circle";
      return new Promise((resolve, reject) => {
        if (!saveCallBack || bool) {
          resolve();
          return;
        }
        UiUtils.confirmSave({
          title: saveTips,
          content,
          okText,
          notOkText: $t_common("do_not_save"),
          cancelText: $t_common("cancel"),
          icon,
          onOk() {
            try {
              //  执行异步的保存事件
              saveCallBack()
                .then(res => {
                  self.clearCommonSaveEventsMixin();
                  resolve(res);
                })
                .catch(e => {
                  reject({
                    status: "reject",
                    message: "保存方法报错：" + e
                  });
                });
            } catch (e) {
              reject({
                status: "reject",
                message: "保存方法报错：" + e
              });
            }
          },
          onNotOk() {
            // 先清空保存回调，防止进入不保存回调之后，关闭页签触发保存回调
            self.clearCommonSaveEventsMixin();
            notOkCb && notOkCb();
            // PlatformCommunication.cancelSaveTab();
            notOkCb = "";
            resolve({ status: "notSave", message: "执行不保存方法" });
          },
          onCancel() {
            reject({
              status: "cancel",
              message: "取消操作"
            });
          },
          class: saveModuleInfo.moduleName
            ? `confirmSave_mixin_${saveModuleInfo.moduleName}`
            : "confirmSave_mixin"
        });
      });
    },
    // 清除公共保存事件
    clearCommonSaveEventsMixin() {
      saveCallBack = "";
      saveTips = "";
      // 通知平台不再监听tab 关闭给出提示
      PlatformCommunication.savePrompt(false);
    },
    // 添加保存的后调事件，如果存在不重复添加
    /**
     * @description 添加回调
     * @param{Function}saveEvent 保存事件回调 必传
     * @param{String}title 保存提示 非必填
     * @param{Function}noSaveCb 不保存的回调 非必填
     * @param{Function}moduleInfo 触发提示框传递过来的信息
     */
    addCallBackFnMixin(saveEvent, title, noSaveCb, moduleInfo) {
      if (saveCallBack) return;
      if (!this.selfTab) {
        const self = this;
        PlatformCommunication.savePrompt(true);
        window.addEventListener(
          "message",
          async event => {
            let data = event.data;
            if (data && typeof data === "string") {
              data = JSON.parse(data);
              const { type, tabKey } = data || {};
              if (type === "iframe_message_close_notify") {
                self.savePromptMixin().then(() => {
                  PlatformCommunication.closeTab(tabKey);
                });
              }
            }
          },
          false
        );
      }
      saveCallBack = saveEvent;
      let modalTitle = title;
      let noSaveCallCb = noSaveCb;
      if (typeof title !== "string") {
        modalTitle = "";
        noSaveCallCb = title;
      }
      saveTips = modalTitle;
      notOkCb = noSaveCallCb;
      if (moduleInfo && Object.keys(moduleInfo).length) {
        saveModuleInfo = moduleInfo;
      }
    },
    hasSaveEventMixin() {
      return saveCallBack !== "";
    }
  }
};
export default savePromptMixin;
