export const LOG = {
  LEVEL: process.env.VUE_APP_LOG_LEVEL
    ? Number(process.env.VUE_APP_LOG_LEVEL)
    : 1 // 0: NONE, 1: Error, 2: Warn, 3: Info, 4: Log
};
export const APPS = {
  LANGUAGE: "", // empty means use default language. English language code is en.
  NAME: "consolidation"
};

export const MRKey = "_C1MR";

// 用作获取多语言 暂时写的是 mdd 的值
export const ConsolidationKey = "_C1MDD";
export const SysKey = "_SysInfo";

export const BACKEND = {
  BASE_URL: window.YN_ENV.CONSOLIDATION.VUE_APP_BASE_URL,
  ECS_CONSOLE_BASE_URL: window.YN_ENV.ECS.VUE_APP_ECS_CONSOLE_BASE_URL, // 控制台
  ECS_CONSOLE_URL: window.YN_ENV.ECS.VUE_APP_ECS_BASE_URL,
  VUE_APP_ECS_PLATFORM: window.YN_ENV.ECS.VUE_APP_ECS_PLATFORM,
  CONSOLIDATION_MDD_BASE_CUSTOM__URL:
    window.YN_ENV.CONSOLIDATION.VUE_APP_CUSTOM_URL, // 读取自定义接口
  MR_BASE_URL: window.YN_ENV.MR.VUE_APP_MR_BASE_URL + "/bff", // mr http://192.168.12.178:91/mr/bff
  MDD_BASE_URL: window.YN_ENV.MR.VUE_APP_MDD_BASE_URL, // mr http://192.168.12.178:91/mr/bff
  DASHBOARD_BASE_URL: window.YN_ENV.DASHBOARD.VUE_APP_DASHBOARD_BASE_URL // dashboard http://192.168.12.178:91/dashboard
};
export const FRONTEND = {
  DASHBOARD_PORTAL_URL: window.YN_ENV.MR.VUE_APP_DASHBOARD_PORTAL_URL,
  DASHBOARD_REPORT_VIEW_URL: window.YN_ENV.MR.VUE_APP_DASHBOARD_REPORT_VIEW_URL,
  DASHBOARD_REPORT_PREVIEW_URL:
    window.YN_ENV.MR.VUE_APP_DASHBOARD_REPORT_PREVIEW_URL,
  DASHBOARD_REPORT_MOBILE_URL:
    window.YN_ENV.MR.VUE_APP_DASHBOARD_REPORT_MOBILE_URL,

  MR_FRONT_VIEW_URL: window.YN_ENV.MR.VUE_APP_MR_FRONT_URL,
  DASHBOARD_FRONT_VIEW_URL: window.YN_ENV.DASHBOARD.VUE_APP_DASHBOARD_FRONT_URL,
  MDD_FRONT_VIEW_URL: window.YN_ENV.MDD.VUE_APP_MDD_FRONT_URL
};
