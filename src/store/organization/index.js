import Vue from "vue";
import organizationService from "@/services/organization";
import commonService from "@/services/common";
import { PERMISSION_NAME_MAPPING } from "@/views/ownershipManagement/constant";
const utils = {
  getTreeNodeTitle(treeNode, titleType) {
    const { memberName, memberCodeIndex, hasLeaf, childNum } = treeNode;
    let title = memberName;
    if (titleType === "CODE") {
      title = memberCodeIndex;
    } else if (titleType === "NAME_AND_CODE") {
      title = `${memberCodeIndex} ${memberName}`;
    }
    if (!hasLeaf) {
      title += `（${childNum}）`;
    }
    return title;
  },
  async getTreeData(params, cb) {
    await organizationService("getOrganizationMenuTree", params).then(res => {
      const { data } = res;
      const { searchWord } = params;
      const childrenNode = data.map(item => {
        const pId = typeof params.hasRoot === "undefined" ? params.id : "-1";
        const treeNode = {
          ...item,
          isLeaf: searchWord ? true : item.hasLeaf,
          key: item.objectId,
          disabled: item.readOnly,
          treeNodeName: utils.getTreeNodeTitle(
            item,
            state.tableConfig.cellType
          ),
          scopedSlots: {
            title: "custom"
          },
          parentId: pId,
          pId
        };
        return treeNode;
      });
      cb && cb(childrenNode);
    });
  },
  async updateOrganizationEntityChildNum(params) {
    return await organizationService(
      "updateOrganizationEntityChildNum",
      params
    );
  },
  getUpdateTreeNodeData(treeData, cellType) {
    const loop = data => {
      data.forEach(treeNode => {
        const { children } = treeNode;
        treeNode.treeNodeName = utils.getTreeNodeTitle(treeNode, cellType);
        if (children && children.length) {
          loop(children);
        }
      });
    };
    loop(treeData);
    return treeData;
  }
};
const state = {
  toBeSavedObj: {}, // 待保存对象
  tableConfig: {
    // 组织架构表格的配置信息
    cellType: "NAME" //  显示名称 NAME | CODE |NAME_AND_CODE
  },
  pageDimObj: {
    mergeGroupVal: "",
    monthVal: "",
    versionVal: "",
    yearVal: ""
  }, // 页面维 选中的值
  authData: [],
  // 维度穿梭框信息，新增组织、合并组
  dimensionTransferInfo: {
    treeNode: {}, // 添加到指定树节点下
    visible: false,
    type: ""
  },
  treeData: [],
  mapTreeData: {},
  refreshTree: false, // 刷新树
  selectedTreeNode: {}, //  选中的节点信息
  dimSelectStatus: false,
  offsetRule: "parallel" // 权益抵消合并规则 parallel: 平行合并 ; level: 层级合并
};

const getters = {
  toBeSavedList: state => {
    return Object.values(state.toBeSavedObj);
  },
  authObj: state => {
    const authObj = {};
    state.authData.forEach(item => {
      const { privilegeCode, value } = item;
      authObj[privilegeCode] = value === "TRUE";
    });
    // 新增权限包含编辑权限
    if (authObj[PERMISSION_NAME_MAPPING.add]) {
      authObj[PERMISSION_NAME_MAPPING.edit] = true;
    }
    return authObj;
  },
  isAllPageDims: state => {
    const { mergeGroupVal, monthVal, versionVal, yearVal } = state.pageDimObj;
    return mergeGroupVal && monthVal && versionVal && yearVal;
  }
};

const mutations = {
  setDimSelectStatus(state, status) {
    state.dimSelectStatus = status;
  },
  addItemToSaveObj(state, params) {
    const { key } = params;
    state.toBeSavedObj[key] = params;
  },
  clearToBeSaveObj(state, params) {
    state.toBeSavedObj = {};
  },
  deleteOneToBeSaveObj(state, params) {
    Vue.delete(state.toBeSavedObj, params);
  },
  changeTableConfig(state, params) {
    const { cellType } = params;
    if (cellType && state.tableConfig.cellType !== cellType) {
      state.tableConfig.cellType = cellType;
      if (state.treeData.length) {
        state.treeData = utils.getUpdateTreeNodeData(state.treeData, cellType);
      }
    }
  },
  setPageDimVal(state, params) {
    const [key, val] = params;
    state.pageDimObj[key] = val;
    state.selectedTreeNode = {};
  },
  setAuthData(state, authData) {
    state.authData = authData;
  },
  setDimensionTransferInfo(state, params) {
    const { visible = false, type = "", treeNode = {}, dimInfo = {} } = params;
    state.dimensionTransferInfo = {
      visible,
      type,
      treeNode,
      dimInfo
    };
  },
  setRootTreeData(state, { treeData: data, isKeepChildren }) {
    const mapTreeData = {};
    const oldMapTreeData = state.mapTreeData;
    data.forEach(item => {
      const { objectId } = item;
      if (oldMapTreeData[objectId] && isKeepChildren) {
        item.children = oldMapTreeData[objectId].children;
      }
    });
    const loop = data => {
      data.forEach(item => {
        const { parentId, objectId, children } = item;
        if (parentId === "-1") {
          if (mapTreeData[parentId]) {
            mapTreeData[parentId].push(item);
          } else {
            mapTreeData[parentId] = [item];
          }
        }
        mapTreeData[objectId] = item;
        if (children && children.length) {
          loop(children);
        }
      });
    };
    loop(data);
    this._vm.$set(state, "treeData", data);
    this._vm.$set(state, "mapTreeData", mapTreeData);
  },
  setChildrenTreeData(state, { pId, data }) {
    if (state.mapTreeData[pId] && state.mapTreeData[pId].children) {
      data.forEach(item => {
        const { objectId } = item;
        const oldTreeNodeData = state.mapTreeData[objectId];
        if (oldTreeNodeData && oldTreeNodeData.children) {
          item.children = state.mapTreeData[objectId].children;
        }
      });
      // 数据上的 children 应该是 mapTreeData 上的children
      state.mapTreeData[pId].children.splice(
        0,
        state.mapTreeData[pId].children.length,
        ...data
      );
    } else {
      state.mapTreeData[pId].children = data;
    }
    data.forEach(item => {
      item.parentId = pId;
      state.mapTreeData[item.key] = item;
    });
    this._vm.$set(state, "mapTreeData", state.mapTreeData);
    this._vm.$set(state, "treeData", state.treeData);
  },
  setSelectedTreeNode(state, treeNodeId) {
    state.selectedTreeNode = state.mapTreeData[treeNodeId] || {};
  },
  updateTreeNodeEntityChildNum(state, numObj) {
    const { mapTreeData } = state;
    Object.keys(numObj).forEach(id => {
      mapTreeData[id].childNum = numObj[id];
      mapTreeData[id].treeNodeName = utils.getTreeNodeTitle(
        mapTreeData[id],
        state.tableConfig.cellType
      );
    });
  },
  setRefreshTreeStatus(state, status) {
    state.refreshTree = status;
  },
  setRefreshTreeData(state, { data, pId }) {
    if (pId === "-1") {
      const rootMapTreeData = {};
      state.mapTreeData["-1"].forEach(item => {
        rootMapTreeData[item.objectId] = item;
      });
      state.mapTreeData["-1"] = [];
      data.forEach(item => {
        const { objectId } = item;
        const { children } = state.mapTreeData[objectId] || {};
        item.children = children;
        item.treeNodeName = utils.getTreeNodeTitle(
          item,
          state.tableConfig.cellType
        );
        state.mapTreeData[objectId] = item;
        state.mapTreeData["-1"].push(item);
      });
      this._vm.$set(state, "mapTreeData", state.mapTreeData);
      this._vm.$set(state, "treeData", state.mapTreeData["-1"]);
      return;
    }
    if (state.mapTreeData[pId] && state.mapTreeData[pId].children) {
      data.forEach(item => {
        const { objectId } = item;
        const oldTreeNodeData = state.mapTreeData[objectId];
        if (oldTreeNodeData && oldTreeNodeData.children) {
          item.children = state.mapTreeData[objectId].children;
        }
      });
      // 数据上的 children 应该是 mapTreeData 上的children
      state.mapTreeData[pId].children.splice(
        0,
        state.mapTreeData[pId].children.length,
        ...data
      );
    } else {
      state.mapTreeData[pId].children = data;
    }
    data.forEach(item => {
      item.parentId = pId;
      state.mapTreeData[item.key] = item;
    });
    this._vm.$set(state, "mapTreeData", state.mapTreeData);
    this._vm.$set(state, "treeData", state.treeData);
  },
  updateOffsetRule(state, offsetRule) {
    state.offsetRule = offsetRule;
  }
};

const actions = {
  saveTableConfig: ({ commit }, params) => {
    // 调用接口 保存表格设置信息
    commit("changeTableConfig", params);
  },
  async getPermissionSet({ commit }, params) {
    const {
      data: { items }
    } = await commonService("getMetadataRecords", "organizationManagerAuth");
    const refId = items.length ? items[0].objectId : "";
    await organizationService("getOrganizationAuth", refId).then(res => {
      const { items } = res.data || [];
      commit("setAuthData", items);
    });
  },
  // 获取根节点 信息,hasFrist hasRoot 可以从外部传入
  async getRootTreeData({ commit, state }, requestParams = {}) {
    const {
      mergeGroupVal: id,
      monthVal: period,
      versionVal: version,
      yearVal: year
    } = state.pageDimObj;
    const { isKeepChildren, ...otherParams } = requestParams;
    const param = {
      id,
      period,
      version,
      year,
      hasRoot: false,
      hasFirst: true,
      ...otherParams
    };
    if (id === "allScope") {
      delete param.id;
      param.allScope = true;
    }
    if (!(id && period && version && year)) {
      commit("setRootTreeData", {
        treeData: [],
        isKeepChildren: false
      });
      return;
    }
    await utils.getTreeData(param, data => {
      commit("setRootTreeData", {
        treeData: data,
        isKeepChildren
      });
    });
  },
  async moveAfterRefresh({ commit, state }, pId) {
    const {
      monthVal: period,
      versionVal: version,
      yearVal: year,
      mergeGroupVal
    } = state.pageDimObj;
    const param = {
      id: pId,
      period,
      version,
      year,
      hasRoot: false,
      hasFirst: true
    };
    const pNoode = state.mapTreeData[pId] || {};
    // 全部成员，根节点移动
    if (mergeGroupVal === "allScope" && pId === "-1") {
      delete param.id;
      param.allScope = true;
    } else {
      param.hasRoot = pNoode.hasRoot;
      param.hasFirst = false;
    }
    await utils.getTreeData(param, data => {
      commit("setRefreshTreeData", { data, pId });
    });
  },
  // 获取子节点信息
  async getChildNodeByPId({ commit, state }, pId) {
    const {
      monthVal: period,
      versionVal: version,
      yearVal: year,
      mergeGroupVal
    } = state.pageDimObj;
    await utils.getTreeData(
      {
        id: pId,
        period,
        version,
        year,
        hasRoot:
          mergeGroupVal === "allScope"
            ? true
            : pId === state.treeData[0].objectId
      },
      data => {
        commit("setChildrenTreeData", { data, pId });
      }
    );
  },
  // 更新合并组下的组织个数
  async updateOrganizationEntityChildNum({ commit, state }, pId) {
    const {
      monthVal: period,
      versionVal: version,
      yearVal: year
    } = state.pageDimObj;
    const res = await utils.updateOrganizationEntityChildNum({
      period,
      version,
      year,
      id: state.treeData[0].key,
      updateId: pId
    });
    commit("updateTreeNodeEntityChildNum", res.data ? res.data.data : []);
  },
  async getOffsetRule({ commit, state }) {
    const {
      data: { items }
    } = await organizationService("getAppParams");
    commit("updateOffsetRule", items[0].paramValue);
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
