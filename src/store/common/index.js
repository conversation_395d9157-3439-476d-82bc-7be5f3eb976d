import commonService from "@/services/common";
import { APPS } from "@/config/SETUP";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { isType } from "@/utils/common";
import PlatformCommunication from "@/utils/platformCommunication";
const state = {
  lang: "",
  tabs: [], // 所有tab 信息
  tabActiveId: "", //  当前选中的tab id
  menuList: [], // 所有菜单信息
  menuMapObj: {}, // 所有菜单对象
  keepAliveList: {},
  modalEvent: {}, // 消息弹框
  languages: [], // 语种信息
  currentUserInfo: {} // 用户信息
};

const getters = {
  mapTabObj: state => {
    const tempObj = {};
    state.tabs.map(item => {
      tempObj[item.id] = item;
    });
    return tempObj;
  }
};

const mutations = {
  getUserInfo(state, params) {
    state.currentUserInfo = params;
  },
  addTab(state, params) {
    const { tabInfo } = params;
    state.tabs.push(tabInfo);
    this.commit("common/selectTab", tabInfo.id);
  },
  addModal(state, { id, modals }) {
    const isArray = Array.isArray(modals);
    modals = isArray ? modals : [modals];
    if (state.modalEvent[id]) {
      state.modalEvent[id] = new Set([...state.modalEvent[id], ...modals]);
    } else {
      state.modalEvent[id] = new Set(modals);
    }
  },
  resolveModal(state, { id, status }) {
    const modals = state.modalEvent[id];
    if (modals) {
      const deleted = [];
      for (const item of modals.values()) {
        if (isType(item, "Function")) {
          const modal = item();
          this.commit("common/removeModal", { id: id, modals: item });
          this.commit("common/addModal", { id: id, modals: modal });
        } else {
          item.exist() ? item[status]() : deleted.push(item);
        }
      }
      this.commit("common/removeModal", { id: id, modals: deleted });
    }
  },
  updateTab(state, { id, params }) {
    const { tabs } = state;
    for (let i = 0; i < tabs.length; i++) {
      const { id: currId } = tabs[i];
      if (currId === id) {
        tabs[i].params = params;
        break;
      }
    }
    // 平台 修改tab签信息
    if (!this.selectTab) {
      PlatformCommunication.updateTab(params);
    }
  },
  removeModal(state, { id, modals }) {
    id = id || state.tabActiveId;
    if (!state.modalEvent[id]) return;
    if (modals) {
      const isArray = Array.isArray(modals);
      modals = isArray ? modals : [modals];
      modals.forEach(modal => {
        state.modalEvent[id].delete(modal);
      });
    } else {
      state.modalEvent[id].forEach(modal => {
        modal.exist() && modal.destory();
      });
      state.modalEvent[id].clear();
    }
  },
  remove(state, params) {
    const { id } = params;
    state.tabs = state.tabs.filter((item, index) => {
      return item.id !== id;
    });
    if (state.tabActiveId !== id) return;
    // 如果还存在tab 选中 最后一个tab签
    if (state.tabs.length) {
      const { id: tabId } = state.tabs[state.tabs.length - 1];
      this.commit("common/selectTab", tabId);
      this.commit("common/resolveModal", { id: tabId, status: "show" });
    } else {
      this.commit("common/selectTab", "");
    }
  },
  // 修改 tab 标题
  updateTabTitle(state, params) {
    const { id, title } = params;
    // 平台 修改tab签信息
    if (!this.selectTab) {
      PlatformCommunication.updateTab({ title });
    }
    state.tabs.forEach(tab => {
      if (tab.id === id) {
        tab.title = title;
      }
    });
  },
  selectTab(state, id) {
    state.tabActiveId = id;
  },
  setLanguages(state, data) {
    let lang = state.lang;
    if (!state.lang) {
      lang = DsUtils.getSessionStorageItem("lang", {
        storagePrefix: APPS.NAME,
        isJson: true
      });
      state.lang = lang;
    }
    state.languages = data.filter(item => item.code !== lang);
  },
  setMenuInfo(state, menuList) {
    const menuListMapObj = {};
    menuList.reduce((mapObj, item) => {
      mapObj[item.menuId] = item;
      return mapObj;
    }, menuListMapObj);
    menuList.map(item => {
      const { parentId } = item;
      if (parentId) {
        menuListMapObj[parentId] &&
          menuListMapObj[parentId].children.push(item);
      }
    });
    const treeMenuData = Object.values(menuListMapObj).filter(item => {
      if (!item.parentId) {
        return item;
      }
    });
    state.menuMapObj = menuListMapObj;
    state.menuList = treeMenuData;
  }
};

const actions = {
  getSystemMenuList: ({ commit }, params) => {
    const { appId, userLoginName } = params;
    return new Promise(resolve => {
      commonService("getSystemMenuList", {
        appId,
        userLoginName
      }).then(res => {
        commit("setMenuInfo", res.data.data);
        resolve();
      });
    });
  },
  async getEnableLanguages(context) {
    const res = await commonService("getEnableLanguages");
    context.commit("setLanguages", res.data.data);
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
