import reconciliationService from "@/services/reconciliation";
import commonService from "@/services/common";
import Logger from "yn-p1/libs/modules/log/logger";
const state = {
  selectTreeNode: {},
  treeData: [],
  reconciliationConfigObj: {}, // 对账配置信息
  hasLeaf: false, // 是否有叶子节点
  reportInfo: {},
  hasAuthority: true, // 当前用户是否拥有公有报表权限
  mainLoading: false,
  authSet: {}, // 权限集权限
  collaborativeStatus: false,
  batchType: "", // 批量修改对账状态类型
  selection: [] // 批量选择项
};
const mutations = {
  setTreeData(state, treeData) {
    state.treeData = treeData;
  },
  setSelectTreeNode(state, treeNodeData) {
    state.selectTreeNode = treeNodeData;
  },
  changeHasLeafStatus(state, hasLeaf) {
    state.hasLeaf = hasLeaf;
  },
  setConfig(state, obj) {
    const { id, config } = obj;
    state.reconciliationConfigObj[id] = config;
  },
  clearConfig() {
    state.reconciliationConfigObj = {};
  },
  setReportInfo(state, reportInfo) {
    state.reportInfo = reportInfo;
  },
  setAuth(state, auth) {
    state.hasAuthority = auth;
  },
  setAuthObj(state, authSet) {
    state.authSet = authSet;
  },
  setCollaborativeStatus(state, collaborativeStatus) {
    state.collaborativeStatus = collaborativeStatus;
  },
  controlMainLoading(state, status = false) {
    state.mainLoading = status;
  },
  setBatchType(state, batchType) {
    state.batchType = batchType;
  },
  setSelection(state, selection) {
    state.selection = selection;
  }
};
const actions = {
  async asyncGenerateReport({ commit }, queryParams) {
    let res = {};
    try {
      res = await reconciliationService("asyncGenerateReport", queryParams);
    } catch (e) {
      return Promise.reject();
    }
    return res.data;
  },
  async getPublicAuth({ commit }, queryParams) {
    let auth = true;
    try {
      const res = await reconciliationService("getPublicAuth");
      auth = res.data;
    } catch (e) {
      Logger.error(`对账公有权限接口报错：${e}`);
    }

    commit("setAuth", auth);
  },
  async getAuthSet({ commit }) {
    let authSet = {};
    const {
      data: { items }
    } = await commonService("getMetadataRecords", "balanceReconciliationAuth");
    const refId = items.length ? items[0].objectId : "";
    await reconciliationService("getBalanceReconciliationAuth", {
      privilegeCode: "Delete",
      refId
    }).then(res => {
      if (res.data && res.data.items) {
        const vList = [];
        const { items } = res.data;
        items.forEach(item => {
          vList.push(item.value === "TRUE");
          authSet[item.privilegeCode] = item.value === "TRUE";
        });
        // 其他权限都包含查看权限
        authSet["balanceReconciliation-read"] = vList.some(v => v);
        // 新增权限包含 编辑权限
        if (authSet["balanceReconciliation-add"]) {
          authSet["balanceReconciliation-edit"] = true;
        }
      }
    });
    authSet = mapping(authSet, "balanceReconciliation-");
    commit("setAuthObj", authSet);
  },
  // 获取协同状态
  async getCollaborativeStatus({ commit }, queryParams) {
    const collaborativeStatus = true;
    commit("setCollaborativeStatus", collaborativeStatus);
  }
};
/**
 * 转换 authSet
 * @param {*} authSet
 * @returns
 */
const mapping = (authSet, cur) => {
  const newAuthSet = {};
  for (const key in authSet) {
    const newK = key.split(cur)[1];
    newAuthSet[newK] = authSet[key];
  }
  return newAuthSet;
};
const getters = {};
export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
