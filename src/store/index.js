import Vue from "vue";
import Vuex from "vuex";
import routerState from "./navi/";
import themeState from "./theme/";
import common from "./common";
import organization from "./organization";
import rearrange from "./rearrange";
import currentoffset from "./currentoffset";
import reconciliation from "./reconciliation";
import voucherReconciliation from "./voucherReconciliation";
import scriptManagement from "./scriptManagement";
import ruleSetMaintenance from "./ruleSetMaintenance";
import processStore from "./process";
import todoStore from "./todo";
import createLogger from "vuex/dist/logger";

Vue.use(Vuex);

const debug = process.env.NODE_ENV !== "production";

export default new Vuex.Store({
  namespaced: true,
  modules: {
    routerState,
    themeState,
    common,
    organization,
    reconciliation,
    voucherReconciliation,
    rearrange,
    ruleSetMaintenance,
    currentoffset,
    processStore,
    todoStore,
    scriptManagement
  },
  strict: debug,
  plugins: debug ? [createLogger()] : []
});
