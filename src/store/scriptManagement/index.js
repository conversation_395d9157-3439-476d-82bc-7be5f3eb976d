import precessService from "@/services/process";

export default {
  namespaced: true,
  actions: {
    // 获取单条脚本数据
    async getScriptItem(context, params) {
      const res = await precessService("getScriptItem", params);
      return res;
    },
    // 获取沙箱list
    async getSandboxList(context, params) {
      const res = await precessService("getSandboxList", params);
      return res;
    },
    // 执行
    async executeScript(context, params) {
      return await precessService("executeScript", params);
    },
    async getSelectTreeByExp(context, params) {
      return await precessService("getSelectTreeByExp", params);
    },
    // 获取-维度-成员
    async getDimMembers(context, params) {
      const res = await precessService("getDimMembers", params);
      const result = res && res.data ? res.data.items : [];
      const newRes = result.map(item => {
        const {
          dimMemberName,
          objectId,
          dimMemberHasChildren,
          dimMemberParentId
        } = item;
        return {
          ...item,
          label: dimMemberName,
          title: dimMemberName,
          key: objectId,
          type: "member",
          isLeaf: !dimMemberHasChildren,
          value: objectId,
          parentId: dimMemberParentId,
          scopedSlots: { title: "custom" }
        };
      });
      return newRes;
    }
  }
};
