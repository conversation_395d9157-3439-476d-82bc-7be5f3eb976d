import ruleSetMaintenanceService from "@/services/ruleSetMaintenance";

const state = {
  total: 0,
  tableData: []
};

const mutations = {
  setRequestData(state, params) {
    const { total, item } = params;
    state.total = total;
    state.tableData = item;
  }
};

const actions = {
  getRuleSetMaintenance({ commit }) {
    ruleSetMaintenanceService("getData").then(res => {
      commit("setRequestData", res.data);
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
