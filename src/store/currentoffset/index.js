import currentoffsetService from "@/services/currentoffset";
import { paveTree } from "../../utils/common";
import cloneDeep from "lodash/cloneDeep";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import { APPS } from "@/config/SETUP";
const state = {
  pageStatus: {}, // 新增newAdd、编辑edit、详情detail
  accountData: [], // 科目数据
  assetsExpenseAccount: [], // 资产科目数据
  liabilitiesIncomeAccount: [], // 负债科目数据
  paveAccountData: {}, // 铺平之后的科目数据
  raData: [], // 科目数据
  paveRAData: {} // 铺平之后的科目数据
};

const addKeyToData = data => {
  if (!Array.isArray(data)) return;
  const loop = list => {
    list.forEach(item => {
      item.key = item.id;
      item.label = item.name;
      if (item.children && item.children.length) {
        loop(item.children);
      }
    });
  };
  loop(data);
};

const mutations = {
  updatePageStaus(state, params) {
    const copyPageStatus = cloneDeep(state.pageStatus);
    copyPageStatus[params.key] = params.status;
    state.pageStatus = copyPageStatus;
  },

  setAccountData(state, data) {
    // state.accountData = data;
    // state.paveAccountData = paveTree(data);
    const { assetsExpenseAccount, liabilitiesIncomeAccount } = data;
    state.assetsExpenseAccount = assetsExpenseAccount;
    state.liabilitiesIncomeAccount = liabilitiesIncomeAccount;
    // DsUtils.setSessionStorageItem("accountData", data, {
    //   storagePrefix: APPS.NAME,
    //   isJson: true
    // });
    // DsUtils.setSessionStorageItem("paveAccountData", state.paveAccountData, {
    //   storagePrefix: APPS.NAME,
    //   isJson: true
    // });
  },

  setRAData(state, data) {
    state.raData = data;
    state.paveRAData = paveTree(data);
    DsUtils.setSessionStorageItem("raData", data, {
      storagePrefix: APPS.NAME,
      isJson: true
    });
    DsUtils.setSessionStorageItem("paveRAData", state.paveRAData, {
      storagePrefix: APPS.NAME,
      isJson: true
    });
  }
};

const actions = {
  // getAccountMembers 之前的。  getOffsetAccount 迭代后数据过滤的
  getAccountData({ commit }) {
    currentoffsetService("getOffsetAccount").then(res => {
      // 处理资产数据
      addKeyToData(res.data.assetsExpenseAccount);
      // 处理负债数据
      addKeyToData(res.data.liabilitiesIncomeAccount);
      commit("setAccountData", res.data);
    });
  },

  getRAData({ commit }) {
    currentoffsetService("getRAMembers").then(res => {
      addKeyToData(res.data);
      commit("setRAData", res.data);
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
