import rearrangeService from "@/services/rearrange";
import { addKeyToData, paveTree } from "../../utils/rearrange";
import cloneDeep from "lodash/cloneDeep";
const state = {
  pageStatus: {}, // 新增newAdd、编辑edit、详情detail
  accountData: [], // 科目数据
  paveAccountData: {} // 铺平之后的科目数据
};

const mutations = {
  updatePageStaus(state, params) {
    const copyPageStatus = cloneDeep(state.pageStatus);
    copyPageStatus[params.key] = params.status;
    state.pageStatus = copyPageStatus;
  },
  setAccountData(state, data) {
    state.accountData = data;
    state.paveAccountData = paveTree(data);
  }
};

const actions = {
  getAccountData({ commit }) {
    rearrangeService("getAccountMembers").then(res => {
      addKeyToData(res.data);
      commit("setAccountData", res.data);
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
