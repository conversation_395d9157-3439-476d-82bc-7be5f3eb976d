export const state = {
  params: {},
  viewParams: {},
  isReport: false, // 是否填报
  batchType: "", // 批量操作类型
  batchNumber: 0, // 批量操作数量
  auditAbleState: [], // 可驳回的状态统计
  auditTableFilter: {},
  selection: [], // 选择的数据
  tableFilter: {}, // 流程表过滤条件
  batchList: [],
  nodeNameSummary: [], // 节点名称汇总
  checkStateSummary: [], // 流程状态汇总
  actionCol: 5,
  fileOperateCol: 3
};
export const mutations = {
  setParams(state, res) {
    state.params = { ...res };
  },
  setActionCol(state, actionCol) {
    state.actionCol = actionCol;
  },
  setFileOperateCol(state, fileOperateCol) {
    state.fileOperateCol = fileOperateCol;
  },
  setIsReport(state, res) {
    state.isReport = res;
  },
  setViewParams(state, res) {
    state.viewParams = { ...res };
  },
  setBatchType(state, res) {
    state.batchType = res;
  },
  setBatchNumber(state, res) {
    state.batchNumber = res;
  },
  setSelection(state, res) {
    state.selection = [...res];
    state.batchNumber = res.length;
  },
  setBatchList(state, res) {
    state.batchList = [...res.filter(item => item)];
  },
  setTableFilter(state, res) {
    state.tableFilter = { ...res };
  },
  setAuditTableFilter(state, res) {
    state.auditTableFilter = { ...res };
  },
  setNodeNameSummary(state, res) {
    state.nodeNameSummary = [...new Set(res)];
  },
  setCheckStateSummary(state, res) {
    state.checkStateSummary = [...new Set(res)];
  },
  setAuditAbleState(state, res) {
    state.auditAbleState = [...new Set(res)];
  },
  resetProcessControlStore(state) {
    state.params = {};
    state.viewParams = {};
    state.batchType = ""; // 批量操作类型
    state.batchNumber = 0; // 批量操作数量
    state.auditAbleState = []; // 可驳回的状态统计
    state.auditTableFilter = {};
    state.selection = []; // 选择的数据
    state.tableFilter = {}; // 流程表过滤条件
    state.nodeNameSummary = []; // 节点名称汇总
    state.checkStateSummary = []; // 流程状态汇总
  }
};
