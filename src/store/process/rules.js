import precessService from "@/services/process";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import { PROCESS_RULE_CODE } from "@/constant/templateCode";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
import DsUtils from "yn-p1/libs/utils/DsUtils";

export const state = {
  ruleIsEdit: false, // write
  ruleSaving: false, // 保存进度
  ruleBaseInfo: {},
  ruleValidate: true,
  ruleCmpValidate: true,
  rulePeriod: {},
  ruleCompany: {},
  ruleFlow: [],
  ruleSpinning: false,
  ruleIsSearch: false,
  ruleSelectTreeNode: null,
  ruleResult: {}
};
export const mutations = {
  setRuleIsEdit(state, model) {
    state.ruleIsEdit = model;
  },
  setRuleIsSearch(state, ruleIsSearch) {
    state.ruleIsSearch = ruleIsSearch;
  },
  setRuleBaseInfo(state, info) {
    state.ruleBaseInfo = { ...info };
  },
  setRuleSaving(state, process) {
    state.ruleSaving = process;
  },
  setRulePeriod(state, rulePeriod) {
    state.rulePeriod = rulePeriod;
  },
  setRuleCompany(state, ruleCompany) {
    state.ruleCompany = ruleCompany;
  },
  setRuleFlow(state, ruleFlow) {
    state.ruleFlow = ruleFlow;
  },
  setRuleSpinning(state, ruleSpinning) {
    state.ruleSpinning = ruleSpinning;
  },
  setRuleValidate(state, ruleValidate) {
    state.ruleValidate = ruleValidate;
  },
  setRuleCmpValidate(state, ruleCmpValidate) {
    state.ruleCmpValidate = ruleCmpValidate;
  },
  setRuleSelectTreeNode(state, treeNode) {
    const node = { ...treeNode };
    delete node.children;
    state.ruleSelectTreeNode = node;
  },
  setRuleResult(state, ruleResult) {
    state.ruleResult = ruleResult;
  }
};

function resetField(record, field, value = "") {
  if (Array.isArray(field)) {
    field.forEach(key => {
      record[key] = value;
    });
  } else {
    record[field] = value;
  }
}
function formatterParams(state) {
  const {
    ruleBaseInfo,
    ruleFlow,
    ruleSelectTreeNode,
    ruleCompany,
    rulePeriod
  } = state;
  const {
    objectId,
    multiLanguages,
    sourceObjectId,
    companyObjectId,
    templateName
  } = ruleBaseInfo;

  const currentLocale = UrlUtils.getQuery("lang") ||
    DsUtils.getSessionStorageItem("lang") ||
    DsUtils.getLocalStorageItem("lang") ||
    "zh_CN";
  let templateVersion = "";
  if (templateName && templateName.indexOf("_") > -1) {
    templateVersion = templateName.split("_")[1];
  }
  const languages = multiLanguages.map(item => ({
    ...item,
    text: item.languageCode === currentLocale
      ? templateName : `${item.text}_${templateVersion}`
  }));
  return {
    objectId: ruleSelectTreeNode.flag && sourceObjectId ? "" : objectId,
    processVerificationList: ruleFlow.map(item => {
      const isResult = item.verificationType === "result";
      if (isResult) {
        resetField(item, ["rightFormUrl", "leftFormUrl"]);
      } else {
        resetField(item, "resultFormUrl");
      }
      return item;
    }),
    multiLanguages: languages,
    commonTemplateDTO: {
      ...ruleBaseInfo,
      multiLanguages: languages,
      objectId: ruleSelectTreeNode.flag && sourceObjectId ? "" : objectId
    },
    memberExp: ruleCompany,
    operateType: sourceObjectId ? "add" : companyObjectId ? "update" : "add",
    templateFlag: true,
    companyObjectId: sourceObjectId ? "" : companyObjectId,
    sourceObjectId: sourceObjectId,
    templatePeriod: rulePeriod.period.join(","),
    templateVersion: rulePeriod.version.join(","),
    templateYear: rulePeriod.year.join(",")
  };
}

export const action = {
  async validateName({ state }) {
    const { templateName, objectId } = state.ruleBaseInfo;
    return precessService("checkTemplateName", {
      objectId: objectId,
      operateType: "update",
      templateName: templateName,
      templateModule: PROCESS_RULE_CODE,
      templateFlag: true
    }).then(res => {
      if (res.data) {
        return true;
      } else {
        UiUtils.errorMessage("配置名称重复");
        return false;
      }
    });
  },
  async saveRule({ commit, state }) {
    const { ruleCmpValidate, ruleBaseInfo, ruleSelectTreeNode } = state;
    if (!ruleCmpValidate) return;
    const sign = await this.dispatch("processStore/validateName");
    if (sign) {
      const { templateName } = ruleBaseInfo;
      const params = formatterParams(state);
      commit("setRuleSaving", true);
      return precessService("addEditTemplateInfo", params)
        .then(res => {
          const {
            success,
            data: { memberNameList, objectId, message }
          } = res.data;
          if (success) {
            UiUtils.successMessage("保存成功");
            commit("setRuleIsEdit", false);
            commit("setRuleBaseInfo", {
              ...ruleBaseInfo,

              objectId
            });
            commit("setRuleSelectTreeNode", {
              ...ruleSelectTreeNode,
              sourceObjectId: "",
              templateName: templateName,
              flag: false,
              key: objectId,
              objectId
            });
            commit("setRuleResult", {
              objectId: objectId,
              parentId: ruleSelectTreeNode.parentId
            });
            return { id: objectId };
          } else {
            if (!memberNameList && message) {
              return UiUtils.errorMessage(message);
            }
            return Promise.reject(memberNameList);
          }
        })
        .finally(() => {
          commit("setRuleSaving", false);
        });
    } else {
      return Promise.reject();
    }
  }
};
