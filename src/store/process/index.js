import taskFlowServeice from "@/services/taskFlowTemp";
import { pavingTree, handleFormData } from "@/utils/taskFlowTemp.js";
import {
  state as controlState,
  mutations as controlMutations
} from "./control";

import {
  state as ruleState,
  mutations as ruleMutations,
  action as ruleActions
} from "./rules";

import { state as watchState, mutations as watchMutations } from "./watch";

const state = {
  formList: [],
  pavingFormList: [],
  ...watchState,
  ...controlState,
  ...ruleState
};

const mutations = {
  setFormData(state, res) {
    state.formList = res;
    state.pavingFormList = pavingTree(res);
  },
  ...watchMutations,
  ...controlMutations,
  ...ruleMutations
};

const actions = {
  async getFormList({ commit }) {
    await taskFlowServeice("getSceneList").then(res => {
      if (res.data.length) {
        const result = res.data;
        handleFormData(result);
        commit("setFormData", result);
      }
    });
  },
  ...ruleActions
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
