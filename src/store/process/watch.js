export const state = {
  watchParams: {},
  cardLoading: false,
  sortLoading: false,
  watchViewParams: {},
  setting: [],
  scopeInfo: {},
  cardsData: [],
  archInfo: {
    level: 0
  },
  refresh: 0
};
export const mutations = {
  setWatchParams(state, res) {
    state.watchParams = { ...res };
  },
  setWatchViewParams(state, res) {
    state.watchViewParams = { ...res };
  },
  setSetting(state, res) {
    state.setting = [...res];
  },
  setScopeInfo(state, res) {
    state.scopeInfo = { ...res };
  },
  setCardsData(state, res) {
    state.cardsData = [...res];
  },
  setCardLoading(state, res) {
    state.cardLoading = res;
  },
  setSortLoading(state, res) {
    state.sortLoading = res;
  },
  setArchInfo(state, Info) {
    state.archInfo = { ...Info };
  },
  setRefresh(state, refresh) {
    state.refresh = refresh;
  }
};
