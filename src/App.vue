<template>
  <div id="app">
    <yn-about />
    <yn-spin :spinning="spinning" size="large">
      <yn-locale-provider :locale="locale">
        <Tabs @changeSpinning="handleSpinning" />
      </yn-locale-provider>
    </yn-spin>
  </div>
</template>

<script>
import Tabs from "@/components/hoc/tabs";
import YnAbout from "./views/version/About.vue";
import "yn-p1/libs/themes/style/yn-app.less";
import "yn-p1/libs/components/yn-spin/";
import "yn-p1/libs/components/yn-locale-provider/";
import UiUtils from "yn-p1/libs/utils/UiUtils";
import DsUtils from "yn-p1/libs/utils/DsUtils";
import SecurityUtils from "yn-p1/libs/utils/SecurityUtils";
import RouterUtils from "yn-p1/libs/utils/RouterUtils";
import { APPS, BACKEND } from "@/config/SETUP";

import Logger from "yn-p1/libs/modules/log/logger";
import moment from "moment";
import "moment/locale/zh-cn";
import "@/themes/theme.less";
import commonService from "@/services/common";
import UrlUtils from "yn-p1/libs/utils/UrlUtils";
import zh_CN from "yn-p1/libs/assets/platform/local/zh_CN";
import en_US from "yn-p1/libs/assets/platform/local/en_US";
import { getFirstPathNameFromUrl } from "@/utils/common.js";
// import api from "@/services/api";
const LANGUAGE_MAP = {
  zh_CN,
  en_US
};
export default {
  name: "App",
  components: {
    Tabs,
    YnAbout
  },
  data() {
    return {
      screenWidth: 0,
      spinning: true,
      locale: null
    };
  },
  watch: {
    $route(to, from) {
      if (to.query.lang !== from.query.lang) {
        this.getLocale();
      }
    }
  },
  beforeCreate() {
    let BASE_URL = BACKEND.BASE_URL;
    let TOKEN = UrlUtils.getQuery("TOKEN");
    let lang = UrlUtils.getQuery("lang");
    let appId = UrlUtils.getQuery("appId");
    let menuId = UrlUtils.getQuery("menuId");
    let roleId = UrlUtils.getQuery("roleId");
    let serviceName = UrlUtils.getQuery("serviceName");
    let securityFlag = UrlUtils.getQuery("securityFlag");
    let timeDelta = UrlUtils.getQuery("timeDelta");
    let origin = UrlUtils.getQuery("origin");
    let logoutTargetUrl = UrlUtils.getQuery("logoutTargetUrl");
    const debugServer = "";
    const debugIgnore = "";
    TOKEN = TOKEN && decodeURI(decodeURI(TOKEN));
    lang = lang && decodeURI(decodeURI(lang));
    appId = appId && decodeURI(decodeURI(appId));
    menuId = (menuId && decodeURI(decodeURI(menuId))) || "metadataMenu";
    roleId = roleId && decodeURI(decodeURI(roleId));
    serviceName = (serviceName && decodeURI(decodeURI(serviceName))) || "ecs";
    securityFlag = securityFlag && decodeURI(decodeURI(securityFlag));
    timeDelta = timeDelta && decodeURI(decodeURI(timeDelta));
    origin = origin && decodeURIComponent(origin);
    let inTab = UrlUtils.getQuery("inTab");
    const tabKey = "";
    inTab = (tabKey && decodeURI(decodeURI(inTab))) || menuId;
    Logger.log(origin);
    logoutTargetUrl = logoutTargetUrl && decodeURIComponent(logoutTargetUrl);

    if (debugServer) {
      // 调试服务
      BASE_URL = debugServer;
    } else if (BASE_URL.indexOf("/") === 0) {
      BASE_URL = window.location.origin;
    }
    let urlServiceName = serviceName;
    const EcsOrigin = BASE_URL || window.location.origin;
    DsUtils.addGlobalRequestInterceptor(
      config => {
        // eslint-disable-next-line prefer-const
        let { headers, url } = config || {};
        const { ServiceName, appId: selectAppId } = headers || {};
        headers.LoginToken = TOKEN;
        headers.appId = selectAppId || appId;
        headers.MenuId = menuId;
        headers.ServiceName = ServiceName || serviceName;
        urlServiceName = ServiceName || serviceName;
        if (!url.match(/^(http|https)/)) {
          if (!url.match(/^\//)) {
            url = "/" + url;
          }
          if (debugServer) {
            // 调试服务
            if (debugIgnore && debugIgnore === "true") {
              config.url = debugServer + url;
            } else {
              config.url = debugServer + "/" + urlServiceName + url;
            }
          } else {
            config.url = EcsOrigin + "/" + url;
          }
        }
        config.url = config.url.replace(/(?<=\:\/.*)(\/\/*)/g, "/");
        if (securityFlag && securityFlag === "true") {
          const sha = SecurityUtils.getShaId(
            timeDelta || DsUtils.getSessionStorageItem("timeDelta") || 0,
            TOKEN,
            config.url,
            config.data || config.params,
            config.headers["Content-Type"],
            config.method,
            !!config.params,
            config
          );
          const { stid, Timestamp } = sha;
          config.headers.stid = stid;
          config.headers.Timestamp = Timestamp;
          config.headers.ServiceName = getFirstPathNameFromUrl(config.url);
        }
        return config;
      },
      err => DsUtils.axiosErrorHandler(err)
    );
    let serverUrl = "";
    if (debugServer) {
      if (debugIgnore && debugIgnore === "true") {
        serverUrl = debugServer;
      } else {
        serverUrl = debugServer + "/" + urlServiceName + "/";
      }
    } else {
      serverUrl = EcsOrigin + "/";
    }
    DsUtils.addGlobalResponseInterceptor(
      response => DsUtils.axiosResponseHandler(response),
      err => DsUtils.axiosErrorHandler(err)
    );
    DsUtils.init(serverUrl, `${inTab}_${APPS.NAME || ""}`);
    DsUtils.setSessionStorageItem("lang", lang || "zh_CN", {
      storagePrefix: APPS.NAME,
      isJson: true
    })
      .setSessionStorageItem("TOKEN", TOKEN || "", {
        storagePrefix: APPS.NAME,
        isJson: true
      })
      .setSessionStorageItem("appId", appId, {
        storagePrefix: APPS.NAME,
        isJson: true
      })
      .setSessionStorageItem("MenuId", menuId, {
        storagePrefix: APPS.NAME,
        isJson: true
      })
      .setSessionStorageItem("roleId", roleId || "", {
        storagePrefix: APPS.NAME,
        isJson: true
      })
      .setSessionStorageItem("ServiceName", serviceName, {
        storagePrefix: APPS.NAME,
        isJson: true
      })
      .setSessionStorageItem("timeDelta", timeDelta, {
        storagePrefix: APPS.NAME,
        isJson: true
      })
      .setSessionStorageItem("securityFlag", securityFlag, {
        storagePrefix: APPS.NAME,
        isJson: true
      })
      .setSessionStorageItem("logoutTargetUrl", logoutTargetUrl, {
        storagePrefix: APPS.NAME,
        isJson: true
      })
      .setSessionStorageItem("origin", origin, {
        storagePrefix: APPS.NAME,
        isJson: true
      });
    UiUtils.init(this);
    RouterUtils.init(this);
  },
  async created() {
    this.getLocale();
    // this.getServerVer();
    await this.getCurrUser();
    await this.getMenuItem();
  },
  mounted() {
    const that = this;
    that.setWidth();
    window.onresize = () => {
      that.setWidth();
    };
  },
  methods: {
    getLocale() {
      const language = APPS.LANGUAGE || UrlUtils.getQuery("lang") || "zh_CN";
      if (language === "zh_CN") {
        moment.locale("zh-cn");
      }
      console.log(language);
      this.locale = LANGUAGE_MAP[language];
    },
    handleSpinning(status = false) {
      this.spinning = status;
    },
    async getCurrUser() {
      await commonService("getCurrentUser").then(res => {
        this.$store.commit("common/getUserInfo", res.data || {});
        DsUtils.setSessionStorageItem("currentUserInfo", res.data || {}, {
          storagePrefix: APPS.NAME,
          isJson: true
        });
      });
    },
    getSafeUrl(config) {
      const url = config.url.replace(/(?<=\:\/.*)(\/\/*)/g, "/");
      const index = url.indexOf("?");
      if (config.method === "get" && index !== -1 && config.iscode !== false) {
        // const query = UrlUtils.getQuery(null,url);
        const query = this.getSafeQuery(url);
        let base = url.slice(0, index + 1);
        for (const key in query) {
          base += `${key}=${query[key]}&`;
        }
        return base.replace(/&$/, "");
      }
      return url;
    },
    getSafeQuery(url) {
      const index = url.indexOf("?");
      const query = url.slice(index + 1);
      const params = {};
      // 获取所有key匹配， 参数内本身具备特殊字符，不能用 &，= 直接split切割字符串提取query
      const allMatch = [...query.matchAll(/[\w\d_$@!-]+?=/g)];
      allMatch.forEach((item, index) => {
        const start = item.index;
        const end = allMatch[index + 1]
          ? allMatch[index + 1].index - 1
          : query.length;
        const squery = query.slice(start, end);
        const sindex = squery.indexOf("=");
        const skey = squery.slice(0, sindex);
        const svalue = squery.slice(sindex + 1);
        params[skey] = encodeURIComponent(svalue);
      });
      return params;
    },
    getMenuItem() {
      const appId = DsUtils.getSessionStorageItem("appId", {
        storagePrefix: APPS.NAME,
        isJson: true
      });
      const currentUserInfo = DsUtils.getSessionStorageItem("currentUserInfo", {
        storagePrefix: APPS.NAME,
        isJson: true
      });
      this.$store.dispatch("common/getSystemMenuList", {
        appId,
        userLoginName: currentUserInfo.userLoginName
      });
    },
    // getServerVer() {
    //   DsUtils.get(api.getConsolidationServerVer).then(res => {
    //     if (res && res.data) {
    //       if (!window.APPVER) window.APPVER = {};
    //       if (!window.APPVER.servers) window.APPVER.servers = {};
    //       window.APPVER.servers.mddServer = res.data;
    //     }
    //   });
    // },
    setWidth() {
      this.screenWidth = document.body.offsetWidth;
    }
  }
};
</script>

<style lang="less">
html {
  overflow-x: auto;
}
#app .ant-spin-nested-loading {
  height: 100%;
  position: static; /* fix the tree context menu position is not right issue. */
}
#app .ant-spin-nested-loading > .ant-spin-container {
  position: static; /* fix the tree context menu position is not right issue. */
  height: 100%;
}

#app > .ant-spin-nested-loading > div > .ant-spin-spinning {
  max-height: inherit;
}

.confirmSave_mixin {
  .ant-modal-confirm-btns {
    .ant-btn {
      &:nth-child(2) {
        color: @yn-error-color;
        border: 1px solid @yn-error-color;
      }
    }
  }
}
.confirmSave_mixin_equity {
  .ant-modal-confirm-btns {
    .ant-btn {
      &:nth-child(2) {
        display: none;
      }
    }
  }
}
#app .yn-page-title {
  border-bottom: 0;
}
// ---- 修复全局提示被其他弹出层覆盖
// 平台的两个组件悬浮层级冲突了， 目前全局提示 model弹出层z-index =1000,
// select-tree-menu 悬浮层高度z-index = 1050 需要从组件层面处理
.ant-modal-wrap {
  z-index: 1056;
}
// ----- 修复全局提示被其他弹出层覆盖 end
</style>
