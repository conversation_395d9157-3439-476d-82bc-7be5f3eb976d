{"name": "consolidation-frontend", "version": "0.0.1", "private": true, "scripts": {"serve": "vue-cli-service serve --open", "build": "set LOG_STRING=`sh scripts/yn_p1_version.sh` && node scripts/integrate.js", "build:development": "set NODE_ENV=development && set LOG_STRING=`sh scripts/yn_p1_version.sh` && node scripts/integrate.js", "lint": "vue-cli-service lint", "lint:sonar": "vue-cli-service lint --format=json --max-errors=9999999999999 > report.json", "analyze": "use_analyzer=true npm run serve", "analyze:production": "use_analyzer=true npm run build", "test:unit": "vue-cli-service test:unit", "release": "gulp release -f scripts/gulp/gulpfile.js", "compile": "grunt ynConsolidation && gulp compile -f ./scripts/gulp/gulpfile.js", "publish:fix": "node publish.js fix", "publish:minor": "node publish.js minor", "publish:major": "node publish.js major", "publish:rc": "node publish.js rc", "publish": "node publish.js"}, "author": "<EMAIL>", "license": "See license on LICENSE file", "dependencies": {"@handsontable/vue": "^4.1.1", "@jsplumb/browser-ui": "5.6.4", "@jsplumb/connector-flowchart": "5.6.4", "@jsplumb/core": "5.6.4", "axios": "^0.19.0", "consolidation-frontend": "file:", "core-js": "^2.6.11", "decimal.js": "^10.3.1", "echarts": "^5.3.2", "esprima": "^4.0.1", "estraverse": "^4.3.0", "handsontable": "^7.2.2", "html2canvas": "^1.0.0-rc.5", "js-beautify": "^1.10.2", "jspdf": "^2.4.0", "lodash": "^4.17.21", "normalize.css": "^8.0.1", "prismjs": "^1.17.1", "qs": "^6.7.0", "reqwest": "^2.0.5", "screenfull": "^4.2.0", "sortablejs": "^1.15.0", "vue": "^2.6.10", "vue-custom-element": "^3.2.7", "vue-echarts": "6.2.0", "vue-infinite-scroll": "^2.0.2", "vue-prism-editor": "^0.3.0", "vue-router": "^3.0.3", "vuedraggable": "^2.23.0", "vuex": "^3.1.1", "yn-mdd": "1.0.0-C1-Planning-V20.20240930.1.3", "yn-p1": "3.2.75-dev"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@vue/cli-plugin-babel": "^3.8.0", "@vue/cli-plugin-eslint": "^3.8.0", "@vue/cli-plugin-unit-jest": "^3.8.0", "@vue/cli-service": "^3.8.0", "@vue/composition-api": "1.6.1", "@vue/eslint-config-prettier": "^4.0.1", "@vue/test-utils": "1.0.0-beta.29", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^10.0.1", "babel-jest": "^24.8.0", "babel-plugin-import": "1.13.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "cross-env": "^7.0.3", "eslint": "^5.16.0", "eslint-config-vue": "^2.0.2", "eslint-plugin-import": "^2.20.0", "eslint-plugin-vue": "^5.0.0", "express": "^4.17.1", "grunt": "1.5.3", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-copy": "^1.0.0", "grunt-string-replace": "^1.3.3", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-htmlmin": "^5.0.1", "gulp-if": "^3.0.0", "gulp-sourcemaps": "^2.6.5", "gulp-uglify": "^3.0.2", "gulp-uglify-es": "^2.0.0", "gulp-vue-file": "^0.1.4", "html-loader": "^0.5.5", "husky": "^3.1.0", "less": "^3.9.0", "less-loader": "^5.0.0", "lint-staged": "^9.5.0", "moment-locales-webpack-plugin": "^1.1.2", "node-fetch": "2.*", "prettier": "^1.19.1", "rimraf": "3.*", "source-map-loader": "^0.2.4", "style-resources-loader": "^1.5.0", "vue-template-compiler": "^2.6.10", "yn-webpack-plugin": "1.1.26"}, "eslintConfig": {"root": true, "env": {"browser": true, "node": true}, "extends": ["plugin:vue/essential", "@vue/prettier"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"./**/*.{js,jsx,vue,ts,tsx}": ["eslint --fix", "npm run lint", "git add"]}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"], "prettier": {"trailingComma": "none"}, "jest": {"moduleFileExtensions": ["js", "jsx", "json", "vue"], "transform": {"^.+\\.vue$": "vue-jest", ".+\\.(css|styl|less|sass|scss|svg|png|jpg|ttf|woff|woff2)$": "jest-transform-stub", "^.+\\.jsx?$": "babel-jest"}, "transformIgnorePatterns": ["/node_modules/"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}, "snapshotSerializers": ["jest-serializer-vue"], "testMatch": ["**/tests/unit/**/*.spec.(js|jsx|ts|tsx)|**/__tests__/*.(js|jsx|ts|tsx)"], "testURL": "http://localhost/", "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}}