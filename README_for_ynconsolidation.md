# 元年 CONSOLIDATION 组件库使用流程

## 简介

- 该组件库适仅适用于元年管理平台以及 C1 其他应用。
- 该组件库是基于 vue 平台进行搭建，目前只适用于 vue 项目。
- 该组件库中主要包括穿梭框以及其他相关组件。

## 通过 npm/cnpm 下载最新组件库（推荐使用 yarn)

命令如下：

```
npm install yn-consolidation
or
cnpm install yn-consolidation
or
yarn add yn-consolidation
```

## 通过 npm/cnpm 下载指定版本组件库

命令如下：

```
npm install yn-consolidation
or
cnpm install yn-consolidation
or
yarn add yn-consolidation
```

## 使用方法

- 只需在具体地方使用时，引入具体组件。方法如下：

```
...
import { Organization, Equity } from "yn-consolidation/libs/main/index.js";
...
```

## 其他组件库使用功能

如果有新组件或者工具发布，请联系@张玉成 或者 @宫超

### 维度管理组件

#### 使用

- **public/config.js 需要有 MDD 的相关配置**
  每个系统配置一样

```js
  CONSOLIDATION: {
    VUE_APP_CUSTOM_URL: baseUrl + "/**/bff",
    VUE_APP_BASE_URL: baseUrl + "/****"
  },
```

- **引用**

```js
import { Organization, Equity } from "yn-consolidation/libs/main/index.js";
```

##### **Demo**

```js
<template>
  <Organization
    ref="organization"
    class="organization"
  />
</template>
<script>
import { Organization, Equity } from "yn-consolidation/libs/main/index.js";

export default {
  components: {
    Organization,
  },
  data() {
  return {

  }
  }
};
</script>

```

##### **API**

#### 开发

- Gruntfile.js // filesSrc 维度相关文件打包配置
  _其余文件保持 CONSOLIDATION 功能_
