const PUBLISH_FOLDER = "dist_publish";
const YNCONSOLIDATION_PUBLISH_FOLDER = `${PUBLISH_FOLDER}/yn-consolidation`;
const filesSrc = [
  "commonLess/**",
  "directive/**",
  "mixin/**",
  "views/**",
  "components/**",
  "services/**",
  "store/**",
  "utils/**",
  "router/**",
  "image/**",
  "config/**",
  "locales/**",
  "constant/**",
  "assets/**",
  "main/**",
  "main/**"
];

module.exports = function(grunt) {
  grunt.initConfig({
    pkg: grunt.file.readJSON("package.json"),
    clean: {
      publish_folder: { src: [PUBLISH_FOLDER] },
      ynconsolidation_publish_folder: { src: [YNCONSOLIDATION_PUBLISH_FOLDER] }
    },
    copy: {
      ynconsolidation_components: {
        files: [
          {
            expand: true,
            cwd: "src/",
            src: filesSrc,
            dest: `${YNCONSOLIDATION_PUBLISH_FOLDER}/source`
          }
        ]
      },
      ynconsolidation_index_js: {
        src: "index_for_ynmdd.js",
        dest: `${YNCONSOLIDATION_PUBLISH_FOLDER}/source/index.js`
      },
      ynconsolidation_package_json: {
        src: "package_for_ynconsolidation.json",
        dest: `${YNCONSOLIDATION_PUBLISH_FOLDER}/package.json`
      },
      ynconsolidation_read_me: {
        src: "README_for_ynconsolidation.md",
        dest: `${YNCONSOLIDATION_PUBLISH_FOLDER}/README.md`
      },
      ynconsolidation_license: {
        src: "LICENSE",
        dest: `${YNCONSOLIDATION_PUBLISH_FOLDER}/LICENSE`
      },
      iconInfo: {
        files: [
          {
            expand: true,
            cwd: "public/",
            src: ["iconfont/**"],
            dest: `${YNCONSOLIDATION_PUBLISH_FOLDER}/source/assest`
          }
        ]
      }
    },
    "string-replace": {
      dist: {
        files: [
          {
            expand: true,
            cwd: "src/",
            src: filesSrc,
            dest: `${YNCONSOLIDATION_PUBLISH_FOLDER}/source`
          }
        ],
        options: {
          replacements: [
            {
              pattern: /@\/components/g,
              replacement: "yn-consolidation/libs/components"
            },
            {
              pattern: /@\/utils/g,
              replacement: "yn-consolidation/libs/utils"
            },
            {
              pattern: /@\/services/g,
              replacement: "yn-consolidation/libs/services"
            },
            {
              pattern: /@\/config/g,
              replacement: "yn-consolidation/libs/config"
            },
            {
              pattern: /@\/views/g,
              replacement: "yn-consolidation/libs/views"
            },
            {
              pattern: /@\/image/g,
              replacement: "yn-consolidation/libs/image"
            },
            {
              pattern: /@\/assets/g,
              replacement: "yn-consolidation/libs/assets"
            },
            {
              pattern: /@\/router/g,
              replacement: "yn-consolidation/libs/router"
            },
            {
              pattern: /@\/constant/g,
              replacement: "yn-consolidation/libs/constant"
            },
            {
              pattern: /@\/directive/g,
              replacement: "yn-consolidation/libs/directive"
            },
            {
              pattern: /@\/store/g,
              replacement: "yn-consolidation/libs/store"
            },
            {
              pattern: /@\/views/g,
              replacement: "yn-consolidation/libs/views"
            },
            {
              pattern: /\.\.\/public\/iconfont/g,
              replacement: "yn-consolidation/libs/assest/iconfont"
            },
            {
              pattern: /:deep\((.*?)\)/g,
              replacement: function(match, p1) {
                console.error("匹配目标: ", match);
                console.error("匹配项: ", p1);
                console.error("替换后: ", `/deep/ ${p1}`, "\n");
                return `/deep/ ${p1}`;
              }
            },
            {
              pattern: /\~\@/g,
              replacement: "yn-consolidation/libs"
            }
          ]
        }
      }
    }
  });
  grunt.loadNpmTasks("grunt-contrib-copy");
  grunt.loadNpmTasks("grunt-contrib-clean");
  grunt.loadNpmTasks("grunt-string-replace");
  grunt.registerTask("ynConsolidation", [
    "clean:ynconsolidation_publish_folder",
    "copy:ynconsolidation_components",
    "copy:iconInfo",
    "copy:ynconsolidation_index_js",
    "copy:ynconsolidation_package_json",
    "copy:ynconsolidation_read_me",
    "copy:ynconsolidation_license",
    "string-replace"
  ]);
};
