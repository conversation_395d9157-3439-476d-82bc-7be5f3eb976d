## 合并开发须知

### 配置文件说明

#### 生产环境配置
- **[consolidation_config.js](./public/consolidation_config.js)**
  - 用于生产环境部署
  - 部署位置：`nginx/html/config/consolidation_config.js`
  - 所有合并环境的配置信息从此文件读取

#### 开发环境配置
- **[config.js](./public/config.js)**
  - 用于本地开发环境
  - 默认连接91环境
  - 切换环境：修改文件中的 `baseUrl` 变量

### 本地开发访问

1. 启动开发环境后，访问以下地址：
   ```
   http://localhost:8080/#/?selfTab=true&appId=3b81d5d055ef11e9b8da4572f5e1f83b&serviceName=consolidation&menuId=11ecd0171f617b32b2fa950cbe8c9532&menuName=%E6%B5%81%E7%A8%8B%E6%8E%A7%E5%88%B6&lang=zh_CN&TOKEN=11f07e32c7001088b83e892b1beaa8ea&securityFlag=false&timeDelta=140645&logoutTargetUrl=http%3A%2F%2F192.168.48.122%3A91%2Fecs_console%2Findex.html%23%2Flogin&sandboxId=default&origin=http%3A%2F%2F192.168.48.122%3A91%2Fecs_console%2Findex.html&newHomePageDesignerEnable=false&oauthToken=&inTab=11ecd0171f617b32b2fa950cbe8c9532&menuCode=&extends=
   ```

2. **重要提示**：将URL中的 `TOKEN` 参数替换为 `config.js` 中对应环境的有效token值

3. 修改token后即可正常访问应用主页面



## 必要步骤

### [使用元年私有仓储](https://www.tapd.cn/65863259/markdown_wikis/view/#1165863259001006951)

## 参考网址

### [元年脚手架使用流程](https://www.tapd.cn/65863259/markdown_wikis/view/#1165863259001009609)

### [Cookbook](http://*************:7000/#/cookbook)

### [开发环境及本地开发标准](https://www.tapd.cn/65863259/markdown_wikis/view/#1165863259001002526)

### [开发语言标准](https://www.tapd.cn/65863259/markdown_wikis/view/#1165863259001001334)

### [前端通用规范](https://www.tapd.cn/65863259/markdown_wikis/view/#1165863259001005664)

### [元年组件标准](https://www.tapd.cn/65863259/markdown_wikis/view/#1165863259001001242)

### [元年 VSCode 插件](https://marketplace.visualstudio.com/items?itemName=yuanian.yn-p1)

### [Vue 官网](https://cn.vuejs.org/v2/guide/index.html)

### [路由 Vue Router 已封装进 RouterUtils](https://cn.vuejs.org/v2/guide/index.html)

### [HTTP 请求已封装进 DsUtils](https://github.com/axios/axios)

### [前端问题反馈](https://www.tapd.cn/65863259/markdown_wikis/view/#1165863259001012242)

## 安装依赖

** 2020 年 3 月 30 号之前安装的 yn-p1 需要重新安装（yn-p1 改为读 package.json，使用最新版 yn-p1）**

** 推荐用 cnpm **

** 无论 cnpm 还是 npm 必须使用元年私有仓储 **

```
cnpm install

or

npm install
```

### 本地开发启动

```
npm run serve
```

### 部署打包

```
npm run build
```

### 运行 UT

```
npm run test:unit
```

### 文件结构说明

- .vscode
  --- vSCode IDE 设置
- node_modules
  --- npm module 使用的目录
- public
  --- index.html 和 网站 favorite icon.
- src
  --- 源文件目录 - config
  --- 配置文件夹。SETUP.js - 配置后端地址，navi/menuStructure.js - 配置菜单项
  - custom
    --- 二开代码目录 - store
    --- Vuex store 应用文件夹 - themes
    --- 元年 css 主题文件夹 - views
    --- 视图文件夹。建议所有应用代码都放在 views/applications 下新建一个文件夹。 - App.vue
    --- 最外层 vue 文件 - main.js
    --- main.js 文件
- tests
  --- 测试目录
- .gitignore
  --- git ignore 文件
- babel.config.js
  --- babel 配置文件
- LICENSE
  --- 版本说明文档
- package-lock.json
  --- npm module 具体依赖关系和版本文件
- package.json
  --- npm moudle 列表文件
- README.md
  --- 工程使用说明文档
- vue.config.js
  --- vue 配置文件

# 待实现

1. 参考 ECS 框架层实现多语言。
2. UT 标准
3. Theme

# Supports

## Vue 项目模板

1.  温立志

## MDV Grid 等表格

1. 宫超

## 公共组件

1. 潘柯华

## 手机端

1. 潘柯华

## 元年私有仓储

1. 温立志