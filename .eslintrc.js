module.exports = {
  parserOptions: {
    ecmaVersion: 6,
    sourceType: "module",
    parser: "babel-eslint"
  },
  rules: {
    "vue/singleline-html-element-content-newline": 0,
    "vue/max-attributes-per-line": 0,
    "no-console": "off",
    // indent: { ignoredNodes: ["ConditionalExpression"] },
    // offsetTernaryExpressions: true,
    // indent: {
    //   offsetTernaryExpressions: true
    // },
    "jsx-quotes": [2, "prefer-double"],
    quotes: [2, "double", { allowTemplateLiterals: true }],
    semi: [2, "always"],
    "space-before-function-paren": 0,
    camelcase: [0, { properties: "never" }],
    "arrow-parens": ["error", "as-needed"],
    "vue/attribute-hyphenation": [
      "error",
      "never",
      {
        ignore: []
      }
    ],
    "object-curly-spacing": [
      0,
      "always",
      {
        objectsInObjects: false
      }
    ],
    "vue/html-self-closing": [
      "error",
      {
        html: {
          void: "always",
          normal: "never",
          component: "always"
        },
        svg: "always",
        math: "always"
      }
    ]
  },
  extends: ["vue", "plugin:vue/recommended"],
  plugins: ["import", "vue"],
  env: {
    es6: true,
    browser: true,
    node: true
  }
};
