/* eslint-disable no-console */
const path = require("path");
const YnWebpackPlugin = require("yn-webpack-plugin");
const productionFlag = process.env.NODE_ENV === "production";
const publicPath = productionFlag
  ? typeof process.env.publicPath === "undefined"
    ? ""
    : process.env.publicPath
  : "";
const commandName = process.argv[2];
if (commandName !== "lint") {
  console.log(`productionFlag=${productionFlag}`);
  console.log(`publicPath=${publicPath}`);
}

function resolve(dir) {
  return path.join(__dirname, dir);
}

// 打包版本相关信息
process.env.VUE_APP_BRANCH_NAME = process.env.npm_config_branchName;
process.env.VUE_APP_VERSION = process.env.npm_config_appVersion;
process.env.VUE_APP_BUILDMODE = process.env.npm_config_buildMode;
process.env.VUE_APP_BUILDTIME = new Date();
process.env.VUE_APP_USER_NAME = "chenst";

module.exports = {
  publicPath,
  runtimeCompiler: !productionFlag,
  productionSourceMap: false,
  configureWebpack: config => {
    if (productionFlag) {
      //
    } else {
      config.devtool = "eval-source-map";
    }
  },
  lintOnSave: !productionFlag,
  chainWebpack: config => {
    // 设置自动化导入全局变量
    const types = ["vue-modules", "vue", "normal-modules", "normal"];
    types.forEach(type =>
      addStyleResource(config.module.rule("less").oneOf(type))
    );
    if (process.env.use_analyzer) {
      config
        .plugin("webpack-bundle-analyzer")
        .use(require("webpack-bundle-analyzer").BundleAnalyzerPlugin);
    }
    config
      .plugin("moment-locales-webpack-plugin")
      .use(require("moment-locales-webpack-plugin"), [
        { localesToKeep: ["zh-cn"] }
      ]);
    config.module
      .rule("html-loader")
      .test(/^((?!index).)*\.html$/)
      .use("html-loader")
      .loader("html-loader")
      .end();
    if (process.env.NODE_ENV !== "development") {
      config.plugin("html").tap(args => {
        args[0].configPath = "../config/consolidation_config.js";
        return args;
      });
    } else {
      config.plugin("html").tap(args => {
        args[0].configPath = "./config.js";
        return args;
      });
    }
    config.plugin("yn-webpack-plugin").use(YnWebpackPlugin, [
      {
        noSourceCode: {
          enabled: true
        },
        multiTheme: {
          enabled: true,
          otherBuiltInThemes: [
            // 这里添加新增的色系
            // {
            //   themeName: "newName",
            //   modifyVars: require(resolve("src/themes/themeLoaderNewName.js"))
            // },
            {
              themeName: "default",
              modifyVars: require(resolve("src/themes/themeLoaderDefault.js"))
            },
            {
              themeName: "autumnBlue",
              modifyVars: require(resolve(
                "src/themes/themeLoaderAutumnBlue.js"
              ))
            },
            {
              themeName: "nationalFlagRed",
              modifyVars: require(resolve(
                "src/themes/themeLoaderNationalFlagRed.js"
              ))
            },
            {
              themeName: "peacockGreen",
              modifyVars: require(resolve(
                "src/themes/themeLoaderPeacockGreen.js"
              ))
            },
            {
              themeName: "seaBlue",
              modifyVars: require(resolve("src/themes/themeLoaderSeaBlue.js"))
            }
          ]
        }
      }
    ]);
    YnWebpackPlugin.setupVueProject(config);
    config.resolve.alias.set("vue$", "vue/dist/vue.esm.js");
    config.resolve.alias.set("@", resolve("src"));
    if (productionFlag) {
      config.optimization.splitChunks({
        maxSize: 700000,
        minSize: 300000,
        maxInitialRequests: 6,
        chunks: chunk => YnWebpackPlugin.customFilter(chunk, "all"),
        cacheGroups: {
          styles: {
            name: "styles",
            test: module => module.type === "css/mini-extract",
            chunks: chunk => YnWebpackPlugin.customFilter(chunk, "all"),
            enforce: true,
            maxSize: Infinity,
            priority: 999 // 需要保证styles的cacheGroup优先级最高
          },
          YnP1: {
            name: "vendor-ynp1",
            test: /[\\/]node_modules[\\/]_?yn-p1/,
            priority: 90
          },
          default: {
            minChunks: 2,
            priority: -20
          }
        }
      });
    }
  },
  transpileDependencies: [
    "ant-design-vue",
    "vue-echarts",
    "resize-detector",
    "resize-detector"
  ],
  css: {
    loaderOptions: {
      less: {
        modifyVars: require(resolve("src/themes/themeLoaderDefault")),
        javascriptEnabled: true
      }
    }
  }
};
function addStyleResource(rule) {
  rule
    .use("style-resource")
    .loader("style-resources-loader")
    .options({
      patterns: [path.resolve(__dirname, "./src/themes/theme.less")]
    });
}
