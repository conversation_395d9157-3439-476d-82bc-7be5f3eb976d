// 开发环境config 配置，开发环境新增属性同步consolidation_config文件
// const baseUrl = "http://192.168.112.191:8080/"
// const baseUrl = "http://192.168.112.192:8080/"
// const baseUrl = "http://192.168.112.212:8080/";
// const baseUrl = "http://192.168.112.56:8080/";
// const baseUrl = "http://192.168.99.66:8080/";

// 开发人员自己的环境，前端自己调试用，不用可以删除
const xue_bing = "http://192.168.96.8:8881/";
const hu_ming = "http://192.168.97.165:8080/";
const local_back_end = "http://127.0.0.1:8888/";


// 开发、测试、hotfix 环境方便本地联调
const base_dev = "http://**************:81/";
const test_91 = "http://**************:91/";
const hotfix = "http://**************:81/";
// const xin_chuang = "http://**************:81/";


const baseUrl = test_91;

window.YN_ENV = {
  DASHBOARD: {
    VUE_APP_BASE_URL: "http://**************:8585/dashboard/",
    VUE_APP_BASE_URL_INIT: "http://**************:8585/dashboard/",
    VUE_APP_MOCK_BASE_URL: "https://localhost:3001/p1_platform/",
    VUE_APP_MDD_BASE_URL: "http://**************:8282/mdd",
    VUE_APP_DASHBOARD_BASE_URL: "http://**************:91/dashboard",
    VUE_APP_DASHBOARD_ATTACHMENT: "http://**************:8585/dashboard",
    VUE_APP_PLUGIN_BASE_URL: "http://**************:8585/dashboard/",
    VUE_APP_YNAI_BASE_URL: "//*************:28383/",
    VUE_APP_LOG_LEVEL: 4,
    VUE_APP_CUSTOM_COMPONENTS_BASE_URL: "",
    VUE_APP_DASHBOARD_FRONT_URL: baseUrl + "dashboard"
  },
  MR: {
    VUE_APP_MR_BASE_URL: baseUrl + "mr",
    VUE_APP_DASHBOARD_REPORT_VIEW_URL: baseUrl + "mr",
    VUE_APP_MR_FRONT_URL: baseUrl + "mr",
    VUE_APP_MDD_BASE_URL: baseUrl + "consolidation"
    // VUE_APP_MR_BASE_URL: "http://*************/mr/bff",
    // VUE_APP_DASHBOARD_REPORT_VIEW_URL:"http://*************/mr"
  },
  MDD: {
    VUE_APP_CUSTOM_URL: local_back_end + "consolidation/bff",
    VUE_APP_BASE_URL: local_back_end + "consolidation"
  },
  CONSOLIDATION: {
    // VUE_APP_CUSTOM_URL: "http://*************:8080/consolidation/bff",
    // VUE_APP_BASE_URL: "http://*************:8080/consolidation"
    // VUE_APP_CUSTOM_URL: "https://635lr48401.goho.co/consolidation/bff",
    // VUE_APP_BASE_URL: "https://635lr48401.goho.co/consolidation"
    // https://3tj1687310.imdo.co
    VUE_APP_CUSTOM_URL: local_back_end + "consolidation/bff",
    VUE_APP_BASE_URL: local_back_end + "consolidation"
  },
  ECS: {
    VUE_APP_HOST_NAME: "",
    VUE_APP_ECS_PORT: "",
    // VUE_APP_ECS_CONSOLE_BASE_URL: "http://*************/console", // 控制台后端地址
    // VUE_APP_ECS_BASE_URL: "http://*************/console/console/v2"
    VUE_APP_ECS_PLATFORM: baseUrl + "/ecs",
    VUE_APP_ECS_CONSOLE_BASE_URL: baseUrl + "/console", // 控制台后端地址
    VUE_APP_ECS_BASE_URL: baseUrl + "/console/console/v2"
  }
};