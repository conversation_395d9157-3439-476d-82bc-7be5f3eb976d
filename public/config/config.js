window.YN_ENV = {
  DASHBOARD: {
    VUE_APP_BASE_URL: "/dashboard/",
    VUE_APP_BASE_URL_INIT: "/",
    VUE_APP_MOCK_BASE_URL: "/p1_platform/",
    VUE_APP_MDD_BASE_URL: "/consolidation",
    VUE_APP_MR_BASE_URL: "/mr",
    VUE_APP_DASHBOARD_BASE_URL: "/dashboard",
    VUE_APP_DASHBOARD_ATTACHMENT: "/dashboard/",
    VUE_APP_PLUGIN_BASE_URL: "/dashboard/",
    VUE_APP_YNAI_BASE_URL: "",
    VUE_APP_LOG_LEVEL: 4,
    VUE_APP_CUSTOM_COMPONENTS_BASE_URL: ""
  },
  MDD: {
    VUE_APP_CUSTOM_URL: "/consolidation/bff",
    VUE_APP_BASE_URL: "/consolidation",
    VUE_APP_MR_BASE_URL: "/mr",
    VUE_APP_DASHBOARD_BASE_URL: "/dashboard",
    VUE_APP_TABASE_URL: "/mdd",
    VUE_APP_MDD_PORTAL_URL: "/mdd", //mdd 页面的访问地址，域名需要和mr的访问地址域名一致
    VUE_APP_MDD_FRONT_URL: baseUrl + "/mdd/#/"
  },
  MR: {
    VUE_APP_HOST_NAME: "",
    VUE_APP_MR_PORT: "",
    VUE_APP_BASE_URL: "/consolidation/metadata",
    VUE_APP_BASE_URL_INIT: "/",
    VUE_APP_MDD_BASE_URL: "/consolidation",
    VUE_APP_MR_BASE_URL: "/mr",
    VUE_APP_MDD_FRONT_URL: "/mdd",
    VUE_APP_DASHBOARD_BASE_URL: "",
    VUE_APP_DASHBOARD_PORTAL_URL:
      "/dashboard/#/dashboard_dt?loginName=admin&projectCode=mddengine&hideHeader=true&_self=true&source=109",
    VUE_APP_DASHBOARD_REPORT_VIEW_URL:
      "/dashboard/running.html#/dashboard_rt?loginName=admin&projectCode=mddengine&hideHeader=true&_self=true&source=109",
    VUE_APP_DASHBOARD_REPORT_PREVIEW_URL:
      "/dashboard/#/dashboard_pt?loginName=admin&projectCode=mddengine&hideHeader=true&_self=true&source=109",
    VUE_APP_DASHBOARD_REPORT_MOBILE_URL:
      "/dashboard/running.html#/dashboard_rt?loginName=admin&projectCode=mddengine&hideHeader=true&_self=true&source=109",
    VUE_APP_ZD_MOBILE_URL:
      "http://*************:26528/smartq-front/mobile.html#/ai?botId=1d9ac7aec27f11ebb99bcf63428b9c31&appId=11ebf8be0081e7b7a01f878f4c37540f"
  },
  ECS: {
    VUE_APP_HOST_NAME: "",
    VUE_APP_ECS_PORT: "",
    VUE_APP_ECS_PLATFORM: "/ecs",
    VUE_APP_ECS_CONSOLE_BASE_URL: "/console", //控制台的base
    VUE_APP_ECS_BASE_URL: "/console/console/v2" //v2版本ecs应用的base
  }
};
