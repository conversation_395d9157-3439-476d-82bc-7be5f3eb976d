// 生成环境config 配置，开发环境新增属性同步到此文件
window.YN_ENV = {
  DASHBOARD: {
    VUE_APP_BASE_URL: "http://**************:8585/dashboard/",
    VUE_APP_BASE_URL_INIT: "http://**************:8585/dashboard/",
    VUE_APP_MOCK_BASE_URL: "https://localhost:3001/p1_platform/",
    VUE_APP_MDD_BASE_URL: "http://**************:8282/mdd",
    VUE_APP_DASHBOARD_BASE_URL: "/dashboard",
    VUE_APP_DASHBOARD_ATTACHMENT: "http://**************:8585/dashboard",
    VUE_APP_PLUGIN_BASE_URL: "http://**************:8585/dashboard/",
    VUE_APP_YNAI_BASE_URL: "//*************:28383/",
    VUE_APP_LOG_LEVEL: 4,
    VUE_APP_CUSTOM_COMPONENTS_BASE_URL: "",
    VUE_APP_DASHBOARD_FRONT_URL: "/dashboard"
  },
  MR: {
    VUE_APP_MR_BASE_URL: "/mr",
    VUE_APP_MR_FRONT_URL: "/mr",
    VUE_APP_MDD_BASE_URL: "/consolidation"
  },
  MDD: {
    VUE_APP_CUSTOM_URL: "/consolidation/bff",
    VUE_APP_BASE_URL: "/consolidation",
    VUE_APP_MDD_FRONT_URL: "/mdd/#/"
  },
  CONSOLIDATION: {
    VUE_APP_CUSTOM_URL: "/consolidation/bff",
    VUE_APP_BASE_URL: "/consolidation"
  },
  ECS: {
    VUE_APP_HOST_NAME: "",
    VUE_APP_ECS_PORT: "",
    VUE_APP_ECS_PLATFORM: "/ecs",
    VUE_APP_ECS_CONSOLE_BASE_URL: "/console", // 控制台后端地址
    VUE_APP_ECS_BASE_URL: "/console/console/v2"
  }
};
