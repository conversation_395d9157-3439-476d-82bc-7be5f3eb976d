<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=3034203" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ce;</span>
                <div class="name">分批</div>
                <div class="code-name">&amp;#xe7ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8d3;</span>
                <div class="name">Data-details</div>
                <div class="code-name">&amp;#xe8d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe633;</span>
                <div class="name">c1_select-all-subsets</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe630;</span>
                <div class="name">copy</div>
                <div class="code-name">&amp;#xe630;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">C1_form_freeze</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe621;</span>
                <div class="name">arrow-down-line</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">more</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">multilingual</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">Header - Search - default</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">search</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">clock-circle</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe86c;</span>
                <div class="name">c1_cr_reply size</div>
                <div class="code-name">&amp;#xe86c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe86d;</span>
                <div class="name">c1_cr_minimize</div>
                <div class="code-name">&amp;#xe86d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe865;</span>
                <div class="name">c1_cr_analysis</div>
                <div class="code-name">&amp;#xe865;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe866;</span>
                <div class="name">c1_cr_accessory</div>
                <div class="code-name">&amp;#xe866;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe867;</span>
                <div class="name">c1_cr_file</div>
                <div class="code-name">&amp;#xe867;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe868;</span>
                <div class="name">c1_cr_Time</div>
                <div class="code-name">&amp;#xe868;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe854;</span>
                <div class="name">c1_cr_activity</div>
                <div class="code-name">&amp;#xe854;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe855;</span>
                <div class="name">c1_cr_end</div>
                <div class="code-name">&amp;#xe855;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe856;</span>
                <div class="name">c1_cr_begin</div>
                <div class="code-name">&amp;#xe856;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe857;</span>
                <div class="name">c1_cr_approval</div>
                <div class="code-name">&amp;#xe857;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe723;</span>
                <div class="name">field-people</div>
                <div class="code-name">&amp;#xe723;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d4;</span>
                <div class="name">up</div>
                <div class="code-name">&amp;#xe6d4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81f;</span>
                <div class="name">c1_cr_drag-and-drop</div>
                <div class="code-name">&amp;#xe81f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81a;</span>
                <div class="name">c1_cr_form_enter</div>
                <div class="code-name">&amp;#xe81a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe818;</span>
                <div class="name">c1_cr_export-drop down</div>
                <div class="code-name">&amp;#xe818;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe809;</span>
                <div class="name">cr_add</div>
                <div class="code-name">&amp;#xe809;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe800;</span>
                <div class="name">shuaxin</div>
                <div class="code-name">&amp;#xe800;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe801;</span>
                <div class="name">set-up</div>
                <div class="code-name">&amp;#xe801;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f6;</span>
                <div class="name">Process</div>
                <div class="code-name">&amp;#xe7f6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70f;</span>
                <div class="name">设置</div>
                <div class="code-name">&amp;#xe70f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec58;</span>
                <div class="name">edit</div>
                <div class="code-name">&amp;#xec58;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec44;</span>
                <div class="name">file-gif</div>
                <div class="code-name">&amp;#xec44;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec50;</span>
                <div class="name">file-png</div>
                <div class="code-name">&amp;#xec50;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec53;</span>
                <div class="name">file-rar</div>
                <div class="code-name">&amp;#xec53;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec54;</span>
                <div class="name">file-zip</div>
                <div class="code-name">&amp;#xec54;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec56;</span>
                <div class="name">file-picture</div>
                <div class="code-name">&amp;#xec56;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec57;</span>
                <div class="name">file-ppt</div>
                <div class="code-name">&amp;#xec57;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec59;</span>
                <div class="name">file-word</div>
                <div class="code-name">&amp;#xec59;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec5b;</span>
                <div class="name">file-txt</div>
                <div class="code-name">&amp;#xec5b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec5c;</span>
                <div class="name">file-Excel</div>
                <div class="code-name">&amp;#xec5c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec62;</span>
                <div class="name">file-pdf</div>
                <div class="code-name">&amp;#xec62;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec70;</span>
                <div class="name">file-unknown</div>
                <div class="code-name">&amp;#xec70;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb8d;</span>
                <div class="name">icon_设置</div>
                <div class="code-name">&amp;#xeb8d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c8;</span>
                <div class="name">穿梭框</div>
                <div class="code-name">&amp;#xe7c8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f3;</span>
                <div class="name">yn_cookbook_common_export</div>
                <div class="code-name">&amp;#xe7f3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e6;</span>
                <div class="name">lines</div>
                <div class="code-name">&amp;#xe7e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e7;</span>
                <div class="name">connection points</div>
                <div class="code-name">&amp;#xe7e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e8;</span>
                <div class="name">automatic</div>
                <div class="code-name">&amp;#xe7e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec4c;</span>
                <div class="name">enlarge</div>
                <div class="code-name">&amp;#xec4c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec73;</span>
                <div class="name">narrow</div>
                <div class="code-name">&amp;#xec73;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a1;</span>
                <div class="name">download</div>
                <div class="code-name">&amp;#xe7a1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">Import</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">记录</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xee3a;</span>
                <div class="name">导出</div>
                <div class="code-name">&amp;#xee3a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe671;</span>
                <div class="name">文件夹</div>
                <div class="code-name">&amp;#xe671;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79b;</span>
                <div class="name">purge</div>
                <div class="code-name">&amp;#xe79b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79d;</span>
                <div class="name">Shut down</div>
                <div class="code-name">&amp;#xe79d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79e;</span>
                <div class="name">visible</div>
                <div class="code-name">&amp;#xe79e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79f;</span>
                <div class="name">invisible</div>
                <div class="code-name">&amp;#xe79f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d7;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe7d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d8;</span>
                <div class="name">退出全屏</div>
                <div class="code-name">&amp;#xe7d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d9;</span>
                <div class="name">排序</div>
                <div class="code-name">&amp;#xe7d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7db;</span>
                <div class="name">保存</div>
                <div class="code-name">&amp;#xe7db;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7dc;</span>
                <div class="name">文件夹</div>
                <div class="code-name">&amp;#xe7dc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7dd;</span>
                <div class="name">文件01</div>
                <div class="code-name">&amp;#xe7dd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a6;</span>
                <div class="name">关于</div>
                <div class="code-name">&amp;#xe7a6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a8;</span>
                <div class="name">并集</div>
                <div class="code-name">&amp;#xe7a8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a9;</span>
                <div class="name">箭头下</div>
                <div class="code-name">&amp;#xe7a9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7aa;</span>
                <div class="name">向左收起</div>
                <div class="code-name">&amp;#xe7aa;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ab;</span>
                <div class="name">箭头右</div>
                <div class="code-name">&amp;#xe7ab;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ac;</span>
                <div class="name">上移</div>
                <div class="code-name">&amp;#xe7ac;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ad;</span>
                <div class="name">排除</div>
                <div class="code-name">&amp;#xe7ad;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ae;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe7ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7af;</span>
                <div class="name">下拉</div>
                <div class="code-name">&amp;#xe7af;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b0;</span>
                <div class="name">下移</div>
                <div class="code-name">&amp;#xe7b0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b1;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe7b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b2;</span>
                <div class="name">向右展开</div>
                <div class="code-name">&amp;#xe7b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b3;</span>
                <div class="name">交集</div>
                <div class="code-name">&amp;#xe7b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b4;</span>
                <div class="name">置顶</div>
                <div class="code-name">&amp;#xe7b4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b5;</span>
                <div class="name">右移</div>
                <div class="code-name">&amp;#xe7b5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b6;</span>
                <div class="name">收起下级</div>
                <div class="code-name">&amp;#xe7b6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b8;</span>
                <div class="name">上传</div>
                <div class="code-name">&amp;#xe7b8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ba;</span>
                <div class="name">展开下级</div>
                <div class="code-name">&amp;#xe7ba;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bb;</span>
                <div class="name">折叠</div>
                <div class="code-name">&amp;#xe7bb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bc;</span>
                <div class="name">警示</div>
                <div class="code-name">&amp;#xe7bc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bd;</span>
                <div class="name">筛选</div>
                <div class="code-name">&amp;#xe7bd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7be;</span>
                <div class="name">排序</div>
                <div class="code-name">&amp;#xe7be;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bf;</span>
                <div class="name">展开</div>
                <div class="code-name">&amp;#xe7bf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c0;</span>
                <div class="name">复制</div>
                <div class="code-name">&amp;#xe7c0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c1;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe7c1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c2;</span>
                <div class="name">删除icon</div>
                <div class="code-name">&amp;#xe7c2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c4;</span>
                <div class="name">文件柜</div>
                <div class="code-name">&amp;#xe7c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c5;</span>
                <div class="name">更多</div>
                <div class="code-name">&amp;#xe7c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c6;</span>
                <div class="name">失败提示</div>
                <div class="code-name">&amp;#xe7c6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c7;</span>
                <div class="name">危险提示</div>
                <div class="code-name">&amp;#xe7c7;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1718595125826') format('woff2'),
       url('iconfont.woff?t=1718595125826') format('woff'),
       url('iconfont.ttf?t=1718595125826') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-fenpi"></span>
            <div class="name">
              分批
            </div>
            <div class="code-name">.icon-fenpi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Data-details"></span>
            <div class="name">
              Data-details
            </div>
            <div class="code-name">.icon-Data-details
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c1_select-all-subsets"></span>
            <div class="name">
              c1_select-all-subsets
            </div>
            <div class="code-name">.icon-c1_select-all-subsets
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-copy1"></span>
            <div class="name">
              copy
            </div>
            <div class="code-name">.icon-copy1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-C1_form_freeze"></span>
            <div class="name">
              C1_form_freeze
            </div>
            <div class="code-name">.icon-C1_form_freeze
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-arrow-down-line"></span>
            <div class="name">
              arrow-down-line
            </div>
            <div class="code-name">.icon-arrow-down-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-more"></span>
            <div class="name">
              more
            </div>
            <div class="code-name">.icon-more
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-multilingual"></span>
            <div class="name">
              multilingual
            </div>
            <div class="code-name">.icon-multilingual
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-bianzu18"></span>
            <div class="name">
              Header - Search - default
            </div>
            <div class="code-name">.icon-a-bianzu18
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-search1"></span>
            <div class="name">
              search
            </div>
            <div class="code-name">.icon-search1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-clock-circle"></span>
            <div class="name">
              clock-circle
            </div>
            <div class="code-name">.icon-clock-circle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-c1_cr_replysize"></span>
            <div class="name">
              c1_cr_reply size
            </div>
            <div class="code-name">.icon-a-c1_cr_replysize
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c1_cr_minimize"></span>
            <div class="name">
              c1_cr_minimize
            </div>
            <div class="code-name">.icon-c1_cr_minimize
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c1_cr_analysis"></span>
            <div class="name">
              c1_cr_analysis
            </div>
            <div class="code-name">.icon-c1_cr_analysis
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c1_cr_accessory"></span>
            <div class="name">
              c1_cr_accessory
            </div>
            <div class="code-name">.icon-c1_cr_accessory
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c1_cr_file"></span>
            <div class="name">
              c1_cr_file
            </div>
            <div class="code-name">.icon-c1_cr_file
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c1_cr_Time"></span>
            <div class="name">
              c1_cr_Time
            </div>
            <div class="code-name">.icon-c1_cr_Time
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c1_cr_activity"></span>
            <div class="name">
              c1_cr_activity
            </div>
            <div class="code-name">.icon-c1_cr_activity
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c1_cr_end"></span>
            <div class="name">
              c1_cr_end
            </div>
            <div class="code-name">.icon-c1_cr_end
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c1_cr_begin"></span>
            <div class="name">
              c1_cr_begin
            </div>
            <div class="code-name">.icon-c1_cr_begin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c1_cr_approval"></span>
            <div class="name">
              c1_cr_approval
            </div>
            <div class="code-name">.icon-c1_cr_approval
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-field-people"></span>
            <div class="name">
              field-people
            </div>
            <div class="code-name">.icon-field-people
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-up"></span>
            <div class="name">
              up
            </div>
            <div class="code-name">.icon-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c1_cr_drag-and-drop"></span>
            <div class="name">
              c1_cr_drag-and-drop
            </div>
            <div class="code-name">.icon-c1_cr_drag-and-drop
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c1_cr_form_enter"></span>
            <div class="name">
              c1_cr_form_enter
            </div>
            <div class="code-name">.icon-c1_cr_form_enter
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-c1_cr_export-dropdown"></span>
            <div class="name">
              c1_cr_export-drop down
            </div>
            <div class="code-name">.icon-a-c1_cr_export-dropdown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cr_add"></span>
            <div class="name">
              cr_add
            </div>
            <div class="code-name">.icon-cr_add
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuaxin"></span>
            <div class="name">
              shuaxin
            </div>
            <div class="code-name">.icon-shuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-set-up"></span>
            <div class="name">
              set-up
            </div>
            <div class="code-name">.icon-set-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Process"></span>
            <div class="name">
              Process
            </div>
            <div class="code-name">.icon-Process
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shezhi"></span>
            <div class="name">
              设置
            </div>
            <div class="code-name">.icon-shezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-edit"></span>
            <div class="name">
              edit
            </div>
            <div class="code-name">.icon-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-file-gif"></span>
            <div class="name">
              file-gif
            </div>
            <div class="code-name">.icon-file-gif
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-file-png"></span>
            <div class="name">
              file-png
            </div>
            <div class="code-name">.icon-file-png
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-file-rar"></span>
            <div class="name">
              file-rar
            </div>
            <div class="code-name">.icon-file-rar
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-file-zip"></span>
            <div class="name">
              file-zip
            </div>
            <div class="code-name">.icon-file-zip
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-file-picture"></span>
            <div class="name">
              file-picture
            </div>
            <div class="code-name">.icon-file-picture
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-file-ppt"></span>
            <div class="name">
              file-ppt
            </div>
            <div class="code-name">.icon-file-ppt
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-file-word"></span>
            <div class="name">
              file-word
            </div>
            <div class="code-name">.icon-file-word
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-file-txt"></span>
            <div class="name">
              file-txt
            </div>
            <div class="code-name">.icon-file-txt
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-file-Excel"></span>
            <div class="name">
              file-Excel
            </div>
            <div class="code-name">.icon-file-Excel
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-file-pdf"></span>
            <div class="name">
              file-pdf
            </div>
            <div class="code-name">.icon-file-pdf
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-file-unknown"></span>
            <div class="name">
              file-unknown
            </div>
            <div class="code-name">.icon-file-unknown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_shezhi"></span>
            <div class="name">
              icon_设置
            </div>
            <div class="code-name">.icon-icon_shezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chuansuokuang"></span>
            <div class="name">
              穿梭框
            </div>
            <div class="code-name">.icon-chuansuokuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yn_cookbook_common_export"></span>
            <div class="name">
              yn_cookbook_common_export
            </div>
            <div class="code-name">.icon-yn_cookbook_common_export
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lines"></span>
            <div class="name">
              lines
            </div>
            <div class="code-name">.icon-lines
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-connectionpoints"></span>
            <div class="name">
              connection points
            </div>
            <div class="code-name">.icon-a-connectionpoints
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-automatic"></span>
            <div class="name">
              automatic
            </div>
            <div class="code-name">.icon-automatic
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-enlarge"></span>
            <div class="name">
              enlarge
            </div>
            <div class="code-name">.icon-enlarge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-narrow"></span>
            <div class="name">
              narrow
            </div>
            <div class="code-name">.icon-narrow
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-download1"></span>
            <div class="name">
              download
            </div>
            <div class="code-name">.icon-download1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Import"></span>
            <div class="name">
              Import
            </div>
            <div class="code-name">.icon-Import
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jilu"></span>
            <div class="name">
              记录
            </div>
            <div class="code-name">.icon-jilu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daochu"></span>
            <div class="name">
              导出
            </div>
            <div class="code-name">.icon-daochu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenjianjia1"></span>
            <div class="name">
              文件夹
            </div>
            <div class="code-name">.icon-wenjianjia1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-purge"></span>
            <div class="name">
              purge
            </div>
            <div class="code-name">.icon-purge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Shutdown"></span>
            <div class="name">
              Shut down
            </div>
            <div class="code-name">.icon-a-Shutdown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-visible"></span>
            <div class="name">
              visible
            </div>
            <div class="code-name">.icon-visible
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-invisible"></span>
            <div class="name">
              invisible
            </div>
            <div class="code-name">.icon-invisible
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanping"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.icon-quanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuichuquanping"></span>
            <div class="name">
              退出全屏
            </div>
            <div class="code-name">.icon-tuichuquanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paixu1"></span>
            <div class="name">
              排序
            </div>
            <div class="code-name">.icon-paixu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baocun"></span>
            <div class="name">
              保存
            </div>
            <div class="code-name">.icon-baocun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenjianjia"></span>
            <div class="name">
              文件夹
            </div>
            <div class="code-name">.icon-wenjianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenjian01"></span>
            <div class="name">
              文件01
            </div>
            <div class="code-name">.icon-wenjian01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guanyu"></span>
            <div class="name">
              关于
            </div>
            <div class="code-name">.icon-guanyu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bingji"></span>
            <div class="name">
              并集
            </div>
            <div class="code-name">.icon-bingji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiantouxia"></span>
            <div class="name">
              箭头下
            </div>
            <div class="code-name">.icon-jiantouxia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangzuoshouqi"></span>
            <div class="name">
              向左收起
            </div>
            <div class="code-name">.icon-xiangzuoshouqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiantouyou"></span>
            <div class="name">
              箭头右
            </div>
            <div class="code-name">.icon-jiantouyou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangyi"></span>
            <div class="name">
              上移
            </div>
            <div class="code-name">.icon-shangyi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paichu"></span>
            <div class="name">
              排除
            </div>
            <div class="code-name">.icon-paichu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiala"></span>
            <div class="name">
              下拉
            </div>
            <div class="code-name">.icon-xiala
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-c"></span>
            <div class="name">
              下移
            </div>
            <div class="code-name">.icon-c
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sousuo"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.icon-sousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangyouzhankai"></span>
            <div class="name">
              向右展开
            </div>
            <div class="code-name">.icon-xiangyouzhankai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiaoji"></span>
            <div class="name">
              交集
            </div>
            <div class="code-name">.icon-jiaoji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhiding"></span>
            <div class="name">
              置顶
            </div>
            <div class="code-name">.icon-zhiding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-youyi"></span>
            <div class="name">
              右移
            </div>
            <div class="code-name">.icon-youyi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouqixiaji"></span>
            <div class="name">
              收起下级
            </div>
            <div class="code-name">.icon-shouqixiaji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianzu"></span>
            <div class="name">
              上传
            </div>
            <div class="code-name">.icon-bianzu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhankaixiaji"></span>
            <div class="name">
              展开下级
            </div>
            <div class="code-name">.icon-zhankaixiaji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhedie"></span>
            <div class="name">
              折叠
            </div>
            <div class="code-name">.icon-zhedie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-warningbeifen"></span>
            <div class="name">
              警示
            </div>
            <div class="code-name">.icon-warningbeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-filter-default"></span>
            <div class="name">
              筛选
            </div>
            <div class="code-name">.icon-filter-default
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paixu"></span>
            <div class="name">
              排序
            </div>
            <div class="code-name">.icon-paixu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhankai"></span>
            <div class="name">
              展开
            </div>
            <div class="code-name">.icon-zhankai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-copy"></span>
            <div class="name">
              复制
            </div>
            <div class="code-name">.icon-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianzubeifen"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-bianzubeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchuicon"></span>
            <div class="name">
              删除icon
            </div>
            <div class="code-name">.icon-shanchuicon
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenjiangui"></span>
            <div class="name">
              文件柜
            </div>
            <div class="code-name">.icon-wenjiangui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduo"></span>
            <div class="name">
              更多
            </div>
            <div class="code-name">.icon-gengduo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shibaitishi"></span>
            <div class="name">
              失败提示
            </div>
            <div class="code-name">.icon-shibaitishi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weixiantishi"></span>
            <div class="name">
              危险提示
            </div>
            <div class="code-name">.icon-weixiantishi
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenpi"></use>
                </svg>
                <div class="name">分批</div>
                <div class="code-name">#icon-fenpi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Data-details"></use>
                </svg>
                <div class="name">Data-details</div>
                <div class="code-name">#icon-Data-details</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c1_select-all-subsets"></use>
                </svg>
                <div class="name">c1_select-all-subsets</div>
                <div class="code-name">#icon-c1_select-all-subsets</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-copy1"></use>
                </svg>
                <div class="name">copy</div>
                <div class="code-name">#icon-copy1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-C1_form_freeze"></use>
                </svg>
                <div class="name">C1_form_freeze</div>
                <div class="code-name">#icon-C1_form_freeze</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-arrow-down-line"></use>
                </svg>
                <div class="name">arrow-down-line</div>
                <div class="code-name">#icon-arrow-down-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-more"></use>
                </svg>
                <div class="name">more</div>
                <div class="code-name">#icon-more</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-multilingual"></use>
                </svg>
                <div class="name">multilingual</div>
                <div class="code-name">#icon-multilingual</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianzu18"></use>
                </svg>
                <div class="name">Header - Search - default</div>
                <div class="code-name">#icon-a-bianzu18</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-search1"></use>
                </svg>
                <div class="name">search</div>
                <div class="code-name">#icon-search1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-clock-circle"></use>
                </svg>
                <div class="name">clock-circle</div>
                <div class="code-name">#icon-clock-circle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-c1_cr_replysize"></use>
                </svg>
                <div class="name">c1_cr_reply size</div>
                <div class="code-name">#icon-a-c1_cr_replysize</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c1_cr_minimize"></use>
                </svg>
                <div class="name">c1_cr_minimize</div>
                <div class="code-name">#icon-c1_cr_minimize</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c1_cr_analysis"></use>
                </svg>
                <div class="name">c1_cr_analysis</div>
                <div class="code-name">#icon-c1_cr_analysis</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c1_cr_accessory"></use>
                </svg>
                <div class="name">c1_cr_accessory</div>
                <div class="code-name">#icon-c1_cr_accessory</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c1_cr_file"></use>
                </svg>
                <div class="name">c1_cr_file</div>
                <div class="code-name">#icon-c1_cr_file</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c1_cr_Time"></use>
                </svg>
                <div class="name">c1_cr_Time</div>
                <div class="code-name">#icon-c1_cr_Time</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c1_cr_activity"></use>
                </svg>
                <div class="name">c1_cr_activity</div>
                <div class="code-name">#icon-c1_cr_activity</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c1_cr_end"></use>
                </svg>
                <div class="name">c1_cr_end</div>
                <div class="code-name">#icon-c1_cr_end</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c1_cr_begin"></use>
                </svg>
                <div class="name">c1_cr_begin</div>
                <div class="code-name">#icon-c1_cr_begin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c1_cr_approval"></use>
                </svg>
                <div class="name">c1_cr_approval</div>
                <div class="code-name">#icon-c1_cr_approval</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-field-people"></use>
                </svg>
                <div class="name">field-people</div>
                <div class="code-name">#icon-field-people</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-up"></use>
                </svg>
                <div class="name">up</div>
                <div class="code-name">#icon-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c1_cr_drag-and-drop"></use>
                </svg>
                <div class="name">c1_cr_drag-and-drop</div>
                <div class="code-name">#icon-c1_cr_drag-and-drop</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c1_cr_form_enter"></use>
                </svg>
                <div class="name">c1_cr_form_enter</div>
                <div class="code-name">#icon-c1_cr_form_enter</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-c1_cr_export-dropdown"></use>
                </svg>
                <div class="name">c1_cr_export-drop down</div>
                <div class="code-name">#icon-a-c1_cr_export-dropdown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cr_add"></use>
                </svg>
                <div class="name">cr_add</div>
                <div class="code-name">#icon-cr_add</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxin"></use>
                </svg>
                <div class="name">shuaxin</div>
                <div class="code-name">#icon-shuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-set-up"></use>
                </svg>
                <div class="name">set-up</div>
                <div class="code-name">#icon-set-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Process"></use>
                </svg>
                <div class="name">Process</div>
                <div class="code-name">#icon-Process</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shezhi"></use>
                </svg>
                <div class="name">设置</div>
                <div class="code-name">#icon-shezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-edit"></use>
                </svg>
                <div class="name">edit</div>
                <div class="code-name">#icon-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-file-gif"></use>
                </svg>
                <div class="name">file-gif</div>
                <div class="code-name">#icon-file-gif</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-file-png"></use>
                </svg>
                <div class="name">file-png</div>
                <div class="code-name">#icon-file-png</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-file-rar"></use>
                </svg>
                <div class="name">file-rar</div>
                <div class="code-name">#icon-file-rar</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-file-zip"></use>
                </svg>
                <div class="name">file-zip</div>
                <div class="code-name">#icon-file-zip</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-file-picture"></use>
                </svg>
                <div class="name">file-picture</div>
                <div class="code-name">#icon-file-picture</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-file-ppt"></use>
                </svg>
                <div class="name">file-ppt</div>
                <div class="code-name">#icon-file-ppt</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-file-word"></use>
                </svg>
                <div class="name">file-word</div>
                <div class="code-name">#icon-file-word</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-file-txt"></use>
                </svg>
                <div class="name">file-txt</div>
                <div class="code-name">#icon-file-txt</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-file-Excel"></use>
                </svg>
                <div class="name">file-Excel</div>
                <div class="code-name">#icon-file-Excel</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-file-pdf"></use>
                </svg>
                <div class="name">file-pdf</div>
                <div class="code-name">#icon-file-pdf</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-file-unknown"></use>
                </svg>
                <div class="name">file-unknown</div>
                <div class="code-name">#icon-file-unknown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_shezhi"></use>
                </svg>
                <div class="name">icon_设置</div>
                <div class="code-name">#icon-icon_shezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chuansuokuang"></use>
                </svg>
                <div class="name">穿梭框</div>
                <div class="code-name">#icon-chuansuokuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yn_cookbook_common_export"></use>
                </svg>
                <div class="name">yn_cookbook_common_export</div>
                <div class="code-name">#icon-yn_cookbook_common_export</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lines"></use>
                </svg>
                <div class="name">lines</div>
                <div class="code-name">#icon-lines</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-connectionpoints"></use>
                </svg>
                <div class="name">connection points</div>
                <div class="code-name">#icon-a-connectionpoints</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-automatic"></use>
                </svg>
                <div class="name">automatic</div>
                <div class="code-name">#icon-automatic</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-enlarge"></use>
                </svg>
                <div class="name">enlarge</div>
                <div class="code-name">#icon-enlarge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-narrow"></use>
                </svg>
                <div class="name">narrow</div>
                <div class="code-name">#icon-narrow</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-download1"></use>
                </svg>
                <div class="name">download</div>
                <div class="code-name">#icon-download1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Import"></use>
                </svg>
                <div class="name">Import</div>
                <div class="code-name">#icon-Import</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jilu"></use>
                </svg>
                <div class="name">记录</div>
                <div class="code-name">#icon-jilu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daochu"></use>
                </svg>
                <div class="name">导出</div>
                <div class="code-name">#icon-daochu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjianjia1"></use>
                </svg>
                <div class="name">文件夹</div>
                <div class="code-name">#icon-wenjianjia1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-purge"></use>
                </svg>
                <div class="name">purge</div>
                <div class="code-name">#icon-purge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Shutdown"></use>
                </svg>
                <div class="name">Shut down</div>
                <div class="code-name">#icon-a-Shutdown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-visible"></use>
                </svg>
                <div class="name">visible</div>
                <div class="code-name">#icon-visible</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-invisible"></use>
                </svg>
                <div class="name">invisible</div>
                <div class="code-name">#icon-invisible</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanping"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#icon-quanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuichuquanping"></use>
                </svg>
                <div class="name">退出全屏</div>
                <div class="code-name">#icon-tuichuquanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paixu1"></use>
                </svg>
                <div class="name">排序</div>
                <div class="code-name">#icon-paixu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baocun"></use>
                </svg>
                <div class="name">保存</div>
                <div class="code-name">#icon-baocun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjianjia"></use>
                </svg>
                <div class="name">文件夹</div>
                <div class="code-name">#icon-wenjianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjian01"></use>
                </svg>
                <div class="name">文件01</div>
                <div class="code-name">#icon-wenjian01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanyu"></use>
                </svg>
                <div class="name">关于</div>
                <div class="code-name">#icon-guanyu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bingji"></use>
                </svg>
                <div class="name">并集</div>
                <div class="code-name">#icon-bingji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiantouxia"></use>
                </svg>
                <div class="name">箭头下</div>
                <div class="code-name">#icon-jiantouxia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangzuoshouqi"></use>
                </svg>
                <div class="name">向左收起</div>
                <div class="code-name">#icon-xiangzuoshouqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiantouyou"></use>
                </svg>
                <div class="name">箭头右</div>
                <div class="code-name">#icon-jiantouyou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangyi"></use>
                </svg>
                <div class="name">上移</div>
                <div class="code-name">#icon-shangyi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paichu"></use>
                </svg>
                <div class="name">排除</div>
                <div class="code-name">#icon-paichu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiala"></use>
                </svg>
                <div class="name">下拉</div>
                <div class="code-name">#icon-xiala</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-c"></use>
                </svg>
                <div class="name">下移</div>
                <div class="code-name">#icon-c</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sousuo"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#icon-sousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangyouzhankai"></use>
                </svg>
                <div class="name">向右展开</div>
                <div class="code-name">#icon-xiangyouzhankai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiaoji"></use>
                </svg>
                <div class="name">交集</div>
                <div class="code-name">#icon-jiaoji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhiding"></use>
                </svg>
                <div class="name">置顶</div>
                <div class="code-name">#icon-zhiding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youyi"></use>
                </svg>
                <div class="name">右移</div>
                <div class="code-name">#icon-youyi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouqixiaji"></use>
                </svg>
                <div class="name">收起下级</div>
                <div class="code-name">#icon-shouqixiaji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianzu"></use>
                </svg>
                <div class="name">上传</div>
                <div class="code-name">#icon-bianzu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhankaixiaji"></use>
                </svg>
                <div class="name">展开下级</div>
                <div class="code-name">#icon-zhankaixiaji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhedie"></use>
                </svg>
                <div class="name">折叠</div>
                <div class="code-name">#icon-zhedie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-warningbeifen"></use>
                </svg>
                <div class="name">警示</div>
                <div class="code-name">#icon-warningbeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-filter-default"></use>
                </svg>
                <div class="name">筛选</div>
                <div class="code-name">#icon-filter-default</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paixu"></use>
                </svg>
                <div class="name">排序</div>
                <div class="code-name">#icon-paixu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhankai"></use>
                </svg>
                <div class="name">展开</div>
                <div class="code-name">#icon-zhankai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-copy"></use>
                </svg>
                <div class="name">复制</div>
                <div class="code-name">#icon-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianzubeifen"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-bianzubeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchuicon"></use>
                </svg>
                <div class="name">删除icon</div>
                <div class="code-name">#icon-shanchuicon</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenjiangui"></use>
                </svg>
                <div class="name">文件柜</div>
                <div class="code-name">#icon-wenjiangui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduo"></use>
                </svg>
                <div class="name">更多</div>
                <div class="code-name">#icon-gengduo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shibaitishi"></use>
                </svg>
                <div class="name">失败提示</div>
                <div class="code-name">#icon-shibaitishi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weixiantishi"></use>
                </svg>
                <div class="name">危险提示</div>
                <div class="code-name">#icon-weixiantishi</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
