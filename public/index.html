<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="./favicon.ico" />
    <script
      type="text/javascript"
      src="<%=htmlWebpackPlugin.options.configPath%>"
    ></script>
    <style>
      .loadCont {
        display: none;
      }
    </style>
    <style href="./pageLoad.css"></style>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but sample project doesn't work properly without JavaScript
        enabled. Please enable it to continue.</strong
      >
    </noscript>
    <div id="app">
      <div class="loadCont">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          style="margin:auto;background:0 0"
          width="32"
          height="32"
          viewBox="0 0 100 100"
          preserveAspectRatio="xMidYMid"
          display="block"
        >
          <circle cx="84" cy="50" r=".254" fill="#007aff">
            <animate
              attributeName="r"
              repeatCount="indefinite"
              dur="0.28409090909090906s"
              calcMode="spline"
              keyTimes="0;1"
              values="10;0"
              keySplines="0 0.5 0.5 1"
              begin="0s"
            />
            <animate
              attributeName="fill"
              repeatCount="indefinite"
              dur="1.1363636363636362s"
              calcMode="discrete"
              keyTimes="0;0.25;0.5;0.75;1"
              values="#007aff;#58a8ff;#cde5ff;#58a8ff;#007aff"
              begin="0s"
            />
          </circle>
          <circle cx="16" cy="50" r="0" fill="#007aff">
            <animate
              attributeName="r"
              repeatCount="indefinite"
              dur="1.1363636363636362s"
              calcMode="spline"
              keyTimes="0;0.25;0.5;0.75;1"
              values="0;0;10;10;10"
              keySplines="0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1"
              begin="0s"
            />
            <animate
              attributeName="cx"
              repeatCount="indefinite"
              dur="1.1363636363636362s"
              calcMode="spline"
              keyTimes="0;0.25;0.5;0.75;1"
              values="16;16;16;50;84"
              keySplines="0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1"
              begin="0s"
            />
          </circle>
          <circle cx="16" cy="50" r="9.743" fill="#58a8ff">
            <animate
              attributeName="r"
              repeatCount="indefinite"
              dur="1.1363636363636362s"
              calcMode="spline"
              keyTimes="0;0.25;0.5;0.75;1"
              values="0;0;10;10;10"
              keySplines="0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1"
              begin="-0.28409090909090906s"
            />
            <animate
              attributeName="cx"
              repeatCount="indefinite"
              dur="1.1363636363636362s"
              calcMode="spline"
              keyTimes="0;0.25;0.5;0.75;1"
              values="16;16;16;50;84"
              keySplines="0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1"
              begin="-0.28409090909090906s"
            />
          </circle>
          <circle cx="49.126" cy="50" r="10" fill="#cde5ff">
            <animate
              attributeName="r"
              repeatCount="indefinite"
              dur="1.1363636363636362s"
              calcMode="spline"
              keyTimes="0;0.25;0.5;0.75;1"
              values="0;0;10;10;10"
              keySplines="0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1"
              begin="-0.5681818181818181s"
            />
            <animate
              attributeName="cx"
              repeatCount="indefinite"
              dur="1.1363636363636362s"
              calcMode="spline"
              keyTimes="0;0.25;0.5;0.75;1"
              values="16;16;16;50;84"
              keySplines="0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1"
              begin="-0.5681818181818181s"
            />
          </circle>
          <circle cx="83.126" cy="50" r="10" fill="#58a8ff">
            <animate
              attributeName="r"
              repeatCount="indefinite"
              dur="1.1363636363636362s"
              calcMode="spline"
              keyTimes="0;0.25;0.5;0.75;1"
              values="0;0;10;10;10"
              keySplines="0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1"
              begin="-0.8522727272727272s"
            />
            <animate
              attributeName="cx"
              repeatCount="indefinite"
              dur="1.1363636363636362s"
              calcMode="spline"
              keyTimes="0;0.25;0.5;0.75;1"
              values="16;16;16;50;84"
              keySplines="0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1;0 0.5 0.5 1"
              begin="-0.8522727272727272s"
            />
          </circle>
        </svg>
      </div>
    </div>
    <!-- built files will be auto injected -->
  </body>
</html>
