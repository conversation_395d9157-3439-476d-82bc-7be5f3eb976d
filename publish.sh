#!/bin/bash

# 计算最新版本方法
calcLastestVersion() {
    # 从私服获取最新的版本信息
    # local lastestVersion=`curl http://192.168.55.132:4873/-/verdaccio/data/sidebar/yn-consolidation | grep -o '"latest":".*"},"_' | sed 's/"},"_//g' | sed 's/".*":"//g'`

    latestVersion=$(curl http://192.168.55.132:4873/-/verdaccio/data/sidebar/yn-consolidation | grep -o '"latest":".*"},"_' | sed 's/"},"_//g' | sed 's/".*":"//g')
    if [[ $1 = "rc" ]]; then
        # 处理rc版本
        lastestVerArr=(${lastestVersion//-rc./ }) 
        mainVersion=${lastestVerArr[0]}
        if [[ ${#lastestVerArr[@]} = 2 ]]; then
            # 当前最新版本是rc版本，在原有rc版本基础上加1
            rcVersion=${lastestVerArr[1]}
            rcVersion=$((rcVersion + 1))
            lastestVersion=${lastestVerArr[0]}-rc.${rcVersion}
        else
            # 当前最新版本不是rc版本则从rc版本1开始
            lastestVersion=${lastestVerArr[0]}-rc.1 
        fi
    elif [[ $1 = "hotfix" ]]; then
        # 处理hotfix版本  
        lastestVerArr=(${lastestVersion//-rc./ })
        lastestVerArr=(${lastestVerArr[0]//-hotfix./ }) 
        mainVersion=${lastestVerArr[0]}
        
        if [[ ${#lastestVerArr[@]} = 2 ]]; then
            # 当前最新版本是rc版本，在原有rc版本基础上加1
            rcVersion=${lastestVerArr[1]}
            rcVersion=$((rcVersion + 1))
            lastestVersion=${lastestVerArr[0]}-hotfix.${rcVersion}
        else
            # 当前最新版本不是rc版本则从rc版本1开始
            lastestVersion=${lastestVerArr[0]}-hotfix.1 
        fi
    else
        # 处理非rc版本
        lastestVerArr=(${lastestVersion//-rc./ }) 
        mainVersion=${lastestVerArr[0]}
        mainVerArry=(${mainVersion//./ })
        majorVersion=${mainVerArry[0]}
        minorVersion=${mainVerArry[1]}
        fixVersion=${mainVerArry[2]}
        if [[ $param = 'fix' ]]; then
            fixVersion=$((fixVersion + 1))
        fi
        if [[ $param = 'minor' ]]; then
            minorVersion=$((minorVersion + 1))
            fixVersion=0
        fi
        if [[ $param = 'major' ]]; then
            majorVersion=$((majorVersion + 1))
            minorVersion=0
            fixVersion=0
        fi
        lastestVersion=${majorVersion}.${minorVersion}.${fixVersion}
    fi
    echo $lastestVersion
    return $local 
}

#一旦脚本中有命令的返回值非0，脚本立即退出不执行
set -e

# 接受发包参数（参数氛围小版本-fix 中版本-minor 大版本-minor 临时版本-rc）
param=""
if [[ $# > 0 ]]; then
    # 按参数区分更新哪种版本
    param=$1
else
    # 无参默认按小版本来处理
    param="fix"
fi

template_version=0.0.0
version=`calcLastestVersion $param`

# read -p "请输入要发布的元年管报应用组件库版本号(格式标准x.y.z):" version

#1）cnpm 安装string-replace
# cnpm install grunt-string-replace --save-dev

#2) grunt打包出元年MDD组件库资源source文件夹
echo "---------开始打包元年CONSOLIDATION组件资源文件夹------------"
# npm run compile
# npm run compile

#3) 切目录到元年MDD组件库模版下并更新版本号
cd dist_publish/yn-consolidation
osName=`uname`
# echo "version，，，${version}  aa             ";
# echo "template_version:${template_version}";
# echo "osName:$osName";
if [[ "$osName" = "Darwin" ]]; then
    sed -i "" "s/${template_version}/${version}/" package.json
else
    sed -i "" "s/${template_version}/${version}/" package.json
fi

#4）执行npm发布脚本直接发布到私有仓库
echo "---------元年CONSOLIDATION组件发布中------------"
# npm publish

#5) 为每个发布版本打上tag【rc版本不打tag，只有正式版本fix以上的才打tag】
# if [[ $version != *${rc}* ]]; then
#     echo "---------发布Git tag------------"
#     git tag $version -m "自定义发布到元年私有仓库：${version}"
#     git push origin $version
# fi

#6）删除发布后的本地临时文件夹
# rm -rf ../yn-consolidation/
echo "---------元年CONSOLIDATION组件发布完毕！版本为：${version}------------"

