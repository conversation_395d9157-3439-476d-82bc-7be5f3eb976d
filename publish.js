const fetch = require("node-fetch");
const fs = require("fs");
const path = require("path");
const { spawn } = require("child_process");
const rimraf = require("rimraf");
const log = {
  error(message) {
    process.stderr.write(`\x1b[31m${message}\x1b[0m`);
  },
  info(message) {
    process.stdout.write(message);
  },
  success(message) {
    process.stdout.write(`\x1b[32m${message}\x1b[0m`);
  }
};

const getLastVersion = async () => {
  return new Promise(res => {
    fetch(
      "http://192.168.55.132:4873/-/verdaccio/data/sidebar/yn-consolidation",
      {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json"
        }
      }
    )
      .then(response => response.json())
      .then(data => {
        res(data.latest);
      });
  });
};
const calcLastestVersion = lastVersion => {
  const type = process.argv[2];
  if (type !== "rc") {
    const [major, minor, fix] = lastVersion.split("-")[0].split(".");
    let finllyVersionArr = [];
    if (type === "fix") {
      finllyVersionArr = [major, minor, Number(fix) + 1];
    } else if (type === "minor") {
      finllyVersionArr = [major, Number(minor) + 1, 0];
    } else if (type === "major") {
      finllyVersionArr = [Number(major) + 1, 0, 0];
    } else {
      // 传入了 版本信息 publish 命令
      const tempStr = process.argv.slice(2)[0];
      finllyVersionArr = tempStr.split("=")[1].split(".");
    }
    return finllyVersionArr.join(".");
  } else {
    const tempArr = lastVersion.split("-rc.");
    const rcv = tempArr[1] || 0;
    return tempArr[0] + "-rc." + (Number(rcv) + 1);
  }
};

const rewriteVersion = async newVersion => {
  await new Promise((res, rej) => {
    fs.readFile("dist_publish/yn-consolidation/package.json", (err, data) => {
      if (err) throw err;
      const json = JSON.parse(data);
      json.version = newVersion;
      fs.writeFile(
        "dist_publish/yn-consolidation/package.json",
        JSON.stringify(json, null, 2),
        function(err) {
          if (err) {
            rej(err);
            throw err;
          }
          res();
        }
      );
    });
  });
};
async function publish() {
  log.info("---------元年CONSOLIDATION组件发布中------------\n");
  const lastVersion = await getLastVersion();
  const newVersion = calcLastestVersion(lastVersion.version);
  await rewriteVersion(newVersion);
  process.chdir(path.join(__dirname, "dist_publish/yn-consolidation"));
  const publishProcess = spawn("yarn publish", { shell: true });
  publishProcess.stdout.on("data", data => {
    log.info(data);
  });
  publishProcess.stderr.on("data", data => {
    log.error(data);
  });
  publishProcess.on("exit", code => {
    if (code === 0) {
      process.chdir("../../");
      rimraf("./dist_publish", err => {
        if (!err) {
          log.info(
            `---------元年CONSOLIDATION组件发布完毕！版本为：${newVersion}------------\n`
          );
        }
      });
    }
  });
}

log.info(
  "---------开始打包元年CONSOLIDATION组件资源文件夹------------------------\n"
);
const compileProcess = spawn(`yarn compile`, { shell: true });
compileProcess.stdout.on("data", data => {
  log.info(data);
});
compileProcess.stderr.on("data", data => {
  log.error(data);
});
compileProcess.on("exit", code => {
  if (code === 0) {
    publish();
  }
});
