var argv = require("minimist")(process.argv.slice(2));
const { exec } = require("child_process");
const fs = require("fs");
const path = require("path");
const moment = require("moment");

let mode = process.env.NODE_ENV;

let { log } = console;

let options = {
  maxBuffer: 100 * 1024 * 1024
};

exec(`vue-cli-service build`, options, (err, stdout) => {
  err && log(err);
  log(stdout);
  log("build dist over");

  // 给html写入版本信息
  let { tag } = argv;
  let time = moment(Date.now()).format("YYYY-MM-DD HH:mm:ss");
  let extraInfo = `<!-- ${"mode:" + mode} version:${
    process.env.LOG_STRING
  } time:${time} ${tag ? "tag:" + tag : ""} -->`;

  fs.appendFile(
    path.join(__dirname, "../dist/index.html"),
    extraInfo,
    "utf-8",
    function(err) {
      if (err) {
        log("dist/index.html版本信息写入失败！");
        log(err);
        return;
      }
      log("dist/index.html版本信息写入成功");
    }
  );
});
