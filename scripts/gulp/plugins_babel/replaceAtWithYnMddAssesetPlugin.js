"use strict";

function replacePath(path) {
  const { source } = path.node;
  const reg = /\.\.\/public\/iconfont/;
  if (source && reg.test(source.value)) {
    source.value = source.value.replace(reg, "yn-consolidation/libs/assest");
  }
}

function replaceLib() {
  return {
    visitor: {
      ImportDeclaration: replacePath,
      ExportNamedDeclaration: replacePath
    }
  };
}

module.exports = replaceLib;
