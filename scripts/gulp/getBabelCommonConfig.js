"use strict";
module.exports = function(modules) {
  const plugins = [
    "@babel/plugin-proposal-optional-chaining",
    "@babel/plugin-proposal-nullish-coalescing-operator"
  ];
  const presets = [
    [
      "@vue/app",
      {
        useBuiltIns: "entry",
        corejs: 3,
        loose: true,
        modules: modules ? "auto" : false,
        polyfills: ["es6.promise", "es6.symbol", "es6.object.assign"],
        absoluteRuntime: false,
        targets: {
          browsers: [
            "last 2 versions",
            "Firefox ESR",
            "> 1%",
            "ie >= 9",
            "iOS >= 8",
            "Android >= 4"
          ]
        }
      }
    ]
  ];
  return {
    plugins,
    presets,
    babelrc: false
  };
};
