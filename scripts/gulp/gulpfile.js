"use strict";

/**
 * 目前纯vue组件编辑成js file的没有现成gulp支持sourcemap，需要下面的工具才可支持
 * https://github.com/gulp-sourcemaps/gulp-sourcemaps/wiki/Plugins-with-gulp-sourcemaps-support
 * source目录: 这个是grunt打包后的source folder
 * es目录: 这个是es模块，目前为了跟平台保持一直，用libs代替。
 * libs目录: 这个是真实的运行模块，里面内容已经是babel过的，跟es模块功能类似，与平台统一使用libs。
 */
const babel = require("gulp-babel");
const gulp = require("gulp");
const gulpif = require("gulp-if");
const uglify = require("gulp-uglify");
const sourcemaps = require("gulp-sourcemaps");
// const merge2 = require("merge2");
// const through2 = require("through2");
const getBabelCommonConfig = require("./getBabelCommonConfig");
const replaceVueImportInJsFilePlugin = require("./plugins_babel/replaceVueImportInJsFilePlugin");
const replaceAtWithYnMddPlugin = require("./plugins_babel/replaceAtWithYnMddPlugin");
const replaceAtWithYnMddAssesetPlugin = require("./plugins_babel/replaceAtWithYnMddAssesetPlugin");
const path = require("path");
const rimraf = require("rimraf");

const gulpVue = require("gulp-vue-file");
const cwd = process.cwd();
const rootDir = path.join(cwd, "../../");
const sourceDir = path.join(rootDir, "dist_publish/yn-consolidation/source/**");
// const sourceDir = path.join(rootDir, "tests/gulp/**");

// js相关的资源文件
const jsInSourceDir = [
  path.join(sourceDir, "/*.vue"),
  path.join(sourceDir, "/*.js"),
  path.join(sourceDir, "/*.jsx")
];
// 非js相关的资源文件，比如css json等
const assetInSourceDir = [
  sourceDir,
  "!" + path.join(sourceDir, "/*.js"),
  "!" + path.join(sourceDir, "/*.jsx"),
  "!" + path.join(sourceDir, "/*.vue")
];
const libDir = path.join(rootDir, "dist_publish/yn-consolidation/libs");
const esDir = path.join(rootDir, "dist_publish/yn-consolidation/es");

function init(cb) {
  rimraf.sync(esDir);
  rimraf.sync(libDir);
  cb();
}

function copyAsset(modules) {
  const distDir = modules ? libDir : esDir;
  return gulp.src(assetInSourceDir).pipe(gulp.dest(distDir));
}

function compile(modules) {
  const distDir = modules ? libDir : esDir;
  rimraf.sync(distDir);
  const babelConfig = getBabelCommonConfig(modules);
  babelConfig.plugins.push(replaceVueImportInJsFilePlugin);
  babelConfig.plugins.push(replaceAtWithYnMddAssesetPlugin);
  babelConfig.plugins.push(replaceAtWithYnMddPlugin);
  delete babelConfig.cacheDirectory;
  return gulp
    .src([
      ...jsInSourceDir,
      "!dist_publish/yn-consolidation/source/assest/iconfont.js"
    ])
    .pipe(sourcemaps.init())
    .pipe(
      gulpVue({
        style: {
          lang: "less",
          preprocessOptions: {
            less: {
              javascriptEnabled: true,
              modifyVars: {
                hack:
                  "true; @import '../../node_modules/yn-p1/libs/themes/style/theme_var.less';"
              }
            }
          }
        }
      })
    )
    .pipe(babel(babelConfig))
    .pipe(gulpif(modules, uglify()))
    .pipe(sourcemaps.write("."))
    .pipe(gulp.dest(distDir));
}

function copyLibAsset(cb) {
  copyAsset(true).on("finish", function() {
    cb();
  });
}

function compileLib(cb) {
  compile(true).on("finish", function() {
    cb();
  });
}

function sourcemapTest(cb) {
  gulp
    .src(sourceDir)
    .pipe(sourcemaps.init())
    .pipe(
      babel({
        presets: ["@babel/preset-env"]
        // sourceMap: true
      })
    )
    // .pipe(babel(babelConfig))
    // .pipe(gulpif(modules, uglify()))
    .pipe(sourcemaps.write("."))
    .pipe(gulp.dest(libDir))
    .on("finish", function() {
      cb();
    });
}
exports.sourcemapTest = gulp.series(init, sourcemapTest);
exports.compile = gulp.series(init, gulp.parallel(copyLibAsset, compileLib));
